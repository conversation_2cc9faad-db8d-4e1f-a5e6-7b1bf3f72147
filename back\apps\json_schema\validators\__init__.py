from apps.ide_const import Document
from apps.json_schema.validators.app_security import AppSecurityValidator
from apps.json_schema.validators.approvalflow import ApprovalFlowValidator
from apps.json_schema.validators.const import ConstValidator
from apps.json_schema.validators.connector import ConnectorValidator
from apps.json_schema.validators.enum import EnumValidator
from apps.json_schema.validators.excel_template import ExcelTemplateValidator
from apps.json_schema.validators.export_template import ExportTemplateValidator
from apps.json_schema.validators.func import FuncValidator
from apps.json_schema.validators.image import ImageValidator
from apps.json_schema.validators.json import JsonValidator
from apps.json_schema.validators.model import ModelValidator
from apps.json_schema.validators.module_deploy import ModuleDeployValidator
from apps.json_schema.validators.module_theme import ModuleThemeValidator
from apps.json_schema.validators.navigation import NavigationValidator
from apps.json_schema.validators.page import PageValidator
from apps.json_schema.validators.print import PrintValidator
from apps.json_schema.validators.restful import RestfulValidator
from apps.json_schema.validators.rulechain import RulechainValidator
from apps.json_schema.validators.security import SecurityValidator
from apps.json_schema.validators.theme import ThemeValidator
from apps.json_schema.validators.third_auth import ThirdAuthValidator
from apps.json_schema.validators.watermark import WatermarkValidator
from apps.json_schema.validators.workflow import WorkflowValidator
from apps.json_schema.validators.label_print import LabelPrintValidator
from apps.json_schema.validators.permission_config import PermissionConfigValidator
from apps.json_schema.validators.extension_packages import ExpansionConfigValidator

VALIDATOR_CLS_MAP = {
    Document.TYPE.PAGE: PageValidator,
    Document.TYPE.MODEL: ModelValidator,
    Document.TYPE.APP_SECURITY: AppSecurityValidator,
    Document.TYPE.APPROVALFLOW: ApprovalFlowValidator,
    Document.TYPE.CONST: ConstValidator,
    Document.TYPE.CONNECTOR: ConnectorValidator,
    Document.TYPE.ENUM: EnumValidator,
    Document.TYPE.EXCEL_TEMPLATE: ExcelTemplateValidator,
    Document.TYPE.EXPORT_TEMPLATE: ExportTemplateValidator,
    Document.TYPE.FUNC: FuncValidator,
    Document.TYPE.IMAGE: ImageValidator,
    Document.TYPE.JSON: JsonValidator,
    Document.TYPE.MODULE_DEPLOY: ModuleDeployValidator,
    Document.TYPE.MODULE_THEME: ModuleThemeValidator,
    Document.TYPE.NAVIGATION: NavigationValidator,
    Document.TYPE.PRINT: PrintValidator,
    Document.TYPE.RESTFUL: RestfulValidator,
    Document.TYPE.RULECHAIN: RulechainValidator,
    Document.TYPE.SECURITY: SecurityValidator,
    Document.TYPE.THEME: ThemeValidator,
    Document.TYPE.DEPLOY_CONFIG: ThirdAuthValidator,
    Document.TYPE.WATERMARK: WatermarkValidator,
    Document.TYPE.WORKFLOW: WorkflowValidator,
    Document.TYPE.LABEL_PRINT: LabelPrintValidator,
    Document.TYPE.PERMISSION_CONFIG: PermissionConfigValidator,
    Document.TYPE.EXPANSION_PACK: ExpansionConfigValidator
}
