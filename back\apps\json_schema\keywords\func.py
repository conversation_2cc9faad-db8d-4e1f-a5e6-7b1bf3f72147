from apps.json_schema.utils import LemonAttributeVisitor, ReferenceData
from apps.json_schema.context import AppCtx, Resource, KeyWordCtx, DocCtx
from apps.json_schema.const import ReferenceAttrType
from jsonschema.exceptions import ValidationError
from apps.ide_const import LemonDesignerErrorC<PERSON> as LDEC
import ast


sys_namespace = ["system", "utils"]
resource_type_map = {
    ReferenceAttrType.MODEL: "app_model",
    ReferenceAttrType.MODEL_FIELD: "app_model_field",
    ReferenceAttrType.FUNC: "app_func",
    ReferenceAttrType.ENUM: "app_enum",
    ReferenceAttrType.CONST: "app_const",
    ReferenceAttrType.ENUM_ITEM: "app_enum_item",
}


def python_block(validator, value, instance, schema):
    doc_ctx: DocCtx = validator.doc_ctx
    app_ctx: AppCtx = validator.app_ctx
    keyword_ctx: KeyWordCtx = validator.keyword_ctx
    try:
        tree = ast.parse(instance)
    except Exception:
        message = LDEC.PYTHON_CODE_FORMAT_ERROR.message
        error = ValidationError(message)
        error.return_code = LDEC.PYTHON_CODE_FORMAT_ERROR
        error.parent = keyword_ctx.parent
        yield error
        return
    vistor = LemonAttributeVisitor()
    vistor.visit(tree)
    for attributes in vistor.get_found_attributes():
        attr_name = None
        attributes_split = attributes.split(".")
        if len(attributes_split) == 3:
            namespace, module_name, res_name = attributes_split
        elif len(attributes_split) > 3:
            namespace, module_name, res_name, attr_name, *_ = attributes_split
        else:
            return
        if module_name in sys_namespace:
            continue
        module = app_ctx.app_module.get_resource_by_name(module_name)
        if module:
            module_uuid = module.get("module_uuid")
            resource = None
            for ref_type, resource_store_name in resource_type_map.items():
                resource_store = getattr(app_ctx, resource_store_name, None)
                if not resource_store:
                    continue
                resource = resource_store.get_resource_by_name(res_name, attr_name, module_uuid)
                if resource:
                    ref_document = resource.get("document_uuid") if isinstance(resource, dict) else resource.document_uuid
                    attr_name = keyword_ctx.element.current_attr or keyword_ctx.element.name
                    ref_data = ReferenceData(
                        ref_type, ref_uuid=resource.get(resource_store.resource_id_name),
                        path=keyword_ctx.path, title=keyword_ctx.element.name,
                        attr_uuid=keyword_ctx.element.uuid, control_uuid=keyword_ctx.element.uuid,
                        control_type=keyword_ctx.element.type,
                        element=keyword_ctx.element, attr=attr_name, ref_document=ref_document
                    )
                    doc_ctx.to_add_reference(ref_data)
                    break
            if not resource:
                message = LDEC.REFERENCE_NOT_FOUND.message
                error = ValidationError(message)
                error.return_code = LDEC.REFERENCE_NOT_FOUND
                error.parent = keyword_ctx.parent
                yield error
