import asyncio
from apps.json_schema.validators import VALIDATOR_CLS_MAP
from apps.json_schema.context import (
    AppModel, AppModelField, AppModelRelationship, AppFunc, AppConst,
    AppEnum, AppCtx, AppPage, AppWorkflow, AppImage, AppLabelPrint,
    AppPrint, AppModule, AppModuleRole, Resource
)

from apps.json_schema.utils import ReferenceData
from apps.json_schema.const import ReferenceAttrType
from apps.entity import DocumentReference, DocumentLinks
from apps.base_engine import BaseEngine
from typing import List, Optional
from collections import defaultdict
from copy import deepcopy
from peewee import SQL
import json
import time
from loguru import logger
from baseutils.utils import LemonContextVar


VALIDATOR_CACHE = {}


async def get_real_app_all_entity(app_uuid: str, engine: BaseEngine) -> List:
    app_module_list = await engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
    app_model_list = await engine.access.list_model_by_app_uuid(
        app_uuid, with_sys=True)
    app_relationship_list = await engine.access.list_relationship_by_app_uuid(app_uuid)
    app_field_list = await engine.access.list_field_by_app_uuid(
        app_uuid, with_sys=True, need_hide=True)
    app_index_list = await engine.access.list_index_by_app_uuid(app_uuid)
    app_enum_list = await engine.access.list_enum_by_app_uuid(app_uuid, with_sys=True)
    const_list = await engine.access.list_const_by_app_uuid(app_uuid, with_sys=True)
    app_func_list = await engine.access.list_func_by_app_uuid(app_uuid, with_sys=True)
    page_list = await engine.access.list_page_by_app_uuid(app_uuid)
    workflow_list = await engine.access.list_workflow_by_app_uuid(app_uuid)
    modules_role_list = await engine.access.list_module_role_by_app_uuid(app_uuid)
    url_setting_list = await engine.access.list_page_url_by_app_uuid(app_uuid)
    restful_list = await engine.access.list_restful_request_by_app_uuid(app_uuid, basic_auth=False)
    label_print_list = await engine.access.list_label_print_by_app_uuid(app_uuid)
    print_list = await engine.access.list_print_by_app_uuid(app_uuid)
    image_list = await engine.access.list_image_by_app_uuid(app_uuid)
    return [list(app_module_list), list(app_model_list), list(app_relationship_list),
            list(app_field_list), list(app_index_list),
            list(app_enum_list), list(app_func_list),
            list(page_list), list(workflow_list),
            list(const_list), list(modules_role_list),
            list(url_setting_list), list(restful_list),
            list(label_print_list), list(print_list), list(image_list)]


async def get_app_all_entity(app_uuid: str, engine: BaseEngine, force_update_cache=False) -> List:
    cache_key = f"app_all_entity_{app_uuid}"
    app_datas = await engine.redis.get_cache(cache_key)

    async def update_cache():
        start_time = time.time()
        app_datas = await get_real_app_all_entity(app_uuid, engine)
        await engine.redis.set_cache(cache_key, json.dumps(app_datas), expiry=600)
        logger.debug(f"update cache: {app_uuid}, cost: {time.time() - start_time}")
        return app_datas

    if app_datas:
        logger.debug(f"hit cache: {app_uuid}")
        app_datas = json.loads(app_datas)
        if force_update_cache:
            asyncio.get_event_loop().call_soon(asyncio.create_task, update_cache())
    else:
        app_datas = await update_cache()
    return app_datas


async def process_reference_data(app_uuid: str, document_uuid: str,
                                 reference_data_list: List[ReferenceData],
                                 engine: BaseEngine, app_ctx: AppCtx):

    def resource_info_to_link_info(resource_dict: dict, link_info: dict, ref_type: str):
        for resource_id, resource_info in resource_dict.items():
            app_entities: Optional[Resource] = getattr(app_ctx, f"app_{ref_type}", None)
            if app_entities is None:
                return
            resource_entity = app_entities.get_resource(resource_id)
            if resource_entity is None:
                return
            resource_info.update({
                "link_document": resource_entity.get("document_uuid")
            })
            link_info[(app_uuid, document_uuid, resource_id, ref_type)].update(deepcopy(resource_info))

    new_reference_data = {}
    reference_update_data = {}
    field_dict = defaultdict(dict)
    model_dict = defaultdict(dict)
    r_dict = defaultdict(dict)
    func_dict = defaultdict(dict)
    enum_dict = defaultdict(dict)
    page_dict = defaultdict(dict)
    print_dict = defaultdict(dict)
    wf_dict = defaultdict(dict)
    enum_item_dict = defaultdict(dict)
    label_print_dict = defaultdict(dict)

    reference_info = await engine.access.get_document_reference_by_document_uuid(document_uuid)
    doc_links = []
    all_link_data = defaultdict(dict)
    if reference_info:
        field_dict.update(reference_info.field_reference)
        model_dict.update(reference_info.model_reference)
        r_dict.update(reference_info.r_reference)
        func_dict.update(reference_info.func_reference)
        enum_dict.update(reference_info.enum_reference)
        page_dict.update(reference_info.page_reference)
        print_dict.update(reference_info.print_reference)
        wf_dict.update(reference_info.wf_reference)
        enum_item_dict.update(reference_info.enum_item_reference)
        label_print_dict.update(reference_info.label_print_reference)

        # 旧数据转化为新数据
        resource_info_to_link_info(field_dict, all_link_data, ReferenceAttrType.MODEL_FIELD)
        resource_info_to_link_info(model_dict, all_link_data, ReferenceAttrType.MODEL)
        resource_info_to_link_info(r_dict, all_link_data, ReferenceAttrType.MODEL_RELATIONSHIP)
        resource_info_to_link_info(func_dict, all_link_data, ReferenceAttrType.FUNC)
        resource_info_to_link_info(enum_dict, all_link_data, ReferenceAttrType.ENUM)
        resource_info_to_link_info(page_dict, all_link_data, ReferenceAttrType.PAGE)
        resource_info_to_link_info(print_dict, all_link_data, ReferenceAttrType.PRINT)
        resource_info_to_link_info(wf_dict, all_link_data, ReferenceAttrType.WORKFLOW)
        resource_info_to_link_info(enum_item_dict, all_link_data, ReferenceAttrType.ENUM_ITEM)
        resource_info_to_link_info(label_print_dict, all_link_data, ReferenceAttrType.LABEL_PRINT)

    for reference in reference_data_list:
        link_data = all_link_data[(app_uuid, document_uuid, reference.ref_uuid, reference.ref_type)]
        link_data.update(reference.to_dict())
        link_data.update({"link_document": reference.ref_document})

        if reference.hide:
            continue
        if reference.ref_type == ReferenceAttrType.MODEL:
            model_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.MODEL_FIELD:
            field_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.MODEL_RELATIONSHIP:
            r_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.PAGE:
            page_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.FUNC:
            func_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.ENUM:
            enum_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.PRINT:
            print_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.WORKFLOW:
            wf_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.ENUM_ITEM:
            enum_item_dict[reference.ref_uuid].update(reference.to_dict())
        elif reference.ref_type == ReferenceAttrType.LABEL_PRINT:
            label_print_dict[reference.ref_uuid].update(reference.to_dict())
    reference_update_data.update({
        DocumentReference.field_reference.name: field_dict,
        DocumentReference.model_reference.name: model_dict,
        DocumentReference.r_reference.name: r_dict,
        DocumentReference.func_reference.name: func_dict,
        DocumentReference.enum_reference.name: enum_dict,
        DocumentReference.page_reference.name: page_dict,
        DocumentReference.print_reference.name: print_dict,
        DocumentReference.wf_reference.name: wf_dict,
        DocumentReference.enum_item_reference.name: enum_item_dict,
        DocumentReference.label_print_reference.name: label_print_dict,
    })
    new_reference_data.update(reference_update_data)
    new_reference_data.update({
        DocumentReference.document_uuid.name: document_uuid,
        DocumentReference.app_uuid.name: app_uuid
    })
    if reference_info:
        after_atomic = engine.access.update_reference_by_document_uuid(
            document_uuid, **reference_update_data)
    else:
        after_atomic = engine.access.create_document_reference(**new_reference_data)
    LemonContextVar.atomic_cache_tasks.get().append(asyncio.ensure_future(after_atomic))
    for key, value in all_link_data.items():
        app_uuid, document_uuid, link_resource, link_type = key
        doc_links.append({
            DocumentLinks.app_uuid.name: app_uuid,
            DocumentLinks.document_uuid.name: document_uuid,
            DocumentLinks.link_resource.name: link_resource,
            DocumentLinks.link_type.name: link_type,
            DocumentLinks.link_document.name: value.get("link_document"),
            DocumentLinks.link_data.name: value
        })
    to_delete = []
    origin_links = await engine.access.list_documentlinks_by_document_uuid(document_uuid, as_dict=False)
    for link in origin_links:
        if (app_uuid, document_uuid, link.link_resource, link.link_type) not in all_link_data.keys():
            to_delete.append(link.id)
    async with engine.db.objs.atomic():
        async with engine.db.objs.atomic():
            async with engine.db.objs.atomic():
                async with engine.db.objs.atomic():
                    if to_delete:
                        delete_query = DocumentLinks.delete().where(DocumentLinks.id.in_(to_delete))
                        await engine.access.delete_obj_by_query(DocumentLinks, delete_query)
                    if doc_links:
                        insert_query = DocumentLinks.insert_many(doc_links).on_conflict(update={
                            DocumentLinks.link_data: SQL(f"VALUES({DocumentLinks.link_data.name})"),
                            DocumentLinks.link_document: SQL(f"VALUES({DocumentLinks.link_document.name})")
                            })
                        await engine.db.objs.execute(insert_query)


class Validator:

    def __new__(cls, document_type: int, engine) -> 'Validator':
        if document_type not in VALIDATOR_CACHE:
            VALIDATOR_CACHE[document_type] = object.__new__(cls)
        return VALIDATOR_CACHE[document_type]

    def __init__(self, document_type: int, engine) -> None:
        self.document_type = document_type
        self.validator_cls = VALIDATOR_CLS_MAP.get(document_type)
        self.engine: BaseEngine = engine
        if not self.validator_cls:
            raise ValueError(f"Unsupported document type: {document_type}")
        self._validator = None
        self.app_ctx = None
        self.app_uuid = None

    def update_ctx(self, app_uuid: str, app_entities: Optional[List] = None):
        if not self.app_ctx or app_uuid != self.app_uuid:
            self.app_uuid = app_uuid
            self.app_ctx = AppCtx()
            self.app_ctx.app_module = AppModule(app_uuid)
            self.app_ctx.app_model = AppModel(app_uuid)
            self.app_ctx.app_model_relationship = AppModelRelationship(app_uuid)
            self.app_ctx.app_model_field = AppModelField(app_uuid)
            self.app_ctx.app_func = AppFunc(app_uuid)
            self.app_ctx.app_const = AppConst(app_uuid)
            self.app_ctx.app_enum = AppEnum(app_uuid)
            self.app_ctx.app_page = AppPage(app_uuid)
            self.app_ctx.app_workflow = AppWorkflow(app_uuid)
            self.app_ctx.app_image = AppImage(app_uuid)
            self.app_ctx.app_print = AppPrint(app_uuid)
            self.app_ctx.app_label_print = AppLabelPrint(app_uuid)
            self.app_ctx.app_module_role = AppModuleRole(app_uuid)

        # todo 不需要每次都更新全部entity
        if app_entities:
            (app_module_list, app_model_list, app_relationship_list, app_field_list, app_index_list,
                app_enum_list, app_func_list, page_list, workflow_list, const_list,
                modules_role_list, url_setting_list,
                restful_list, label_print_list,
                print_list, image_list) = app_entities
            self.app_ctx.app_module.update_data(app_module_list)
            self.app_ctx.app_model.update_data(app_model_list)
            self.app_ctx.app_model_relationship.update_data(
                app_model_list, app_relationship_list, app_field_list)
            self.app_ctx.app_model_field.update_data(app_field_list, app_model_list)
            # self.app_ctx.app_index.update_data(app_index_list)
            self.app_ctx.app_enum.update_data(app_enum_list)
            self.app_ctx.app_func.update_data(app_func_list)
            self.app_ctx.app_page.update_data(page_list)
            self.app_ctx.app_workflow.update_data(workflow_list)
            self.app_ctx.app_const.update_data(const_list)
            self.app_ctx.app_label_print.update_data(label_print_list)
            self.app_ctx.app_print.update_data(print_list)
            self.app_ctx.app_image.update_data(image_list)
            self.app_ctx.app_module_role.update_data(modules_role_list)

    async def pre_validate(self, app_uuid: str):
        entities = await self.get_app_entities(app_uuid)
        self.update_ctx(app_uuid, entities)
        if not self._validator:
            self._validator = self.validator_cls(self.app_ctx)
            self._validator.compile()

    def validate(self, data: dict):
        if not self._validator:
            raise ValueError("validator Not initialized")
        return self._validator.validate(data)

    async def handle_reference_data(self, document_uuid: str,
                                    reference_info: List[ReferenceData]):
        if not self._validator:
            raise ValueError("validator Not initialized")
        await process_reference_data(self.app_uuid, document_uuid,
                                     reference_info, self.engine, self.app_ctx)

    async def post_validate(self):
        # todo: 按文档类型和commit entity message更新
        await self.get_app_entities(self.app_uuid, force_update_cache=True)

    async def get_app_entities(self, app_uuid: str, force_update_cache=False) -> List:
        return await get_app_all_entity(app_uuid, self.engine, force_update_cache=force_update_cache)
