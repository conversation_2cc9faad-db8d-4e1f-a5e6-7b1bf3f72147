source ./config.sh

# 形如--app_uuid或--database_name为传参, 需要将对应值替换为实际应用值


# 2023.09.25 前部署的未执行过的, 部署前需要解开注释一次

# docker exec -it web-$app_env python ucenter/server.py process_sys_model
# docker exec -it web-$app_env python apps/server.py copy_role_member_data --app_uuid="456525ee99d25c42ab805b4e53c78ec1" --database_name="a4a48f6043a0514fb55e70efa531fd60"
# docker exec -it web-$app_env python ucenter/server.py create_tenant_table_all


# 2024.09.27 前部署的未执行过的, 部署前需要解开注释一次, 请勿重复执行
# env_name: 如部署正式环境, env_name=""; 如部署测试环境, env_name="test"

# docker exec -it web-$app_env python apps/server.py process_redis_data --app_uuid="456525ee99d25c42ab805b4e53c78ec1" --env_name=""

echo patch down