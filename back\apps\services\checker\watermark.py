# # -*- coding:utf-8 -*-

from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker
from apps.ide_const import Watermark
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError


class WatermarkCheckerService(CheckerService):

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.image = self.element.get("image")
        self.show = self.element.get("show")

    @checker.run
    def check_image_watermark_set(self):
        if self.show and self.type == Watermark.TYPE.IMAGE:
            url = self.image.get("url", "")
            if not url:
                attr = Watermark.ATTR.IMAGEWATERMARK
                kwargs = {
                    "element_data": {
                        "name": self.document_name,
                        "uuid": self.document_uuid,
                    }
                }
                self._add_error_list(
                    attr=attr, return_code=LDEC.WATERMARK_IMAGE_NOT_SET, **kwargs
                )


class WatermarkDocumentCheckerService(DocumentCheckerService):

    async def commit_modify(self, engine):
        ...

    @checker.run
    def check_watermark(self):
        watermark_checker_service = WatermarkCheckerService(
            self.app_uuid, self.module_uuid, self.module_name,
            self.document_uuid, self.document_name, self.element,
        )

        try:
            watermark_checker_service.check_all()
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(watermark_checker_service)
            raise e
        else:
            self.update_any_list(watermark_checker_service)
