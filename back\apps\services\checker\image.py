# -*- coding:utf-8 -*-

import os
import base64
import asyncio
from hashlib import md5

from baseutils.log import app_log
from apps.exceptions import <PERSON>NameError, CheckUUI<PERSON>rror, CheckUUIDUniqueError
from apps.entity import ImageTable
from apps.ide_const import Image
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker


class ImageCheckerService(CheckerService):

    attr_class = Image.ATTR
    uuid_error = LDEC.IMAGE_UUID_ERROR
    uuid_unique_error = LDEC.IMAGE_UUID_UNIQUE_ERROR
    name_error = LDEC.IMAGE_NAME_FAILED
    name_unique_error = LDEC.IMAGE_NAME_NOT_UNIQUE
    allow_chinese_name = True
    allow_keywords = False
    
    def initialize(self):
        super().initialize()
        self.description = self.element.get("description", "")
        self.type = self.element.get("type")
        self.width = self.element.get("width")
        self.height = self.element.get("height")
        self.url = self.element.get("url")
        # self.s_url = self.element.get("s_url")
        # self.md5 = self.element.get("md5")
        # self.content = self.element.get("content")
        # self.s_content = self.element.get("s_content")
        # self.storage_type = image_storage_type
        # self.static_url_prefix = static_url_prefix
        # self.url = self.build_url()
        # self.s_url = self.build_s_url()
        # # app_log.info(self.url)
        # # app_log.info(self.s_url)
        # self.element.update({"storage_type": self.storage_type})
        # self.element.update({"url": self.url})
        # self.element.update({"s_url": self.s_url})
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
        # self.update_image_list = []
        # self.delete_image_list = []
    
    # def build_url_prefix(self):
    #     return self.static_url_prefix + "/image_list"
    
    # @classmethod
    # def build_image_dir(cls, app_uuid):
    #     return "/" + app_uuid

    # @classmethod
    # def build_image_type_str(cls, image_type):
    #     return Image.TYPE.DICT.get(image_type, "")
    
    # @classmethod
    # def build_image_name(cls, image_uuid, image_type):
    #     image_type_str = cls.build_image_type_str(image_type)
    #     return image_uuid + image_type_str
    
    # @classmethod
    # def build_image_s_name(cls, image_uuid, image_type):
    #     image_type_str = cls.build_image_type_str(image_type)
    #     return image_uuid + "_s" + image_type_str
    
    # @classmethod
    # def build_image_path(cls, app_uuid, image_uuid, image_type):
    #     image_dir = cls.build_image_dir(app_uuid)
    #     image_name = cls.build_image_name(image_uuid, image_type)
    #     return image_dir + "/" + image_name
    
    # @classmethod
    # def build_image_s_path(cls, app_uuid, image_uuid, image_type):
    #     image_dir = cls.build_image_dir(app_uuid)
    #     image_name = cls.build_image_s_name(image_uuid, image_type)
    #     return image_dir + "/" + image_name

    # def build_url(self):
    #     if self.storage_type == Image.STORAGE_TYPE.FILE:
    #         url_preifx = self.build_url_prefix()
    #         image_path = self.build_image_path(self.app_uuid, self.element_uuid, self.type)
    #         url = url_preifx + image_path
    #         return url

    # def build_s_url(self):
    #     if self.storage_type == Image.STORAGE_TYPE.FILE:
    #         url_preifx = self.build_url_prefix()
    #         image_s_path = self.build_image_s_path(self.app_uuid, self.element_uuid, self.type)
    #         url = url_preifx + image_s_path
    #         return url
    
    def build_insert_query_data(self):
        return {
            ImageTable.app_uuid.name: self.app_uuid,
            ImageTable.module_uuid.name: self.module_uuid,
            ImageTable.document_uuid.name: self.document_uuid,
            ImageTable.image_uuid.name: self.element_uuid,
            ImageTable.image_name.name: self.element_name,
            ImageTable.description.name: self.description,
            ImageTable.image_type.name: self.type,
            ImageTable.width.name: self.width,
            ImageTable.height.name: self.height,
            # ImageTable.md5.name: self.md5,
            # ImageTable.storage_type.name: self.storage_type,
            ImageTable.url.name: self.url,
            # ImageTable.s_url.name: self.s_url
        }
    
    def build_update_query_data(self):
        return {
            ImageTable.image_name.name: self.element_name,
            ImageTable.description.name: self.description,
            ImageTable.image_type.name: self.type,
            ImageTable.width.name: self.width,
            ImageTable.height.name: self.height,
            # ImageTable.md5.name: self.md5,
            # ImageTable.storage_type.name: self.storage_type,
            ImageTable.url.name: self.url,
            # ImageTable.s_url.name: self.s_url,
            ImageTable.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return ImageTable.update(**query_data).where(
            ImageTable.image_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(image_uuid):
        return ImageTable.update(**{
                ImageTable.is_delete.name: True
            }).where(ImageTable.image_uuid==image_uuid)
    
    # def build_update_image(self):
    #     image_dir = self.build_image_dir(self.app_uuid)
    #     image_name = self.build_image_name(self.element_uuid, self.type)
    #     image_s_name = self.build_image_s_name(self.element_uuid, self.type)
    #     return {
    #         "image_dir": image_dir,
    #         "image_name": image_name,
    #         "image_s_name": image_s_name,
    #         "content": self.content,
    #         "s_content": self.s_content
    #     }
    
    # @classmethod
    # def build_delete_image(cls, app_uuid, element_uuid, image_type):
    #     image_path = cls.build_image_path(app_uuid, element_uuid, image_type)
    #     image_s_path = cls.build_image_s_path(app_uuid, element_uuid, image_type)
    #     return {
    #         "path": image_path,
    #         "s_path": image_s_path
    #     }
    
    def check_modify(self, document_image_uuid_set):
        if self.element_uuid in document_image_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
        # if self.content:
        #     update_image = self.build_update_image()
        #     self.update_image_list.append(update_image)
    
    # 检查图片类型是否支持，如果不支持，会向错误列表添加一条报错信息
    @checker.run
    def check_image_type(self):
        attr = Image.ATTR.TYPE
        return_code = LDEC.IMAGE_TYPE_NOT_SUPPORT
        return_code.message = return_code.message.format(name=self.element_name)
        if self.type not in Image.TYPE.ALL:
            self._add_error_list(attr=attr, return_code=return_code)
        
    # # 检查图片MD5是否一致，如果不一致，会向错误列表添加一条报错信息
    # def check_image_md5(self, document_image_uuid_dict):
    #     """
    #     # 这里的检查规则
    #     # 1. 如果有内容，先自己计算一遍MD5，如果与文档中的MD5不匹配，报 IMAGE_MD5_INCURRECT
    #     # 2. 如果没内容，则取出数据库的MD5，如果与文档中的MD5不匹配，报 IMAGE_MD5_DIFF
    #     """
    #     attr = Image.ATTR.MD5
    #     if self.content:
    #         content_decode = base64.b64decode(self.content)
    #         image_md5 = md5(content_decode).hexdigest()
    #         if image_md5 != self.md5:
    #             return_code = LDEC.IMAGE_MD5_INCURRECT
    #             return_code.message = return_code.message.format(name=self.element_name)
    #             self._add_error_list(attr=attr, return_code=return_code)
    #             # 替换当前的md5值
    #             self.md5 = image_md5
    #             self.element.update({"md5": self.md5})
    #     else:
    #         image = document_image_uuid_dict.get(self.element_uuid, dict())
    #         image_md5 = image.get("image_md5")
    #         if image_md5 != self.md5:
    #             return_code = LDEC.IMAGE_MD5_DIFF
    #             return_code.message = return_code.message.format(name=self.element_name)
    #             self._add_error_list(attr=attr, return_code=return_code)
    #             # 替换当前的md5值
    #             self.md5 = image_md5
    #             self.element.update({"md5": self.md5})


class ImageDocumentCheckerService(DocumentCheckerService):
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, document_name: str, 
        element: dict, document_version: int, app_image_list: list, ignore_name: bool, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, ImageTable, *args, **kwargs)
        self.image_list = self.element.get("image_list", list())
        self.ignore_name = ignore_name
        self.app_image_uuid_set = set()
        self.module_image_name_set = set()
        self.document_image_uuid_set = set()
        self.document_image_uuid_dict = dict()
        self.document_image_update_list = list()
        self.document_image_delete_list = list()
        for image in app_image_list:
            image_uuid = image.get("image_uuid", "")
            image_name = image.get("image_name", "")
            image_module_uuid = image.get("module_uuid", "")
            image_document_uuid = image.get("document_uuid", "")

            # 找到原文档中所有的图片，为了新增、更新、删除文档的 图片
            if image_document_uuid == self.document_uuid:
                self.document_image_uuid_set.add(image_uuid)
                self.document_image_uuid_dict.update({image_uuid: image})
            else:
                # 排除当前文档所有的 image_uuid ，获取应用的所有 image_uuid
                self.app_image_uuid_set.add(image_uuid)
                # 排除当前文档所有的 image_uuid ，获取模块的所有 image_uuid
                if image_module_uuid == self.module_uuid:
                    if not image.get("is_delete"):
                        self.module_image_name_set.add(image_name)
    
    @checker.run
    def check_image_list(self):
        this_document_image_uuid_set = set()
        # new_image_list = list()
        for image in self.image_list:
            if self.is_copy:
                temp_name = image.get("name")
                image.update({"name": temp_name + "_" + str(self.document_number)})
                # self.module_image_name_set.add(image.get("name"))
            image_checker_service = ImageCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, 
                    self.document_uuid, self.document_name, image, is_copy=self.is_copy)
            image_uuid = image_checker_service.element_uuid
            this_document_image_uuid_set.add(image_uuid)
            try:
                image_checker_service.check_uuid()
                image_checker_service.check_uuid_unique(self.app_image_uuid_set)
                if not self.ignore_name:
                    image_checker_service.check_name()
                    image_checker_service.check_name_unique(self.module_image_name_set)
                image_checker_service.check_all()
                # image_checker_service.check_image_md5(self.document_image_uuid_dict)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(image_checker_service)
                raise e
            else:
                self.update_any_list(image_checker_service)

            # 找到新增 或 更新的 图片
            image_checker_service.check_modify(self.document_image_uuid_set)
            if image_checker_service.insert_query_list:
                self.document_insert_list.extend(image_checker_service.insert_query_list)
            if image_checker_service.update_query_list:
                self.document_update_list.extend(image_checker_service.update_query_list)
            
        #     # 找到新增 或 更新的 图片 内容，保存到本地磁盘 或 OSS
        #     if image_checker_service.update_image_list:
        #         self.document_image_update_list.extend(image_checker_service.update_image_list)
        
        #     # 图片会更新 image_url 和 small_image_url 所以要更新文档内容
        #     new_image_list.append(image)
        # self.element.update({"image_list": new_image_list})

        # 找出删除的 图片，将其 is_delete 置为 True
        delete_image_uuid_set = self.document_image_uuid_set - this_document_image_uuid_set
        for this_image_uuid in delete_image_uuid_set:
            query = ImageCheckerService.build_delete_query(this_image_uuid)
            self.document_delete_list.append(query)

            # # 找出删除的 图片，本地磁盘删除 或 OSS删除
            # this_image = self.document_image_uuid_dict.get(this_image_uuid)
            # this_image_type = this_image.get("image_type")
            # delete_image = ImageCheckerService.build_delete_image(self.app_uuid, this_image_uuid, this_image_type)
            # self.document_image_delete_list.append(delete_image)

    # async def save_to_file(self, engine):
    #     path_prefix = engine.config.STATIC_PATH_PREFIX + "/image_list"
    #     for image in self.document_image_update_list:
    #         image_dir = image.get("image_dir")
    #         image_name = image.get("image_name")
    #         image_s_name = image.get("image_s_name")
    #         image_content = image.get("content")
    #         image_s_content = image.get("s_content")
    #         image_content_decode = base64.b64decode(image_content)
    #         image_s_content_deocde = base64.b64decode(image_s_content)
    #         full_image_dir = path_prefix + image_dir
    #         if not os.path.exists(full_image_dir):
    #             os.makedirs(full_image_dir)
    #         with open(full_image_dir + "/" + image_name, "wb") as open_file:
    #             open_file.write(image_content_decode)
    #         with open(full_image_dir + "/" + image_s_name, "wb") as open_file:
    #             open_file.write(image_s_content_deocde)

    # async def save_to_oss(self, engine):
    #     pass
    
    # async def save_image_list(self, engine):
    #     if self.image_storage_type == Image.STORAGE_TYPE.FILE:
    #         await self.save_to_file(engine)
    #     if self.image_storage_type == Image.STORAGE_TYPE.OSS:
    #         await self.save_to_file(engine)
