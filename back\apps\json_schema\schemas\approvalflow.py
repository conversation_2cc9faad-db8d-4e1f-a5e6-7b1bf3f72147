from apps.json_schema.schemas.utils import gen_const_condition_schema
from apps.ide_const import NodeType

approvalflow = {
    "is_element": True,
    "attr_name": "审批流",
    "type": "object",
    "properties": {
        "version": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "settings": {
                        "type": "object",
                        "properties": {
                            "total_to": {
                                "$ref": "mem://workflow/page_selection"
                            },
                            "total_from": {
                                "$ref": "mem://workflow/page_selection"
                            }
                        }
                    },
                    "nodes": {
                        "type": "array",
                        "items": {
                            "allOf": [
                                gen_const_condition_schema(NodeType.START, "mem://workflow/node_start"), 
                                gen_const_condition_schema(NodeType.END, "mem://workflow/node_end"),
                                gen_const_condition_schema(NodeType.MANUAL, "mem://workflow/node_manual")
                            ]
                        }
                    },
                }
            }
        }
    }
}