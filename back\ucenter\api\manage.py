# -*- coding:utf-8 -*-

from datetime import datetime
import random
import string
import time
import copy
import asyncio
from dataclasses import asdict, dataclass, field
from aiohttp.client import ClientSession
import ujson
from functools import partial

from apps.base_utils import check_document_name, check_lemon_name, make_model_copy, make_tenant_sys_table_copy
from apps.user_entity import NodeInstance, NodeTask
from baseutils.const import DOC, UUID_TenantDepartment
from apps.exceptions import RemoveMemberError
from baseutils.log import app_log
from apps.ide_const import NodeAssignType, NodeSystemActionType, NodeTaskResult, NodeTaskStatus
from apps.utils import LemonDictResponse as LDR, gen_password_salt_hash
from apps.utils import check_lemon_uuid, lemon_uuid, process_args, check_email
from runtime.utils import get_app_uuid, get_tenant_uuid, get_app_tenant_uuid
from runtime.async_utils import manager_of_by_user_uuid
from runtime.uc.utils import process_sub_dm
from runtime.const import RedisChannel
from apps.user_entity import (
    TenantUser, TenantDepartment, TenantDepartmentMember, TenantMember
)
from sanic import Blueprint
from sanic.response import json
# from sanic.views import HTTPMethodView
from sanic_openapi import doc
from ucenter.engine import engine
from apps.ucenter_entity import DepartmentMember
from apps.base_entity import User
from apps.runtime_entity import RoleMember

from .const import Code
from runtime.utils import LemonHTTPMethodView as HTTPMethodView

url_prefix = "/api/manage/v1"
bp = Blueprint("manage_v1", url_prefix=url_prefix)

# todo: 服务时间到期后禁止成员使用app
# todo： 账号停用，禁止成员登录
class GetUser(HTTPMethodView):
    class GetUserObj():
        user_uuid = doc.String("user_uuid")

    @doc.consumes(GetUserObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取用户信息")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.json.get("user_uuid")
            if not user_uuid:
                user_uuid = request.ctx.session.get("user_uuid")
            lemon_user_uuid = request.ctx.session.get("lemon_user_uuid")
            if not lemon_user_uuid and not tenant_uuid:
                tenant_uuid = request.ctx.session.get("tenant_uuid")
            is_anonymous = request.ctx.session.get("is_anonymous")
            app_env = int(request.headers.get("appEnv", 0))
        
        if is_anonymous:
            user_info = dict(user_uuid=user_uuid, user_name="anonymous", mobile_phone="*", full_name="游客")
        else:
            if tenant_uuid:
                user_info = await engine.access.get_user_tenant_by_user_uuid(user_uuid, tenant_uuid)
            else:
                user_info = await engine.access.get_user_by_user_uuid(user_uuid)
            if not user_info:
                return json(LDR(Code.USER_NOT_EXISTS))
        output = {}
        # todo: 关闭匿名后再发布，需要把匿名的token清空
        output.update(dict(
            user_uuid=user_uuid,
            user_name=user_info.get("user_name"),
            mobile_phone=user_info.get("mobile_phone"),
            full_name=user_info.get("full_name"),
            avatar=user_info.get("avatar"),
            wechat_id=user_info.get("wechat_id"),
            wechat_name=user_info.get("wechat_name"), 
            # is_job_number = user_info.get("is_job_number"), 
            tenant_id = user_info.get("tenant_id"),
            is_anonymous=is_anonymous
        ))
        app_log.info(output)
        return json(LDR(data=output))
        if app_uuid and tenant_uuid:
            member_info = await engine.access.get_tenant_member_detail(user_uuid, tenant_uuid)
            app_log.info(member_info)
            user_roles = await engine.access.list_user_role_by_user_uuid(
                user_uuid, tenant_uuid, app_uuid, app_env)
            is_sys_admin = False    
            user_roles_list = list()
            user_role_uuids = list()
            for role in user_roles:
                user_roles_list.append(role["role_name"])
                user_role_uuids.append(role["role_uuid"])
                if role.get("is_admin"):
                    is_sys_admin = True
            department_admin = set()
            try:
                is_dm_admin_list = member_info.pop("is_department_admin", "0").split("|")
                for idx, dm_uuid in enumerate(member_info.pop("department_uuid", "").splitlines(keepends=False)):
                    if int(is_dm_admin_list[idx]):
                        department_admin.add(dm_uuid)
            except:
                pass
            member_info.update(
                dict(
                    roles=user_roles_list,
                    is_sys_admin=is_sys_admin,
                    department_admin=list(department_admin), 
                    user_role_uuids=user_role_uuids
                )
            )
            output.update(member_info)
        return json(LDR(data=output))

class InviteUser(HTTPMethodView):
    class InviteUserObj():
        organization_id = doc.String("企业uuid")
        accounts = doc.String("账号信息, 换行符分割")

    @doc.consumes(InviteUserObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("邀请用户")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            # tenant_uuid = get_tenant_uuid(request)
            account_list_str = str(request.json.get("accounts", ""))
            account_list = account_list_str.splitlines()
            if not account_list:
                return json(LDR(Code.ARGS_ERROR))
        model = User
        query = model.select()
        for account in account_list:
            query = query.orwhere((model.mobile_phone==account) | (model.user_name==account))
        query = query.where(model.is_job_number==False)
        registered = await engine.access.list_obj(model, query)
        registered_uuid = [account["user_uuid"] for account in registered]
        
        # TODO 
        registered_uuid = list()
        tenant_users = list()
        for account in registered:
            registered_uuid.append(account["user_uuid"])
            
            # TODO 需要考虑 有些字段复制到TenantUser之后的field unique问题
            # TODO 删除之后重新邀请的问题
            this_user_info = {
                "user_uuid": account["user_uuid"], 
                "full_name": account["full_name"], 
                "mobile_phone": account["mobile_phone"], 
                "job_number": account["user_name"],   # TODO 从User表拿来的数据 job_number应该是什么
                "email": "", 
                "created_at": account["created_at"], 
                "password_salt": account["password_salt"], 
                "password_hash": account["password_hash"], 
                "lemon_user_uuid": account["user_uuid"]
            }
            tenant_users.append(this_user_info)

        if tenant_users:
            await engine.access.add_tenant_members(registered_uuid, tenant_uuid, is_admin=False)
            await engine.access.add_tenant_users(tenant_users, tenant_uuid)
            # apps = await engine.access.list_tenant_relation_app(tenant_uuid)
            # # TODO 如果当时rulechain进程挂了
            # message = {
            #     "tenant_users": tenant_users,
            #     "tenant_uuid": tenant_uuid
            # }
            # for app in apps:
            #     app_uuid = app.get("app_uuid")
            #     channel = ":".join([app_uuid, RedisChannel.TENANT])
            #     await engine.redis.redis.publish(channel, ujson.dumps(message))
        return json(LDR(data=registered_uuid))


class UpdateAccountStatus(HTTPMethodView):
    class UpdateAccountStatusObj:
        organization_id = doc.String("企业uuid")
        account_list = doc.String("账号uuid列表")
        action = doc.String("0停用，1启用，2删除")

    async def do_disable(self, tenant_uuid, exist_member):
        """停用"""
        data = {TenantMember.is_active.name: False}
        success = list()
        assign_dict = await self.get_assign_dict(tenant_uuid)
        assign_type = NodeAssignType.DISABLE

        async with engine.db.objs.atomic():
            for member in exist_member:
                async with engine.access.check_department_admin(
                        user_uuid=member.member_uuid, tenant_uuid=tenant_uuid):
                    await engine.db.objs.execute(member.update(**data).where(member._pk_expr()))
                success.append(member.member_uuid)

            await self.process_user_workflow(tenant_uuid, success, assign_dict, assign_type)
        return success

    async def do_enable(self, tenant_uuid, exist_member):
        """启用"""
        data = {TenantMember.is_active.name: True}
        
        success = list()
        for member in exist_member:
            async with engine.access.check_department_admin(
                user_uuid = member.member_uuid, tenant_uuid=tenant_uuid):
                await engine.db.objs.execute(member.update(**data).where(member._pk_expr()))
            success.append(member.member_uuid)
        return success

    async def do_delete(self, tenant_uuid, exist_member):
        """删除"""
        success = list()
        assign_type = NodeAssignType.DELETE
        assign_dict = await self.get_assign_dict(tenant_uuid)
        async with engine.db.objs.atomic():
            for member in exist_member:
                async with engine.access.check_department_admin(
                        user_uuid=member.member_uuid, tenant_uuid=tenant_uuid):
                    await engine.db.objs.execute(member.delete().where(member._pk_expr()))
                    member_uuid = member.member_uuid
                    await engine.access.del_tenant_department_user(tenant_uuid, member_uuid, del_user=True)
                    role_member_models = await self.process_model_database(RoleMember)
                    for models in role_member_models:
                        for r_model in models:
                            if r_model:
                                query = r_model.delete().where(
                                    r_model.member_uuid == member.member_uuid,
                                    r_model.tenant_uuid == tenant_uuid)
                                await engine.access.delete_obj_by_query(r_model, query)
                success.append(member.member_uuid)
            await self.process_user_workflow(tenant_uuid, success, assign_dict, assign_type)
        return success

    async def process_user_workflow(self, tenant_uuid, user_uuids, assign_dict, assign_type):
        # TODO self.token能否访问每个应用
        for app_uuid in self.app_uuids:
            for env in ["", "test"]:
                app_uuid += env
                domain = engine.config.APP_DOMAIN
                url = "/".join([domain, app_uuid, tenant_uuid, "api/runtime/v1/handle_assign_workflow.json"])
                # url = "http://127.0.0.1:8002/api/runtime/v1/handle_assign_workflow.json"
                app_log.info(url)
                body = {
                    "user_uuids": user_uuids,
                    "assign_info": assign_dict,
                    "assign_type": assign_type,
                    "tenant_uuid": tenant_uuid
                }
                asyncio.create_task(self.send_request(url, body))

    async def send_request(self, url, body):
        async with ClientSession(trust_env=True) as session:
            await session.post(url, json=body, headers={"Authorization": "Bearer " + self.token})

    async def get_assign_dict(self, tenant_uuid):
        """获取工作流新指派的人"""
        async with engine.db.objs.atomic():
            all_members = await engine.access.list_tenant_member(tenant_uuid)
            all_departments = await engine.access.list_department_by_tenant_uuid(tenant_uuid)
            all_admins = await engine.access.list_admin_by_tenant_uuid(tenant_uuid)

        all_user_uuid, all_admin_dict, all_admin_uuid = set(), dict(), set()
        for member_dict in all_members:
            t_user_uuid = member_dict.get(TenantUser.user_uuid.name)
            if t_user_uuid:
                all_user_uuid.add(t_user_uuid)
        for admin_dict in all_admins:
            t_user_uuid = admin_dict.get(TenantMember.member_uuid.name)
            if t_user_uuid:
                all_admin_dict[t_user_uuid] = admin_dict
                all_admin_uuid.add(t_user_uuid)

        assign_dict = dict()
        for account_dict in self.account_list:
            account = account_dict.get("account")
            if account in all_admin_dict:
                del all_admin_dict[account]
        if not all_admin_dict:
            raise Exception(Code.ORGANIZATION_ADMIN_CAN_NOT_NULL)

        first_admin = list(all_admin_dict.values())[0]
        first_admin_uuid = first_admin.get(TenantMember.member_uuid.name)
        for account_dict in self.account_list:
            account = account_dict.get("account")
            task_assign_type = account_dict.get("type")
            assign_user = None
            if account in all_admin_uuid:
                assign_user = first_admin_uuid
            else:
                if task_assign_type == 0:  # manager
                    assign_user = manager_of_by_user_uuid(
                        account, tenant_uuid, all_members=all_members, all_departments=all_departments,
                        all_admins=all_admins)
                elif task_assign_type == 1:  # user_uuid
                    assign_user = account_dict.get("user")
                    if assign_user not in all_user_uuid:
                        assign_user = manager_of_by_user_uuid(
                            account, tenant_uuid, all_members=all_members, all_departments=all_departments,
                            all_admins=all_admins)
            if assign_user:
                if isinstance(assign_user, list):
                    assign_user = assign_user[0]
                assign_dict.update({account: assign_user})
        return assign_dict

    async def process_model_database(self, base_model):
        from peewee import RawQuery
        query = RawQuery(sql="SHOW DATABASES;", params=None, _database=engine.db.database).dicts()
        database_list = await engine.db.objs.execute(query)  # like: [(), (), ]
        database_list = [d.get("Database") for d in list(database_list)]
        app_log.info(database_list)
        result = list()
        for u in self.middle_users:
            db_name = u
            db_name_test = u + "test"
            model, model_test = None, None
            if db_name in database_list:
                # TODO: memory leaks !
                model = make_model_copy(base_model)
                model._meta.schema = db_name
            if db_name_test in database_list:
                # TODO: memory leaks !
                model_test = make_model_copy(base_model)
                model_test._meta.schema = db_name_test
            result.append((model, model_test))
        return result

    @doc.consumes(UpdateAccountStatusObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("更新用户账号状态")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            self.account_list = request.json.get("account_list", [])
            action = int(request.json.get("action", 0))
            self.token = request.token
            self.app_env = int(request.headers.get("appEnv", 0))
            if not self.account_list:
                return json(LDR(Code.ARGS_ERROR))

        apps = await engine.access.list_tenant_relation_app(tenant_uuid)
        self.middle_users = {p["user_uuid"] for p in apps}
        self.app_uuids = {p["app_uuid"] for p in apps}

        model = TenantMember
        account_query = model.select()
        for account in self.account_list:
            account: dict
            account = account.get("account")
            account_query = account_query.orwhere(model.member_uuid == account)
        account_query = account_query.where(model.tenant_uuid == tenant_uuid)
        exist_member: TenantMember = await engine.access.list_obj(
            model, account_query, need_delete=True, as_dict=False)  # 本次操作的account对象
        success = list()
        async with engine.db.objs.atomic():
            try:
                async with engine.access.check_tenant_admin(tenant_uuid):
                    if action == 0:
                        success = await self.do_disable(tenant_uuid, exist_member)
                    elif action == 1:
                        success = await self.do_enable(tenant_uuid, exist_member)
                    elif action == 2:
                        success = await self.do_delete(tenant_uuid, exist_member)
            except RemoveMemberError:
                return json(LDR(Code.ORGANIZATION_ADMIN_CAN_NOT_NULL))
        return json(LDR(data=success))


class CreateDepartment(HTTPMethodView):
    class CreateDepartmentObj:
        organization_id = doc.String("企业uuid")
        department_name = doc.String("部门名称")
        current = doc.String("当前部门")
        create_type = doc.String("0同级，1子级")

    @doc.consumes(CreateDepartmentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("添加部门")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # tenant_uuid = get_tenant_uuid(request)
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            dm_name = str(request.json.get("department_name", ""))
            current = str(request.json.get("current", ""))
            create_type = int(request.json.get("create_type", 0))

        if current == "":
            if create_type == 0:
                return json(LDR(Code.INVALID_DEPARTMENT))
            parent = None
            level = 0
        else:
            current_dm = await engine.access.get_department_by_department_uuid(current, tenant_uuid)
            if not current_dm:
                return json(LDR(Code.INVALID_DEPARTMENT))
            if create_type == 1:  # 子部门
                tenant_info = await engine.access.get_tenant_by_tenant_uuid(tenant_uuid)
                if not tenant_info.setting.get("allow_da_add"):
                    return json(LDR(Code.SUB_DEPARTMENT_ADD_INVALID))
                parent = current_dm.id
                level = current_dm.department_level + 1
            else:  # 同级部门
                # parent = current_dm.上级部门
                parent = getattr(current_dm, UUID_TenantDepartment.parent.value, None)
                level = current_dm.department_level

        data = dict(
            department_uuid=lemon_uuid(),
            department_name=dm_name,
            parent=parent,
            department_level=level
            )
        app_log.info(data)
        model = make_tenant_sys_table_copy(TenantDepartment, tenant_uuid)
        await engine.access.create_obj(model, **data)
        return json(LDR(Code.OK))
        
class UpdateDepartment(HTTPMethodView):
    class UpdateDepartMentObj:
        organization_id = doc.String("企业uuid")
        department = doc.String("部门uuid")
        name = doc.String("部门名, key是name, 不是name_")

    @doc.consumes(UpdateDepartMentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("更新部门")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # tenant_uuid = get_tenant_uuid(request)
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            dm_uuid = str(request.json.get("department", ""))
            name = str(request.json.get("name", ""))

        dm = await engine.access.get_department_by_department_uuid(dm_uuid, tenant_uuid)
        if not dm:
            return json(LDR(Code.INVALID_DEPARTMENT))
        tenant_info = await engine.access.get_tenant_by_tenant_uuid(tenant_uuid)
        if not tenant_info.setting.get("allow_da_edit"):
            return json(LDR(Code.DEPARTMENT_NAME_EDIT_INVALID))
        
        model: TenantDepartment = make_tenant_sys_table_copy(TenantDepartment, tenant_uuid)
        query = model.update(department_name=name).where(model.department_uuid==dm_uuid)
        await engine.access.update_obj_by_query(model, query)
        return json(LDR(Code.OK))

class RemoveDepartment(HTTPMethodView):
    class RemoveDepartmentObj:
        organization_id = doc.String("企业uuid")
        department = doc.String("部门uuid")

    @doc.consumes(RemoveDepartmentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除部门")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # tenant_uuid = get_tenant_uuid(request)
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            dm_uuid = str(request.json.get("department", ""))

        dm_info = await engine.access.get_department_by_department_uuid(dm_uuid, tenant_uuid)
        if not dm_info:
            return json(LDR(Code.INVALID_DEPARTMENT))
                   
        all_departments = await engine.access.list_department_by_tenant_uuid(
            tenant_uuid, need_delete=True)
        departments_parent2dm = {}
        for dm in all_departments:
            parent = dm["parent_department_uuid"]
            if parent:
                sd: list = departments_parent2dm.setdefault(parent, list())
                sd.append(dm)        
        children_uuid = []
        process_sub_dm(dm_uuid, departments_parent2dm, children_uuid)
        children = list(filter(lambda x: x[TenantDepartment.department_uuid.name] in children_uuid, all_departments))
        app_log.debug(children)
        async with engine.db.objs.atomic():
            for child in children[::-1]:
                await engine.access.del_tenant_department(tenant_uuid, child["department_uuid"])
            await engine.access.del_tenant_department(tenant_uuid, dm_uuid)
        return json(LDR(Code.OK))

class GetDepartment(HTTPMethodView):
    class GetDepartmentObj:
        organization_id = doc.String("企业uuid")

    @doc.consumes(GetDepartmentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description(    """
        data: [
            {
            "name": "department1",
            "uuid": "xxxx",
            "children": [
                {
                    "name": "department2",
                    "uuid": "xxxx"
                },
                {
                    "name": "department3",
                    "uuid": "xxxx",
                    "children": [
                        {
                            "name": "department4",
                            "uuid": "xxxx"
                        },
                    ]
                }
            ]
        }
        ]
    """)
    @doc.tag(url_prefix)
    @doc.summary("获取部门")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # tenant_uuid = get_tenant_uuid(request)
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)

        @dataclass
        class DepartmentInfo:
            name: str
            uuid: str
            children: list = field(default_factory=dict)

        def is_child(uuid, dm_info):
            for dm in dm_info.children:
                if uuid == dm.uuid:
                    return True
                if dm_info.children:
                    if is_child(uuid, dm):
                        return True
            return False

        department_uuid_map = {}
        top_department = []

        department_list = await engine.access.list_department_by_tenant_uuid(tenant_uuid)  
        for department in department_list:
            dm_info = DepartmentInfo(department["department_name"], department["department_uuid"], [])
            department_uuid_map[department["department_uuid"]] = dm_info
            
            if department["department_level"] == 0:
                top_department.append(dm_info)
            else:
                if department["parent_department_uuid"] in department_uuid_map:
                    department_uuid_map[department["parent_department_uuid"]].children.append(dm_info)
        out = [asdict(dm) for dm in top_department]
        return json(LDR(data=out))


class GetDepartmentMember(HTTPMethodView):
    class GetDepartmenMembertObj:
        organization_id = doc.String("企业uuid")
        department = doc.String("部门uuid")

    @doc.consumes(GetDepartmenMembertObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取部门成员")
    async def post(self, request):
        # todo: 分页,排序,标签
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # tenant_uuid = get_tenant_uuid(request)
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            department = request.json.get("department", "")
            only_active = request.json.get("only_active", False)
        members = await engine.access.list_department_user(department, tenant_uuid, active=only_active)
        return json(LDR(data=list(members)))

class AddDepartmentMember(HTTPMethodView):
    class AddDepartmenMembertObj:
        organization_id = doc.String("企业uuid")
        department = doc.String("部门uuid")
        account_list = doc.String("账号uuid列表")

    @doc.consumes(AddDepartmenMembertObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("添加部门成员")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # tenant_uuid = get_tenant_uuid(request)
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            department = str(request.json.get("department", ""))
            account_list = request.json.get("account_list", [])
            if not account_list:
                return json(LDR(Code.ARGS_ERROR))

        dm = await engine.access.get_department_by_department_uuid(department, tenant_uuid)
        if not dm:
            return json(LDR(Code.INVALID_DEPARTMENT))

        model: TenantDepartmentMember = make_tenant_sys_table_copy(TenantDepartmentMember, tenant_uuid)
        department_member_infos = await engine.access.list_department_by_user(tenant_uuid, account_list, as_dict=True)
        exist_member = set()
        exist_member_uuid = set()
        exist_other_major_department_user = set() # 部门成员已经有其他主部门
        for dep in department_member_infos:
            if dep.get("department_uuid") == department:
                exist_member.add(dep)
                exist_member_uuid.add(dep["user_uuid"])
            if dep["is_major"] and dep["department_uuid"] != department and not dep["is_delete"]:
                exist_other_major_department_user.add(dep["user_uuid"])
        
        task_list = []
        for exist in exist_member:
            # 当前不存在主部门, 设为主部门
            if exist["user_uuid"] in exist_other_major_department_user:
                be_major_department = False
            else:
                be_major_department = True
            update_data = {
                    model.is_admin.column_name: 0, 
                    model.is_delete.column_name: False, 
                    model.is_major.column_name: be_major_department
                }
            query = model.update(**update_data).where(model.id==exist["id"])
            task_list.append(
                engine.access.update_obj_by_query(
                    model, query, need_delete=True))
        to_add = set(account_list) - exist_member_uuid
        new_member = []
        department_obj = await engine.access.get_department_by_department_uuid(department, tenant_uuid)
        department_pk = department_obj.id
        if to_add:
            valid_account = await engine.access.list_tenant_member_join_tenant_user(
                tenant_uuid, to_add, need_delete=True, as_dict=False)
            new_member = [account.member_uuid for account in valid_account]
            for member in valid_account:
                user_pk = member.tenantusercopy.user_pk
                member = member.member_uuid
                if member in exist_other_major_department_user:
                    be_major_department = False
                else:
                    be_major_department = True
                data = dict(
                    department=department_pk,
                    user=user_pk,
                    is_admin=0, 
                    is_major=be_major_department
                )
                task_list.append(engine.access.create_obj(model, **data))
        async with engine.db.objs.atomic():
            await asyncio.gather(*task_list)
        query = model.select().where(model.department==department_pk, model.is_admin==2)
        department_director = await engine.access.list_obj(model, query, as_dict=False)
        if not department_director:
            query = model.update(is_admin=2).where(model.department==department_pk).limit(1)
            await engine.access.update_obj_by_query(model, query)
        return json(LDR(data=new_member + list(exist_member_uuid)))

class RemoveDepartmentMember(HTTPMethodView):
    class RemoveDepartmenMembertObj:
        organization_id = doc.String("企业uuid")
        department = doc.String("部门uuid")
        account_list = doc.String("账号uuid列表")

    @doc.consumes(RemoveDepartmenMembertObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("移除部门成员")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # tenant_uuid = get_tenant_uuid(request)
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            department = str(request.json.get("department", ""))
            account_list = request.json.get("account_list", [])
            if not account_list:
                return json(LDR(Code.ARGS_ERROR))
        try:
            async with engine.access.check_department_admin(department, tenant_uuid=tenant_uuid):
                # 被移除的用户如果是主部门, 为其另外的部门设置一个为主部门
                next_major_department = await self.gen_next_major_department(
                    tenant_uuid, account_list, department)
                update_funcs = list()
                model: TenantDepartmentMember = make_tenant_sys_table_copy(TenantDepartmentMember, tenant_uuid)
                for dep in next_major_department:
                    update_query = engine.access.update_obj_by_query(
                        model, model.update(is_major=True).where(model.id==dep["id"])
                    )
                    update_funcs.append(update_query)
                # 移除的成员包含部门主管, 需要从剩下的人当中挑选出一位作为部门主管
                next_dep_admin, cur_dep_admin_being_delete = await self.gen_next_department_admin(
                    tenant_uuid, account_list, department
                )
                if cur_dep_admin_being_delete and next_dep_admin:
                    next_dep_admin_func = engine.access.update_obj_by_query(
                        model, model.update(is_admin=2).where(model.id==next_dep_admin))
                    update_funcs.append(next_dep_admin_func)
                update_funcs.append(
                    engine.access.del_tenant_department_user(
                        tenant_uuid, account_list, department_uuid=department, del_user=False))
                async with engine.db.objs.atomic():
                    await asyncio.gather(*update_funcs)
        except RemoveMemberError:
            return json(LDR(Code.DEPARTMENT_DIRECTOR_CAN_NOT_NULL))
        return json(LDR(Code.OK))
    
    async def gen_next_department_admin(self, tenant_uuid, account_list, department):
        department_members = await engine.access.list_department_member(department, tenant_uuid)
        next_dep_admin = ""
        cur_dep_admin_being_delete = False
        for m in list(department_members)[::-1]:
            if m["user_uuid"] not in account_list:
                next_dep_admin = m["id"]
            else:
                if m["is_admin"] == 2:
                    cur_dep_admin_being_delete = True
        return next_dep_admin, cur_dep_admin_being_delete
    
    async def gen_next_major_department(self, tenant_uuid, account_list, department):
        """
        获取因为移除主部门, 需要被选为主部门的用户其他部门
        """
        dep_members = await engine.access.list_department_by_user(
            tenant_uuid, account_list, as_dict=True)
        
        dep_members_dict = dict()
        for dep in dep_members:
            dep_members_dict.setdefault(dep["user_uuid"], list())
            dep_members_dict[dep["user_uuid"]].append(dep)
        
        need_set_major: list[DepartmentMember] = list()
        for user_uuid, members in dep_members_dict.items():
            if len(members) > 1:  # 用户存在其他部门
                this_dep_is_major = list(filter(
                    lambda x: x["department_uuid"] == department and x["is_major"], members))
                if this_dep_is_major:
                    next_major_dep = filter(
                    lambda x: x["department_uuid"] != department, members).__next__()
                    need_set_major.append(next_major_dep)
        return need_set_major
            
        

# class UpdateDepartmentMember(HTTPMethodView):
#     class UpdateDepartmentMemberObj:
#         tenant_uuid = doc.String("租户uuid")
#         department = doc.String("部门uuid")
#         account_uuid = doc.String("账号uuid")
#         job = doc.String("职务")
#         is_admin = doc.String("是否是管理员")
#         full_name = doc.String("姓名")
#         is_active = doc.String("是否启用")
        
    
#     @doc.consumes(UpdateDepartmentMemberObj, content_type=DOC.JSON_TYPE, location="body", required=True)
#     @doc.consumes(
#         doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
#         location="header", required=True)   
#     @doc.description("")
#     @doc.tag(url_prefix)
#     @doc.summary("编辑部门成员")
#     async def post(self, request):
#         with process_args():
#             user_uuid = request.ctx.session.get("user_uuid")
#             app_uuid = get_app_uuid(request)
#             tenant_uuid = get_tenant_uuid(request)
#             department = str(request.json.get("department", ""))
#             account_uuid = str(request.json.get("account_uuid", ""))
#             job = str(request.json.get("job", ""))
#             is_admin = str(request.json.get("is_admin", ""))

#         model = DepartmentMember
#         query = model.update(job=job, is_admin=is_admin).where(
#             model.department_uuid==department, model.tenant_uuid==tenant_uuid, model.user_uuid==account_uuid
#             )
#         await engine.user_access.update_obj_by_query(model, query)
#         return json(LDR(Code.OK))


class UpdateMemberInfo(HTTPMethodView):
    class UpdateMemberInfo_department:
        job = doc.String("职务")
        is_admin = doc.Integer("是否部门主管---2/子管理员----1/普通成员-----0")
        is_major = doc.Integer("是否主部门") 

    class UpdateMemberInfoObj:
        organization_id = doc.String("企业uuid")
        member = doc.String("成员uuid")
        full_name = doc.String("姓名")
        active = doc.String("启停状态")
    UpdateMemberInfoObj.departments = doc.Dictionary({"department_uuid": UpdateMemberInfo_department})


    @doc.consumes(UpdateMemberInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("更新成员信息")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # tenant_uuid = get_tenant_uuid(request)
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            member_uuid = request.json.get("member", "")
            full_name = request.json.get("full_name", "")
            email = request.json.get("email", "")
            active = request.json.get("active")
            departments = request.json.get("departments", {})
        # if not check_lemon_name(full_name, chinese=True):
        #     return json(LDR(Code.FULL_NAME_INVALID))
        if email and not check_email(email):
            return json(LDR(Code.EMAIL_INVALID))
        user = await engine.access.is_tenant_member(member_uuid, tenant_uuid)
        if not user:
            return json(LDR(Code.USER_NOT_EXISTS))
        is_major = 0
        for dep_uuid, dm in departments.items():
            if not check_lemon_uuid(dep_uuid):
                return json(LDR(Code.INVALID_DEPARTMENT))
            is_major += int(dm.get("is_major", 0))
        if is_major > 1:
            return json(LDR(Code.MAJOR_DEPARTMENT_CAN_NOT_BE_MULTIPLE))
        model: TenantUser = make_tenant_sys_table_copy(TenantUser, tenant_uuid)
        member = await engine.access.get_tenant_user_by_email(email, tenant_uuid, member_uuid)
        if email and member:
            return json(LDR(Code.USER_EMAIL_EXISTS))
        # full_name 是允许重复的
        update_data = {
            "full_name" : full_name, 
            "email" : email
        }
        await engine.access.update_obj_by_query(model, model.update(**update_data).where(model.user_uuid==member_uuid))
        if active is not None:
            await engine.access.update_obj_by_query(TenantMember, user.update(is_active=active).where(user._pk_expr()))
        user_department = await engine.access.list_user_department_base(
            member_uuid, tenant_uuid, need_delete=True)        
        exist_uuid = set([dm["department_uuid"] for dm in user_department])
        new_dm_uuid = set(departments.keys())
        to_del = exist_uuid - new_dm_uuid
        to_update = exist_uuid & new_dm_uuid
        to_add = new_dm_uuid - exist_uuid
        
        exist_id = set([dm["dep_id"] for dm in user_department])
        result_code = await engine.access.update_tenant_user_department(
            to_add, to_update, to_del, departments, member_uuid, tenant_uuid, exist_id)
        

        return json(LDR(result_code))

class GetMemberInfo(HTTPMethodView):
    class GetMemberInfoObj:
        organization_id = doc.String("企业uuid")
        current = doc.String("当前部门uuid")
        members = doc.List("成员uuid 列表")
        app_uuid = doc.String("应用uuid")

    @doc.consumes(GetMemberInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("""
        data: {
            "member_uuid": {
                "member_name": xxx,
                "user_uuid": xxx,
                "full_name": xxx,
                "mobile_phone": xxx,
                "departments": ["department1"],
                "job": ["job1"],
                "roles": ["role1"],
                "department_roles": [],
                "tags": ["is_sys_admin", "is_department_admin", "is_department_director"],
                "account_status": 0,  # 0正常 1停用
                "member_type": 0    # 0 人员 1部门
            }
        }
        """)
    @doc.tag(url_prefix)
    @doc.summary("获取成员信息")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # app_uuid = get_app_uuid(request)
            # tenant_uuid = get_tenant_uuid(request, "organization_id")
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            current = request.json.get("current", "")
            members = request.json.get("members", [])
            hide_mobile = request.json.get("hide_mobile", True)
            app_env = int(request.headers.get("appEnv"), 0)
        output = {}

        for member in members:
            user_info = await engine.access.is_tenant_member(member, tenant_uuid)
            if not user_info:
                member_info = await engine.access.get_department_by_department_uuid(member, tenant_uuid)
                if not member_info:
                    continue
                else:
                    role_info = await engine.access.list_member_role_info(member, tenant_uuid)
                    member_info = member_info.to_dict()
                    member_info.update({
                        "department": [member_info["department_name"]], 
                        "full_name": [member_info["department_name"]],
                        "roles": [r["role_name"] for r in role_info]
                    })
                    output.update({member: member_info})
                    continue
            
            member_info = await engine.access.get_tenant_member_detail(
                member, tenant_uuid, hide_mobile=hide_mobile)
            app_log.debug(member_info)
            tags = set()
            department_roles = []
            if app_uuid:
                # user_roles = await engine.access.list_user_role_by_user_uuid(
                #     member, tenant_uuid, app_uuid, app_env)
                user_roles = []
                
                user_roles_list = []
                for role in user_roles:
                    user_roles_list.append(role["role_name"])
                    if role.get("is_admin"):
                        tags.add("is_sys_admin")
                member_info.update(dict(roles=user_roles_list))
            is_admin_list = (member_info.pop("is_department_admin", "") or "").split("|")
            department_uuids = []
            if member_info.get("department_uuid", ""):
                department_uuids = member_info.pop("department_uuid", "").splitlines(keepends=False)
                for idx, dm_uuid in enumerate(department_uuids):
                    department_role = int(is_admin_list[idx])
                    department_roles.append(department_role)
                    if current == dm_uuid:
                        if department_role == 1:
                            tags.add("is_department_admin")
                        elif department_role == 2:
                            tags.add("is_department_director")
            member_info["department_uuids"] = department_uuids
            member_info["department"] = [] if not member_info.get("department") else member_info["department"].splitlines(keepends=False) 
            if member_info["department"]:
                member_info["job"] = [""] if not member_info.get("job") else member_info["job"].split("\n")
            else:
                member_info["job"] = []
            member_info["is_major"] = [] if not member_info.get("is_major") else member_info["is_major"].splitlines(keepends=False)    
            member_info["is_major"] = list(map(int, member_info["is_major"]))
            member_info.update(dict(tags=list(tags)))
            member_info.update(dict(department_roles=department_roles))
            output.update({member: member_info})
        app_log.debug(output)
        return json(LDR(data=output))

class SearchUser(HTTPMethodView):
    class SearchUserObj:
        organization_id = doc.String("企业uuid")
        keyword = doc.String("关键字")
        type = doc.String("department或role")
        app_uuid = doc.String("应用uuid")

    @doc.consumes(SearchUserObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("搜索用户")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            # app_uuid = get_app_uuid(request)
            # tenant_uuid = get_tenant_uuid(request, "organization_id")
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            keyword = request.json.get("keyword", "")
            result_type = request.json.get("type", "department")
            if not keyword:
                return json(LDR(Code.ARGS_ERROR)) 
        if result_type == "department":
            tenant_uuid_list = [tenant_uuid]
            result = await engine.access.list_tenant_member_join_department(
                keyword=keyword, tenant_uuid_list=tenant_uuid_list, group=False, hide_mobile=True)
            result = list(result)
            for res in result:
                res["department"] = [] if not res.get("department") else res["department"].splitlines(keepends=False) 
                res["job"] = [] if not res.get("job") else res["job"].splitlines(keepends=False)
        else:
            if not app_uuid:
                return json(LDR(Code.ARGS_ERROR))
            result = await engine.access.list_user_join_role(
                keyword=keyword, tenant_uuid=tenant_uuid, app_uuid=app_uuid, group=False, 
                hide_mobile=True)
        return json(LDR(data=list(result)))

class UpdateUser(HTTPMethodView):
    class UpdateUserObj:
        full_name = doc.String("姓名")
    @doc.consumes(UpdateUserObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("搜索用户")
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            full_name = request.json.get("full_name", "")
            if not full_name:
                return json(LDR(Code.ARGS_ERROR))
        data = dict(
            full_name=full_name
        )
        await engine.access.update_user_by_user_uuid(user_uuid, **data)
        return json(LDR(Code.OK))


class GetMemberManager(HTTPMethodView):
    class GetMemberManagerObj:
        members = doc.String("用户UUID")
    @doc.consumes(GetMemberManagerObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取用户直属上级")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            members = request.json.get("members", list())
            if not members:
                user_uuid = request.ctx.session.get("user_uuid")
                members = [user_uuid]
        
        async with engine.db.objs.atomic():
            all_members = await engine.access.list_tenant_member(tenant_uuid)
            all_departments = await engine.access.list_department_by_tenant_uuid(tenant_uuid)
            all_admins = await engine.access.list_admin_by_tenant_uuid(tenant_uuid)
        
        all_member_dict = dict()
        for member_dict in all_members:
            this_user_uuid = member_dict.get(DepartmentMember.user_uuid.name)
            all_member_dict.update({this_user_uuid: member_dict})

        manager_dict = dict()
        for member in members:
            manager = manager_of_by_user_uuid(
                member, tenant_uuid, all_members=all_members, all_departments=all_departments, all_admins=all_admins)
            if manager and isinstance(manager, list):
                manager = manager[0]
                manager_info = all_member_dict.get(manager)
                manager_dict.update({member: manager_info})
        return json(LDR(data=manager_dict))


class CreateJobNumberUser(HTTPMethodView):

    class CreateJobNumberUserObj:
        tenant_uuid = doc.String("租户uuid")
        user_info = doc.List("用户信息")

    @doc.consumes(CreateJobNumberUserObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("创建工号用户")
    async def post(self, request, *args, **kwargs):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_info = request.json.get("user_info", [])
        if not check_lemon_uuid(tenant_uuid):
            return json(LDR(Code.ARGS_ERROR))

        tenant_info = await engine.access.get_tenant_by_tenant_uuid(tenant_uuid)
        if not tenant_info:
            return json(LDR(Code.ERROR))
        job_number_list = [d.get("job_number") for d in user_info if d.get("job_number")]
        mobile_phone_list = [d.get("mobile_phone") for d in user_info if d.get("mobile_phone")]

        exist_user = await engine.access.list_tenant_user_by_job_number_tenant_uuid(job_number_list, tenant_uuid)        
        exist_job_number = [u.get("job_number").lower() for u in exist_user]
        request_authorize_user = {u["job_number"]: u for u in exist_user}
        if mobile_phone_list:
            exist_user = await engine.access.list_tenant_user_by_mobile_phone_tenant_uuid(mobile_phone_list, tenant_uuid)
            exist_mobile_phone = [u.get("mobile_phone") for u in exist_user if u.get("mobile_phone")]
        else:
            exist_mobile_phone = list()
        # request_authorize_user = dict()

        tenant_users = list()
        failed_user = []
        successful_user = []
        need_rewrite_user = []
        for info in user_info:
            job_number = info.get("job_number", "")
            full_name = info.get("full_name", "")
            mobile_phone = info.get("mobile_phone", "")
            email = info.get("email", "")
            password = str(info.get("password", ""))
            user_id = info.get("user_id", "")
            if password:
                password_salt, password_hash = gen_password_salt_hash(password=password)
            else:
                password_salt = info.get("password_salt", "")
                password_hash = info.get("password_hash", "")
            user_uuid = info.get("user_uuid")
            if user_uuid:
                cur_user_exist = await engine.access.get_tenant_user_by_user_uuid(tenant_uuid, user_uuid)
                if cur_user_exist:
                    update_data = {
                        "full_name": info.get("full_name"),
                        "email": info.get("email"),
                        "job_number": info.get("job_number"),
                        "mobile_phone": info.get("mobile_phone"),
                    }
                    await engine.access.update_tenant_user(tenant_uuid, user_uuid, **update_data)
                    app_log.info(f"update user: {user_uuid}, update_data: {update_data}")
                    continue
            else:
                user_uuid = lemon_uuid()
            # job_member已存在, 认为获取授权成功  TODO user_uuid已存在呢?
            # 否则新建tenant_user, tenant_member
            if repeat_user := request_authorize_user.get(job_number):
                successful_user.append(info)
                repeat_user_info = {
                    "user_uuid": repeat_user.get("user_uuid"),
                    "lemon_user_uuid": repeat_user.get("lemon_user_uuid"),
                }
                need_rewrite_user.append({user_uuid: repeat_user_info})
                continue

            if not self.check_user_info(info, exist_job_number, exist_mobile_phone):
                failed_user.append(info)
                app_log.info(f"check failed: {info}")
                continue

            lemon_user_uuid = ""
            if mobile_phone:  # 需要绑定到柠檬平台
                lemon_user = await engine.access.get_user_by_mobile_phone(mobile_phone)
                if lemon_user:  # 绑定到
                    lemon_user_uuid = lemon_user.user_uuid
            this_user_info = {
                "user_uuid": user_uuid,
                "full_name": full_name,
                "mobile_phone": mobile_phone,
                "job_number": job_number,
                "email": email,
                "created_at": int(time.time()),
                "password_salt": password_salt,
                "password_hash": password_hash,
                "user_id": user_id
            }
            if lemon_user_uuid:
                origin_user_uuid = user_uuid
                user_uuid = lemon_user_uuid
                this_user_info.update({
                    "lemon_user_uuid": lemon_user_uuid,
                    "user_uuid": user_uuid
                })
                repeat_user_info = {
                    "user_uuid": user_uuid,
                    "lemon_user_uuid": lemon_user_uuid,
                }
                need_rewrite_user.append({origin_user_uuid: repeat_user_info})
            info["user_uuid"] = user_uuid
            tenant_users.append(this_user_info)
            successful_user.append(info)
        if tenant_users:
            await engine.access.add_tenant_users(tenant_users, tenant_uuid)
            success_user_uuid = [u["user_uuid"] for u in tenant_users]
            await engine.access.add_tenant_members(success_user_uuid, tenant_uuid, is_admin=False)
        data = {
            "successful_user": successful_user,
            "failed_user": failed_user,
            "need_rewrite_user": need_rewrite_user
        }
        return json(LDR(data=data))

    def check_user_info(self, info, exist_job_number, exist_mobile_phone):
        job_number = str(info.get("job_number", "")).lower()
        full_name = info.get("full_name", "")
        mobile_phone = info.get("mobile_phone", "")
        password = str(info.pop("password", ""))
        password_salt = info.get("password_salt", "")
        password_hash = info.get("password_hash", "")
        user_id = info.get("user_id")
        old_length = len(info)
        if not all([job_number, full_name, (password or (password_salt and password_hash))]):
            info.update({"message": Code.ARGS_ERROR.message})
        # elif not check_document_name(job_number):
        #     info.update({"message": Code.JOB_NUMBER_INVALID.message})
        # elif not check_lemon_name(full_name, chinese=True, size=20):
        #     info.update({"message": Code.FULL_NAME_INVALID.message})
        elif job_number in exist_job_number:
            info.update({"message": Code.USER_EXISTS.message})
        elif mobile_phone and mobile_phone in exist_mobile_phone:
            info.update({"message": Code.USER_EXISTS.message})
        elif user_id and any([len(str(user_id)) > 32]):
            info.update({"message": Code.TENANT_USER_ID_ERROR.message})
        exist_job_number.append(job_number)
        if mobile_phone:
            exist_mobile_phone.append(mobile_phone)
        new_length = len(info)
        user_success = new_length == old_length
        return user_success

    def gen_random_user_name(self):
        chars_hub = string.ascii_letters
        prefix = "".join(random.sample(chars_hub, 4))
        random_user_name = prefix + "{0:%m%d%H%M%S%f}".format(datetime.now())
        return random_user_name

class ResetJobNumberUserPassword(HTTPMethodView):
    
    class ResetJobNumberUserPasswordObj:
        tenant_uuid = doc.String("租户uuid")
        user_info = doc.List("")
        type = doc.String("department或role")

    @doc.consumes(ResetJobNumberUserPasswordObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)   
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("重置工号用户密码")
    async def post(self, request, *args, **kwargs):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_name = str(request.json.get("user_name", ""))
            job_number = str(request.json.get("job_number", ""))
            password = str(request.json.get("password", ""))
            
        user = await engine.access.get_tenant_user_by_job_number(tenant_uuid, job_number)
        app_log.info(user)
        user_uuid = user.user_uuid
        password_salt, password_hash = gen_password_salt_hash(password)
        update_data = {
            "password_salt": password_salt, 
            "password_hash": password_hash
        }
        await engine.access.update_tenant_user(tenant_uuid, user_uuid, **update_data)
                            
        # 删除所有用户历史 token
        keys = await engine.redis.get_session_keys(user_uuid)
        for key in keys:
            app_log.info(f"delete key: {key}")
            key = key.split(":")[2]
            await engine.redis.delete_session(key, user_uuid)
        return json(LDR(Code.OK))


bp.add_route(InviteUser.as_view(), "/invite_user.json")
bp.add_route(AddDepartmentMember.as_view(), "/add_department_member.json")
bp.add_route(CreateDepartment.as_view(), "/create_department.json")
bp.add_route(GetUser.as_view(), "/get_user.json")
bp.add_route(GetDepartment.as_view(), "/get_department.json")
bp.add_route(GetDepartmentMember.as_view(), "/get_department_member.json")
bp.add_route(RemoveDepartment.as_view(), "/remove_department.json")
bp.add_route(RemoveDepartmentMember.as_view(), "/remove_department_member.json")
bp.add_route(UpdateAccountStatus.as_view(), "/update_account_status.json")
bp.add_route(UpdateDepartment.as_view(), "/update_department.json")
bp.add_route(UpdateMemberInfo.as_view(), "/update_member_info.json")
bp.add_route(GetMemberInfo.as_view(), "/get_member_info.json")
bp.add_route(SearchUser.as_view(), "/search_user.json")
bp.add_route(UpdateUser.as_view(), "/update_user.json")
bp.add_route(GetMemberManager.as_view(), "/get_member_manager.json")
bp.add_route(CreateJobNumberUser.as_view(), "/create_job_number_user.json")
bp.add_route(ResetJobNumberUserPassword.as_view(), "/reset_job_number_user_password.json")


