from sanic_openapi import doc
import ujson
from peewee import JO<PERSON>
from apps.permission import Permission
from apps.middlewares import LemonVCHTTPMethodView as HTTPMethodView, LemonHTTPMethodView
from sanic.response import json, raw
from .util import bp, url_prefix
from apps.utils import (
    process_args, check_lemon_name, check_document_name, lemon_uuid, check_lemon_uuid,
    get_ext_tenant, restore_adapter
)
from baseutils.const import Code, DOC, Action, SyncTORepoType
from apps.engine import engine
from apps.utils import (
    LemonDictResponse as LDR)
from aiohttp import ClientSession
from apps.log import app_log
from apps.ide_const import DocumentType
from apps.entity import Document, DocumentContent


class GPTChat(HTTPMethodView):
    class GPTChatObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")

    @doc.consumes(GPTChatObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取分支状态")
    @Permission.policy(("app_uuid", Action.SELECT), )
    async def post(self, request):
        with process_args():
            sid = request.ctx.session.sid
            user_uuid = request.ctx.session.get("user_uuid")
            text = request.json.get("text")
            app_uuid = str(request.json.get("app_uuid", ""))
            context_model_uuid = request.json.get("context_model_uuid", "")
        url = 'http://localhost:9900/gpt_helper/v1/context_chat.json'
        app_log.info((sid, user_uuid, text))

        model_document: Document = await engine.access.get_document(
            **{"app_uuid": app_uuid, "document_type": DocumentType.MODEL}
        )
        dc: DocumentContent = await engine.access.get_document_content_by_document_uuid(model_document.document_uuid)

        system_model_document: Document = await engine.access.get_document_content_by_document_uuid(
            "86243de2388811eb983a00155dce904d"
        )
        async with ClientSession() as session:
            response = await session.post(url, json={
                "user_uuid": user_uuid, "sid": sid, "text": text,
                "context_model_uuid": context_model_uuid,
                "model_document": ujson.dumps(dc.document_content),
                "system_model_document": ujson.dumps(dc.document_content)
            })
            json_result = await response.json()
            if json_result.get("code") != Code.OK.code:
                raise
        return json(LDR(data=json_result['data']))


class GenerateCSS(HTTPMethodView):
    class GenerateCSSRequest:
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")

    @doc.consumes(GenerateCSSRequest, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取分支状态")
    @Permission.policy(("app_uuid", Action.SELECT), )
    async def post(self, request):
        with process_args():
            sid = request.ctx.session.sid
            user_uuid = request.ctx.session.get("user_uuid")
            user_instruction = request.json.get("user_instruction")
            current_css = request.json.get("current_css")
            html_structure = request.json.get("html_structure")
        url = 'http://localhost:9900/gpt_helper/v1/generate_css.json'

        async with ClientSession() as session:
            response = await session.post(url, json={
                "user_uuid": user_uuid, "user_instruction": user_instruction,
                "current_css": current_css,
                "html_structure": html_structure
            })
            json_result = await response.json()
            if json_result.get("code") != Code.OK.code:
                raise
        return json(LDR(data=json_result['data']))
