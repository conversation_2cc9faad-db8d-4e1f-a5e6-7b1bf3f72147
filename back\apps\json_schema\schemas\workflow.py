from apps.ide_const import NodeType
from apps.json_schema.schemas.utils import gen_const_condition_schema
from copy import deepcopy


page_selection = {
    "type": "object",
    "properties": {
        "page": {
            "$ref": "mem://common/page_ref"
        }
    }
}

node_event = {
    "type": "object",
    "properties": {
        "func_uuid": {
            "$ref": "mem://common/func_ref"
        }
    }
}

line = {
    "type": "object",
    "properties": {
        "settings": {
            "type": "object",
            "properties": {
                "value": {
                    "$ref": "mem://common/value_editor_all"
                }
            }
        }
    }
}

node_start = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": NodeType.START
        },
        "settings": {
            "type": "object",
            "properties": {
                "customize_wf_status": {
                    "$ref": "mem://common/value_editor_exclude_field"
                }
            }
        }
    }
}

node_end = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": NodeType.END
        },
        "settings": {
            "type": "object",
            "properties": {
                "customize_wf_status": {
                    "$ref": "mem://common/value_editor_exclude_field"
                }
            }
        }
    }
}

node_manual = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": NodeType.MANUAL
        },
        "settings": {
            "type": "object",
            "properties": {
                "mobile_to": {
                    "$ref": "mem://workflow/page_selection"
                },
                "pad_to": {
                    "$ref": "mem://workflow/page_selection"
                },
                "pc_to": {
                    "$ref": "mem://workflow/page_selection"
                },
                "total_to": {
                    "$ref": "mem://workflow/page_selection"
                },
                "customize_wf_status": {
                    "$ref": "mem://common/value_editor_exclude_field"
                }
            }
        },
        "events": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "in": {
                        "$ref": "mem://workflow/node_event"
                    },
                    "out": {
                        "$ref": "mem://workflow/node_event"
                    }
                }
            }
        },
        "messages": {
            "type": "object",
            "properties": {
                "open_messages": {
                    "type": "boolean"
                }
            },
            "if": {
                "properties": {
                    "open_messages": {
                        "const": True
                    }
                }
            },
            "then": {
                "properties": {
                    "messages_info": {
                        "$ref": "mem://common/value_editor_all"
                    },
                    "timeout_info": {
                        "type": "object",
                        "properties": {
                            "time_out_send": {
                                "type": "boolean"
                            }
                        },
                        "if": {
                            "properties": {
                                "time_out_send": {
                                    "const": True
                                }
                            }
                        },
                        "then": {
                            "properties": {
                                "time_out_model": {
                                    "$ref": "mem://common/model_ref"
                                },
                                "period_interval": {
                                    "$ref": "mem://common/value_editor_field"
                                }
                            }
                        }
                    }
                }
            }
        }
          
        
    }
}


node_auto = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": NodeType.AUTO
        },
        "settings": {
            "type": "object",
            "properties": {
                "func": {
                    "type": "object",
                    "properties": {
                        "uuid": {
                            "$ref": "mem://common/func_ref"
                        }
                    }
                },
                "withdraw_func": {
                    "type": "object",
                    "properties": {
                        "uuid": {
                            "$ref": "mem://common/func_ref"
                        }
                    }
                },
                "customize_wf_status": {
                    "$ref": "mem://common/value_editor_exclude_field"
                }
            }
        }
    }
}

node_copy = deepcopy(node_manual)
node_copy["properties"]["type"]["const"] = NodeType.COPY

node_subflow = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": NodeType.SUBFLOW
        },
        "subflow": {
            "type": "object",
            "properties": {
                "wf_uuid": {
                    "$ref": "mem://common/workflow_ref"
                },
                "submit_pk": {
                    "$ref": "mem://common/value_editor_all"
                }
            }
        }
    }
}

node_parallel = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": NodeType.PARALLEL
        },
        "branches": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "nodes": {
                        "allOf": [
                            gen_const_condition_schema(NodeType.START, "mem://workflow/node_start"),
                            gen_const_condition_schema(NodeType.END, "mem://workflow/node_end"),
                            gen_const_condition_schema(NodeType.MANUAL, "mem://workflow/node_manual"),
                            gen_const_condition_schema(NodeType.AUTO, "mem://workflow/node_auto"),
                            gen_const_condition_schema(NodeType.COPY, "mem://workflow/node_copy"),
                            gen_const_condition_schema(NodeType.SUBFLOW, "mem://workflow/node_subflow"),
                            gen_const_condition_schema(NodeType.PARALLEL, "mem://workflow/node_parallel")
                        ]
                    }
                }
            }
        }
    }
}

workflow = {
    "is_element": True,
    "type": "object",
    "attr_name": "工作流",
    "properties": {
        "versions": {
            "type": "array",
            "items": {
                "is_element": True,
                "attr_name": "工作流",
                "type": "object",
                "if": {
                    "properties": {
                        "status": {
                            "type": "number",
                            "const": 0,     # 只检查草稿状态
                        }
                    }
                },
                "then": {
                    "properties": {
                        "nodes": {
                            "type": "array",
                            "items": {
                                "allOf": [
                                    gen_const_condition_schema(NodeType.START, "mem://workflow/node_start"),
                                    gen_const_condition_schema(NodeType.END, "mem://workflow/node_end"),
                                    gen_const_condition_schema(NodeType.MANUAL, "mem://workflow/node_manual"),
                                    gen_const_condition_schema(NodeType.AUTO, "mem://workflow/node_auto"),
                                    gen_const_condition_schema(NodeType.COPY, "mem://workflow/node_copy"),
                                    gen_const_condition_schema(NodeType.SUBFLOW, "mem://workflow/node_subflow"),
                                    gen_const_condition_schema(NodeType.PARALLEL, "mem://workflow/node_parallel")
                                ]
                            }
                        },
                        "settings": {
                            "type": "object",
                            "properties": {
                                "total_to": {
                                    "$ref": "mem://workflow/page_selection"
                                },
                                "total_from": {
                                    "$ref": "mem://workflow/page_selection"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
