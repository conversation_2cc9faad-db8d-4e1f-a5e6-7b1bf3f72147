from apps.services import DocumentCheckerService
from apps.services.checker import checker


class AppLayoutDocumentCheckerService(DocumentCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str,
            element: dict, document_version: int, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, None, *args, **kwargs)
        self.document_version = document_version

    @checker.run
    def check_theme(self):
        ...
