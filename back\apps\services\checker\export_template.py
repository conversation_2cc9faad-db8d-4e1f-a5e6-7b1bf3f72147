'''
Author: lv.jimin
Date: 2023-04-01 11:11:46
LastEditors: huangl
LastEditTime: 2023-07-27 17:17:52
Description: file content
FilePath: /lemon/back/apps/services/checker/export_template.py
'''
# -*- coding:utf-8 -*-

import os
import base64
import asyncio
from hashlib import md5

from openpyxl.utils import column_index_from_string

from baseutils.log import app_log
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.entity import ExportTemplate
from apps.ide_const import FieldType, ExportTemplateClass as TemplateAttr, Icon, ExportTemplateAttr
from apps.ide_const import LemonDesigner<PERSON>rrorCode as LDEC
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker
from apps.base_utils import check_sys_field



class ExportTemplateCheckerService(CheckerService):

    attr_class = TemplateAttr.ATTR
    uuid_unique_error = LDEC.EXPORT_TEMPLATE_UUID_UNIQUE_ERROR
    name_error = LDEC.EXPORT_TEMPLATE_NAME_FAILED
    name_unique_error = LDEC.EXPORT_TEMPLATE_NAME_NOT_UNIQUE
    
    allow_chinese_name = True  
    
    def initialize(self):
        super().initialize()
        template_url = self.element.get("template_url")
        state = self.element.get("state", False)
        template_type = self.element.get("type", 0)
        self.operation_type = self.element.get("operation_type", 0)
        model_uuid = self.element.get("model", "")
        self.model_uuid = model_uuid
        # 更新引用详情需要用到
        self.type = 998
        template_info = {"template_url": template_url,  "model": model_uuid}
        self.template = template_info
        self.template_item_name_uuid_set = set()
        self.state = state
        self.template_type = template_type
        self.app_model_dict = self.kwargs.get("app_model_dict")
        self.app_field_dict = self.kwargs.get("app_field_dict")
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
        self.unqiue_index_dict = {}
        self.column_index_set = set()
    
    def build_insert_query_data(self):
        return {
            ExportTemplate.app_uuid.name: self.app_uuid,
            ExportTemplate.module_uuid.name: self.module_uuid,
            ExportTemplate.document_uuid.name: self.document_uuid,
            ExportTemplate.template_uuid.name: self.element_uuid,
            ExportTemplate.template_name.name: self.element_name,
            ExportTemplate.template.name: self.template,
            ExportTemplate.state.name: self.state,
            ExportTemplate.template_type.name: self.template_type,
            ExportTemplate.operation_type.name: self.operation_type
        }
    
    def build_update_query_data(self):
        return { 
            ExportTemplate.template_name.name: self.element_name,
            ExportTemplate.template.name: self.template,
            ExportTemplate.state.name: self.state,
            ExportTemplate.is_delete.name: False,
            ExportTemplate.template_type.name: self.template_type
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return ExportTemplate.update(**query_data).where(
            ExportTemplate.template_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(template_uuid):
        return ExportTemplate.update(**{
                ExportTemplate.is_delete.name: True
            }).where(ExportTemplate.template_uuid==template_uuid)
    
    def check_modify(self, document_template_uuid_set):
        if self.element_uuid in document_template_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
                       
                       
class ExportTemplateDocumentCheckerService(DocumentCheckerService):
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, document_version: int, 
        app_template_list: list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, ExportTemplate, *args, **kwargs)
        self.template_list = self.element.get("data", list())
        self.app_template_uuid_set = set()
        self.document_template_name_set = set()
        self.document_template_uuid_set = set()
        self.document_template_update_list = list()
        self.document_template_delete_list = list()
        self.app_model_dict = kwargs.get("app_model_dict")
        self.app_field_dict = kwargs.get("app_field_dict")
        self.type = 999
        for template in app_template_list:
            template_uuid = template.get("template_uuid", "")
            template_document_uuid = template.get("document_uuid", "")

            # 找到原文档中所有的 模板 ，为了新增、更新、删除文档的 模板
            if template_document_uuid == self.document_uuid:
                self.document_template_uuid_set.add(template_uuid)
            else:
                self.app_template_uuid_set.add(template_uuid)
           
    @checker.run
    def check_template_list(self):
        this_document_template_name_set = set()
        this_document_template_uuid_set = set()
        model_uuid = self.element.get("data_source", {}).get("model", "")
        for template in self.template_list:
            template["model"] = model_uuid
            template_checker_service = ExportTemplateCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, 
                    self.document_uuid, self.document_name, template, is_copy=self.is_copy, 
                    app_model_dict=self.app_model_dict, app_field_dict=self.app_field_dict)
            template_uuid = template_checker_service.element_uuid
            this_document_template_uuid_set.add(template_uuid)
            try:
                template_checker_service.check_uuid()
                template_checker_service.check_uuid_unique(self.app_template_uuid_set)
                template_checker_service.check_name()
                template_checker_service.check_name_unique(this_document_template_name_set)
                template_checker_service.check_all()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(template_checker_service)
                raise e
            else:
                self.update_any_list(template_checker_service)

            # 找到新增 或 更新的 模板
            template_checker_service.check_modify(self.document_template_uuid_set)
            if template_checker_service.insert_query_list:
                self.document_insert_list.extend(template_checker_service.insert_query_list)
            if template_checker_service.update_query_list:
                self.document_update_list.extend(template_checker_service.update_query_list)

        # 找出删除的 模板 ，将其 is_delete 置为 True
        delete_template_uuid_set = self.document_template_uuid_set - this_document_template_uuid_set
        for this_template_uuid in delete_template_uuid_set:
            query = ExportTemplateCheckerService.build_delete_query(this_template_uuid)
            self.document_delete_list.append(query)
    
    @checker.run        
    def check_data_source(self):
        data_souce = self.element.get("data_source", {})
        model_uuid = data_souce.get("model", "")
        code = LDEC.MODEL_NOT_EXISTS
        model_detail = self.app_model_dict.get(model_uuid)
        attr = ExportTemplateAttr.DATASOURCE
        if not model_detail:
            code = LDEC.MODEL_NOT_EXISTS
            self._add_error_list(attr, code)
        else:
            self.update_reference_by_data_source(data_souce, self.element, attr)
