from apps.services.checker import CheckerService
import os
import base64


from baseutils.log import app_log
from apps.entity import Module<PERSON>ole
from apps.exceptions import CheckNameError, CheckUUI<PERSON>rror, CheckUUIDUniqueError
from apps.ide_const import Security, EntityType
from apps.ide_const import Lemon<PERSON>esigner<PERSON>rrorCode as LDEC
from apps.utils import check_permission
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker
from baseutils.const import PermissionAction

    

class FieldCheckerService(CheckerService):

    attr_class = Security.ATTR

    def initialize(self):
        super().initialize()
        self.permission = self.element.get("permission", "")

    def gen_uuid_error(self):
        attr = self.attr_class.FIELDS
        return_code = LDEC.FIELD_NOT_EXISTS_IN_THIS_MODEL
        self._add_error_list(attr, return_code)

    def check_permission(self):
        if not check_permission(self.permission):
            attr = self.attr_class.FIELDS
            return_code = LDEC.PERMISSION_FORMAT_ERROR
            self._add_error_list(attr, return_code)


class RelationCheckerService(CheckerService):

    attr_class = Security.ATTR

    def initialize(self):
        super().initialize()
        self.permission = self.element.get("permission", "")

    def gen_uuid_error(self):
        attr = self.attr_class.FIELDS
        return_code = LDEC.RELATION_NOT_EXISTS_IN_THIS_MODEL
        self._add_error_list(attr, return_code)

    def check_permission(self):
        if not check_permission(self.permission):
            attr = self.attr_class.FIELDS
            return_code = LDEC.RELATION_PERMISSION_FORMAT_ERROR
            self._add_error_list(attr, return_code)


class ModelCheckerService(CheckerService):

    attr_class = Security.ATTR

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, model_fields_map: dict,
        model_relations_map: dict, model_not_exist, 
        *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.model_fields_map = model_fields_map
        self.model_relations_map = model_relations_map
        self.model_not_exist = model_not_exist

    def initialize(self):
        super().initialize()
        self.fields = self.element.get("fields", {})
        self.model_field_list = None
        self.model_relation_list = None

    def check(self):
        attr = self.attr_class.MODEL
        return_code = LDEC.MODEL_NOT_EXISTS_IN_THIS_MODULE
        if self.element_uuid not in self.model_fields_map:
            self.model_not_exist.append(self.element_uuid)
        else:
            self.model_field_list = self.model_fields_map[self.element_uuid]
            self.model_relation_list = self.model_relations_map[self.element_uuid]

    def check_fields(self):
        if not self.model_field_list:
            self.model_field_list = list()
        if isinstance(self.fields, str):
            attr = self.attr_class.PERMISSION
            return_code = LDEC.PERMISSION_FORMAT_ERROR
            if not check_permission(self.fields):
                self._add_error_list(attr, return_code)
        elif isinstance(self.fields, dict):
            field_uuid_set =  set([field["field_uuid"] for field in self.model_field_list])
            relation_uuid_set = set([relation["relationship_uuid"] for relation in self.model_relation_list])
            this_document_field_uuid_set = set([field for field in self.fields if self.fields[field].get("type", EntityType.FIELD) == EntityType.FIELD])
            this_document_relation_uuid_set = set([field for field in self.fields if self.fields[field].get("type", EntityType.FIELD) == EntityType.RELATIONSHIP])
            error_field_uuid = this_document_field_uuid_set - field_uuid_set
            error_relation_uuid = this_document_relation_uuid_set - relation_uuid_set

            list(map(self.element.get("fields", {}).pop, error_relation_uuid))
            list(map(self.element.get("fields", {}).pop, error_field_uuid))
            normal_field_uuid = (field_uuid_set & this_document_field_uuid_set) | (relation_uuid_set & this_document_relation_uuid_set)
            for normal in normal_field_uuid:
                field = self.fields[normal]
                field.update({"uuid": normal})
                field_checker_service = FieldCheckerService(self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, self.document_name, field)
                field_checker_service.check_permission()
                self.update_any_list(field_checker_service)
        else:
            attr = self.attr_class.FIELDS
            return_code = LDEC.FIELD_PERMISSION_FORMAT_ERROR
            self._add_error_list(attr, return_code)
            
    def check_fields_permission(self):
        def _check_update_no_view(permission):
            attr = self.attr_class.PERMISSION
            return_code = LDEC.PERMISSION_UPDATE_NO_VIEW
            if int(permission, 2) == PermissionAction.UPDATE:
                self._add_error_list(attr, return_code)
        
        if isinstance(self.fields, str):
            _check_update_no_view(self.fields)
        elif isinstance(self.fields, dict):
            for field_uuid, p_info in self.fields.items():
                permission = p_info.get("permission")
                _check_update_no_view(permission)

class PageCheckerService(CheckerService):

    attr_class = Security.ATTR
    
    def __init__(self, app_uuid: str, module_uuid: str, module_name: str, 
                 document_uuid: str, document_name: str, element: dict,
                 page_not_exist, *args, **kwargs):
        super().__init__(app_uuid, module_uuid, module_name, document_uuid, document_name, element, *args, **kwargs)
        self.page_not_exist = page_not_exist

    def initialize(self):
        super().initialize()
        self.permission = self.element.get("action", "")
    
    def check(self, module_page_list):
        attr = self.attr_class.PAGE
        return_code = LDEC.PAGE_NOT_EXISTS_IN_THIS_MODULE
        page_info = [page for page in module_page_list if page["page_uuid"] == self.element_uuid]
        if not page_info:
            self.page_not_exist.append(self.element_uuid)
            # self._add_error_list(attr, return_code)

    def check_permission(self):
        if not check_permission(self.permission):
            attr = self.attr_class.PAGE
            return_code = LDEC.PERMISSION_FORMAT_ERROR
            self._add_error_list(attr, return_code)


class FuncCheckerService(CheckerService):

    attr_class = Security.ATTR
    uuid_error = LDEC.VARIABLE_UUID_ERROR
    uuid_unique_error = LDEC.VARIABLE_UUID_UNIQUE_ERROR
    name_error = LDEC.VARIABLE_NAME_FAILED
    name_unique_error = LDEC.VARIABLE_NAME_NOT_UNIQUE
    
    def __init__(self, app_uuid: str, module_uuid: str, module_name: str, 
                 document_uuid: str, document_name: str, element: dict, 
                 func_not_exist, *args, **kwargs):
        super().__init__(app_uuid, module_uuid, module_name, document_uuid, document_name, element, *args, **kwargs)
        self.func_not_exist = func_not_exist

    def initialize(self):
        super().initialize()
        self.permission = self.element.get("action", "")

    def check(self, module_func_list):
        attr = self.attr_class.FUNC
        return_code = LDEC.FUNC_NOT_EXISTS_IN_THIS_MODULE
        func_info = [func for func in module_func_list if func["func_uuid"] == self.element_uuid]
        if not func_info:
            self.func_not_exist.append(self.element_uuid)
            # self._add_error_list(attr, return_code)

    def check_permission(self):
        if not check_permission(self.permission):
            attr = self.attr_class.FIELDS
            return_code = LDEC.PERMISSION_FORMAT_ERROR
            self._add_error_list(attr, return_code)

class SecurityCheckerService(CheckerService):

    attr_class = Security.ATTR
    uuid_error = LDEC.MODULE_ROLE_UUID_ERROR
    uuid_unique_error = LDEC.MODULE_ROLE_UUID_UNIQUE_ERROR
    name_error = LDEC.MODULE_ROLE_NAME_ERROR
    name_unique_error = LDEC.MODULE_ROLE_NAME_UNIQUE_ERROR
    allow_chinese_name = True
    banned_rule_names = ["系统管理员"]

    def initialize(self):
        super().initialize()
        self.models = self.element.get("models", {})
        self.pages = self.element.get("pages", {})
        self.funcs = self.element.get("funcs", {})
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []

    def build_insert_query_data(self):
        return {
            ModuleRole.app_uuid.name: self.app_uuid,
            ModuleRole.module_uuid.name: self.module_uuid,
            ModuleRole.document_uuid.name: self.document_uuid,
            ModuleRole.role_uuid.name: self.element_uuid,
            ModuleRole.role_name.name: self.element_name,
            ModuleRole.models.name: self.models,
            ModuleRole.pages.name: self.pages,
            ModuleRole.funcs.name: self.funcs,
            ModuleRole.ext_tenant.name: self.ext_tenant
        }
    
    def build_update_query_data(self):
        return {
            ModuleRole.role_name.name: self.element_name,
            ModuleRole.models.name: self.models,
            ModuleRole.pages.name: self.pages,
            ModuleRole.funcs.name: self.funcs,
            ModuleRole.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return ModuleRole.update(**query_data).where(
            ModuleRole.role_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(role_uuid):
        return ModuleRole.update(**{
                ModuleRole.is_delete.name: True
            }).where(ModuleRole.role_uuid==role_uuid)

    def check_name(self):
        super().check_name()
        if self.element_name in self.banned_rule_names:
            attr = self.attr_name
            return_code = LDEC.MODULE_ROLE_NAME_BANNED
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr, return_code)

    def check_modify(self, document_role_uuid_set):
        if self.element_uuid in document_role_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)


    def check_model(self, module_model_list):
        model_fields_map = {model["model_uuid"]: model["fields"] for model in module_model_list}
        model_relations_map = {model["model_uuid"]: model["relations"] for model in module_model_list}
        model_not_exist = list()
        for model_uuid, model in self.models.items():
            model.update({"uuid": model_uuid})
            model_checker_service = ModelCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, 
                self.document_name, model, model_fields_map, model_relations_map, 
                model_not_exist, self_associations=self.self_associations)
            model_checker_service.check()
            if model_uuid not in model_not_exist: # 允许之后的检查
                model_checker_service.check_fields()
                model_checker_service.check_fields_permission()
            self.update_any_list(model_checker_service)

        list(map(self.element.get("models").pop, model_not_exist))
        for new_model in set(model_fields_map.keys()) - set(self.models.keys()):
            self.process_new_model(new_model)

    def process_new_model(self, new_model):
        models_config = self.element.get("batchData", {}).get("batch_models", {})
        add_all = models_config.get("actionAddAll", False)
        del_all = models_config.get("actionDeleteAll", False)
        field_all = models_config.get("fieldTypeAll", False)
        field_action = models_config.get("actionFieldsAll", None)
        model_permission = self.models.setdefault(new_model, {"uuid": new_model})
        action = 0
        fields = 0
        if add_all:
            action |= PermissionAction.INSERT
        if del_all:
            action |= PermissionAction.DELETE
        if field_all:
            if field_action == 0:
                fields |= PermissionAction.SELECT | PermissionAction.UPDATE
            elif field_action == 1:
                fields |= PermissionAction.SELECT
        action = format(action, "08b")
        model_permission.update({"action": action})
        fields = format(fields, "08b")
        model_permission.update({"fields": fields})

    def check_page(self, module_page_list):
        page_not_exist = list()
        for page_uuid, page in self.pages.items():
            page.update({"uuid": page_uuid})
            page_checker_service = PageCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, 
                self.document_name, page, page_not_exist)
            page_checker_service.check(module_page_list)
            page_checker_service.check_permission()
            self.update_any_list(page_checker_service)
        list(map(self.element.get("pages").pop, page_not_exist))
        page_uuids = {p["page_uuid"] for p in module_page_list}
        for new_page in page_uuids - set(self.pages.keys()):
            self.process_new_page(new_page)

    def process_new_page(self, new_page):
        pages_config = self.element.get("batchData", {}).get("batch_pages", {})
        page_permission = self.pages.setdefault(new_page, {"uuid": new_page})
        action = 0
        if pages_config.get("actionAll", False):
            action |= PermissionAction.VISIBLE
        action = format(action, "08b")
        page_permission.update({"action": action})

    def check_func(self, module_func_list):
        func_not_exist = list()
        for func_uuid, func in self.funcs.items():
            func.update({"uuid": func_uuid})
            func_checker_service = FuncCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, 
                self.document_name, func, func_not_exist)
            func_checker_service.check(module_func_list)
            func_checker_service.check_permission()
            self.update_any_list(func_checker_service)
        list(map(self.element.get("funcs").pop, func_not_exist))
        func_uuids = {p["func_uuid"] for p in module_func_list}
        for new_func in func_uuids - set(self.funcs.keys()):
            self.process_new_func(new_func)

    def process_new_func(self, new_func):
        funcs_config = self.element.get("batchData", {}).get("batch_funcs", {})
        func_permission = self.funcs.setdefault(new_func, {"uuid": new_func})
        action = 0
        if funcs_config.get("actionAll", False):
            action |= PermissionAction.EXECUTE
        action = format(action, "08b")
        func_permission.update({"action": action})


class SecurityDocumentCheckerService(DocumentCheckerService):
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, document_version: int, 
        app_module_role_list: list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, ModuleRole, *args, **kwargs)
        self.role_list = self.element.get("module_role_list", list())
        self.module_model_list = kwargs.get("module_model_list", list())
        self.module_page_list = kwargs.get("module_page_list", list())
        self.module_func_list = kwargs.get("module_func_list", list())
        self.app_role_uuid_set = set()
        self.module_role_name_set = set()
        self.document_role_uuid_set = set()
        self.document_role_update_list = list()
        self.document_role_delete_list = list()
        for role in app_module_role_list:
            role_uuid = role.get("role_uuid", "")
            role_name = role.get("role_name", "")
            role_module_uuid = role.get("module_uuid", "")
            role_document_uuid = role.get("document_uuid", "")

            # 找到原文档中所有的 模块角色，为了新增、更新、删除文档的 模块角色
            if role_document_uuid == self.document_uuid:
                self.document_role_uuid_set.add(role_uuid)
            else:
                # 排除当前文档所有的 role_uuid ，获取应用的所有 role_uuid
                self.app_role_uuid_set.add(role_uuid)
                # 排除当前文档所有的 role_uuid ，获取模块的所有 role_uuid
                if role_module_uuid == self.module_uuid:
                    self.module_role_name_set.add(role_name)

    @checker.run
    def check_role_list(self):
        need_check = True
        if self.ext_tenant:
            need_check = False
        this_document_role_uuid_set = set()
        for role in self.role_list:
            role_checker_service = SecurityCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, 
                    self.document_uuid, self.document_name, role, ext_tenant=self.ext_tenant, 
                    self_associations=self.self_associations)
            role_uuid = role_checker_service.element_uuid
            this_document_role_uuid_set.add(role_uuid)
            try:
                role_checker_service.check_uuid()
                if need_check:
                    role_checker_service.check_uuid_unique(self.app_role_uuid_set)
                role_checker_service.check_name()
                if need_check:
                    role_checker_service.check_name_unique(self.module_role_name_set)
                role_checker_service.check_model(self.module_model_list)
                role_checker_service.check_page(self.module_page_list)
                role_checker_service.check_func(self.module_func_list)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(role_checker_service)
                raise e
            else:
                self.update_any_list(role_checker_service)

            # 找到新增 或 更新的 模块角色
            if need_check:
                role_checker_service.check_modify(self.document_role_uuid_set)
            if role_checker_service.insert_query_list:
                self.document_insert_list.extend(role_checker_service.insert_query_list)
            if role_checker_service.update_query_list:
                self.document_update_list.extend(role_checker_service.update_query_list)

        # 找出删除的 模块角色，将其 is_delete 置为 True
        delete_role_uuid_set = self.document_role_uuid_set - this_document_role_uuid_set
        for this_role_uuid in delete_role_uuid_set:
            query = SecurityCheckerService.build_delete_query(this_role_uuid)
            self.document_delete_list.append(query)
            