from apps.services.checker import DocumentCheckerService


class ModuleDeployDocumentCheckService(DocumentCheckerService):
    # TODO 模块配置页面虽然是文档, 但目前没有需要进行文档检查的地方, 也只需要保存一份json
    # 这里先保留
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict, document_version: int, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, None, *args, **kwargs)
        self.document_version = document_version
    
        