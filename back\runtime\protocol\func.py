from enum import Enum
from typing import List, Optional, Any, Type, Literal, Union, Callable, ClassVar

from peewee import Model
from pydantic import BaseModel, Field
from typing_extensions import Annotated


class DeployLocation(int, Enum):
    cloud = 0
    local = 1


class DeployEnv(str, Enum):
    live = "正式环境"
    test = "测试环境"
    dev = "开发环境"


class PageDocumentBrief(BaseModel):
    url: str
    module_name: str
    name: Optional[str]
    page_uuid: str


class RuntimePageDocumentList(BaseModel):
    __root__: List[PageDocumentBrief]


class FuncTriggerType(str, Enum):
    PAGE_INTERACTIVE = "page_interactive"
    SERVER_API = "server_api"
    SYSTEM_API = "system_api"  # 平台实现的api，例如系统校验码
    WORKFLOW_EVENT = "workflow_event"
    MODEL_EVENT = "model_event"
    COMPONENT_EVENT = "component_event"


class BaseRuntimeComponent(BaseModel):
    name: str
    page_name: str
    component_type: int
    raw_component_accessor: Any
    to_form: ClassVar[Callable]
    to_data_list: ClassVar[Callable]

    @property
    def raw_component(self) -> Any:
        if self.raw_component_accessor is None:
            return None
        else:
            return self.raw_component_accessor()


class LemonContextDocument(BaseModel):
    pass


class ServerAPI(BaseModel):
    version: Optional[str] = ""
    description: Optional[str] = ""
    request_method: str = ""
    resource: str = ""


class EventWhen(str, Enum):
    BEFORE = "before"
    AFTER = "after"


class DataModelEventType(str, Enum):
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    CANCEL = "cancel"
    CREATE = "create"
    SAVE = "save"
    SELECT = 'select'
    UNSUPPORTED = "unsupported"


class PageEventType(str, Enum):
    CREATE = "create"
    DESTROY = "destroy"


class FormEventType(str, Enum):
    INIT = "init"
    SAVE = "save"
    DELETE = "delete"
    CANCEL = "cancel"
    LOAD = "load"


class ModelEvent(BaseModel):
    model_class: Optional[Type[Model]] = None
    event_type: DataModelEventType
    when: EventWhen


class ComponentEvent(BaseModel):
    component: BaseRuntimeComponent
    event_type: Union[FormEventType, PageEventType]
    when: EventWhen


class ModelEventTrigger(BaseModel):
    trigger_type: Literal[FuncTriggerType.MODEL_EVENT] = FuncTriggerType.MODEL_EVENT
    model_event: Optional[ModelEvent] = None


class ComponentEventTrigger(BaseModel):
    trigger_type: Literal[FuncTriggerType.COMPONENT_EVENT] = FuncTriggerType.COMPONENT_EVENT
    event: Optional[ComponentEvent] = None


class ServerAPITrigger(BaseModel):
    trigger_type: Literal[FuncTriggerType.SERVER_API] = FuncTriggerType.SYSTEM_API
    server_api: Optional[ServerAPI] = None


class WorkflowEventTrigger(BaseModel):
    trigger_type: Literal[FuncTriggerType.WORKFLOW_EVENT] = FuncTriggerType.WORKFLOW_EVENT
    flow: Any


FuncTrigger = Annotated[
    Union[
        ComponentEventTrigger, ModelEventTrigger, ServerAPITrigger, WorkflowEventTrigger
    ],
    Field(discriminator="trigger_type")]


class PrintOrientation(str, Enum):
    landscape = "landscape"
    portrait = "portrait"


class NavMenuItem: ...


class NavMenu:
    menu_items: List[NavMenuItem]
