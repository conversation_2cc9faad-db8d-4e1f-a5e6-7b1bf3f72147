# -*- coding:utf-8 -*-

import copy
import weakref

from baseutils.log import app_log
from apps.ide_const import (
    ComponentType,
    LinkageType,
    DataSourceType,
    PurposeType,
)
from apps.services.document.page import form_controller
from apps.base_utils import lemon_uuid

from runtime.engine import engine
from runtime.core.utils import lemon_association
from runtime.component.data_adapter import ComponentDataAdapter


class ComponentParser(object):

    def __init__(self, container=None):
        self.container = container
        if self.container is None:
            self.container_tree = dict()
        else:
            self.container_tree = self.container.container_tree

    def exit(self):
        self.container = None

    def get_rows(self, component_dict):
        rows = component_dict.get("rows", list())
        for row in rows:
            self.get_cols(row)

    def get_cols(self, component_dict):
        cols = component_dict.get("cols", list())
        for col in cols:
            self.get_children(col)

    def get_tabs(self, component_dict):
        tabs = component_dict.get("tabs", list())
        for tab in tabs:
            self.get_children(tab)

    def get_panels(self, component_dict):
        panels = component_dict.get("panels", list())
        for panel in panels:
            self.get_children(panel)

    def get_children(self, component_dict):
        children = component_dict.get("children", list())
        for child in children:
            child_type = child.get("type")
            child_uuid = child.get("uuid")
            if child_type == ComponentType.GRID_COL:
                self.get_children(child)
            elif child_type == ComponentType.GRID_ROW:
                self.get_cols(child)
            elif child_type == ComponentType.GRID:
                self.get_rows(child)
            elif child_type == ComponentType.DATALIST:
                self.container_tree[child_uuid] = child
            elif child_type == ComponentType.CARDLIST:
                self.container_tree[child_uuid] = child
            elif child_type == ComponentType.DATAGRID:
                self.container_tree[child_uuid] = child
            elif child_type == ComponentType.FORM:
                self.container_tree[child_uuid] = child
            elif child_type == ComponentType.CONTAINER:
                self.get_children(child)
            elif child_type == ComponentType.TABS:
                self.get_tabs(child)
            elif child_type == ComponentType.COLLAPSE:
                self.get_panels(child)
            elif child_type == ComponentType.SPLIT_PAGE:
                self.get_panels(child)
            elif child_type == ComponentType.EXECL_TABLE:
                self.container_tree[child_uuid] = child


class PageParser(object):

    def __init__(self, page=None):
        self._page = None
        self.page = page

    @property
    def page(self):
        return None if self._page is None else self._page()

    @page.setter
    def page(self, value):
        self._page = None if value is None else weakref.ref(value)

    def _get_deep_children(self, component_dict, child, parent):
        self.get_children(child, parent=parent)

    def _get_children(self, component_dict, children, parent=None):
        for child in children:
            self._get_deep_children(component_dict, child, parent)

    def make_layout_container(self, component_dict, parent=None):
        """处理布局容器, 构造容器container对象"""
        parent_event = parent.component_dict.get("events")
        data_dict = ComponentDataAdapter(
            parent_event=parent_event, **component_dict
        ).data_dict
        component_uuid = component_dict["uuid"]
        component_type = component_dict["type"]
        self.page.controls[component_uuid] = data_dict

        component = {"component": data_dict}
        container = engine.component_creator.create_component(
            component_type=component_type,
            component_dict=component,
            parent=parent,
            control=True,
        )
        self.page.data_controls.update({component_uuid: container})
        return container

    def get_rows(self, component_dict, parent=None):
        self.make_layout_container(component_dict, parent)
        rows = component_dict.get("rows", list())
        for row in rows:
            self.get_cols(row, parent)

    def get_cols(self, component_dict, parent=None):
        self.make_layout_container(component_dict, parent)
        cols = component_dict.get("cols", list())
        for col in cols:
            self.get_col(col, parent)

    def get_col(self, component_dict, parent=None):
        self.make_layout_container(component_dict, parent)
        self.get_children(component_dict, parent)

    def get_tabs(self, component_dict, parent=None):
        self.make_layout_container(component_dict, parent)
        tabs = component_dict.get("tabs", list())
        for tab in tabs:
            self.get_tab(tab, parent)

    def get_tab(self, component_dict, parent=None):
        self.make_layout_container(component_dict, parent)
        self.get_children(component_dict, parent)

    def get_panels(self, component_dict, parent=None):
        self.make_layout_container(component_dict, parent)
        panels = component_dict.get("panels", list())
        for panel in panels:
            self.get_panel(panel, parent)

    def get_panel(self, component_dict, parent=None):
        self.make_layout_container(component_dict, parent)
        self.get_children(component_dict, parent)

    def get_container(self, component_dict, parent=None):
        self._get_deep_children(component_dict, component_dict, parent=parent)

    def dfs(self, component_dict, component_tree, parent_uuid=None):
        """
        Performs a depth-first search on the component dictionary to build a component tree.

        Args:
            component_dict (dict): The component dictionary to be traversed.
            component_tree (dict): The resulting component tree.
            parent_uuid (str, optional): The UUID of the parent component. Defaults to None.
        """
        own_child_key = [
            "columns",
            "children",
            "buttons",
            "cols",
            "rows",
            "tabs",
            "panels",
            "component_list",
            "subtable",
        ]
        if "type" in component_dict and "uuid" in component_dict:
            uuid = component_dict["uuid"]
            component_tree[uuid] = parent_uuid
            if component_dict["type"] in ComponentType.ALL_LAYOUT_CONTAINER_LIST + ComponentType.CONTAINER_LIST:
                parent_uuid = uuid
        if "child_form_uuid" in component_dict:
            parent_uuid_for_cols = component_dict["child_form_uuid"]
            component_tree[parent_uuid_for_cols] = parent_uuid
        else:
            parent_uuid_for_cols = parent_uuid
        for key in own_child_key:
            if key in component_dict:
                if isinstance(component_dict[key], list):
                    for item in component_dict[key]:
                        if isinstance(item, dict):
                            if parent_uuid_for_cols != parent_uuid and key == "columns":
                                self.dfs(item, component_tree, parent_uuid_for_cols)
                            else:
                                self.dfs(item, component_tree, parent_uuid)
                elif isinstance(component_dict[key], dict):
                    self.dfs(component_dict[key], component_tree, parent_uuid)
        for key, value in component_dict.items():
            if key not in own_child_key and isinstance(value, dict):
                self.dfs(value, component_tree, parent_uuid)

    def get_children(self, component_dict, parent=None):
        if parent is None:
            parent = self.page
        parent_event = parent.component_dict.get("events")
        children = component_dict.get("children", list())
        p_context = copy.deepcopy(self.page.p_context)
        p_context.update({"events": parent_event})
        form_nested, root_form_uuid = False, None
        depth = 0 if self.page.depth == 0 else self.page.depth + 1
        app_log.debug(f"depth: {depth}")
        form_children = list()
        memory_storage = self.page.memory_storage
        for child in children:
            child_type = child.get("type")
            child_uuid = child.get("uuid")
            if child_type in [
                ComponentType.DATALIST,
                ComponentType.CARDLIST,
                ComponentType.DATAGRID,
                ComponentType.CALENDAR,
                ComponentType.EXECL_TABLE,
            ]:
                c = engine.component_creator.create_component(
                    component_type=child_type,
                    p_context=p_context,
                    component_dict=child,
                    parent=self.page,
                    memory_storage=memory_storage,
                    form_nested=form_nested,
                    depth=depth,
                    root_form_uuid=root_form_uuid,
                )
                self.page.container_tree[child_uuid] = c
            elif child_type == ComponentType.FORM:
                form_children.append(child)
                # container_tree 需要有序
                self.page.container_tree[child_uuid] = None
            elif child_type in range(30, 50):
                if (
                    child_type not in [46, 47, 48]
                    and self.page.linkage_in_page is not None
                ):
                    filters_define = child.get("filters", {})
                    app_log.info(filters_define)
                    filters_type = filters_define.get("type")
                    component_filters = []
                    if filters_type == 0:
                        component_filters = [filters_define.get("component")]
                        linkage_type = LinkageType.SEARCH_BAR_WITH_BI
                    elif filters_type == 1:
                        component_filters = filters_define.get("component", [])
                        linkage_type = LinkageType.ITEM_FILTER_WITH_BI
                    c = engine.component_creator.create_component(
                        component_type=child_type,
                        p_context=p_context,
                        component_dict=child,
                        parent=self.page,
                        form_nested=form_nested,
                        depth=depth,
                        root_form_uuid=root_form_uuid,
                    )
                    self.page.container_tree[child_uuid] = c
                    for filter in component_filters:
                        default_chart_linkage = {"type": linkage_type, "charts": []}
                        self.page.linkage_in_page.setdefault(
                            filter, default_chart_linkage
                        )
                        # 先添加filter的uuid，全部分析完成后再把uuid换成machine id
                        self.page.linkage_in_page[filter]["charts"].append(c.machine_id)
                else:
                    c = engine.component_creator.create_component(
                        component_type=child_type,
                        p_context=p_context,
                        component_dict=child,
                        parent=self.page,
                        form_nested=form_nested,
                        depth=depth,
                        root_form_uuid=root_form_uuid,
                    )
                    self.page.container_tree[child_uuid] = c

            elif child_type == ComponentType.TIMELINE:
                data_source = child.get("data_source", {})
                data_source_type = data_source.get("type")
                if data_source_type == 2:
                    data_source_component = data_source.get("component")
                    if data_source_component in self.page.model_in_page:
                        source_component = self.page.model_in_page[
                            data_source_component
                        ]
                        data_source["model"] = source_component.get("model", None)
                        child["data_source"] = data_source
                        t = engine.component_creator.create_component(
                            component_type=child_type,
                            p_context=p_context,
                            component_dict=child,
                            parent=self.page,
                            form_nested=form_nested,
                            depth=depth,
                            root_form_uuid=root_form_uuid,
                        )
                        s_component_type = source_component.get("component_type")
                        if s_component_type == ComponentType.FORM:
                            default_timeline_linkage = {
                                "type": LinkageType.FORM_WITH_TIMELINE,
                                "timeline": [],
                            }
                        elif s_component_type in [
                            ComponentType.DATALIST,
                            ComponentType.DATAGRID,
                        ]:
                            default_timeline_linkage = {
                                "type": LinkageType.DATALIST_WITH_TIMELINE,
                                "timeline": [],
                            }
                        elif s_component_type == ComponentType.CARDLIST:
                            default_timeline_linkage = {
                                "type": LinkageType.CARDLIST_WITH_TIMELINE,
                                "timeline": [],
                            }
                        else:
                            continue
                        self.page.linkage_in_page.setdefault(
                            data_source_component, default_timeline_linkage
                        )
                        self.page.linkage_in_page[data_source_component][
                            "timeline"
                        ].append(t.machine_id)
                        self.page.container_tree[child_uuid] = t
                else:
                    t = engine.component_creator.create_component(
                        component_type=child_type,
                        p_context=p_context,
                        component_dict=child,
                        parent=self.page,
                        form_nested=form_nested,
                        depth=depth,
                        root_form_uuid=root_form_uuid,
                    )
                    self.page.container_tree[child_uuid] = t
            elif child_type == ComponentType.SCAN:
                c = engine.component_creator.create_component(
                    component_type=child_type,
                    p_context=p_context,
                    component_dict=child,
                    parent=self.page,
                    form_nested=form_nested,
                    depth=depth,
                    root_form_uuid=root_form_uuid,
                )
                self.page.container_tree[child_uuid] = c
            elif child_type == ComponentType.GRID:
                self.get_rows(child, parent)
            elif child_type == ComponentType.TABS:
                self.get_tabs(child, parent)
            elif child_type == ComponentType.COLLAPSE:
                self.get_panels(child, parent)
            elif child_type == ComponentType.SPLIT_PAGE:
                self.get_panels(child, parent)
            elif child_type == ComponentType.CONTAINER:
                self.make_layout_container(child, parent)
                self.get_children(child, parent)
            elif child_type in [
                ComponentType.IMAGE,
                ComponentType.TEXT,
                ComponentType.NORMAL_BUTTON,
                ComponentType.SCAN_COUNT,
                ComponentType.CUSTOM_PRESENT,
                ComponentType.RING_BAR,
                ComponentType.LINE_BAR,
            ]:
                data_dict = ComponentDataAdapter(
                    parent_event=parent_event, **child
                ).data_dict
                self.page.controls[child_uuid] = data_dict
            elif child_type == ComponentType.TREE:
                c = engine.component_creator.create_component(
                    component_type=child_type,
                    p_context=p_context,
                    component_dict=child,
                    parent=self.page,
                    form_nested=form_nested,
                    depth=depth,
                    root_form_uuid=root_form_uuid,
                )
                self.page.container_tree[child_uuid] = c
            elif child_type == ComponentType.TREELIST:
                c = engine.component_creator.create_component(
                    component_type=child_type,
                    p_context=p_context,
                    component_dict=child,
                    parent=self.page,
                    memory_storage=memory_storage,
                    form_nested=form_nested,
                    depth=depth,
                    root_form_uuid=root_form_uuid,
                )
                self.page.container_tree[child_uuid] = c
            elif child_type == ComponentType.REACT:
                c = engine.component_creator.create_component(
                    component_type=child_type,
                    p_context=p_context,
                    component_dict=child,
                    parent=self.page,
                    form_nested=form_nested,
                    depth=depth,
                    root_form_uuid=root_form_uuid,
                )
                self.page.container_tree[child_uuid] = c
            elif child_type == ComponentType.DROPDOWN_MENU:
                dropdown_menu_data_dict = ComponentDataAdapter(parent_event=parent_event, **child).data_dict
                self.page.controls[child_uuid] = dropdown_menu_data_dict
                dropdown = child.get("dropdown", [])
                for menu in dropdown:
                    menu_uuid = menu.get("uuid")
                    menu_data_dict = ComponentDataAdapter(parent_event, **menu).data_dict
                    self.page.controls[menu_uuid] = menu_data_dict

        for child in form_children:
            child_uuid = child.get("uuid")
            child_type = child.get("type")
            # 直属于页面下的表单，如果时上下文数据源，那么期待页面参数传入一个 PK
            data_source = child.get("data_source", {})
            data_source_type = data_source.get("type", DataSourceType.FORM_WITH_EDITOR)
            # 嵌套的表单保存，要在同一事务里
            if data_source_type == DataSourceType.FORM_LINKAGE:
                data_source = child.get("data_source", {})
                data_source_component = data_source.get("component")
                if data_source_component in self.page.model_in_page:
                    linkage_model_data = self.page.model_in_page[data_source_component]
                    model_uuid = linkage_model_data.get("model", None)
                    path = linkage_model_data.get("path")
                    memory_storage = linkage_model_data.get("memory_storage")
                    data_source["model"] = model_uuid
                    child["data_source"] = data_source
                    # path 作为 RForm 从 source_in_page 取数据的 key
                    self.page.model_in_page[child_uuid].update(
                        {
                            "model": model_uuid,
                            "path": path,
                            "memory_storage": memory_storage,
                        }
                    )
                    f = engine.component_creator.create_component(
                        component_type=child_type,
                        p_context=p_context,
                        component_dict=child,
                        parent=self.page,
                        memory_storage=memory_storage,
                        form_nested=form_nested,
                        depth=depth,
                        root_form_uuid=root_form_uuid,
                    )
                    default_form_linkage = {
                        "type": LinkageType.DATALIST_WITH_FORM,
                        "forms": [],
                    }
                    self.page.linkage_in_page.setdefault(
                        data_source_component, default_form_linkage
                    )
                    # 以数据列表的UUID为 key
                    self.page.linkage_in_page[data_source_component]["forms"].append(
                        f.machine_id
                    )
                    self.page.container_tree[child_uuid] = f
            else:
                f = engine.component_creator.create_component(
                    component_type=child_type,
                    p_context=p_context,
                    component_dict=child,
                    parent=self.page,
                    memory_storage=memory_storage,
                    form_nested=form_nested,
                    depth=depth,
                    root_form_uuid=root_form_uuid,
                )
                f.outer_form_id = f.machine_id
                self.page.container_tree[child_uuid] = f

        buttons = component_dict.get("buttons", list())
        for button in buttons:
            button_uuid = button.get("uuid")
            data_dict = ComponentDataAdapter(
                parent_event=parent_event, **button
            ).data_dict
            self.page.controls[button_uuid] = data_dict

    def post_handle_linkage(self):
        for uuid, linkage_data_pre in self.page.linkage_in_page.items():
            charts_pre = linkage_data_pre.get("charts_pre")
            if charts_pre is not None:
                # default_chart_linkage = {"type": LinkageType.FILTER_WITH_BI, "charts": []}
                # linkage_in_page.setdefault(uuid, default_chart_linkage)
                # linkage_in_page[uuid]["charts"].append()
                for pre in charts_pre:
                    if pre in self.page.container_tree:
                        self.page.linkage_in_page[uuid]["charts"].append(
                            self.page.container_tree[pre].machine_id
                        )


class ContainerParser(ComponentParser):

    def get_model_uuid(self, child_dict, datalist=True):
        child_uuid = child_dict.get("uuid")
        data_source = child_dict.get("data_source", {})
        model_uuid = None
        path = []
        memory_storage = False
        data_source_type = data_source.get("type", 0)
        # 数据模型 with 预处理
        if datalist is True:
            if data_source_type == DataSourceType.MODEL_WITH_PRE:
                model_uuid = data_source.get("model")
            elif data_source_type == DataSourceType.MODEL_WITH_EDITOR:
                # 数据模型 with 值编辑器
                model_uuid = data_source.get("model")
            elif data_source_type == DataSourceType.MODEL_WITH_FUNC:
                # 数据模型 with 云函数
                model_uuid = data_source.get("model")
            # 数据模型关联
            elif data_source_type in [
                DataSourceType.ASSOCIATION,
                DataSourceType.ASSOCIATION_MEMORY,
            ]:
                # association_info = data_source.get("association", {})
                # association = association_info.get("uuid")
                if data_source_type == DataSourceType.ASSOCIATION_MEMORY:
                    memory_storage = True
                last_model_uuid = self.container.model_uuid
                path = data_source.get("path")
                field_model = None
                for p in path:
                    field_model, is_source, is_many, r_type = lemon_association(
                        p, last_model_uuid
                    )
                    if field_model:
                        last_model_uuid = field_model._meta.table_name
                if field_model:
                    model_uuid = field_model._meta.table_name
                    page_container = self.container.connector.find_page_container(
                        self.container.page_machine_id
                    )
                    page_container.model_in_page[child_uuid].update(
                        {
                            "model": model_uuid,
                            "path": path,
                            "memory_storage": memory_storage,
                        }
                    )
        else:
            if data_source_type in (
                DataSourceType.FORM_WITH_CONTEXT,
                DataSourceType.FORM_WITH_EDITOR,
                DataSourceType.FORM_WITH_FUNC,
            ):
                model_uuid = data_source.get("model")
            elif data_source_type in [
                DataSourceType.FORM_ASSOCIATION,
                DataSourceType.FORM_ASSOCIATION_MEMORY,
            ]:
                path = data_source.get("path")
                association_info = data_source.get("association", {})
                association = association_info.get("uuid")
                field_model, is_source, is_many, r_type = lemon_association(
                    association, self.container.model_uuid
                )
                app_log.info(f"association: {association}, model: {field_model}")
                if field_model:
                    if data_source_type == DataSourceType.FORM_ASSOCIATION_MEMORY:
                        memory_storage = True
                    model_uuid = field_model._meta.table_name
                    page_container = self.container.connector.find_page_container(
                        self.container.page_machine_id
                    )
                    page_container.model_in_page[child_uuid].update(
                        {
                            "model": model_uuid,
                            "path": path,
                            "memory_storage": memory_storage,
                        }
                    )
            elif data_source_type == DataSourceType.FORM_LINKAGE:
                data_source_component = data_source.get("component")
                page_container = self.container.connector.find_page_container(
                    self.container.page_machine_id
                )
                if (
                    page_container
                    and data_source_component in page_container.model_in_page
                ):
                    linkage_model_data = page_container.model_in_page[
                        data_source_component
                    ]
                    model_uuid = linkage_model_data.get("model", None)
                    path = linkage_model_data.get("path", [])
                    memory_storage = linkage_model_data.get("memory_storage", False)
                    page_container.model_in_page[child_uuid].update(
                        {
                            "model": model_uuid,
                            "path": path,
                            "memory_storage": memory_storage,
                        }
                    )
        return model_uuid, path

    def get_controls(self, component_dict: dict):
        parent_depth = self.container.depth + 1
        controls = component_dict.get("controls", list())
        scans = component_dict.get("scans", list())
        all_controls = []
        all_controls.extend(controls)
        all_controls.extend(scans)
        root_form_uuid, form_nested = None, False
        container_set_form_nested = False
        outer_form_id = self.container.outer_form_id
        component_dict.setdefault("r_control_paths", [])

        if self.container.component_type == ComponentType.FORM:
            root_form_uuid = self.container.root_form_uuid
            form_nested = True
        if self.container.nested:
            outer_form_id = self.container.parent.outer_form_id
        else:
            if self.container.component_type == ComponentType.FORM:
                outer_form_id = self.container.machine_id
            else:
                outer_form_id = None
        attrs = {"outer_form_id": outer_form_id}
        for control in all_controls:
            memory_storage = False
            control_dict = control.get("component", dict()) or control
            control_type = control_dict.get("type")
            control_uuid = control_dict.get("uuid")
            data_source = control_dict.get("data_source", {})
            p_context = copy.deepcopy(self.container.context)
            if control_type != ComponentType.SCAN:
                # 这里的组件，一般是放在卡片列表里的
                # 放在卡片列表里的扫码组件，只需要把每个扫码组件启动起来即可
                self.container.controls.update({control_uuid: control})
            # 这里的组件是否嵌套，跟随表单的嵌套
            # 如果表单没有嵌套，那么关联子表等组件的数据，会单独返回
            # ； 否则，会跟随表单一起返回
            if control_type in [
                ComponentType.R_SELECT,
                ComponentType.R_SELECT_POPUP,
                ComponentType.R_TILE,
            ]:
                field_info = control_dict.get("field_info", {})
                path = field_info.get("path", [])
                if path:
                    component_dict["r_control_paths"].append(path)
                c = engine.component_creator.create_relation_container_component(
                    component_type=control_type,
                    p_context=p_context,
                    component_dict=control_dict,
                    parent=self.container,
                    root_form_uuid=root_form_uuid,
                    depth=parent_depth,
                    form_nested=form_nested,
                    nested=self.container.nested,
                    **attrs,
                )
                self.container.control_tree[control_uuid] = c
            elif control_type in [
                ComponentType.DATALIST,
                ComponentType.CARDLIST,
                ComponentType.DATAGRID,
                ComponentType.TREELIST,
                ComponentType.EXECL_TABLE,
            ]:
                model_uuid, path = self.get_model_uuid(control_dict)
                container_set_form_nested = True
                # 表单作为数据列表的一行时，数据列表的子表会这样处理
                data_source_type = data_source.get(
                    "type", DataSourceType.MODEL_WITH_PRE
                )
                if data_source_type in [
                    DataSourceType.ASSOCIATION,
                    DataSourceType.ASSOCIATION_MEMORY,
                ]:  # 模型关联
                    p_context.update({"submit_row_list": []})
                    if data_source_type == DataSourceType.ASSOCIATION_MEMORY:
                        memory_storage = True
                nested = False  # 子表组件不需要是嵌入的
                if data_source_type in [DataSourceType.MODEL_WITH_PRE]:
                    component_type = control_type
                else:
                    component_type = ComponentType.SUB_TABLE
                    if control_type == ComponentType.DATAGRID:
                        component_type = ComponentType.DATAGRID_SUB_TABLE
                    elif control_type == ComponentType.CARDLIST:
                        component_type = ComponentType.CARDLIST_SUB_TABLE
                    elif control_type == ComponentType.TREELIST:
                        component_type = ComponentType.TREELIST_SUB_TABLE
                c = engine.component_creator.create_relation_container_component(
                    component_type=component_type,
                    component_dict=control_dict,
                    p_context=p_context,
                    model_uuid=model_uuid,
                    parent=self.container,
                    root_form_uuid=root_form_uuid,
                    machine_id=lemon_uuid(),
                    memory_storage=memory_storage,
                    form_nested=form_nested,
                    nested=nested,
                    depth=parent_depth,
                    **attrs,
                )
                self.container.control_tree[control_uuid] = c
            elif control_type == ComponentType.CALENDAR:
                container_set_form_nested = True
                model_uuid, path = self.get_model_uuid(control_dict)
                c = engine.component_creator.create_component(
                    component_type=control_type,
                    component_dict=control_dict,
                    p_context=p_context,
                    model_uuid=model_uuid,
                    parent=self.container,
                    root_form_uuid=root_form_uuid,
                    form_nested=form_nested,
                    nested=self.container.nested,
                    depth=parent_depth,
                    **attrs,
                )
                self.container.control_tree[control_uuid] = c
            elif control_type == ComponentType.SCAN:
                c = engine.component_creator.create_component(
                    component_type=control_type,
                    component_dict=control_dict,
                    p_context=p_context,
                    parent=self.container,
                    root_form_uuid=root_form_uuid,
                    form_nested=form_nested,
                    nested=self.container.nested,
                    depth=parent_depth,
                    **attrs,
                )
                self.container.control_tree[control_uuid] = c
        if form_nested and container_set_form_nested:
            self.container.form_nested = True

    def get_children(self, component_dict):
        parent_depth = self.container.depth + 1
        children = component_dict.get("children", list())
        root_form_uuid, form_nested = None, False
        container_set_form_nested = False  # 是否将外层表单置为
        outer_form_id = self.container.outer_form_id
        if self.container.component_type == ComponentType.FORM:
            root_form_uuid = self.container.root_form_uuid
            form_nested = True
            outer_form_id = outer_form_id or self.container.machine_id
        attrs = {"outer_form_id": outer_form_id}
        form_children = list()
        for child in children:
            memory_storage = False
            child_dict = child.get("component")
            child_type = child_dict.get("type")
            child_uuid = child_dict.get("uuid")
            data_source = child_dict.get("data_source", {})
            data_source_type = data_source.get("type", DataSourceType.MODEL_WITH_PRE)
            p_context = copy.deepcopy(self.container.context)
            if child_type == ComponentType.FORM:
                form_children.append(child)
            elif child_type in [
                ComponentType.DATALIST,
                ComponentType.CARDLIST,
                ComponentType.DATAGRID,
                ComponentType.TREELIST,
            ]:
                container_set_form_nested = True
                self.container.controls.update({child_uuid: child})
                model_uuid, path = self.get_model_uuid(child_dict)
                if data_source_type in [
                    DataSourceType.ASSOCIATION,
                    DataSourceType.ASSOCIATION_MEMORY,
                ]:  # 数据模型关联
                    # 用来判断 submit_row_list 是不是初始值
                    p_context.update({"submit_row_list": []})
                    if data_source_type == DataSourceType.ASSOCIATION_MEMORY:
                        memory_storage = True
                if data_source_type in [DataSourceType.MODEL_WITH_PRE]:
                    component_type = child_type
                elif child_type == ComponentType.DATAGRID:
                    component_type = ComponentType.DATAGRID_TABLE
                elif child_type == ComponentType.TREELIST:
                    component_type = ComponentType.TREELIST_TABLE
                else:
                    purpose = data_source.get("purpose", PurposeType.ADD)
                    if purpose == PurposeType.ADD:  # 添加用途
                        component_type = ComponentType.ADD_TABLE
                    elif purpose == PurposeType.DELETE:  # 删除用途
                        component_type = ComponentType.DELETE_TABLE
                    else:  # 仅展示
                        component_type = ComponentType.VIEW_TABLE
                c = engine.component_creator.create_relation_container_component(
                    component_type=component_type,
                    component_dict=child_dict,
                    p_context=p_context,
                    model_uuid=model_uuid,
                    parent=self.container,
                    root_form_uuid=root_form_uuid,
                    memory_storage=memory_storage,
                    form_nested=form_nested,
                    nested=self.container.nested,
                    depth=parent_depth,
                    **attrs,
                )
                # 跨表单关联约束
                if hasattr(self.container, "filter_dict"):
                    c.filter_dict = self.container.filter_dict
                self.container.container_tree[child_uuid] = c
            elif child_type in [ComponentType.SCAN, ComponentType.CALENDAR]:
                nested = False  # TODO: 扫码组件应该不需要是嵌入的
                c = engine.component_creator.create_component(
                    component_type=child_type,
                    component_dict=child_dict,
                    p_context=p_context,
                    parent=self.container,
                    root_form_uuid=root_form_uuid,
                    form_nested=form_nested,
                    nested=nested,
                    depth=parent_depth,
                    **attrs,
                )
                self.container.container_tree[child_uuid] = c
            elif child_type == ComponentType.REACT:
                c = engine.component_creator.create_component(
                    component_type=child_type,
                    p_context=p_context,
                    component_dict=child_dict,
                    parent=self.container,
                    form_nested=form_nested,
                    depth=parent_depth,
                    root_form_uuid=root_form_uuid,
                    nested=self.container.nested,
                    **attrs,
                )
                self.container.container_tree[child_uuid] = c
            elif child_type in range(30, 49):
                page_container = self.container.connector.find_page_container(
                    self.container.page_machine_id
                )
                # 可视化组件通过 parent 寻找 父容器是否有 pk
                # p_context.update({"parent_pk": self.container.pk})
                if (
                    child_type not in [46, 47, 48]
                    and page_container.linkage_in_page is not None
                ):
                    filters_define = child.get("filters", {})
                    filters_type = filters_define.get("type")
                    component_filters = []
                    if filters_type == 0:
                        component_filters = [filters_define.get("component")]
                        linkage_type = LinkageType.SEARCH_BAR_WITH_BI
                    elif filters_type == 1:
                        component_filters = filters_define.get("component", [])
                        linkage_type = LinkageType.ITEM_FILTER_WITH_BI
                    c = engine.component_creator.create_component(
                        component_type=child_type,
                        component_dict=child_dict,
                        p_context=p_context,
                        parent=self.container,
                        form_nested=form_nested,
                        nested=self.container.nested,
                        depth=parent_depth,
                        root_form_uuid=root_form_uuid,
                        **attrs,
                    )
                    for filter in component_filters:
                        default_chart_linkage = {"type": linkage_type, "charts": []}
                        page_container.linkage_in_page.setdefault(
                            filter, default_chart_linkage
                        )
                        # 先添加filter的uuid，全部分析完成后再把uuid换成machine id
                        page_container.page.linkage_in_page[filter]["charts"].append(
                            c.machine_id
                        )
                    self.container.container_tree[child_uuid] = c
                else:
                    c = engine.component_creator.create_component(
                        component_type=child_type,
                        component_dict=child_dict,
                        p_context=p_context,
                        parent=self.container,
                        form_nested=form_nested,
                        nested=self.container.nested,
                        depth=parent_depth,
                        root_form_uuid=root_form_uuid,
                        **attrs,
                    )
                    self.container.container_tree[child_uuid] = c
        for child in form_children:
            memory_storage = False
            child_dict = child.get("component")
            child_uuid = child_dict.get("uuid")
            child_type = child_dict.get("type")
            data_source = child_dict.get("data_source", {})
            data_source_type = data_source.get("type", DataSourceType.MODEL_WITH_PRE)
            p_context = copy.deepcopy(self.container.context)
            container_set_form_nested = True
            self.container.controls.update({child_uuid: child})
            model_uuid, path = self.get_model_uuid(child_dict, datalist=False)
            r_control_paths = component_dict.get("r_control_paths", [])
            if path in r_control_paths:
                child_dict.update({"on_relation": True})
            page_container = self.container.connector.find_page_container(
                self.container.page_machine_id
            )
            if (
                data_source_type == DataSourceType.FORM_WITH_CONTEXT
            ):  # 数据模型 with 上下文
                p_context.update({"pk": self.container.pk})
            elif data_source_type == DataSourceType.FORM_LINKAGE:
                data_source_component = data_source.get("component")
                if (
                    page_container
                    and data_source_component in page_container.model_in_page
                ):
                    linkage_model_data = page_container.model_in_page[
                        data_source_component
                    ]
                    path = linkage_model_data.get("path", [])
                    memory_storage = linkage_model_data.get("memory_storage", False)
                    page_container.model_in_page[child_uuid].update(
                        {
                            "model": model_uuid,
                            "path": path,
                            "memory_storage": memory_storage,
                        }
                    )
            if model_uuid:
                data_source["model"] = model_uuid
                data_source["path"] = path
                child_dict["data_source"] = data_source
                if data_source_type == DataSourceType.FORM_ASSOCIATION_MEMORY:
                    memory_storage = True
                f = engine.component_creator.create_relation_container_component(
                    component_type=child_type,
                    component_dict=child_dict,
                    p_context=p_context,
                    parent=self.container,
                    root_form_uuid=root_form_uuid,
                    depth=parent_depth,
                    memory_storage=memory_storage,
                    form_nested=form_nested,
                    nested=self.container.nested,
                    **attrs,
                )
                self.container.container_tree[child_uuid] = f
                if data_source_type == DataSourceType.FORM_LINKAGE:
                    data_source_component = data_source.get("component")
                    default_form_linkage = {
                        "type": LinkageType.DATALIST_WITH_FORM,
                        "forms": [],
                    }
                    page_container.linkage_in_page.setdefault(
                        data_source_component, default_form_linkage
                    )
                    # 以数据列表的UUID为 key
                    page_container.linkage_in_page[data_source_component][
                        "forms"
                    ].append(f.machine_id)
        if form_nested and container_set_form_nested:
            self.container.form_nested = True


class DatalistParser(ContainerParser):
    """
    # 数据列表的 default_form 用于处理 新建一行数据
    # 数据列表的每行都认为是一个表单
    # 数据列表有多行，会导致每行对应的表单状态机 uuid 相同
    # 也会导致，每行表单状态机下的 关联组件状态机 和 容器状态机 uuid 相同
    # 所以这里处理为，每行对应的 容器 和 组件 UUID 保持相同
    # 但每行所对应的 表单状态机 及 内嵌的容器和组件状态机 UUID 不同
    """

    def get_editor(self, parent_dict: dict, transfer_keys: list) -> dict:
        return {k: parent_dict.get(k, dict()) for k in transfer_keys}

    def transfer_parent_info(self, form_dict: dict):
        # 需要传递给下一级表单的值
        need_transfer_keys: list = [
            "card_item",
            "bg_color",
            "odd_line_bg_color",
            "even_line_bg_color",
        ]
        # 把 editable 从 keys 里删除的原因是
        # editable 的定义不能被卡片列表下的子表单继承
        # editable 能选择的字段是由外层表单 决定的
        # 这样写是为了要求传递的key在定义中
        for key in need_transfer_keys:
            if key_dict := self.container.component_dict.get(key):
                if key == "card_item":
                    card_item_editor_keys = [
                        "title",
                        "bg_color",
                        "title_bg_color",
                        "title_font_color",
                    ]
                    editor_dict = self.get_editor(key_dict, card_item_editor_keys)
                    form_dict.update(editor_dict)
                else:
                    form_dict.update({key: key_dict})

    def get_cardlist_title(self, form_dict: dict):
        if self.container.component_type == ComponentType.CARDLIST:
            # 将卡片列表的标题,作为表单的 title
            card_item = self.container.component_dict.get("card_item", {})
            if isinstance(card_item, dict) and card_item:
                form_dict.update({"title": card_item.get("title", {})})

    def get_form_dict(self):
        form_dict = dict()
        form_uuid = self.container.component_dict.get("child_form_uuid")
        form_class_id = self.container.component_dict.get("class_id")
        dynamic_controls = self.container.component_dict.get("dynamic_controls", {})
        controls = self.container.component_dict.get("controls", [])
        dynamic_default_controls = self.container.component_dict.get("dynamic_default_controls", [])
        scans = self.container.component_dict.get("scans", [])
        events, form_events = None, []
        container_events = self.container.component_dict.get("datalist_events", [])
        container_events.extend(self.container.component_dict.get("cardlist_events", []))
        row_settings = self.container.component_dict.get("row_settings", {})
        row_selectable = self.container.component_dict.get("row_selectable", {})
        if isinstance(container_events, list) and container_events:
            form_events.extend(container_events)
        if self.container.component_type == ComponentType.CARDLIST:
            card_item = self.container.component_dict.get("card_item", {})
            events = card_item.get("events", [])
        all_cell_settings = {}
        cell_editable = all_cell_settings.setdefault("cell_editable", [])
        for column_info in self.container.component_dict.get("own_columns", []):
            cell_settings = column_info.get("cell_settings", {})
            if cell_settings:
                if editable := cell_settings.get("editable", {}):
                    cell_editable.append(editable)
        form_dict.update(
            {
                "uuid": form_uuid,
                "type": ComponentType.FORM,
                "class_id": form_class_id,
                "controller": form_controller,
                "controls": controls,
                "dynamic_default_controls": dynamic_default_controls,
                "dynamic_controls": dynamic_controls,
                "scans": scans,
                "events": events,
                "form_events": form_events,
                "row_settings": row_settings,
                "data_source": {"model": self.container.model_uuid},
                "all_cell_settings": all_cell_settings,
                "row_selectable": row_selectable
            }
        )
        self.get_cardlist_title(form_dict)
        children = self.container.component_dict.get("children", [])
        form_dict.update({"children": children})
        self.transfer_parent_info(form_dict)
        return form_dict

    def get_form_attrs(self, p_context, nested=True, machine_id=None):
        _p_context = {"pk": 0}
        if isinstance(p_context, dict):
            _p_context.update(p_context)
        p_context = _p_context
        if not self.container.form_dict:
            self.container.form_dict = self.get_form_dict()
        depth = self.container.depth + 1
        machine_id = machine_id or lemon_uuid()
        attrs = {"outer_form_id": self.container.outer_form_id}
        return dict(
            component_type=ComponentType.FORM,
            p_context=p_context,
            component_dict=self.container.form_dict,
            parent=self.container,
            machine_id=machine_id,
            root_form_uuid=self.container.root_form_uuid,
            form_nested=self.container.form_nested,
            memory_storage=self.container.memory_storage,
            nested=nested,
            depth=depth,
            data_convert=False,
            **attrs
        )

    def get_form(self, p_context=None, nested=True, machine_id=None):
        form_attrs = self.get_form_attrs(p_context, nested, machine_id)
        form = engine.component_creator.create_component(**form_attrs)
        return form

    def get_default_form(self):
        p_context = {"source_in_page": {}}
        return self.get_form(p_context=p_context, nested=True)


class TreeParser(ContainerParser):

    def get_form_dict(self):
        # TODO 根据tree的component_dict 构造一个form_dict
        form_dict = dict()
        form_uuid = self.container.component_dict.get("uuid")
        form_class_id = self.container.component_dict.get("class_id")
        controls = copy.deepcopy(
            self.container.component_dict.get("child_form_control", [])
        )
        controls = [{"component": c} for c in controls]
        event = None
        form_dict.update(
            {
                "uuid": form_uuid,
                "type": ComponentType.FORM,
                "class_id": form_class_id,
                "controller": form_controller,
                "controls": controls,
                "events": event,
                "data_source": {"model": self.container.model_uuid},
            }
        )
        children = copy.deepcopy(self.container.component_dict.get("children", []))
        form_dict.update({"children": children})
        return form_dict

    def get_form(self, p_context=None, nested=True, machine_id=None):
        _p_context = {"pk": 0}
        if isinstance(p_context, dict):
            _p_context.update(p_context)
        p_context = _p_context
        if not self.container.form_dict:
            self.container.form_dict = self.get_form_dict()
        depth = self.container.depth + 1
        machine_id = machine_id or lemon_uuid()
        attrs = {"outer_form_id": self.container.outer_form_id}
        form = engine.component_creator.create_component(
            component_type=ComponentType.FORM,
            p_context=p_context,
            component_dict=self.container.form_dict,
            parent=self.container,
            machine_id=machine_id,
            root_form_uuid=self.container.root_form_uuid,
            form_nested=self.container.form_nested,
            nested=nested,
            depth=depth,
            data_convert=False,
            **attrs,
        )
        return form
