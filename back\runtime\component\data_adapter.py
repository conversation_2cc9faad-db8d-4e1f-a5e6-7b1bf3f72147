# -*- coding:utf-8 -*-

import copy
import weakref
from collections import OrderedDict

from baseutils.log import app_log
from apps.exceptions import LemonRuntimeError
from apps.entity import DocumentContent
from apps.utils import lemon_uuid, build_model_field_path
from baseutils.const import SystemField
from apps.base_utils import get_sys_field_uuid, is_system_field
from apps.ide_const import (
    ComponentType,
    DataSourceType,
    InputType,
    DeviceType,
    NodeType,
    VariableType,
    DynamicColumnType,
    DatalistColumnType
)
from apps.services.document import (
    Event,
    EventTrigger,
    Transition,
)
from apps.base_entity import get_current_user

from runtime.const import LemonRuntimeErrorCode as LREC
from runtime.utils import Configurable, build_join_info, build_field_path
from runtime.engine import engine
from runtime.core.connector import ClientConnector
from runtime.core.sm import ComponentLemonStateMachine
from runtime.core.utils import (
    lemon_association,
    lemon_field,
    lemon_model,
    lemon_model_node,
    build_join,
)
from runtime.core.func import LemonSMValueEditor

from tests.document.sm_rulechain import sm_rulechain
from tests.document.sm_rulechain import (
    EventBind,
    TimerTrigger,
    StringValue,
)
from tests.utils import TriggerBind


class BaseDataAdapter(object):

    def __init__(self, parent_event=None, **component_dict):
        self.component_dict = component_dict
        self.data_dict = dict()
        self.event = parent_event
        event = self.component_dict.get("events", {})
        if isinstance(event, dict) and event:
            self.combine_events(self.event, event)
            self.event = event
        app_log.debug(f"self: {self}, event: {event}, self.event: {self.event}")
        self.data_dict.update({"events": self.event})
        self.default_keys = [
            "uuid",
            "name",
            "class_id",
            "type",
            "visible",
            "real_parent_info",
            "permission_config",
            "linkage_settings",
        ]
        self.initialize()

    def initialize(self):
        pass

    def __repr__(self):
        return "<%s@%s>" % (type(self).__name__, id(self))

    def combine_events(self, source_events: dict, target_events: dict):
        if isinstance(source_events, dict) and source_events:
            for key, source_event_list in source_events.items():
                if source_event_list:
                    target_event_list = target_events.get(key)
                    if target_event_list:
                        target_event_list.extend(source_event_list)
                    else:
                        target_events.update({key: source_event_list})

    def _parse_color(self, value, editor_key):
        color_dict = {"editor": {}}
        color_editor = value.get(editor_key, {})
        color_dict.update({"editor": color_editor})
        return color_dict

    def _parse_default(self, key, value):
        c_value = copy.deepcopy(value) if isinstance(value, (list, dict)) else value
        self.data_dict.update({key: c_value})

    def _parse_deep_children(self, value):
        children = list()
        for child_dict in value:
            child_uuid = child_dict.get("uuid")
            child_title = child_dict.get("title")
            child_visible = child_dict.get("visible", {})
            children.append(
                {"uuid": child_uuid, "title": child_title, "visible": child_visible}
            )
        return children

    def run(self):
        # app_log.debug(f"data_adapter run: {self}")
        for key, value in self.component_dict.items():
            if key in self.default_keys:
                self._parse_default(key, value)
            else:
                func_str = "_".join(["parse", key])
                func = getattr(self, func_str, None)
                if callable(func):
                    func(key, value)
        # app_log.debug(f"data_adapter end: {self}")


class DataContainerDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.controls = list()
        self.dynamic_default_controls = list()
        self.template_controls = dict()
        self.children = list()
        self.scans = list()
        self.dynamic_controls = dict()

    def _parse_children(self, value, event=None):
        for child in value:
            if not child:  # 有 child 直接为 {}
                continue
            child_type = child.get("type")
            if child_type == ComponentType.GRID_COL:
                self.parse_cols(child, event=event)
            elif child_type == ComponentType.GRID_ROW:
                self.parse_rows(child, event=event)
            elif child_type == ComponentType.GRID:
                self.parse_grid(child, event=event)
            elif child_type == ComponentType.TABS:
                self.parse_tabs(child, event=event)
            elif child_type == ComponentType.TAB:
                self.parse_single_tab(child, event=event)
            elif child_type == ComponentType.COLLAPSE:
                self.parse_panels(child, event=event)
            elif child_type == ComponentType.PANEL:
                self.parse_panel(child, event=event)
            elif child_type == ComponentType.SPLIT_PAGE:
                self.parse_panels(child, event=event)
            elif child_type == ComponentType.SPLIT:
                self.parse_panel(child, event=event)
            elif child_type == ComponentType.CONTAINER:
                self.parse_container(child, event=event)
            elif child_type == ComponentType.EXECL_TABLE:
                self.parse_excel(child, event=event)
            elif child_type in ComponentType.CONTAINER_LIST:
                component_data_adapter = ComponentDataAdapter(event, **child)
                if child_type in [
                    ComponentType.DATALIST,
                    ComponentType.DATAGRID,
                    ComponentType.CARDLIST,
                ] and isinstance(self, CardlistDataAdapter):
                    self.controls.append(
                        {"component": component_data_adapter.data_dict}
                    )
                else:
                    self.children.append(
                        {"component": component_data_adapter.data_dict}
                    )
            elif child_type in [ComponentType.CALENDAR, ComponentType.REACT]:
                component_data_adapter = ComponentDataAdapter(event, **child)
                self.children.append({"component": component_data_adapter.data_dict})
            elif child_type in ComponentType.COLUMN_CONTROL_LIST:
                event = event if child_type == ComponentType.IMAGE else None
                component_data_adapter = ComponentDataAdapter(event, **child)
                self.controls.append({"component": component_data_adapter.data_dict})
            elif child_type in ComponentType.RELATION_CONTROL_LIST:
                component_data_adapter = ComponentDataAdapter(event, **child)
                self.controls.append({"component": component_data_adapter.data_dict})
            elif child_type == ComponentType.SCAN:
                if isinstance(self, CardlistDataAdapter):
                    self.scans.append({"component": child})
                else:
                    self.children.append({"component": child})
            elif child_type in ComponentType.ALL_BUTTON:
                component_data_adapter = ComponentDataAdapter(event, **child)
                self.controls.append({"component": component_data_adapter.data_dict})
            elif child_type == ComponentType.PRINT:
                component_data_adapter = ComponentDataAdapter(event, **child)
                self.controls.append({"component": component_data_adapter.data_dict})
            elif child_type in ComponentType.CHART_LIST:
                self.children.append({"component": child})
            elif child_type == ComponentType.DROPDOWN_MENU:
                dropdown_menu_data_dict = ComponentDataAdapter(event, **child).data_dict
                self.controls.append({"component": dropdown_menu_data_dict})
                dropdown = child.get("dropdown", [])
                for menu in dropdown:
                    menu_data_dict = ComponentDataAdapter(event, **menu)
                    self.controls.append({"component": menu_data_dict.data_dict})

    def parse_children(self, key, value):
        self._parse_children(value, event=self.event)
        self.data_dict.update(
            {"controls": self.controls, "children": self.children, "scans": self.scans}
        )

    def parse_grid(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.controls.append({"component": component_data_adapter.data_dict})
        rows = value.get("rows", list())
        self._parse_children(rows, event)

    def parse_rows(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.controls.append({"component": component_data_adapter.data_dict})
        cols = value.get("cols", list())
        self._parse_children(cols, event)

    def parse_cols(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.controls.append({"component": component_data_adapter.data_dict})
        children = value.get("children", list())
        self._parse_children(children, event=event)

    def parse_tabs(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.controls.append({"component": component_data_adapter.data_dict})
        tabs = value.get("tabs", list())
        self._parse_children(tabs, event=event)

    def parse_single_tab(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.controls.append({"component": component_data_adapter.data_dict})
        children = value.get("children", list())
        self._parse_children(children, event=event)

    def parse_panels(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.controls.append({"component": component_data_adapter.data_dict})
        panels = value.get("panels", list())
        self._parse_children(panels, event=event)

    def parse_panel(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.controls.append({"component": component_data_adapter.data_dict})
        children = value.get("children", list())
        self._parse_children(children, event=event)

    def parse_container(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.controls.append({"component": component_data_adapter.data_dict})
        children = value.get("children", list())
        self._parse_children(children, event=event)

    def parse_controls(self, key, value):
        self.controls.extend(value)
        self.data_dict.update({"controls": self.controls})

    def parse_excel(self, value, event=None):
        component_data_adapter = ComponentDataAdapter(event, **value)
        self.children.append({"component": component_data_adapter.data_dict})
        children = value.get("children", list())
        self._parse_children(children, event=event)

    def parse_style(self, key, value):
        class_name_dict = value.get("class_name_value_editor", {})
        self.data_dict.update({
                "style": {
                    "class_name": class_name_dict
                }
            }
        )


class FormDataAdapter(DataContainerDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "controller",
            "editable",
            "title",
            "constraint_setting",
            "auto_fill",
            "relate_approvalflow",
            "show_process",
            "relation_linkage",
            "is_paginated",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_data_source(self, key, value):
        data_source = copy.deepcopy(value)
        data_source_type = data_source.get("type")
        if data_source_type in [
            DataSourceType.FORM_ASSOCIATION,
            DataSourceType.FORM_ASSOCIATION_MEMORY,
        ]:
            association = data_source.get("association")
            if not isinstance(association, dict):
                data_source["association"] = {"uuid": association}
        self.data_dict.update({"data_source": data_source})

    def parse_buttons(self, key, value):
        self._parse_children(value, event=self.event)

    def parse_events(self, key, value):
        self.data_dict.update({"form_events": value})


class BaseDatalistDataAdapter(DataContainerDataAdapter):

    def initialize(self):
        super().initialize()
        self.own_buttons = list()
        self.search_bar_dict = dict()
        self.general_search = dict()
        self.free_search = dict()
        self.own_columns = list()

    def parse_buttons(self, key, value):
        for button_dict in value:
            button_type = button_dict.get("type")
            if button_type == ComponentType.SEARCH_BUTTON:
                search_bar_dict = button_dict.get("search_bar")
                if search_bar_dict and isinstance(search_bar_dict, dict):
                    self.search_bar_dict = self._parse_search_bar(search_bar_dict)
                self.general_search = button_dict.get("general_search", {})
                self.free_search = button_dict.get("free_search", {})
            component_data_adapter = ComponentDataAdapter(self.event, **button_dict)
            component_data = component_data_adapter.data_dict
            if button_type == ComponentType.SEARCH_BUTTON:
                if search_bar_dict:
                    component_data.update({"items": self.search_bar_dict.get("items", [])})
            self.own_buttons.append({"component": component_data})
        self.data_dict.update({key: value})
        self.data_dict.update(
            {
                "search_bar": self.search_bar_dict,
                "general_search": self.general_search,
                "free_search": self.free_search,
            }
        )
        self.data_dict.update({"own_buttons": self.own_buttons})

    def parse_data_source(self, key, value):
        data_source = copy.deepcopy(value)
        association = data_source.get("association")
        if not isinstance(association, dict):
            data_source["association"] = {"uuid": association}
        self.data_dict.update({"data_source": data_source})

    def _parse_search_bar(self, value):
        new_items = list()
        operation_relation = 0
        show_save_button = False
        if value and isinstance(value, dict):
            new_items = value.get("items", [])
            operation_relation = value.get("operation_relation", 0)
            show_save_button = value.get("show_save_button", False)
            show = value.get("show", True)
        return {
            "items": new_items,
            "operation_relation": operation_relation,
            "show_save_button": show_save_button,
            "show": show,
        }

    def parse_pagination(self, key, value):
        page_size_list = value.get("page_size_list")
        mobile = value.get("mobile", 10)
        is_shown = value.get("is_shown", True)
        if is_shown is False:
            page_size = 500
        elif page_size_list:
            page_size = page_size_list[0]
        elif value.get("page_size"):
            page_size = value.get("page_size", 10)
        else:
            page_size = 10
        page_size_list = [10, 20, 50, 100] if page_size_list is None else page_size_list
        self.data_dict.update(
            {
                key: {
                    "page_size": page_size,
                    "is_shown": is_shown,
                    "page_size_list": page_size_list,
                    "mobile": mobile,
                }
            }
        )

    def parse_popup_search_bar(self, key, value):
        if value and isinstance(value, dict):
            search_bar_dict = self._parse_search_bar(value)
            self.data_dict.update({key: search_bar_dict})


class DatalistDataAdapter(BaseDatalistDataAdapter):

    def initialize(self):
        super().initialize()
        self.data_dict.update({"child_form_uuid": lemon_uuid()})
        self.extend_default_keys = [
            "uuid",
            "extension",
            "controller",
            "order",
            "title",
            "title_style",
            "editable",
            "is_required",
            "relation_linkage",
            "default_expand",
            "row_settings",
            "number_attribute",
            "show_search_result",
            "row_selectable",
            "aggregatedColumns"
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_titleStyle(self, key, value):
        self.data_dict.update({"title_style": value})

    def parse_columns(self, key, value):
        dynamic_column_settings = {}
        column_view, column_fix, column_order = {}, {}, []
        fixed_switch = self.component_dict.get("fixed_switch")
        fixed_left = self.component_dict.get("fixed_left", 0)
        fixed_right = len(value) - self.component_dict.get("fixed_right", 0)
        for index, column in enumerate(value):
            all_component_list = []
            input_control = column.get("input_control")
            field_info = column.get("field_info", {})
            aggregation = column.get("aggregation", {})
            component = column.get("component", {})
            font_color = column.get("font_color", {})
            editable = column.get("editable", False)
            dataIndex = column.get("dataIndex", "")
            isGroup = column.get("isGroup", False)
            filterable = column.get("filterable", False)
            candidate_preprocessing = column.get("candidate_preprocessing", {})
            title = column.get("title", {})
            bg_color = column.get("style", {}).get("bg_color", {})
            tooltip = column.get("tooltip", {}) or {}
            default_display_column = column.get("default_display_column", True)
            edit_tip = tooltip.get("edit_tip_value", {})
            title_tip = tooltip.get("title_tip_value", {})
            component_list = column.get("component_list", [])
            visible = column.get("visible", dict())
            column_event = column.get("events", dict())
            new_page = column.get("new_page", {})
            permission_config = column.get("permission_config", {})
            linkage_settings = column.get("linkage_settings", {})
            cell_settings = column.get("cell_settings", {})
            uuid = column.get("uuid")
            number_attribute = column.get("number_attribute", {})
            extension = column.get("extension")
            select_data_source = column.get("select_data_source")
            expand_columns = column.get("expand_columns", [])
            file_limit = column.get("file_limit") or column.get("count_limit", 10)
            preprocessing = column.get("preprocessing", {})
            view_page = column.get("view_page", {})
            modal_view_page = column.get("modal_view_page", {})
            self.combine_events(self.event, column_event)
            if default_display_column:
                is_show = True
                column_order.append(dataIndex)
            else:
                is_show = False
            column_view.update({dataIndex: is_show})
            if fixed_switch:
                if index < fixed_left:
                    column_fix.update({dataIndex: "left"})
                elif index >= fixed_right:
                    column_fix.update({dataIndex: "right"})
            if component:
                all_component_list.append(component)
            all_component_list.extend(component_list)
            if editable is False:
                edit_field_uuid, edit_field_info = None, {}
                edit_field_data_source = {}
            else:
                edit_field_info = column.get("edit_field")
                edit_field_data_source = column.get("edit_field_data_source", {})
                if isinstance(edit_field_info, dict) and edit_field_info:
                    edit_field_uuid = edit_field_info.get("uuid")
                    # 输入组件是field_info，展示组件是edit_field
                    if not field_info:
                        field_info = edit_field_info
                else:
                    edit_field_uuid = column.get("edit_field")
                    edit_field_info = {"uuid": edit_field_uuid, "path": []}

            dynamic_uuid = column.get("dynamic_uuid")
            datalist_column_type = column.get("datalist_column_type", 0)
            dynamic_setting = column.get("dynamic_setting", {})
            is_dynamic = datalist_column_type == DatalistColumnType.DYNAMIC
            is_default_column = is_dynamic and dynamic_setting.get("dynamic_column_type") == DynamicColumnType.DEFAULT
            is_template_column = is_dynamic and dynamic_setting.get("dynamic_column_type") == DynamicColumnType.EXAMPLE
            if is_default_column:  # 是默认动态列
                dynamic_column_settings.update({uuid: dynamic_setting})
            for component in all_component_list:
                component_type = component.get("type")
                if component_type in ComponentType.CONTAINER_LIST:
                    component_data_adapter = ComponentDataAdapter(
                        column_event, **component
                    )
                    component_data = component_data_adapter.data_dict
                    self.children.append(component_data)
                elif (
                    component_type in ComponentType.COLUMN_CONTROL_LIST
                    or component_type in ComponentType.RELATION_CONTROL_LIST
                    or component_type in ComponentType.ALL_BUTTON
                ):
                    component_data_adapter = ComponentDataAdapter(
                        column_event, **component
                    )
                    component_data = component_data_adapter.data_dict
                    # 列上有可编辑，但列上的组件没有设置可编辑
                    # 这样会导致行内编辑的时候，没办法判断列上的组件 是否可编辑
                    view_receive_linkage = component_data.get(
                        "linkage_settings", {}
                    ).get("receive_linkage", True)
                    component_data.update(
                        {
                            "dynamic_uuid": dynamic_uuid,
                            "datalist_column_type": datalist_column_type,
                            "dynamic_setting": dynamic_setting,
                            "editable": editable,
                            "parent_visible": visible,
                            "linkage_settings": linkage_settings,
                            "view_receive_linkage": view_receive_linkage,
                            "select_data_source": select_data_source,
                            "expand_columns": expand_columns,
                            "file_limit": file_limit,
                            "preprocessing": preprocessing,
                            "view_page": view_page,
                            "modal_view_page": modal_view_page
                        }
                    )
                    control_data = {
                        "aggregation": aggregation,
                        "component": component_data,
                        "font_color": font_color,
                        "edit_field": edit_field_uuid,
                        "edit_field_info": edit_field_info,
                        "input_control": input_control,
                        "edit_field_data_source": edit_field_data_source,
                        "candidate_preprocessing": candidate_preprocessing,
                        "title": title,
                        "dataIndex": dataIndex,
                        "isGroup": isGroup,
                        "filterable": filterable,
                        "bg_color": bg_color,
                        "edit_tip": edit_tip,
                        "field_info": field_info,
                        "new_page": new_page,
                        "extension": extension,
                        "uuid": uuid,
                        "number_attribute": number_attribute,
                        "view_page": view_page,
                        "modal_view_page": modal_view_page
                    }
                    if is_template_column or is_default_column:
                        all_dynamic_setting = copy.copy(dynamic_column_settings.get(dynamic_uuid, {}))
                        all_dynamic_setting.update(dynamic_setting)
                        all_dynamic_setting.update({"old_column_uuid": uuid})
                        component_data.update({
                            "dynamic_setting": all_dynamic_setting
                        })
                        dynamic_column_controls = self.dynamic_controls.setdefault(dynamic_uuid, [])
                        dynamic_column_controls.append(control_data)
                    if not is_template_column:
                        self.controls.append(control_data)
            if (
                isinstance(title, dict)
                or isinstance(editable, dict)
                or isinstance(visible, dict)
            ):
                self.own_columns.append(
                    {
                        "uuid": uuid,
                        "datalist_column_type": datalist_column_type,
                        "dataIndex": dataIndex,
                        "title": title,
                        "title_tip": title_tip,
                        "editable": editable,
                        "edit_field": edit_field_uuid,
                        "visible": visible,
                        "permission_config": permission_config,
                        "cell_settings": cell_settings,
                    }
                )
        self.data_dict.update({"column_info": {"column_fix": column_fix, "column_view": column_view, "column_order": column_order}})
        self.data_dict.update({"controls": self.controls})
        self.data_dict.update({"children": self.children})
        self.data_dict.update({"own_columns": self.own_columns})
        self.data_dict.update({"dynamic_controls": self.dynamic_controls})

    def parse_subtable(self, key, value):
        for table in value:
            table_data = ComponentDataAdapter(self.event, **table).data_dict
            self.controls.append({"component": table_data})
        self.data_dict.update({"controls": self.controls})

    def parse_row_info(self, key, value):
        style_keys = ["odd_line_bg_color", "even_line_bg_color"]
        for key, d in value.items():
            if key in style_keys:
                if d.get("type") == "2":
                    d = d.get("color", dict())
            self.data_dict.update({key: d})

    def parse_events(self, key, value):
        self.data_dict.update({"datalist_events": value})


class CardlistDataAdapter(BaseDatalistDataAdapter):

    def initialize(self):
        super().initialize()
        self.data_dict.update({"child_form_uuid": lemon_uuid()})
        self.extend_default_keys = [
            "controller",
            "editable",
            "is_required",
            "relation_linkage",
            "card_type",
            "row_selectable",
            "is_sort",
            "sort_field"
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_card_item(self, key, value):
        operations = value.get("operations", [])
        if isinstance(operations, list):
            # 卡片列表 操作区可以放按钮
            for operation in operations:
                data = ComponentDataAdapter(self.event, **operation).data_dict
                self.controls.append({"component": data})
            self.data_dict.update({"controls": self.controls})
        style = value.get("style", dict())
        if style and isinstance(style, dict):
            value.pop("style")
            card_bg_color = style.get("card_bg_color", dict())
            if card_bg_color.get("type") == "2":
                value["bg_color"] = card_bg_color.get("color", dict())
            title_bg_color = style.get("title_bg_color", dict())
            if title_bg_color.get("type") == "2":
                value["title_bg_color"] = title_bg_color.get("color", dict())
            title_font_color = style.get("title_font_color", dict())
            if title_font_color.get("type") == "2":
                value["title_font_color"] = title_font_color.get("color", dict())
        self.data_dict.update({key: value})
        
    def parse_events(self, key, value):
        self.data_dict.update({"cardlist_events": value})


class DatagridDataAdapter(DatalistDataAdapter):

    def initialize(self):
        self.extend_default_keys = [
            "controller",
            "search_bar",
            "default_sm",
            "is_required",
            "relation_linkage",
            "aggregatedColumns"
        ]
        self.default_keys.extend(self.extend_default_keys)
        super().initialize()


class CalendarDataAdapter(BaseDatalistDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["controller", "relation_linkage"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class TreeDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "common",
            "operate",
            "controller",
            "child_form_control",
            "relation_linkage",
            "show_from_level",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_data_source(self, key, value):
        data_source = copy.deepcopy(value)
        data_source_type = data_source.get("type")
        if data_source_type in [
            DataSourceType.ASSOCIATION,
            DataSourceType.ASSOCIATION_MEMORY,
        ]:
            association = data_source.get("association")
            if not isinstance(association, dict):
                data_source["association"] = {"uuid": association}
        self.data_dict.update({"data_source": data_source})


class TreelistDataAdapter(DatalistDataAdapter):
    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["relation_linkage"]
        # self.extend_default_keys = ["common", "operate", "controller", "child_form_control"]
        # self.default_keys.extend(self.extend_default_keys)
        # self.run()

    def parse_columns(self, key, value):
        if value:
            name_ = self.component_dict.get("data_source", {}).get("name")
            tree_column = value[0]
            tree_column["input_control"] = 5
            tree_column["component"] = {
                "uuid": "tree_column",
                "type": ComponentType.TEXT,
                "data_source": {
                    "valueEdit": {
                        "uuid": lemon_uuid(),
                        "field": name_.get("field"),
                        "model": name_.get("model"),
                        "path": name_.get("path"),
                        "once": True,
                        "type": 4,
                    }
                },
            }
        return super().parse_columns(key, value)


class GridDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.run()


class GridRowDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.run()


class GridColDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.run()


class TabsDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.run()

    def parse_tabs(self, key, value):
        tabs = self._parse_deep_children(value)
        self.data_dict.update({"tabs": tabs})


class TabDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["badge"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class CollapseDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.run()

    def parse_panels(self, key, value):
        panels = self._parse_deep_children(value)
        self.data_dict.update({"panels": panels})


class PanelDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["badge"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class ExcelDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "templates", "events", "not_save", "default_template", "variables"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class SplitPageDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.run()

    def parse_panels(self, key, value):
        panels = self._parse_deep_children(value)
        self.data_dict.update({"panels": panels})


class SplitDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["badge"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class ContainerDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["disabled"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_style(self, key, value):
        # TODO back_color 和 class_name 可以统一一下格式
        back_color = self._parse_color(value, "backgroundColor")
        class_name_dict = value.get("class_name_value_editor", {})
        self.data_dict.update({
            "back_color": back_color,
            "style": {
                "class_name": class_name_dict
            }
        })


class BaseInputDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.default_keys.extend(
            [
                "field_info",
                "title",
                "editable",
                "placeholder",
                "tooltip",
                "is_required",
                "preprocessing",
            ]
        )
        self.run()


class InputDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["text_attribute", "number_attribute"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_input_setting(self, key, value):
        # 认为type=InputType.external_adapter的情况, 结构都这样
        if value.get("type") in [InputType.external_adapter]:
            settings = value.get("settings")
            uncalc_data_list = ["driver", "scheme"]
            for k, w_value in settings.items():
                if k not in uncalc_data_list:
                    self.data_dict.setdefault("input_external_adapter", {}).update(
                        {k: w_value.get("value")}
                    )


class TextareaDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["max_length_type", "max_length"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class RadioDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = []
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class CheckboxDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = []
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class SelectDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["data_source", "preprocessing"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class DatetimeDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = []
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class SwitchDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = []
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class SliderDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["max", "min"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class UploadFileDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["file_limit", "file_size", "extension"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class UploadImageDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["extension"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_count_limit(self, key, value):
        self.data_dict.update({"file_limit": value})

    def parse_max_file_size(self, key, value):
        self.data_dict.update({"file_size": value})


class ColorDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = []
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class CustomInputDataAdapter(BaseInputDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["custom"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class ReactDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.default_keys.extend(["func_uuid"])
        self.run()


class TextDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "format",
            "tooltip",
            "is_required",
            "title",
            "new_page",
            "badge",
            "show_title"
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_data_source(self, key, value):
        field_info = value.get(
            "field_info", {}
        )  # 之前编辑时的字段，用的是这个，为了兼容才保留
        value_editor = value.get("valueEdit", {})
        show_level_type = value.get("show_level_type", None)
        association = value.get("association", {})
        mapping_key = value.get("mapping_key", {})
        self.data_dict.update(
            {
                "data_source": {
                    "mapping_key": mapping_key,
                    "field_info": field_info,
                    "editor": value_editor,
                    "show_level_type": show_level_type,
                    "association": association,
                }
            }
        )
        # self.data_dict.update({"data_source": {"editor": value_editor}})

    def parse_style(self, key, value):
        color_type = value.get("colorType", "2")  # 1 主题  2 值编辑器
        self.data_dict.update({"color_type": color_type})
        font_color = self._parse_color(value, "colorEdit")
        back_color = self._parse_color(value, "backgroundColor")
        # 动态定义 className
        class_name_dict = value.get("class_name_value_editor", {})
        self.data_dict.update(
            {"font_color": font_color, "background_color": back_color, "class_name": class_name_dict}
        )


class ImageDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["data_source", "is_required", "badge"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class FileDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["is_required"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_data_source(self, key, value):
        # 上个版本没有 data_source ，需要兼容处理
        if value:
            self.data_dict.update({key: value})

    def parse_field_info(self, key, value):
        if value and value.get("uuid"):
            self.data_dict.update({"data_source": {key: value, "type": 1}})


class TagDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["format"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_data_source(self, key, value):
        value_editor = value.get("valueEdit", {})
        self.data_dict.update({"data_source": {"editor": value_editor}})

    def parse_style(self, key, value):
        font_color = self._parse_color(value, "colorEdit")
        fill_color = self._parse_color(value, "fillColorEdit")
        # 动态定义 className
        class_name_dict = value.get("class_name_value_editor", {})
        self.data_dict.update(
            {"font_color": font_color, "fill_color": fill_color, "class_name": class_name_dict}
        )


class ProgressDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.run()

    def parse_data_source(self, key, value):
        target_value_editor = value.get("valueEdit", {})
        current_value_editor = value.get("currentvalueEdit", {})
        should_value_editor = value.get("compltevalueEdit", {})
        self.data_dict.update(
            {
                "data_source": {
                    "target_value_editor": target_value_editor,
                    "current_value_editor": current_value_editor,
                    "should_value_editor": should_value_editor,
                }
            }
        )

    def parse_textStyle(self, key, value):
        text_color_dict = value.get("color", dict())
        text_color_type = text_color_dict.get("type")
        text_color_editor = text_color_dict.get("color", {})
        text_content_editor = value.get("textContent", {})
        self.data_dict.update(
            {
                "text_style": {
                    "text_color": {
                        "type": text_color_type,
                        "editor": text_color_editor,
                    },
                    "text_content": {"editor": text_content_editor},
                }
            }
        )

    def parse_style(self, key, value):
        current_color_dict = value.get("currentFiledColor", dict())
        should_color_dict = value.get("shouldCompleteFiledColor", dict())
        target_color_dict = value.get("targetFiledColor", dict())
        current_color_type = current_color_dict.get("type")
        current_color_editor = current_color_dict.get("color", {})
        should_color_type = should_color_dict.get("type")
        should_color_editor = should_color_dict.get("color", {})
        target_color_type = target_color_dict.get("type")
        target_color_editor = target_color_dict.get("color", {})
        self.data_dict.update(
            {
                "style": {
                    "target_color": {
                        "type": target_color_type,
                        "editor": target_color_editor,
                    },
                    "current_color": {
                        "type": current_color_type,
                        "editor": current_color_editor,
                    },
                    "should_color": {
                        "type": should_color_type,
                        "editor": should_color_editor,
                    },
                }
            }
        )


class CustomPresentDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["custom"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class RSelectDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "field_info",
            "candidate_preprocessing",
            "title",
            "editable",
            "placeholder",
            "expand_columns",
            "new_page",
            "view_page",
            "tooltip",
            "is_required",
            "display_type",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class TransferDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "candidate_preprocessing",
            "title",
            "editable",
            "placeholder",
            "display_columns",
            "search",
            "tooltip",
            "is_required",
            "pagination",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

    def parse_data_source(self, key, value):
        data_source = copy.deepcopy(value)
        association = data_source.get("association")
        if not isinstance(association, dict):
            data_source["association"] = {"uuid": association}
        self.data_dict.update({"data_source": data_source})


class RSelectPopupDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "field_info",
            "candidate_preprocessing",
            "title",
            "editable",
            "placeholder",
            "modal_page",
            "view_page",
            "select_page",
            "tooltip",
            "is_required",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class RTileDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "field_info",
            "candidate_preprocessing",
            "title",
            "editable",
            "placeholder",
            "tooltip",
            "is_required",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class FormSelectorDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "field_info",
            "candidate_preprocessing",
            "title",
            "editable",
            "placeholder",
            "tooltip",
            "is_required",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class RCascadeDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "field_info",
            "preprocessing",
            "title",
            "editable",
            "placeholder",
            "tooltip",
            "is_required",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class RTreeDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "field_info",
            "data_source",
            "preprocessing",
            "title",
            "editable",
            "placeholder",
            "tooltip",
            "is_required",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class DatetimeCycleDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = ["field_info", "editable", "tooltip", "is_required"]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class NormalButtonDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "title",
            "loading",
            "disabled",
            "button_color",
            "visible",
            "style",
            "badge",
            "function_bar",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class ScanCountDataAdapter(TextDataAdapter):

    def parse_data_source(self, key, value):
        field_info = value.get("field_info", {})
        value_editor = value.get("valueEdit", {})
        need_keys = [
            "scan_field",
            "scan_results",
            "scan_success_text",
            "mode",
            "success_button",
            "unit",
            "show_repeat_error",
            "count_rule",
            "show_title",
            "title",
            "visible",
            "style",
        ]
        data_source = {"field_info": field_info, "editor": value_editor}
        for key in need_keys:
            data_source[key] = value.get(key)
        self.data_dict.update({"data_source": data_source})


class PrintDataAdapter(BaseDataAdapter):
    def initialize(self):
        super().initialize()
        self.default_keys.extend(["disabled"])
        self.run()


class CustomExtendDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.default_keys.extend(["custom"])
        self.run()


class RSelectTableDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "field_info",
            "preprocessing",
            "title",
            "editable",
            "placeholder",
            "tooltip",
            "is_required",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class RCreateTableDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "field_info",
            "preprocessing",
            "title",
            "editable",
            "placeholder",
            "tooltip",
            "is_required",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class DropdownMenuDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "dropdown",
            "dropdown_style",
            "dropdown_text_content",
            "visible",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()


class MenuDataAdapter(BaseDataAdapter):

    def initialize(self):
        super().initialize()
        self.extend_default_keys = [
            "menu_content",
            "visible",
        ]
        self.default_keys.extend(self.extend_default_keys)
        self.run()

class ComponentDataAdapter(Configurable, BaseDataAdapter):

    @classmethod
    def configurable_base(cls, *args, **kwargs):
        component_type = kwargs.get("type", ComponentType.INPUT)
        data_adapter_dict = {
            ComponentType.DATALIST: DatalistDataAdapter,
            ComponentType.FORM: FormDataAdapter,
            ComponentType.CARDLIST: CardlistDataAdapter,
            ComponentType.CALENDAR: CalendarDataAdapter,
            ComponentType.INPUT: InputDataAdapter,
            ComponentType.TEXTAREA: TextareaDataAdapter,
            ComponentType.RADIO: RadioDataAdapter,
            ComponentType.CHECKBOX: CheckboxDataAdapter,
            ComponentType.SELECT: SelectDataAdapter,
            ComponentType.DATETIME: DatetimeDataAdapter,
            ComponentType.DATETIME_CYCLE: DatetimeCycleDataAdapter,
            ComponentType.SWITCH: SwitchDataAdapter,
            ComponentType.SLIDER: SliderDataAdapter,
            ComponentType.UPLOAD_FILE: UploadFileDataAdapter,
            ComponentType.UPLOAD_IMAGE: UploadImageDataAdapter,
            ComponentType.R_SELECT: RSelectDataAdapter,
            ComponentType.R_SELECT_POPUP: RSelectPopupDataAdapter,
            ComponentType.R_TILE: RTileDataAdapter,
            ComponentType.FORM_SELECTOR: FormSelectorDataAdapter,
            ComponentType.COLOR: ColorDataAdapter,
            ComponentType.TEXT: TextDataAdapter,
            ComponentType.IMAGE: ImageDataAdapter,
            ComponentType.FILE: FileDataAdapter,
            ComponentType.TAG: TagDataAdapter,
            ComponentType.LINE_BAR: ProgressDataAdapter,
            ComponentType.RING_BAR: ProgressDataAdapter,
            ComponentType.R_CASCADE: RCascadeDataAdapter,
            ComponentType.R_TREE: RTreeDataAdapter,
            ComponentType.NORMAL_BUTTON: NormalButtonDataAdapter,
            ComponentType.SCAN_COUNT: ScanCountDataAdapter,
            ComponentType.GRID: GridDataAdapter,
            ComponentType.GRID_ROW: GridRowDataAdapter,
            ComponentType.GRID_COL: GridColDataAdapter,
            ComponentType.TABS: TabsDataAdapter,
            ComponentType.CONTAINER: ContainerDataAdapter,
            ComponentType.TAB: TabDataAdapter,
            ComponentType.PRINT: PrintDataAdapter,
            ComponentType.COLLAPSE: CollapseDataAdapter,
            ComponentType.PANEL: PanelDataAdapter,
            ComponentType.SPLIT_PAGE: SplitPageDataAdapter,
            ComponentType.SPLIT: SplitDataAdapter,
            ComponentType.TREE: TreeDataAdapter,
            ComponentType.TREELIST: TreelistDataAdapter,
            ComponentType.DATAGRID: DatagridDataAdapter,
            ComponentType.CUSTOM_INPUT: CustomInputDataAdapter,
            ComponentType.CUSTOM_PRESENT: CustomPresentDataAdapter,
            ComponentType.CUSTOM_EXTEND: CustomExtendDataAdapter,
            ComponentType.R_SELECT_TABLE: RSelectTableDataAdapter,
            ComponentType.R_CREATE_TABLE: RCreateTableDataAdapter,
            ComponentType.TRANSFER: TransferDataAdapter,
            ComponentType.REACT: ReactDataAdapter,
            ComponentType.EXECL_TABLE: ExcelDataAdapter,
            ComponentType.DROPDOWN_MENU: DropdownMenuDataAdapter,
            ComponentType.MENU: MenuDataAdapter
        }
        for button_type in ComponentType.ALL_BUTTON:
            data_adapter_dict.update({button_type: NormalButtonDataAdapter})
        base_class = data_adapter_dict.get(component_type, BaseDataAdapter)
        return base_class


class RuleChainAdapter:

    def __init__(self, **rule_dict):
        self.rule_dict = rule_dict
        self.data_dict = dict()

    def convert(self):
        self.rulechain = sm_rulechain

        variable_list = self.rule_dict.get("variable_list", [])
        for variable in variable_list:
            variable.update(
                {
                    "default": StringValue(
                        value_type=VariableType.AUTO, value=None
                    ).to_dict()
                }
            )
        timer_list = self.rule_dict.get("timer_list", [])
        event_list = self.rule_dict.get("event_list", [])

        sm_trigger_dict = OrderedDict()

        sm_event_list = []
        for event in event_list:
            event_uuid = event["uuid"]
            event_name = event["name"]
            event_arg_list = event.get("arg_list", [])
            sm_event = Event(uuid=event_uuid, name=event_name, arg_list=event_arg_list)
            sm_event_list.append(sm_event)
            event_bind_list = []
            for arg in event.get("arg_list", []):
                # event_arg = EventArg(name=arg["name"], description=arg["description"], arg_type=0,
                #                     default=arg["default"])
                arg_bind_to = arg.get("bind_to", "")
                event_bind = EventBind(
                    arg_name=arg["name"], variable=arg_bind_to, description=""
                )
                event_bind_list.append(event_bind)
            tg_bind = TriggerBind(event_uuid=event_uuid, bind_list=event_bind_list)
            condition_list = []
            for condition in event.get("condition_list", []):
                condition.update({"to_state": self.rulechain.state_list[0].uuid})
                condition_list.append(condition)
            transition = Transition(
                name="transition", description="", condition_list=condition_list
            )

            et = EventTrigger(
                name=f"et_{event_name}",
                description="",
                transition=transition,
                event_list=[tg_bind],
            )

            sm_trigger_dict.update({event_uuid: et})
        self.rulechain.variable_list = variable_list
        self.rulechain.timer_list = timer_list
        for timer in timer_list:
            timer_uuid = timer["uuid"]
            timer_name = timer["name"]
            for event in timer.get("event_list", []):
                et = sm_trigger_dict.get(event)
                if et is not None:
                    transition = et.transition
                else:
                    transition = None
                tt = TimerTrigger(
                    name=f"tt_{timer_name}",
                    description="",
                    timer=timer["uuid"],
                    transition=transition,
                )
                trigger_uuid = str(hash(timer_uuid) + hash(event))
                sm_trigger_dict.update({trigger_uuid: tt})
        # todo: 添加timer触发器
        self.rulechain.event_list = sm_event_list
        self.rulechain.state_list[0].trigger_list = list(sm_trigger_dict.values())

        self.data_dict = self.rulechain.to_dict()
        self.data_dict.update(
            {
                "name": self.rule_dict.get("name"),
                "uuid": self.rule_dict.get("uuid"),
                "description": self.rule_dict.get("description"),
            }
        )


class WorkflowAdapter(object):

    def __init__(self, workflow_dict):
        self.workflow_dict = workflow_dict
        self.all_node_dict = dict()
        self.all_form_dict = dict()
        self.start_node_uuid = None
        self.start_node_name = None
        self.all_columns = list()
        self.all_node_columns = dict()
        nodes = self.workflow_dict.get("nodes", list())
        node_name_set = set()
        self.form_model = None
        for node_dict in nodes:
            node_uuid = node_dict.get("uuid")
            node_name = node_dict.get("name")
            node_type = node_dict.get("type")
            if node_type != NodeType.COUNTERSIGN and node_name in node_name_set:
                return_code = LREC.WORKFLOW_NODE_NAME_SAME
                return_code.message = return_code.message.format(name=node_name)
                raise LemonRuntimeError(return_code)
            else:
                node_name_set.add(node_name)
            self.all_node_dict[node_uuid] = {
                "name": node_name,
                "type": node_type,
                "data": node_dict,
            }
            if node_type == NodeType.START:
                self.start_node_uuid = node_uuid
                self.start_node_name = node_name
            settings = node_dict.get("settings", dict())
            handlers = settings.get("handlers", list())
            for h in handlers:
                v = h.get("value")
                self.process_editor_dict(node_uuid, v)

            lines = node_dict.get("lines", [])
            for line in lines:
                settings = line.get("settings", dict())
                v = settings.get("value")
                self.process_editor_dict(node_uuid, v)

            # 老数据messages可能是列表
            messages = node_dict.get("messages", dict()) or dict()
            messages_info = messages.get("messages_info")
            self.process_editor_dict(node_uuid, messages_info)

            subflow = node_dict.get("subflow", dict())
            submit_pk_dict = subflow.get("submit_pk")
            self.process_editor_dict(node_uuid, submit_pk_dict)
        if self.start_node_uuid is None:
            raise LemonRuntimeError(LREC.WORKFLOW_START_NODE_NOT_FOUND)

    def process_editor_dict(self, node_uuid, editor_dict):
        if isinstance(editor_dict, dict):
            editor = LemonSMValueEditor().init(editor_dict)
            if editor:
                path_columns = editor.path_columns
                self.all_columns.append(path_columns)
                node_columns = self.all_node_columns.setdefault(node_uuid, list())
                node_columns.append(path_columns)

    def get_rows(self, value):
        rows = value.get("rows", list())
        for row in rows:
            self.get_cols(row)

    def get_cols(self, value):
        cols = value.get("cols", list())
        for col in cols:
            self.get_children(col.get("children", list()))

    def get_tabs(self, component_dict):
        tabs = component_dict.get("tabs", list())
        for tab in tabs:
            self.get_children(tab.get("children", list()))

    def get_panels(self, component_dict):
        panels = component_dict.get("panels", list())
        for panel in panels:
            self.get_children(panel.get("children", list()))

    def get_children(self, value):
        for child in value:
            child_type = child.get("type")
            if child_type == ComponentType.FORM:
                form_uuid = child.get("uuid")
                self.all_form_dict[form_uuid] = child
            elif child_type == ComponentType.GRID:
                self.get_rows(child)
            elif child_type == ComponentType.CONTAINER:
                children = child.get("children", list())
                self.get_children(children)
            elif child_type == ComponentType.COLLAPSE:
                self.get_panels(child)
            elif child_type == ComponentType.SPLIT_PAGE:
                self.get_panels(child)
            elif child_type == ComponentType.TABS:
                self.get_tabs(child)

    async def get_form_data_source(self, page_uuid):
        if page_uuid is None:
            return dict()
        page_document = await engine.user_access.select_runtime_page_content_by_page_uuid(page_uuid)
        page_content = page_document.get(DocumentContent.document_content.name)
        container_model = page_document.get("container_model", dict())
        outer_form_uuid = None
        for form_uuid, d in container_model.items():
            if d.get("is_outer_container"):
                outer_form_uuid = form_uuid
                break
        if not outer_form_uuid:
            return dict()
        children = page_content.get("children", list())

        self.get_children(children)
        form_dict = self.all_form_dict.get(outer_form_uuid, dict())
        if not form_dict:
            return dict()
        data_dict = ComponentDataAdapter(**form_dict).data_dict
        return data_dict

    async def get_workflow_form_model(self, device_type, node_uuid=None):
        if node_uuid:
            page_uuid = self.get_node_form_page_uuid(device_type, node_uuid)
        else:
            page_uuid = self.get_start_form_page_uuid(device_type)
        data_dict = await self.get_form_data_source(page_uuid)
        data_source = data_dict.get("data_source", {})
        model_uuid = data_source.get("model")
        self.form_model = lemon_model(model_uuid)
        return model_uuid

    async def get_node_transition_data(self, node_uuid, form_pk):
        form_data = dict()
        if not self.form_model:
            await self.get_workflow_form_model(DeviceType.PC)
        model_uuid = self.form_model._meta.table_name
        model_alias_columns = list()
        for field_name in SystemField.FIELD_DICT:
            if field_name in ["_status", "_wf_status", "_creator", "id"]:
                full_sys_field_name = get_sys_field_uuid(model_uuid, field_name)
                sys_field = lemon_field(full_sys_field_name)
                model_alias_columns.append(sys_field)
        model_query = (
            self.form_model.select(*model_alias_columns)
            .where(self.form_model.id == form_pk)
            .dicts()
        )
        relation_column_dict, handler_columns = self.gen_relation_column(
            self.form_model, node_uuid
        )
        model_query = model_query.select_extend(*handler_columns)
        async with engine.userdb.objs.atomic():
            model_data_list = await engine.userdb.objs.execute(model_query)
        if model_data_list:
            model_data_list = list(model_data_list)
            form_data.update(model_data_list[0])
        await self.select_association_model(relation_column_dict, form_pk, form_data)
        return form_data

    async def select_association_model(
        self, association_model_dict, form_pk, form_data
    ):
        start_node = lemon_model_node(self.form_model._meta.table_name)
        for _model, model_dict in association_model_dict.items():
            columns = model_dict.get("columns")
            is_many = model_dict.get("is_many")
            path = model_dict.get("path", list())
            path_list = model_dict.get("path_list", list())
            if not path_list:
                path_list = [path]
            for path in path_list:
                model_with_path = build_model_field_path(_model._meta.table_name, path)
                model = _model.alias(model_with_path)
                alias_columns = list()
                for column in columns:
                    column = getattr(model, column.name)
                    if column.column_name == "id":
                        alias_name = build_model_field_path(
                            model._meta.table_name, path
                        )
                        alias_name += "_pk"
                        alias_columns.append(column.alias(alias_name))
                        if is_system_field(column):
                            column_name = get_sys_field_uuid(
                                model._meta.table_name, column.name
                            )
                            system_field_alias_name = build_field_path(
                                path, column_name
                            )
                            alias_columns.append(column.alias(system_field_alias_name))
                    alias_name = build_field_path(path, column.column_name)
                    alias_columns.append(column.alias(alias_name))
                    alias_columns.append(column.alias(column.column_name))
                end_node = lemon_model_node(model._meta.table_name)
                query = build_join(start_node, end_node, path=path, rename_model=True)
                query = (
                    query.select(*alias_columns)
                    .where(self.form_model.id == form_pk)
                    .dicts()
                )
                async with engine.userdb.objs.atomic():
                    data_list = await engine.userdb.objs.execute(query)
                if is_many:
                    for data in data_list:
                        for k, v in data.items():
                            if k != "id":
                                d_value = form_data.setdefault(k, list())
                                if isinstance(d_value, list):
                                    d_value.append(v)
                else:
                    if len(data_list) >= 1:
                        data = data_list[0]
                        data.pop("id", 0)
                        form_data.update(data)

    def gen_relation_column(self, data_model, node_uuid=None):

        handler_columns = list()
        relation_column_dict = dict()

        for info in self.all_node_columns.get(node_uuid, [{}]):
            for field_uuid, column_info in info.items():
                column = column_info.get("column")
                path = column_info.get("path")
                path_list = column_info.get("path_list")
                column_model = column.model
                # 自关联时, 需要判断是否有 path
                if column_model == data_model and not path:
                    handler_columns.append(column.alias(field_uuid))
                else:

                    field = lemon_field(field_uuid)
                    data_model_uuid = data_model._meta.table_name
                    data_model_node = lemon_model_node(data_model_uuid)
                    start_node = data_model_node
                    model_uuid = field.model._meta.table_name
                    end_node = lemon_model_node(model_uuid)
                    if not path_list:
                        path_list = [path]
                    for path in path_list:
                        # TODO 过滤重复path
                        is_many, last_path, model_query = build_join_info(
                            start_node,
                            end_node,
                            path=path,
                            lemon_association=lemon_association,
                            lemon_model_node=lemon_model_node,
                        )
                        if field:
                            field_model = field.model
                            relation_column_dict.setdefault(
                                field_model,
                                {
                                    "columns": set(),
                                    "is_many": is_many,
                                    "path": path,
                                    "path_list": list(),
                                },
                            )
                            relation_column_dict[field_model]["columns"].add(field)
                            relation_column_dict[field_model]["path_list"].append(path)
        return relation_column_dict, handler_columns

    def get_start_form_page_uuid(self, device_type):
        page_uuid = None
        settings = self.workflow_dict.get("settings", {})
        if device_type == DeviceType.PC:
            page_uuid = settings.get("pc_from", {}).get("page")
        elif device_type == DeviceType.MOBILE:
            page_uuid = settings.get("mobile_from", {}).get("page")
        elif device_type == DeviceType.PAD:
            page_uuid = settings.get("pad_from", {}).get("page")
        if not page_uuid:
            page_uuid = settings.get("total_from", {}).get("page")
        return page_uuid

    def find_real_node_dict(self, nodes, node_uuid, node_dict):
        for n in nodes:
            if n.get("type") == NodeType.PARALLEL:
                branches = n.get("data", dict()).get("branches", list()) or n.get(
                    "branches", list()
                )
                for b in branches:
                    self.find_real_node_dict(b.get("nodes"), node_uuid, node_dict)
            elif n.get("uuid") == node_uuid:
                node_dict.update(n)

    def get_node_form_page_uuid(self, device_type, node_uuid):
        page_uuid = None
        settings = self.workflow_dict.get("settings", {})
        nodes = self.workflow_dict.get("nodes", list())
        for n_dict in nodes:
            n_uuid = n_dict.get("uuid")
            if n_uuid == node_uuid:
                node_settings = n_dict.get("settings", {})
                if device_type == DeviceType.PC:
                    page_uuid = node_settings.get("pc_to", {}).get("page")
                elif device_type == DeviceType.MOBILE:
                    page_uuid = node_settings.get("mobile_to", {}).get("page")
                elif device_type == DeviceType.PAD:
                    page_uuid = node_settings.get("pad_to", {}).get("page")
                if not page_uuid:
                    page_uuid = node_settings.get("total_to", {}).get("page")
                break
        if not page_uuid:
            # node可能是分支中的节点
            node_dict = dict()
            self.find_real_node_dict(nodes, node_uuid, node_dict)
            if node_dict:
                node_settings = node_dict.get("settings", {})
                if device_type == DeviceType.PC:
                    page_uuid = node_settings.get("pc_to", {}).get("page")
                elif device_type == DeviceType.MOBILE:
                    page_uuid = node_settings.get("mobile_to", {}).get("page")
                elif device_type == DeviceType.PAD:
                    page_uuid = node_settings.get("pad_to", {}).get("page")
                if not page_uuid:
                    page_uuid = node_settings.get("total_to", {}).get("page")

        if not page_uuid:
            if device_type == DeviceType.PC:
                page_uuid = settings.get("pc_to", {}).get("page")
            elif device_type == DeviceType.MOBILE:
                page_uuid = settings.get("mobile_to", {}).get("page")
            elif device_type == DeviceType.PAD:
                page_uuid = settings.get("pad_to", {}).get("page")
        if not page_uuid:
            page_uuid = settings.get("total_to", {}).get("page")
        return page_uuid


class RuleChain(object):

    def __init__(
        self,
        p_context=None,
        rule_dict=None,
        connector: ClientConnector = None,
        root_machine_id=None,
        page_machine_id=None,
        parent_machine_id=None,
        lsm_class=None,
    ):
        self.p_context = p_context
        self.rule_dict = rule_dict
        self.connector = connector
        self.sid = self.connector.sid if self.connector else None
        self.root_machine_id = root_machine_id
        self.page_machine_id = page_machine_id or parent_machine_id
        self.parent_machine_id = parent_machine_id
        self.uuid = rule_dict.get("uuid")
        self.name = rule_dict.get("name")
        self.machine_id = self.uuid
        self.lsm_class = lsm_class or ComponentLemonStateMachine
        self.parent = None
        self._lsm = None

    async def create_lsm(self):
        data_adapter = RuleChainAdapter(**self.rule_dict)
        data_adapter.convert()
        self.lsm = self.lsm_class(component=self, **data_adapter.data_dict)
        await self.lsm.async_start_machine()

    @property
    def lsm(self):
        return None if self._lsm is None else self._lsm()

    @lsm.setter
    def lsm(self, value):
        self._lsm = None if value is None else weakref.ref(value)

    async def start(self):
        await self.create_lsm()
        # asyncio.create_task(self.page_inited())

    async def exit(self):
        self.connector = None
        if self.lsm:
            await self.lsm.stop()
            self.lsm = None
