# -*- coding:utf-8 -*-

import asyncio

from baseutils.log import app_log
from apps.entity import Func as FuncModel
from apps.exceptions import CheckNameError, CheckUUI<PERSON>rror, CheckUUIDUniqueError, ElementNotFoundError
from apps.utils import check_lemon_name, check_lemon_uuid
from baseutils.const import ReturnCode
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.ide_const import Variable, Func, Icon
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker


class ArgCheckerService(CheckerService):

    attr_class = Func.ATTR
    attr_uuid = attr_class.ARG_UUID
    attr_name = attr_class.ARG_NAME
    uuid_error = LDEC.ENUM_UUID_ERROR
    uuid_unique_error = LDEC.ENUM_UUID_UNIQUE_ERROR
    name_error = LDEC.ENUM_NAME_FAILED
    name_unique_error = LDEC.ENUM_NAME_NOT_UNIQUE
    
    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.model = self.element.get("model")
    
    # 检查 参数类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_arg_type(self):
        if self.type not in Variable.TYPE.ALL:
            attr = self.attr_class.ARG_TYPE
            return_code = LDEC.FUNC_ARG_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 参数所选数据模型 或 枚举类型 是否存在，如果不存在，会向错误列表添加一条报错信息
    def check_arg_model(self, app_model_uuid_set, app_enum_uuid_set):
        if self.model is None:
            if self.type == Variable.TYPE.ENUM:
                attr = self.attr_class.ARG_ENUM
                return_code = LDEC.FUNC_ARG_ENUM_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
            elif self.type in Variable.TYPE.WITH_MODEL:
                attr = self.attr_class.ARG_MODEL
                return_code = LDEC.VARIABLE_MODEL_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
        else:
            if self.type == Variable.TYPE.ENUM:
                if self.model not in app_enum_uuid_set:
                    attr = self.attr_class.ARG_ENUM
                    return_code = LDEC.VARIABLE_ENUM_NOT_EXISTS
                    self._add_error_list(attr=attr, return_code=return_code)
            elif self.type in Variable.TYPE.WITH_MODEL:
                if self.model not in app_model_uuid_set:
                    attr = self.attr_class.ARG_MODEL
                    return_code = LDEC.VARIABLE_MODEL_NOT_EXISTS
                    self._add_error_list(attr=attr, return_code=return_code)


class ReturnCheckerService(CheckerService):

    attr_class = Func.ATTR
    attr_uuid = attr_class.RETURN_UUID
    attr_name = attr_class.RETURN_NAME
    uuid_error = LDEC.ENUM_UUID_ERROR
    uuid_unique_error = LDEC.ENUM_UUID_UNIQUE_ERROR
    name_error = LDEC.ENUM_NAME_FAILED
    name_unique_error = LDEC.ENUM_NAME_NOT_UNIQUE
    
    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
    
    # 检查 返回值类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_arg_type(self):
        if self.type not in Variable.TYPE.ALL:
            attr = Func.ATTR.RETURN_TYPE
            return_code = LDEC.FUNC_RETURN_TYPE_NOT_SUUPORT
            self._add_error_list(attr=attr, return_code=return_code)


class FuncCheckerService(CheckerService):

    attr_class = Func.ATTR
    uuid_error = LDEC.FUNC_UUID_ERROR
    uuid_unique_error = LDEC.FUNC_UUID_UNIQUE_ERROR
    name_error = LDEC.FUNC_NAME_FAILED
    name_unique_error = LDEC.FUNC_NAME_NOT_UNIQUE
    allow_chinese_name = True
    allow_keywords = False
    allow_lemon_keywords = False

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, document_name: str, 
        element: dict, app_model_uuid_set:set, app_enum_uuid_set: set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.app_model_uuid_set = app_model_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        # self.module_model_name_set = module_model_name_set
        self.description = self.element.get("description")
        self.func = self.element.get("func")
        self.icon_type = self.element.get("icon_type")
        self.icon = self.element.get("icon")
        self.install_sm_toolbox = self.element.get("install_sm_toolbox")
        self.sm_tool_name = self.element.get("sm_tool_name", "")
        self.sm_tool_category = self.element.get("sm_tool_category", "")
        self.arg_list = self.element.get("arg_list", list())
        self.return_list = self.element.get("return_list", list())
        self.arg_uuid_set = set()
        self.arg_name_set = set()
        self.return_uuid_set = set()
        self.return_name_set = set()
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            FuncModel.app_uuid.name: self.app_uuid,
            FuncModel.module_uuid.name: self.module_uuid,
            FuncModel.document_uuid.name: self.document_uuid,
            FuncModel.func_uuid.name: self.element_uuid,
            FuncModel.func_name.name: self.element_name,
            FuncModel.description.name: self.description,
            FuncModel.func.name: self.func,
            FuncModel.icon.name: self.icon,
            FuncModel.install_sm_toolbox.name: self.install_sm_toolbox,
            FuncModel.sm_tool_name.name: self.sm_tool_name,
            FuncModel.sm_tool_category.name: self.sm_tool_category,
            FuncModel.arg_list.name: self.arg_list,
            FuncModel.return_list.name: self.return_list
        }
    
    def build_update_query_data(self):
        return {
            FuncModel.func_name.name: self.element_name,
            FuncModel.description.name: self.description,
            FuncModel.func.name: self.func,
            FuncModel.icon.name: self.icon,
            FuncModel.install_sm_toolbox.name: self.install_sm_toolbox,
            FuncModel.sm_tool_name.name: self.sm_tool_name,
            FuncModel.sm_tool_category.name: self.sm_tool_category,
            FuncModel.arg_list.name: self.arg_list,
            FuncModel.return_list.name: self.return_list,
            FuncModel.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return FuncModel.update(**query_data).where(
            FuncModel.func_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(func_uuid):
        return FuncModel.update(**{
                FuncModel.is_delete.name: True
            }).where(FuncModel.func_uuid==func_uuid)
    
    def check_modify(self, document_func_uuid_set):
        if self.element_uuid in document_func_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
    # 检查 图标类型 是否正确，如果不正确，会向错误列表添加一条报错信息
    # @checker.run
    def check_icon_type(self):
        if self.icon_type not in Icon.TYPE.ALL:
            attr = Func.ATTR.ICON_TYPE
            return_code = LDEC.FUNC_ICON_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 图标 是否在图标库中存在，如果不存在，会向错误列表添加一条报错信息
    def check_icon_exists(self, app_icon_uuid_set, app_image_uuid_set):
        if self.icon_type == Icon.TYPE.ICON:
            if self.icon not in app_icon_uuid_set:
                attr = Func.ATTR.ICON
                return_code = LDEC.FUNC_ICON_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
        elif self.icon_type == Icon.TYPE.IMAGE:
            if self.icon not in app_image_uuid_set:
                attr = Func.ATTR.ICON
                return_code = LDEC.FUNC_ICON_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 安装到状态机工具箱 是否设定正确，如果不正确，会向错误列表添加一条报错信息
    @checker.run
    def check_install_sm_toolbox(self):
        if not isinstance(self.install_sm_toolbox, bool):
            attr = Func.ATTR.INSTALL_SM_TOOLBOX
            return_code = LDEC.FUNC_INSTALL_SM_TOOBOX_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 状态机动作标题 格式是否正确，如果不正确，会向错误列表添加一条报错信息
    @checker.run
    def check_sm_tool_name(self):
        pass
        if self.install_sm_toolbox is True:
            attr = Func.ATTR.SM_TOOL_NAME
            return_code = LDEC.FUNC_SM_TOOL_NAME_FAILED
            if not check_lemon_name(self.sm_tool_name, chinese=True, size=40):
                self._add_error_list(attr, return_code)
    
    # 检查 状态机动作分类 是否存在，如果不存在，会向错误列表添加一条报错信息
    def check_sm_tool_category(self, sm_tool_category_uuid_set):
        # 忘记这里需要检查什么
        attr = Func.ATTR.SM_TOOL_CATEGORY
        # return_code = LDEC.FUNC_SM_TOOL_CATEGORY_FAILED
        # self._check_name(attr=attr, return_code=return_code)
        if self.install_sm_toolbox is True:
            pass
            # 这里暂时用不到，注释掉；等凡哥想清楚这个功能后面怎么做吧
            # if self.sm_tool_category not in sm_tool_category_uuid_set:
            #     return_code = LDEC.FUNC_SM_TOOL_CATEGORY_NOT_EXISTS
            #     self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 参数列表
    @checker.run
    def check_arg_list(self):
        for arg in self.arg_list:
            if not arg:
                error_code = LDEC.ELEMENT_NAME_NOT_FOUND
                error_code.message = error_code.message.format(name="参数列表")
                raise ElementNotFoundError(message=error_code)
            arg_checker_service = ArgCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, arg)
            arg_checker_service.check_name()
            arg_checker_service.check_name_unique(self.arg_name_set)
            arg_checker_service.check_arg_type()
            arg_checker_service.check_arg_model(
                self.app_model_uuid_set, self.app_enum_uuid_set)
    
    # 检查 返回值列表
    @checker.run
    def check_return_list(self):
        for re in self.return_list:
            return_checker_service = ReturnCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, re)
            return_checker_service.check_name()
            return_checker_service.check_name_unique(self.return_name_set)
            return_checker_service.check_arg_type()

    @checker.run
    def check_args_return_name(self):
        attr = self.attr_class
        arg_name_set = set()
        return_name_set = set()
        args_return_code = LDEC.FUNC_ARGS_NAME_ERROR
        return_return_code = LDEC.FUNC_RETURN_NAME_ERROR
        for arg in self.arg_list:
            arg_name = arg.get("name")
            if arg_name in arg_name_set:
                return_code = args_return_code
                return_code.message = return_code.message_init.format(name=arg_name)
                self._add_error_list(attr.FUNC_ARGS, return_code)
            else:
                arg_name_set.add(arg_name)
        for return_info in self.return_list:
            return_name = return_info.get("name")
            if return_name in return_name_set:
                return_code = return_return_code
                return_code.message = return_code.message_init.format(name=return_name)
                self._add_error_list(attr.FUNC_RETURN, return_code)
            else:
                return_name_set.add(return_name)
        # return_code.message = return_code.message.format(name=self.element_name)
        # self._add_error_list(attr.FUNC_RETURN, return_return_code)


class FuncDocumentCheckerService(DocumentCheckerService):
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, document_version: int, 
        app_func_list: list, app_model_uuid_set:set,
        app_enum_uuid_set: set, app_icon_uuid_set: set, 
        app_image_uuid_set: set, sm_tool_category_uuid_set: set, 
        module_model_name_set: set, 
        *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, FuncModel, *args, **kwargs)
        self.app_model_uuid_set = app_model_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_icon_uuid_set = app_icon_uuid_set
        self.app_image_uuid_set = app_image_uuid_set
        self.sm_tool_category_uuid_set = sm_tool_category_uuid_set
        self.module_model_name_set = module_model_name_set
        self.app_func_uuid_set = set()
        self.module_func_name_set = set()
        self.document_func_uuid_set = set()
        for func in app_func_list:
            func_uuid = func.get("func_uuid", "")
            func_name = func.get("func_name", "")
            func_module_uuid = func.get("module_uuid", "")
            func_document_uuid = func.get("document_uuid", "")
            func_is_delete = func.get("is_delete", "")

            # 找到原文档中所有的云函数，为了新增、更新、删除文档的云函数
            if func_document_uuid == self.document_uuid:
                self.document_func_uuid_set.add(func_uuid)
            else:
                # 排除当前文档所有的 func_uuid，获取应用的所有 func_uuid
                self.app_func_uuid_set.add(func_uuid)
                # 排除当前文档所有的 func_uuid，获取模块的所有 func_uuid
                # func.get("is_delete", "")是为了防止查询到已经删除的云函数的名称
                if func_module_uuid == self.module_uuid and not func_is_delete:
                    self.module_func_name_set.add(func_name)
        self.other_conflict_document_names_set = kwargs.get("other_conflict_document_names_set", set())

    @checker.run
    def check_func(self):
        func_checker_service = FuncCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, self.element,
                self.app_func_uuid_set, self.app_model_uuid_set, 
                self.app_enum_uuid_set, is_copy=self.is_copy, 
                module_model_name_set=self.module_model_name_set)
        func_uuid = func_checker_service.element_uuid
        try:
            func_checker_service.check_uuid()
            func_checker_service.check_uuid_unique(self.app_func_uuid_set)
            func_checker_service.check_name()
            func_checker_service.check_name_unique(self.module_func_name_set)
            func_checker_service.check_name_unique(self.other_conflict_document_names_set)
            func_checker_service.check_icon_exists(self.app_icon_uuid_set, self.app_image_uuid_set)
            func_checker_service.check_sm_tool_category(self.sm_tool_category_uuid_set)
            func_checker_service.check_all()
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(func_checker_service)
            raise e
        else:
            self.update_any_list(func_checker_service)

        # 找到新增 或 更新的云函数
        func_checker_service.check_modify(self.document_func_uuid_set)
        if func_checker_service.insert_query_list:
            self.document_insert_list.extend(func_checker_service.insert_query_list)
        if func_checker_service.update_query_list:
            self.document_update_list.extend(func_checker_service.update_query_list)

        # 找出删除的云函数，将其 is_delete 置为 True
        delete_func_uuid_set = self.document_func_uuid_set - set([func_uuid])
        for this_func_uuid in delete_func_uuid_set:
            query = func_checker_service.build_delete_query(this_func_uuid)
            self.document_delete_list.append(query)
