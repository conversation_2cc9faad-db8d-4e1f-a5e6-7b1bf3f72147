# -*- coding:utf-8 -*-

# import asyncio
import traceback
from functools import partial
from typing import Dict, AnyStr
from collections import OrderedDict

import ujson
import weakref

from baseutils.const import Code, ReturnCode, EventWhen, TagPermissionAction
from baseutils.log import app_log
from baseutils.utils import FuncRunError
from apps.utils import (
    PageFinder, PrintFinder, PrintContainerFinder, PrintDatalistFinder, LabelContainerFinder, calc_action
)
from apps.ide_const import (
    ComponentType, LinkageType, PrintType, ImageSourceType,
    ValueEditorType, PageEventType, DocumentType
)
from apps.entity import DocumentContent
from apps.services.document.page import page_controller
from runtime.component.utils import DatalistIterQueue
from runtime.const import LemonRuntimeErrorCode as LREC
from runtime.const import RefreshType
from runtime.engine import engine
from runtime.utils import (
    LemonDictResponseCommand as LDRC, LemonMessage,
    decorator_helper, LemonDictResponseExport
)
from runtime.core.utils import LemonE<PERSON>, build_topic
from runtime.core.variable import Variable
from runtime.core.sm import PageLemonStateMachine
from runtime.core.connector import ClientConnector
from runtime.core.utils import lemon_field, lemon_model
from runtime.core.value_editor import LemonValueEditor
from runtime.component.base import (
    BaseComponent, BaseControlComponent, BaseContainerComponent, DropdownMenu
)
from runtime.component.data_adapter import RuleChain
from runtime.component.parser import PageParser
from runtime.component.utils import public_data_format
from runtime.log import runtime_log
from tests.document.sm_page import sm_page
from tests.utils import UUID


class Page(BaseComponent):

    data_convert = False
    rulechain_class = RuleChain
    component_type = ComponentType.PAGE
    lsm_class = PageLemonStateMachine
    state_machine_dict = sm_page.to_dict()

    def __init__(self):
        super().__init__()
        self.p_context = dict()
        self.page_location = dict()
        self.data_context = dict()
        self.containers = weakref.WeakValueDictionary()
        # 页面上的控件 （控件对象）
        self.data_controls = OrderedDict()
        self.data_controls: OrderedDict[AnyStr, BaseControlComponent]
        # 页面上的数据容器 （数据容器对象）
        self.container_tree: Dict[AnyStr, BaseContainerComponent] = dict()
        self.controls = dict()  # 页面上的控件 （dict 原始定义数据）
        self.form_row_data = dict()
        self.rulechain_dict = dict()
        self.is_refreshing = False
        self.print_queue = DatalistIterQueue()
        self.all_component = dict()

    def update_component_dict(self, component_obj: BaseComponent):
        self.all_component.update({component_obj.uuid: component_obj})

    def init_connector(self, connector: ClientConnector) -> None:
        super().init_connector(connector)
        self.connector.current_page = self

    def init_nested_data(self, nested=False) -> None:
        self.nested = nested
        if not self.nested:
            self.connector.root_page = self

    def init_root_form_data(
            self, root_form_uuid=None, form_nested=False, depth=0,
            memory_storage=False):
        self.root_form_uuid = root_form_uuid  # 最外层表单的 machine_id
        self.form_nested = form_nested  # 是否嵌在最外层表单内
        self.depth = depth  # 嵌在最外层表单内的深度
        self._memory_storage = memory_storage

    def init_component_dict(self, component_dict, data_convert=False) -> None:
        super().init_component_dict(component_dict, data_convert)
        self.controller = self.component_dict.get("controller", {})

    def init_p_context(self, p_context):
        if isinstance(p_context, dict):
            self.p_context.update(p_context)

    def init_data_context(self, data_context):
        if isinstance(data_context, dict):
            self.data_context.update(data_context)

    def initialize(self):
        self.connector.add_page_container(self, is_page_obj=True)
        self.machine_in_page = weakref.WeakValueDictionary()
        self.model_in_page = dict()
        self.linkage_in_page = dict()
        self.filter_data_dict = dict()
        # 运行时的 PageFinder 无需获取 子表、关联子表的模型数据
        # 在运行这些组件的时候，会解析出来
        self.page_finder = PageFinder(self.model_in_page)
        self.page_finder.find_func(self.component_dict)
        self.print_control = self.page_finder.print_control
        app_log.debug(f"model_in_page: {self.model_in_page}")
        # page_parser 找到页面所有的容器
        self.page_parser = PageParser(self)
        self.component_uuid_tree = dict()
        self.page_parser.dfs(self.component_dict, self.component_uuid_tree)
        self.page_parser.get_children(self.component_dict)
        self.find_container_machine()
        self.print_data_source = self.find_print_data_source()
        app_log.debug(f"linkage_in_page: {self.linkage_in_page}")
        # app_log.debug(f"linkage_in_page: {self.linkage_in_page}")
        for component_uuid, component_list in self.linkage_in_page.items():
            container = self.containers.get(component_uuid)
            if container:
                for component in component_list:
                    component.linkage_container = container
        page_watch_variables = page_controller.get("watch_variables", list())
        self.p_context.update({
            "component": weakref.proxy(self),
            "watch_variables": page_watch_variables,
            "print_control": self.print_control,
            "machine_in_page": self.machine_in_page,
            "linkage_in_page": self.linkage_in_page,
            "model_in_page": self.model_in_page
        })
        self.collect_events()

    def collect_events(self):
        page_events = {}
        events = self.component_dict.get("page_events", [])
        # 将事件按{事件类型{调用时间[(事件1其他信息),(事件2其他信息)]}}分类
        for event in events:
            event_type = event.get("type")
            # 页面事件前端不会传调用时间了，需要设置默认的 'after' 才会正确调用页面事件
            event_call_at = event.get("call_at", EventWhen.AFTER)
            event_func = event.get("func")
            event_data_as_param = event.get("data_as_param")
            event_error_tip = event.get("error_tip")
            event_type_data = page_events.setdefault(event_type, dict())
            event_call_at_data = event_type_data.setdefault(event_call_at, list())
            event_call_at_data.append((event_func, event_data_as_param, event_error_tip))
        self.page_events = page_events

    async def _entry(self):
        # 切换租户后，sid 不变，但需要更新租户数据
        self.form_row_data.clear()
        self.is_refreshing = False
        self.lsm.lemon.system.update_user(self.connector.current_user)
        self.update_parent_component_tree()
        await self.lsm.start()
        # for _, control in self.data_controls.items():
        #     await control.entry()

    async def publish_exit_data(self):
        if self.parent and self.machine_id in self.parent.component_tree:
            variable_dict = self.build_exit_data()
            await self.parent.handle_exit(self.machine_id, variable_dict)
            app_log.debug(f"m_id: {self.machine_id} publish exit.")

    async def _exit(self):
        await self.publish_exit_data()
        self.p_context = dict()
        await self.lsm.stop()
        self.update_parent_component_tree(delete=True)
        if self.connector.root_page is self:
            self.connector.root_page = None
        # app_log.info(f"sys.getrefcount(self): {sys.getrefcount(self)}")
        # 清理 connector
        # if self.connector.root_page is self:
        #     self.connector.delete_page_container(page_uuid=self.connector.root_page.uuid)
        # else:
        #     self.connector.delete_page_container(machine_id=self.uuid)
        # app_log.info(f"sys.getrefcount(self): {sys.getrefcount(self)}")

        # 清理 control
        # for _, control in self.data_controls.items():
        #     await control.exit()
        # self.data_controls = dict()

        # 清理 容器
        # for container in self.container_tree.values():
        #     app_log.info(f"container: {container.uuid}")
        #     await container.exit()
        # self.container_tree = dict()
        app_log.info("exit page done.")

        # 清理 其他
        # self.component_dict = dict()
        # self.state_machine_dict = dict()
        # self.controls = dict()
        # self.machine_in_page = dict()
        # self.model_in_page = dict()
        # self.linkage_in_page = dict()
        # self.page_finder.exit()
        # self.print_control = None
        # self.page_finder = None
        # self.print_data_source = dict()
        # if self.lsm is not None:
        #     await self.lsm.stop()
        #     self.lsm = None
        # self.p_context = dict()
        # self.connector = None
        # gc.collect()
        # refers = gc.get_referrers(self)
        # for ref in refers:
        #     app_log.info(f"ref: {ref}, {type(ref)}, {id(ref)}")
        # app_log.info(f"sys.getrefcount(self): {sys.getrefcount(self)}")
        await super()._exit()

    async def _run_page_events(self, event_type, when=EventWhen.AFTER, model=None, need_throw_exception=True):
        type_events = self.page_events.get(event_type, {})
        events = type_events.get(when, [])
        await self.run_events(events, event_type, when, model, need_throw_exception=need_throw_exception)

    def build_exit_data(self):
        parent_control_uuid = self.parent_control_uuid
        self.lsm.child_variable_dict.update(
            {"parent_control_uuid": parent_control_uuid})
        app_log.info(parent_control_uuid)
        return self.lsm.child_variable_dict

    def update_from_variable_dict(self, variable_dict):
        for name, value in variable_dict.items():
            self.lsm.child_variable_dict[name] = value
            # 只能是全局变量，没办法知道当前状态机处于什么状态
            variable = self.globals.get(name)
            if isinstance(variable, Variable):
                variable.value = value

    async def handle_exit(self, child_machine_id, variable_dict):
        await super().handle_exit(child_machine_id, variable_dict)
        self.update_from_variable_dict(variable_dict)
        app_log.info(f"m_id: {self.machine_id}, page sys_done")

    async def handle_form_data(self, variable_dict):
        self.update_from_variable_dict(variable_dict)
        await self.publish_exit_data()

    def update_form_row_data(self, form, control, value, form_pk=None):
        if not form or form.component_type != ComponentType.FORM:
            return
        form_uuid = form.uuid
        form_pk = form_pk or form.pk
        control_uuid = control.uuid
        self.form_row_data.setdefault(form_uuid, dict())
        self.form_row_data[form_uuid].setdefault(
            form_pk, {"form": {}, "control": {}})
        k = "form" if self.component_type == ComponentType.FORM else "control"
        self.form_row_data[form_uuid][form_pk][k].update(
            {control_uuid: value})

    def get_form_row_data(self, form):
        form_uuid = form.uuid
        form_pk = form.pk
        return self.form_row_data.get(form_uuid, dict()).get(form_pk, {})

    def update_form_row_data_pk(self, form):
        form_uuid = form.uuid
        form_row_data = self.form_row_data.setdefault(form_uuid, {})
        if form.snapshot_pk in form_row_data:
            row_data = form_row_data.get(form.snapshot_pk)
            form_row_data.update({form.pk: row_data})

    @property
    def sm(self):
        if self.lsm:
            return self.lsm.sm
        return None

    @property
    def page(self):
        return self

    @property
    def root_page(self):
        if self.nested:
            return super().root_page
        else:
            return self

    @property
    def machine_id(self):
        return self.uuid

    @property
    def root_machine_id(self):
        return self.root.machine_id

    @property
    def page_machine_id(self):
        return self.machine_id

    @property
    def parent_machine_id(self):
        return self.parent.machine_id

    @property
    def parent_control_uuid(self):
        return self.p_context.get("parent_control_uuid")

    @property
    def memory_storage(self):
        if self.parent_control_uuid:
            # 如果是 关联下拉框 新建按钮 打开的页面，则默认不是 内存存储
            return False
        return self.parent.memory_storage

    @property
    def ignore_save_search_option(self):
        return self.p_context.get("ignore_save_search_option")

    def get_context_trigger_obj(self):
        return self.parent.get_obj()

    def find_print_data_source(self):
        print_data_source = dict()
        for control_uuid, print_control_dict in self.print_control.items():
            pc_data_source = print_data_source.setdefault(control_uuid, dict())
            data_source = print_control_dict.get("data_source", dict())
            if isinstance(data_source, dict):
                for print_c_uuid, page_c_uuid in data_source.items():
                    if print_c_uuid and page_c_uuid:
                        if page_c_uuid in self.machine_in_page:
                            component = self.machine_in_page.get(page_c_uuid)
                            machine_id = component.machine_id
                            pc_data_source.update({
                                print_c_uuid: {
                                    "component": page_c_uuid,
                                    "machine": machine_id}
                                })
        return print_data_source

    def find_container_machine(self):
        for component_uuid, component in self.container_tree.items():
            if component.component_type in ComponentType.BASE_DATA_CONTAINER_LIST:
                # machine_id = component.machine_id
                # TODO: 如何处理嵌套
                self.machine_in_page.update({component_uuid: component})
                root_form_id = None
                self.find_child_container_machine(component, 0, root_form_id)

    '''
    @description: 递归寻找页面下的状态机
    @param {*} self
    @param {*} component
    @param {*} depth
    @return {*}
    @author: lv.jimin
    '''
    def find_child_container_machine(self, component, depth, root_form_id):
        if hasattr(component, "container_tree") and depth < 10:
            depth += 1
            # if isinstance(component, Form):
            #     if not root_form_id:
            #         root_form_id = component.machine_id
            #         component.outer_form_id = None
            #     else:
            #         component.outer_form_id = root_form_id
            container_tree = component.container_tree
            self.machine_in_page.update(container_tree)
            for container in container_tree.values():
                self.find_child_container_machine(
                    container, depth=depth, root_form_id=root_form_id)

    async def send_export_message(
            self, export_control_uuid, file_url=None, error=None, default_name=None, **kwargs):
        need_default_file_name = kwargs.get("need_default_file_name", False)
        if need_default_file_name:
            return_code = Code.RUNTIME_OK
        else:
            return_code = Code.RUNTIME_OK if file_url else ReturnCode(-1, error)
        data = dict()
        data.update(kwargs)
        data.update({
                "component": export_control_uuid, "url": file_url,
                "default_file_name": default_name
        })
        message = LemonDictResponseExport(
            return_code, data=data)
        app_log.info(f"export_message: {message}")
        await self.connector.call(message, topic=self.lsm.result_topic)

    async def send_import_message(
            self, export_control_uuid, error_dict=dict(), **kwargs):
        return_code = Code.RUNTIME_OK
        data = dict()
        data.update(kwargs)
        data.update({
                "error_dict": error_dict,
                "component": export_control_uuid
        })
        message = LemonDictResponseExport(
            return_code, data=data)
        app_log.info(f"export_message: {message}")
        await self.connector.call(message, topic=self.lsm.result_topic)

    async def _run_print(
            self, print_control_uuid, print_control, print_params, print_data):
        print_control_dict = print_control.get(print_control_uuid, {})
        data_source = print_control_dict.get("data_source", {})
        is_skip_preview = print_control_dict.get("is_skip_preview", False)
        is_cpcl_print = print_control_dict.get("is_cpcl_print", False)
        printer_name = print_control_dict.get("printer_name", "")
        print_type = "cpcl" if is_cpcl_print else None
        # 一个打印组件上, 选中的打印模板上, 放了多个打印容器 同时对应多个 页面上的容器组件
        from_event = False
        for print_c_uuid, page_c_uuid in data_source.items():
            component = self.machine_in_page.get(page_c_uuid)
            app_log.info(f"page_c_uuid: {page_c_uuid}, {component}")
            if component:
                app_log.info(f"print_control_uuid: {print_control_uuid}")
                app_log.info(f"print_params: {print_params}")
                data = await component.get_filter_data(print_params)

                # 页面打印组件
                # print_control_uuid = data.get("print_control_uuid")
                machine_id = data.get("machine_id")
                filter_data = data.get("filter_data")
                # from_event = data.get("from_event", True)  # 是否为页面点击触发
                self.filter_data_dict.update({machine_id: filter_data})
        print_control_data_source = self.print_data_source.get(
            print_control_uuid, dict())
        kwargs = {"is_skip_preview": is_skip_preview, "is_cpcl_print": is_cpcl_print, "printer_name": printer_name}
        app_log.info(f"print_control_data_source: {print_control_data_source}")
        await self._handle_control_print(
            print_control_uuid, print_control_data_source, print_data,
            print_type=print_type, from_event=from_event, **kwargs)

    async def page_inited(self):
        event_uuid = UUID.sm_page.event_inited
        event_topic = self.lsm.build_event_topic(event_uuid)
        event_msg = LemonMessage(
            data={"event_args": dict()}, topic=event_topic)
        app_log.info(f"page inited: event_topic {event_topic}")
        await self.connector.call(event_msg)

    async def send_page_inited(self):
        event_uuid = UUID.sm_page.event_inited
        await self.send_event(event_uuid, event_args=dict(), publish=True)

    async def send_page_refresh(self):
        event_uuid = UUID.sm_page.event_refresh
        await self.send_event(event_uuid, event_args=dict(), publish=True)

    async def send_page_print(self, control_uuid, pk=None, from_event=False):
        event_uuid = UUID.sm_page.event_print
        event_args = {"print_control": control_uuid}
        print_params = {"pk": pk, "from_event": from_event}
        event_args.update({"print_params": print_params})
        # await self.send_event(event_uuid, event_args=event_args, publish=True)
        await self.connector.run_callbacks.put(
            partial(self.send_event, event_uuid, event_args=event_args))

    async def refresh(self, init=False, call_later=False):
        # self.instances.db.clear()
        # self.form_row_data.clear()
        await self.connector.run_callbacks.put(self.page_inited)
        for container in self.container_tree.values():
            app_log.info(f"container: {container}")
            await container.refresh(init=init, call_later=call_later)

    async def refresh_result(self, call_later=False, *args, **kwargs):
        if not self.is_refreshing:
            self.is_refreshing = True
            # 这里 call_later 是必须的，因为当前正处于事件中
            # 如果直接执行事件，状态机的事件判断会失败，不再执行 inited 事件
            # await self.send_page_inited()
            await self.connector.run_callbacks.put(self.page_inited)
            # self.instances.db.clear()
            # self.form_row_data.clear()
            refresh_type = kwargs.get("refresh_args", {}).get("refresh_type")
            if refresh_type == RefreshType.REFRESH_ONLY:
                for container in self.container_tree.values():
                    app_log.info(f"container: {container}")
                    await container.refresh_only(*args, **kwargs)
            elif refresh_type == RefreshType.WITH_DATA:
                for container in self.container_tree.values():
                    app_log.info(f"container: {container}")
                    await container.refresh_result(
                        call_later=call_later, *args, **kwargs)
        self.is_refreshing = False

    async def control_editor_value(self, control_editor_dict: dict):
        result_dict = {}
        editor_dict = control_editor_dict.get("editor_dict", {})
        for control_uuid, editor_list in editor_dict.items():
            control_obj = self.data_controls.get(control_uuid)
            control_obj = control_obj or self.container_tree.get(control_uuid)
            if control_obj:
                result = await control_obj.calcu_editor_list(editor_list)
            else:
                result = {}
            # result_dict[control_uuid] = result
            result_dict.update(result)
        request_uuid = control_editor_dict.get("request_uuid")
        result = {"result": result_dict}
        # 多次请求,前端以此判断是哪次请求
        if request_uuid:
            result.update({"request_uuid": request_uuid})
        return result

    async def page_print(self, control, pk=None):
        for control_uuid, print_control_dict in self.print_control.items():
            control_name = print_control_dict.get("name")
            if control_name == control:
                await self.send_page_print(
                    control_uuid, pk=pk, from_event=False)
                break

    async def create_lsm(self):
        if self.lsm:
            return
        lsm = self.lsm_class(component=self, **self.state_machine_dict)
        self.lsm = lsm
        self.update_parent_component_tree()
        await self.lsm.async_start_machine()

    async def create_rulechain(self):
        rulechain_uuid = self.controller.get("rulechain")
        if rulechain_uuid:
            self.rulechain_dict = \
                await engine.access.select_rulechain_content_by_uuid(
                    rulechain_uuid)
        if self.rulechain_dict:
            rulechain = self.rulechain_class(
                self.p_context, self.rulechain_dict, connector=self.connector,
                root_machine_id=self.root_machine_id,
                parent_machine_id=self.parent_machine_id)
            self.rulechain = rulechain
            await self.rulechain.start()

    async def start(self):
        await self.create_lsm()
        await self.create_rulechain()
        event_type = PageEventType.CREATE
        need_throw_exception = engine.config.get("NEED_THROW_EXCEPTION")
        await self._run_page_events(event_type, need_throw_exception=need_throw_exception)
        await self.page_inited()

    @decorator_helper.cloud_func
    async def inited(self, sm=None):
        form_data, editor_data, children_data = dict(), dict(), dict()
        children_editor_data = dict()
        data_in_page = {
            "sm": self.machine_id, "data": form_data,
            "editor": editor_data, "children": children_data,
            "children_editor": children_editor_data,
            "context": self.data_context
        }
        for control_uuid, control_dict in self.controls.items():
            if not control_dict:
                continue
            if control_uuid in self.data_controls:
                # 在创建 布局组件（栅格、容器等）时，此时页面还未创建完成
                # 所以这些 布局组件 的 lsm 为 None，需要再重置一下
                control_obj = self.data_controls[control_uuid]
                control_obj.set_lsm(self.lsm)
                continue
            control = {"component": control_dict}
            control_type = control_dict.get("type")
            control_obj = engine.component_creator.create_component(
                component_type=control_type, control=True,
                component_dict=control, parent=self)
            self.data_controls.update({control_uuid: control_obj})
        event_type = PageEventType.LOAD
        need_throw_exception = engine.config.get("NEED_THROW_EXCEPTION")
        await self._run_page_events(event_type, need_throw_exception=need_throw_exception)
        for control_uuid, control_obj in self.data_controls.items():
            app_log.info(f"control_obj: {control_obj}")
            f_data, c_data, e_data = await control_obj.calc_result()
            form_data.update(f_data)
            children_data.update(c_data)
            children_editor_data.update({control_uuid: e_data})
        title_editor_dict = self.component_dict.get("page_title")
        if title_editor_dict and isinstance(title_editor_dict, dict):
            title = await self._calc_editor(title_editor_dict, calc_data=True)
            editor_uuid = title_editor_dict.get("uuid")
            editor_data.update({editor_uuid: title})
        children_list = self.component_dict.get("children", [])
        for children in children_list:
            children_uuid = children.get("uuid")
            data_source = children.get("data_source", {})
            tooltips = data_source.get("tooltips", [])
            for tooltip in tooltips:
                edit_value_dict = tooltip.get("edit_value_dict", {})
                tooltip_uuid = tooltip.get("uuid")
                tooltip_edit_uuid = edit_value_dict.get("uuid")
                if tooltip_edit_uuid and tooltip_uuid:
                    tooltip_title = await self._calc_editor(edit_value_dict, calc_data=True)
                    children_editor_data.update(
                        {children_uuid: {tooltip_edit_uuid: tooltip_title}})
        for container in self.container_tree.values():
            is_visible_dict = container.component_dict.get(
                "visible", {}).get("is_visible", {})
            is_visible_uuid = is_visible_dict.get("uuid")
            visible_value = await self._calc_editor(is_visible_dict, calc_data=True)
            real_visible_value = self.calc_container_permission_config(container, visible_value)
            editor_type = is_visible_dict.get("type")
            if editor_type not in [
                ValueEditorType.STRING, ValueEditorType.CONST
            ] or visible_value != real_visible_value:
                children_editor_data.update({
                    container.uuid: {is_visible_uuid: real_visible_value}})
        return data_in_page

    def calc_container_permission_config(self, container, visible_value):
        permission_config = container.component_dict.get("permission_config", {})
        enable = permission_config.get("enable", False)
        tags = permission_config.get("tag", [])
        be_controlled = enable and tags and engine.pm.permission_check
        if be_controlled:
            target_action = TagPermissionAction.VISIBLE
            visible_value = calc_action(self.connector.current_user, tags, target_action)
        return visible_value

    async def do_linkage(
            self, machine_id, event_uuid, linkage_args, event_key="event"):
        event_topic = build_topic(
            self.sid, machine_id, event_key, event_uuid)
        event_msg = LemonMessage(
            data={"event_args": linkage_args}, topic=event_topic)
        await self.connector.call(event_msg)

    @decorator_helper.cloud_func
    async def linkage(self, sm):
        linkage_in_page = sm.globals.linkage_in_page
        linkage_component = sm.locals.linkage_component
        linkage_args = sm.locals.linkage_args
        linkage_data = linkage_in_page.get(linkage_component, {})
        linkage_type = linkage_data.get("type", 0)
        if linkage_type == LinkageType.DATALIST_WITH_FORM:  # 数据列表 with 表单
            linkage_forms = linkage_data.get("forms", [])
            for form_uuid in linkage_forms:
                await self.do_linkage(
                    form_uuid, LemonEvent.form_inited, linkage_args)
        elif linkage_type in [
                LinkageType.SEARCH_BAR_WITH_BI,
                LinkageType.ITEM_FILTER_WITH_BI]:
            if linkage_type == LinkageType.SEARCH_BAR_WITH_BI:
                await self.process_linkage_args(linkage_args)
            linkage_args.update(
                {"source": linkage_component, "filter_type": linkage_type})
            linkage_args = {"data_filter": linkage_args}
            linkage_charts = linkage_data.get("charts", [])
            for chart_uuid in linkage_charts:
                await self.do_linkage(
                    chart_uuid, LemonEvent.chart_inited, linkage_args)
        elif linkage_type in [
                LinkageType.DATALIST_WITH_TIMELINE,
                LinkageType.FORM_WITH_TIMELINE,
                LinkageType.CARDLIST_WITH_TIMELINE]:
            linkage_timelines = linkage_data.get("timeline", [])
            for timeline_uuid in linkage_timelines:
                await self.do_linkage(
                    timeline_uuid, LemonEvent.timeline_inited, linkage_args)

    async def process_linkage_args(self, linkage_args):
        for _, search_item in linkage_args.items():
            if search_item.get("input_control") == "RELATIONSELECT":
                values = search_item.get("values", None)
                if values is None:
                    continue
                elif isinstance(values, list):
                    new_values = []
                    for option in values:
                        new_values.append(option.get("value"))
                    search_item.update({"values": new_values})
                else:
                    search_item.update({"values": values.get("value")})

    @decorator_helper.cloud_func
    async def print(self, sm=None):
        print_control = self.globals.print_control
        pc_uuid = self.locals.print_control
        print_params = self.locals.print_params
        print_data = self.locals.print_data
        app_log.info(f"pc_uuid: {pc_uuid}")
        try:
            app_log.info(f"pc_uuid: {pc_uuid}, p_params: {print_params}")
            await self._run_print(
                pc_uuid, print_control, print_params, print_data)
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            # TODO: 发送打印失败的消息
            await self.send_print_message(pc_uuid)
        finally:
            # 无论是否成功，都要清空
            self.filter_data_dict = dict()

    @decorator_helper.cloud_func
    async def page_refresh(self, sm):
        refresh_args = sm.locals.refresh_args
        await self.refresh_result(refresh_args=refresh_args)

    @decorator_helper.cloud_func
    async def page_calcu_editor(self, sm):
        control_editor_dict = sm.locals.control_editor
        result = await self.control_editor_value(control_editor_dict)
        return result

    async def event(self, msg):
        data = msg.data
        params = data.get("event_args", dict())
        index = msg.index
        handle = msg.handle
        return_code = Code.RUNTIME_OK
        stdout, stderr, result = "", "", True
        if isinstance(params, dict):
            control_uuid = params.get("control_uuid")
            control_obj = self.data_controls.get(control_uuid)
            app_log.info(f"control_obj: {control_obj}")
            if not control_obj:
                control_obj = self.container_tree.get(control_uuid)
            if control_obj:
                stdout, result = await control_obj.calc_event(
                    {}, run_func=True, index=index, handle=handle,
                    kwargs=params)
        if isinstance(result, FuncRunError):
            return_code = LREC.EventFuncExecError
            if result.rollback:
                await self.connector.run_callbacks.put(self.raise_rollback)
        message = LDRC(return_code, data={
            "stdout": stdout, "stderr": stderr,
            "component": self.machine_id, "command": msg.command,
            "request_id": msg.request_id})
        # await self.lsm.connector.publish(self.lsm.result_topic, message)
        await self.lsm.connector.call(message, topic=self.lsm.result_topic)

    @decorator_helper.cloud_func
    async def page_dropdown(self, sm):
        control_uuid = sm.locals.dropdown_field
        other_info = sm.locals.other_info or dict()
        control_obj = self.data_controls.get(control_uuid)
        page = None
        select_in_column = {}
        if isinstance(control_obj, DropdownMenu):
            page = 1
            select_in_column.update({control_obj.uuid: await control_obj.dropdown(self)})
        if page and isinstance(select_in_column, dict):
            select_in_column.update({"page": page})
        return select_in_column

    def _calc_control_column(self, value_editor_dict):
        if isinstance(value_editor_dict, dict):
            value_editor = LemonValueEditor(**value_editor_dict)
            return value_editor.path_columns
        else:
            return dict()

    def _calc_aggregation_func(
            self, control_dict, columns, index, data_aggre_items):
        agg_dict = control_dict.get("aggregation", {})
        use_aggregation = agg_dict.get("use_aggregation")
        if use_aggregation:
            column_info = list(columns.values())[0]
            column = column_info.get("column")
            if not column:
                return
            agg_func = agg_dict.get("func")
            column_name = column.column_name
            column_alias_name = "_".join([
                column_name, str(index), str(agg_func)])
            column_path = column_info.get("path")
            path_column = lemon_field(column_name, column_path)
            # agg_field = select_aggregation(agg_func, path_column)
            if path_column:
                path_column_alias = path_column.alias(column_alias_name)
                data_aggre_items.update({
                    column_alias_name: path_column_alias})

    async def _calc_container_control(self, control_tree):
        data_columns = []
        data_controls = []
        data_aggre_result = []
        data_aggre_items = {}
        column_indexs = []
        control_indexs = []
        index = 0
        for control_uuid, control_dict in control_tree.items():
            control_type = control_dict.get("type")
            control_data_source = control_dict.get("data_source", {})
            # edit 编辑字段， 返回给前端 value 使用， 需要根据控件类型返回相应的值
            # handle 操作字段， 列的分组、筛选、过滤、排序 时使用， 字段值编辑器 或 字段选择器 这里才有值
            column_dict = {
                "edit": None,
                "edit_path": [],
                "handle": None,
                "handle_path": [],
                "other": {}    # 其他字段， 表达式值编辑器里的选择的任何字段
            }
            if control_type == PrintType.TEXT:
                if isinstance(control_data_source, dict):
                    edit_value_dict = control_data_source.get("edit_value", {})
                    columns = self._calc_control_column(edit_value_dict)
                    column_dict.update({"other": columns})
                    self._calc_aggregation_func(
                        control_dict, columns, index, data_aggre_items)
                control_indexs.append(index)
            elif control_type == PrintType.IMAGE:
                if isinstance(control_data_source, dict):
                    c_data_source_type = control_data_source.get("type", 0)
                    if c_data_source_type == ImageSourceType.FIELD:
                        field_uuid = control_data_source.get("field")
                        path = control_data_source.get("path", [])
                        column = lemon_field(field_uuid)
                        if column:
                            columns = {
                                field_uuid: {
                                    "column": column, "path": path,
                                    "path_list": [path]}
                            }
                            column_dict.update(
                                {"edit": column, "edit_path": path})
                            column_dict["other"].update(columns)
                            self._calc_aggregation_func(
                                control_dict, columns, index, data_aggre_items)
                    elif c_data_source_type == ImageSourceType.CODE:
                        edit_value_dict = control_data_source.get("code_data")
                        columns = self._calc_control_column(edit_value_dict)
                        column_dict.update({"other": columns})
                        self._calc_aggregation_func(
                            control_dict, columns, index, data_aggre_items)
                control_indexs.append(index)
            elif control_type is None:  # 数据列表的 column
                edit_value_dict = control_data_source.get("edit_value", {})
                columns = self._calc_control_column(edit_value_dict)
                column_dict.update({"other": columns})
                control_info = control_dict.get("component", {})
                # control_uuid = control_info.get("uuid")
                control_type = control_info.get("type")
                self._calc_aggregation_func(
                    control_dict, columns, index, data_aggre_items)
                column_indexs.append(index)
            data_columns.append(column_dict)
            data_controls.append(control_dict)
            data_aggre_result.append("")
            index += 1
        return data_columns, data_controls, data_aggre_result, \
            data_aggre_items, column_indexs, control_indexs

    async def _calc_control_data(self, data_dict, column_dict, control_dict):
        control_data = ""
        # control_uuid = control_dict.get("uuid")
        control_type = control_dict.get("type")
        if control_type is None or control_type == PrintType.TEXT:
            data_source = control_dict.get("data_source", {})
            if isinstance(data_source, dict):
                edit_value_dict = data_source.get("edit_value", {})
                if edit_value_dict:
                    value_editor = self.lemon_editor.init(edit_value_dict)
                    # 这里的 值编辑器 不会用到模型对象，所以直接用 page 的 lsm
                    value_editor.set_globals(self.lsm)
                    if value_editor.type in [
                            ValueEditorType.EXPR, ValueEditorType.FIELD]:
                        value_editor.update_globals(**data_dict)
                        await value_editor.calc_value()
                        v = await value_editor.value_async
                    else:
                        v = value_editor.value
                    control_data = "" if v is None else v
                    column = value_editor.column
                    column_format = control_dict.get("format", {}) or {}
                    column_format['separator'] = getattr(
                        value_editor, 'separator', None)
                    control_data = public_data_format(
                        control_data, column, column_format)
        elif control_type == PrintType.IMAGE:
            data_source = control_dict.get("data_source", {})
            data_source_type = data_source.get("type", 0)
            if data_source_type == ImageSourceType.IMAGE:  # 图片表
                image_dict = data_source.get("image", dict())
                image_url = image_dict.get("url")
                if isinstance(image_url, str):
                    control_data = image_url
            elif data_source_type == ImageSourceType.FIELD:  # 字段
                column = column_dict.get("edit")
                if column:
                    column_name = column.column_name
                    image_url_list = []
                    control_value = data_dict.get(column_name)
                    if isinstance(control_value, list):
                        for c_value in control_value:
                            if c_value and isinstance(c_value, dict):
                                image_url = c_value.get("url")
                                if isinstance(image_url, str):
                                    image_url_list.append(image_url)
                    if image_url_list:
                        control_data = image_url_list[0]  # 暂时只取第一个
            elif data_source_type == ImageSourceType.CODE:
                data_editor_dict = data_source.get("code_data", {})
                # data_value_editor = lemon_editor.init(data_editor_dict)
                value_editor = LemonValueEditor(**data_editor_dict)
                # 这里的 值编辑器 不会用到模型对象，所以直接用 page 的 lsm
                value_editor.set_globals(self.lsm)
                value_editor.update_globals(**data_dict)
                control_data = value_editor.value or ""
        return control_data

    async def _calc_datalist_control_data(
            self, data_columns, data_controls, data_list, data_aggre_result,
            column_indexs, control_indexs):
        # data_columns 是以 column_dict 组成的列表
        all_line_data_list, all_line_control_data_list = [], []
        for data_dict in data_list:
            line_data_list = []
            line_data_dict = {}
            for index, control_dict in enumerate(data_controls, start=0):
                column_dict = data_columns[index]
                control_value = await self._calc_control_data(
                    data_dict, column_dict, control_dict)
                if index in column_indexs:
                    line_data_list.append(control_value)
                elif index in control_indexs:
                    control_uuid = control_dict.get("uuid")
                    line_data_dict.update({control_uuid: control_value})
            all_line_data_list.append(line_data_list)
            all_line_control_data_list.append(line_data_dict)
        agg_data_list, control_agg_data_dict = [], {}
        index = 0
        for control_index, control_dict in enumerate(data_controls, start=0):
            # control_type = control_dict.get("type")
            aggregation = control_dict.get("aggregation", {})
            use_aggregation = aggregation.get("use_aggregation", False)
            format_info = control_dict.pop("format_info", None)
            column_data = data_aggre_result[control_index]
            agg_data = column_data
            try:
                if format_info:
                    column_format = format_info.get("column_format")
                    column = format_info.get("column")
                    agg_data = public_data_format(
                        column_data, column, column_format)
            except Exception:
                format_error = traceback.format_exc()
                app_log.error(format_error)
                await runtime_log.error(format_error)
                agg_data = ""
            if control_index in column_indexs:
                agg_data_list.append(agg_data)
            if use_aggregation and control_index in control_indexs:
                control_uuid = control_dict.get("uuid")
                control_agg_data_dict.update({control_uuid: column_data})
            index += 1
        return all_line_data_list, agg_data_list, \
            all_line_control_data_list, control_agg_data_dict

    async def _calc_cardlist_control_data(
            self, data_columns, data_controls, data_list):
        # data_columns 是以 column_dict 组成的列表
        all_line_data_list = list()
        for data_dict in data_list:
            line_data = {}
            for index, control_dict in enumerate(data_controls, start=0):
                control_uuid = control_dict.get("uuid")
                column_dict = data_columns[index]
                control_value = await self._calc_control_data(
                    data_dict, column_dict, control_dict)
                line_data.update({control_uuid: control_value})
            all_line_data_list.append(line_data)
        return all_line_data_list, list(), list(), dict()

    async def _calc_form_control_data(
            self, data_columns, data_controls, data_dict):
        # data_columns 是以 control_uuid 为 key  column_dict 为 value 的 dict
        form_data_dict = dict()
        for control_dict in data_controls:
            control_uuid = control_dict.get("uuid")
            column_dict = data_columns[control_uuid]
            control_value = await self._calc_control_data(
                data_dict, column_dict, control_dict)
            form_data_dict.update({control_uuid: control_value})
        return form_data_dict

    async def _calc_datalist_data(
            self, container_dict, filter_data, component, is_datalist=True):
        if not filter_data:
            return list(), list(), list(), dict()
        source_data_list = filter_data.get("source_data_list", list())
        if source_data_list:
            # 表示页面上的卡片列表用的值编辑器，可以通过值编辑器计算出卡片列表上需要的所有数据
            return list(), list(), list(), dict()
        else:
            if is_datalist:
                container_finder = PrintDatalistFinder()
            else:
                container_finder = PrintContainerFinder()
            container_finder.find_func(container_dict)
            container_data_source = container_dict.get("data_source", dict())
            if not isinstance(container_data_source, dict):
                return list(), list(), list(), dict()
            data_model_uuid = container_data_source.get("model")
            filter_model_uuid = filter_data.get("model_uuid")
            app_log.info(f"d: {data_model_uuid}, f: {filter_model_uuid}")
            if data_model_uuid != filter_model_uuid:
                return list(), list(), list(), dict()

            # 打印模板的数据列表，同时有 columns 和 children
            # 需要将 columns 和 children 的数据，分开发送给打印生成服务器
            container_finder.control_tree.update(container_finder.column_tree)
            data_columns, data_controls, data_aggre_result, data_aggre_items, \
                col_indexs, con_indexs = await self._calc_container_control(
                    container_finder.control_tree)
            data_list, data_aggre_result = await component.get_print_data(
                data_columns, data_aggre_result, data_aggre_items)
            if is_datalist:
                return await self._calc_datalist_control_data(
                    data_columns, data_controls, data_list, data_aggre_result,
                    col_indexs, con_indexs)
            else:
                return await self._calc_cardlist_control_data(
                    data_columns, data_controls, data_list)

    async def _calc_form_data(self, container_dict, filter_data, component):
        if not filter_data:
            return dict()
        source_data_list = filter_data.get("source_data", list())
        if source_data_list:
            # 表示页面上的表单用的值编辑器，可以通过值编辑器计算出表单上需要的所有数据
            return dict()
        else:
            container_finder = PrintContainerFinder()
            container_finder.find_func(container_dict)
            container_data_source = container_dict.get("data_source", dict())
            if not isinstance(container_data_source, dict):
                return dict()
            data_model_uuid = container_data_source.get("model")
            filter_model_uuid = filter_data.get("model_uuid")
            if data_model_uuid != filter_model_uuid:
                return dict()

            data_pk = filter_data.get("pk")
            result = await component.get_print_data(
                container_finder.control_tree, data_pk)
            return result

    async def _calc_label_data(self, container_dict, filter_data, component):
        if not filter_data:
            return dict()
        source_data_list = filter_data.get("source_data", list())
        container_finder = LabelContainerFinder()
        container_finder.find_func(container_dict)
        container_data_source = container_dict.get("data_source", dict())
        if not isinstance(container_data_source, dict):
            return dict()
        data_model_uuid = container_data_source.get("model")
        filter_model_uuid = filter_data.get("model_uuid")
        if data_model_uuid != filter_model_uuid:
            return dict()

        data_pk = filter_data.get("pk")
        result = await component.get_print_data(container_finder.control_tree, data_pk)
        return result

    async def _calc_container_data(
            self, container_dict, filter_data, component_uuid):
        container_uuid = container_dict.get("uuid")
        container_type = container_dict.get("type")
        component = self.connector.find_page_container(component_uuid)
        if container_type == PrintType.CARDLIST:
            data_list, _, _, _ = await self._calc_datalist_data(
                container_dict, filter_data, component, is_datalist=False)
            return {container_uuid: data_list}
        elif container_type == PrintType.DATALIST:
            data_list, agg_data, control_data_list, control_agg_data \
                = await self._calc_datalist_data(
                    container_dict, filter_data, component, is_datalist=True)
            return {container_uuid: {
                "data": data_list, "agg": agg_data,
                "control_data": control_data_list,
                "control_agg": control_agg_data}}
        elif container_type == PrintType.FORM:
            data_dict = await self._calc_form_data(
                container_dict, filter_data, component)
            return {container_uuid: data_dict}

    async def gen_print_file(
            self, print_content, request_print_data, print_control_uuid,
            print_type=None, tenant_uuid=None, from_event=False, merge_data=None, **kwargs):
        if print_type is None:
            print_type = self.locals.print_type or "pdf"
        suffix_dict = {
            DocumentType.PRINT: "generate",
            DocumentType.LABEL_PRINT: "print_label_generate"
        }
        document_type = print_content.get("document_type", DocumentType.PRINT)
        suffix = suffix_dict.get(document_type)
        app_log.info(f"tenant_uuid: {tenant_uuid}")
        print_domain = engine.config.PRINT_DOMAIN or engine.config.DOMAIN
        
        json_body = {
            "tenant_uuid": tenant_uuid, "print_content": print_content,
            "print_data": request_print_data
        }
        post_url = f"http://{print_domain}:5001/{print_type}/{suffix}"
        
        if document_type == DocumentType.LABEL_PRINT and print_type == "PDF":
            if merge_data is not None:
                json_body_in_merge = merge_data.get("json_body")
                if json_body_in_merge:
                    json_body_in_merge.get("print_data").append(request_print_data)
                else:
                    json_body.update({"print_data": [request_print_data]})
                    merge_data.update({
                        "json_body": json_body,
                        "post_url": post_url,
                        "print_control_uuid": print_control_uuid,
                    })
                return

        pdf_url, return_code = await self.get_pdf_url(json_body, post_url, print_type)
        print_in_page_value = {}
        print_in_page_value = {print_control_uuid: pdf_url}
        if from_event:
            self.globals.print_in_page = print_in_page_value
        else:
            orientation = print_content.get("orientation")  # 打印模板的打印方向
            await self.send_print_message(
                print_control_uuid, pdf_url, print_type, orientation, return_code=return_code, **kwargs)

    async def _handle_form_print(self, data, **kwargs):
        # 表单打印事件
        machine_id = data.get("machine_id")
        filter_data = data.get("filter_data")
        print_type = data.get("print_type", "pdf")
        print_content = data.get("print_content")
        pc_data_source = data.get("print_container_data_source")
        print_finder = PrintFinder()
        print_finder.find_func(print_content)
        request_print_data = dict()
        for _, container_dict in print_finder.container_tree.items():
            container_type = container_dict.get("type")
            if container_type == PrintType.FORM and pc_data_source:
                # 打印组件 上可以将 打印模板 与 页面上的数据容器 分别对应打印
                # machine_id = pc_data_source.get("machine")
                component_uuid = pc_data_source.get("component")
                container_data = await self._calc_container_data(
                    container_dict, filter_data, component_uuid)
                request_print_data.update(container_data)
                break
        tenant_uuid = None
        current_user = self.lsm.lemon.system.current_user
        if current_user:
            tenant_uuid = current_user.tenant_id
        app_log.info(f"r_print_data: {ujson.dumps(request_print_data)[:200]}")
        await self.gen_print_file(
            print_content, request_print_data, machine_id,
            print_type=print_type, tenant_uuid=tenant_uuid,
            from_event=False, **kwargs)

    async def _handle_label_print(self, data, merge_data, **kwargs):
        # 表单打印事件
        machine_id = data.get("machine_id")
        filter_data = data.get("filter_data")
        print_type = data.get("print_type", "pdf")
        print_content = data.get("print_content")
        pc_data_source = data.get("print_container_data_source")
        request_print_data = dict()
        component_uuid = pc_data_source.get("component")
        component = self.connector.find_page_container(component_uuid)
        container_data = await self._calc_label_data(print_content, filter_data, component)
        request_print_data.update(container_data)
        tenant_uuid = None
        current_user = self.lsm.lemon.system.current_user
        if current_user:
            tenant_uuid = current_user.tenant_id
        app_log.info(f"r_print_data: {ujson.dumps(request_print_data)[:200]}")
        await self.gen_print_file(
            print_content, request_print_data, machine_id,
            print_type=print_type, tenant_uuid=tenant_uuid,
            from_event=False, merge_data=merge_data, **kwargs)

    async def _handle_form_export(self, data, **kwargs):
        machine_id = data.get("machine_id")
        file_url = data.get("file_url")
        error = data.get("error", "")
        default_name = data.get("default_file_name")
        await self.send_export_message(
            machine_id, file_url, error=error, default_name=default_name, **kwargs)

    async def _handle_form_import(self, data, **kwargs):
        machine_id = data.get("machine_id")
        error_dict = data.get("error_dict", dict())
        await self.send_import_message(
            machine_id, error_dict=error_dict, **kwargs)

    async def _handle_control_print(
            self, print_control_uuid, print_data_source, print_data,
            print_type=None, from_event=False, **kwargs):
        tenant_uuid = None
        current_user = self.lsm.lemon.system.current_user
        if current_user:
            tenant_uuid = current_user.tenant_id
        print_data = dict() if not isinstance(print_data, dict) else print_data

        print_uuid = self.print_control.get(print_control_uuid, dict()).get(
            "selectedPage", dict()).get("page_uuid")
        print_dict = await engine.access.select_print_content_by_print_uuid(
            print_uuid, ext_tenant=tenant_uuid)
        print_content = print_dict.get(DocumentContent.document_content.name)
        print_finder = PrintFinder()
        print_finder.find_func(print_content)
        request_print_data = dict()
        for c_uuid, c_dict in print_finder.container_tree.items():
            pc_data_source = print_data_source.get(c_uuid)
            if isinstance(pc_data_source, dict) and pc_data_source:
                # 打印组件 上可以将 打印模板 与 页面上的数据容器 分别对应打印
                machine_id = pc_data_source.get("machine")
                component_uuid = pc_data_source.get("component")
                filter_data = self.filter_data_dict.get(machine_id, dict())
                container_data = await self._calc_container_data(
                    c_dict, filter_data, component_uuid)
                request_print_data.update(container_data)
        for visual_uuid in print_finder.visual_list:
            visual_data = print_data.get(visual_uuid)
            if visual_data:
                request_print_data.update({visual_uuid: visual_data})
        for form_uuid, visual_list in print_finder.form_visual_tree.items():
            for visual_uuid in visual_list:
                visual_data = print_data.get(visual_uuid)
                if visual_data:
                    form_container_data = request_print_data.setdefault(
                        form_uuid, dict())
                    form_container_data.update({visual_uuid: visual_data})
        # 重置 filter_data_dict
        self.filter_data_dict = dict()
        app_log.info(f"r_print_data: {ujson.dumps(request_print_data)[:200]}")
        await self.gen_print_file(
            print_content, request_print_data, print_control_uuid, print_type=print_type,
            tenant_uuid=tenant_uuid, from_event=from_event, **kwargs)