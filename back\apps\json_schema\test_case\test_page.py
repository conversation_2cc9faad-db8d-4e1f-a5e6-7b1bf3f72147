from apps.json_schema.main import PageValidator, AppCtx, Validator
from apps.ide_const import Document
import json
import os
from loguru import logger
import asyncio
from apps.settings import create_app, create_config
from apps.engine import engine


async def main():
    app = create_app()
    config = create_config()
    app.config.from_object(config)
    engine.init_app(app)
    engine.init_service()
    page_validator = Validator(Document.TYPE.PAGE, engine)
    await page_validator.pre_validate("52c46acb306355c4b98279d4bc6d4fc7")
    with open(os.path.dirname(__file__) + "/page.json", "r") as f:
        data = json.load(f)
    for error in page_validator.validate(data):
        logger.info(error)
    await page_validator.handle_reference_data("fdfbaff780d65a1680daef0704877ee6", page_validator._validator.references)

asyncio.run(main())