# encoding: utf-8

import re
import json
import traceback

import xml.etree.ElementTree as ET
from aiohttp import ClientSession

from apps.engine import engine
from apps.entity import IconFont
from apps.utils import LemonDictResponse as LDR
from baseutils.log import app_log


async def sync_iconfont(app_uuid):
    iconfont_config = await engine.access.get_app_iconfont_config(app_uuid)
    project_url = iconfont_config.get('project_url')
    app_info = await engine.access.get_app_by_app_uuid(app_uuid=app_uuid)
    app_id = app_info.id
    if project_url:
        icon_list = await engine.access.list_iconfont_name(app_uuid)
        name_list = icon_list[0].get('name_list') or []
        name_list_set = set(json.loads(name_list)) if name_list else set()
        new_name_set = set()
        data_list = []
        async with ClientSession(trust_env=True) as session:
            try:
                result = await session.get(f"https:{project_url}")
                content = await result.text()
                xml_content = re.findall(r'(<svg>.*?</svg>)', content)
                tree = ET.fromstring(xml_content[0])
                for child in tree.findall('symbol'):
                    name = child.attrib.get('id')
                    if name.startswith('icon'):
                        font_name = name[4:]
                    else:
                        font_name = name
                    font_class = f'app{app_id}-{font_name}'
                    child.set('id', f'icon{font_class}')
                    content = ET.tostring(child)
                    data_list.append({
                        IconFont.name.name: name,
                        IconFont.content.name: content,
                        IconFont.is_delete.name: False,
                        IconFont.app_uuid: app_uuid,
                        IconFont.font_class.name: font_class
                    })
                    new_name_set.add(name)
            except Exception as e:
                app_log.info(traceback.print_exc())
                return dict(message='同步失败，链接可能不正确或链接不是Symbol格式')
            await engine.access.update_iconfont(app_uuid, data_list)
            delete_name_set = name_list_set - new_name_set
            if delete_name_set:
                await engine.access.delete_iconfont_by_name(app_uuid, list(delete_name_set))
    return dict()


async def gather_iconfont(app_uuid):
    custom_icon_list = await engine.access.list_iconfont(app_uuid)
    system_icon_list = await engine.access.list_iconfont(app_uuid="system")
    all_icon = [custom_icon_list, system_icon_list]
    tree = ET.fromstring('<svg></svg>')
    for icon_list in all_icon:
        for icon in icon_list:
            content = icon.get(IconFont.content.name)
            icon_element = ET.fromstring(content)
            tree.append(icon_element)
    return ET.tostring(tree)
