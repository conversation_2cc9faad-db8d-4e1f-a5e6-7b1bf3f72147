# -*- coding:utf-8 -*-

import weakref
import asyncio

from baseutils.utils import clone_obj
from apps.ide_const import VariableType


class Variable(object):

    def __init__(self, v_dict, lsm=None):
        self.uuid = v_dict.get("uuid")
        self.name = v_dict.get("name")
        self.type = v_dict.get("type", VariableType.AUTO)
        self.model = v_dict.get("model", None)
        self.default_dict = v_dict.get("default", dict())
        self.value_editor = None
        self._lsm = None
        self.lsm = lsm
        self._value_set = False
        self._value = None

    def set_value(self, value):
        # 不发送变量更新通知
        # app_log.info(f"set v: {self}")
        # app_log.debug(f"set v: {self}, value: {value}")
        self._value = value
        self._value_set = True

    @property
    def lsm(self):
        return None if self._lsm is None else self._lsm()

    @lsm.setter
    def lsm(self, value):
        self._lsm = None if value is None else weakref.ref(value)

    @property
    def value(self):
        # 赋值后，以后均用赋的值
        if self._value_set is True:
            return self._value

        # 值编辑器的 value 只是初始值
        if self.value_editor is None:
            self.value_editor = self.lsm.editor.init(self.default_dict)
        v = self.value_editor.value
        if not v:
            if isinstance(v, dict):
                v = dict()
            elif isinstance(v, list):
                v = list()
            self.set_value(v)
        return v

    @value.setter
    def value(self, value):
        # 发送变量更新通知
        self._value = value
        self._value_set = True
        if self.lsm:
            asyncio.create_task(self.lsm.variable_publish(self))

    async def set_value_async(self, value):
        self.set_value(value)
        if self.lsm:
            await self.lsm.variable_publish(self)

    def __repr__(self):
        return "<%s('%s')('%s')@%s>" % (type(self).__name__, self.uuid, self.name, id(self))

    def clone(self, lsm):
        return clone_obj(self, **{"lsm": lsm})


class VariableProxy(dict):

    def __init__(self, v_dict=None):
        if isinstance(v_dict, dict):
            self.update(v_dict)

    def __getattr__(self, k):
        v = self.get(k, None)
        if isinstance(v, Variable):
            return v.value
        return v

    def __setattr__(self, name, value) -> None:
        v = self.get(name, None)
        if isinstance(v, Variable):
            v.set_value(value)
            return None
        return super().__setattr__(name, value)
