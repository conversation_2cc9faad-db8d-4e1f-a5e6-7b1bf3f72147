# -*- coding:utf-8 -*-

from apps.utils import <PERSON><PERSON>, lemon_uuid
from apps.ide_const import (
    NodeType, HandlerType, HandleType, NodeSystemActionType
)


class LineSettings(Json):

    def __init__(self, condition_name, action=None, default=False, value=None, to_node=None, *args, **kwargs):
        self.condition_name = condition_name
        self.action = action
        self.default = default
        self.value = dict() if value is None else value
        self.to_node = to_node
        super().__init__(*args, **kwargs)


class NodeLine(Json):

    def __init__(self, name, description="", settings=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.description = description
        self.settings = dict() if settings is None else settings
        super().__init__(*args, **kwargs)


class NodeMessage(Json):

    def __init__(self, name, message_type, to=None, content=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.type = message_type
        self.to = dict() if to is None else to
        self.content = dict() if content is None else content
        super().__init__(*args, **kwargs)


class NodeTimeout(<PERSON>son):

    def __init__(
        self, name, timeout_type=0, timeout_days=0, timeout_hours=0, 
        urging_days=0, urging_hours=0, urging_limit=1, urging_content=None, 
        timeout_to_node=None, timeout_content=None, assign_handler=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.type = timeout_type
        self.timeout_days = timeout_days
        self.timeout_hours = timeout_hours
        self.urging_days = urging_days
        self.urging_hours = urging_hours
        self.urging_limit = urging_limit
        self.urging_content = dict() if urging_content is None else urging_content
        self.timeout_to_node = timeout_to_node
        self.timeout_content = dict() if timeout_content is None else timeout_content
        self.assign_handler = dict() if assign_handler is None else assign_handler
        super().__init__(*args, **kwargs)


class CustomAction(Json):

    def __init__(self, name, transition_rule_type, transition_rule_value, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.transition_rule_type = transition_rule_type
        self.transition_rule_value = transition_rule_value
        super().__init__(*args, **kwargs)


class SystemAction(Json):

    def __init__(self, accept_name, reject_name, *args, **kwargs):
        self.accept_uuid = NodeSystemActionType.ACCEPT
        self.accept_name = accept_name
        self.reject_uuid = NodeSystemActionType.REJECT
        self.reject_name = reject_name
        super().__init__(*args, **kwargs)


class RoleHandler(Json):

    def __init__(self, role_uuid, role_name, *args, **kwargs):
        self.type = HandlerType.ROLE
        self.role_uuid = role_uuid
        self.role_name = role_name
        super().__init__(*args, **kwargs)


class ValueEditorHandler(Json):

    def __init__(self, value_editor_dict, *args, **kwargs):
        self.type = HandlerType.VALUE
        self.value = value_editor_dict
        super().__init__(*args, **kwargs)


class ManagerHandler(Json):

    def __init__(self, value, *args, **kwargs):
        self.type = HandlerType.ROLE
        self.value = value
        super().__init__(*args, **kwargs)



class AutoNodeSttings(Json):

    def __init__(self, func=None, arg_list=None, result=None, *args, **kwargs):
        self.func = dict() if func is None else func
        self.arg_list = list() if arg_list is None else arg_list
        self.result = dict() if result is None else result
        super().__init__(*args, **kwargs)


class ManualNodeSettings(Json):

    def __init__(
        self, handlers=None, pc_to=None, mobile_to=None, pad_to=None, 
        accept_return=True, accept_assign=True, handle_type=0, *args, **kwargs):
        self.handlers = list() if handlers is None else handlers
        self.pc_to = dict() if pc_to is None else pc_to
        self.mobile_to = dict() if mobile_to is None else mobile_to
        self.pad_to = dict() if pad_to is None else pad_to
        self.accept_return = accept_return
        self.accept_assign = accept_assign
        self.handle_type = handle_type
        super().__init__(*args, **kwargs)


class StartNode(Json):

    def __init__(self, name, lines=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.type = NodeType.START
        self.lines = list() if lines is None else lines
        super().__init__(*args, **kwargs)


class EndNode(Json):

    def __init__(self, name, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.type = NodeType.END
        self.lines = list()
        super().__init__(*args, **kwargs)


class ManualNode(Json):

    def __init__(
        self, name, settings=None, form_settings=None, actions=None, 
        timeouts=None, messages=None, lines=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.type = NodeType.MANUAL
        self.settings = dict() if settings is None else settings
        self.form_settings = dict() if form_settings is None else form_settings
        self.actions = list() if actions is None else actions
        self.timeouts = list() if timeouts is None else timeouts
        self.messages = list() if messages is None else messages
        self.lines = list() if lines is None else lines
        super().__init__(*args, **kwargs)


class AutoNode(Json):

    def __init__(self, name, settings=None, messages=None, lines=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.type = NodeType.AUTO
        self.settings = dict() if settings is None else settings
        self.messages = list() if messages is None else messages
        self.lines = list() if lines is None else lines
        super().__init__(*args, **kwargs)


class WorkflowSettings(Json):

    def __init__(
        self, pc_from=None, mobile_from=None, pad_from=None, pc_to=None, mobile_to=None, pad_to=None, 
        show_detail=True, variables=None, *args, **kwargs):
        self.pc_from = dict() if pc_from is None else pc_from
        self.mobile_from = dict() if mobile_from is None else mobile_from
        self.pad_from = dict() if pad_from is None else pad_from
        self.pc_to = dict() if pc_to is None else pc_to
        self.mobile_to = dict() if mobile_to is None else mobile_to
        self.pad_to = dict() if pad_to is None else pad_to
        self.show_detail = show_detail
        self.variables = list() if variables is None else variables
        super().__init__(*args, **kwargs)


class WorkflowVersion(Json):

    def __init__(
        self, timestamp, user_uuid, status=0, version_type=0, online=True, 
        version=1, settings=None, nodes=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.timestamp = timestamp
        self.user_uuid = user_uuid
        self.status = status
        self.type = version_type
        self.online = online,
        self.version = version
        self.settings = dict() if settings is None else settings
        self.nodes = list() if nodes is None else nodes
        super().__init__(*args, **kwargs)


class Workflow(Json):

    def __init__(
        self, name, description, online_version=None, versions=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.description = description
        self.online_version = online_version
        self.versions = list() if versions is None else versions
        super().__init__(*args, **kwargs)
