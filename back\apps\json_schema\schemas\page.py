from apps.json_schema.const import ScopeType, UniqueName, SchemaKeys as SK
from apps.entity import Page as PageTable
from apps.json_schema.schemas.utils import gen_const_condition_schema
from apps.ide_const import (
    LemonDesignerErrorCode as <PERSON>DE<PERSON>, PageAttr, FormAttr, ComponentType)


event_action = {
    "type": "object",
    "properties": {
        
    }
}

visible_field = {
    "type": "object",
    "properties": {
        "is_visible": {
            "$ref": "mem://common/value_editor_all"
        }
    }
}

grid = {
    SK.is_element: True,
    SK.type: "object",
    SK.properties: {
        "visible": {
            "$ref": "mem://page/visible_field"
        },
        "rows": {
            SK.type: "array",
            SK.items: {
                "$ref": "mem://page/grid_row"
            }
        }
    }
}

grid_row = {
    SK.is_element: True,
    "type": "object",
    "properties": {
        "visible": {
            "$ref": "mem://page/visible_field"
        },
        "cols": {
            SK.type: "array",
            SK.items: {
                "$ref": "mem://page/grid_col"
            }
        }
    }
}

grid_col = {
    "type": "object",
    "properties": {
        "visible": {
            "$ref": "mem://page/visible_field"
        }
    },
    "is_element": True,
}

button = {
    "is_element": True,
    "attr_name": "按钮",
    "type": "object",
    "properties": {
        "click": {
            "type": "array",
            "item": {
                "$ref": "mem://common/event_aciton"
            }
        },
        "visible": {
            "$ref": "mem://page/visible_field"
        },
        "disabled": {
            "$ref": "mem://common/value_editor_all"
        },
        "loading": {
            "$ref": "mem://common/value_editor_all"
        },
        "title": {
            "$ref": "mem://common/value_editor_all"
        }
    }
}

form = {
    "is_element": True,
    "attr_name": "表单",
    "type": "object",
    "properties": {
        "uuid": {
            "$ref": "mem://common/lemon_uuid",
        },
        "editable": {
            "$ref": "mem://common/value_editor_all"
        },
        "visible": {
            "$ref": "mem://page/visible_field",
        },
        "data_source": {
            SK.attr_name: FormAttr.DATASOURCE,
            "$ref": "mem://common/data_source_form"
        },
        "buttons": {
            "type": "array",
            "items": {
                "$ref": "mem://page/button"
            }
        },
        "children": {
            "type": "array",
            "items": {

            }
        }
    }
}

data_list = {}

card_list = {}

data_grid = {}



input_ = {}

textarea = {}

radio = {}

checkbox = {}

select = {}

r_select = {}

custom_component = {}


page_schema = {
    "is_element": True,
    "type": "object",
    "properties": {
        "uuid": {
            SK.attr_name: PageAttr.UUID,
            "$ref": "mem://common/lemon_uuid",
        },
        "name": {
            SK.attr_name: PageAttr.NAME,
            "type": "string"
        },
        "page_title": {
            SK.attr_name: PageAttr.TITLE,
            "$ref": "mem://common/value_editor_exclude_field"
        },
        "refresh": {
            "attr_name": PageAttr.REFRESH,
            "type": "object",
            "properties": {
                "is_auto": {
                    "type": "boolean"
                },
                "gap": {
                    "type": "integer"
                }
            },
            "dependentRequired": {
                "is_auto": ["gap"]
            }
        },
        "custom_path": {
            "attr_name": PageAttr.CUSTOM_PATH,
            "type": "object",
            "errorMessage": {
                "properties": {
                    "path": {
                        "pattern": "$LDEC.PAGE_URL_INVALID",
                    }
                },
                "dependentRequired": {
                    "show": LDEC.PAGE_URL_IS_NULL,
                }
            },
            "properties": {
                "show": {
                    "type": "boolean"
                },
                "path": {
                    "type": "string",
                    "pattern":
                    "^[a-zA-Z0-9!@#$%^&*()_+{}\[\]:;<>,.?\/\-=|\s]+$",
                    "unique": (ScopeType.APP, UniqueName.PAGE_URL)
                }
            },
            "dependentRequired": {
                "show": ["path"]
            },
        },
        "children": {
            "type": "array",
            "items": {
                "allOf": [
                    gen_const_condition_schema(ComponentType.FORM, ref="mem://page/form"),
                    gen_const_condition_schema(ComponentType.GRID, ref="mem://page/grid"),
                ]
            }
        }
    }
    # "entityRecord": {
    #     "entity": PageTable,
    #     "fields": {
    #         "uuid": PageTable.page_uuid.name,
    #         "name": PageTable.page_name.name,
    #         "open_type": PageTable.open_type.name,
    #         "$datalist_count": PageTable.datalist_count.name,
    #         "$cardlist_count": PageTable.cardlist_count.name,
    #         "$datagrid_count": PageTable.datagrid_count.name,
    #         "$form_count": PageTable.form_count.name,
    #         "$container_model": str(PageTable.container_model.name),
    #     },
    #     "unique": ["uuid"]
    # }
}
