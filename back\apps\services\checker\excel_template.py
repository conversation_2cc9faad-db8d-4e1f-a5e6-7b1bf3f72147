# -*- coding:utf-8 -*-

import os
import base64
import asyncio
from hashlib import md5

from openpyxl.utils import column_index_from_string

from baseutils.log import app_log
from apps.exceptions import CheckNameError, Check<PERSON><PERSON><PERSON>rror, CheckUUIDUniqueError
from apps.entity import TemplateTable
from apps.ide_const import FieldType, Template, Icon, TemplateAttr
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker
from apps.base_utils import check_sys_field


class TemplateCheckerService(CheckerService):

    attr_class = Template.ATTR
    uuid_unique_error = LDEC.EXCEL_TEMPLATE_UUID_UNIQUE_ERROR
    name_error = LDEC.EXCEL_TEMPLATE_NAME_FAILED
    name_unique_error = LDEC.EXCEL_TEMPLATE_NAME_NOT_UNIQUE

    allow_chinese_name = True

    def initialize(self):
        super().initialize()
        sheet_name = self.element.get("sheet_name")
        title_row = self.element.get("table_header_row_number")
        state = self.element.get("state", False)
        template_type = self.element.get("type", 0)
        columns = self.element.get("sub_table", [])
        model_uuid = self.element.get("model", "")
        self.model_uuid = model_uuid
        self.columns = columns
        # 更新引用详情需要用到
        self.type = 999
        template_info = {"sheet_name": sheet_name,
                         "title_row": title_row, "columns": columns, "model": model_uuid}
        self.template = template_info
        self.template_item_name_uuid_set = set()
        self.state = state
        self.template_type = template_type
        self.app_model_dict = self.kwargs.get("app_model_dict")
        self.app_field_dict = self.kwargs.get("app_field_dict")
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
        self.unqiue_index_dict = {}
        self.import_method_dict = {}
        self.column_index_set = set()

    def build_insert_query_data(self):
        return {
            TemplateTable.app_uuid.name: self.app_uuid,
            TemplateTable.module_uuid.name: self.module_uuid,
            TemplateTable.document_uuid.name: self.document_uuid,
            TemplateTable.template_uuid.name: self.element_uuid,
            TemplateTable.template_name.name: self.element_name,
            TemplateTable.template.name: self.template,
            TemplateTable.state.name: self.state,
            TemplateTable.template_type.name: self.template_type
        }

    def build_update_query_data(self):
        return {
            TemplateTable.template_name.name: self.element_name,
            TemplateTable.template.name: self.template,
            TemplateTable.state.name: self.state,
            TemplateTable.is_delete.name: False,
            TemplateTable.template_type.name: self.template_type
        }

    def build_update_query(self):
        query_data = self.build_update_query_data()
        return TemplateTable.update(**query_data).where(
            TemplateTable.template_uuid == self.element_uuid)

    @staticmethod
    def build_delete_query(template_uuid):
        return TemplateTable.update(**{
            TemplateTable.is_delete.name: True
        }).where(TemplateTable.template_uuid == template_uuid)

    def check_modify(self, document_template_uuid_set):
        if self.element_uuid in document_template_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

    def check_column(self, column_info):
        column_index = column_info.get("column_index")
        index_list = column_index.split(",")
        attr = self.attr_class.COLUMN
        index_error = LDEC.EXCEL_COLUMN_INDEX_ERROR
        index_repeat = LDEC.EXCEL_COLUMN_INDEX_REPEAT
        for index in index_list:
            try:
                column_index_from_string(index)
                if index in self.column_index_set:
                    self._add_error_list(attr, index_repeat)
                else:
                    self.column_index_set.add(index)
            except:
                self._add_error_list(attr, index_error)
        field_info = column_info.get("field_info", {})
        if field_info:
            field_uuid = field_info.get("uuid")
            if check_sys_field(field_uuid):
                return True
            field_detail = self.app_field_dict.get(field_uuid)
            if field_detail:
                model = field_detail.get("model_uuid")
                field_type = field_detail.get("field_type")
                if field_type == FieldType.FILE:
                    code = LDEC.EXCEL_FILE_FIELD_ERROR
                    self._add_error_list(attr, code)
                if model != self.model_uuid:
                    path = field_info.get("path")
                    if not path:
                        code = LDEC.EXCEL_RELATIONSHIP_NOT_EXIST
                        self._add_error_list(attr, code)
                    else:
                        rel_path = path[0]
                        # if column_info.get("unique_key"):
                        #     if self.unqiue_index_dict.get(rel_path):
                        #         code = LDEC.EXCEL_UNIQUE_COLUMN_ERROR
                        #         self._add_error_list(attr, code)
                        #     else:
                        #         self.unqiue_index_dict[rel_path] = field_info
                        import_method = column_info.get("relation_data_method")
                        if rel_path in self.import_method_dict:
                            if self.import_method_dict.get(rel_path) != import_method:
                                code = LDEC.EXCEL_IMPORT_METHOD_REPEAT
                                self._add_error_list(attr, code)
                        else:
                            self.import_method_dict[rel_path] = import_method  
                        self.update_reference_by_field_info(
                            field_info, self.element, attr)
                # else:
                #     if column_info.get("unique_key"):
                #         if self.unqiue_index_dict.get(model):
                #             code = LDEC.EXCEL_UNIQUE_COLUMN_ERROR
                #             self._add_error_list(attr, code)
                #         else:
                #             self.unqiue_index_dict[model] = field_info
            else:
                code = LDEC.EXCEL_FIELD_NOT_EXIST
                self._add_error_list(attr, code)

    @checker.run
    def check_columns(self):
        for column in self.columns:
            if column.get("relation_type") == 3:
                self.check_relation_model(column)
            else:
                self.check_column(column)

    def check_relation_model(self, mult_model_info):
        model_list = mult_model_info.get("model_list")
        for model_info in model_list:
            column_list = model_info.get("field_list", [])
            for column_info in column_list:
                self.check_relation_column(column_info)

    def check_relation_column(self, column_info):
        column_index = column_info.get("column_index")
        index_list = column_index.split(",")
        attr = self.attr_class.COLUMN
        index_error = LDEC.EXCEL_COLUMN_INDEX_ERROR
        index_repeat = LDEC.EXCEL_COLUMN_INDEX_REPEAT
        for index in index_list:
            try:
                column_index_from_string(index)
                if index in self.column_index_set:
                    self._add_error_list(attr, index_repeat)
                else:
                    self.column_index_set.add(index)
            except:
                self._add_error_list(attr, index_error)
        field_info = column_info.get("field_info", {})
        if field_info:
            field_uuid = field_info.get("uuid")
            if check_sys_field(field_uuid):
                return True
            field_detail = self.app_field_dict.get(field_uuid)
            if field_detail:
                field_type = field_detail.get("field_type")
                if field_type == FieldType.FILE:
                    code = LDEC.EXCEL_FILE_FIELD_ERROR
                    self._add_error_list(attr, code)

                self.update_reference_by_field_info(
                    field_info, self.element, attr)

            else:
                code = LDEC.EXCEL_FIELD_NOT_EXIST
                self._add_error_list(attr, code)


class ExcelTemplateDocumentCheckerService(DocumentCheckerService):

    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str,
            document_uuid: str, document_name: str,
            element: dict, document_version: int,
            app_template_list: list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, TemplateTable, *args, **kwargs)
        self.template_list = self.element.get("data", list())
        self.app_template_uuid_set = set()
        self.document_template_name_set = set()
        self.document_template_uuid_set = set()
        self.document_template_update_list = list()
        self.document_template_delete_list = list()
        self.app_model_dict = kwargs.get("app_model_dict")
        self.app_field_dict = kwargs.get("app_field_dict")
        self.type = 999
        for template in app_template_list:
            template_uuid = template.get("template_uuid", "")
            template_document_uuid = template.get("document_uuid", "")

            # 找到原文档中所有的 模板 ，为了新增、更新、删除文档的 模板
            if template_document_uuid == self.document_uuid:
                self.document_template_uuid_set.add(template_uuid)
            else:
                self.app_template_uuid_set.add(template_uuid)

    @checker.run
    def check_template_list(self):
        this_document_template_name_set = set()
        this_document_template_uuid_set = set()
        model_uuid = self.element.get("data_source", {}).get("model", "")
        for template in self.template_list:
            template["model"] = model_uuid
            template_checker_service = TemplateCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, template, is_copy=self.is_copy,
                app_model_dict=self.app_model_dict, app_field_dict=self.app_field_dict)
            template_uuid = template_checker_service.element_uuid
            this_document_template_uuid_set.add(template_uuid)
            try:
                template_checker_service.check_uuid()
                template_checker_service.check_uuid_unique(
                    self.app_template_uuid_set)
                template_checker_service.check_name()
                template_checker_service.check_name_unique(
                    this_document_template_name_set)
                template_checker_service.check_all()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(template_checker_service)
                raise e
            else:
                self.update_any_list(template_checker_service)

            # 找到新增 或 更新的 模板
            template_checker_service.check_modify(
                self.document_template_uuid_set)
            if template_checker_service.insert_query_list:
                self.document_insert_list.extend(
                    template_checker_service.insert_query_list)
            if template_checker_service.update_query_list:
                self.document_update_list.extend(
                    template_checker_service.update_query_list)

        # 找出删除的 模板 ，将其 is_delete 置为 True
        delete_template_uuid_set = self.document_template_uuid_set - \
            this_document_template_uuid_set
        for this_template_uuid in delete_template_uuid_set:
            query = TemplateCheckerService.build_delete_query(
                this_template_uuid)
            self.document_delete_list.append(query)

    @checker.run
    def check_data_source(self):
        data_souce = self.element.get("data_source", {})
        model_uuid = data_souce.get("model", "")
        code = LDEC.MODEL_NOT_EXISTS
        model_detail = self.app_model_dict.get(model_uuid)
        attr = TemplateAttr.DATASOURCE
        if not model_detail:
            code = LDEC.MODEL_NOT_EXISTS
            self._add_error_list(attr, code)
        else:
            self.update_reference_by_data_source(
                data_souce, self.element, attr)
