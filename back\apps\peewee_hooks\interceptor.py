from typing import Any
from sanic import Sanic
from peewee import BaseQuery, Expression, ColumnBase, Field
from apps.engine import Engine
from apps.entity import DocumentContent, Document
from apps.peewee_hooks.utils import (get_doc_info_from_query, without_interceptor, use_interceptor)
import json
from playhouse.shortcuts import dict_to_model, model_to_dict
from baseutils.hack import LemonAsyncQueryWrapper
from peewee_async import RowsCursor
from apps.base_config import Config


def get_column_value_from_expression(expr: Expression, column: ColumnBase):
    lhs = getattr(expr, "lhs", None)
    rhs = getattr(expr, 'rhs', None)
    if lhs:
        if isinstance(lhs, column.__class__):
            # todo: rhs is expression
            return rhs
        elif isinstance(lhs, Expression):
            return get_column_value_from_expression(lhs, column)
    if rhs and isinstance(rhs, Expression):
        return get_column_value_from_expression(rhs, column)
    return None


async def get_content(document_uuid: str, engine: Engine):
    cache_key = f'{Config.DOCUMENT_CONTENT_CACHE_PREFIX}:{document_uuid}'
    doc_content = await engine.access.get_document_content_by_document_uuid(document_uuid)
    if doc_content:
        await engine.redis.setex(cache_key, Config.DOCUMENT_CONTENT_CACHE_EXPIRY, doc_content.to_dict())
    return doc_content


async def get_content_by_cache(document_uuid: str, engine: Engine):
    cache_key = f'{Config.DOCUMENT_CONTENT_CACHE_PREFIX}:{document_uuid}'
    data = await engine.redis.get(cache_key)
    if not data:
        async with without_interceptor():
            return await get_content(document_uuid, engine)
    doc_content = dict_to_model(DocumentContent, json.loads(data))
    return doc_content


class SelectInterceptor:

    def __init__(self, app: Sanic, engine: Engine, sender: DocumentContent):
        self.app = app
        self.engine = engine
        self.sender = sender

    @property
    def use_interceptor(self):
        return use_interceptor.get()

    def validate(self, query):
        where = getattr(query, "_where", None)
        joins = getattr(query, "_joins", {})
        if not where or len(joins):
            return False
        return True

    async def __call__(self, query: BaseQuery) -> Any:
        where = getattr(query, "_where", None)
        document_uuid = get_column_value_from_expression(where, self.sender.document_uuid)

        content = None
        if document_uuid:
            if use_interceptor.get():
                task = get_content_by_cache(document_uuid, self.engine)
            else:
                task = get_content(document_uuid, self.engine)
            content = await task

        return [content]
