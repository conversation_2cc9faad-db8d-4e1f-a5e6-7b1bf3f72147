import time
from functools import reduce

from apps.base_utils import lemon_uuid
from baseutils.log import app_log
from apps.ide_const import ApprovalFlow, HandleType, HandlerType, LemonDesignerErrorCode as LDEC, Node, NodeMessage, NodeLine, NodeSystemAction, NodeSystemActionRule
from apps.services.checker import CheckerService, DocumentCheckerService
from apps.entity import Workflow as WorkflowModel, Page as PageModel
from apps.services.checker import checker
from apps.services.checker.workflow import find_form_model
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.utils import check_placeholder_exist


class ApprovalFlowCheckerService(CheckerService):

    attr_class = ApprovalFlow.ATTR
    uuid_error = LDEC.AF_UUID_ERROR
    uuid_unique_error = LDEC.AF_UUID_UNIQUE_ERROR
    name_error = LDEC.AF_NAME_FAILED
    name_unique_error = LDEC.AF_NAME_NOT_UNIQUE

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, element: dict,  
        app_wf_uuid_set: set, app_model_uuid_set: set, 
        app_field_uuid_set: set, app_func_uuid_dict: dict, 
        app_page_uuid_set: set, app_const_uuid_set: set, 
        app_enum_uuid_set: set, app_user_role_uuid_set: set, 
        app_page_dict: dict, modules_role, app_relationship_uuid_set, 
        *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.app_wf_uuid_set = app_wf_uuid_set
        self.app_model_uuid_set = app_model_uuid_set
        self.app_field_uuid_set = app_field_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_uuid_set = app_page_uuid_set
        self.app_const_uuid_set = app_const_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.app_page_dict = app_page_dict
        self.modules_role = modules_role
        self.app_relationship_uuid_set = app_relationship_uuid_set
        self.description = self.element.get("description", "")
        self.online_version = self.element.get("online_version", "")
        self.show_version = self.element.get("show_version", "")
        self.versions = self.element.get("versions", list())
        self.type = self.element.get("type", 0)
        self.online_version_uuid_set = set()
        self.version_uuid_set = set()
        self.version_error = False
        self.version_unique = True
        self.workflow = dict()
        self.all_version_variables = dict()
        self.pc_from = ""
        self.mobile_from = ""
        self.pad_from = ""
        self.allow_chinese_name = True
        if self.is_copy:
            new_versions = list()
            for wf in self.versions:
                version_uuid = wf.get("uuid")
                if version_uuid == self.show_version:
                    temp_uuid = lemon_uuid()
                    self.element.update({"online_version": temp_uuid})
                    self.element.update({"show_version": temp_uuid})
                    self.online_version = temp_uuid
                    self.show_version = temp_uuid
                    wf.update({
                        "uuid": temp_uuid, 
                        "status": 0,  # 草稿
                        "version": 1
                    })
                    new_versions.append(wf)
                    self.versions = new_versions
                    self.element["versions"] = self.versions
                    break
        
        for wf in self.versions:
            online = wf.get("online", False)
            version_uuid = wf.get("uuid")
            settings = wf.get("settings", dict())
            variables = settings.get("variables", list())
            self.all_version_variables.update({version_uuid: variables})
            if online is True:
                self.online_version_uuid_set.add(version_uuid)
            if version_uuid == self.online_version:
                self.workflow = wf
                for device in ["pc", "mobile", "pad"]:
                    from_dic = settings.get(device+"_from", {})
                    from_page = from_dic.get("page", "")
                    setattr(self, device+"_from", from_page)
            if version_uuid in self.version_uuid_set:
                self.version_unique = False
            else:
                self.version_uuid_set.add(version_uuid)
        if self.online_version not in self.version_uuid_set:
            self.version_error = True
        self.settings = self.workflow.get("settings", dict())
        self.variables = self.settings.get("variables", list())
        self.nodes = self.workflow.get("nodes", list())
        self.node_uuid_set = set()
        self.node_name_set = set()
        self.variable_uuid_set = set()
        self.variable_name_set = set()
        
        # 拿到当前工作流所有 节点 的UUID
        self.wf_node_uuid_set = set()
        self.wf_node_name_set = set()
        self.node_list = list()
        self.node_dict_list = list()
        for node in self.nodes:
            if isinstance(node, dict):
                node_uuid = node.get("uuid")
                node_name = node.get("name")
                node_type = node.get("type")
                self.wf_node_uuid_set.add(node_uuid)
                self.wf_node_name_set.add(node_name)
                self.node_list.append(node)
                self.node_dict_list.append(
                    {"uuid": node_uuid, "name": node_name, "type": node_type})
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            WorkflowModel.app_uuid.name: self.app_uuid,
            WorkflowModel.module_uuid.name: self.module_uuid,
            WorkflowModel.document_uuid.name: self.document_uuid,
            WorkflowModel.wf_uuid.name: self.element_uuid,
            WorkflowModel.wf_name.name: self.element_name,
            WorkflowModel.description.name: self.description,
            WorkflowModel.online_version.name: self.online_version, 
            WorkflowModel.variables.name: self.all_version_variables,
            WorkflowModel.nodes.name: self.node_dict_list,
            WorkflowModel.modify_time.name: int(time.time()),
            WorkflowModel.pc_from.name: self.pc_from,
            WorkflowModel.mobile_from.name: self.mobile_from,
            WorkflowModel.pad_from.name: self.pad_from,
            WorkflowModel.type.name: self.type
        }
    
    def build_update_query_data(self):
        return {
            WorkflowModel.wf_name.name: self.element_name,
            WorkflowModel.description.name: self.description,
            WorkflowModel.online_version.name: self.online_version, 
            WorkflowModel.variables.name: self.all_version_variables,
            WorkflowModel.nodes.name: self.node_dict_list,
            WorkflowModel.modify_time.name: int(time.time()),
            WorkflowModel.is_delete.name: False, 
            WorkflowModel.pc_from.name: self.pc_from,
            WorkflowModel.mobile_from.name: self.mobile_from,
            WorkflowModel.pad_from.name: self.pad_from,
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return WorkflowModel.update(**query_data).where(
            WorkflowModel.wf_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(sm_uuid):
        return WorkflowModel.update(**{
                WorkflowModel.is_delete.name: True
            }).where(WorkflowModel.wf_uuid==sm_uuid)
    
    def check_modify(self, document_wf_uuid_set):
        if self.element_uuid in document_wf_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
    def check_settings(self):
        pc_from_page = self.settings.get("pc_from", {}).get("page")
        mobile_from_page = self.settings.get("mobile_from", {}).get("page")
        pad_from_page = self.settings.get("pad_from", {}).get("page")
        pc_to_page = self.settings.get("pc_to", {}).get("page")
        mobile_to_page = self.settings.get("mobile_to", {}).get("page")
        pad_to_page = self.settings.get("pad_to", {}).get("page")
        element_uuid = self.element_uuid
        title = self.element.get("name")
        # if pc_from_page not in self.app_page_uuid_set:
        #     attr = ApprovalFlow.ATTR.PC_FROM_PAGE
        #     return_code = LDEC.WF_FROM_PAGE_NOT_FOUND
        #     self._add_error_list(attr=attr, return_code=return_code)
        # else:
        #     attr = ApprovalFlow.ATTR.PC_FROM_PAGE
        #     self.update_page_reference(pc_from_page, title, element_uuid, attr )
        # if mobile_from_page not in self.app_page_uuid_set:
        #     attr = ApprovalFlow.ATTR.MOBILE_FROM_PAGE
        #     return_code = LDEC.WF_FROM_PAGE_NOT_FOUND
        #     self._add_error_list(attr=attr, return_code=return_code)
        # else:
        #     attr = ApprovalFlow.ATTR.MOBILE_FROM_PAGE
        #     self.update_page_reference(mobile_from_page, title, element_uuid, attr )
        # if pad_from_page not in self.app_page_uuid_set:
        #     attr = ApprovalFlow.ATTR.PAD_FROM_PAGE
        #     return_code = LDEC.WF_FROM_PAGE_NOT_FOUND
        #     self._add_error_list(attr=attr, return_code=return_code)
        # else:
        #     attr = ApprovalFlow.ATTR.PAD_FROM_PAGE
        #     self.update_page_reference(pad_from_page, title, element_uuid, attr )
        to_page_set = pc_to_page or mobile_to_page or pad_to_page
        if pc_to_page not in self.app_page_uuid_set and not to_page_set:
            attr = ApprovalFlow.ATTR.PC_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NOT_FOUND
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            attr = ApprovalFlow.ATTR.PC_TO_PAGE
            self.update_page_reference(pc_to_page, title, element_uuid, attr )
        if mobile_to_page not in self.app_page_uuid_set and not to_page_set:
            attr = ApprovalFlow.ATTR.MOBILE_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NOT_FOUND
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            attr = ApprovalFlow.ATTR.MOBILE_TO_PAGE
            self.update_page_reference(mobile_to_page, title, element_uuid, attr )
        if pad_to_page not in self.app_page_uuid_set and not to_page_set:
            attr = ApprovalFlow.ATTR.PAD_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NOT_FOUND
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            attr = ApprovalFlow.ATTR.PAD_TO_PAGE
            self.update_page_reference(pad_to_page, title, element_uuid, attr )
        pc_from_page_dict = self.app_page_dict.get(pc_from_page, dict())
        mobile_from_page_dict = self.app_page_dict.get(mobile_from_page, dict())
        pad_from_page_dict = self.app_page_dict.get(pad_from_page, dict())
        pc_to_page_dict = self.app_page_dict.get(pc_to_page, dict())
        mobile_to_page_dict = self.app_page_dict.get(mobile_to_page, dict())
        pad_to_page_dict = self.app_page_dict.get(pad_to_page, dict())

        pc_from_page_form_count = pc_from_page_dict.get(PageModel.form_count.name, 0)
        mobile_from_page_form_count = mobile_from_page_dict.get(PageModel.form_count.name, 0)
        pad_from_page_form_count = pad_from_page_dict.get(PageModel.form_count.name, 0)
        pc_to_page_form_count = pc_to_page_dict.get(PageModel.form_count.name, 0)
        mobile_to_page_form_count = mobile_to_page_dict.get(PageModel.form_count.name, 0)
        pad_to_page_form_count = pad_to_page_dict.get(PageModel.form_count.name, 0)

        # if pc_from_page_form_count != 1:
        #     attr = ApprovalFlow.ATTR.PC_TO_PAGE
        #     return_code = LDEC.WF_FROM_PAGE_NEED_ONE_FORM
        #     self._add_error_list(attr=attr, return_code=return_code)
        # if mobile_from_page_form_count != 1:
        #     attr = ApprovalFlow.ATTR.MOBILE_TO_PAGE
        #     return_code = LDEC.WF_FROM_PAGE_NEED_ONE_FORM
        #     self._add_error_list(attr=attr, return_code=return_code)
        # if pad_from_page_form_count != 1:
        #     attr = ApprovalFlow.ATTR.PAD_TO_PAGE
        #     return_code = LDEC.WF_FROM_PAGE_NEED_ONE_FORM
        #     self._add_error_list(attr=attr, return_code=return_code)

        if pc_to_page_form_count != 1 and pc_to_page_dict:
            attr = ApprovalFlow.ATTR.PC_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NEED_ONE_FORM
            self._add_error_list(attr=attr, return_code=return_code)
        if mobile_to_page_form_count != 1 and mobile_to_page_dict:
            attr = ApprovalFlow.ATTR.MOBILE_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NEED_ONE_FORM
            self._add_error_list(attr=attr, return_code=return_code)
        if pad_to_page_form_count != 1 and pad_to_page_dict:
            attr = ApprovalFlow.ATTR.PAD_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NEED_ONE_FORM
            self._add_error_list(attr=attr, return_code=return_code)

        pc_from_page_container_model = pc_from_page_dict.get(PageModel.container_model.name, dict())
        mobile_from_page_container_model = mobile_from_page_dict.get(PageModel.container_model.name, dict())
        pad_from_page_container_model = pad_from_page_dict.get(PageModel.container_model.name, dict())
        pc_to_page_container_model = pc_to_page_dict.get(PageModel.container_model.name, dict())
        mobile_to_page_container_model = mobile_to_page_dict.get(PageModel.container_model.name, dict())
        pad_to_page_container_model = pad_to_page_dict.get(PageModel.container_model.name, dict())
        pc_from_page_form_models = find_form_model(pc_from_page_container_model)
        mobile_from_page_form_models = find_form_model(mobile_from_page_container_model)
        pad_from_page_form_models = find_form_model(pad_from_page_container_model)
        pc_to_page_form_models = find_form_model(pc_to_page_container_model)
        mobile_to_page_form_models = find_form_model(mobile_to_page_container_model)
        pad_to_page_form_models = find_form_model(pad_to_page_container_model)

        # if not (pc_from_page_form_models == mobile_from_page_form_models == pad_from_page_form_models):
        #     attr = ApprovalFlow.ATTR.PC_FROM_PAGE
        #     return_code = LDEC.WF_FROM_PAGE_FORM_MODEL_DIFF
        #     self._add_error_list(attr=attr, return_code=return_code)

        all_device_models = [pc_to_page_form_models, mobile_to_page_form_models, pad_to_page_form_models]
        device_models = [models for models in all_device_models if models]
        if device_models:
            device_models_the_same = reduce(lambda x, y: x if x==y else False, device_models)  # TODO 别这样写
            if not device_models_the_same:
                attr = ApprovalFlow.ATTR.PC_TO_PAGE
                return_code = LDEC.WF_TO_PAGE_FORM_MODEL_DIFF
                self._add_error_list(attr=attr, return_code=return_code)
    
    def check_workflow_version(self):
        if self.version_error:
            attr = ApprovalFlow.ATTR.VERSION
            return_code = LDEC.WF_ONLINE_VERSION_ERROR
            self._add_error_list(attr=attr, return_code=return_code)
        if not self.version_unique:
            attr = ApprovalFlow.ATTR.VERSION
            return_code = LDEC.WF_VERSION_NOT_UNIQUE
            self._add_error_list(attr=attr, return_code=return_code)
        if len(self.online_version_uuid_set) > 1:
            attr = ApprovalFlow.ATTR.VERSION
            return_code = LDEC.WF_ONLINE_VERSION_THAN_ONE
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 变量列表
    # def check_variables(self):
    #     for variable in self.variables:
    #         variable_checker_service = VariableCheckerService(
    #             self.app_uuid, self.module_uuid, self.module_name, 
    #             self.document_uuid, self.document_name, variable)
    #         try:
    #             variable_checker_service.check_uuid()
    #             variable_checker_service.check_uuid_unique(self.variable_uuid_set)
    #             variable_checker_service.check_name()
    #             variable_checker_service.check_name_unique(self.variable_name_set)
    #         except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
    #             self.update_any_list(variable_checker_service)
    #             raise e
    #         else:
    #             self.update_any_list(variable_checker_service)
    
    # 检查 各个节点
    def check_nodes(self):
        start_node_count = 0
        end_node_count = 0
        flag = True if self.is_copy else False
        self.is_copy = not self.is_copy if flag else self.is_copy
        for node in self.nodes:
            node_checker_service = NodeCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, node, self.element_uuid, self.app_wf_uuid_set, 
                self.app_func_uuid_dict, self.app_page_uuid_set, self.app_user_role_uuid_set,
                self.app_page_dict, self.modules_role, self.app_relationship_uuid_set, 
                self.app_field_uuid_set)
            node_checker_service.document_other_info = self.document_other_info
            node_type = node_checker_service.type
            if node_type == Node.TYPE.START:
                start_node_count += 1
            elif node_type == Node.TYPE.END:
                end_node_count += 1
            try:
                node_checker_service.check_uuid()
                node_checker_service.check_uuid_unique(self.node_uuid_set)
                node_checker_service.check_name()
                node_checker_service.check_name_unique(self.node_name_set)
                node_checker_service.check_node_type()
                node_checker_service.check_node(self.wf_node_uuid_set, self.variable_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(node_checker_service)
                self.is_copy = not self.is_copy if flag else self.is_copy
                raise e
            else:
                self.update_any_list(node_checker_service)
        self.is_copy = not self.is_copy if flag else self.is_copy
        if start_node_count != 1:
            attr = ApprovalFlow.ATTR.START_NODE
            return_code = LDEC.WF_START_NODE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)
        if end_node_count != 1:
            attr = ApprovalFlow.ATTR.END_NODE
            return_code = LDEC.WF_END_NODE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)
            
            
class MessageCheckerService(CheckerService):

    attr_class = NodeMessage.ATTR
    uuid_error = LDEC.WF_MESSAGE_UUID_ERROR
    uuid_unique_error = LDEC.WF_MESSAGE_UUID_UNIQUE_ERROR
    name_error = LDEC.NODE_MESSAGE_NAME_FAILED
    name_unique_error = LDEC.NODE_MESSAGE_NAME_NOT_UNIQUE
    allow_chinese_name = True

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
    
    def check_message_type(self):
        if self.type not in NodeMessage.TYPE.ALL:
            attr = self.attr_class.ATTR.TYPE
            return_code = LDEC.MESSAGE_TYPE_NOT_SUPPORT
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr=attr, return_code=return_code)
            
            
class LineCheckerService(CheckerService):

    attr_class = NodeLine.ATTR
    uuid_error = LDEC.WF_LINE_UUID_ERROR
    uuid_unique_error = LDEC.WF_LINE_UUID_UNIQUE_ERROR
    name_error = LDEC.NODE_LINE_NAME_FAILED
    name_unique_error = LDEC.NODE_LINE_NAME_NOT_UNIQUE
    allow_chinese_name = True 

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", {})
        self.action = self.element.get("action")
        self.default = self.settings.get("default", False)
        self.to_node = self.settings.get("to_node")

    def check_action(self, action_uuid_set):
        if self.action:
            if self.action not in action_uuid_set:
                attr = self.attr_class.ACTION
                return_code = LDEC.NODE_LINE_ACTION_NOT_FOUND
                self._add_error_list(attr=attr, return_code=return_code)
    
    def check_to_node(self, node_uuid_set):
        if self.to_node not in node_uuid_set:
            attr = self.attr_class.TO_NODE
            return_code = LDEC.NODE_LINE_TO_NODE_NOT_FOUND
            self._add_error_list(attr=attr, return_code=return_code)
            
    def check_default_line(self, default_lines: dict):
        if self.source_element.get("settings", {}).get("default"):
            default_lines["default_count"] += 1
        # skip_check用来控制遍历间仅一次进入该报错
        if default_lines["default_count"] > 1 and not default_lines.get("skip_check"):
            default_lines["skip_check"] = True
            attr = self.attr_class.DEFAULT
            return_code = LDEC.NODE_LINE_DEFAULT_ERROR
            self._add_error_list(attr=attr, return_code=return_code)
            
    def check_condition_name_unique(self, name_set):
        if self.settings.get("condition_name") in name_set:
            self._add_error_list(attr=Node.ATTR.LINE, return_code=LDEC.CONDITION_NAME_NOT_UNIQUE)
        else:
            name_set.add(self.settings.get("condition_name"))
            

class BaseNodeCheckerService(CheckerService):

    attr_class = Node.ATTR
    uuid_error = LDEC.NODE_UUID_ERROR
    uuid_unique_error = LDEC.NODE_UUID_UNIQUE_ERROR
    name_error = LDEC.NODE_NAME_FAILED
    name_unique_error = LDEC.NODE_NAME_NOT_UNIQUE_IN_AF
    allow_chinese_name = True
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict, wf_uuid: str, app_wf_uuid_set: set, 
        app_func_uuid_dict: set, app_page_uuid_set: set, 
        app_user_role_uuid_set: set, app_page_dict: dict, 
        modules_role, app_relationship_uuid_set, app_field_uuid_set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.wf_uuid = wf_uuid
        self.app_wf_uuid_set = app_wf_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_uuid_set = app_page_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.app_page_dict = app_page_dict
        self.modules_role = modules_role
        self.app_relationship_uuid_set = app_relationship_uuid_set
        self.app_field_uuid_set = app_field_uuid_set

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.lines = self.element.get("lines", list())
        self.action_uuid_set = set()
        self.message_uuid_set = set()
        self.message_name_set = set()
        self.line_uuid_set = set()
        self.line_name_set = set()
    
    # 检查 节点类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_node_type(self):
        if self.type not in Node.TYPE.ALL:
            attr = Node.ATTR.TYPE
            return_code = LDEC.STATE_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    def check_messages(self):
        messages = self.element.get("messages", list())
        for message in messages:
            message_checker_service = MessageCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, message)
            try:
                message_checker_service.check_uuid()
                message_checker_service.check_uuid_unique(self.message_uuid_set)
                message_checker_service.check_name()
                message_checker_service.check_name_unique(self.message_name_set)
                message_checker_service.check_message_type()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(message_checker_service)
                raise e
            else:
                self.update_any_list(message_checker_service)
    
    def check_lines(self, node_uuid_set, manual_node=False):
        lines = self.element.get("lines", list())
        default_lines = {"default_count": 0}
        condition_name_set = set()
        
        for line in lines:
            line_checker_service = LineCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, line)
            try:
                line_checker_service.check_uuid()
                line_checker_service.check_uuid_unique(self.line_uuid_set)
                # line_checker_service.check_name()
                # line_checker_service.check_name_unique(self.line_name_set)
                line_checker_service.check_condition_name_unique(condition_name_set)

                if manual_node:
                    line_checker_service.check_action(self.action_uuid_set)
                line_checker_service.check_to_node(node_uuid_set)
                line_checker_service.check_default_line(default_lines)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(line_checker_service)
                raise e
            else:
                self.update_any_list(line_checker_service)
    
    def check_node(self, node_uuid_set, variable_uuid_set):
        pass


class AutoNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", dict())
    
    def check_settings(self, variable_uuid_set):
        func_dict = self.settings.get("func", dict())
        func_uuid = func_dict.get("uuid")
        arg_list = self.settings.get("arg_list", list())
        return_list = self.settings.get("return_list", list())
        if func_uuid in self.app_func_uuid_dict:
            func = self.app_func_uuid_dict.get(func_uuid, dict())
            func_arg_list = func.get("arg_list", list())
            func_return_list = func.get("return_list", list())
            if len(arg_list) > len(func_arg_list):
                attr = Node.ATTR.FUNC
                return_code = LDEC.AUTO_NODE_FUNC_ARG_LARGE
            if len(return_list) > len(func_return_list):
                attr = Node.ATTR.FUNC
                return_code = LDEC.AUTO_NODE_FUNC_RETURN_LARGE
            for return_dict in return_list:
                bind_to = return_dict.get("bind_to")
                if bind_to not in variable_uuid_set:
                    attr = Node.ATTR.FUNC
                    return_code = LDEC.AUTO_NODE_FUNC_RESULT_VARIABLE_ERROR
                    self._add_error_list(attr=attr, return_code=return_code)
        else:
            attr = Node.ATTR.FUNC
            return_code = LDEC.AUTO_NODE_FUNC_NOT_FOUND
            self._add_error_list(attr=attr, return_code=return_code)

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.check_settings(variable_uuid_set)
        self.check_messages()
        self.check_lines(node_uuid_set)


class ManualNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", dict())
        self.actions = self.element.get("actions", dict())
        self.timeouts = self.element.get("timeouts", list())
        self.timeout_uuid_set = set()
        self.timeout_name_set = set()
        self.custom_action_uuid_set = set()
        self.custom_action_name_set = set()
    
    def check_settings(self):
        module_role = self.modules_role.get(self.module_uuid, list())
        module_role_uuids = {r["role_uuid"] for r in module_role}
        handlers = self.settings.get("handlers", [])
        if handlers:
            for handler in handlers:
                attr = Node.ATTR.HANDLER
                handler_type = handler.get("type")
                value = handler.get("value", dict())
                if handler_type not in HandlerType.ALL:
                    return_code = LDEC.NODE_HANDLER_TYPE_NOT_SUPPORT
                    self._add_error_list(attr=attr, return_code=return_code)
                if handler_type == HandlerType.ROLE:
                    role_uuid = handler.get("role_uuid")
                    if role_uuid not in module_role_uuids:
                        handler["role_name"] = "**角色不存在**"
                        return_code = LDEC.NODE_HANDLER_ROLE_NOT_FOUND
                        self._add_error_list(attr=attr, return_code=return_code)
                elif handler_type == HandlerType.MANAGER:
                    manager_level = int(handler.get("value", 0))
                    if manager_level < 0:
                        return_code = LDEC.NODE_HANDLER_MANAGER_ERROR
                        self._add_error_list(attr=attr, return_code=return_code)
                elif handler_type == HandlerType.VALUE:
                    if value:
                        exprArr = value.get("exprArr", list())
                        if not exprArr:
                            self._add_error_list(
                                attr, return_code=LDEC.NODE_HANDLER_EXPR_NOT_EXIST,
                                element_data=self.element)
                        else:
                            self._check_expr_value_editor(
                                value, attr, self.app_field_uuid_set,
                                self.app_relationship_uuid_set)
                    else:
                        self._add_error_list(
                            attr, return_code=LDEC.NODE_HANDLER_NOT_EXISTS,
                            element_data=self.element)

                elif handler_type == HandlerType.EXTRA_ASSIGN:
                    limit_roles = handler.get("limit_roles", list())
                    new_limit_roles = list()
                    if handler.get("role_limit"):  # 解决角色被删除的情况
                        if not limit_roles:
                            attr = Node.ATTR.HANDLER
                            return_code = LDEC.NODE_HANDLER_NO_LIMIT_ROLES
                            self._add_error_list(attr=attr, return_code=return_code)
                        # for role in limit_roles:
                        #     role_uuid = role["role_uuid"]
                        #     if role_uuid in self.app_user_role_uuid_set:
                        #         new_limit_roles.append(role)
                        # handler["limit_roles"] = new_limit_roles  # TODO role_name修改的情况
            handle_type = self.settings.get("handle_type")
            if handle_type not in HandleType.ALL:
                attr = Node.ATTR.HANDLER
                return_code = LDEC.NODE_HANDLE_TYPE_NOT_SUPPORT
                self._add_error_list(attr=attr, return_code=return_code)
        else:
            attr = Node.ATTR.HANDLER
            return_code = LDEC.NODE_HANDLER_NOT_EXISTS
            self._add_error_list(attr=attr, return_code=return_code)
        element_uuid = self.element_uuid
        title = self.element.get("name")
        pc_to_page = self.settings.get("pc_to", {}).get("page")
        mobile_to_page = self.settings.get("mobile_to", {}).get("page")
        pad_to_page = self.settings.get("pad_to", {}).get("page")
        # if pc_to_page not in self.app_page_uuid_set:
        #     attr = Node.ATTR.PC_TO_PAGE
        #     return_code = LDEC.NODE_TO_PAGE_NOT_FOUND
        #     self._add_error_list(attr=attr, return_code=return_code)
        # else:
        #     attr = Node.ATTR.PC_TO_PAGE
        #     self.update_page_reference(pc_to_page, title, element_uuid, attr)
        # if mobile_to_page not in self.app_page_uuid_set:
        #     attr = Node.ATTR.MOBILE_TO_PAGE
        #     return_code = LDEC.NODE_TO_PAGE_NOT_FOUND
        #     self._add_error_list(attr=attr, return_code=return_code)
        # else:
        #     attr = Node.ATTR.MOBILE_TO_PAGE
        #     self.update_page_reference(mobile_to_page, title, element_uuid, attr)
        # if pad_to_page not in self.app_page_uuid_set:
        #     attr = Node.ATTR.PAD_TO_PAGE
        #     return_code = LDEC.NODE_TO_PAGE_NOT_FOUND
        #     self._add_error_list(attr=attr, return_code=return_code)
        # else:
        #     attr = Node.ATTR.PAD_TO_PAGE
        #     self.update_page_reference(pad_to_page, title, element_uuid, attr)

        pc_to_page_dict = self.app_page_dict.get(pc_to_page, dict())
        mobile_to_page_dict = self.app_page_dict.get(mobile_to_page, dict())
        pad_to_page_dict = self.app_page_dict.get(pad_to_page, dict())

        pc_to_page_form_count = pc_to_page_dict.get(PageModel.form_count.name, 0)
        mobile_to_page_form_count = mobile_to_page_dict.get(PageModel.form_count.name, 0)
        pad_to_page_form_count = pad_to_page_dict.get(PageModel.form_count.name, 0)

        if pc_to_page_form_count != 1 and pc_to_page_dict:
            attr = ApprovalFlow.ATTR.PC_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NEED_ONE_FORM
            self._add_error_list(attr=attr, return_code=return_code)
        if mobile_to_page_form_count != 1 and mobile_to_page_dict:
            attr = ApprovalFlow.ATTR.MOBILE_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NEED_ONE_FORM
            self._add_error_list(attr=attr, return_code=return_code)
        if pad_to_page_form_count != 1 and pad_to_page_dict:
            attr = ApprovalFlow.ATTR.PAD_TO_PAGE
            return_code = LDEC.WF_TO_PAGE_NEED_ONE_FORM
            self._add_error_list(attr=attr, return_code=return_code)

        pc_to_page_container_model = pc_to_page_dict.get(PageModel.container_model.name, dict())
        mobile_to_page_container_model = mobile_to_page_dict.get(PageModel.container_model.name, dict())
        pad_to_page_container_model = pad_to_page_dict.get(PageModel.container_model.name, dict())
        pc_to_page_form_models = find_form_model(pc_to_page_container_model)
        mobile_to_page_form_models = find_form_model(mobile_to_page_container_model)
        pad_to_page_form_models = find_form_model(pad_to_page_container_model)
        app_log.info(f"pc_to_page_form_models: {pc_to_page_form_models}")
        app_log.info(f"mobile_to_page_form_models: {mobile_to_page_form_models}")
        app_log.info(f"pad_to_page_form_models: {pad_to_page_form_models}")
        
        all_device_models = [pc_to_page_form_models, mobile_to_page_form_models, pad_to_page_form_models]
        device_models = [models for models in all_device_models if models]
        if device_models:
            device_models_the_same = reduce(lambda x, y: x if x==y else False, device_models)  # TODO 别这样写
            if not device_models_the_same:
                attr = ApprovalFlow.ATTR.PC_TO_PAGE
                return_code = LDEC.WF_TO_PAGE_FORM_MODEL_DIFF
                self._add_error_list(attr=attr, return_code=return_code)
    
    def check_actions(self):
        # system_actions = self.actions.get("system", {})
        # custom_actions = self.actions.get("custom", {})
        # if not any([system_actions, custom_actions]):
        #     attr = Node.ATTR.ACTION
        #     return_code = LDEC.NODE_ACTIONS_IS_NULL
        #     self._add_error_list(attr=attr, return_code=return_code)
        # default_rule = self.actions.get("default_rule")
        # if system_actions:
        #     if default_rule not in NodeSystemActionRule.ALL:
        #         attr = Node.ATTR.ACTION
        #         return_code = LDEC.NODE_ACTION_DEFAULT_RULE_NOT_SUPPORT
        #         self._add_error_list(attr=attr, return_code=return_code)
        # system_accept_uuid = system_actions.get("accept_uuid")
        # system_reject_uuid = system_actions.get("reject_uuid")
        # if system_accept_uuid is not None:
        #     self.action_uuid_set.add(system_accept_uuid)
        #     if system_accept_uuid != NodeSystemAction.TYPE.ACCEPT:
        #         attr = Node.ATTR.ACTION
        #         return_code = LDEC.NODE_ACTION_ACCEPT_UUID_ERROR
        #         self._add_error_list(attr=attr, return_code=return_code)
        # if system_reject_uuid is not None:
        #     self.action_uuid_set.add(system_reject_uuid)
        #     if system_reject_uuid != NodeSystemAction.TYPE.REJECT:
        #         attr = Node.ATTR.ACTION
        #         return_code = LDEC.NODE_ACTION_REJECT_UUID_ERROR
        #         self._add_error_list(attr=attr, return_code=return_code)
        # for action in custom_actions:
        #     custom_action_checker_service = CustomActionCheckerService(
        #         self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
        #         self.document_name, action)
        #     action_uuid = custom_action_checker_service.element_uuid
        #     self.action_uuid_set.add(action_uuid)
        #     try:
        #         custom_action_checker_service.check_uuid()
        #         custom_action_checker_service.check_uuid_unique(self.custom_action_uuid_set)
        #         custom_action_checker_service.check_name()
        #         custom_action_checker_service.check_name_unique(self.custom_action_name_set)
        #         custom_action_checker_service.check_transition_rule()
        #     except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
        #         self.update_any_list(custom_action_checker_service)
        #         raise e
        #     else:
        #         self.update_any_list(custom_action_checker_service)
        ...
    
    def check_timeouts(self, node_uuid_set):
        # for timeout in self.timeouts:
        #     timeout_checker_service = TimeoutCheckerService(
        #         self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
        #         self.document_name, timeout)
        #     try:
        #         timeout_checker_service.check_uuid()
        #         timeout_checker_service.check_uuid_unique(self.timeout_uuid_set)
        #         timeout_checker_service.check_name()
        #         timeout_checker_service.check_name_unique(self.timeout_name_set)
        #         timeout_checker_service.check_type()
        #         timeout_checker_service.check_timeout()
        #         timeout_checker_service.check_urging()
        #         timeout_checker_service.check_to_node(node_uuid_set)
        #     except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
        #         self.update_any_list(timeout_checker_service)
        #         raise e
        #     else:
        #         self.update_any_list(timeout_checker_service)
        ...
        
    def check_events(self):
        attr = Node.ATTR.EVENT
        return_code = LDEC.NODE_EVENT_NO_FUNC
        events = self.element.get("events", dict())
        in_events = events.get("in", list())
        out_events = events.get("out", list())
        for f in in_events + out_events:
            func_uuid = f.get("func_uuid")
            if not func_uuid:
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                self.update_cloud_func_reference(
                    func_uuid, title=self.element_name, element_uuid=self.element_uuid, attr=attr)

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.check_settings()
        self.check_messages()
        self.check_actions()
        self.check_timeouts(node_uuid_set)
        self.check_lines(node_uuid_set, manual_node=True)
        self.check_events()


class StartNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
    
    def check_node(self, node_uuid_set, variable_uuid_set):
        # 判断开始节点，有且只有一条线
        if len(self.lines) != 1:
            attr = Node.ATTR.LINE
            return_code = LDEC.START_NODE_LINE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)


class EndNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
    
    def check_node(self, node_uuid_set, variable_uuid_set):
        # 判断结束节点，应没有线
        if len(self.lines) != 0:
            attr = Node.ATTR.LINE
            return_code = LDEC.END_NODE_LINE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)

            

class NodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
        self.node_checker_class_dict = {
            Node.TYPE.AUTO: AutoNodeCheckerService,
            Node.TYPE.MANUAL: ManualNodeCheckerService,
            Node.TYPE.START: StartNodeCheckerService,
            Node.TYPE.END: EndNodeCheckerService
        }
    
    def check_node(self, node_uuid_set, variable_uuid_set):
        node_checker_class = self.node_checker_class_dict.get(self.type, BaseNodeCheckerService)
        node_checker_service = node_checker_class(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, self.element, self.wf_uuid, self.app_wf_uuid_set, 
                self.app_func_uuid_dict, self.app_page_uuid_set, self.app_user_role_uuid_set,
                self.app_page_dict, self.modules_role, self.app_relationship_uuid_set, 
                self.app_field_uuid_set)
        node_checker_service.document_other_info = self.document_other_info
        node_checker_service.check_node(node_uuid_set, variable_uuid_set)
        self.update_any_list(node_checker_service)


class ApprovalFlowDocumentCheckService(DocumentCheckerService):
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict, document_version: int, 
        app_workflow_list: list, app_model_uuid_set: set, 
        app_field_uuid_set: set, app_func_uuid_dict: dict, 
        app_page_list: set, app_const_uuid_set: set, 
        app_enum_uuid_set: set, app_user_role_uuid_set: set, 
        modules_role, app_relationship_uuid_set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, WorkflowModel, *args, **kwargs)
        self.document_version = document_version
        self.app_model_uuid_set = app_model_uuid_set
        self.app_field_uuid_set = app_field_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_list = app_page_list
        self.app_const_uuid_set = app_const_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.modules_role = modules_role
        self.app_relationship_uuid_set = app_relationship_uuid_set
        self.app_page_uuid_set = set()
        self.app_page_dict = dict()
        for page in self.app_page_list:
            page_uuid = page.get(PageModel.page_uuid.name)
            self.app_page_uuid_set.add(page_uuid)
            self.app_page_dict.update({page_uuid: page})
        self.app_wf_uuid_set = set()
        self.module_wf_name_set = set()
        self.document_wf_uuid_set = set()
        for wf in app_workflow_list:
            wf_uuid = wf.get("wf_uuid", "")
            wf_name = wf.get("wf_name", "")
            wf_module_uuid = wf.get("module_uuid", "")
            wf_document_uuid = wf.get("document_uuid", "")

            # 找到原文档中所有的工作流，为了新增、更新、删除文档的工作流
            if wf_document_uuid == self.document_uuid:
                self.document_wf_uuid_set.add(wf_uuid)
            else:
                # 排除当前文档所有的 wf_uuid，获取应用的所有 wf_uuid
                self.app_wf_uuid_set.add(wf_uuid)
                # 排除当前文档所有的 wf_name，获取模块的所有 wf_name
                if wf_module_uuid == self.module_uuid:
                    self.module_wf_name_set.add(wf_name)

    @checker.run
    def check_workflow(self):
        workflow_checker_service = ApprovalFlowCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, self.element,
                self.app_wf_uuid_set, self.app_model_uuid_set,
                self.app_field_uuid_set, self.app_func_uuid_dict, 
                self.app_page_uuid_set, self.app_const_uuid_set, 
                self.app_enum_uuid_set, self.app_user_role_uuid_set,
                self.app_page_dict, self.modules_role, self.app_relationship_uuid_set, 
                is_copy=self.is_copy)
        wf_uuid = workflow_checker_service.element_uuid
        workflow_checker_service.document_other_info = self.document_other_info
        try:
            workflow_checker_service.check_uuid()
            workflow_checker_service.check_uuid_unique(self.app_wf_uuid_set)
            workflow_checker_service.check_name()
            workflow_checker_service.check_settings()
            # workflow_checker_service.check_name_unique(self.module_wf_name_set)
            # workflow_checker_service.check_variables()
            workflow_checker_service.check_nodes()
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(workflow_checker_service)
            raise e
        else:
            self.update_any_list(workflow_checker_service)

        # 找到新增 或 更新的工作流
        workflow_checker_service.check_modify(self.document_wf_uuid_set)
        if workflow_checker_service.insert_query_list:
            self.document_insert_list.extend(workflow_checker_service.insert_query_list)
        if workflow_checker_service.update_query_list:
            self.document_update_list.extend(workflow_checker_service.update_query_list)
        
        # 找出删除的工作流，将其 is_delete 置为 True
        delete_wf_uuid_set = self.document_wf_uuid_set - set([wf_uuid])
        for this_wf_uuid in delete_wf_uuid_set:
            query = workflow_checker_service.build_delete_query(this_wf_uuid)
            self.document_delete_list.append(query)
    
