# -*- coding:utf-8 -*-

import time
import asyncio
import unittest
import logging

from apps import exceptions
from baseutils.log import app_log
from apps.utils import lemon_uuid, <PERSON>son
from apps.ide_const import (
    Form, StateMachine, State, Event, Variable, Timer, Trigger, Transition, Condition,
    HandleType, HandlerType, HandleCondition, ValueEditor, Action
)
from runtime.engine import engine
from runtime.component.base import RootComponent
from runtime.utils import LemonMessage
from runtime.core.connector import ClientConnector
from runtime.core.utils import build_topic
from runtime.core.sm import ComponentLemonStateMachine
from runtime.core.sm import RootLemonStateMachine
from runtime.core.sm import PageLemonStateMachine
from runtime.core.sm import DatalistStateMachine
from runtime.core.sm import FormStateMachine
from runtime.component.page import Page
from runtime.component.container import Form as FormContainer

from tests.runtime import RuntimeTestCase
from tests.document.sm_root import sm_root
from tests.document.sm_page import sm_page
from tests.document.sm_form import sm_form
from tests.document.sm_datalist import sm_datalist
from tests.document.page import form_author_page
from tests.document.form import Forms, AuthorItem


sid = "ec2e226ef98c11ea96b984c5a603df26"


class StateMachineRuntimeTestCase(RuntimeTestCase):

    async def asyncSetUp(self):
        await super().asyncSetUp()
        self.sid = sid
        connector = ClientConnector(sid)
        self.page_machine_id = form_author_page.uuid
        self.form_machine_id = Forms.AUTHOR.uuid
        self.rselect_machine_id = AuthorItem.BOOK.uuid
        self.rcreate_machine_id = AuthorItem.CREATE_BOOK.uuid
        self.rselect_p_context = {"submit_row_list": [0]}
        self.rcreate_p_context = {"submit_row_list": [0]}
        root_component = RootComponent(
            p_context=dict(), component_dict=dict(), connector=connector)
        self.root_lsm = RootLemonStateMachine(root_component, **sm_root.to_dict())
        await self.root_lsm.async_start_machine()
        page_dict = {"uuid": self.page_machine_id}
        page_component = Page(
            p_context=dict(), page_dict=page_dict, connector=connector, 
            root_machine_id=self.sid, parent_machine_id=self.sid)
        self.page_lsm = PageLemonStateMachine(page_component, **sm_page.to_dict())
        await self.page_lsm.async_start_machine()
        form_dict = {"uuid": self.form_machine_id}
        form_component = FormContainer(
            p_context=dict(), component_dict=form_dict, connector=connector, 
            root_machine_id=self.sid, parent_machine_id=self.page_machine_id)
        self.form_lsm = FormStateMachine(form_component, **sm_form.to_dict())
        await self.form_lsm.async_start_machine()
        await asyncio.sleep(1)
    
    async def test_aaaa_machine_tree(self):
        app_log.info(f"self.root_lsm.machine_tree: {self.root_lsm.machine_tree}")
        self.assertEqual(self.root_lsm.machine_tree, {self.page_machine_id})
        self.assertEqual(self.page_lsm.machine_tree, {self.form_machine_id})
        self.assertEqual(self.form_lsm.machine_tree, {self.rselect_machine_id, self.rcreate_machine_id})
    
    async def test_aaab_machine_stop(self):
        await self.rcreate_lsm.stop()
        await asyncio.sleep(1)
        app_log.info(f"form_machine_tree: {self.form_lsm.machine_tree}")
        self.assertEqual(self.form_lsm.machine_tree, {self.rselect_machine_id})
        await self.rselect_lsm.stop()
        await asyncio.sleep(1)
        self.assertEqual(self.form_lsm.machine_tree, set())

        # 表单 的子状态机结束后，表单状态机不会停止
        self.assertEqual(self.form_lsm.is_stopped, False)

        await self.form_lsm.stop()
        await asyncio.sleep(1)
        self.assertEqual(self.page_lsm.machine_tree, set())

        # 页面 的子状态机结束后，页面状态机停止
        self.assertEqual(self.page_lsm.is_stopped, True)

        # 根 的子状态机结束后，根状态机不会停止
        self.assertEqual(self.root_lsm.is_stopped, False)

    async def asyncTearDown(self):
        app_log.info("page_lsm stop")
        await self.page_lsm.stop()
        await self.root_lsm.stop()
        await super().asyncTearDown()


if __name__ == "__main__":
    unittest.main()
