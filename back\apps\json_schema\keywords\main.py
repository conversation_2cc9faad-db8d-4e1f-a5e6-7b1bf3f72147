from apps.base_utils import (
    check_lemon_uuid, check_common_name, check_document_func_name,
    check_document_name, check_calc_field_value, check_email, check_lemon_name,
    check_permission, check_sys_field
)
from jsonschema.exceptions import ValidationError
from apps.ide_const import LemonDesignerErrorC<PERSON> as LDEC, ReturnCode
from apps.json_schema.const import UniqueName, ScopeType
from apps.json_schema.context import DocCtx, AppCtx, Resource, KeyWordCtx, JsonElement
from apps.json_schema.utils import ReferenceData
from apps import entity
from typing import Optional
from loguru import logger


def lemon_uuid(validator, value, instance, schema):
    """
    校验lemon_uuid
    :return:
    """
    if not check_lemon_uuid(instance):
        message = LDEC.ELEMENT_UUID_ERROR.message
        error = ValidationError(message)
        error.return_code=LDEC.ELEMENT_UUID_ERROR
        error.parent = validator.keyword_ctx.parent
        yield error


def reference(validator, value, instance, schema):
    doc_ctx: DocCtx = validator.doc_ctx
    app_ctx: AppCtx = validator.app_ctx
    keyword_ctx: KeyWordCtx = validator.keyword_ctx
    ref_type = value.get("ref_type", "")
    res_type = value.get("res_type", ref_type)
    res_key = value.get("res_key", None)
    res_attr_key = value.get("res_attr_key", None)
    instance_key = value.get("instance_key", res_key)   # 默认和res_key一致
    instance_attr_key = value.get("instance_attr_key", res_attr_key)    # 默认和res_attr_key一致
    is_hide = value.get("hide", False)
    app_resources: Optional[Resource] = getattr(app_ctx, f"app_{res_type}", None)
    if app_resources and instance is not None:
        if res_key and res_attr_key:
            res_unique_value = instance.get(instance_key, None)
            resource_attr_item_value = instance.get(instance_attr_key, None)
        else:
            res_unique_value = instance
            resource_attr_item_value = None
        resource = app_resources.get_resource(res_unique_value, res_attr_key, resource_attr_item_value)
        if resource:
            if keyword_ctx.element is None:
                logger.error(f"{keyword_ctx.path}, keyword_ctx.element is None")
                return
            logger.debug(keyword_ctx.path)
            control_uuid = keyword_ctx.element.uuid
            control_type = keyword_ctx.element.type
            attr_name = keyword_ctx.element.current_attr or keyword_ctx.element.name
            # attr = keyword_ctx.path[-1]
            # attr_name = getattr(keyword_ctx.element.attr_names, attr, "")
            if isinstance(instance, dict):
                attr_uuid = instance.get("uuid", "")
            else:
                attr_uuid = keyword_ctx.element.uuid
            # todo: 引用了一个资源的子集，也需要记录这个资源的引用
            resource = app_resources.get_resource(res_unique_value)
            ref_document = resource.get("document_uuid") if isinstance(resource, dict) else resource.document_uuid
            reference_attr_data = ReferenceData(
                ref_type, ref_uuid=res_unique_value, ref_instance_attr=resource_attr_item_value,
                path=keyword_ctx.path, title=keyword_ctx.element.name,
                attr_uuid=attr_uuid, control_uuid=control_uuid, control_type=control_type,
                element=keyword_ctx.element, attr=attr_name, ref_document=ref_document, hide=is_hide)
            doc_ctx.to_add_reference(reference_attr_data)
    else:
        message = LDEC.REFERENCE_NOT_FOUND.message
        error = ValidationError(message)
        error.return_code = LDEC.REFERENCE_NOT_FOUND
        error.parent = keyword_ctx.parent
        yield error


def unique(validator, value, instance, schema):
    unique_type, unique_name = value
    resource_checker = None
    return



def is_element(validator, value, instance, schema):
    if value:
        element = JsonElement.from_json(instance)
        if isinstance(value, dict):
            if uuid_key := value.get("uuid_key"):
                element.uuid = instance.get(uuid_key, "")
            if type_key := value.get("type_key"):
                element.type = instance.get(type_key, "")
        validator.keyword_ctx.parent = validator.keyword_ctx.element
        validator.keyword_ctx.element = element


def attr_name(validator, value, instance, schema):
    if validator.keyword_ctx.element:
        # attr = validator.keyword_ctx.path[-1]
        # validator.keyword_ctx.element.set_attr_name(attr, value)
        validator.keyword_ctx.element.current_attr = value


def error_message(validator, value, instance, schema):
    def process_error(error, message_unformated):
        if message_unformated is None:
            return
        if isinstance(message_unformated, ReturnCode):
            return_code = message_unformated
            return_code.message.format((error.parent or {}).get("name", ""))
            error.return_code = return_code
            error.message = return_code.message
        elif isinstance(message_unformated, str) and message_unformated.startswith("$"):
            message_unformated = message_unformated[1:]
            cls_name, field_name = message_unformated.split(".")
            if cls_name != "LDEC":
                return
            return_code: ReturnCode = getattr(LDEC, field_name, None)
            if not return_code:
                return
            return_code.message.format((error.parent or {}).get("name", ""))
            error.return_code = return_code
            error.message = return_code.message
        else:
            error.message = message_unformated.format((error.parent or {}).get("name", ""))
        error.processed = True

    for error in (validator.schema_ctx.errors or []):
        if getattr(error, "processed", False):
            # todo: 这里会有所有的error，暂时标记处理一下
            continue
        if error.validator == "dependentRequired":
            dependents = value.get("dependentRequired", {})
            for dependent_key in error.validator_value:
                if dependent_key not in dependents:
                    continue
                message_unformated = dependents[dependent_key]
                process_error(error, message_unformated)
        else:
            properties = value.get("properties", {})
            property = properties.get(error.path[-1], {})
            if not property:
                continue
            message_unformated = property.get(error.validator)
            process_error(error, message_unformated)


def entity_record(validator, value, instance, schema):
    doc_ctx: DocCtx = validator.doc_ctx
    entity_cls = value.get("entity")
    unique_keys = value.get("unique", [])
    need_delete = None
    if isinstance(entity_cls, str):
        entity_cls = getattr(entity, entity_cls, None)
    if not entity_cls:
        return
    query_data = {}
    for attr_name, entity_field in value.get("fields", {}).items():
        if isinstance(entity_field, str):
            entity_field = getattr(entity_cls, entity_field, None)
        if not entity_field:
            continue
        if attr_name[0] == "$":
            entity_field_value = doc_ctx.get_current_entity_attr(attr_name[1:])
        else:
            # todo：default关键字修改json
            entity_field_value = instance.get(attr_name)
        if attr_name in unique_keys:
            original_entity_unique_values = getattr(doc_ctx.original_entity, attr_name, [])
            
        query_data.update({entity_field: entity_field_value})
    if need_delete:
        doc_ctx.to_remove_entity(entity_cls, query_data)
