import time
import asyncio
import socket
import re
import ujson
from functools import partial
from apps.ide_const import RelationshipType
from jinja2 import Environment, FileSystemLoader
from peewee_async import Manager, PooledMySQLDatabase
from peewee import RawQuery
from baseutils.peeweedbevolve_wrap import evolve_models
from baseutils.utils import make_model_copy, timeit_async
from aiohttp import ClientSession
from baseutils.log import app_log
from .config import TEMPLATE_PATH
from apps.entity import (
    Event, Navigation, NavigationItem, Page, Print, LabelPrint, Func, TemplateTable, Workflow, Document,
    DocumentContent, StateMachine, UserRole, RuleChain, Module, ExportTemplate,
    ModelBasic, ModelField, RelationshipBasic, Resource, Tag
)
from apps.utils import make_runtime_table_name
from apps.user_entity import SystemConfig, RoleMember, sys_table_handler
from apps.base_entity import ModelCloudFunction, PermissionConfig
from baseutils.const import SystemField

runtime_sys_table = {
        Event: {},
        Page: {},
        Print: {},
        LabelPrint: {},
        Func: {},
        ModelBasic: {},
        ModelField: {},
        RelationshipBasic: {},
        Workflow: {Workflow.document_uuid: [DocumentContent]},
        StateMachine: {},
        RuleChain: {},
        UserRole: {},
        Document: {Document.document_uuid: [DocumentContent]},
        Navigation: {Navigation.navigation_uuid: [NavigationItem]},
        TemplateTable: {},
        ExportTemplate: {},
        Module: {},
        Resource: {},
        Tag: {}
    }
runtime_sys_table_add = {
    RoleMember: {},
    SystemConfig: {},
    ModelCloudFunction: {},
    PermissionConfig: {}
}
runtime_json_value = []

jinja2_env = Environment(loader=FileSystemLoader(searchpath=TEMPLATE_PATH), enable_async=True)
local_deploy_template = Environment(loader=FileSystemLoader(searchpath=TEMPLATE_PATH+"/local_deploy"), enable_async=True)

def get_free_port():
    sock = socket.socket()
    sock.bind(("", 0))
    ip, port = sock.getsockname()
    sock.close()
    return port

async def async_run(cmd, env=None):
    app_log.info("async run cmd: " + repr(cmd))
    start_time = time.time()
    proc = await asyncio.create_subprocess_shell(
        cmd,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
        env=env)

    stdout, stderr = await proc.communicate()
    step = time.time() - start_time
    app_log.debug(f"result: {proc.returncode}")
    app_log.info(f"async run cmd ({cmd}) result: {proc.returncode} run_time:{step}")
    if proc.returncode != 0:
        app_log.info(f"async_run error: {stdout}")
        app_log.info(f"async_run error: {stderr}")
    return proc.returncode, stdout, stderr


async def update_runtime_info(app, db, database, user_uuid, app_uuid):
    from apps.user_entity import (
        FieldAutoIncrement, FormTmp, WorkflowInstance, NodeInstance, NodeTask, WFStore,
        UniverDocument, UniverTemplate)
    from apps.runtime_entity import RoleMember
    # create runtime user common table
    # RoleMember._meta.set_database(database)
    FieldAutoIncrement._meta.set_database(database)
    FormTmp._meta.set_database(database)
    UniverDocument._meta.set_database(database)
    UniverTemplate._meta.set_database(database)
    wf_name = make_runtime_table_name(app_uuid, None, WorkflowInstance._meta.meta_table_name)
    wf = make_model_copy(
        WorkflowInstance, wf_name, must_new=True, must_rename=True)
    node_name = make_runtime_table_name(app_uuid, None, NodeInstance._meta.meta_table_name)
    node = make_model_copy(
        NodeInstance, node_name, must_new=True, must_rename=True)
    task_name = make_runtime_table_name(app_uuid, None, NodeTask._meta.meta_table_name)
    task = make_model_copy(
        NodeTask, task_name, must_new=True, must_rename=True)
    wf_store_name = make_runtime_table_name(app_uuid, None, WFStore._meta.meta_table_name)
    wf_store = make_model_copy(
        WFStore, wf_store_name, must_new=True, must_rename=True)
    wf._meta.set_database(database)
    node._meta.set_database(database)
    task._meta.set_database(database)
    wf._meta._schema = user_uuid
    node._meta._schema = user_uuid
    task._meta._schema = user_uuid
    wf_store._meta.set_database(database)
    wf_store._meta._schema = user_uuid
    wf._meta.aka = app_uuid[:6] + "_" + WorkflowInstance._meta.table_name
    node._meta.aka = app_uuid[:6] + "_" + NodeInstance._meta.table_name
    task._meta.aka = app_uuid[:6] + "_" + NodeTask._meta.table_name
    wf_store._meta.aka = app_uuid[:6] + "_" + WFStore._meta.table_name
    # todo:改成异步evolve
    with database.allow_sync():
        await app.loop.run_in_executor(None, partial(db.connection().ping, True))
        # try:
        await app.loop.run_in_executor(
            None, partial(
                evolve_models, db, 
                [FieldAutoIncrement, FormTmp, UniverDocument, UniverTemplate,
                    task, node, wf,
                    wf_store], [wf._meta.aka, node._meta.aka, task._meta.aka, wf_store._meta.aka],
                interactive=False))
        # except Exception as e:
        #     import traceback
        #     traceback.print_exc()
    return [task, node, wf]

async def update_ucenter_info(
        ucenter_db, app, app_uuid, user_uuid, app_revision, database=None, system_tables=None,
        app_env=""):
    # return
    from apps.runtime_entity import RoleMember
    # 创建ucenter视图
    with ucenter_db.allow_sync():
        views = await app.loop.run_in_executor(None, ucenter_db.get_views)
        view_names = {view.name: view for view in views}
        user_role_table_name = UserRole._meta.table_name
        user_role_view_name = "_".join([user_role_table_name, app_uuid])
        # 创建userrole_app_uuid视图
        sql = (f'CREATE OR REPLACE VIEW `{user_role_view_name}` AS (SELECT * FROM '
                    f'`{user_uuid}`.`{make_runtime_table_name(app_uuid, app_revision, user_role_table_name)}`)')
        await app.loop.run_in_executor(None, partial(ucenter_db.execute_sql, sql))
        
        role_member_table_name = RoleMember._meta.table_name
        role_member_view_name = "_".join([role_member_table_name, user_uuid])
        role_member_view = view_names.get(role_member_view_name)
        real_model_view_name = "_".join([role_member_table_name, app_uuid])
        real_model_view_name += app_env
        real_model_view = view_names.get(real_model_view_name)
        real_model = system_tables.get("role_member")
        real_model: RoleMember = make_model_copy(real_model, real_model._meta.table_name, database)
        real_model._meta.schema = database.database
        if not role_member_view:
            if not real_model_view:
                # 生成本表的, 生成总的
                real_view_sql = gen_real_model_view_sql(
                    real_model, real_model_view_name, user_uuid)
                await app.loop.run_in_executor(None, partial(ucenter_db.execute_sql, real_view_sql))
                all_sql = gen_role_member_sql(view_names, role_member_table_name, real_model_view_name)
                await app.loop.run_in_executor(None, partial(ucenter_db.execute_sql, all_sql))
        else:
            if not real_model_view:
                # 生成本表的, 生成总的
                real_view_sql = gen_real_model_view_sql(
                    real_model, real_model_view_name, user_uuid)
                await app.loop.run_in_executor(None, partial(ucenter_db.execute_sql, real_view_sql))
                role_member_view_sql = update_role_member_view_sql(
                    role_member_view, app_uuid)
                await app.loop.run_in_executor(None, partial(ucenter_db.execute_sql, role_member_view_sql))
                view_names[role_member_view_name] = None
                all_sql = gen_role_member_sql(view_names, role_member_table_name, real_model_view_name)
                await app.loop.run_in_executor(None, partial(ucenter_db.execute_sql, all_sql))


def gen_real_model_view_sql(real_model, real_model_view_name, user_uuid):
    """创建指向运行时应用role_member表的视图"""
    import peewee
    selected_fields = [real_model.id]
    index = 1
    for field in real_model._meta.sorted_fields:
        if field.column_name in SystemField.FIELD_NAME_LIST:
            continue
        elif isinstance(field, peewee.ForeignKeyField):
            continue
        if field.name == "tenant_uuid":
            index = len(selected_fields)
        if field.name == "app_uuid":
            selected_fields.insert(index, field.alias(field.name))
        else:
            selected_fields.append(field.alias(field.name))
    # swap_field(selected_fields)
    query = real_model.select(*selected_fields)
    app_log.info(query.dicts())

    sql = f'CREATE OR REPLACE VIEW `{real_model_view_name}` AS ({query.dicts()})'
    app_log.info(sql)
    return sql


def gen_role_member_sql(view_names, role_member_table_name, role_member_view_name):
    """创建 role_member 总视图"""
    views_name_list = [name for name in view_names if name.startswith(role_member_table_name + "_")]
    if role_member_view_name:
        views_name_list.append(role_member_view_name)
    select_sql_list = [f"SELECT * FROM {name}" for name in views_name_list]
    all_sql = f"CREATE OR REPLACE VIEW `{role_member_table_name}` AS ({' UNION '.join(select_sql_list)}) "
    return all_sql


def update_role_member_view_sql(role_view, app_uuid):
    """兼容role_member总视图的两种数据源"""
    select_sql = role_view.sql
    if not re.search(r"where ", select_sql, re.I):
        select_sql += f" where `app_uuid` <> '{app_uuid}'"
    else:
        pattern = r'( WHERE\s*\(.*\))'
        replace = r"\1 AND (`app_uuid` <> '{u}')"
        replace = replace.format(u=app_uuid)
        select_sql = re.sub(pattern, replace, select_sql, flags=re.I)
    role_view_sql = f"CREATE OR REPLACE VIEW `{role_view.name}` AS ({select_sql})"
    app_log.info(role_view_sql)
    return role_view_sql


async def process_role_member_relationship(
        db, app, database, objs, database_name, app_uuid, app_revision):
    app_log.info("process role_member")

    def process_model_data(uuid_field, table_name, data_dict):
        sql = f"select id, `{uuid_field}` from `{table_name}`"
        tenant_uuid = table[9:17]
        app_log.info(tenant_uuid)
        with db.allow_sync():
            result = db.execute_sql(sql)
            for info in list(result):
                tenant_dict = data_dict.setdefault(tenant_uuid, dict())
                tenant_dict.update({info[1]: info[0]})

    def process_user_role(user_roles):
        role_dict = dict()
        for u in user_roles:
            role_dict.update({u.role_uuid: u.id})
        return role_dict

    def process_role_member_obj(role_members, u_dict, d_dict, r_dict):
        for r in role_members:
            if not r.member_uuid or not r.role_uuid:
                continue
            tenant_uuid = r.tenant_uuid[:8]
            member_type = r.member_type
            role_uuid = r.role_uuid
            if member_type == 0:
                r.to_user = u_dict.get(tenant_uuid, {}).get(r.member_uuid)
                r.role = r_dict.get(role_uuid)
            elif member_type == 1:
                r.to_department = d_dict.get(tenant_uuid, {}).get(r.member_uuid)
                r.role = r_dict.get(role_uuid)
            with db.allow_sync():
                r.save()

    with db.allow_sync():
        table_all = await app.loop.run_in_executor(
            None, db.get_tables)
    pattern_user = r'^{}.+tenant_user$'.format(app_uuid[:8])
    pattern_dep = r'^{}.+tenant_department$'.format(app_uuid[:8])
    user_dict, dep_dict = dict(), dict()
    for table in table_all:
        if re.match(pattern_user, table):
            process_model_data("06de10052a5e53d59582b7893a41e9c0", table, user_dict)
        elif re.match(pattern_dep, table):
            process_model_data("e8ab04eabc145719a5e79196854e61ec", table, dep_dict)
    from apps.user_entity import RoleMember
    from apps.user_entity import UserRole
    table_name = make_runtime_table_name(app_uuid, app_revision, "lemon_userrole")
    user_role = make_model_copy(UserRole, table_name, database)
    user_role._meta.schema = database_name
    user_roles = await objs.execute(user_role.select())
    role_dict = process_user_role(user_roles)

    role_member_table_name = make_runtime_table_name(app_uuid, None, "role_member")
    role_member = make_model_copy(RoleMember, role_member_table_name, database)
    role_member._meta.schema = database_name
    role_members = await objs.execute(role_member.select())
    role_members = process_role_member_obj(role_members, user_dict, dep_dict, role_dict)


def process_jdbc_tables(tenant_models, wf_models, models):
    need_filter_tenant = []
    need_filter_tenant += wf_models
    not_need_filter_tenant = []
    not_need_filter_tenant += tenant_models
    for model in models:
        for base_model in sys_table_handler._lemon_model:
            if base_model._meta.table_name in model._meta.table_name:
                not_need_filter_tenant.append(model)
                break
        if model not in not_need_filter_tenant:
            need_filter_tenant.append(model)
    return need_filter_tenant, not_need_filter_tenant


def process_special_middle(relationship):
    data = {}
    if relationship.backref == "用户部门":
        data.update({
            "real_name": "成员",
            "real_backref": "部门",
            "real_uuid": "7137d83b7422547ea393483183d55e06"
        })
    else:
        data.update({
            "real_name": "部门",
            "real_backref": "成员",
            "real_uuid": "b8e68020dc885868aac0594cd511f025"
        })
    return data


def process_special_many(relationship):
    data = {}
    data.update({
        "real_ref_type": RelationshipType.MANY_TO_MANY
    })
    if relationship.frontref == "成员":
        data.update({
            "real_backref": "部门",
        })
    elif relationship.frontref == "部门":
        data.update({
            "real_backref": "成员",
        })
    return data


@timeit_async
async def update_wf_tables_fk(database, objs, database_name, app_uuid, engine):
    """
    查出全部的workflow_instance / node_instance 的uuid 与 id的映射关系
    更新node_instance / node_task 的workflow_instance_id 与 node_instance_id
    """
    from apps.user_entity import WorkflowInstance, NodeInstance, NodeTask
    from apps.base_entity import TenantUser
    from apps.runtime_entity import AppTenant

    wf_name = make_runtime_table_name(app_uuid, None, WorkflowInstance._meta.meta_table_name)
    wf = make_model_copy(
        WorkflowInstance, wf_name, must_new=True, must_rename=True)
    node_name = make_runtime_table_name(app_uuid, None, NodeInstance._meta.meta_table_name)
    node = make_model_copy(
        NodeInstance, node_name, must_new=True, must_rename=True)
    task_name = make_runtime_table_name(app_uuid, None, NodeTask._meta.meta_table_name)
    task = make_model_copy(
        NodeTask, task_name, must_new=True, must_rename=True)
    query = AppTenant.select().where(AppTenant.app_uuid == app_uuid)
    app_tenant_info = await engine.access.list_obj(AppTenant, query, need_delete=True, as_dict=True)
    tenant_user_models = {
        t["tenant_uuid"]: make_model_copy(
            TenantUser, app_uuid[:8] + "_" + t["tenant_uuid"][:8] + "_tenant_user",
            must_new=True, must_rename=True) for t in app_tenant_info}
    wf._meta.set_database(database)
    node._meta.set_database(database)
    task._meta.set_database(database)
    wf._meta._schema = database_name
    node._meta._schema = database_name
    task._meta._schema = database_name
    wf_list = await objs.execute(wf.select(wf.id, wf.wf_instance_uuid, wf.tenant_uuid, wf.creator, wf.outer_wf_instance_uuid))
    node_list = await objs.execute(node.select(node.id, node.node_instance_uuid, node.wf_instance_uuid))
    task_list = await objs.execute(task.select(
        task.id, task.node_instance_uuid, task.tenant_uuid, task.handler, task.assigner, task.withdrawer))

    t_dict = {}
    for tenant_uuid, t in tenant_user_models.items():
        t._meta.set_database(database)
        t._meta._schema = database_name
        try:
            t_list = await objs.execute(t.select(t.id, t.user_uuid))
            t_dict.update({
                tenant_uuid: {u.user_uuid: u.id for u in t_list}
            })
        except Exception as e:
            app_log.error(f"update_wf_tables_fk error: {e}")

    app_log.info(f"wf_list: {len(wf_list)}, node_list: {len(node_list)}, task_list: {len(task_list)}")

    # 处理一下workflow_instance / node_instance 的uuid 与 id的映射关系
    wf_dict = {w.wf_instance_uuid: w.id for w in wf_list}
    node_dict = {n.node_instance_uuid: n.id for n in node_list}

    def update_wf(w, t_dict):
        w.creator_id = t_dict.get(w.tenant_uuid, {}).get(w.creator)
        w.outer_wf_instance_id = wf_dict.get(w.outer_wf_instance_uuid)
    [update_wf(w, t_dict) for w in wf_list]

    def update_node(n, wf_dict):
        n.wf_instance_id = wf_dict.get(n.wf_instance_uuid)
    [update_node(n, wf_dict) for n in node_list]

    def update_task(t, node_dict, t_dict):
        t.node_instance_id = node_dict.get(t.node_instance_uuid)
        t.handler_id = t_dict.get(t.tenant_uuid, {}).get(t.handler)
        t.assigner_id = t_dict.get(t.tenant_uuid, {}).get(t.assigner)
        t.withdrawer_id = t_dict.get(t.tenant_uuid, {}).get(t.withdrawer)
    [update_task(t, node_dict, t_dict) for t in task_list]

    with database.allow_sync():
        wf.bulk_update(
            wf_list, [wf.creator_id, wf.outer_wf_instance_id], batch_size=1000, update_modify_when_dirty=True)
        node.bulk_update(
            node_list, [node.wf_instance_id.name], batch_size=1000, update_modify_when_dirty=True)
        task.bulk_update(
            task_list, [
                task.node_instance_id.name, task.handler_id.name, task.assigner_id.name, task.withdrawer_id.name],
            batch_size=1000, update_modify_when_dirty=True)


@timeit_async
async def process_redis_cache(app_uuid, engine, env_name):
    all_keys = []

    for p in [engine.config.DATALIST_OPTION_PREFIX, engine.config.SPLIT_OPTION_PREFIX]:
        prefix_key = env_name + p
        key_reg = prefix_key + "*" + app_uuid
        # key_reg = "*".join([key, app_uuid])
        keys = await engine.redis.keys(key_reg, prefix=True)

        if keys:
            temp_k = keys[0]
            last_part = temp_k.split(":")[-1]
            if last_part.count("_") < 4:
                # 认为已处理过
                continue
        all_keys.extend(keys)

        for key in keys:
            key: str
            res = await engine.redis.redis.hgetall(key)
            for d_uuid, d in res.items():
                if d:
                    res[d_uuid] = ujson.loads(d)

            key = key.replace(prefix_key, "")
            tenant_uuid, _, user_uuid, page_uuid, last = key.split("_", 4)
            app_log.info([tenant_uuid, app_uuid, user_uuid, page_uuid, last])
            new_key = "_".join([tenant_uuid, app_uuid, user_uuid, page_uuid])
            new_key = prefix_key + new_key

            await engine.redis.redis.hset(new_key, last, ujson.dumps(res))
            await engine.redis.redis.expire(new_key, 3600*24*7)

    app_log.info(f"process_redis_cache delete keys: {all_keys}")
    for key in all_keys:
        await engine.redis.delete(key)
