#!/bin/bash
DIR="$( cd "$( dirname "$0"  )" && pwd  )"
cd $DIR
source ./config.sh
SERVICE_DIR=""
if [[ -d /usr/lib/systemd/system ]]  
then
    SERVICE_DIR=/etc/systemd/system
elif [[ -d /etc/systemd/system ]]  
then
    SERVICE_DIR=/usr/lib/systemd/system
fi

if [ $SERVICE_DIR ] 
then 
    cd $SERVICE_DIR
    echo "" > ${app_env}.service
    echo "[Unit]" >> ${app_env}.service
    echo "Description=" >> ${app_env}.service
    echo "Documentation=" >> ${app_env}.service
    echo "After=network.target" >> ${app_env}.service
    echo "Wants=" >> ${app_env}.service
    echo "Requires=" >> ${app_env}.service

    echo "[Service]" >> ${app_env}.service
    echo "ExecStart=${DIR}/start_app.sh" >> ${app_env}.service
    echo "ExecStop=" >> ${app_env}.service
    echo "ExecReload=${DIR}/start_app.sh" >> ${app_env}.service
    echo "Type=simple" >> ${app_env}.service

    echo "[Install]" >> ${app_env}.service
    echo "WantedBy=multi-user.target" >> ${app_env}.service
    systemctl enable ${app_env}.service
else
    echo '您的系统暂不支持自动设置开机自启'
fi

