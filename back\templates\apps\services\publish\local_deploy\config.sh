#!/bin/bash
export mysql_host="{{mysql_host}}"
export mysql_port={{mysql_port}}   #mysql 容器映射到主机的端口
export mysql_real_port=3306
export expose_redis_port=16379
export mysql_user="{{mysql_user}}"
export mysql_pwd="{{mysql_password}}"
export mysql_max_size={{mysql_max_size}}
export nginx_port={{nginx_port}}    #nginx 容器映射到主机的端口
export app_env={{app_env}}
export subnet="*********/16"
export gateway="*********"
export web_data_dir="{{file_storage_dir}}"
export runtime_file_storage_dir="{{runtime_file_storage_dir}}"
export proxy_connect_timeout={{proxy_connect_timeout}}
export proxy_read_timeout={{proxy_read_timeout}}
export proxy_send_timeout={{proxy_send_timeout}}
export send_timeout={{send_timeout}}
export endpoint="{{endpoint}}"
export oss_status={{oss_status}}
export api_response_timeout={{api_response_timeout}}
export app_domain="{{server_address}}"
export domain_name="{{custom_domain_name}}"
export custom_server_address="{{custom_server_address}}"
export request_max_size={{request_max_size}}
export local_database_name="{{local_database_name}}"
export MINIO_USER="minio"
export MINIO_PWD="minio123"
export MINIO_PORT=9000
export MINIO_CONSOLE_PORT=9001
export kafka_hosts={{kafka_hosts}}