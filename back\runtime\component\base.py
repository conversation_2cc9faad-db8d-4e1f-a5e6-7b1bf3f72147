# -*- coding:utf-8 -*-

import os
import abc
import copy
import asyncio
import time
import traceback
import datetime
import decimal
import aiohttp
import ujson
import _operator
import weakref

import peewee
from typing import Dict, AnyStr
from collections import OrderedDict

from apps.base_entity import lemon_entity, UUID as SYS_ENTITY_UUID
from baseutils.const import SystemNamespace, EventWhen
from apps import Runtime
from baseutils.log import app_log
from baseutils.const import CustomComponentType
from baseutils.utils import LemonContextVar, FuncRunError, func_trigger_context
from baseutils.hack import LemonAsyncQueryWrapper
from apps.base_utils import (
    lemon_uuid, join_search_path, safe_pop, get_sys_field_uuid,
    convert_timestamp_to_datetime, GlobalVars
)
from apps.utils import build_model_field_path, make_runtime_table_name
from baseutils.const import Code, PermissionAction, ValueEditorMeaning
from apps.ide_const import (
    AddMethodType, BaseComponentType, ComponentType, EventAction,
    EventResponseMethod, SelectType, FieldType, PreprocessType, RelationshipType,
    ValueEditorType, PageToolName, IDECode, DataSourceType, PurposeType, DynamicSourceType,
    DropdownStyle
)
from apps.exceptions import LemonPermissionError, FuncExecError, LemonRollbackError, ExprExecError
from runtime.const import (
    DatetimeCycleType, DefaultResult, EventActionName, FormatErrorType,
    LemonRuntimeErrorCode as LREC
)
from runtime.core.connector import ClientConnector
from runtime.core.data_preprocess import AsyncDataPreprocess
from runtime.utils import (
    LemonDictResponseCommand as LDRC, build_field_path, build_join_info,
    LemonMessage, decorator_helper, LemonDictResponsePrint, 
    find_locals_subclass, calc_permission_action
)
from runtime.engine import engine
from runtime.core.utils import (
    LemonActor, calc_self_referential_path_join, lemon_model, lemon_model_node, lemon_field, lemon_association,
    build_join, lemon_path_model, lemon_relationship, get_join_path, MessageBox,
    is_self_referential_field, rename_system_model_uuid
)
from runtime.core.func import LemonSMValueEditor
from runtime.core.func_run import FuncRun
from runtime.core.sm import ComponentLemonStateMachine
from runtime.core.value_editor import BaseValueEditor
from runtime.component.data_adapter import ComponentDataAdapter
from runtime.component.utils import (
    BaseSelect, build_recursive_query_inverted, get_timestamp_by_datetime, get_datetime_by_timestamp, process_inverted_tree_data,
    public_build_cascade, public_parse_cascade, get_memory_instance,
    calc_edit_column_permission, get_memory_instance_by_pk_list,
    public_data_format, parse_error_message, build_hierarchy_data
)
from runtime.core.value_editor_utils import get_default_value
from runtime.log import runtime_log
from runtime.protocol import func as func_proto

from tests.utils import UUID


class BaseFieldCheck(object):

    def __init__(self, control_obj):
        self.control_obj = control_obj
        self.control_dict = self.control_obj.control_dict
        self.control_type = self.control_dict.get("type")
        self.control_name = self.control_obj.component_name
        self.data_model = self.control_obj.data_model
        self.column_is_required = True
        self.column_lemon_type = None
        self.collect_data()
        self.column_dict = dict()
        self.validate_result = copy.deepcopy(DefaultResult.VALIDATE)

    @property
    def edit_column(self):
        return self.control_obj.edit_column

    @property
    def edit_column_path(self):
        return self.control_obj.edit_column_path

    def exit(self):
        pass
        # self.control_obj = None
        # self.column_dict = dict()

    def clean(self):
        self.validate_result = copy.deepcopy(DefaultResult.VALIDATE)

    async def validate_field(self, check_value):
        if self.validate_result.get("code") != 0:
            return self.validate_result
        data = {self.edit_column.column_name: check_value}
        model_obj = self.control_obj.parent.model_obj
        pk = self.control_obj.parent.pk
        v_result, v_msg = await self.data_model.validate_field(
            self.edit_column, data=data, pk=pk, obj=model_obj)
        app_log.info(f"v_result: {v_result}, v_msg: {v_msg}")
        if v_result is False:
            self.validate_result.update({"code": -1, "message": v_msg})
        else:
            self.validate_result.update({"real_value": check_value})
        return self.validate_result

    def collect_data(self):
        if self.control_obj.edit_control_type in [
            ComponentType.DATALIST, ComponentType.CARDLIST,
            ComponentType.DATAGRID, ComponentType.TREELIST,
            ComponentType.R_SELECT, ComponentType.R_SELECT_POPUP,
            ComponentType.R_TILE, ComponentType.R_TREE, ComponentType.R_CASCADE
        ]:
            # 子表 和 关联相关
            self.column_is_required = self.control_obj.relation_required
            if self.control_obj.edit_control_type in [
                ComponentType.DATALIST, ComponentType.CARDLIST,
                ComponentType.DATAGRID, ComponentType.TREELIST
            ]:
                placeholder = self.control_obj.data_source.get("placeholder", {})
                if placeholder:
                    if placeholder.get("is_required") is True:
                        self.column_is_required = True

            # elif self.control_obj.edit_control_type == ComponentType.R_TILE:
            #     placeholder = self.control_obj.edit_field_info.get("placeholder", {})
            #     if placeholder:
            #         if placeholder.get("is_required") is True:
            #             self.column_is_required = True
        elif self.edit_column:
            self.column_lemon_type = self.edit_column.lemon_type
            self.column_is_required = self.edit_column.is_required
        if self.control_obj.is_required is True:
            self.column_is_required = True

    def check_is_require(self, check_value):
        # editable_value is not False 的判断是因为旧的值编辑器格式没有uuid
        # 导致 editable_value 一直为 None
        if (
            self.column_is_required
            and
            self.control_obj.editable_value is not False
            and
            self.control_obj.visible_value is not False
        ):
            if check_value in ["", None]:  # 针对 input 输入框
                self.validate_result.update({"code": -1, "message": "请填写必填项"})
            elif isinstance(check_value, list) and not check_value:  # 针对 关联数据
                self.validate_result.update({"code": -1, "message": "请填写必填项"})
            if self.control_obj.component_type in [
                ComponentType.UPLOAD_FILE, ComponentType.UPLOAD_IMAGE
            ]:
                if not check_value:
                    self.validate_result.update(
                        {"code": -1, "message": "请填写必填项"})
        return self.validate_result

    def check_number_attribute(self, check_value):
        check_value = float(check_value)
        number_attribute = self.control_dict.get("number_attribute", {})
        max = number_attribute.get("max")
        min = number_attribute.get("min")
        message = ""
        if isinstance(max, (int, float)) and check_value > max:
            message = f"超出最大值：{str(max)}"
        if isinstance(min, (int, float)) and check_value < min:
            message = f"小于最小值：{str(min)}"
        if message:
            self.validate_result.update({"code": -1, "message": message})

    def check_field_str(self, value):
        max_length_type = self.control_dict.get("max_length_type", 0)
        max_length = self.control_dict.get("max_length", 300)
        value_length = len(value)
        if max_length_type == 0:
            if value_length > self.edit_column.max_length:
                self.validate_result.update({"code": -1, "message": "超出字段最大长度限制"})
        elif max_length_type == 1:
            if value_length > max_length:
                self.validate_result.update({"code": -1, "message": "超出字段最大长度限制"})
        return self.validate_result

    def check_field_int(self, value):
        if value > 2147483647 or value < -2147483648:
            self.validate_result.update({"code": -1, "message": "超出字段范围限制"})
    
    def check_field_decimal(self, value):
        value = decimal.Decimal(str(value)).quantize(
            decimal.Decimal(10) ** (-self.edit_column.decimal_places))
        return value

    def check_field_enum(self, value):
        if value is None:
            return
        choices = self.edit_column.choices
        try:
            choices(value).value
        except Exception:
            self.validate_result.update({"code": -1, "message": "所选枚举值不存在"})

    def check_field_bool(self, value):
        if value not in [True, False, None]:
            self.validate_result.update({"code": -1, "message": "所选布尔值不正确"})

    def check_field_datetime(self, value):
        if value < 0 or value > 9999999999:
            self.validate_result.update({"code": -1, "message": "日期时间超出范围"})

    def check_field_datetime_cycle(self, value):
        start_timestamp = int(value.get("start_timestamp"))
        self.check_field_datetime(start_timestamp)
        cycle_type = value.get("type")
        if cycle_type != DatetimeCycleType.ONCE:
            end_timestamp = int(value.get("end_timestamp"))
            self.check_field_datetime(end_timestamp)
            step_type = value.get("step_type")
            assert step_type in range(8)
            if cycle_type == DatetimeCycleType.STEP:
                int(value.get("step_time"))
            else:
                step_point = value.get("step_point")
                assert isinstance(step_point, list)

    def check_filed_file(self, value):
        control_file_limit = self.control_dict.get("file_limit", 10)
        if len(value) > control_file_limit:
            message = f"上传文件个数超出，上限{control_file_limit}个"
            self.validate_result.update({"code": -1, "message": message})

    async def check_field_relation(self, value):
        column_name = self.edit_column.column_name
        column_model = self.edit_column.model
        app_log.info(f"c: {self.edit_column}, {column_name}, {column_model}")
        start_node = lemon_model_node(self.data_model._meta.table_name)
        end_node = lemon_model_node(column_model._meta.table_name)
        last_path = join_search_path(
            start_node, end_node, last=True, path=self.edit_column_path)
        _, _, r_type = last_path.frontref, last_path.backref, last_path.r_type
        list_value = value
        if list_value is None:
            list_value = []
        if isinstance(list_value, list):
            app_log.info(f"list_value: {list_value}, r_type: {r_type}")
            if last_path.is_middle:
                # 多对多模型，当前模型永远为中间模型的目标模型
                query = build_join(
                    start_node, end_node, path=self.edit_column_path)
                query = query.select(
                    column_model.id,
                    self.edit_column.alias(column_name)).order_by(
                    column_model.id).dicts()
            elif last_path.is_source:
                # 当前模型为目标模型
                # 获取外键模型所有数据的pk
                query = build_join(
                    start_node, end_node, path=self.edit_column_path)
                query = query.select(
                    column_model.id,
                    self.edit_column.alias(column_name)).order_by(
                    column_model.id).dicts()
                if r_type == RelationshipType.ONE_TO_ONE:  # one to one
                    if len(list_value) > 1:
                        message = "一对一关联所选关联数据不能多于一个"
                        self.validate_result.update(
                            {"code": -1, "message": message})
            else:
                # 当前模型为外键模型
                # 只能选择一条数据
                if len(list_value) > 1:
                    message = "外键模型所选关联数据不能多于一个"
                    self.validate_result.update(
                        {"code": -1, "message": message})
                else:
                    query = column_model.select(
                        column_model.id,
                        self.edit_column.alias(column_name)).order_by(
                        column_model.id).dicts()
        else:
            self.validate_result.update(
                {"code": -1, "message": "所选数据与控件类型不匹配"})

    '''
    @description: check(input)
    @Date: 2021-05-13 19:19:16
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def input_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        column_lemon_type = self.edit_column.lemon_type
        try:
            if check_value is None and not self.edit_column.is_required:
                check_value = None
            elif column_lemon_type == FieldType.STRING:  # 文本
                check_value = str(check_value)
                self.check_field_str(check_value)
            elif column_lemon_type == FieldType.INTEGER:  # 整数
                if isinstance(check_value, int):
                    self.check_field_int(check_value)
                    self.check_number_attribute(check_value)
                else:
                    message = f"输入数据{check_value}不是整数"
                    self.validate_result.update({"code": -1, "message": message})

            elif column_lemon_type == FieldType.DECIMAL:  # 小数
                check_value = float(check_value)
                self.check_number_attribute(check_value)
                check_value = self.check_field_decimal(check_value)
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result

    '''
    @description: check(TEXTAREA)
    @Date: 2021-05-14 09:16:14
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def textarea_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        if self.validate_result["code"] == 0:
            try:
                if check_value is None and not self.edit_column.is_required:
                    check_value = None
                else:
                    check_value = str(check_value)
                    self.check_field_str(check_value)
            except Exception:
                format_error = traceback.format_exc()
                app_log.error(format_error)
                await runtime_log.error(format_error)
                message = f"输入数据与{self.control_name}控件类型不匹配"
                self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result

    '''
    @description: check(radio, select)
    @Date: 2021-05-14 09:38:08
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def radio_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        column_lemon_type = self.edit_column.lemon_type
        if column_lemon_type == FieldType.ENUM:  # 枚举
            self.check_field_enum(check_value)
        elif column_lemon_type == FieldType.BOOLEAN:  # 布尔
            self.check_field_bool(check_value)
        else:
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result

    async def select_check_field(self, check_value, select_type=None):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        column_lemon_type = self.edit_column.lemon_type
        try:
            if check_value is None and not self.edit_column.is_required:
                check_value = None
            elif not select_type or column_lemon_type in SelectType.SELECT.get(select_type):
                if column_lemon_type == FieldType.STRING:  # 文本
                    check_value = str(check_value)
                    self.check_field_str(check_value)
                elif column_lemon_type == FieldType.INTEGER:  # 整数
                    check_value = int(check_value)
                    self.check_field_int(check_value)
                    self.check_number_attribute(check_value)
                elif column_lemon_type == FieldType.DECIMAL:  # 小数
                    check_value = float(check_value)
                    self.check_number_attribute(check_value)
                    check_value = self.check_field_decimal(check_value)
                elif column_lemon_type == FieldType.ENUM:  # 枚举
                    self.check_field_enum(check_value)
                elif column_lemon_type == FieldType.BOOLEAN:  # 布尔
                    self.check_field_bool(check_value)
                elif column_lemon_type == FieldType.DATETIME:
                    if check_value is not None:
                        check_value = int(check_value)
                        self.check_field_datetime(check_value)
            else:
                self.validate_result.update(
                    {"code": -1, "message": "下拉框不支持的字段类型"})
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result

    '''
    @description: check(checkbox, switch)
    @Date: 2021-05-14 10:00:13
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def checkbox_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        column_lemon_type = self.edit_column.lemon_type
        if column_lemon_type == FieldType.BOOLEAN:  # 布尔
            self.check_field_bool(check_value)
        else:
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result

    '''
    @description: check(datetime)
    @Date: 2021-05-14 10:03:23
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def datetime_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        try:
            if check_value is not None:
                check_value = int(check_value)
                self.check_field_datetime(check_value)
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result

    '''
    @description: check(datetime_cycle)
    @Date: 2021-05-21 10:53:04
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def datetime_cycle_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        try:
            if check_value:
                self.check_field_datetime_cycle(check_value)
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result

    async def custom_input_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        column_lemon_type = self.edit_column.lemon_type
        try:
            if column_lemon_type == FieldType.STRING:  # 文本
                check_value = str(check_value)
                self.check_field_str(check_value)
            elif column_lemon_type == FieldType.INTEGER:  # 整数
                check_value = int(check_value)
                self.check_field_int(check_value)
            elif column_lemon_type == FieldType.DECIMAL:  # 小数
                check_value = self.check_field_decimal(check_value)
            elif column_lemon_type == FieldType.ENUM:  # 枚举
                self.check_field_enum(check_value)
            elif column_lemon_type == FieldType.BOOLEAN:  # 布尔
                self.check_field_bool(check_value)
            elif column_lemon_type == FieldType.DATETIME:
                if check_value is not None:
                    check_value = int(check_value)
                    self.check_field_datetime(check_value)
            elif column_lemon_type == FieldType.FILE:
                if self.control_type == ComponentType.CUSTOM_INPUT:
                    pass  # TODO 自定义输入组件检查文件数(前端有检查) self.check_filed_file(check_value)
                else:
                    self.validate_result.update({"code": -1, "message": "自定义输入组件不支持的字段类型"})
            else:
                self.validate_result.update(
                    {"code": -1, "message": "自定义输入组件不支持的字段类型"})
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result


    '''
    @description: check(r_select; r_select_popup; r_table r_cascade; r_tree)
    @Date: 2021-05-14 10:11:48
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def r_select_check_field(self, check_value):
        # r_select; r_select_popup; r_tile; r_cascade; r_table; r_tree
        # 树形下拉 在表单中，只能选中最后一级
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        await self.check_field_relation(check_value)
        return self.validate_result

    '''
    @description: check(slider)
    @Date: 2021-05-14 10:21:35
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def slider_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        try:
            check_value = int(check_value)
            control_min = int(self.control_dict.get("min", -2147483648))
            control_max = int(self.control_dict.get("max", 2147483647))
            if check_value < control_min or check_value > control_max:
                self.validate_result.update(
                    {"code": -1, "message": "超出字段范围限制"})
            else:
                self.check_field_int(check_value)
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result

    '''
    @description: check(upload_file, upload_image)
    @Date: 2021-05-14 10:23:13
    @LastEditors: lv
    @param {*} self
    @param {*} check_value
    '''
    async def upload_check_field(self, check_value):
        self.check_is_require(check_value)
        if self.validate_result.get("code") != 0:
            return self.validate_result
        if isinstance(check_value, list):
            self.check_filed_file(check_value)
        else:
            message = f"输入数据与{self.control_name}控件类型不匹配"
            self.validate_result.update({"code": -1, "message": message})
        await self.validate_field(check_value)
        return self.validate_result


class VariableAccessor(object):

    def __init__(self, globals=True):
        self.globals = globals

    def __get__(self, instance, instance_type=None):
        if instance is not None:
            if instance.lsm:
                if self.globals is True:
                    return instance.lsm.globals
                else:
                    return instance.lsm.locals
        return None


class ComponentABC(abc.ABC):

    base_type = None
    drag_type = False
    component_type = None
    component_name = None
    connector: ClientConnector = None
    data_convert = True  # 是否对 component_dict 进行转换
    # 组件类的值编辑器实例公用； 每个数据容器的实例，会拥有自己的值编辑器实例
    lemon_actor = LemonActor()
    globals = VariableAccessor()
    locals = VariableAccessor(globals=False)

    def __init__(self):
        super().__init__()
        self.is_exiting = False
        self.lemon_actor = LemonActor()
        self.lemon_editor: LemonSMValueEditor = LemonSMValueEditor(self)
        self.init_data()

    def initialize(self):
        pass

    def init_data(self) -> None:
        self.component_dict = dict()
        self.parent_event = None

    def init_connector(self, connector: ClientConnector) -> None:
        self.connector = connector

    def init_component_dict(self, component_dict, data_convert=False) -> None:
        if isinstance(component_dict, dict):
            if data_convert:
                component_dict = ComponentDataAdapter(
                    parent_event=self.parent_event, **component_dict).data_dict
                self.component_dict.update(component_dict)
            else:
                self.component_dict = component_dict
        self.uuid = self.component_dict.get("uuid", "")
        self.name = self.component_dict.get("name", "")


class BaseComponent(ComponentABC):

    def __init__(self):
        super().__init__()
        self._form = None
        self.linkage_container: BaseComponent = None
        self.component_tree: Dict[AnyStr, BaseComponent] = dict()
        self._instances = None
        self.init_data()
        self._lsm: ComponentLemonStateMachine = None
        self._task_registry = {}

    def __repr__(self):
        return "<%s('%s')('%s')@%s>" % (
            type(self).__name__, self.uuid, self.name, id(self))

    def init_data(self):
        super().init_data()
        self._connector: ClientConnector = None
        self._sid = None
        self._root = None
        self._page = None
        self._root_page = None
        self._parent = None
        self._memory_storage = False
        self._all_columns = dict()
        self.outer_form_id = None
        self._outer_form = None
        self.once_editor_list = list()
        self._editor_data = dict()  # 包括自身 与 子组件的值编辑器数据
        self._view_editor_data = dict()  # 仅包括 自身 的值编辑器数据
        self._control_editor_data = dict()  # 仅包括 子组件 的值编辑器数据
        self.dynamic_editor_data = dict()  # 可见性、可编辑性、可用性 等值编辑器数据
        self.custom_editor_dict_list = list()
        self.self_referential_dict = dict()
        self._disabled_value = None
        self._visible_value = None
        self._editable_value = None

    def initialize(self):
        self.limit_path_info = None

    def update_parent_component_tree(self, delete=False):
        # 这个方法，只能 数据容器 和 页面 调用
        # 或者是 拥有 self.machine_id 的组件才能调用
        if delete and self.machine_id in self.parent.component_tree:
            del self.parent.component_tree[self.machine_id]
        elif (
            self.parent
            and
            self.machine_id not in self.parent.component_tree
        ):
            self.parent.component_tree.update({self.machine_id: self})

    async def _entry(self):
        pass

    async def entry(self):
        self.is_exiting = False
        await self._entry()

    def add_task(self, task, name=None):
        if not name:
            name = f"component.task.{hash(task)}"
        self._task_registry[name] = task

    def purge_tasks(self):
        for key, task in self._task_registry.items():
            if task is None:
                continue
            if task.done() or task.cancelled():
                self._task_registry[key] = None

        self._task_registry = {
            k: v for k, v in self._task_registry.items() if v is not None
        }

    def shutdown_tasks(self):
        for _, task in self._task_registry.items():
            if task is not None:
                task.cancel()

        self.purge_tasks()

    async def _exit(self):
        self.shutdown_tasks()
        # if self.connector is not None:
        #     self.connector = None

    async def exit(self):
        # if self.lsm is not None:
        #     await self.lsm.stop()
        # self.component_dict = dict()
        # self.lemon_editor = None
        if not self.is_exiting:
            self.is_exiting = True
            child_machine_id_list = list(self.component_tree.keys())
            for child_machine_id in child_machine_id_list:
                child = self.component_tree[child_machine_id]
                variable_dict = child.build_exit_data()
                await child.exit()
                await self.handle_exit(child_machine_id, variable_dict)
            await self._exit()
        # self.init_data()

    def build_exit_data(self, variable_dict=None):
        if not isinstance(variable_dict, dict):
            variable_dict = dict()
        return variable_dict

    async def handle_exit(self, child_machine_id, variable_dict):
        # 这里是 父状态机 收到了 子状态机 发来的，子状态机 停止的消息
        # if child_machine_id in self.component_tree:
        #     del self.component_tree[child_machine_id]
        pass

    def set_parent(self, value):
        self._parent = weakref.ref(value)
        if isinstance(value, RootComponent):
            self._root = weakref.ref(value)
        else:
            self._root = weakref.ref(value.root)
            self._page = weakref.ref(value.page)
            self._root_page = weakref.ref(value.root_page)

    def set_lsm(self, value):
        self._lsm = value
        if value is not None:
            if getattr(value, "sid", None) is not None:
                self.lemon_actor.sid = value.sid
            value.lemon.system.context_trigger_obj = weakref.WeakMethod(
                self.get_context_trigger_obj)

    @property
    def root(self):
        return None if self._root is None else self._root()

    @property
    def page(self):
        return None if self._page is None else self._page()

    @property
    def root_page(self):
        return None if self._root_page is None else self._root_page()

    @property
    def parent(self):
        return None if self._parent is None else self._parent()

    @property
    def form(self):
        return self._form

    @form.setter
    def form(self, value):
        self._form = value

    @property
    def outer_form(self):
        if self.parent is None:
            self._outer_form = None
        elif self.parent.component_type == ComponentType.FORM:
            self._outer_form = self.parent
        else:
            self._outer_form = self.parent.outer_form
        return self._outer_form

    @property
    def connector(self) -> ClientConnector:
        return None if self._connector is None else self._connector()

    @connector.setter
    def connector(self, value: ClientConnector):
        if value is None:
            self._connector = None
            self._sid = None
        else:
            self._connector = weakref.ref(value)
            self._sid = value.sid

    @property
    def sid(self):
        return self._sid

    @property
    def all_columns(self):
        return self._all_columns

    @property
    def lsm(self) -> ComponentLemonStateMachine:
        return self._lsm

    @lsm.setter
    def lsm(self, value):
        self.set_lsm(value)

    @property
    def memory_storage(self):
        if self.linkage_container is None:
            if self.parent is self.page:
                return self.parent.memory_storage
            else:
                return self._memory_storage
        return self.linkage_container.memory_storage

    def get_forms(self):
        return []

    def get_obj(self):
        return None

    @property
    def selected_objs(self):
        return []

    def get_context_trigger_obj(self):
        if self.page:
            return self.page.get_context_trigger_obj()
        return None

    def form_get_dynamic_editor_data(self, form, meaning):
        return form.dynamic_editor_data.get(self.uuid, {}).get(meaning)

    def form_update_dynamic_editor_data(self, form, meaning, editor_value):
        editor_data = form.dynamic_editor_data.setdefault(self.uuid, {})
        editor_data.update({meaning: editor_value})

    def update_dynamic_editor_data(self, meaning, editor_value):
        if self.form:
            self.form_update_dynamic_editor_data(self.form, meaning, editor_value)
        else:
            setattr(self, "_" + meaning + "_value", editor_value)

    def get_dynamic_editor_data(self, meaning):
        if self.form:
            return self.form_get_dynamic_editor_data(self.form, meaning)
        return getattr(self, "_" + meaning + "_value", None)

    @property
    def control_uuid(self):
        return self.uuid

    @property
    def instances(self):
        return self.root._instances

    @property
    def editable_value(self):
        return self.get_dynamic_editor_data(ValueEditorMeaning.EDITABLE)

    @editable_value.setter
    def editable_value(self, value):
        self.update_dynamic_editor_data(ValueEditorMeaning.EDITABLE, value)

    @property
    def visible_value(self):
        return self.get_dynamic_editor_data(ValueEditorMeaning.VISIBLE)

    @visible_value.setter
    def visible_value(self, value):
        self.update_dynamic_editor_data(ValueEditorMeaning.VISIBLE, value)

    @property
    def disabled_value(self):
        return self.get_dynamic_editor_data(ValueEditorMeaning.DISABLED)

    @disabled_value.setter
    def disabled_value(self, value):
        self.update_dynamic_editor_data(ValueEditorMeaning.DISABLED, value)

    @property
    def editor_data(self):
        if self.parent is None:
            return self._editor_data
        return self.parent.editor_data

    @property
    def view_editor_data(self):
        if self.parent is None:
            return self._view_editor_data
        return self.parent.control_editor_data.setdefault(self.uuid, {})

    @property
    def control_editor_data(self):
        if self.parent is None:
            return self._control_editor_data
        return self.parent.control_editor_data

    async def run_cloud_func(self, func_name):
        func = getattr(self, func_name, None)
        if callable(func) and asyncio.iscoroutinefunction(func):
            return await func(self.lsm.sm)
        return None

    async def set_lsm_variable_value(self, key, value, set_value=None):
        if set_value is None:
            # set_value 不会触发变量更新通知
            set_value = True if self.nested and self.lsm.forward else False
        variable = self.lsm.global_variable_name_dict.get(key)
        if set_value:
            variable.set_value(value)
        else:
            await variable.set_value_async(value)

    @decorator_helper.cloud_func
    async def send_event(self, event_uuid, event_args, publish=False):
        event_topic = self.lsm.build_event_topic(event_uuid)
        event_msg = LemonMessage(
            data={"event_args": event_args}, topic=event_topic)
        app_log.info(f"send event: event_topic {event_topic}")
        if publish:
            await self.connector.publish(event_topic, event_msg)
        else:
            await self.connector.call(event_msg)

    @decorator_helper.cloud_func
    async def put_callback(self, func):
        await self.connector.run_callbacks.put(func)

    async def refresh(self, *args, **kwargs):
        raise NotImplementedError()

    async def refresh_only(self, *args, **kwargs):
        raise NotImplementedError()

    async def refresh_result(self, *args, **kwargs):
        raise NotImplementedError()

    async def raise_rollback(self):
        raise LemonRollbackError()

    def add_column(self, column, path, uuid=None):
        path_columns = dict()
        if column:
            uuid = uuid or column.column_name
            path_columns = {
                uuid: {"column": column, "path": path, "path_list": [path]}}
            self.update_all_column_from_path_columns(self._all_columns, path_columns)
        return path_columns

    @calc_permission_action
    async def _calc_editor(
            self, editor_dict, default=None, extend_value=None, extend_op=None,
            update_editor=True, meaning=None, to_str=True, update_dynamic_editor=False,
            calc_data=False, use_cache=True, **control_value):
        data = None
        if isinstance(editor_dict, dict) and editor_dict:
            editor_uuid = editor_dict.get("uuid")
            if not editor_uuid:
                return data
            editor_type = editor_dict.get("type")
            dynamic_editor = meaning in [
                ValueEditorMeaning.EDITABLE, ValueEditorMeaning.VISIBLE,
                ValueEditorMeaning.DISABLED
            ]
            static_editor_type = editor_type in [
                ValueEditorType.STRING, ValueEditorType.CONST
            ]
            nested_forms = LemonContextVar.nested_forms.get()
            if nested_forms:
                self_is_form = True if isinstance(self, type(nested_forms[0])) else False
            else:
                self_is_form = False
            if dynamic_editor or not static_editor_type:
                use_cache, calc_data = False, True
            if calc_data:
                # 大部分的非可编辑、非可见、非可用 并且是 常量、符号常量 的值编辑器
                # 不需要计算数据，直接返回 None 即可
                source_value = None
                value_editor = self.lemon_editor.init(editor_dict)
                if static_editor_type:
                    source_value = value_editor.value
                data = await self.lemon_editor.get_value(
                    value_editor, default=default, extend_value=extend_value,
                    extend_op=extend_op, to_str=to_str, **control_value)
                if dynamic_editor and update_dynamic_editor:
                    if nested_forms:
                        for index, form in enumerate(nested_forms):
                            d = data[index]
                            self.form_update_dynamic_editor_data(form, meaning, d)
                    else:
                        self.update_dynamic_editor_data(meaning, data)
                if static_editor_type:
                    if nested_forms:
                        for index, form in enumerate(nested_forms):
                            d = data[index]
                            if not dynamic_editor and source_value == d:
                                update_editor = False
                            elif source_value != d:
                                view_editor_data = form.control_editor_data.setdefault(self.uuid, {})
                                view_editor_data.update({editor_uuid: d})
                            elif source_value == d:
                                self.form_clean_editor_data(
                                    form, editor_uuid, self_is_form=self_is_form)
                    else:
                        if not dynamic_editor and source_value == data:
                            update_editor = False
                        elif source_value != data:
                            self.view_editor_data.update({editor_uuid: data})
                        elif source_value == data:
                            self.clean_editor_data(editor_uuid)
            update_view = False
            if update_editor and not use_cache:
                if not static_editor_type:
                    update_view = True
                if nested_forms:
                    for index, form in enumerate(nested_forms):
                        d = data[index]
                        self.form_update_editor_data(
                            form, editor_uuid, d, self_is_form=self_is_form, update_view=update_view)
                else:
                    self.update_editor_data(
                        editor_uuid, data, update_view=update_view)
        elif extend_value is not None:
            data = extend_value
        return data

    def update_editor_data(
            self, editor_uuid, editor_value, update_view=True,
            editor_data=None, view_editor_data=None):
        editor_data = editor_data if editor_data is not None else self.editor_data
        editor_data.update({editor_uuid: editor_value})
        if update_view:
            view_editor_data = view_editor_data if view_editor_data is not None else self.view_editor_data
            view_editor_data.update({editor_uuid: editor_value})

    def clean_editor_data(self, editor_uuid, editor_data=None, view_editor_data=None):
        editor_data = editor_data if editor_data is not None else self.editor_data
        view_editor_data = view_editor_data if view_editor_data is not None else self.view_editor_data
        if editor_uuid in editor_data:
            del editor_data[editor_uuid]
        if editor_uuid in view_editor_data:
            del view_editor_data[editor_uuid]

    def form_update_editor_data(self, form, editor_uuid, editor_value, self_is_form=False, update_view=True):
        editor_data = form.editor_data
        if update_view:
            if self_is_form:
                view_editor_data = form.view_editor_data
            else:
                view_editor_data = form.control_editor_data.setdefault(self.uuid, {})
        else:
            view_editor_data = None
        self.update_editor_data(
            editor_uuid, editor_value, update_view=update_view,
            editor_data=editor_data, view_editor_data=view_editor_data)

    def form_clean_editor_data(self, form, editor_uuid, self_is_form=False):
        editor_data = form.editor_data
        if self_is_form:
            view_editor_data = form.view_editor_data
        else:
            view_editor_data = form.control_editor_data.setdefault(self.uuid, {})
        self.clean_editor_data(editor_uuid, editor_data, view_editor_data)

    def get_event(self, index, handle):
        return self.component_dict.get("events", {}).get(handle, [])[index]

    def get_events(self, events_dict: dict = None):
        events = []
        if events_dict is None:
            events_dict = self.component_dict.get("events") or {}
            if self.component_type == ComponentType.EXECL_TABLE:
                events_dict = {}
        for _, events_ in events_dict.items():
            events.extend(events_)
        return events

    async def run_events(self, events, event_type, when=EventWhen.BEFORE, model=None, need_throw_exception=True):
        trigger_name = str(self.page.name) if self.component_type == ComponentType.PAGE \
            else "/".join([str(self.page.name), str(self.name)])
        trigger_context = func_proto.ComponentEventTrigger(event=func_proto.ComponentEvent(
            component=func_proto.BaseRuntimeComponent(
                raw_component_accessor=weakref.ref(self), name=self.name, component_type=self.component_type, page_name=self.page.name
            ),
            event_type=event_type, when=when
        ))
        with func_trigger_context(trigger_context):
            for event_data in events:
                event_func, data_as_param, error_tip = event_data
                start_time = time.time()
                args = (model,) if data_as_param == "0" else ()  # 前端用的就是 "0" 表示发送
                func_wrapper = await FuncRun.get_func(event_func)
                stdout, result = await FuncRun.run(func_wrapper, *args, need_stdout=True)
                raise_stdout = stdout if need_throw_exception else ""
                # 与前端约定云函数出错时 message 为空字符串时不弹窗
                if isinstance(result, FuncRunError) or result is False:
                    if isinstance(error_tip, dict) and error_tip:
                        eval_globals = {"obj": model}
                        message = await lemon_entity.async_eval_expr(
                            error_tip, globals=eval_globals, obj=model)
                    else:
                        message = ""
                    await engine.elastic_client.form_event_log(
                        stdout, func_wrapper, event_type, start_time, success=0, component=trigger_name)
                    if result is False:
                        raise FuncExecError(-1, message, raise_stdout)
                    if isinstance(result, FuncRunError) and result.rollback:
                        raise FuncExecError(-1, message, raise_stdout)
                else:
                    await engine.elastic_client.form_event_log(
                        stdout, func_wrapper, event_type, start_time, success=1, component=trigger_name)

    async def _run_form_print(
            self, print_uuid, pk_list, print_type, print_content=None,
            **kwargs):
        if self.parent:
            return await self.parent._run_form_print(
                print_uuid, pk_list, print_type, print_content, **kwargs)
        raise NotImplementedError()

    async def _run_form_label(self, label_uuid, pk_list, print_type, print_content=None, **kwargs):
        if self.parent:
            return await self.parent._run_form_label(label_uuid, pk_list, print_type, print_content, **kwargs)
        raise NotImplementedError()

    async def _run_form_export(self, template_uuid, pk_list, **kwargs):
        if self.parent:
            return await self.parent._run_form_export(
                template_uuid, pk_list, **kwargs)
        raise NotImplementedError()

    async def _run_form_import(self, template_uuid, data_url, **kwargs):
        if self.parent:
            return await self.parent._run_form_import(
                template_uuid, data_url, **kwargs)
        raise NotImplementedError()

    async def _calc_click_event_action(
            self, action, event, control_value):
        if action == EventAction.OPEN:
            page_props_dict = event.get("page_props")
            await self._calc_editor(page_props_dict, **control_value)
            page_title_dict = event.get("page_title")
            await self._calc_editor(page_title_dict, **control_value)
        elif action == EventAction.LINK:
            link_addr_dict = event.get("link_addr")
            await self._calc_editor(link_addr_dict, **control_value)
            page_props_dict = event.get("page_props")
            await self._calc_editor(page_props_dict, **control_value)

    async def _calc_event_action(
            self, event, control_value, run_action=False, calc_click=False, **kwargs):
        action = event.get("action")
        if calc_click:
            await self._calc_click_event_action(action, event, control_value)
        if action == EventAction.FORM_PRINT:
            if run_action:
                print_uuid = event.get("page")
                pk_list = safe_pop(kwargs, "pk_list", [])
                print_type = safe_pop(kwargs, "print_type", "pdf")
                if pk_list and isinstance(pk_list, int):
                    pk_list = [pk_list]
                if print_uuid and pk_list:
                    await self._run_form_print(
                        print_uuid, pk_list, print_type, **kwargs)
                else:
                    app_log.error(f"print_uuid: {print_uuid}, pk: {pk_list}")
            else:
                page_props_dict = event.get("page_props")
                await self._calc_editor(page_props_dict, **control_value)
        elif action == EventAction.EXPORT:
            if run_action:
                default_file_name = event.get("default_file_name", dict())
                # need_default_file_name计算默认名称跟导出模板共用一个接口，需要这个值判断是哪种事件
                need_default = kwargs.get("need_default_file_name", False)
                template_uuid = safe_pop(kwargs, "template_uuid", "")
                pk_list = safe_pop(kwargs, "pk_list", [])
                convert_to_pdf = safe_pop(kwargs, "convert_to_pdf", False)
                if pk_list and isinstance(pk_list, int):
                    pk_list = [pk_list]
                if (template_uuid and pk_list) or need_default:
                    await self._run_form_export(
                        template_uuid, pk_list, convert_to_pdf=convert_to_pdf,
                        default_file_name=default_file_name,
                        **kwargs)
                else:
                    app_log.error(f"t_uuid: {template_uuid}, pk: {pk_list}")
        elif action == EventAction.IMPORT:
            if run_action:
                template_uuid = safe_pop(kwargs, "template_uuid", "")
                data_url = safe_pop(kwargs, "data_url", "")
                convert_to_pdf = safe_pop(kwargs, "convert_to_pdf", False)
                if template_uuid and data_url:
                    await self._run_form_import(
                        template_uuid, data_url, **kwargs)
                else:
                    app_log.error(f"t_uuid: {template_uuid}, data_url: {data_url}")
        elif action == EventAction.LABEL_PRINT:
            if run_action:
                label_uuid = event.get("page")
                pk_list = safe_pop(kwargs, "pk_list", [])
                if pk_list and isinstance(pk_list, int):
                    pk_list = [pk_list]
                if label_uuid and pk_list:
                    await self._run_form_label(label_uuid, pk_list, None, **kwargs)
                else:
                    app_log.error(f"label_uuid: {label_uuid}, pk: {pk_list}")
            else:
                page_props_dict = event.get("page_props")
                await self._calc_editor(page_props_dict, **control_value)

    def _collect_event_editor(self, event):
        action = event.get("action")
        editor_dict = dict()
        if action in [EventAction.OPEN]:
            page_props = event.get("page_props")
            if isinstance(page_props, dict) and page_props:
                editor_dict[page_props.get("uuid")] = page_props
            page_title = event.get("page_title", {})
            if event.get("is_override") is True and isinstance(page_title, dict) and page_title:
                editor_dict[page_title.get("uuid")] = page_title
        elif action == EventAction.MESSAGE_BOX:
            modal_content = event.get("modal_content")
            if isinstance(modal_content, dict) and modal_content:
                editor_dict[modal_content.get("uuid")] = modal_content
            button_content = event.get("button_content")
            if isinstance(button_content, dict) and button_content:
                editor_dict[button_content.get("uuid")] = button_content
            modal_title = event.get("modal_title")
            if isinstance(modal_title, dict) and modal_title:
                editor_dict[modal_title.get("uuid")] = modal_title
        elif action == EventAction.LINK:
            link_addr = event.get("link_addr")
            if isinstance(link_addr, dict) and link_addr:
                editor_dict[link_addr.get("uuid")] = link_addr
            page_props = event.get("page_props")
            if isinstance(page_props, dict) and page_props:
                editor_dict[page_props.get("uuid")] = page_props
        return editor_dict

    async def calc_event(
            self, control_value=None, run_func=False, index=None,
            handle="click", kwargs=None, calc_click=False):
        control_value = control_value or {}
        kwargs = kwargs if kwargs is not None else {}
        if index is None:
            events = self.get_events()
        elif self.component_type in [ComponentType.SEARCH_BUTTON, ComponentType.SEARCH_BAR]:
            events = [self.get_event(index, handle, kwargs)]
        else:
            events = [self.get_event(index, handle)]

        for event in events:
            if isinstance(event, dict) and event:
                method = event.get("response_method")
                if run_func and method == EventResponseMethod.FUNC:
                    stdout, result = await self.execute_func(
                        event, control_value, handle=handle, kwargs=kwargs)
                    if isinstance(result, FuncRunError):
                        need_throw_exception = engine.config.get("NEED_THROW_EXCEPTION")
                        stdout = stdout if need_throw_exception else ""
                        return stdout, result
                elif method == EventResponseMethod.ACTION:
                    await self._calc_event_action(
                        event, control_value, run_action=run_func, calc_click=calc_click, **kwargs)
        return "", True

    # 收集需要实时计算的事件相关值编辑器
    def collect_events_editor(self):
        events = self.get_events()
        all_editor_dict = {}
        for event in events:
            if isinstance(event, dict) and event:
                method = event.get("response_method")
                if method == EventResponseMethod.ACTION:
                    editor_dict = self._collect_event_editor(event)
                    all_editor_dict.update(editor_dict)
        return all_editor_dict

    async def _calc_func_arg_value(
            self, func_arg_length, index, func_arg, func_params,
            control_value, **kwargs):
        if index > func_arg_length:
            arg_dict = func_arg.default
        else:
            arg_dict = func_params[index]
        arg_value = await self._calc_editor(
            arg_dict, to_str=False, calc_data=True,
            update_editor=False, **control_value)
        return arg_value

    async def _calc_func_args_by_change(
            self, func_params, func_arg_list, control_value, **kwargs):
        return await self._calc_func_args_by_normal(
            func_params, func_arg_list, control_value, **kwargs)

    async def _calc_func_args_by_normal(
            self, func_params, func_arg_list, control_value, **kwargs):
        arg_value_dict = dict()
        func_arg_length = len(func_params) - 1
        for index, func_arg in enumerate(func_arg_list):
            arg_value = await self._calc_func_arg_value(
                func_arg_length, index, func_arg, func_params, control_value)
            arg_name = func_arg.name
            arg_value_dict.update({arg_name: arg_value})
        return arg_value_dict

    async def _calc_search_item_func_args(
        self, func_params, func_arg_list, control_value, **kwargs):
        # 搜索项没有模型对象
        arg_value_dict = dict()
        func_arg_length = len(func_params) - 1
        search_uuid = kwargs.get("search_uuid")
        search_item = self.get_search_item(search_uuid)
        safe_pop(kwargs, "search_uuid", None)
        safe_pop(kwargs, "control_uuid", None)
        for index, func_arg in enumerate(func_arg_list):
            # index: 0 模型对象  1 旧值  2 新值
            if index == 0:
                arg_value = None
            elif index == 1:
                arg_value = safe_pop(kwargs, "old_value", None)
                arg_value = await self._calc_search_item_value(arg_value, search_item)
            elif index == 2:
                arg_value = safe_pop(kwargs, "new_value", None)
                arg_value = await self._calc_search_item_value(arg_value, search_item)
            else:
                arg_value = await self._calc_func_arg_value(
                    func_arg_length, index, func_arg, func_params,
                    control_value, **kwargs)
            arg_name = func_arg.name
            arg_value_dict.update({arg_name: arg_value})
        return arg_value_dict

    async def _calc_search_item_value(self, arg_value, search_item):
        input_control = search_item.get("input_control")
        field_value_list = search_item.get('field_value_list', [])  # 新版本
        if not field_value_list:
            field_info = search_item.get('fieldsValue', dict())  # 老版本
        else:
            field_info = field_value_list[0]
        if not field_info:
            return arg_value
        field_uuid = field_info.get("field")
        column = lemon_field(field_uuid)
        if not column:
            return arg_value
        model = column.model
        if input_control in ["RELATIONSELECT", "CASCADERSELECT", "TREESELECT"]:
            is_single_choice = search_item.get("is_single_choice", False)
            if arg_value is None:
                return arg_value if is_single_choice else []
            if not isinstance(arg_value, list):
                arg_value = [arg_value]
            if is_single_choice:
                return await engine.userdb.objs.get(model, id=arg_value[0])
            else:
                query = model.select().where(model.id.in_(arg_value))
                return await engine.userdb.objs.execute(query)
        else:
            column_lemon_type = column.lemon_type
            if column_lemon_type == FieldType.INTEGER:
                return int(arg_value) if arg_value not in [None, ""] else 0
            elif column_lemon_type == FieldType.DECIMAL:
                return decimal.Decimal(arg_value) if arg_value not in [None, ""] else 0
            elif column_lemon_type == FieldType.STRING:
                return str(arg_value) if arg_value is not None else arg_value
            else:
                return arg_value

    async def calc_func_args(
            self, func_params, func_arg_list, control_value, handle, **kwargs):
        if handle in [EventActionName.CHANGE, EventActionName.BLUR]:
            if self.component_type in [ComponentType.SEARCH_BUTTON, ComponentType.SEARCH_BAR] \
                    and self.uuid != kwargs.get("search_uuid"):
                #搜索项
                arg_value_dict = await self._calc_search_item_func_args(
                    func_params, func_arg_list, control_value, **kwargs)
            else:
                arg_value_dict = await self._calc_func_args_by_change(
                    func_params, func_arg_list, control_value, **kwargs)
        else:
            arg_value_dict = await self._calc_func_args_by_normal(
                func_params, func_arg_list, control_value)
        return arg_value_dict

    async def execute_func(
            self, event, control_value, handle="click", kwargs=None):
        kwargs = kwargs if kwargs is not None else {}
        start_time = time.time()
        component_name = self.component_name or ""
        trigger_name = f"{component_name} 云函数"
        trigger_component = "/".join([str(self.page.name), str(self.name)])
        try:
            lsm = self.lsm
            func_uuid = event.get("func")
            func_params = event.get("params", list())
            func_async = event.get("call_method", 0)
            func_wrapper = await FuncRun.get_func(func_uuid)
            app_log.info(f"func_wrapper: {func_wrapper}")
            arg_value_dict = dict()
            func_arg_list = func_wrapper.func.func_arg_list
            arg_value_dict = await self.calc_func_args(
                func_params, func_arg_list, control_value, handle, **kwargs)
            app_log.info(f"func_args: {arg_value_dict}")
            stdout, result = "", None
            if func_async:
                asyncio.create_task(FuncRun.run(
                    func_wrapper, lsm=lsm, **arg_value_dict))
            else:
                stdout, result = await FuncRun.run(
                    func_wrapper, lsm=lsm, need_stdout=True, **arg_value_dict)
            if handle in [EventActionName.CHANGE, EventActionName.BLUR]:
                func_return_list = func_wrapper.func.func_return_list
                if func_return_list:
                    first_func_return = func_return_list[0]
                    return_name = first_func_return.name
                    new_value = result.get(return_name)
                elif isinstance(result, dict) or isinstance(result, FuncRunError):
                    last_func_arg = func_arg_list[-1]
                    new_value = arg_value_dict.get(last_func_arg.name)
                else:
                    new_value = result
                kwargs.update({"new_value": new_value})
        except BaseException:
            error_location = self.get_error_location()
            stdout = traceback.format_exc()
            app_log.error(stdout)
            await runtime_log.error(stdout)
            if func_wrapper:
                func_name = func_wrapper.func_name
            else:
                func_name = "未知云函数"
            func_message = f"出错的云函数：{func_name}"
            func_error = FuncRunError("事件函数执行异常", func_name)
            await engine.elastic_client.container_event_log(
                trigger_name, stdout, func_wrapper, handle, start_time, success=0,
                component=trigger_component)
            return "\n".join([error_location, func_message, stdout]), func_error
        else:
            if isinstance(result, FuncRunError):
                stdout_after_regex = parse_error_message(result)
                if stdout_after_regex:
                    if stdout_after_regex[0] == FormatErrorType.COLUMN_NOT_NULL:
                        stdout = self.column_not_null(stdout_after_regex[1:], stdout)
                    elif stdout_after_regex[0] == FormatErrorType.FOREIGN_KEY_CONSTRAINT:
                        stdout = self.format_foreign_key_constraint_error(stdout_after_regex[1:])
                # BackGroundError 不需要弹窗
                if stdout:
                    stdout = self.get_error_location() + stdout
                await engine.elastic_client.container_event_log(
                    trigger_name, stdout, func_wrapper, handle, start_time,
                    success=0, component=trigger_component)
                return stdout, result
            else:
                await engine.elastic_client.container_event_log(
                    trigger_name, stdout, func_wrapper, handle, start_time,
                    success=1, component=trigger_component)
        return stdout, result

    def format_foreign_key_constraint_error(self, error_info):
        # 多对多 model_node 直接是中间表，非多对多 model_node 是有外键那一端
        model_node = lemon_model_node(error_info[1])
        foreign_key_constraint_name = error_info[2]
        foreign_key_name = ""
        model_name = model_node.model_name
        # 因为报错里给的外键字段 uuid 会不全，所以需要通过外键约束判断
        for node in model_node.relation_nodes:
            field_uuid = node.r_uuid
            field = lemon_field(field_uuid)
            if field is None:
                continue
            name = 'fk_%s_%s_refs_%s' % (field.model._meta.table_name, field.column_name, field.rel_model._meta.table_name)
            name = peewee._truncate_constraint_name(name)
            if foreign_key_constraint_name == name:
                foreign_key_name = field.name
                break
        if foreign_key_name:
            stdout = f"外键约束报错：\n外键约束名：{foreign_key_constraint_name}\n模型名称：{model_name}\n外键字段：{foreign_key_name}"
        else:
            stdout = f"外键约束报错：\n外键约束名：{foreign_key_constraint_name}\n中间表：{model_name}"
        return stdout

    def column_not_null(self, error_info, stdout):
        field_uuid = error_info[0]
        field = lemon_field(field_uuid)
        model_name = field.model._meta.model_name
        return stdout.replace(field_uuid, f"{model_name}.{field.name}")

    async def calc_editor_data(
            self, control_value=None, settings_value=None, **kwargs):
        control_value = control_value or {}
        try:
            for editor_func_name in self.once_editor_list:
                editor_func = getattr(self, editor_func_name, None)
                await editor_func(
                    control_value, settings_value=settings_value, **kwargs)
            for editor_dict in self.custom_editor_dict_list:
                await self._calc_editor(editor_dict, **control_value)
        except ExprExecError as e:
            e.stdout = self.get_error_location()
            raise e

    def collect_editor_dict(self):
        modal_page_title = self.component_dict.get("modal_view_page", {}).get("page_title", {})
        view_page_title = self.component_dict.get("view_page", {}).get("page_title", {})
        event_editor_dict = self.collect_events_editor()
        if modal_page_title.get("uuid"):
            event_editor_dict.update({modal_page_title.get("uuid"): modal_page_title})
        if view_page_title.get("uuid"):
            event_editor_dict.update({view_page_title.get("uuid"): view_page_title})
        return event_editor_dict

    def get_editor_control(self, control_uuid):
        """获取计算值编辑器事件的control"""
        control_obj = self.data_controls.get(control_uuid)
        control_obj = control_obj or self.container_tree.get(control_uuid)
        return control_obj

    async def editor_result(self, msg):
        result_dict = {}
        data = msg.data
        control_editor_dict = data.get("event_args")
        return_code = Code.RUNTIME_OK
        editor_dict = control_editor_dict.get("control_editor", {})
        for control_uuid, editor_list in editor_dict.items():
            control_obj = self.get_editor_control(control_uuid)
            if control_obj:
                control_obj.find_all_columns()
                result = await control_obj.calcu_editor_list(editor_list)
            else:
                result = await self.calcu_editor_list(editor_list)
            # result_dict[control_uuid] = result
            result_dict.update(result)
        request_uuid = control_editor_dict.get("request_uuid")
        result = {"result": result_dict}
        # 多次请求,前端以此判断是哪次请求
        if request_uuid:
            result.update({"request_uuid": request_uuid})
        stdout, stderr = "", ""
        message = LDRC(return_code, data={
            "stdout": stdout, "stderr": stderr, "result": result_dict,
            "command": msg.command, "request_id": msg.request_id,
            "component": self.machine_id})
        # await self.lsm.connector.publish(self.lsm.result_topic, message)
        await self.lsm.connector.call(message, topic=self.lsm.result_topic)

    # 允许前端通过传递值编辑器的uuid来获取值编辑器的结果
    async def calcu_editor_list(self, editor_list, *args, **kwargs):
        result = dict()
        all_editor = self.collect_editor_dict()
        for editor_uuid in editor_list:
            editor_setting = all_editor.get(editor_uuid)
            if editor_setting:
                control_value = {}
                form = self.form or self.outer_form
                if form is not None:
                    control_value = form.source_in_page
                editor_value = await self.lemon_editor(
                    editor_setting, **control_value)
                result[editor_uuid] = editor_value
        return result

    def create_editor(self, editor_dict) -> BaseValueEditor:
        # 若 editor_dict 为空或为None 时， 当前会返回 None
        return self.lemon_editor.init(editor_dict)

    def find_decorator_func(self):
        last_editor_list = list()
        for name in dir(self):
            if name.startswith("_"):
                continue
            obj = getattr(self, name, None)
            if callable(obj):
                if getattr(obj, "last_editor", None):
                    last_editor_list.append(name)
                elif getattr(obj, "once_editor", None):
                    self.once_editor_list.append(name)
        self.once_editor_list.extend(last_editor_list)

    def copy_reuse_attrs(self, component):
        # 优化：复用属性数据
        self.once_editor_list = component.once_editor_list

    def copy_initialize_reuse_attrs(self, component):
        pass

    def update_all_column_from_path_columns(self, all_columns, path_columns):
        for field_uuid, field_info in path_columns.items():
            field_path_list = field_info.get("path_list")
            if field_uuid in all_columns:
                all_columns_field_info = all_columns[field_uuid]
                all_columns_field_path_list = all_columns_field_info.get(
                    "path_list", [])
                for field_path in field_path_list:
                    if field_path not in all_columns_field_path_list:
                        all_columns_field_path_list.append(field_path)
            else:
                all_columns.update({field_uuid: field_info})

    def _find_column(self, field_info, return_column=True):
        lemon_column, field_path, path_columns = None, [], dict()
        if isinstance(field_info, dict) and field_info:
            field_uuid = field_info.get("uuid", None)
            if field_uuid:
                field_path = field_info.get("path", [])
                lemon_column = lemon_field(field_uuid)
                if lemon_column.is_system:
                    rename_field_uuid = get_sys_field_uuid(
                        lemon_column.model._meta.table_name,
                        lemon_column.column_name)
                    path_columns = self.add_column(
                        lemon_column, field_path, rename_field_uuid)
                else:
                    path_columns = self.add_column(lemon_column, field_path)
        if return_column is True:
            return lemon_column, field_path
        else:
            return lemon_column, path_columns

    def _find_editor_columns(self, editor_obj):
        path_columns = dict()
        if isinstance(editor_obj, BaseValueEditor):
            editor = editor_obj
        else:
            editor = self.create_editor(editor_obj)
        if editor:
            path_columns = editor.path_columns
            self.update_all_column_from_path_columns(self._all_columns, path_columns)
        return path_columns

    def _find_normal_editor_columns(
            self, part_dict, first_key, second_key="editor"):
        first_part_dict = part_dict.get(first_key, {})
        editor_dict = first_part_dict.get(second_key, {})
        return self._find_editor_columns(editor_dict)

    def _find_event_columns(self, event):
        event_columns = dict()
        action = event.get("action")
        if action == EventAction.OPEN:
            page_props_dict = event.get("page_props")
            path_columns = self._find_editor_columns(page_props_dict)
            event_columns.update(path_columns)
            page_title_dict = event.get("page_title")
            path_columns = self._find_editor_columns(page_title_dict)
            event_columns.update(path_columns)
        elif action == EventAction.LINK:
            link_addr_dict = event.get("link_addr")
            path_columns = self._find_editor_columns(link_addr_dict)
            event_columns.update(path_columns)
            page_props_dict = event.get("page_props")
            path_columns = self._find_editor_columns(page_props_dict)
            event_columns.update(path_columns)
        elif action == EventAction.EXPORT:
            default_filename_dict = event.get("default_file_name")
            path_columns = self._find_editor_columns(default_filename_dict)
            event_columns.update(path_columns)
        return event_columns

    def find_event_columns(self, events=None):
        if events is None:
            events = self.get_events()
        event_columns = dict()
        for event in events:
            if isinstance(event, dict) and event:
                method = event.get("response_method")
                if method == EventResponseMethod.ACTION:
                    event_columns_part = self._find_event_columns(event)
                    event_columns.update(event_columns_part)
        return event_columns

    def find_custom_columns(self):
        custom = self.component_dict.get("custom", {})
        attr_bind = custom.get("attr_bind", {})
        for _, bind_info in attr_bind.items():
            component = bind_info.get("component")
            if component == CustomComponentType.VALUE_EDITOR:
                value_editor_dict = bind_info.get("value")
                if value_editor_dict:
                    editor = self.create_editor(value_editor_dict)
                    self._find_editor_columns(editor)
                    self.custom_editor_dict_list.append(value_editor_dict)

    def find_all_columns(self):
        self.find_event_columns()
        self.find_custom_columns()

    def init_self_referential_dict(self):
        pass

    def update_self_referential_dict(
            self, column, field_uuid, path, self_referential_path=None):
        column_path_uuid = build_field_path(path, field_uuid)
        column_path = path or self_referential_path
        self_referential = is_self_referential_field(column, column_path)
        self.self_referential_dict.update({
            column_path_uuid: {
                "control_obj": self,
                "column": column,
                "path": column_path,
                "self_referential": self_referential}})

    def calc_datalist_column_editor_value(self, parent, editor_dict):
        editor_value = None
        parent_control_obj = getattr(parent.parent, "control_obj", None) or parent.parent
        if parent_control_obj and isinstance(editor_dict, dict):
            editor_uuid = editor_dict.get("uuid")
            if editor_uuid:
                parent_value_editor_data = parent.parent.editor_data
                column_editor_value = parent_value_editor_data.get(
                    editor_uuid)
                editor_value = column_editor_value
        return editor_value

    def calc_editor_value_by_parent(
            self, editor_dict, default_key="editable_value"):
        control_obj = getattr(self, "control_obj", None)
        if control_obj:
            editor_value = getattr(control_obj, default_key, None)
        else:
            editor_value = getattr(self, default_key, None)
        if editor_dict:
            value_editor = self.lemon_editor.init(editor_dict)
            editor_uuid = value_editor.uuid
            update_view = self.calc_need_update_view(value_editor, editor_value)
            if not update_view:
                # 这是由于即使认为不需要更新editor_data, 但值也可能已经发生了变化, 需要清除, 让前端使用值编辑器中的值
                # 例如: 值编辑器的值是常量True, 但受parent影响, 值变为False, 在变回True的时候
                # 前面的判断会认为 <变回的True> == <值编辑器的值>, 会让update_view=False
                # 但其实值已经发生了变化, 需要清除
                self.clean_editor_data(editor_uuid)
            self.update_editor_data(
                editor_uuid, editor_value, update_view=update_view)
        return editor_value

    def calc_need_update_view(self, value_editor, source_value):
        update_view = True
        if (
            value_editor.type in [
                ValueEditorType.STRING, ValueEditorType.CONST
            ] and value_editor.value == source_value
        ):
            update_view = False
        return update_view

    @decorator_helper.once_editor
    async def visible(self, control_value=None, settings_value=None, **kwargs):
        ignore_relation_container = kwargs.get("ignore_relation_container")
        sub_container = kwargs.get("sub_container")
        visible_dict = self.component_dict.get("visible", dict())
        is_visible_dict = visible_dict.get("is_visible")
        parent_visible_dict = self.component_dict.get("parent_visible", dict()).get("is_visible", dict())
        control_uuid = self.component_dict.get("uuid", "")
        extend_value, extend_op = None, 1

        if ignore_relation_container and sub_container:
            # 子表单组件,认为自己的可见性在父容器中计算过了,不重新计算
            visible_value = self.calc_editor_value_by_parent(
                is_visible_dict, default_key="visible_value")
            self.visible_value = visible_value
            return self.visible_value
        real_parent = self.parent
        parent_uuid = self.page.component_uuid_tree.get(self.uuid)
        parent = self.page.all_component.get(parent_uuid)
        parent = getattr(parent, "control_obj", parent) or parent
        if (
            real_parent.component_type == ComponentType.FORM
            and
            real_parent.nested
        ):
            # 数据列表的列，支持设置可见性，每个格子需要继承，列设置的可见性
            # 每列的可见性数据，不应该在每行的数据中返回
            # 无需再次计算，直接取 数据列表 上的列可见性即可
            parent_visible_value = self.calc_datalist_column_editor_value(
                real_parent, parent_visible_dict)
        else:
            parent_visible_value = getattr(parent, "visible_value", None)
        if control_value is None:
            control_value = dict()
        # TODO: 这里还需要判断 当前用户角色 ？ 当前设备 ？
        extend_value, extend_op = True, 1
        if parent_visible_value is False:
            extend_value = False
        if extend_value and settings_value and isinstance(settings_value, dict):
            extend_value = settings_value.get(
                control_uuid, {}).get("visible", True)
        # _calc_editor 时，会根据 meaning 自动赋值到 _visible_value
        visible_value = await self._calc_editor(
            is_visible_dict, extend_value=extend_value,
            extend_op=extend_op, meaning=ValueEditorMeaning.VISIBLE,
            update_dynamic_editor=True, **control_value)
        return visible_value

    @decorator_helper.once_editor
    async def editable(self, control_value=None, settings_value=None, **kwargs):
        control_uuid = self.component_dict.get("uuid")
        default, extend_value, extend_op = None, None, 1
        if settings_value and isinstance(settings_value, dict):
            extend_value = settings_value.get(
                control_uuid, {}).get("editable", True)
        ignore_relation_container = kwargs.get("ignore_relation_container")
        sub_container = kwargs.get("sub_container")
        editable_dict = self.component_dict.get("editable")
        if ignore_relation_container and sub_container:
            # 子表单组件,认为自己的可见性在父容器中计算过了,不重新计算
            editable_value = self.calc_editor_value_by_parent(editable_dict)
            self.editable_value = editable_value
            return self.editable_value
        real_parent = self.parent
        parent_uuid = self.page.component_uuid_tree.get(self.uuid)
        parent = self.page.all_component.get(parent_uuid)
        parent = getattr(parent, "control_obj", parent) or parent
        if (
            real_parent.component_type == ComponentType.FORM
            and
            real_parent.nested
            and
            self.component_type not in ComponentType.CONTAINER_LIST
        ):
            # 数据列表的列，支持设置可编辑性，每个格子需要继承，列设置的可编辑性
            # 每列的可编辑性数据，不应该在每行的数据中返回
            # 无需再次计算，直接取 数据列表 上的列可编辑性即可
            parent_editable_value = self.calc_datalist_column_editor_value(
                real_parent, editable_dict)
        else:
            parent_editable_value = getattr(parent, "editable_value", None)
        if control_value is None:
            control_value = dict()
        if parent_editable_value is False:
            extend_value = False
        if (
            real_parent.component_type == ComponentType.FORM and
            real_parent.pk and real_parent.pk < 0
        ):
            resource_id = self.data_model._meta.table_name
            try:
                self.data_model._permission_manager.predicate(resource_id, PermissionAction.INSERT)
            except Exception:
                extend_value = False
        if extend_value and settings_value and isinstance(settings_value, dict):
            extend_value = settings_value.get(
                control_uuid, {}).get("editable", True)
        # _calc_editor 时，会根据 meaning 自动赋值到 _editable_value
        editable_value = await self._calc_editor(
            editable_dict, default=default, extend_value=extend_value,
            extend_op=extend_op, meaning=ValueEditorMeaning.EDITABLE,
            update_dynamic_editor=True, **control_value)
        return editable_value

    @decorator_helper.once_editor
    async def disabled(self, control_value=None, settings_value=None, **kwargs):
        # 可用性需要继承父组件的可用性, 为了保证继承, 认为全部组件都需要计算可用性
        extend_value, extend_op = None, 1
        parent_uuid = self.page.component_uuid_tree.get(self.uuid)
        parent = self.page.all_component.get(parent_uuid)
        parent = getattr(parent, "control_obj", parent) or parent
        parent_disabled_value = getattr(parent, "disabled_value", None)
        if parent_disabled_value is False:
            extend_value = False
        disabled_dict = self.component_dict.get("disabled")
        # _calc_editor 时，会根据 meaning 自动赋值到 _disabled_value
        disabled_value = await self._calc_editor(
            disabled_dict, to_str=False, extend_op=extend_op,
            extend_value=extend_value, meaning=ValueEditorMeaning.DISABLED,
            update_dynamic_editor=True, **control_value)
        return disabled_value

    async def handle_container_figure(
            self, *, control_value=None, settings_value=None,
            figure_dict=None):
        """
        计算父组件的可见性与可编辑性
        优先检查是否已存在配置
        其次使用父组件配置
        最后使用自身配置
        """
        control_value = control_value or {}
        settings_value = settings_value or {}
        real_parent_info = self.component_dict.get("real_parent_info", [])
        parent_machine_id = self.parent_machine_id
        base_setting = {}

        real_parent_info_cp = real_parent_info[:]
        real_parent_info_cp.insert(0, parent_machine_id)
        for k in ["visible", "editable"]:
            # machine_id_ 0是字符串, 之后是对象
            for index, p_machine_id_ in enumerate(real_parent_info_cp):
                # 是否已存在
                if index == 0:
                    p_machine_id = p_machine_id_
                else:
                    p_machine_id = p_machine_id_.uuid
                if figure_dict.get(p_machine_id, {}).get(k) is False:
                    v = False
                    base_setting.update({k: False})
                    break
            else:
                # 计算父组件
                for p_obj in real_parent_info_cp[1:]:
                    parent_f_ = getattr(p_obj, k, None)
                    parent_v = await parent_f_()
                    if parent_v is False:
                        figure_dict.update({p_obj.uuid: {k: False}})
                        base_setting.update({k: False})
                        break
                else:
                    # 计算自身
                    f_ = getattr(self, k, None)
                    v = await f_(
                        control_value=control_value,
                        settings_value=settings_value)
                    if v is False:
                        base_setting.update({k: False})
        figure_dict.update({self.machine_id: base_setting})
        # app_log.info(figure_dict)
        return base_setting

    async def column_visible(
            self, editor_dict, control_obj, permission_config=None, **control_value):
        default, extend_value, extend_op = None, None, None
        if control_obj:
            control_extend_value = control_obj.visible_value
            if control_extend_value in [True, False]:
                extend_value = control_extend_value
                extend_op = 1
        await self._calc_editor(
            editor_dict, default=default,
            extend_value=extend_value, extend_op=extend_op,
            meaning=ValueEditorMeaning.VISIBLE,
            permission_config=permission_config,
            **control_value)

    async def column_editable(
            self, editor_dict, edit_field_info, control_obj, permission_config=None,
            **control_value):
        edit_column = edit_field_info.get("column")
        path = edit_field_info.get("path", list())
        column_edit_permission = await calc_edit_column_permission(self, edit_column, path)
        default, extend_value, extend_op = None, None, None
        if column_edit_permission is False:  # editable
            default = False
        if control_obj:
            control_extend_value = control_obj.editable_value
            if control_extend_value in [True, False]:
                extend_value = control_extend_value
                extend_op = 1
        await self._calc_editor(
            editor_dict, default=default,
            extend_value=extend_value, extend_op=extend_op,
            meaning=ValueEditorMeaning.EDITABLE,
            permission_config=permission_config,
            **control_value)

    async def _calc_badge(self, control_value, badge: dict):
        # 徽标数的值编辑器计算
        data_source = None
        color = None
        if badge:
            data_source = badge.get("data_source", {})
            if data_source:
                data_source = await self._calc_editor(data_source, **control_value)
            color = badge.get("color", {})
            if color:
                color = await self._calc_editor(color, **control_value)
        return data_source, color

    async def send_print_message(
            self, print_control_uuid, pdf_url=None, print_type="pdf",
            orientation="portrait", return_code=None, need_prefix=True,
            page_size="", wps_dir="", **kwargs):
        if return_code is None:
            return_code = Code.RUNTIME_OK if pdf_url else LREC.PRINT_ERROR
        pdf_url = [] if pdf_url is None else pdf_url
        data = dict()
        data.update(kwargs)
        data.update({
                "component": print_control_uuid, "url": pdf_url, "need_prefix": need_prefix,
                "print_type": print_type, "orientation": orientation,
                "page_size": page_size, "wps_dir": wps_dir
        })
        if printer_name := kwargs.get("printer_name"):
            data.update({"printer_name": printer_name})
        message = LemonDictResponsePrint(
            return_code, data=data)
        app_log.info(f"print_message: {message}")
        await self.connector.call(message, topic=self.lsm.result_topic)

    async def get_pdf_url(self, json_body, post_url, print_type):
        async with aiohttp.ClientSession() as session:
            json_body_dumps = ujson.dumps(json_body)
            json_body = ujson.loads(json_body_dumps)
            app_log.info(f"json_body: {json_body_dumps[:200]}")
            request = await session.post(post_url, json=json_body)
            result = await request.json()
        app_log.info(f"json_result: {result}")
        print_code = result.get("code", 200)
        return_code = None
        if print_code == 401:
            return_code = LREC.BARCODE_GENERATION_ERROR
        pdf_path = result.get("data")
        pdf_url = []
        print_type_list = ['pdf', 'bmp', 'png', 'jpg', 'cpcl', "pdf2img", "CPCL", "ZPL", "ZICOX_CPCL", "PDF", "TSPL"]
        pdf_gen_url = "static/runtime/pdf/"
        if print_type in print_type_list and pdf_path:
            if isinstance(pdf_path, str):
                pdf_path = [pdf_path]
            for path in pdf_path:
                this_path = path.replace(
                    "/var/www/html/static/runtime/pdf/", "")
                this_url = pdf_gen_url + this_path
                pdf_url.append(this_url)
        return pdf_url, return_code

    def get_error_location(self):
        parent = self.lsm.parent
        error_location = ""
        if parent:
            if parent.component.component_type in [ComponentType.DATALIST, ComponentType.DATAGRID]:
                parent_control_obj = parent.component
                column_title_data = parent_control_obj.column_title_data
                title_name = column_title_data.get(self.control.get("uuid"), "")
                error_location = f"页面名：{self.page.name}，容器名：{parent_control_obj.name}，列名：{title_name}\n"
            elif parent.component.component_type == ComponentType.PAGE and self.parent.component_type == ComponentType.FORM:
                # 关联子表
                component_name = getattr(self, "name", "")
                error_location = f"页面名：{self.page.name}，容器名：{self.parent.name}，组件名：{component_name}\n"
        return error_location


class RootComponent(BaseComponent):

    component_type = ComponentType.ROOT

    def __init__(self):
        super().__init__()
        self.p_context = dict()
        self.root_machine_id = None
        self.parent_machine_id = None

    def init_connector(self, connector: ClientConnector) -> None:
        super().init_connector(connector)
        if connector:
            self.machine_id = self.connector.sid
        else:
            self.machine_id = lemon_uuid()

    @property
    def memory_storage(self):
        return False

    @property
    def root(self):
        return self


class BaseDataComponent(BaseComponent):

    lsm_class = ComponentLemonStateMachine
    state_machine_dict = dict()

    def __init__(self):
        super().__init__()
        self.p_context = dict()
        self.subscribe_models = list()
        self.controls = OrderedDict()
        self.control_tree: Dict[AnyStr, BaseComponent] = dict()
        self.container_tree: Dict[AnyStr, BaseComponent] = dict()

    def init_connector(self, connector: ClientConnector) -> None:
        super().init_connector(connector)

    def init_component_dict(self, component_dict, data_convert=False) -> None:
        super().init_component_dict(component_dict, data_convert)
        controller = self.component_dict.get("controller", {})
        self.state_machine = controller.get("state_machine")

    def init_p_context(self, p_context):
        if isinstance(p_context, dict):
            self.p_context.update(p_context)
        controller = self.component_dict.get("controller", {})
        watch_variables = controller.get("watch_variables", [])
        self.p_context.update({"watch_variables": watch_variables})
        # 通过p_context 赋值数据列表状态机的 component 状态机变量
        self.p_context.update({"component": weakref.proxy(self)})

    def init_root_form_data(
            self, root_form_uuid=None, form_nested=False, depth=0,
            memory_storage=False):
        self.root_form_uuid = root_form_uuid  # 最外层表单的 machine_id
        self.form_nested = form_nested  # 是否嵌在最外层表单内
        self.depth = depth  # 嵌在最外层表单内的深度
        self._memory_storage = memory_storage

    def init_model_data(self, model_uuid=None):
        if model_uuid is None:
            data_source = self.component_dict.get("data_source", {})
            self.model_uuid = data_source.get("model")
        else:
            self.model_uuid = model_uuid
        self.model_uuid = rename_system_model_uuid(self.model_uuid)
        self.data_model = lemon_model(self.model_uuid)
        self.model_name = self.data_model._meta.model_name if self.data_model else None

    def init_machine_id(self, nested=False, machine_id=None):
        self.nested = nested  # 是否为数据、卡片列表的某行
        if machine_id:
            self.machine_id = machine_id
        else:
            self.machine_id = lemon_uuid() if self.nested else self.uuid
        self.connector.add_page_container(self)
        if self.page:
            self.page.containers.update({self.name: self})

    def copy_reuse_attrs(self, component):
        super().copy_reuse_attrs(component)
        # 优化：批量创建表单时，复用模型数据
        self.model_uuid = component.model_uuid
        self.data_model = component.data_model
        self.model_name = component.model_name

    def copy_initialize_reuse_attrs(self, component):
        super().copy_initialize_reuse_attrs(component)
        # 优化：批量创建表单时，复用字段数据
        self._all_columns = component._all_columns

    @property
    def pk(self):
        return self._pk

    @pk.setter
    def pk(self, value):
        self._pk = value

    def get_result(self, *args, **kwargs):
        raise NotImplementedError()

    async def result(self, *args, **kwargs):
        raise NotImplementedError()

    async def refresh_only(self, *args, **kwargs):
        pass

    async def refresh_result(self, call_later=False, *args, **kwargs):
        pass

    async def get_filter_data(self, *args, **kwargs):
        pass

    def update_p_context(self, p_context, key_list=None):
        if key_list is None:
            key_list = [
                "pk", "parent_pk", "control_settings", "submit_row_list"
            ]
        for key, value in p_context.items():
            if key in key_list and key in self.globals:
                setattr(self.globals, key, value)
                self.p_context.update({key: value})

    async def _entry(self):
        # 切换租户后，sid 不变，但需要更新租户数据
        self.lsm.lemon.system.update_user(self.connector.current_user)
        self.update_parent_component_tree()
        await self.lsm.start()
        for _, control in self.control_tree.items():
            await control.entry()
        for _, container in self.container_tree.items():
            await container.entry()

    async def _exit(self):
        # if self.connector is not None:
        #     self.connector.delete_page_container(self.machine_id)
        #     self.connector = None
        # self.p_context = dict()
        # self.controls = dict()
        # for _, control in self.control_tree.items():
        #     await control.exit()
        # self.control_tree = dict()
        # for _, container in self.container_tree.items():
        #     await container.exit()
        # self.container_tree = dict()
        self.p_context = dict()
        self.lsm.p_context = dict()
        if self.parent and self.machine_id in self.parent.component_tree:
            variable_dict = self.build_exit_data()
            await self.parent.handle_exit(self.machine_id, variable_dict)
            app_log.debug(f"m_id: {self.machine_id} publish exit.")
        await self.lsm.stop()
        self.update_parent_component_tree(delete=True)
        await super()._exit()

    def build_exit_data(self, variable_dict=None):
        if not isinstance(variable_dict, dict):
            variable_dict = dict()
            # 默认将 状态机全局变量 发送给 父组件，目前看不需要
            # for name, variable in self.globals.items():
            #     value = variable.value
            #     try:
            #         ujson.dumps(value)
            #     except Exception:
            #         pass
            #     else:
            #         variable_dict.update({name: value})
        return variable_dict

    @property
    def root_machine_id(self):
        return self.root.machine_id

    @property
    def page_machine_id(self):
        return self.page.machine_id

    @property
    def parent_machine_id(self):
        return self.parent.machine_id

    @property
    def editor_data(self):
        return self._editor_data

    @property
    def view_editor_data(self):
        return self._view_editor_data

    @property
    def control_editor_data(self):
        return self._control_editor_data

    async def datalist_inited(self, publish=False):
        event_uuid = UUID.sm_datalist.event_inited
        app_log.info("datalist inited")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def r_select_init(self):
        event_uuid = UUID.sm_form.event_dropdown
        app_log.info("r_select inited")
        await self.send_event(
            event_uuid, event_args={
                "field": self.limit_control_control_uuid, "other_info": {"page": 1}
                }, publish=True)

    async def datalist_init_only(self):
        event_uuid = UUID.sm_datalist.event_init_only
        app_log.info("datalist init_only")
        await self.send_event(event_uuid, event_args=dict())

    async def datalist_result(self, publish=False):
        event_uuid = UUID.sm_datalist.event_result
        app_log.info("datalist result")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def calendar_result(self, publish=False):
        event_uuid = UUID.sm_calendar.event_result
        app_log.info("calendar result")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def treelist_result(self, publish=False):
        event_uuid = UUID.sm_treelist.event_result
        app_log.info("treelist result")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def tree_result(self, publish=False):
        event_uuid = UUID.sm_tree.event_result
        app_log.info("tree result")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def tree_inited(self, publish=False):
        event_uuid = UUID.sm_tree.event_inited
        app_log.info("tree inited")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def form_inited(self, publish=False):
        event_uuid = UUID.sm_form.event_inited
        app_log.info("form inited")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def form_view(self, view=True):
        event_uuid = UUID.sm_form.event_inited if view else UUID.sm_form.event_edit
        app_log.info("form view: {view}")
        await self.send_event(event_uuid, event_args=dict())

    async def form_result(self, publish=False):
        event_uuid = UUID.sm_form.event_result
        app_log.info("form result")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def form_new(self):
        event_uuid = UUID.sm_form.event_new
        app_log.info("form new")
        await self.send_event(event_uuid, event_args=dict())

    async def form_recovery(self, publish=False):
        event_uuid = UUID.sm_form.event_recovery
        app_log.info("form recovery")
        await self.send_event(event_uuid, event_args=dict(), publish=publish)

    async def send_chart_inited(self):
        event_uuid = UUID.sm_chart.event_inited
        app_log.info("chart inited")
        await self.send_event(event_uuid, event_args=dict())

    async def timeline_inited(self):
        event_uuid = UUID.rc_timeline.event_init
        event_args = {"pk": self.pk}
        app_log.info("timeline init")
        await self.send_event(event_uuid, event_args=event_args)

    def do_create_lsm(self, init_start=True):
        state_machine_dict = self.state_machine_dict
        self.lsm = self.lsm_class(
            component=self, nested=self.nested,
            subscribe_models=self.subscribe_models,
            init_start=init_start, **state_machine_dict)
        self.update_parent_component_tree()
        return self.lsm

    @decorator_helper.cloud_func
    async def create_lsm(self):
        if self.lsm:
            return
        self.do_create_lsm()
        await self.lsm.async_start_machine()

    async def start(self):
        await self.create_lsm()

    async def refresh(self, init=True, call_later=False):
        pass

    async def handle_temp_in_page(
            self, data, db_data=True, force_update=False, linkage=False, exit_linkage=False):
        pass


class BaseControlComponent(BaseComponent):

    def __init__(self):
        super().__init__()
        self.is_required = False
        self.relation_required = False  # 关联是否必填，读取设计时的值
        self.edit_field_info = dict()  # 编辑字段的信息（貌似已弃用）
        self._edit_column = None  # 编辑字段
        self._edit_column_path = []  # 编辑字段的关联路径
        self._edit_column_set = False  # 是否已获取到编辑字段
        self._handle_column = None  # 操作字段 （如：数据列表的列上可过滤、排序的那个字段）
        self._handle_column_path = []  # 操作字段的关联路径
        self._pk = 0
        self.aggre_func = None  # 是否使用了聚合函数
        self.separator = None  # concat 分隔符
        self.editable_value = None  # 当前控件的可编辑性
        self.own_buttons = dict()  # 组件 的 子组件

    def init_component_dict(self, component_dict, data_convert=False) -> None:
        self.control = component_dict
        component_dict = self.control.get("component", dict())
        super().init_component_dict(component_dict, data_convert)
        self.control.update({"component": self.component_dict})
        self.control_dict = self.component_dict
        input_control = self.control.get("input_control")
        self.candidate_preprocessing = self.control.get("candidate_preprocessing", {})
        if self.candidate_preprocessing:
            data_type = self.candidate_preprocessing.get("dataType")
            self.is_func_data = data_type == PreprocessType.CloudFunction
        else:
            self.is_func_data = False
        control_type = self.control_dict.get("type", None)
        # 用来确认，控件，是哪种编辑方式，默认使用 input_control
        # 因为当在数据列表的列上时，会有 input_control 的值
        self.edit_control_type = control_type if input_control is None else input_control
        # 数据联动设置, 老数据默认True
        self.init_linkage_settings()
        # 区分是否动态列上的控件
        dynamic_setting = component_dict.get("dynamic_setting", {})
        self.dynamic_column_type = dynamic_setting.get("dynamic_column_type", None)  # DynamicColumnType
        self.is_dynamic = self.dynamic_column_type is not None
        self.mapping_key_result = dynamic_setting.get("mapping_key_result", None)  # 映射键的实际值

    def init_linkage_settings(self):
        p_receive_linkage = True
        parent_uuid = self.page.component_uuid_tree.get(self.uuid)
        parent = self.page.all_component.get(parent_uuid)
        self.send_linkage = self.component_dict.get("linkage_settings", {}).get("send_linkage", True)
        view_receive_linkage = self.component_dict.get("view_receive_linkage")
        if parent and hasattr(parent, "receive_linkage"):
            p_receive_linkage = parent.receive_linkage
        receive_linkage = self.component_dict.get("linkage_settings", {}).get("receive_linkage", True)
        self.receive_linkage = p_receive_linkage if p_receive_linkage is False else receive_linkage
        self.view_receive_linkage = self.receive_linkage if view_receive_linkage is None else view_receive_linkage

    def init_data_model(self, data_model):
        self.data_model = data_model

    def initialize(self):
        # 是否显示 自关联 字段的多层级
        self.handle_data_source = self.component_dict.get("data_source", {})
        self.show_level_type = self.handle_data_source.get("show_level_type")
        self.self_referential_path = self.handle_data_source.get(
            "association", {}).get("path", [])
        self.control_data_source = self.control_dict.get("data_source", {})
        self.is_setting_default = False  # 是否配置了组件默认值
        self.edit_default_value = dict()  # 组件默认值设置
        self.find_edit_column()
        self.is_required = self.component_dict.get("is_required", False)
        self.field_check_service = BaseFieldCheck(self)
        self.parent_component = None  # 父组件的UUID
        self.title_name = ""  # 关联约束中，下拉框 placeholder 中需要展示的数据
        self.limit_type = 0
        self.limit_path_info = {
            "limit_path": list(), "path_models": list(), "limit_query": None,
            "last_pk": [0], "limit_pks": None,  "id_field": None,
            "start_node": None, "end_node": None,
            "backref": None, "frontref": None, "model_dict": dict()}
        self.find_limit_path()
        self.find_all_columns()
        self.init_own_buttons()
        self.init_self_referential_dict()

    def __get__(self, instance, instance_type=None):
        self._form = instance._form
        self._lsm = self._form.lsm
        return self

    @property
    def limit_filter_dict(self):
        return self._form.control_filter_dict[self.control_uuid]

    @limit_filter_dict.setter
    def limit_filter_dict(self, value):
        self._form.control_filter_dict[self.control_uuid] = value

    """
       该变量为共享变量，传递顺序为表单-关联子表-表单-组件
       一般场景为父表单的约束条件用于约束子表单的组件
    """
    @property
    def parent_filter_dict(self):
        if self.component_type != ComponentType.FORM:
            if hasattr(self._form.parent, "parent_filter_dict"):
                parent_filter_dict = self._form.parent.filter_dict
            else:
                # 父组件继承数据列表的表单需要获取数据列表的filter_dict
                if self._form.parent and self._form.parent.component_type in [
                        ComponentType.DATALIST]:
                   
                    parent_filter_dict = self._form.parent.filter_dict
                else:
                    parent_filter_dict = self._form.parent_filter_dict
            return parent_filter_dict
        else:
            return dict()

    """
        关联约束的信息，跨组件时，
        会由父组件传下来用于判断相关约束条件是否已选择，
        未选择的话会提示未选择约束条件
    """
    @property
    def filter_dict(self):
        if self.component_type != ComponentType.FORM:
            return self._form.filter_dict

    @property
    def parent(self):
        if self.form:
            return self.form
        else:
            return None if self._parent is None else self._parent()

    @property
    def lsm(self):
        return self.parent.lsm

    @property
    def edit_column(self):
        return self._edit_column

    @property
    def edit_column_path(self):
        return self._edit_column_path

    @property
    def handle_column(self):
        return self._handle_column

    @property
    def handle_column_path(self):
        return self._handle_column_path

    def set_handle_column(self, handle_column):
        self._handle_column = handle_column

    def set_handle_column_path(self, column_path):
        self._handle_column_path = column_path

    def set_edit_column_path(self, column_path):
        self._edit_column_path = column_path

    def _find_edit_column(self):
        return self._edit_column

    def find_edit_column(self):
        self._find_edit_column()

    def _find_all_columns(self):
        self.find_visible_columns()
        self.find_editable_columns()
        self.find_disabled_columns()

    def find_font_columns(self):
        return self._find_normal_editor_columns(
            self.control_dict, "font_color", "value")

    def find_dynamic_columns(self):
        dynamic_setting = self.component_dict.get("dynamic_setting", {})
        if dynamic_setting.get("dynamic_source_type") == DynamicSourceType.JSON:
            data_editor_dict = dynamic_setting.get("data_field")
            data_editor = self.create_editor(data_editor_dict)
            self.control_data_source.update({"data_editor": data_editor})
            self._find_editor_columns(data_editor)
        elif dynamic_setting.get("dynamic_source_type") == DynamicSourceType.SUBTABLE:
            data_source_editor_dict = dynamic_setting.get("data_source_field")
            data_source_editor = self.create_editor(data_source_editor_dict)
            self.control_data_source.update({"data_source_editor": data_source_editor})
            self._find_editor_columns(data_source_editor)

            title_editor_dict = dynamic_setting.get("title_field")
            title_editor = self.create_editor(title_editor_dict)
            self.control_data_source.update({"title_editor": title_editor})
            self._find_editor_columns(title_editor)

    def find_data_source_columns(self):
        value_editor_dict = self.control_data_source.get("editor")
        self.aggre_func = value_editor_dict.get("aggre_func")
        self.separator = value_editor_dict.get("separator")
        value_editor = self.create_editor(value_editor_dict)
        if value_editor and value_editor.type == ValueEditorType.FIELD:
            self.set_handle_column(value_editor.column)
            self.set_handle_column_path(value_editor.path)
        self.find_dynamic_columns()
        return self._find_editor_columns(value_editor)

    def find_title_columns(self):
        title_dict = self.control_dict.get("title", {})
        value_editor = self.create_editor(title_dict)
        return self._find_editor_columns(value_editor)

    def find_tooltip_columns(self):
        tooltip_dict = self.control_dict.get("tooltip", {})
        value_dict = tooltip_dict.get("value", {})
        value_editor = self.create_editor(value_dict)
        return self._find_editor_columns(value_editor)

    def find_visible_columns(self):
        visible_dict = self.control_dict.get("visible", {})
        is_visible_dict = visible_dict.get("is_visible", {})
        value_editor = self.create_editor(is_visible_dict)
        return self._find_editor_columns(value_editor)

    def find_editable_columns(self):
        editable_dict = self.control_dict.get("editable", {})
        value_editor = self.create_editor(editable_dict)
        return self._find_editor_columns(value_editor)

    def find_disabled_columns(self):
        disabled_dict = self.control_dict.get("disabled", {})
        value_editor = self.create_editor(disabled_dict)
        return self._find_editor_columns(value_editor)

    def find_all_columns(self):
        super().find_all_columns()
        self.edit_column  # 触发 edit_column 的赋值
        self.find_font_columns()
        self._find_all_columns()

    def init_own_buttons(self):
        own_buttons = self.control_dict.get("own_buttons", [])
        # app_log.info(f"controls: {controls}")
        # 添加数据模型的pk字段
        for control in own_buttons:
            control_dict = control.get("component")
            control_type = control_dict.get("type")
            control_obj = engine.component_creator.create_component(
                component_type=control_type, control=True,
                component_dict=control, parent=self,
                data_model=self.data_model)
            # app_log.info(f"control_obj: {control_obj}")
            if control_obj:
                self.own_buttons.update({control_obj.uuid: control_obj})

    def get_event(self, index, handle):
        return self.control_dict.get("events", {}).get(handle, [])[index]

    async def edit_result(self, control_value):
        raise NotImplementedError()

    async def view_result(self, control_value):
        raise NotImplementedError()

    async def result(self, control_value, form_data, children_data, need_validate=False):
        raise NotImplementedError()

    async def validate(self, check_value):
        return await self.check_field(check_value)

    async def select_data(self, select_data_source:dict=None):
        # 参数 data_model, control_uuid, column, control, pk
        preprocessing = self.control_dict.get("preprocessing", {})
        preprocessing_open = preprocessing.get("open")
        select_data = []
        if preprocessing_open:
            edit_value_dict = preprocessing.get("edit_value", {})
            edit_value = await self._calc_editor(
                edit_value_dict, calc_data=True, **self.source_in_page)
            if isinstance(edit_value, list):
                for index, value in enumerate(edit_value, start=1):
                    value = value if value else ""
                    select_data.append({
                        "name": value, "value": value, "id": index})
            elif isinstance(edit_value, str):
                edit_value = edit_value if edit_value else ""
                select_data.append({
                    "name": edit_value_dict.get("enum_item_name"), "value": edit_value, "id": 1})
            elif isinstance(edit_value, dict):
                select_data.append({
                    "name": edit_value_dict.get("enum_item_name"), "value": edit_value, "id": 1})
        else:
            select_data_source = select_data_source if select_data_source is not None else self.control_data_source
            select_type = select_data_source.get("type", DataSourceType.SELECT_FIELD)
            if select_type in [DataSourceType.SELECT_FUNC]:
                func_uuid = select_data_source.get("func")
                message = ""
                if not func_uuid:
                    message = f"{self.component_name}数据源云函数未设置！"
                else:
                    func_wrapper = await GlobalVars.FuncRun.get_func(func_uuid)
                    stdout, result = await GlobalVars.FuncRun.run(func_wrapper, need_stdout=True)
                    if isinstance(result, FuncRunError):
                        message = f"{self.component_name}执行云函数异常：\n{stdout}"
                    elif not isinstance(result, list):
                        message = f"{self.component_name}云函数结果不是 list 类型！"
                need_throw_exception = engine.config.get("NEED_THROW_EXCEPTION")
                if message:
                    if need_throw_exception:
                        message_box = MessageBox(self.page.sid, self.page.machine_id)
                        message_box.showerror(message)
                else:
                    if self.edit_column.lemon_type == FieldType.ENUM:
                        enum_values = [e.value for e in self.edit_column.choices]
                    for index, value in enumerate(result, start=1):
                        if self.edit_column.lemon_type == FieldType.DATETIME:
                            real_value = int(value.timestamp()) if value is not None else value
                            value = value.strftime("%Y/%m/%d %H:%M:%S") if value is not None else ""
                        elif self.edit_column.lemon_type == FieldType.BOOLEAN:
                            real_value = value
                            if value is None:
                                value = ""
                            else:
                                value = "是" if value else "否"
                        elif self.edit_column.lemon_type == FieldType.ENUM:
                            if value not in enum_values:
                                continue
                            else:
                                real_value = value
                                value = str(value) if value is not None else ""
                        else:
                            real_value = value
                            value = str(value) if value is not None else ""
                        select_data.append({"name": value, "value": real_value,"id": index})
        return select_data

    async def _check_field(self, check_value):
        return self.field_check_service.validate_result

    async def check_field(self, check_value):
        self.field_check_service.clean()
        self.field_check_service.validate_result.update({"real_value": check_value})
        return await self._check_field(check_value)

    async def calc_own_buttons_editor_data(self, control_value, control_settings):
        for control in self.own_buttons.values():
            if control:
                await control.calc_result(control_value, control_settings)

    async def calc_result_editor_data(self, control_value=None, control_settings=None, calc_click=False):
        if control_value:
            build_hierarchy_data(self, [control_value])
        await self.calc_editor_data(control_value, control_settings)
        await self.calc_own_buttons_editor_data(control_value, control_settings)
        await self.calc_event(control_value, calc_click=calc_click)

    async def calc_result(self, control_value=None, control_settings=None, need_validate=False, calc_click=False):
        control_value = control_value or {}
        control_settings = control_settings or {}
        children_data, form_data, editor_data = {}, {}, {}
        # 层次字段 在值编辑器里，应该是什么格式的数据 ？
        # 列表内 以 "/" 分割的字符串 吗？
        # 但返回给前端的是 某种固定格式的数据
        # TODO: 这里也没想好，应该把层次字段的数据处理放到取数据那里，还是放到这里  哪种方式好？
        if self.view_receive_linkage or not need_validate and control_value:
            await self.calc_result_editor_data(
                control_value=control_value, control_settings=control_settings,
                calc_click=calc_click)
            editor_data = self.view_editor_data
        await self.result(control_value, form_data, children_data, need_validate=need_validate)
        return form_data, children_data, editor_data

    def process_edit_column_permission(self, only_editable=False):
        """计算列column是否有编辑权限"""
        is_relationship_component = self.component_type in ComponentType.RELATION_CONTROL_LIST
        editable_dict = {}
        editable = None
        if self.edit_column:
            model = self.edit_column.model
            try:
                if is_relationship_component:
                    for resource_id in self.edit_column_path:
                        if resource_id in [SYS_ENTITY_UUID.lemon_tag.resource_id.value]:
                            # 自定义权限管理页面，需要使<标签>与<标签分组>之间的关联处于可编辑状态以达到可选中的效果
                            continue
                        model._predicate_accessor_set(resource_id)
                elif self.component_type in ComponentType.INPUT_CONTROL_LIST:
                    model._predicate_accessor_set(self.edit_column.column_name)
            except LemonPermissionError:
                control_edit_dict = self.control_dict.get("editable")
                editable_uuid = control_edit_dict.get("uuid")
                editable = False
                editable_dict.update({editable_uuid: editable})
        if only_editable:
            return editable
        else:
            return editable_dict

    async def calc_color(
            self, control_color, control_value, value_out=None, key=None):
        editor_dict = control_color.get("editor", {})
        if not editor_dict:
            value = None
        else:
            value = await self._calc_editor(
             editor_dict, calc_data=True, **control_value)
        if value_out and key:
            value_out.update({key: value})
        return value

    def calc_input_result(self, control_value, update_form_row_data=True):
        column_name = self.edit_column.column_name
        # 目前动态列不支持行内编辑, 先注释
        # if self.component_dict.get("dynamic_uuid"):
        #     # 动态列上, 如果edit_field是总值, 就用mapping_key
        #     if column_name == self.handle_column.column_name:
        #         column_name = self.lsm.lemon.system.mapping_key
        value = control_value.get(column_name)
        column_lemon_type = self.edit_column.lemon_type

        value_result = None
        if column_lemon_type in [
                FieldType.STRING, FieldType.INTEGER, FieldType.DECIMAL]:
            value_result = value
        elif column_lemon_type == FieldType.BOOLEAN:
            if self.edit_control_type in [
                    ComponentType.CHECKBOX, ComponentType.SWITCH,
                    ComponentType.RADIO
            ]:
                if value is None:
                    name = ""
                else:
                    name = "是" if value else "否"
            else:
                name = "是" if value else "否"
            value_result = {"name": name, "value": value}
        elif column_lemon_type == FieldType.ENUM:
            try:
                name = self.edit_column.choices(value).name
                value_result = {"name": value, "value": value}
            except Exception:
                app_log.error(f"enum data: {value}")
                value = None
                name = None
                value_result = {"name": value, "value": value}
        elif column_lemon_type in [FieldType.DATETIME,
                                   FieldType.DATETIME_CYCLE]:
            if isinstance(value, datetime.datetime):
                value_result = value.timestamp()
            else:
                value_result = value
        elif column_lemon_type in [FieldType.FILE, FieldType.IMAGE]:
            if not isinstance(value, list):
                value = []
            value_result = value
        if update_form_row_data:
            self.page.update_form_row_data(self.parent, self, value)
        return value_result

    def calc_select_result(self, control_value, update_form_row_data=True):
        column_name = self.edit_column.column_name
        value = control_value.get(column_name)
        column_lemon_type = self.edit_column.lemon_type
        select_data_source = self.component_dict.get("select_data_source", None)
        data_source = self.component_dict.get("data_source", {})
        select_data_source = select_data_source if select_data_source is not None else data_source
        data_source_type = select_data_source.get("type", DataSourceType.SELECT_FIELD)
        value_result = None
        if column_lemon_type in [FieldType.STRING, FieldType.INTEGER, FieldType.DECIMAL]:
            name = "" if value is None else str(value)
            value_result = {"name": name, "value": value}
        elif column_lemon_type == FieldType.BOOLEAN:
            if value is None:
                name = ""
            else:
                name = "是" if value else "否"
            value_result = {"name": name, "value": value}
        elif column_lemon_type == FieldType.ENUM:
            try:
                name = self.edit_column.choices(value).name
                value_result = {"name": value, "value": value}
            except Exception:
                app_log.error(f"enum data: {value}")
                value = None
                name = ""
                value_result = {"name": value, "value": value}
        elif column_lemon_type in [FieldType.DATETIME, FieldType.DATETIME_CYCLE]:
            if value is None:
                name = ""
            else:
                name = datetime.datetime.fromtimestamp(value)
            value_result = {"name": str(name), "value": value}
        if data_source_type == DataSourceType.SELECT_FUNC:
            value_result.update({"ad": True})
        if update_form_row_data:
            self.page.update_form_row_data(self.parent, self, value)
        return value_result

    def _calc_relation_control_name(self, name):
        """
        本来是用来处理关联数据的展示问题, 但目前所有关联数据都展示其name
        接口保留, 后续可能有用
        """
        if name == "" or name is None:
            return ""
        # column_lemon_type = getattr(self.edit_column, "lemon_type", -1)
        # 关联下拉框 选择枚举字段，需要将数据处理为 枚举名称 (已过时)
        # 枚举字段显示value
        # if column_lemon_type == FieldType.ENUM:
        #     try:
        #         enum_data = self.edit_column.choices(name)
        #         name = enum_data.value
        #     except Exception:
        #         name = ""
        column_lemon_type = getattr(self.edit_column, "lemon_type", -1)
        if column_lemon_type == FieldType.BOOLEAN:
            name = "是" if name else "否"
        elif column_lemon_type == FieldType.DATETIME:
            date_format = get_datetime_by_timestamp(self.lsm, name)
            name = date_format.strftime("%Y/%m/%d %H:%M:%S")
        return name

    def get_relation_value_by_control_value(self, control_value):
        column_name = self.edit_column.column_name
        column_path = self.edit_column_path
        column_model = self.edit_column.model
        model_uuid = column_model._meta.table_name
        if self.edit_column.is_system:
            column_name = get_sys_field_uuid(model_uuid, column_name)

        if column_path:
            model_path_uuid = build_model_field_path(model_uuid, column_path)
            model_path_pk_uuid = model_path_uuid + "_pk"
        elif column_model == self.data_model:
            # 当前组件为 子表单选择器 时，需要这么处理
            # 自关联时，column_model 和 self.data_model 也会相等；
            # 但因为有 column_path 所以不应走到这里
            model_path_pk_uuid = "id"
        else:
            model_path_pk_uuid = model_uuid + "_pk"
        model_value = control_value.get(model_path_pk_uuid)
        # app_log.info(f"path_pk_uuid: {model_path_pk_uuid}")
        # app_log.info(f"edit_control_type: {self.edit_control_type}")
        column_path_name = build_field_path(column_path, column_name)
        column_value = control_value.get(column_path_name)
        if not isinstance(model_value, list):  # 1 端数据时 model_value 不为列表
            model_value = [model_value]
        if not isinstance(column_value, list):  # 1 端数据时 column_value 不为列表
            column_value = [column_value]
        return model_value, column_value

    def calc_relation_result(self, control_value, update_form_row_data=True):
        model_value, column_value = self.get_relation_value_by_control_value(control_value)
        value_result = self.process_relation_value_result(
            model_value, column_value, update_form_row_data=update_form_row_data)
        return value_result

    def process_relation_value_result(self, model_value, column_value, update_form_row_data=True):
        value_result = list()
        # app_log.info(f"m_value: {model_value}, c_value: {column_value}")
        # control_uuid = self.control_dict.get("uuid")
        pk_list = []
        for index, pk in enumerate(model_value, start=0):
            if pk is None:
                continue
            pk_list.append(pk)
            id_index = index + 1
            if self.edit_control_type == ComponentType.R_SELECT:
                name = self._calc_relation_control_name(column_value[index])
                # ad是让前端判断是否是关联下拉框的数据
                value_result.append({
                    "id": id_index, "name": name, "value": pk, "ad": True})
            elif self.edit_control_type == ComponentType.R_SELECT_POPUP:
                name = self._calc_relation_control_name(column_value[index])
                # 为什么要加个 full 为 True ， 前端需要区分关联下拉框 和 关联弹窗的数据
                value_result.append({
                    "id": id_index, "name": name, "value": pk, "full": True})
            elif self.edit_control_type == ComponentType.R_TILE:
                name = self._calc_relation_control_name(column_value[index])
                value_result.append({
                    "id": id_index, "name": name, "value": pk, "tile": True})
            elif self.edit_control_type == ComponentType.FORM_SELECTOR:
                name = self._calc_relation_control_name(column_value[index])
                value_result.append({
                    "id": id_index, "name": name, "value": pk, "ad": True})
            elif self.edit_control_type in [
                ComponentType.FORM, ComponentType.DATALIST,
                ComponentType.CARDLIST, ComponentType.DATAGRID,
                ComponentType.TREELIST
            ]:
                # 关联选择子表 和 关联填写子表 的状态机 需要表单推送初始化数据
                # 当 temp_in_page 被赋值， 触发 publish_temp_in_page
                # 所以这里需要 value_result
                value_result.append(pk)
        if update_form_row_data:
            self.page.update_form_row_data(self.parent, self, pk_list)
        return value_result

    async def handle_parent_relation_update(self, pk_list, parent=None):
        parent = parent or self.parent
        if (
            self.edit_control_type in [
                ComponentType.DATALIST, ComponentType.CARDLIST,
                ComponentType.DATAGRID, ComponentType.TREELIST
            ]
            and self.memory_storage
            and self.purpose not in [PurposeType.VIEW, PurposeType.DELETE]
        ):
            await parent.relation_update(self, pk_list, validate=True)
        elif (
            self.edit_control_type == ComponentType.FORM
            and self.memory_storage
        ):
            await parent.relation_update(self, pk_list, validate=True)

    async def update_source_in_page_value(self, control_value):

        def find_paths_in_tree(node, all_paths, level=0):
            # 重构树形数据，使其格式统一
            current_path = {
                "id": node.get("id"), "title": node.get("title"), "value": node.get("value")}
            if node.get("children"):
                for child in node.get("children"):
                    child_path = find_paths_in_tree(child, [], level)
                    level = level + 1
                    if "children" in current_path:
                        current_path["children"].append(child_path)
                    else:
                        current_path["children"] = [child_path]
            current_path.update({"level": level})
            all_paths.append(current_path)
            return current_path

        def get_all_paths(data_list):
            all_paths = []
            for data in data_list:
                find_paths_in_tree(data, all_paths)
            return all_paths

        value_result = []
        column_name = self.edit_column.column_name
        data_model = self.edit_column.model
        column_path_key = build_field_path(
            self.edit_column_path, column_name)
        column_value_key = "_".join([column_path_key, "value"])
        model_uuid = self.edit_column.model._meta.table_name
        path_model_uuid = build_model_field_path(
            model_uuid, self.edit_column_path)
        column_pk_key = "_".join([path_model_uuid, "pk"])
        if column_pk_key in control_value:
            pk_result = control_value.get(column_pk_key, [])
            value_result = control_value.get(column_value_key, [])
            if not isinstance(pk_result, list):
                pk_result = [pk_result]
            if not isinstance(value_result, list):
                value_result = [value_result]
            if not pk_result or len(pk_result) <= len(value_result):
                return
            self_referential_path, frontref = calc_self_referential_path_join(
                data_model, data_source=self.data_source)
            data_query = data_model.select(
                data_model.id,
                data_model.id.alias("value"),
                frontref.alias("pid"),
                self.edit_column.alias("title")
                )
            tenant_uuid = self.lsm.lemon.system.current_user.tenant_id
            query, cte = build_recursive_query_inverted(
                data_model, data_query, tenant_uuid=tenant_uuid, name_key="title",
                filter_outside_op=_operator.and_,  extra_conditions=[data_model.id.in_(pk_result)])

            data_list = await engine.userdb.objs.execute(query.dicts())
            data_list = process_inverted_tree_data(data_list, self.edit_column, name_key="title")
            all_paths = get_all_paths(data_list)
            control_value.update({column_value_key: all_paths})

    async def calc_hierarchy_result(self, control_value, update_form_row_data=True):
        value_result = []
        if (
            self.edit_column.hierarchy_field == 1   # 层次字段
            or
            self.data_source_type == DataSourceType.RTREE_SELF_REFERENTIAL
        ):
            column_name = self.edit_column.column_name
            if self.data_source_type == DataSourceType.RTREE_SELF_REFERENTIAL:
                # TODO 感觉换个地方比较好
                await self.update_source_in_page_value(control_value)
                # 自关联多路径处理
                column_path_key = build_field_path(
                    self.edit_column_path, column_name)
                column_value_key = "_".join([column_path_key, "value"])
            else:
                column_path_key = build_field_path(
                    self.edit_column_path, column_name)
                column_value_key = "_".join([column_path_key, "value"])
            if column_value_key in control_value:
                value_result = control_value.get(column_value_key)
        if update_form_row_data:
            value = self.get_hierarchy_result_value(control_value)
            self.page.update_form_row_data(self.parent, self, value)
        return value_result

    def get_hierarchy_result_value(self, control_value):
        value = list()
        if (
            self.edit_column.hierarchy_field == 1   # 层次字段
            or
            self.data_source_type == DataSourceType.RTREE_SELF_REFERENTIAL
        ):
            column_name = self.edit_column.column_name
            if self.data_source_type == DataSourceType.RTREE_SELF_REFERENTIAL:

                # 自关联多路径处理
                model_uuid = self.edit_column.model._meta.table_name
                path_model_uuid = build_model_field_path(
                    model_uuid, self.edit_column_path)
                column_pk_key = "_".join([path_model_uuid, "pk"])
            else:
                column_path_key = build_field_path(
                    self.edit_column_path, column_name)
                column_pk_key = "_".join([column_path_key, "pk"])

            if column_pk_key in control_value:
                value = control_value.get(column_pk_key)
                value = [] if value is None else value
                if not isinstance(value, list):
                    value = [value]
        return value

    def find_limit_path(self):
        data_preprocessing = self.candidate_preprocessing
        candidate = False
        if not data_preprocessing:
            data_preprocessing = self.control_dict.get("data_source", {}).get("candidate_preprocessing", {})
            # 关联子表仅展示
            if not data_preprocessing:
                data_preprocessing = self.control_dict.get("data_source", {}).get("preprocessing", {})
                self.limit_type = 1
                if data_preprocessing.get("dataType") != PreprocessType.PathLimit:
                    # 子表仅展示没有设置关联约束的时候不需要进行处理
                    return None
            else:
                candidate = True
        data_preprocessing = data_preprocessing or {}
        limit_path_info = data_preprocessing.get("relationPath")
        limit_detail = self.limit_path_info
        # 判断是否设置了关联路径约束
        if limit_path_info and data_preprocessing.get("dataType") == PreprocessType.PathLimit:
            limit_path = limit_path_info.get("path", [])[::]
            limit_detail["limit_path"] = limit_path
            model_dict = limit_detail["model_dict"]
            path_models = set()
            source_model = limit_path_info.get("sourceModel")
            start_model = lemon_model(source_model)
            field_model = self.edit_column.model
            end_model = field_model
            end_model_uuid = field_model._meta.table_name
            field_model_name = field_model._meta.table_name
             # 查找本模型的外键
            field_model_node = lemon_model_node(field_model_name)
            last_path = limit_path[-1]
            limit_detail["source_model"] = start_model
            limit_detail["last_path"] = last_path
            relation_node = list(filter(lambda node:node.r_uuid==last_path, field_model_node.relation_nodes))[0]
            limit_detail["frontref"] = relation_node.frontref
            limit_detail["backref"] = relation_node.backref
            condition = True
            insert_pre = False
            if self.control_dict.get("type") in [
                ComponentType.DATALIST, ComponentType.CARDLIST,
                ComponentType.DATAGRID, ComponentType.TREELIST
            ]:
                if self.control_dict.get("data_source").get("add_method") == AddMethodType.HAND_INPUT:
                    # condition = False
                    data_source = self.control_dict.get("data_source")
                    if data_source.get("insert_type") is None and candidate:
                        # 兼容之前预填写的页面
                        data_source["insert_type"] = True
                        self.limit_type = 0 
                    insert_pre =  True
            else:
                if self.component_type == ComponentType.FORM:
                    insert_pre = True
            if insert_pre:
                data_source = self.control_dict.get("data_source")
                # insert_type 为1触发预填写
                if data_source.get("insert_type"):
                    # 如果是预填写，不能join本模型否则会找不到数据
                    s_model, t_model, r_type, s_referential, s_from_source = lemon_relationship(limit_path[-1])
                    s_model_uuid = s_model._meta.table_name if hasattr(s_model, "_meta") else s_model.uuid
                    t_model_uuid = t_model._meta.table_name if hasattr(t_model, "_meta") else t_model.uuid
                    if s_model_uuid == field_model_name:
                        end_model_uuid = t_model_uuid
                    else:
                        end_model_uuid = s_model_uuid
                    end_model = lemon_model(end_model_uuid)
                    model_dict[end_model._meta.table_name] = lemon_path_model(end_model_uuid, [])
                    limit_path.pop(-1)
            reverse_limit_path = limit_path[::-1]
            swp_model = start_model
            start_model = end_model
            end_model = swp_model
            start_model_uuid = end_model_uuid
            end_model_uuid = source_model
            end_node = lemon_model_node(end_model_uuid)
            select_column = start_model.id
            start_node = lemon_model_node(start_model_uuid)
            model_query = start_model.select(start_model.id)
            is_many, last_path, model_query = build_join_info(
                start_node, end_node, path=reverse_limit_path, query=model_query, rename_model=True,
                lemon_association=lemon_association, lemon_model_node=lemon_model_node)
            # if condition:
            # model_query = AsyncDataPreprocess(start_model, data_preprocessing, self.lsm, rename_model=True)(model_query)
            model_query = model_query.group_by(select_column)
            preprocess_class = AsyncDataPreprocess(start_model, data_preprocessing, self.lsm, rename_model=True)(model_query)
            limit_detail["start_node"] = start_node
            limit_detail["end_node"] = end_node
            limit_detail["limit_query"] = None
            limit_detail["preprocess_class"] = preprocess_class
            limit_detail["group_column"] = select_column
            r_path = []
            last_model_uuid = start_model_uuid
            for r_uuid in reverse_limit_path:
                s_model, t_model, r_type, s_referential, s_from_source = lemon_relationship(r_uuid)
                s_model_uuid = s_model._meta.table_name if hasattr(s_model, "_meta") else s_model.uuid
                t_model_uuid = t_model._meta.table_name if hasattr(t_model, "_meta") else t_model.uuid
                path_models.add(s_model_uuid)
                path_models.add(t_model_uuid)
                r_path.append(r_uuid)
                if last_model_uuid != lemon_model(s_model_uuid)._meta.table_name:
                    next_model = s_model_uuid
                else:
                    next_model = t_model_uuid
                model_dict[lemon_model(next_model)._meta.table_name] = lemon_path_model(next_model, r_path)
                last_model_uuid = next_model
            path_models.remove(source_model)
            if field_model_name in path_models:
                path_models.remove(field_model_name)
            limit_detail["path_models"] = path_models
            limit_detail["id_field"] = start_model.id
            limit_detail["proprocess_type"] = PreprocessType.PathLimit
            limit_detail["start_model_uuid"] = start_model_uuid
        elif (data_preprocessing.get("dataType") == PreprocessType.Normal
              and
              self.control_dict.get("type") in [
                  ComponentType.DATALIST, ComponentType.CARDLIST,
                  ComponentType.DATAGRID, ComponentType.TREELIST
                  ]):
            field_model = self.edit_column.model
            start_model = field_model
            preprocess_class = AsyncDataPreprocess(
                start_model, data_preprocessing, self.lsm, rename_model=True)()
            # model_query = model_query.group_by(start_model.id)
            # limit_detail["limit_query"] = model_query
            limit_detail["proprocess_type"] = PreprocessType.Normal
            limit_detail["id_field"] = start_model.id
            limit_detail["limit_query"] = None
            limit_detail["preprocess_class"] = preprocess_class
            limit_detail["group_column"] = start_model.id

    async def get_limit_query(self):
        if preprocess_class := self.limit_path_info.get("preprocess_class"):
            limit_query = await preprocess_class
            group_column = self.limit_path_info.get("group_column")
            limit_query = limit_query.group_by(group_column)
            self.limit_path_info["limit_query"] = limit_query
            self.limit_path_info.pop("preprocess_class")
            return limit_query
        else:
            return self.limit_path_info["limit_query"]

    async def update_limit_condition(self, edit_field, value):
        filter_dict = self.limit_filter_dict
        field_model = edit_field.model
        field_model_name = field_model._meta.table_name
        last_value = filter_dict.get(field_model_name, {}).get("value")
        # 用于判断条件是否有变化
        if last_value != value:
            # 保存当前表单上所有相关字段的筛选，并且value值为对应模型的pk
            filter_dict.update({
                field_model._meta.table_name: {
                    "column": field_model.id, "value": value}})
            return True
        else:
            return False

    '''
    description: 从父表单获取关联约束条件
    param {*} self
    return {*}
    author: lv.jimin
    '''
    def get_parent_form_filter(self):
        filter_dict = {}
        limit_path = self.limit_path_info.get("limit_path")
        # 关联仅展示不需要第一段的路径约束
        if self.limit_type == 1:
            limit_path = limit_path[1:]
        if limit_path:
            for column_info in self.parent_filter_dict.values():
                model_uuid = column_info.get("model_uuid")
                field_path = column_info.get("path")
                control_uuid = column_info.get("control_uuid")
                # 避免循环约束
                if self.parent_component == control_uuid:
                    continue
                if field_path in limit_path:
                    filter_dict[model_uuid] = column_info
        return filter_dict

    """判断是否所有约束都被满足"""
    def check_precondition(self):
        limit_path = self.limit_path_info.get("limit_path")
        # 关联仅展示不需要第一段的路径约束
        if self.limit_type == 1:
            limit_path = limit_path[1:]
        tip_list = []
        index = 0
        parent_tip = False
        if limit_path:
            r_control_dict = self.filter_dict.get("r_control_dict", {})
            p_control_dict = self.parent_filter_dict.get("r_control_dict", {})
            for path in limit_path:
                control_dict = r_control_dict.get(path, {})
                tip_info = None
                for control_info in control_dict.values():
                    if not control_info.get("value_set"):
                        control = control_info.get("control")
                        # name = control.title_name
                        edit_column = control.edit_column
                        name = edit_column.name
                        model_name = edit_column.model._meta.name
                        index += 1
                        error = IDECode.NULL_CONDITION.format(
                            model_name=model_name, name=name)
                        item = {"id": index, "name": error, "value": 0,
                                "disabled": True, "ad": True}
                        tip_info = item
                    else:
                        tip_info = None
                        break
                if tip_info:
                    tip_list.append(tip_info)
                p_dict = p_control_dict.get(path, {})
                p_tip_info = None
                for control_info in p_dict.values():
                    # 避免循环约束
                    control_uuid = control_info.get("uuid")
                    if self.parent_component == control_uuid:
                        continue
                    if not control_info.get("value_set"):
                        p_tip_info = True
                    else:
                        p_tip_info = False
                        break
                if p_tip_info:
                    parent_tip = True
        if parent_tip:
            index += 1
            error = IDECode.P_NULL_CONDITION
            item = {"id": index, "name": error, "value": 0,
                    "disabled": True, "ad": True}
            tip_list.append(item)
        return tip_list

    async def find_limit_pks(self):
        limit_detail = self.limit_path_info
        limit_pks = None
        if limit_detail.get("proprocess_type") == PreprocessType.PathLimit:
            filter_dict = self.limit_filter_dict
            condition_dict = self.get_parent_form_filter()
            condition_dict.update(filter_dict)
            if not condition_dict:
                return None
            condition_list = []
            model_dict = limit_detail.get("model_dict")
            for model_uuid, filter_info in condition_dict.items():
                column = filter_info.get("column")
                value = filter_info.get("value")
                column_model = model_dict.get(model_uuid)
                column = getattr(column_model, column.name)
                condition_list.append(column.in_(value))
            limit_pks = await self.get_limit_pks(condition_list)
        elif limit_detail.get("proprocess_type") == PreprocessType.Normal:
            limit_pks = await self.get_limit_pks()
        return limit_pks

    async def get_limit_pks(self, condition_list: list = None):
        data_query = await self.get_limit_query()
        data_query = data_query.clone()
        if condition_list is not None:
            data_query = data_query.where(*condition_list)
        data_query = data_query.dicts()
        data_list = await engine.userdb.objs.execute(data_query)
        limit_pks = [data.get("id") for data in data_list]
        self.limit_path_info["limit_pks"] = limit_pks
        return limit_pks

    # 由预填写触发的预填写
    # 第一级约束路径 ABCDE
    # 第二级约束路径 EDFG, 通过第一级的EF 预填写G, 第二级预填写的时候要直接将EG也关联上
    async def find_limit_pks_by_insert_pre(self, pk_list, model_uuid):
        limit_detail = self.limit_path_info
        model_dict = limit_detail.get("model_dict")
        # source_model 为 E
        source_model = limit_detail["source_model"]
        column_model = model_dict.get(source_model._meta.table_name)
        if column_model and pk_list:
            data_query = await self.get_limit_query()
            data_query = data_query.clone().where(
                column_model.id.in_(pk_list)).select_extend(
                column_model.id.alias("source_id")).dicts()
            data_list = await engine.userdb.objs.execute(data_query)
            # 获取所有筛选结果pk
            limit_dict = {
                data.get("id"): data.get("source_id") for data in data_list
            }
            limit_detail["limit_pks"] = limit_dict.keys()
            return limit_dict
        else:
            return None

    async def get_default_value(self):
        update_default, default_value_dict = False, {}
        return update_default, default_value_dict

    async def n_get_default_value(self):
        default_value_setting = self.edit_default_value
        update_default, default_value_dict = False, {}
        if default_value_setting:
            if isinstance(default_value_setting, dict):
                if self.edit_column.lemon_type == FieldType.DATETIME:
                    default_value = await self.lemon_editor(
                        default_value_setting, to_str=False)
                    if isinstance(
                            default_value, (datetime.datetime, datetime.date)):
                        default_value = get_timestamp_by_datetime(
                            self.lsm, default_value)
                else:
                    default_value = await self.lemon_editor(
                        default_value_setting)
            else:
                default_name, default_value = get_default_value(
                    default_value_setting, self.edit_column)
            default_value_dict = {self.edit_column.column_name: default_value}
            update_default = True
        return update_default, default_value_dict

    async def r_get_default_value(self, is_many):
        update_default, default_value = False, dict()
        if not self.is_setting_default:
            return update_default, default_value
        else:
            update_default = True
        column_name = self.edit_column.column_name
        column_model = self.edit_column.model
        path = self.edit_column_path
        preprocessing_dict = self.edit_default_value
        column_model_uuid = column_model._meta.table_name
        column_model_path_name = build_model_field_path(
            column_model_uuid, path) + "_pk"
        column_path_name = build_field_path(path, column_name)
        pk_column = column_model.id.alias(column_model_path_name)
        value_column = self.edit_column.alias(column_path_name)
        columns = [pk_column, value_column]
        data_query = column_model.select(*columns)
        data_query = await AsyncDataPreprocess(
                        column_model, preprocessing_dict,
                        lsm=self.lsm, rename_model=True)(data_query)
        default_option = preprocessing_dict.get("default_option", 1)
        data_query = data_query.dicts()
        data_query = data_query.offset(default_option-1).limit(1)
        data_list = await engine.userdb.objs.execute(data_query)
        start = 1
        pk_list = []
        value_list = []
        for index, data in enumerate(data_list, start=start):
            name = data.get(column_path_name)
            value = data.get(column_model_path_name)
            pk_list.append(value)
            value_list.append(name)
        if value_list:
            value = value_list[0]
            # if is_many:
            #     model_pk = pk_list
            #     column_value = value
            # else:
            #     model_pk = pk_list
            #     column_value = value
            column_info = {column_model_path_name: pk_list,
                           column_path_name: value}
        else:
            column_info = {}
        return update_default, column_info

    async def _get_old_value(self, kwargs, from_obj=False, pk_only=False):
        if (
            self.edit_column.model == self.data_model
            and
            not self.edit_column_path
        ):
            if "old_value" in kwargs and not from_obj:
                old_value = safe_pop(kwargs, "old_value", None)
            else:
                old_value = self.parent.model_obj.get_data(
                    self.edit_column.name)
            if isinstance(old_value, float):
                old_value = decimal.Decimal(str(old_value))
            old_value = self.type_conversion(old_value)
            return old_value
        else:
            model_obj = self.parent.model_obj
            last_path = get_join_path(
                self.data_model, self.edit_column.model,
                path=self.edit_column_path)
            if last_path.is_middle or last_path.is_source:
                if last_path.is_middle:
                    name = last_path.backref.name
                elif last_path.is_source:
                    name = last_path.frontref.backref
                backref_query = getattr(model_obj, name)
                old_value, old_value_pk = [], []
                if "old_value" in kwargs and not from_obj:
                    old_pk_list = safe_pop(kwargs, "old_value", [])
                    if isinstance(old_pk_list, list) and old_pk_list:
                        obj_list = await get_memory_instance_by_pk_list(
                            old_pk_list, self.edit_column.model)
                        for obj in obj_list:
                            old_value.append(obj)
                            old_value_pk.append(obj.get_id(memory_pk=True))
                else:
                    if (
                        name in model_obj._v_dirty_many_to_many_set or
                        name in model_obj._v_dirty_backref_set
                    ):
                        for obj in backref_query:
                            old_value.append(obj)
                            old_value_pk.append(obj.get_id(memory_pk=True))
                    else:
                        if backref_query.all:
                            for obj in backref_query:
                                old_value.append(obj)
                                old_value_pk.append(obj.get_id(memory_pk=True))
                        data_list = await backref_query.default_all()
                        if data_list:
                            for obj in data_list:
                                old_value.append(obj)
                                old_value_pk.append(obj.get_id())
                if last_path.r_type == RelationshipType.ONE_TO_ONE:
                    # 一端箭尾返回的应该是列表，如果没有值返回的应该是 []
                    old_value = [old_value[0]] if old_value else []
                    old_value_pk = old_value_pk[0] if old_value_pk else []
            else:
                frontref = last_path.frontref
                frontref_name = frontref.name
                if "old_value" in kwargs and not from_obj:
                    old_value = safe_pop(kwargs, "old_value", [])
                    if isinstance(old_value, list) and old_value:
                        old_value = old_value[0]
                else:
                    old_value = self.parent.model_obj.get_data(frontref_name)
                old_value_pk = []
                if old_value:
                    if isinstance(old_value, int):
                        obj = self.parent.model_obj.get_data(
                            frontref_name, fk=True)
                        if not obj:
                            field = self.parent.model_obj._meta.fields.get(
                                frontref_name)
                            obj = await get_memory_instance(
                                old_value, field.rel_model,
                                memory_storage=self.memory_storage, db=True)
                        old_value_pk.append(old_value)
                    elif isinstance(old_value, peewee.Model):
                        old_value_pk.append(old_value.get_id(memory_pk=True))
                else:
                    old_value = None
            if pk_only:
                return old_value_pk
            return old_value

    async def _get_new_value(self, kwargs):
        new_value = safe_pop(kwargs, "new_value", None)
        if (
            self.edit_column.model == self.data_model
            and
            not self.edit_column_path
        ):
            # 本表
            if isinstance(new_value, float):
                new_value = decimal.Decimal(str(new_value))
            new_value = self.type_conversion(new_value)
            self.parent.model_obj.update_validate({self.edit_column.name: new_value},
                                validate=True)
            return new_value
        else:
            column_model = self.edit_column.model
            last_path = get_join_path(
                self.data_model, column_model, path=self.edit_column_path)
            if last_path:
                if last_path.is_middle:
                    name = last_path.backref.name
                elif last_path.is_source:
                    name = last_path.frontref.backref
                else:
                    name = last_path.frontref.name
            if self.is_func_data:
                if last_path.is_source:
                    obj = getattr(self.parent.model_obj, name)
                    if last_path.r_type == RelationshipType.ONE_TO_ONE:
                        # 一端箭尾返回的应该是列表，如果没有值返回的应该是 []，参考 bug 11839
                        if new_value and isinstance(new_value, list):
                            value = await get_memory_instance(
                                new_value[0], column_model,
                                memory_storage=self.memory_storage, db=False)
                            new_new_value = [value]
                            if obj is not None:
                                obj.replace(new_new_value)
                            return new_new_value
                        else:
                            obj.replace([])
                            return []
                    else:
                        value = [await get_memory_instance(
                            n, column_model,
                            memory_storage=self.memory_storage, db=False) for n in new_value]
                        if obj is not None:
                            obj.replace(value)
                        return value
                else:
                    if new_value and isinstance(new_value, list):
                        value = await get_memory_instance(
                            new_value[0], column_model,
                            memory_storage=self.memory_storage, db=False)
                        self.parent.model_obj.update_validate({name: value})
                        return value
                    else:
                        setattr(self.parent.model_obj, name, None)
                        return None
            else:
                if last_path.is_source:
                    obj = getattr(self.parent.model_obj, name)
                    if last_path.r_type == RelationshipType.ONE_TO_ONE:
                        # 一对一，关联的字段是源，即箭尾
                        if new_value and isinstance(new_value, list):
                            # LemonBackrefQuery 类型
                            value = await engine.userdb.objs.get(
                                column_model, id=new_value[0])
                            new_new_value = [value]
                            if obj is not None:
                                obj.replace(new_new_value)
                            return new_new_value
                        else:
                            obj.replace([])
                            return []
                    else:
                        # 一对多、多对多，关联字段是多端，即箭尾
                        query = column_model.select().where(
                            column_model.id.in_(new_value))
                        value = await engine.userdb.objs.execute(query)
                        if obj is not None:
                            obj.replace(list(value))
                        return list(value)
                else:
                    # 一对一、一对多，关联的字段是目标，即箭头
                    if new_value and isinstance(new_value, list):
                        value = await engine.userdb.objs.get(
                            column_model, id=new_value[0])
                        self.parent.model_obj.update_validate({name: value})
                        return value
                    else:
                        setattr(self.parent.model_obj, name, None)
                        return None

    def type_conversion(self, value):
        # 类型转换是因为传入的 value 的类型并不是 edit_column 的类型，
        # 导致后面调用云函数时通过 type 方法获取 value 的类型时得到的结果不是 edit_column 定义的类型
        if value is None:
            return None
        if self.edit_column.lemon_type == FieldType.DECIMAL:
            value = decimal.Decimal(value)
        elif self.edit_column.lemon_type == FieldType.INTEGER:
            value = int(value)
        elif self.edit_column.lemon_type == FieldType.DATETIME:
            if isinstance(value, datetime.datetime):
                value = value.timestamp()
            value = convert_timestamp_to_datetime(value)
        return value

    async def _replace_new_value(self, new_value):
        if (
            self.edit_column.model == self.data_model
            and
            not self.edit_column_path
        ):
            if self.edit_column.lemon_type == FieldType.DATETIME:
                if isinstance(new_value, datetime.datetime):
                    return int(time.mktime(new_value.timetuple()))
            return new_value
        else:
            column_model = self.edit_column.model
            last_path = get_join_path(
                self.data_model, column_model, path=self.edit_column_path)

            if last_path.is_middle:
                if isinstance(new_value, LemonAsyncQueryWrapper):
                    n_list = []
                    for n in new_value:
                        if isinstance(n, peewee.Model):
                            n_list.append(n.get_id())
                    return n_list

            if last_path.is_source:
                if last_path.r_type == RelationshipType.ONE_TO_ONE:
                    if isinstance(new_value, peewee.Model):
                        return [new_value.get_id()]
                    elif isinstance(new_value, list):
                        n_list = []
                        for n in new_value:
                            if isinstance(n, peewee.Model):
                                n_list.append(n.get_id())
                            elif isinstance(n, int):
                                n_list.append(n)
                        return n_list
                else:
                    if isinstance(new_value, list):
                        n_list = []
                        for n in new_value:
                            if isinstance(n, peewee.Model):
                                n_list.append(n.get_id())
                            elif isinstance(n, int):
                                n_list.append(n)
                        return n_list
                    elif isinstance(new_value, LemonAsyncQueryWrapper):
                        n_list = []
                        for n in new_value:
                            if isinstance(n, peewee.Model):
                                n_list.append(n.get_id())
                        return n_list
            else:
                if isinstance(new_value, peewee.Model):
                    return [new_value.get_id()]
            if new_value is None:
                new_value = []
                return new_value
            if not isinstance(new_value, list):
                new_value = [new_value]
            return new_value

    async def _calc_func_args_by_change(
            self, func_params, func_arg_list, control_value, **kwargs):
        if self.edit_column:
            arg_value_dict = dict()
            func_arg_length = len(func_params) - 1
            for index, func_arg in enumerate(func_arg_list):
                # index: 0 模型对象  1 旧值  2 新值
                if index == 0:
                    arg_value = self.parent.model_obj
                elif index == 1:
                    arg_value = await self._get_old_value(kwargs)
                elif index == 2:
                    arg_value = await self._get_new_value(kwargs)
                else:
                    arg_value = await self._calc_func_arg_value(
                        func_arg_length, index, func_arg, func_params,
                        control_value, **kwargs)
                arg_name = func_arg.name
                arg_value_dict.update({arg_name: arg_value})
            return arg_value_dict
        else:
            return await super()._calc_func_args_by_change(
                func_params, func_arg_list, control_value, **kwargs)

    def normal_field_editor_result(
            self, data_value_editor, value, column_format, font_color_value=None, fill_color_value=None):
        value_dict = {
            "name": None, "icon": None, "value": None,
            "font_color": font_color_value,
            "fill_color": fill_color_value
        }
        data_column = data_value_editor.column
        if data_column:
            column_lemon_type = data_column.lemon_type
            column_format['separator'] = getattr(
                data_value_editor, 'separator', None)
            data = public_data_format(
                value, data_column, column_format)
            # 目前设计时按钮选择多端字段时，未选择聚合类型
            if isinstance(data, list):
                data = ",".join(["" if v is None else str(v) for v in data])
            value_dict.update({"name": data})
            if data and column_lemon_type == FieldType.ENUM:
                name = data.get("name")
                if "icon" in data:
                    icon = data.get("icon", None)
                    value_dict.update({"icon": icon})
                if "color" in data:
                    font_color = data.get("color", None)
                    value_dict.update({"font_color": font_color})
                value_dict.update({"name": name})
        return value_dict


class BaseInputComponent(BaseControlComponent):

    base_type = BaseComponentType.INPUT

    def _find_edit_column(self):
        field_info = self.control_dict.get("field_info")
        lemon_column, field_path = self._find_column(field_info)
        self.edit_field_info = field_info
        # 关联必填时设计时会改变这个值以达到必填效果
        self.relation_required = field_info.get(
            "placeholder", {}).get("relation_required", False)
        self._edit_column = lemon_column
        self._edit_column_path = field_path
        self._edit_column_set = True
        self.is_required = self.component_dict.get("is_required", False)
        # 组件默认值配置
        self.edit_default_value = field_info.get("edit_default_value", {})
        self.is_setting_default = field_info.get("is_setting_default", None)
        if self.is_setting_default is None:
            if field_info.get("edit_default_value", {}):
                self.is_setting_default = True
        return self._edit_column

    @property
    def edit_column(self):
        if self._edit_column_set:
            return self._edit_column
        else:
            return self._find_edit_column()

    def find_all_columns(self):
        super().find_all_columns()
        self.find_title_columns()
        self.find_tooltip_columns()

    @decorator_helper.once_editor
    async def title(self, control_value, settings_value=None, **kwargs):
        title_dict = self.control_dict.get("title")
        title = await self._calc_editor(
            title_dict, calc_data=True, **control_value)
        if title:
            self.title_name = title
        return title

    @decorator_helper.once_editor
    async def tooltip(self, control_value, settings_value=None, **kwargs):
        tooltip_dict = self.control_dict.get("tooltip", {})
        value_dict = tooltip_dict.get("value")
        tooltip = await self._calc_editor(
            value_dict, **control_value)
        return tooltip

    @decorator_helper.once_editor
    async def editable(
        self, control_value, settings_value=None, **kwargs):
        editable_dict = self.component_dict.get("editable")
        parent = self.parent
        parent = getattr(parent, "control_obj", parent) or parent
        parent_editable_value = getattr(parent, "editable_value", None)
        editable_dict = self.control_dict.get("editable")
        control_uuid = self.control_dict.get("uuid")
        default, extend_value, extend_op = None, True, 1
        if parent_editable_value is False:
            extend_value = False
        if extend_value and settings_value and isinstance(settings_value, dict):
            extend_value = settings_value.get(
                control_uuid, {}).get("editable", True)
        # _calc_editor 时，会根据 meaning 自动赋值到 _editable_value
        editable_value = await self._calc_editor(
            editable_dict, default=default, extend_value=extend_value,
            extend_op=extend_op, meaning=ValueEditorMeaning.EDITABLE,
            update_dynamic_editor=True, **control_value)
        return editable_value

    @decorator_helper.once_editor
    async def placeholder(self, control_value, settings_value=None, **kwargs):
        placeholder_dict = self.control_dict.get("placeholder")
        placeholder = await self._calc_editor(placeholder_dict, **control_value)
        return placeholder

    @decorator_helper.once_editor
    async def new_page(self, control_value, settings_value=None, **kwargs):
        new_page_dict = self.control_dict.get(
            "new_page", {}).get("page_title", {})
        return await self._calc_editor(new_page_dict, **control_value)

    @decorator_helper.once_editor
    async def view_page(self, control_value, settings_value=None, **kwargs):
        view_page_dict = self.control_dict.get(
            "view_page", {}).get("page_title", {})
        control_uuid = self.control_dict.get("uuid")
        extend_value = None
        if settings_value and isinstance(settings_value, dict):
            extend_value = settings_value.get(
                control_uuid, {}).get("visible", True)
        return await self._calc_editor(
            view_page_dict, extend_value=extend_value, **control_value)

    async def dropdown(self):
        select_in_column = await self.select_data()
        return {self.uuid: select_in_column}

    async def new(self):
        pass

    async def edit_result(self, control_value):
        if self.edit_control_type == ComponentType.SELECT:
            return self.calc_select_result(control_value)
        return self.calc_input_result(control_value)

    async def batch_result(self, control_values, form_data):
        edit_results = [await self.edit_result(control_value) for control_value in control_values]
        for index in range(len(control_values)):
            form_data[index].update({self.uuid: edit_results[index]})

    async def result(self, control_value, form_data, children_data, need_validate=False):
        if not need_validate or self.receive_linkage:
            if isinstance(control_value, list):
                await self.batch_result(control_value, form_data)
            else:
                edit_result = await self.edit_result(control_value)
                form_data.update({self.uuid: edit_result})

    @decorator_helper.once_editor
    async def serial_ports(self, control_value, settings_value=None, **kwargs):
        serial_ports_dict = self.control_dict.get("serial_ports")
        # control_uuid = self.control_dict.get("uuid")
        serial_ports = await self._calc_editor(serial_ports_dict)
        return serial_ports

    @decorator_helper.once_editor
    async def unit(self, control_value, settings_value=None, **kwargs):
        unit_dict = self.control_dict.get("unit")
        # control_uuid = self.control_dict.get("uuid")
        unit = await self._calc_editor(unit_dict)
        return unit

    @decorator_helper.once_editor
    async def input_external_adapter(self, control_value, settings_value=None, **kwargs):
        if self.control_dict.get("type") in [ComponentType.INPUT]:
            input_external_adapter = self.control_dict.get("input_external_adapter", {})
            for k, w_value in input_external_adapter.items():
                await self._calc_editor(w_value)

    async def get_default_value(self):
        field_info = self.control_dict.get("field_info")
        if not field_info:
            return False, dict()
        # 组件默认值配置
        if self.is_setting_default:
            update_default, default_value_dict = await self.n_get_default_value()
        else:
            update_default, default_value_dict = False, dict()
        return update_default, default_value_dict


class BasePresentComponent(BaseControlComponent, BaseSelect):

    base_type = BaseComponentType.PRESENT

    def find_all_columns(self):
        super().find_all_columns()
        self.find_bg_color_columns()
        self.find_tooltip_columns()
        self.find_title_columns()
        self.find_view_page_title()
        self.find_modal_page_title()

    def _find_edit_column(self):
        field_info = self.control.get("edit_field_info", {})
        lemon_column, field_path = self._find_column(field_info)
        self._edit_column = lemon_column
        self._edit_column_path = field_path
        if isinstance(field_info, dict) and field_info:
            self.relation_required = field_info.get(
                "placeholder", {}).get("relation_required", False)
        self._edit_column_set = True
        self.is_required = self.component_dict.get("is_required", False)
        self.edit_default_value = field_info.get("edit_default_value", {})
        self.data_source = self.control.get("edit_field_data_source", {})
        self.data_source_type = self.data_source.get(
            "type", DataSourceType.RTREE_HIERARCHY)
        self.is_setting_default = self.control.get("field_info", {}).get(
            "is_setting_default", None)
        if self.is_setting_default is None:
            if self.control.get("field_info", {}).get("edit_default_value", {}):
                self.is_setting_default = True
        return self._edit_column

    @property
    def edit_column(self):
        if self._edit_column_set:
            return self._edit_column
        else:
            return self._find_edit_column()

    # 计算值编辑器
    @decorator_helper.once_editor
    async def title(self, control_value, settings_value=None, **kwargs):
        show_title = self.control_dict.get("show_title", True)
        title_dict = self.control_dict.get("title")
        title = ""
        if show_title:
            title = await self._calc_editor(title_dict, calc_data=True, **control_value)
        self.title_name = title
        return title

    # 获取计算值编辑器数据
    def find_title_columns(self):
        show_title = self.control_dict.get("show_title", True)
        title_dict = self.control_dict.get("title", {})
        if title_dict and show_title:
            value_editor = self.create_editor(title_dict)
            return self._find_editor_columns(value_editor)
        return dict()

    @decorator_helper.once_editor
    async def bg_color(self, control_value, settings_value=None, **kwargs):
        bg_color_dict = self.control.get("bg_color")
        bg_color = None
        if bg_color_dict:
            bg_color = await self._calc_editor(bg_color_dict, **control_value)
        return bg_color

    def find_bg_color_columns(self):
        color_dict = self.control.get("bg_color", {})
        if color_dict:
            value_editor = self.create_editor(color_dict)
            return self._find_editor_columns(value_editor)
        return dict()

    def find_class_name_columns(self):
        class_name_dict = self.control_dict.get("class_name", {})
        self._find_editor_columns(class_name_dict)

    async def calc_class_name(self, control_value):
        class_name_dict = self.control_dict.get("class_name", {})
        class_name = await self._calc_editor(class_name_dict, **control_value)
        return class_name

    @decorator_helper.once_editor
    async def modal_page_title(self, control_value, settings_value=None, **kwargs):
        modal_view_page_title = self.control.get("modal_view_page", {}).get("page_title", {})
        modal_view_value_editor = None
        if modal_view_page_title:
            modal_view_value_editor = await self._calc_editor(modal_view_page_title, **control_value)
        return modal_view_value_editor

    @decorator_helper.once_editor
    async def view_page_title(self, control_value, settings_value=None, **kwargs):
        view_page_title = self.control.get("view_page", {}).get("page_title", {})
        view_value_editor = None
        if view_value_editor:
            view_page_title = await self._calc_editor(view_page_title, **control_value)
        return view_page_title

    def find_view_page_title(self):
        view_page_title = self.control.get("view_page", {}).get("page_title", {})
        if view_page_title:
            view_value_editor = self.create_editor(view_page_title)
            return self._find_editor_columns(view_value_editor)
        return dict()

    def find_modal_page_title(self):
        modal_view_page_title = self.control.get("modal_view_page", {}).get("page_title", {})
        if modal_view_page_title:
            modal_view_value_editor = self.create_editor(modal_view_page_title)
            return self._find_editor_columns(modal_view_value_editor)
        return dict()

    @decorator_helper.once_editor
    async def edit_tip(self, control_value, settings_value=None, **kwargs):
        edit_tip_dict = self.control.get("edit_tip", {})
        edit_tip_value = None
        if edit_tip_dict:
            edit_tip_value = await self._calc_editor(edit_tip_dict, **control_value)
        return edit_tip_value

    def find_tooltip_columns(self):
        edit_tip_dict = self.control.get("edit_tip", {})
        if edit_tip_dict:
            value_editor = self.create_editor(edit_tip_dict)
            return self._find_editor_columns(value_editor)
        return dict()

    async def get_edit_value_result(self, control_value, update_form_row_data=True):
        edit_value_result = dict()
        if self.edit_column is not None:
            if (
                self.edit_column.model == self.data_model
                and
                not self.edit_column_path
            ):
                if isinstance(control_value, list) and control_value:
                    # 输入组件一定保证 control_value 是个 dict
                    control_value = control_value[0]
                if self.edit_control_type == ComponentType.SELECT:
                    edit_value_result = self.calc_select_result(
                        control_value, update_form_row_data=update_form_row_data)
                else:
                    edit_value_result = self.calc_input_result(
                        control_value, update_form_row_data=update_form_row_data)
            elif self.edit_control_type in [
                ComponentType.DATALIST, ComponentType.CARDLIST,
                ComponentType.DATAGRID, ComponentType.TREELIST,
                ComponentType.R_SELECT, ComponentType.R_SELECT_POPUP,
                ComponentType.R_TILE
            ]:
                edit_value_result = self.calc_relation_result(
                    control_value, update_form_row_data=update_form_row_data)
            elif self.edit_control_type in [
                ComponentType.R_CASCADE, ComponentType.R_TREE
            ]:
                edit_value_result = await self.calc_hierarchy_result(
                    control_value, update_form_row_data=update_form_row_data)
        return edit_value_result

    async def edit_result(self, control_value):
        return await self.get_edit_value_result(control_value)

    async def batch_edit_result(self, control_values):
        edit_results = [
            await self.get_edit_value_result(control_value, update_form_row_data=False)
            for control_value in control_values
        ]
        return edit_results

    async def batch_view_result(self, control_values):
        return [[] for _ in range(len(control_values))]

    async def batch_result(self, control_values, children_data):
        edit_results = await self.batch_edit_result(control_values)
        view_results = await self.batch_view_result(control_values)
        for index in range(len(control_values)):
            children_data[index].update({self.uuid: {"edit": edit_results[index], "view": view_results[index]}})

    async def single_result(self, control_value, form_data, children_data, need_validate=False):
        if not need_validate:
            edit_result = await self.edit_result(control_value)
            view_result = await self.view_result(control_value)
            children_data.update(
                {self.uuid: {"edit": edit_result, "view": view_result}})
        else:
            update_data = {}
            if self.receive_linkage:
                edit_result = await self.edit_result(control_value)
                update_data.update({"edit": edit_result})
            if self.view_receive_linkage:
                view_result = await self.view_result(control_value)
                update_data.update({"view": view_result})
            if update_data:
                children_data.update({self.uuid: update_data})

    async def result(self, control_value, form_data, children_data, need_validate=False):
        if isinstance(control_value, list):
            await self.batch_result(control_value, children_data)
        else:
            await self.single_result(control_value, form_data, children_data, need_validate)

    async def dropdown(self):
        select_in_column = []
        if (
            self.edit_column.model == self.data_model
            and
            not self.edit_column_path
        ):
            select_in_column = []
        elif self.edit_control_type in [
            ComponentType.R_SELECT, ComponentType.R_SELECT_POPUP,
            ComponentType.R_TILE
        ]:
            expand_columns = None
            path_columns = None
            show_multi_column = self.control.get("field_info", {}).get("show_multi_column")
            if show_multi_column:
                expand_columns = self.control_dict.get("expand_columns", [])
                path_columns = dict()
                for column_dict in expand_columns:
                    editor = self.create_editor(column_dict)
                    if editor:
                        path_columns.update(editor.path_columns)
            select_in_column = await self.r_select_data(expand_columns=expand_columns, path_columns=path_columns)
        elif self.edit_control_type == ComponentType.R_CASCADE:
            select_in_column = await self.r_cascade_select_data()
        elif self.edit_control_type == ComponentType.R_TREE:
            select_in_column = await self.r_cascade_select_data(
                self.data_source_type)
        if self.edit_control_type in [ComponentType.SELECT]:
            select_data_source = self.control.get("component", {}).get("select_data_source")
            select_type = select_data_source.get("type") if select_data_source else None
            if select_type == DataSourceType.SELECT_FUNC:
                select_in_column = await self.select_data(select_data_source)
        return {self.uuid: select_in_column}

    async def _check_field(self, check_value):
        if self.edit_control_type == ComponentType.INPUT:
            return await self.field_check_service.input_check_field(
                check_value)
        elif self.edit_control_type == ComponentType.TEXTAREA:
            return await self.field_check_service.textarea_check_field(
                check_value)
        elif self.edit_control_type == ComponentType.CHECKBOX:
            return await self.field_check_service.checkbox_check_field(
                check_value)
        elif self.edit_control_type == ComponentType.SELECT:
            select_type = None
            if self.field_check_service.control_dict.get("select_data_source"):
                select_type = self.field_check_service.control_dict.get("select_data_source").get("type")
            return await self.field_check_service.select_check_field(
                check_value, select_type)
        elif self.edit_control_type == ComponentType.DATETIME:
            return await self.field_check_service.datetime_check_field(
                check_value)
        elif self.edit_control_type in [
            ComponentType.R_SELECT, ComponentType.R_SELECT_POPUP,
            ComponentType.R_TILE, ComponentType.R_CASCADE, ComponentType.R_TREE
        ]:
            return await self.field_check_service.r_select_check_field(
                check_value)
        elif self.edit_control_type in [
                ComponentType.UPLOAD_FILE, ComponentType.UPLOAD_IMAGE]:
            return await self.field_check_service.upload_check_field(
                check_value)
        else:
            return self.field_check_service.validate_result

    async def get_default_value(self):
        edit_field_info = self.control.get("edit_field_info", {})
        # field_info = self.control.get("field_info", {})
        # self.is_setting_default = field_info.get("is_setting_default", False)
        if self.is_setting_default:
            if self.edit_control_type in [
                ComponentType.R_SELECT, ComponentType.R_SELECT_POPUP,
                ComponentType.R_TILE
            ]:
                is_many = edit_field_info.get("placeholder", {}).get("is_many", 0)
                update_default, default_value_dict = await self.r_get_default_value(is_many)
            else:
                update_default, default_value_dict = await self.n_get_default_value()
        else:
            update_default, default_value_dict = False, dict()
        return update_default, default_value_dict


class BaseButtonComponent(BaseControlComponent):

    base_type = BaseComponentType.BUTTON
    data_convert = False

    @decorator_helper.once_editor
    async def loading(self, control_value=None, settings_value=None, **kwargs):
        loading_dict = self.control_dict.get("loading")
        loading = await self._calc_editor(loading_dict, to_str=False, **control_value)
        return loading

    @decorator_helper.once_editor
    async def button_font_color(
            self, control_value=None, settings_value=None, **kwargs):
        font_color = await self._calc_button_style_color("fontColor", control_value)
        return font_color

    @decorator_helper.once_editor
    async def button_background_color(
            self, control_value=None, settings_value=None, **kwargs):
        background_color = await self._calc_button_style_color("backgroundColor", control_value)
        return background_color

    @decorator_helper.once_editor
    async def button_class_name(
            self, control_value=None, settings_value=None, **kwargs):
        style_dict = self.control_dict.get("style", {})
        class_name_dict = style_dict.get("class_name_value_editor")
        class_name = await self._calc_editor(class_name_dict, **control_value)
        return class_name

    @decorator_helper.once_editor
    async def badge(self, control_value, settings_value=None, **kwargs):
        badge = self.component_dict.get("badge", {})
        show = badge.get("show", False)
        if show:
            return await self._calc_badge(control_value, badge)

    async def _calc_button_style_color(self, field, control_value):
        color = None
        style_dict = self.control_dict.get("style", {})
        color_dict = style_dict.get(field, {})
        color_type = color_dict.get("type")
        if color_type == "2":
            color_editor = color_dict.get("color")
            color = await self._calc_editor(color_editor, **control_value)
        return color

    def find_color_columns_by_type(self, style_dict, key):
        color_dict = style_dict.get(key, {})
        color_type = color_dict.get("type")
        if color_type == "2":
            color_editor = color_dict.get("color")
            value_editor = self.create_editor(color_editor)
            self._find_editor_columns(value_editor)

    def find_style_columns(self):
        style_dict = self.control_dict.get("style", {})
        self.find_color_columns_by_type(style_dict, "fontColor")
        self.find_color_columns_by_type(style_dict, "backgroundColor")
        # 动态定义 className
        self._find_normal_editor_columns(self.control_dict, "style", "class_name_value_editor")

    def find_badge_columns(self):
        data_source = self._find_normal_editor_columns(self.control_dict, "badge", "data_source")
        color = self._find_normal_editor_columns(self.control_dict, "badge", "color")
        return data_source, color

    async def calc_data_value_editor(self, control_value):
        data_editor_dict = self.control_dict.get("title")
        data_value_editor, value = None, None
        if data_editor_dict:
            data_value_editor = self.lemon_editor.init(data_editor_dict)
            value = await self.lemon_editor(data_editor_dict, **control_value)
        return data_value_editor, value

    def get_column_format(self):
        column_format = self.control_dict.get("format", {})
        if column_format is None:
            column_format = {}
        column_format = column_format if isinstance(column_format, dict) else {}
        return column_format

    def process_data_value_editor(self, data_value_editor, value, column_format):
        value_dict = {
            "name": None, "icon": None, "value": None
        }
        if data_value_editor:
            if data_value_editor.type == ValueEditorType.FIELD:
                value_dict = self.normal_field_editor_result(
                    data_value_editor, value, column_format)
            else:
                value_dict = {
                    "name": value, "icon": None, "value": None
                }
        return value_dict

    async def view_result(self, control_value):
        data_value_editor, value = await self.calc_data_value_editor(control_value)
        column_format = self.get_column_format()
        value_dict = self.process_data_value_editor(data_value_editor, value, column_format)
        return value_dict

    async def batch_view_result(self, control_values):
        data_value_editor, values = await self.calc_data_value_editor({})
        column_format = self.get_column_format()
        args_list = []
        for index in range(len(control_values)):
            value = values[index] if isinstance(values, list) else None
            args_list.append([data_value_editor, value, column_format])
        value_results = [self.process_data_value_editor(*args) for args in args_list]
        return value_results

    async def batch_result(self, control_values, children_data):
        view_results = await self.batch_view_result(control_values)
        for index in range(len(control_values)):
            children_data[index].update({self.uuid: {"view": view_results[index]}})

    async def result(self, control_value, form_data, children_data, need_validate=False):
        if isinstance(control_value, list):
            await self.batch_result(control_value, children_data)
        else:
            view_result = {}
            if not need_validate or self.view_receive_linkage:
                view_result = await self.view_result(control_value)
            children_data.update(
                {self.uuid: {"view": view_result}})


class BaseNavigationComponent(BaseControlComponent):
    base_type = BaseComponentType.NAVIGATION
    data_convert = False

    def get_column_format(self):
        column_format = self.control_dict.get("format", {})
        if column_format is None:
            column_format = {}
        column_format = column_format if isinstance(column_format, dict) else {}
        return column_format

    def process_data_value_editor(self, data_value_editor, value, column_format):
        value_dict = {
            "name": None, "icon": None, "value": None
        }
        if data_value_editor:
            if data_value_editor.type == ValueEditorType.FIELD:
                value_dict = self.normal_field_editor_result(
                    data_value_editor, value, column_format)
            else:
                value_dict = {
                    "name": value, "icon": None, "value": None
                }
        return value_dict

    async def result(self, control_value, form_data, children_data, need_validate=False):
        pass


class BaseExtendComponent(BaseDataComponent):

    base_type = BaseComponentType.EXTEND
    data_convert = False


class BaseVisualComponent(BaseComponent):

    base_type = BaseComponentType.VISUAL
    data_convert = False


class BaseContainerComponent(BaseControlComponent):

    base_type = BaseComponentType.CONTAINER
    drag_type = True

    def find_title_columns(self):
        title_dict = self.component_dict.get("title", {})
        value_editor = self.create_editor(title_dict)
        return self._find_editor_columns(value_editor)

    def find_bg_color_columns(self):
        bg_color_dict = self.component_dict.get("bg_color", {})
        value_editor = self.create_editor(bg_color_dict)
        return self._find_editor_columns(value_editor)

    def _find_all_columns(self):
        self.find_title_columns()
        self.find_bg_color_columns()
        super()._find_all_columns()

    @decorator_helper.once_editor
    async def title(self, control_value, settings_value=None, **kwargs):
        title_dict = self.component_dict.get("title")
        title = None
        if title_dict:
            title = await self._calc_editor(title_dict, **control_value)
        return title

    @decorator_helper.once_editor
    async def bg_color(self, control_value, settings_value=None, **kwargs):
        bg_color_dict = self.component_dict.get("bg_color")
        bg_color = None
        if bg_color_dict:
            bg_color = await self._calc_editor(bg_color_dict, **control_value)
        return bg_color

    async def result(self, control_value, form_data, children_data, need_validate=False):
        pass


class GridComponent(BaseContainerComponent):

    component_type = ComponentType.GRID

class GridRowComponent(BaseContainerComponent):

    component_type = ComponentType.GRID_ROW

class GridColComponent(BaseContainerComponent):

    component_type = ComponentType.GRID_COL


class Tabs(BaseContainerComponent):

    component_type = ComponentType.TABS

class Tab(BaseContainerComponent):

    component_type = ComponentType.TAB

    def _find_all_columns(self):
        super()._find_all_columns()
        self.find_badge_columns()

    def find_badge_columns(self):
        data_source = self._find_normal_editor_columns(self.control_dict, "badge", "data_source")
        color = self._find_normal_editor_columns(self.control_dict, "badge", "color")
        return data_source, color

    @decorator_helper.once_editor
    async def badge(self, control_value, settings_value=None, **kwargs):
        badge = self.component_dict.get("badge", {})
        show = badge.get("show", False)
        if show:
            return await self._calc_badge(control_value, badge)

class Container(BaseContainerComponent):

    component_type = ComponentType.CONTAINER
    component_name = PageToolName.CONTAINER

    def find_all_columns(self):
        self.find_class_name_columns()
        super().find_all_columns()

    def find_bg_color_columns(self):
        bg_color_dict = self.component_dict.get("back_color", {}).get("editor", {})
        value_editor = self.create_editor(bg_color_dict)
        return self._find_editor_columns(value_editor)

    @decorator_helper.once_editor
    async def bg_color(self, control_value, settings_value=None, **kwargs):
        bg_color_dict = self.component_dict.get("back_color", {}).get("editor")
        bg_color = None
        if bg_color_dict:
            bg_color = await self._calc_editor(bg_color_dict, **control_value)
        return bg_color

    def find_class_name_columns(self):
        style_dict = self.component_dict.get("style", {})
        class_name_dict = style_dict.get("class_name", {})
        self._find_editor_columns(class_name_dict)

    async def calc_class_name(self, control_value):
        style_dict = self.component_dict.get("style", {})
        class_name_dict = style_dict.get("class_name", {})
        class_name = await self._calc_editor(class_name_dict, **control_value)
        return class_name

    @property
    def control_uuid(self):
        # 容器 组件计算的值编辑器值，直接挂到它的 父数据容器 上
        return None

    @decorator_helper.once_editor
    async def class_name(self, control_value=None, setting_value=None, **kwargs):
        #动态定义 className
        return await self.calc_class_name(control_value)


class Collapse(BaseContainerComponent):

    component_type = ComponentType.COLLAPSE


class Panel(BaseContainerComponent):

    component_type = ComponentType.PANEL

    def _find_all_columns(self):
        super()._find_all_columns()
        self.find_badge_columns()

    def find_badge_columns(self):
        data_source = self._find_normal_editor_columns(self.control_dict, "badge", "data_source")
        color = self._find_normal_editor_columns(self.control_dict, "badge", "color")
        return data_source, color

    @decorator_helper.once_editor
    async def badge(self, control_value, settings_value=None, **kwargs):
        badge = self.component_dict.get("badge", {})
        show = badge.get("show", False)
        if show:
            return await self._calc_badge(control_value, badge)


class SplitPage(BaseContainerComponent):

    component_type = ComponentType.SPLIT_PAGE


class Split(BaseContainerComponent):

    component_type = ComponentType.SPLIT

    def _find_all_columns(self):
        super()._find_all_columns()
        self.find_badge_columns()

    def find_badge_columns(self):
        data_source = self._find_normal_editor_columns(self.control_dict, "badge", "data_source")
        color = self._find_normal_editor_columns(self.control_dict, "badge", "color")
        return data_source, color

    @decorator_helper.once_editor
    async def badge(self, control_value, settings_value=None, **kwargs):
        badge = self.component_dict.get("badge", {})
        show = badge.get("show", False)
        if show:
            return await self._calc_badge(control_value, badge)


class DropdownMenu(BaseContainerComponent):

    component_type = ComponentType.DROPDOWN_MENU
    component_name = PageToolName.DROPDOWN_MENU

    def find_all_columns(self):
        super().find_all_columns()
        self.find_text_content_columns()
    
    def find_text_content_columns(self):
        text_content_dict = self.control_dict.get("dropdown_text_content", {})
        value_editor = self.create_editor(text_content_dict)
        return self._find_editor_columns(value_editor)

    @decorator_helper.once_editor
    async def text_content(self, control_value, settings_value=None, **kwargs):
        style = self.component_dict.get("dropdown_style", 0)
        if style == DropdownStyle.TEXT_STYLE or style == DropdownStyle.BUTTON_STYLE:
            text_content = self.component_dict.get("dropdown_text_content", {})
            return await self._calc_editor(text_content, **control_value)

    async def dropdown(self, parent):
        menu_content_data = {}
        control_value = getattr(parent, "source_in_page", None)
        dropdown = self.control_dict.get("dropdown", [])
        for menu in dropdown:
            menu_uuid = menu.get("uuid")
            menu_control_obj = parent.data_controls.get(menu_uuid)
            menu_content_data.update(await menu_control_obj.calc_menu_content(control_value))
        return menu_content_data


CONTAINER_COMPONENT_CLASSES = find_locals_subclass(
    BaseContainerComponent, locals().values())

