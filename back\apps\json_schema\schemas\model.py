from apps.json_schema.const import ScopeType, UniqueName, SchemaKeys as SK
from apps.entity import Page as PageTable
from apps.json_schema.schemas.utils import gen_const_condition_schema
from apps.ide_const import (
    LemonDesignerErrorCode as <PERSON>DE<PERSON>, PageAttr, FormAttr, ComponentType)


model_schema = {
    "is_element": True,
    "type": "object",
    "properties": {
        "models": {
            "type": "array",
            "items": {
                "type": "object",
                "is_element": True,
                SK.properties: {
                    "fields": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "attr_name": "字段",
                            "is_element": True,
                            "properties": {
                                "enum": {
                                    "$ref": "mem://common/enum_ref"
                                },
                                "calculate_field": {
                                    "type": "boolean"
                                }
                            },
                            "allOf": [
                                {
                                    "if": {
                                        "properties": {
                                            "calculate_field": {
                                                "const": True
                                            }
                                        }
                                    },
                                    "then": {
                                        "allOf": [
                                            gen_const_condition_schema(0, ref="mem://common/value_editor_all",
                                                                    const_key="calculate_type",
                                                                    then_property="calculate_function"),
                                            gen_const_condition_schema(1, ref="mem://common/func_ref",
                                                                    const_key="calculate_type",
                                                                    then_property="calculate_func")
                                        ]
                                    }
                                },
                                {
                                    "if": {
                                        "properties": {
                                            "aggre_field": {
                                                "const": True
                                            },
                                            "aggre_func": {
                                                "type": "object",
                                                "properties": {
                                                    "calc": {
                                                        "const": 6
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    "then": {
                                        "properties": {
                                            "aggre_func": {
                                                "type": "object",
                                                "properties": {
                                                    "func": {
                                                        "$ref": "mem://common/func_ref"
                                                    },
                                                    "params": {
                                                        "type": "array",
                                                        "items": {
                                                            "$ref": "mem://common/field_ref"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "events": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "func": {
                                    "$ref": "mem://common/func_ref"
                                },
                                "error_tip": {
                                    "$ref": "mem://common/value_editor_all"
                                }
                            }
                        }
                    }
                }
            }
        },
        "relationships": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "target_model": {
                        "$ref": "mem://common/model_ref_hide"
                    }
                }
            }
        },
        "system_relationships": {}
    }
}
