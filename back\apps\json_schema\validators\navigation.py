from apps.json_schema.validators.base import BaseValidator
from apps.json_schema.refresolver import RefResolver
from apps.json_schema.context import AppCtx, AppModel
from apps.utils import PageFinder
from apps.ide_const import Document
from apps.json_schema.utils import id_of
from loguru import logger


schema = {
    "is_element": True,
    "attr_name": "导航",
    "type": "object",
    "properties": {
        "navigations": {
            "type": "array",
            "items": {
                "is_element": {
                    "uuid_key": "navigation_uuid",
                    "type_key": "platform"
                },
                "type": "object",
                "properties": {
                    "main_page": {
                        "$ref": "mem://common/page_ref"
                    },
                    "attr": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "page_uuid": {
                                    "$ref": "mem://common/page_ref"
                                },
                                "children": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "page_uuid": {
                                                "$ref": "mem://common/page_ref"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


class NavigationValidator(BaseValidator):
    def __init__(self, app_ctx, version: str = '1.0') -> None:
        super().__init__(app_ctx, version)
        self.document_type = Document.TYPE.MODEL
        self.schema = schema
        self.resolver = RefResolver(base_uri=id_of(schema), referrer=schema)
        self.model_in_page = {}
