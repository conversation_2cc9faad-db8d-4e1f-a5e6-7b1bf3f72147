# -*- coding:utf-8 -*-

from baseutils.log import app_log
from apps.entity import Connector as ConnectorModel
from apps.exceptions import CheckNameError, CheckU<PERSON><PERSON>rror, CheckUUIDUniqueError
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.ide_const import  Connector
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker


class ConnectorCheckerService(CheckerService):
    
    attr_class = Connector.ATTR
    uuid_error = LDEC.CONNECTOR_UUID_UNIQUE_ERROR
    uuid_unique_error = LDEC.CONNECTOR_UUID_UNIQUE_ERROR
    name_error = LDEC.CONNECTOR_NAME_FAILED
    name_unique_error = LDEC.CONNECTOR_UUID_UNIQUE_ERROR

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, document_name: str, 
        element: dict,  *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        
    def initialize(self):
        super().initialize()
        self.open_type = self.element.get("open_type", 0)
        self.children = self.element.get("children", list())
        self.component_uuid_set = set()
        self.component_name_set = set()
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            ConnectorModel.app_uuid.name: self.app_uuid,
            ConnectorModel.module_uuid.name: self.module_uuid,
            ConnectorModel.document_uuid.name: self.document_uuid,
            ConnectorModel.connector_uuid.name: self.element_uuid,
            ConnectorModel.connector_name.name: self.element_name,
            ConnectorModel.ext_tenant.name: self.ext_tenant,
        }
    
    def build_update_query_data(self):
        return {
            ConnectorModel.connector_name.name: self.element_name,
            ConnectorModel.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return ConnectorModel.update(**query_data).where(
            ConnectorModel.connector_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(connector_uuid):
        return ConnectorModel.update(**{
                ConnectorModel.is_delete.name: True
            }).where(ConnectorModel.connector_uuid==connector_uuid)
    
    def check_modify(self, document_connector_uuid_set):
        if self.element_uuid in document_connector_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
          
class ConnectorDocumentCheckerService(DocumentCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict, document_version: int, 
        app_model_uuid_set: set, 
        app_field_uuid_set: set, app_func_uuid_dict: dict, 
        app_connector_list: list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, ConnectorModel, *args, **kwargs)
        self.document_version = document_version
        self.app_model_uuid_set = app_model_uuid_set
        self.app_field_uuid_set = app_field_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_connector_uuid_set = set()
        self.module_connector_name_set = set()
        self.document_connector_uuid_set = set()
        self.ext_tenant = self.kwargs.get("ext_tenant")

        for connector_dict in app_connector_list:
            connector_uuid = connector_dict.get("connector_uuid", "")
            connector_name = connector_dict.get("connector_name", "")
            connector_module_uuid = connector_dict.get("module_uuid", "")
            connector_document_uuid = connector_dict.get("document_uuid", "")

            # 找到原文档中所有的 连接器 ，为了新增、更新、删除文档的 连接器
            if connector_document_uuid == self.document_uuid:
                self.document_connector_uuid_set.add(connector_uuid)
            else:
                # 排除当前文档所有的 connector_uuid ，获取应用的所有 connector_uuid
                self.app_connector_uuid_set.add(connector_uuid)
                # 排除当前文档所有的 connector_name ，获取模块的所有 connector_name
                if connector_module_uuid == self.module_uuid:
                    self.module_connector_name_set.add(connector_name)

    @checker.run
    def check_connector(self):
        connector_checker_service = ConnectorCheckerService(
            self.app_uuid, self.module_uuid, self.module_name,
            self.document_uuid, self.document_name, self.element,
            is_copy=self.is_copy, ext_tenant = self.ext_tenant)
        connector_checker_service.document_other_info = self.document_other_info
        connector_uuid = connector_checker_service.element_uuid
        try:
            connector_checker_service.check_uuid()
            # connector_checker_service.check_uuid_unique(self.app_connector_uuid_set)
            connector_checker_service.check_name()
            connector_checker_service.check_name_unique(self.module_connector_name_set)
            connector_checker_service.check_all()
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(connector_checker_service)
            raise e
        else:
            self.update_any_list(connector_checker_service)

        # 找到新增 或 更新的 连接器
        connector_checker_service.check_modify(self.document_connector_uuid_set)
        if connector_checker_service.insert_query_list:
            self.document_insert_list.extend(connector_checker_service.insert_query_list)
        if connector_checker_service.update_query_list:
            self.document_update_list.extend(connector_checker_service.update_query_list)
        
        # 找出删除的 连接器 ，将其 is_delete 置为 True
        delete_connector_uuid_set = self.document_connector_uuid_set - set([connector_uuid])
        for this_connector_uuid in delete_connector_uuid_set:
            query = connector_checker_service.build_delete_query(this_connector_uuid)
            self.document_delete_list.append(query)
    