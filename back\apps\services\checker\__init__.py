# -*- coding:utf-8 -*-

import copy
import async<PERSON>
import ujson
from apps.base_utils import lemon_uuid

from baseutils.log import app_log
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.entity import ModelBasic
from apps.utils import Lemon<PERSON>esignerErrorDict as LDED
from apps.utils import (
    check_lemon_name, check_lemon_uuid, check_placeholder_exist
)
from baseutils.const import CalculateType, ReturnCode, SYSConfig, TagPermissionAction

from apps.ide_const import DataSourceType, EventAction, FieldType, IDECode, LemonDesignerErrorCode as LDEC, ValueEditorType
from apps.ide_const import Action, PythonKeyWords, Element, Page, LemonKeyWords
from apps.value_editor_utils import LemonBaseValueEditor


class Checker(object):
    
    def run(self, func):
        func._run = True
        return func


checker = Checker()


class CheckerService(object):

    attr_class = Element.ATTR
    uuid_error = LDEC.ELEMENT_UUID_ERROR
    uuid_unique_error = LDEC.ELEMENT_UUID_UNIQUE_ERROR
    name_error = LDEC.ELEMENT_NAME_ERROR
    name_unique_error = LDEC.ELEMENT_NAME_UNIQUE_ERROR
    allow_chinese_name = False  # name 能否使用中文名
    allow_keywords = True  # name 是否可使用 python 语法 等关键字
    allow_lemon_keywords = True
    is_copy = False

    def __init__(
        self, app_uuid: str, module_uuid:str, module_name: str, 
        document_uuid: str, document_name: str, element: dict, *args, **kwargs):
        self.kwargs = kwargs
        self.attr_uuid = self.attr_class.UUID
        self.attr_name = self.attr_class.NAME
        self.app_uuid = app_uuid
        self.module_uuid = module_uuid
        self.module_name = module_name
        self.document_uuid = document_uuid
        self.document_name = document_name
        self.source_element = copy.deepcopy(element)
        self.element = element
        self.element_uuid = self.element.get("uuid", "")
        self.element_name = self.element.get("name", "")
        self.page_size = self.source_element.get("size", [])
        self.page_px_info = self.source_element.get("page_px_info", {})
        self.return_code = ReturnCode()
        self.default_error = LDEC.MODEL_DEFAULT_ERROR
        self.error_list = []
        self.warning_list = []
        self.info_list = []
        self.document_other_info = {"field_dict": {}, "relationship_dict": {}, "model_dict": {},
                                    "page_dict": {}, "func_dict": {}, "enum_dict": {}, 
                                    "print_dict": {}, "label_print_dict": {}, "wf_dict": {}, "enum_item_dict": {},
                                    "uuid": self.element_uuid, "scan_field": {}, "component_copy_dict": {}, 
                                    "old_component_dict": {}, "page_monitor": {}}
        for key, value in self.kwargs.items():
            setattr(self, key, value)
        self.model_in_page = kwargs.get("model_in_page", {})
        self.initialize()
    
    def initialize(self):
        pass
    
    @property
    def is_element_changed(self):
        return not(self.element == self.source_element)
    
    @property
    def component_copy_dict(self):
        return self.document_other_info.get("component_copy_dict")
    
    @property
    def old_component_dict(self):
        return self.document_other_info.get("old_component_dict")
    
    def _build_error_dict(self, attr: str, return_code: ReturnCode, **kwargs):
        error_code, error_message = return_code.code, return_code.message
        error_position = return_code.position
        element_data = kwargs.pop("element_data", None) or self.element
        if element_data.get("type") is not None:
            kwargs.update({"element_type": element_data.get("type")})
        if getattr(return_code, "stop_publish", False):
            kwargs.update({"stop_publish": True})
        if self.kwargs.get("position_info"):
            # 由于组件嵌套关系可能复杂, 仅又element_uuid前端无法确定位置
            # 父组件将可以帮助确认位置的信息放在position_info
            kwargs.update({"position_info": self.kwargs.get("position_info")})
        return LDED(
            error_code=error_code, 
            error_message=error_message, 
            module_name=self.module_name,
            document_name=self.document_name,
            element_uuid=element_data.get("uuid"),
            element_name=element_data.get("name"),
            element_attr=attr,
            error_position=error_position, 
            **kwargs
        )

    def _add_error_list(self, attr: str, return_code: ReturnCode, **kwargs):
        if self.module_uuid == SYSConfig.MODULE_UUID:
            return
        error_dict = self._build_error_dict(
            attr=attr, return_code=return_code, **kwargs)
        self.error_list.append(error_dict)

    def _add_warning_list(self, attr: str, return_code: ReturnCode, element_data=None, **kwargs):
        if self.module_uuid == SYSConfig.MODULE_UUID:
            return
        error_dict = self._build_error_dict(
            attr=attr, return_code=return_code, element_data=element_data, **kwargs)
        self.warning_list.append(error_dict)

    def _add_info_list(self, attr: str, return_code: ReturnCode, element_data=None):
        if self.module_uuid == SYSConfig.MODULE_UUID:
            return
        error_dict = self._build_error_dict(
            attr=attr, return_code=return_code, element_data=element_data)
        self.warning_list.append(error_dict)

    # 检查 uuid 格式，是否正确，如果不通过，会直接报错
    def check_uuid(self):
        attr = self.attr_uuid
        return_code = self.uuid_error
        self._check_uuid(attr=attr, return_code=return_code)

    # 检查 uuid 是否在 页面 中唯一，如果不通过，会直接报错
    def check_uuid_unique(self, uuid_set: set):
        attr = self.attr_uuid
        return_code = self.uuid_unique_error
        self._check_uuid_unique(uuid_set, attr=attr, return_code=return_code)

    # 检查 name 格式，是否正确，如果不通过，会向错误列表添加一条报错信息
    def check_name(self):
        attr = self.attr_name
        return_code = self.name_error
        self._check_name(attr=attr, return_code=return_code,
                         chinese=self.allow_chinese_name)
        if not self.allow_keywords:
            self._check_name_kwords(attr=attr)
        if not self.allow_lemon_keywords:
            self._check_lemon_kwords(attr=attr)

    # 检查 name 是否在 页面 中唯一，如果不通过，会向错误列表添加一条报错信息
    def check_name_unique(self, name_set: set):
        attr = self.attr_name
        return_code = self.name_unique_error
        return_code.message = return_code.message.format(
            name=self.element_name)
        self._check_name_unique(name_set, attr=attr, return_code=return_code)

    def _check_uuid(self, attr: str, return_code: ReturnCode):
        if not check_lemon_uuid(self.element_uuid):
            return_code.message = return_code.message.format(
                name=self.element_name)
            self._add_error_list(attr, return_code)
            raise CheckUUIDError(message=return_code)

    def _check_name(self, attr: str, return_code: ReturnCode, chinese: bool = False):
        if not check_lemon_name(self.element_name, chinese=chinese, size=40):
            app_log.info(f"element_name: {self.element_name}")
            self._add_error_list(attr, return_code)

    def _check_name_error(self, attr: str, return_code: ReturnCode, chinese: bool = False):
        if not check_lemon_name(self.element_name, chinese=chinese):
            app_log.error(f"element_name: {self.element_name}")
            self._add_error_list(attr, return_code)
            raise CheckNameError(message=return_code)

    def _check_name_kwords(self, attr: str):
        name = PythonKeyWords.KWDICT.get(self.element_name)
        if name:
            return_code = IDECode.KEY_WORDS_ERROR
            return_code.message = IDECode.KEY_WORDS_NAME.format(name)
            self._add_error_list(attr, return_code)
            raise CheckNameError(message=return_code)

    def _check_lemon_kwords(self, attr: str):
        name = LemonKeyWords.KWDICT.get(self.element_name)
        if name:
            return_code = IDECode.KEY_WORDS_ERROR
            return_code.message = IDECode.KEY_WORDS_NAME.format(name)
            self._add_error_list(attr, return_code)
            raise CheckNameError(message=return_code)

    def _update_component_copy_dict(self, old_uuid, new_uuid):
        self.component_copy_dict.update({old_uuid: new_uuid})
        self.old_component_dict.update({new_uuid: old_uuid})

    def _check_uuid_unique(self, uuid_set: set, attr: str, return_code: ReturnCode):
        if self.is_copy:
            if self.component_copy_dict.get(self.element_uuid):
                self.element_uuid = self.component_copy_dict.get(
                    self.element_uuid)
                self.element.update({"uuid": self.element_uuid})
            else:
                temp_uuid = lemon_uuid()
                self._update_component_copy_dict(self.element_uuid, temp_uuid)
                self.element.update({"uuid": temp_uuid})
                self.element_uuid = temp_uuid
            return

        if self.element_uuid in uuid_set:
            app_log.error(f"element_uuid: {self.element_uuid}")
            return_code.message = return_code.message.format(
                name=self.element_name)
            self._add_error_list(attr, return_code)
            raise CheckUUIDUniqueError(message=return_code)
        else:
            uuid_set.add(self.element_uuid)

    def _check_name_unique(self, name_set: set, attr: str, return_code: ReturnCode):
        if self.element_name in name_set:
            self._add_error_list(attr, return_code)
        else:
            name_set.add(self.element_name)

    # 检查 条件动作列表
    def _check_action_list(
            self, action_checker_class, action_list, app_func_uuid_dict, action_uuid_set,
            sm_variable_uuid_set, state_variable_uuid_set):
        if not action_list:
            return
        if not isinstance(action_list, list):
            return
        for action in action_list:
            action_checker_service = action_checker_class(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, action)
            try:
                action_checker_service.check_uuid()
                action_checker_service.check_uuid_unique(action_uuid_set)
                action_checker_service.check_name()
                action_checker_service.check_action_type()
                action_checker_service.check_auto()
                action_checker_service.check_func(app_func_uuid_dict)
                action_checker_service.check_run_async()
                action_checker_service.check_sync_type()
                action_checker_service.check_run_delay()
                action_checker_service.check_delay_time()
                action_checker_service.check_arg_list(
                    app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set)
                action_checker_service.check_loop_variable(state_variable_uuid_set)
                action_checker_service.check_control_flow()
                if action_checker_service.type == Action.TYPE.FOR:
                    for_expr = action.get("for_expr", dict())
                    if isinstance(for_expr, dict):
                        for_action_list = for_expr.get("action_list", list())
                        self._check_action_list(
                            action_checker_class, for_action_list, app_func_uuid_dict, 
                            action_uuid_set, sm_variable_uuid_set, state_variable_uuid_set)
                elif action_checker_service.type == Action.TYPE.IF:
                    if_expr = action.get("if_expr", dict())
                    if isinstance(if_expr, dict):
                        if_action_list = if_expr.get("action_list", list())
                        self._check_action_list(
                            action_checker_class, if_action_list, app_func_uuid_dict, 
                            action_uuid_set, sm_variable_uuid_set, state_variable_uuid_set)
                    else_expr = action.get("else_expr", dict())
                    if isinstance(else_expr, dict):
                        else_action_list = else_expr.get("action_list", list())
                        self._check_action_list(
                            action_checker_class, else_action_list, app_func_uuid_dict, 
                            action_uuid_set, sm_variable_uuid_set, state_variable_uuid_set)
                    elif_expr_list = action.get("elif_expr", list())
                    if isinstance(elif_expr_list, list):
                        for elif_expr in elif_expr_list:
                            elif_action_list = elif_expr.get("action_list", list())
                            self._check_action_list(
                                action_checker_class, elif_action_list, app_func_uuid_dict, 
                                action_uuid_set, sm_variable_uuid_set, state_variable_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                raise e 
            else:
                self.update_any_list(action_checker_service)

    '''
    @description: 检查组件所引用字段是否存在
    @Date: 2021-05-18 16:11:08
    @LastEditors: lv
    @param {*} self
    @param {*} field_id
    @param {*} app_field_dict
    @param {*} attr
    @param {*} element_data
    '''
    # def _check_field_exist(self, field_id, app_field_dict, attr, element_data):
    #     if field_id and not app_field_dict.get(field_id):
    #             self._add_error_list(attr, return_code=LDEC.MODEL_FIELD_NOT_EXISTS, dataIndex=element_data.get("dataIndex"))

    def _check_value_editor_normal_field(
            self, field_info, app_field_dict, attr, element_data):
        value_editor_type = field_info.get("type")
        field_id = field_info.get("field")
        # 值编辑器选择字段,该字段不是计算字段,字段类型不为图片/文件/日期循环
        # must_field_and_normal_field: bool = None
        value_editor_type_field = value_editor_type == ValueEditorType.FIELD
        is_normal_field = False
        if value_editor_type_field:
            if field_id:
                model_field_info = app_field_dict.get(field_id)
                if not model_field_info:
                    self._add_error_list(
                        attr, return_code=LDEC.MODEL_FIELD_NOT_EXISTS,
                        dataIndex=element_data.get("dataIndex"))
                else:
                    field_type = model_field_info.get("field_type")
                    is_calc_field = model_field_info.get("calculate_field")
                    is_generated_column = model_field_info.get("calculate_type") == CalculateType.GENERATED_COLUMN
                    # 这里认为生成列是普通字段
                    is_calc_field = is_calc_field and not is_generated_column
                    if not any(
                        [field_type in (
                            FieldType.IMAGE, FieldType.FILE,
                            FieldType.DATETIME_CYCLE),
                         is_calc_field]
                    ):
                        is_normal_field = True
        return value_editor_type_field, is_normal_field

    def _check_value_editor(
            self, value_editor, attr, app_field_dict=None,
            app_relationship_dict=None, r_finder=None, position=None, check_parent_model=False):
        # check_parent_model 是否检查字段的关联的起始模型与父容器的模型相同，需要传入 r_finder
        if not value_editor:
            return_code = LDEC.VALUE_EDITOR_FIELD_NOT_EXIST
            return_code.position = return_code.position if not position else position
            self._add_error_list(
                attr, return_code=return_code,
                element_data=self.element)
        else:
            attr = attr or self.get_attr_by_name("DATASOURCE")
            self.update_reference_by_value_edit(
                value_editor, self.element, attr)
            value_editor_type = value_editor.get("type")
            if value_editor_type == ValueEditorType.FIELD:
                field = value_editor.get("field")
                if field not in app_field_dict:
                    return_code = LDEC.VALUE_FIELD_NOT_EXIST
                    return_code.position = return_code.position if not position else position
                    self._add_error_list(
                        attr, return_code=return_code,
                        element_data=self.element)
                # 检查模型和值编辑器的值的关联关系
                if r_finder:
                    model = value_editor.get("model")
                    path = value_editor.get("path", [])
                    field_model_uuid = app_field_dict.get(field, {}).get("model_uuid") if app_field_dict else model
                    self._check_value_editor_relationship(
                        attr, field_model_uuid, path, r_finder, position, check_parent_model)
            elif value_editor_type == ValueEditorType.EXPR:
                self._check_expr_value_editor(
                    value_editor, attr, app_field_dict, app_relationship_dict,
                    r_finder, position, check_parent_model)

    def _check_expr_value_editor(
            self, value_editor, attr, app_field_uuid_set,
            app_relationship_uuid_set, r_finder=None, position=None, check_parent_model=False):
        exprArr = value_editor.get("exprArr", list())
        for placeholder in exprArr:
            exist = check_placeholder_exist(
                placeholder, app_field_uuid_set, app_relationship_uuid_set)
            if not exist:
                return_code = LDEC.PLACEHOLDER_NOT_EXIST
                return_code.message = return_code.message.format(
                    name=str(placeholder.get("key", 99)))
                return_code.position = return_code.position if not position else position
                self._add_error_list(attr=attr, return_code=return_code)
        for expr in exprArr:
            field = expr.get("value").get("field")
            if field and field not in app_field_uuid_set:
                return_code = LDEC.VALUE_FIELD_NOT_EXIST
                return_code.position = return_code.position if not position else position
                self._add_error_list(
                    attr, return_code=return_code,
                    element_data=self.element)

            expr_type = expr.get("value").get("type")
            if r_finder and expr_type in (ValueEditorType.FIELD, ValueEditorType.EXPR):
                value = expr.get("value")
                model = value.get("model")
                path = value.get("path", [])
                field_model_uuid = app_field_uuid_set.get(field, {}).get("model_uuid") if app_field_uuid_set else model
                self._check_value_editor_relationship(
                    attr, field_model_uuid, path, r_finder, position, check_parent_model)

    def _check_value_editor_relationship(self, attr, field_model_uuid, path, r_finder, position, check_parent_model):
        # 按钮本身没有 model_uuid 属性，要从按钮的父容器里取
        parent_model_uuid = self.model_uuid if self.model_uuid else self.parent.get("model_uuid")
        if field_model_uuid != parent_model_uuid:
            # 没有关联关系的模型 path 为 []，所以要检查
            if not path:
                return_code = LDEC.RELATION_PATH_NOT_FOUND
                return_code.position = return_code.position if not position else position
                self._add_error_list(attr, return_code=return_code)
            elif path and self.model_uuid:
                for p in path:
                    associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                        p, self.model_uuid)
                    if associaton_model is None:
                        return_code = LDEC.RELATION_PATH_NOT_FOUND
                        return_code.position = return_code.position if not position else position
                        self._add_error_list(attr, return_code=return_code)

        if check_parent_model and path:
            target_model_uuid = field_model_uuid
            for p in path[::-1]:
                source_model, is_source, is_many, r_type = r_finder.lemon_association(p, target_model_uuid)
                if source_model is None:
                    break
                target_model_uuid = source_model.uuid
            else:
                source_model_uuid = target_model_uuid
                if source_model_uuid != parent_model_uuid:
                    return_code = LDEC.FIELD_AND_MODEL_NOT_EQUAL
                    return_code.message = return_code.message.format(name=self.element_name)
                    return_code.position = return_code.position if not position else position
                    self._add_error_list(attr, return_code=return_code)

    def update_any_list(self, any_checker_service):
        self.error_list.extend(any_checker_service.error_list)
        self.warning_list.extend(any_checker_service.warning_list)
        self.info_list.extend(any_checker_service.info_list)

    def check_all(self):
        for name in dir(self):
            if name.startswith("_"):
                continue
            obj = getattr(self, name, None)
            if callable(obj):
                is_run = getattr(obj, "_run", None)
                if is_run:
                    obj()
        # 使用进程池进行文档检查的时候,需要将返回结果dumps之后返回，不然会报错
        result = {"info_list": self.info_list, "warning_list": self.warning_list,
                  "error_list": self.error_list, "document_other_info": self.document_other_info,
                  "element": self.element}
        result = ujson.dumps(result)
        return result

    async def commit_modify(self, engine):
        raise NotImplementedError()
    
    def handle_reference_title(self, title_info):
        if not isinstance(title_info, str):
            title = ""
            if isinstance(title_info, dict):
                value = LemonBaseValueEditor(**title_info).value
                if not value:
                    value = ""
                else:
                    title = value
        else:
            title = title_info
        return title
    
    def update_field_reference(self, field_uuid, title, element_uuid, attr):
        self.update_reference_info(field_uuid, title, element_uuid, attr, "field_dict")
        
    def update_model_reference(self, model_uuid, title, element_uuid, attr):
        self.update_reference_info(model_uuid, title, element_uuid, attr, "model_dict")
        
    def update_relationship_reference(self, relationship_uuid, title, element_uuid, attr):
        self.update_reference_info(relationship_uuid, title, element_uuid, attr, "relationship_dict")
            
    def update_reference_info(self, reference_uuid, title, element_uuid, attr, reference_name):
        if reference_uuid and element_uuid:
            element_dict = self.document_other_info.get(reference_name, {}).setdefault(
                reference_uuid, {})
            if not attr and hasattr(self, "ATTR"):
                attr = self.ATTR.TYPE
            control_type = self.element.get("type", "") or getattr(self, "type", "")
            control_uuid = self.element.get("uuid", "")
            title = self.handle_reference_title(title)
            element_info = {"title": title, "attr": attr, "control_type": control_type,
                            "control_uuid": control_uuid, "uuid": element_uuid}
            element_dict[element_uuid] = element_info
    
    def update_cloud_func_reference(self, func_uuid, title, element_uuid, attr=""):
        self.update_reference_info(func_uuid, title, element_uuid, attr, "func_dict")
    
    def update_enum_reference(self, enum_uuid, title, element_uuid, attr=""):
        self.update_reference_info(enum_uuid, title, element_uuid, attr, "enum_dict")
        
    def update_page_reference(self, page_uuid, title, element_uuid, attr=""):
        self.update_reference_info(page_uuid, title, element_uuid, attr, "page_dict")
        
    def update_print_template_reference(self, print_uuid, title, element_uuid, attr=""):
        self.update_reference_info(print_uuid, title, element_uuid, attr, "print_dict")
    
    def update_label_print_reference(self, label_uuid, title, element_uuid, attr=""):
        self.update_reference_info(label_uuid, title, element_uuid, attr, "label_print_dict")
    
    def update_work_flow_reference(self, wf_uuid, title, element_uuid, attr=""):
        self.update_reference_info(wf_uuid, title, element_uuid, attr, "wf_dict")
        
    def update_enum_item_reference(self, enum_item_uuid, title, element_uuid, attr=""):
        self.update_reference_info(enum_item_uuid, title, element_uuid, attr, "enum_item_dict")
        
    def update_reference_by_value_edit(self, value_edit, element_data, attr=""):
        field_list = []
        # 数据列表的列使用dataIndex代替uuid
        element_uuid = element_data.get("uuid") or element_data.get("dataIndex")
        title = element_data.get("name") or element_data.get("title")
        if isinstance(value_edit, dict):
            type_ = value_edit.get("type")
            if type_ == ValueEditorType.FIELD or type_ == "field":
                field_uuid = value_edit.get("field")
                field_list.append(field_uuid)
                r_path = value_edit.get("path", [])
                for r_uuid in r_path:
                    self.update_relationship_reference(r_uuid, title, element_uuid, attr)
            elif type_ == ValueEditorType.ENUM:
                enum_item_uuid = value_edit.get("enum_uuid")
                self.update_enum_item_reference(enum_item_uuid, title, element_uuid, attr)
        for field_uuid in field_list:
            self.update_field_reference(field_uuid, title, element_uuid, attr)
            
    def update_reference_by_data_source(self, data_source, element_data, attr=""):
        # 数据列表的列使用dataIndex代替uuid
        element_uuid = element_data.get("uuid") or element_data.get("dataIndex")
        title = element_data.get("name") or element_data.get("title")
        if data_source.get("valueEdit"):
            value_edit = data_source.get("valueEdit")
            self.update_reference_by_value_edit(value_edit, element_data, attr)
        elif data_source.get("edit_value"):
            value_edit = data_source.get("edit_value")
            self.update_reference_by_value_edit(value_edit, element_data, attr)
        else:
            data_source_type = data_source.get("type", DataSourceType.MODEL_WITH_PRE)
            if data_source_type in [
                DataSourceType.ASSOCIATION, DataSourceType.ASSOCIATION_MEMORY]:
                r_path = data_source.get("path", [])
                for r_uuid in r_path:
                    self.update_relationship_reference(r_uuid, title, element_uuid, attr)
            elif data_source_type == DataSourceType.MODEL_WITH_PRE:
                model = data_source.get("model")
                self.update_model_reference(model, title, element_uuid, attr)
                
    def update_reference_by_field_info(self, field_info, element_data, attr=""):
        element_uuid = element_data.get("uuid")
        title = element_data.get("name") or element_data.get("title")
        if isinstance(field_info, dict):
            field_uuid = field_info.get("uuid") or field_info.get("field")
            r_path = field_info.get("path", [])
            for r_uuid in r_path:
                self.update_relationship_reference(r_uuid, title, element_uuid, attr)
            self.update_field_reference(field_uuid, title, element_uuid, attr)
        else:
            self.update_field_reference(field_info, title, element_uuid, attr)
            
    def update_reference_by_event(self, event_info, element_data, attr=""):
        element_data = element_data or self.element
        response_method = event_info.get("response_method")
        element_uuid = element_data.get("uuid")
        title = element_data.get("name") or element_data.get("title")
        if response_method == 2:
            func_uuid = event_info.get("func")
            self.update_cloud_func_reference(func_uuid, title, element_uuid, attr)
        elif response_method == 1:
            action = event_info.get("action")
            if action == EventAction.OPEN:
                page_uuid = event_info.get("page")
                self.update_page_reference(page_uuid, title, element_uuid, attr)
            elif action == EventAction.NEW:
                page_uuid = event_info.get("new_page")
                self.update_page_reference(page_uuid, title, element_uuid, attr)
            elif action == EventAction.START_WF:
                wf_uuid = event_info.get("workflow")
                self.update_work_flow_reference(wf_uuid, title, element_uuid, attr)
            elif action == EventAction.FORM_PRINT:
                page_uuid = event_info.get("page")
                self.update_print_template_reference(page_uuid, title, element_uuid, attr)
            elif action == EventAction.LABEL_PRINT:
                page_uuid = event_info.get("page")
                self.update_label_print_reference(page_uuid, title, element_uuid, attr)

    def get_attr_by_name(self, name):
        attr = ""
        if hasattr(self, "ATTR"):
            if hasattr(self.ATTR, name):
                attr = getattr(self.ATTR, name)
        elif hasattr(self, "attr_class"):
            if hasattr(self.attr_class, name):
                attr = getattr(self.attr_class, name)
        return attr

    def _check_badge(self, attr_badge:str, badge: dict):
        isshow = badge.get("show", False)
        if isshow:
            # 检查显示数据来源
            data_source = badge.get("data_source", {})
            self.app_relationship_dict = getattr(self, "app_relationship_dict", {})
            app_field_dict = getattr(self, "app_field_dict", None)
            self._check_value_editor(data_source, attr_badge, app_field_dict, self.app_relationship_dict, position=attr_badge)
            self._check_value_editor_required(data_source, attr_badge, position=attr_badge)
            # 检查角标背景颜色
            color = badge.get("color", {})
            self._check_value_editor(color, attr_badge, app_field_dict, self.app_relationship_dict, position=attr_badge)
            self._check_value_editor_required(color, attr_badge, position=attr_badge)
    
    def _check_value_editor_required(self, value_editor, attr, position=None):
        if value_editor:
            value_editor_type = value_editor.get("type")
            if value_editor_type == ValueEditorType.STRING:
                value = value_editor.get("value")
                if not value and value != False:
                    return_code = LDEC.VALUE_EDITOR_FIELD_NOT_EXIST
                    return_code.position = return_code.position if not position else position
                    self._add_error_list(attr, return_code=return_code, element_data=self.element)

    def _check_permission_config(self, permission_config, tag_dict, element_data=None):
        attr = "权限资源标签"
        enable = permission_config.get("enable")
        tag = permission_config.get("tag")
        # 面板、单个页签等未设置名称时为“”
        element_name = self.element.get("name") or self.element.get("title")
        if isinstance(element_data, dict):
            element_name = element_data.get("name")
        if enable is True:
            if not tag:
                return_code = LDEC.PERMISSION_CONFIG_NOT_CHOOSE_TAG
                return_code.message = return_code.message.format(element_name=element_name)
                self._add_error_list(attr, return_code=return_code, element_data=element_data)
            else:
                for t in tag:
                    tag_uuid = t.get("tag_uuid")
                    tag_info = tag_dict.get(tag_uuid, {})
                    action = tag_info.get("action")
                    if tag_info is None:
                        return_code = LDEC.PERMISSION_CONFIG_TAG_NOT_EXISTS
                        return_code.message = return_code.message.format(element_name=element_name)
                        self._add_error_list(attr, return_code=return_code, element_data=element_data)
                    elif action and int(action, 2) >= TagPermissionAction.MAX_ACTION:
                        return_code = LDEC.PERMISSION_CONFIG_ACTION_NOT_EXISTS
                        return_code.message = return_code.message.format(
                            element_name=element_name)
                        self._add_error_list(attr, return_code, element_data=element_data)


class DocumentCheckerService(CheckerService):

    def __init__(
        self, app_uuid: str, module_uuid:str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, model: ModelBasic, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.model = model
        self.document_insert_list = []
        self.document_update_list = []
        self.document_delete_list = []
    
    async def commit_modify(self, engine):
        document_update_func_list = []
        document_delete_func_list = []
        if not self.model:
            return
        for query in self.document_update_list:
            func = engine.access.update_obj_by_query(self.model, query, need_delete=True)
            document_update_func_list.append(func)
        for query in self.document_delete_list:
            func = engine.access.update_obj_by_query(self.model, query, need_delete=True)
            document_delete_func_list.append(func)
        
        # 这里如果数据量大的话，会有性能问题
        async with engine.db.objs.atomic():
            if self.document_insert_list:
                app_log.info(f"Insert {self.model.__name__}, len: {len(self.document_insert_list)}")
                await engine.access.insert_many_obj(self.model, self.document_insert_list)

            app_log.info(f"Update {self.model.__name__}, len: {len(document_update_func_list)}")
            # await asyncio.gather(*document_update_func_list)
            for update_func in document_update_func_list:
                await update_func

            app_log.info(f"Update {self.model.__name__}.is_delete, len: {len(document_delete_func_list)}")
            # await asyncio.gather(*document_delete_func_list)
            for delete_func in document_delete_func_list:
                await delete_func
            
