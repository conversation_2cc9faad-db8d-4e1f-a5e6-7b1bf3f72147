kafka_produce_default: dict = {
    "allow.auto.create.topics": True
}


class ConfigMetaClass(type):

    def __new__(cls, name, bases, attrs):
        """
        cls: 需要创建的类
        name: 需要创建的类名
        bases: 需要创建的类的所有父类
        attrs: 需要创建的类的所有属性、方法组成的字典
        """
        kafka_produce_config = {}
        kafka_produce_config.update(kafka_produce_default)
        kafka_produce_config.update(attrs["kafka_produce_kwargs"])
        attrs["kafka_produce_config"] = kafka_produce_config
        return super(ConfigMetaClass, cls).__new__(cls, name, bases, attrs)


class BaseConfig(metaclass=ConfigMetaClass):
    server_theard_pool_workers = 10
    kafka_produce_kwargs: dict = {}


class DevelopmentConfig(BaseConfig):
    kafka_produce_kwargs: dict = {
        "bootstrap.servers": "127.0.0.1:9092"
    }


class TestingConfig(BaseConfig):
    kafka_produce_kwargs = {
        "bootstrap.servers": "172.24.187.107:9092"
    }


class PreproductionConfig(BaseConfig):
    kafka_produce_kwargs = {
        "bootstrap.servers": "172.24.187.107:9092"
    }


class ProductionConfig(BaseConfig):
    kafka_produce_kwargs = {
        "bootstrap.servers": "172.24.187.99:9092"
    }
