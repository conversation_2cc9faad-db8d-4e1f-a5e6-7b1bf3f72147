# -*- coding:utf-8 -*-


from io import BytesIO
# from PIL import Image

from sanic import Blueprint
# from sanic.response import json
from sanic.views import HTTPMethodView
from sanic_openapi import doc

from baseutils.log import app_log
# from apps.utils import LemonDictResponse as LDR
from apps.utils import (
    process_args, stream_response
)
from baseutils.const import DOC
from apps.engine import engine
from apps.watermark.const import API
from apps.watermark.utils import (
    WatermarkType, watermark_creator, WatermarkABC, ResourceType
)
from apps.helper import DesignOss

API_NAME_VERSION = "_".join([API.NAME, API.V1])
url_prefix = "/api/" + "/".join([API.NAME, API.V1])
bp = Blueprint(API_NAME_VERSION, url_prefix=url_prefix)


class AddWatermark(HTTPMethodView):
    class AddWatermarkObj:
        watermark_settings = doc.String("水印设置")
        watermark_text = doc.String("水印文字")
        file_url = doc.String("需要加水印的文件url")

    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("为文件添加水印")
    async def post(self, request):
        with process_args():
            watermark_settings = request.json.get("watermark_settings", {})
            watermark_text = request.json.get("watermark_text")
            file_url = request.json.get("file_url")
            style = request.json.get("style", "")
            preview_button = request.json.get("preview_button", "")
            # headers = request.json.get("headers", {})
        # headers.update({"preview_button": preview_button})
        watermark_kwargs = {"text": watermark_text, "testing": False}
        watermark_type = watermark_settings.get("type", WatermarkType.STRING)
        design_oss = DesignOss()
        file_obj = await design_oss.get_oss_object(
            url=file_url, style=style, stream=False, preview_button=preview_button)
        content_type = file_obj.content_type
        app_log.info(f"content_type: {content_type}")
        file_type = ResourceType.CONTENT_TYPE_TO_RESOURCE_TYPE.get(
            content_type)
        if file_type is None:
            return await stream_response(BytesIO(file_obj.body), content_type)
        app_log.info(f"AddWatermarkObj:v1:{watermark_type},{watermark_text}")
        if watermark_type == WatermarkType.STRING and not watermark_text:
            return await stream_response(BytesIO(file_obj.body), content_type)

        watermark_instance: WatermarkABC = watermark_creator.create_obj(
            file_type, **watermark_settings)
        if watermark_type == WatermarkType.IMAGE:
            image_url = watermark_instance.image_url
            request_args = {
                "size_type": 0, "row_size": 1,
                "width": watermark_instance.image_width,
                "height": watermark_instance.image_height
            }
            object_stream = await design_oss.get_oss_object(
                url=image_url, request_args=request_args, stream=False)
            image_obj = BytesIO(object_stream.body)
            watermark_kwargs.update({"image_obj": image_obj})
        file_with_watermark = watermark_instance.save(
            BytesIO(file_obj.body), **watermark_kwargs)
        file_with_watermark.seek(0)
        return await stream_response(file_with_watermark, content_type)


bp.add_route(AddWatermark.as_view(), "/add_watermark.json")
