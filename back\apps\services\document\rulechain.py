from apps.utils import <PERSON><PERSON>, lemon_uuid

class RC_Condition(Json):
    def __init__(self, condition, expr=None, action_list=None, description="", *args, **kwargs):
        self.uuid = lemon_uuid()
        self.condition = condition
        self.description = description
        self.expr = expr
        if action_list is None:
            action_list = list()
        self.action_list = action_list
        super().__init__(*args, **kwargs)

class RuleChain(Json):
    def __init__(self, uuid, name, description="", system_service=False, 
        event_list=None, timer_list=None, variable_list=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.system_service = system_service
        if event_list is None:
            event_list = list()
        if timer_list is None:
            timer_list = list()
        if variable_list is None:
            variable_list = list()
        self.event_list = event_list
        self.timer_list = timer_list
        self.variable_list = variable_list
        super().__init__(*args, **kwargs)