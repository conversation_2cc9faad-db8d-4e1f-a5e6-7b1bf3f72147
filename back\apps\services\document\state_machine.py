# -*- coding:utf-8 -*-

from apps.utils import <PERSON><PERSON>, lemon_uuid
from apps.ide_const import (
    StateType, HandlerType, HandleType, HandleCondition, TimerType, IntervalType, PeriodType, 
    ActionType, TriggerType, ActionSyncType, VariableType, VariableLevel, ValueEditorType
)
    
class Expr(Json):

    def __init__(self, name, action_list=None, *args, **kwargs):
        self.name = name
        if action_list is None:
            action_list = list()
        self.action_list = action_list
        super().__init__(*args, **kwargs)
    
    def add_action(self, action):
        self.action_list.append(action)
    
    def add_action_list(self, action_list):
        for action in action_list:
            self.add_action(action)
    

class If(Expr):

    def __init__(self, expr, action_list=None, *args, **kwargs):
        name = "if"
        super().__init__(name, action_list=action_list, *args, **kwargs)
        self.expr = expr
    

class Elif(Expr):

    def __init__(self, expr, action_list=None, *args, **kwargs):
        name = "elif"
        super().__init__(name, action_list=action_list, *args, **kwargs)
        self.expr = expr
    

class Else(Expr):

    def __init__(self, expr, action_list=None, *args, **kwargs):
        name = "else"
        super().__init__(name, action_list=action_list, *args, **kwargs)
        self.expr = expr


class For(Expr):

    def __init__(self, loop_variable, loop_list, action_list=None, *args, **kwargs):
        name = "for"
        super().__init__(name, action_list=action_list, *args, **kwargs)
        self.loop_variable = loop_variable
        self.loop_list = loop_list


class Action(Json):

    def __init__(
        self, name, func, func_type=0, description="", auto=False, run_async=False, 
        sync_type=None, run_delay=False, delay_time=None, arg_list=None, return_list=None):
        self.uuid = lemon_uuid()
        self.name = name
        self.type = ActionType.FUNC
        self.description = description
        self.func = func
        self.func_type = func_type
        self.auto = auto
        self.run_async = run_async
        self.sync_type = sync_type
        self.run_delay = run_delay
        self.delay_time = delay_time
        if arg_list is None:
            arg_list = list()
        if return_list is None:
            return_list = list()
        self.arg_list = arg_list
        self.return_list = return_list


class ForAction(Json):

    def __init__(self, for_expr, description=""):
        self.uuid = lemon_uuid()
        self.name = "for"
        self.type = ActionType.FOR
        self.description = description
        self.for_expr = for_expr


class IfAction(Json):

    def __init__(self, if_expr, else_expr=None, elif_expr_list=None, description=""):
        self.uuid = lemon_uuid()
        self.name = "if"
        self.type = ActionType.IF
        self.description = description
        self.if_expr = if_expr
        self.else_expr = else_expr
        if elif_expr_list is None:
            elif_expr_list = list()
        self.elif_expr_list = elif_expr_list


class Condition(Json):

    def __init__(self, name, to_state, default, expr, action_list=None, description="", *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.description = description
        self.to_state = to_state
        self.default = default
        self.expr = expr
        if action_list is None:
            action_list = list()
        self.action_list = action_list
        super().__init__(*args, **kwargs)


class Transition(Json):

    def __init__(
        self, name, description="", to_state=None, skip_entry_exit=False, run_delay=False, delay_time=None, 
        condition_list=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.description = description
        self.to_state = to_state
        self.skip_entry_exit = skip_entry_exit
        self.run_delay = run_delay
        self.delay_time = delay_time
        if condition_list is None:
            condition_list = list()
        self.condition_list = condition_list
        super().__init__(*args, **kwargs)


class Trigger(Json):

    def __init__(self, name, trigger_type, transition, description="", *args, **kwargs):
        self.uuid = lemon_uuid()
        self.name = name
        self.description = description
        self.type = trigger_type
        self.transition = transition
        super().__init__(*args, **kwargs)


class AutoTrigger(Trigger):

    def __init__(self, name, transition, description="", *args, **kwargs):
        trigger_type = TriggerType.AUTO
        super().__init__(name, trigger_type, transition, description, *args, **kwargs)


class EventTrigger(Trigger):

    def __init__(self, name, transition, event_list=None, description="", *args, **kwargs):
        trigger_type = TriggerType.EVENT
        super().__init__(name, trigger_type, transition, description, *args, **kwargs)
        if event_list is None:
            event_list = list()
        self.event_list = event_list
        
    def __deepcopy__(self):
        info = self.to_dict()
        return EventTrigger(**info)


class TimerTrigger(Trigger):

    def __init__(self, name, transition, timer, description="", *args, **kwargs):
        trigger_type = TriggerType.TIMER
        super().__init__(name, trigger_type, transition, description, *args, **kwargs)
        self.timer = timer


class Timer(Json):

    def __init__(self, uuid, name, timer_type, start_timestamp, description="", *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.type = timer_type
        self.start_timestamp = start_timestamp
        super().__init__(*args, **kwargs)


class OnceTimer(Timer):

    def __init__(self, uuid, name, start_timestamp, description="", *args, **kwargs):
        timer_type = TimerType.ONCE
        super().__init__(uuid, name, timer_type, start_timestamp, description, *args, **kwargs)


class IntervalTimer(Timer):

    def __init__(self, uuid, name, start_timestamp, interval_type, interval_value, description="", *args, **kwargs):
        timer_type = TimerType.INTERVAL
        super().__init__(uuid, name, timer_type, start_timestamp, description, *args, **kwargs)
        self.interval_type = interval_type
        self.interval_value = interval_value


class PeriodTimer(Timer):

    def __init__(self, uuid, name, start_timestamp, period_type, period_value, description="", *args, **kwargs):
        timer_type = TimerType.PERIOD
        super().__init__(uuid, name, timer_type, start_timestamp, description, *args, **kwargs)
        self.period_type = period_type
        self.period_value = period_value


class Variable(Json):

    def __init__(
        self, uuid, name, description="", variable_type=VariableType.AUTO, 
        default=None, model=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.type = variable_type
        self.default = dict() if default is None else default
        self.model = model
        super().__init__(*args, **kwargs)


class Event(Json):

    def __init__(self, uuid, name, arg_list=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        if arg_list is None:
            arg_list = list()
        self.arg_list = arg_list
        super().__init__(*args, **kwargs)


class State(Json):

    def __init__(
        self, uuid, name, description, state_type, entry_list=None, exit_list=None, 
        trigger_list=None, variable_list=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.type = state_type
        if entry_list is None:
            entry_list = list()
        if exit_list is None:
            exit_list = list()
        if trigger_list is None:
            trigger_list = list()
        if variable_list is None:
            variable_list = list()
        self.entry_list = entry_list
        self.exit_list = exit_list
        self.trigger_list = trigger_list
        self.variable_list = variable_list
        super().__init__(*args, **kwargs)


class AutoState(State):

    def __init__(
        self, uuid, name, description="", entry_list=None, exit_list=None,
        trigger_list=None, variable_list=None, *args, **kwargs):
        state_type = StateType.AUTO
        super().__init__(
            uuid, name, description, state_type, entry_list=entry_list, exit_list=exit_list, 
            trigger_list=trigger_list, variable_list=variable_list, *args, **kwargs)


class ManualState(State):

    def __init__(
        self, uuid, name, description="", entry_list=None, exit_list=None,
        trigger_list=None, variable_list=None, handler_list=None, handle_type=None,
        handle_condition=None, handle_value=None, page=None, *args, **kwargs):
        state_type = StateType.MANUAL
        super().__init__(
            uuid, name, description, state_type, entry_list=entry_list, exit_list=exit_list, 
            trigger_list=trigger_list, variable_list=variable_list, *args, **kwargs)
        if handler_list is None:
            handler_list = list()
        if handle_type is None:
            handle_type = HandleType.ORDER
        if handle_condition is None:
            handle_condition = HandleCondition.COUNT
        if handle_value is None:
            handle_value = 1
        self.handler_list = handler_list
        self.handle_type = handle_type
        self.handle_condition = handle_condition
        self.handle_value = handle_value
        self.page = page


class ChildState(State):

    def __init__(
        self, uuid, name, description="", child_sm=None, entry_list=None, exit_list=None,
        trigger_list=None, variable_list=None, recovery=True, *args, **kwargs):
        state_type = StateType.CHILD
        super().__init__(
            uuid, name, description, state_type, entry_list=entry_list, exit_list=exit_list, 
            trigger_list=trigger_list, variable_list=variable_list, *args, **kwargs)
        self.child_sm = child_sm
        self.recovery = recovery


class StartState(State):


    def __init__(
        self, uuid, name, description="", entry_list=None, exit_list=None,
        trigger_list=None, variable_list=None, *args, **kwargs):
        state_type = StateType.START
        name = "开始"
        super().__init__(
            uuid, name, description, state_type, entry_list=entry_list, exit_list=exit_list, 
            trigger_list=trigger_list, variable_list=variable_list, *args, **kwargs)


class EndState(State):

    def __init__(
        self, uuid, name, description="", entry_list=None, exit_list=None,
        trigger_list=None, variable_list=None, *args, **kwargs):
        state_type = StateType.END
        name = "结束"
        super().__init__(
            uuid, name, description, state_type, entry_list=entry_list, exit_list=exit_list, 
            trigger_list=trigger_list, variable_list=variable_list, *args, **kwargs)


class StateMachine(Json):

    def __init__(
        self, uuid, name, description="", multi_instance=True, strict_mode=True,
        event_list=None, variable_list=None, timer_list=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.multi_instance = multi_instance
        self.strict_mode = strict_mode
        if event_list is None:
            event_list = list()
        if variable_list is None:
            variable_list = list()
        if timer_list is None:
            timer_list = list()
        self.event_list = event_list
        self.variable_list = variable_list
        self.timer_list = timer_list
        self.start_state = None
        self.state_list = list()
        super().__init__(*args, **kwargs)
    
    def add_state(self, state):
        self.state_list.append(state)
    
    def add_state_list(self, state_list):
        for state in state_list:
            self.add_state(state)
    
    def add_start_state(self, start_state):
        if self.start_state is None:
            if start_state.type == StateType.START:
                self.start_state = start_state
