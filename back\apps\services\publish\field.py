'''
Description: 
Author: lv
Date: 2021-04-27 15:43:53
LastEditTime: 2021-04-30 11:39:00
LastEditors: lv
Reference: 
'''
from dataclasses import dataclass
from apps.services.document.page import DateTimeCycle
import peewee
import apps.peewee_ext as peewee_ext
from enum import Enum, auto
from apps.services.publish.config import PEEWEE_ALIAS, PEEWEE_EXT_ALIAS
from apps.ide_const import FieldType, TypeSystem


lemon_field_map = {v:k for k, v in FieldType.__dict__.items() if isinstance(v, int)}
field_arg_alias = {"choices": "choices", "default": "default", "max_digits": "length", "decimal_places": "decimals", "max_length": "length"}


class _LemonFieldAttr:
    CharField = f"LemonCharField", ["max_length"]
    IntegerField = f"LemonIntegerField", []
    DecimalField = f"LemonDecimalField", ["max_digits", "decimal_places"]
    DateTimeField = f"LemonDateTimeField", []
    BooleanField = f"LemonBooleanField", []
    EnumField = f"LemonEnumField", ["choices", "default"]
    FileField = f"LemonFileField", []
    ImageField = f"LemonImageField", []
    DateTimeCycleField = f"LemonDateTimeCycleField", []
    JsonField = f"LemonJsonField", []
    TextField = f"LemonTextField", []
    MediumTextField = "LemonMediumTextField", []

class LemonField(object):
    ENUM = _LemonFieldAttr.EnumField
    STRING = _LemonFieldAttr.CharField
    INTEGER = _LemonFieldAttr.IntegerField
    BOOLEAN = _LemonFieldAttr.BooleanField
    DECIMAL = _LemonFieldAttr.DecimalField
    DATETIME = _LemonFieldAttr.DateTimeField
    DATE = _LemonFieldAttr.DateTimeField
    TIME = _LemonFieldAttr.DateTimeField
    PHONE = _LemonFieldAttr.IntegerField
    BINARY = _LemonFieldAttr.CharField
    CURRENCY = _LemonFieldAttr.IntegerField
    FILE = _LemonFieldAttr.FileField
    IMAGE = _LemonFieldAttr.ImageField
    DATETIME_CYCLE = _LemonFieldAttr.DateTimeCycleField
    JSON = _LemonFieldAttr.JsonField
    TEXT = _LemonFieldAttr.TextField
    MEDIUMTEXT = _LemonFieldAttr.MediumTextField


class OptionOnDelete(Enum):
    SETNULL = "SET NULL"
    CASCADE = "CASCADE"
    RESTRICT = "RESTRICT"
    NOACTION = "NO ACTION"
    

class FieldInstantiation:

    def __init__(self, field_type: int, **kwargs) -> None:
        self.field_type = field_type
        self.kwargs = kwargs
        self.instance

    @property
    def instance(self):
        
        try:
            self.type_str = getattr(LemonField, lemon_field_map[self.field_type])[0]
        except:
            raise Exception("filed type not found")
        else:
            attr = getattr(peewee, self.type_str, None)
            self.module_name = PEEWEE_ALIAS
            if attr is None:
                attr = getattr(peewee_ext, self.type_str)
                self.module_name = PEEWEE_EXT_ALIAS
            return attr(**self.kwargs)

    def str(self, kwargs={}):
        return ".".join([self.module_name, self.type_str]) + f"(**{kwargs})"


if __name__ == "__main__":
    print(LemonField.DATETIME)
    print(lemon_field_map[23])
    print(getattr(LemonField, lemon_field_map[23])[0])
