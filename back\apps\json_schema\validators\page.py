from apps.json_schema.validators.base import BaseValidator
from apps.json_schema.refresolver import RefResolver
from apps.json_schema.context import AppCtx, AppModel
from apps.json_schema.schemas.page import page_schema
from apps.utils import PageFinder
from apps.ide_const import Document
from apps.json_schema.utils import id_of
from loguru import logger


class PageValidator(BaseValidator):
    def __init__(self, app_ctx, version: str = '1.0') -> None:
        super().__init__(app_ctx, version)
        self.document_type = Document.TYPE.PAGE
        self.schema = {"$ref": "mem://page/page_schema"}
        self.resolver = RefResolver(base_uri=id_of(page_schema), referrer=page_schema)
        self.model_in_page = {}
        self.page_finder = PageFinder(
            self.model_in_page, except_form=False, count_container=True,
            find_association=True)
        # self.page_finder.relationship_finder = self.app_ctx.app_model_relationship.relationship_finder


if __name__ == "__main__":
    page_validator = PageValidator()
    page_validator.app_ctx = AppCtx()
    page_validator.compile()

    data = {
        "controller": {},
        "style": {},
        "uuid": "18ee5c9bb4ff51dba7c56b0cb6e0e4ed",
        "name": "通天塔",
        "title": "通天塔",
        "routine": {},
        "open_type": 1,
        "page_title": {
            "type": 0,
            "uuid": "8dff37784c4a5bf8a5ec640f27ddff9e",
            "value": "通天塔",
            "is_monitor": True,
            "model": "123"
        },
        "custom_path": {
            "path": "",
            "show": True
        },
        "open_in_new_tab": False,
        "routine_modal_width": {},
        "routine_modal_height": {},
        "children": [{}]
    }
    for error in page_validator.validate(data):
        logger.info(error)
