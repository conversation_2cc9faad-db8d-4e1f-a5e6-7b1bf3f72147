from peewee import (
    BooleanField, Char<PERSON>ield, FixedCharField, IntegerField, SQL
)
from apps.base_entity import RuntimeBaseModel as BaseModel
from apps.entity import Workflow, DocumentContent


def __modify_design_model_attr():
    design_model_list = [Workflow, DocumentContent]
    for model in design_model_list:
        model._meta.schema = "lemon"


class DepartmentMember(BaseModel):
    # 这是tenant_center的视图
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    department_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    job = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    is_admin = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0 普通成员 1 子管理员 2 主管
    is_major = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("tenant_uuid", "department_uuid"), False),
            (("tenant_uuid", "user_uuid"), False),
        )


class AppManager(BaseModel):
    # create view app_manager as (select `lemon_app_manager`.`user_uuid` AS `manager_uuid`,
    #                               `lemon_app_manager`.`app_uuid` AS `app_uuid`,
    #                               `lemon_app_manager`.`is_delete` AS `is_delete` from `lemon`.`lemon_app_manager`)
    manager_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("manager_uuid", "app_uuid"), False),
        )


class Terminal(BaseModel):

    # 这是tenant_center的视图
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    terminal_id = FixedCharField(max_length=32, unique=True)
    name = FixedCharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')])
    version = FixedCharField(max_length=20, default="", constraints=[SQL('DEFAULT ""')])
    status = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 0 offline  1 online
    is_block = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        sys_table = True
        evolve = True
        indexes = (
            (("tenant_uuid", "terminal_id", ), False),
        )


class Gateway(BaseModel):

    # 这是tenant_center的视图
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    gateway_id = FixedCharField(max_length=32, unique=True)
    name = FixedCharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')])
    version = FixedCharField(max_length=20, default="", constraints=[SQL('DEFAULT ""')])
    status = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 0 offline  1 online
    connect_status = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 0 disconnect  1 connect
    machine_info = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_block = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        sys_table = True
        evolve = True
        indexes = (
            (("tenant_uuid", "gateway_id", ), False),
        )
