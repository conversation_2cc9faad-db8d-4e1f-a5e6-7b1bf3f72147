# -*- coding:utf-8 -*-

import os
import asyncio
import time
import importlib
from traceback import print_exc
from collections import namedtuple, OrderedDict
from weakref import WeakSet

import ujson
from sanic import Sanic
from sanic_jwt import initialization
from sanic_openapi import swagger_blueprint

from baseutils.log import gen_log, app_log
from baseutils.utils import CVE, LemonContextVar, timeit_async
from apps import ENV_KEY, Runtime
from baseutils.const import SanicListener
from apps.ide_const import NodeType
from apps.base_settings import create_exception_handle, create_listener
from apps.base_utils import GlobalVars, mysql_ping, make_model_copy, get_runtime_app_uuid
from apps.base_entity import UserRole, Tag
from apps.user_entity import sys_table_handler, PermissionConfig, make_runtime_table_name
from apps.utils import temp_unlock
from runtime.interfaces import RuntimeLemonSessionInterFace
from runtime import config
# from runtime.uc import authenticate, auth_views, AuthenticateEndpoint
from runtime.uc import user_manager_bp_v1
from runtime.api import runtime_bp_v1, vfs_bp_v1, applet_bp_v1
from runtime.apijson import apijson_bp_v1
from runtime.api.utils import (
    LemonConnectorManager, RestfulManager, get_app_watermark,
    gen_client, current_user_system_ctx
)
from runtime.component.data_adapter import RuleChain
from runtime.component.builder import ComponentCreator
from runtime.index.api import index_bp_v1
from runtime.engine import engine
from runtime.elastic import RuntimeElastic, RuntimeSyncElastic, LocalRuntimeElastic, LogSysWsClient
from apps.peewee_ext import LemonModelQueryHelper
from runtime.core.sm import LemonStateMachine
from runtime.core.value_editor import LemonValueEditor
from runtime.core.namespace import LemonWrapper
from runtime.api.wf_helper import system_start_all_tenant_workflow
from runtime.api.wf_utils import timeout_schedule_task_on_server_start
from runtime.api.v1 import handle_message, handle_stream_message
from runtime.security import Policy
from runtime.utils import lemon_uuid, LemonRedis, Scheduler, run_func
from runtime.core.connector import ClientConnector
from runtime.core.actor import LemonPubsubActor
from runtime.core.func import FuncWrapper
from runtime.log import runtime_log
from runtime.local_config import Config as ConfigPlus
from ucenter.cas import CASClient, CASAuthentication
from ucenter.api import AuthenticateEndpoint
from functools import partial
from apps.ide_const import DocUpdateMessage, DocumentType, DocUpdateType
from runtime.ext import MemoryResource
from RestrictedPython import compile_restricted_eval, compile_restricted
from runtime.core.queue import upload_queue
from runtime.const import RedisChannel


_EndpointMapping = namedtuple("_EndpointMapping", ["cls", "endpoint", "keys", "is_protected"])

endpoint_mappings = (
    _EndpointMapping(AuthenticateEndpoint, "authenticate", ["auth_mode"], False),
    # _EndpointMapping(VerifyEndpoint, "verify", ["auth_mode"], False)
)

# 为了兼容柠檬标准的数据返回格式
initialization.endpoint_mappings = endpoint_mappings


def create_app(app_name: str = Runtime.APP_NAME) -> Sanic:
    app = Sanic(name=app_name)
    gen_log.info(f"app name: {app_name}")
    return app


def reset_redis_prefix_key(config_obj, app_env_name):
    # 根据发布到的 环境 不同，适配不同的 前缀
    config_obj.DATALIST_OPTION_PREFIX = app_env_name + config_obj.DATALIST_OPTION_PREFIX
    config_obj.SPLIT_OPTION_PREFIX = app_env_name + config_obj.SPLIT_OPTION_PREFIX
    config_obj.EXTERNAL_BUTTON_PREFIX = config_obj.EXTERNAL_BUTTON_PREFIX
    config_obj.FORM_STAGE_PREFIX = app_env_name + config_obj.FORM_STAGE_PREFIX
    config_obj.WORKFLOW_LABEL_PREFIX = app_env_name + config_obj.WORKFLOW_LABEL_PREFIX


def create_config(env_key: str = Runtime.ENV_KEY) -> config.Config:
    env = os.getenv(env_key) or "Development"
    config_key = f"{env}Config"
    gen_log.info(f"Config key: {config_key}")
    config_obj = getattr(config, config_key, config.DevelopmentConfig)
    for key, value in  ConfigPlus.__dict__.items():
        if not key.startswith("__"):
            setattr(config_obj, key, value)
    config_obj.ENV = env
    var_app_env_name = os.environ.get("APP_ENV_NAME", "").rstrip(";")
    real_app_env_name = os.environ.get("REAL_ENV_NAME", "").rstrip(";")
    config_obj.APP_ENV_NAME = var_app_env_name or "pro"
    gen_log.info(f"Load config from env: {env}")
    reset_redis_prefix_key(config_obj, var_app_env_name)
    user_uuid = os.getenv(Runtime.MIDDLE_USER_KEY) or "runtime_user"
    sys_state_machine_uuid = os.getenv(Runtime.SYS_STATE_MACHINE_UUID) or "3d3afb66e83d11eab9f7814a1886278d"
    config_obj.MIDDLE_USER_UUID = user_uuid     # 中间用户uuid，可被MYSQL_DB_RUNTIME_USER替代
    config_obj.MYSQL_DB_RUNTIME_USER = user_uuid
    config_obj.SYS_STATE_MACHINE_UUID = sys_state_machine_uuid
    app_uuid = get_runtime_app_uuid()
    config_obj.APP_UUID = app_uuid
    if hasattr(config_obj, "CUSTOM_APP_DOMAIN"):
        _APP_DOMAIN = config_obj.CUSTOM_APP_DOMAIN
    else:
        _APP_DOMAIN = config_obj.APP_DOMAIN
    # 运行时应用请求的链接
    config_obj.APP_LOCALTION = "/".join([_APP_DOMAIN,
                                         app_uuid+var_app_env_name])
    config_obj.REAL_APP_LOCALTION = "/".join([_APP_DOMAIN,
                                              app_uuid+real_app_env_name])
    config_obj.REDIS_CHANNEL_NOTIFY_FRONT = config_obj.REDIS_CHANNEL_NOTIFY_FRONT + "-" + app_uuid
    config_obj.NEED_THROW_EXCEPTION = False if (
        config_obj.APP_ENV_NAME == "pro" or
        config_obj.APP_ENV_NAME.startswith("group")
    ) else True
    direct_access_design = os.environ.get(Runtime.DIRECT_ACCESS_DESIGN, 'OFF')
    if direct_access_design.lower() == "on" or env == "Development" or "sandbox" in real_app_env_name:
        config_obj.DIRECT_ACCESS_DESIGN = True
        app_log.info("direct access design on")
    # config_obj.DIRECT_ACCESS_DESIGN = False
    return config_obj


def create_blueprint(app: Sanic, bp_name: str = None) -> None:
    bps = OrderedDict([
        (index_bp_v1.name, index_bp_v1),
        (runtime_bp_v1.name, runtime_bp_v1),
        (applet_bp_v1.name, applet_bp_v1),
        (vfs_bp_v1.name, vfs_bp_v1),
        (apijson_bp_v1.name, apijson_bp_v1),
        (user_manager_bp_v1.name, user_manager_bp_v1)
    ])
    if bp_name is not None:
        bps = {bp_name: bps[bp_name]}
    for name, bp in bps.items():
        app.blueprint(bp)
        gen_log.info(f"Create blueprint: {name}: {bp}")


def create_doc_blueprint(app: Sanic) -> None:
    if app.config.DOC:
        doc_blueprint = swagger_blueprint
        app.blueprint(doc_blueprint)
        gen_log.info(f"Create blueprint: {doc_blueprint.name}: {doc_blueprint}")


def create_auth(app: Sanic) -> None:
    gen_log.info("initialize app")
    app.jwt = initialization.Initialize(
        app,
        url_prefix="/api/auth",
        path_to_authenticate="/login.json",
        path_to_refresh="/refresh.json",
        path_to_retrieve_user="/me.json",
        path_to_verify="/verify.json",
        authentication_class=CASAuthentication,
        user_id="user_uuid",
        # class_views=auth_views,
        expiration_delta=app.config.JWT_EXPIRATION_DELTA)



def register_start_engine(
        app: Sanic, with_connector=False, start_mysql_ping=False,
        with_rulechain=False, with_vfs=False, with_stream=False) -> None:

    from ucenter.api.wx_utils import WeiXinAPI
    local_deploy = app.config.LOCAL_DEPLOY
    config = app.config
    engine.init_app(app)
    app.engine = engine
    engine.wx_api = WeiXinAPI(app)
    ws_client = LogSysWsClient()
    cas_client = CASClient(app, engine, create_manage_bp=True)
    elastic_client = LocalRuntimeElastic() if local_deploy else RuntimeElastic()
    elastic_client.init_app(engine.app, ws_client)
    engine.elastic_client = elastic_client
    sync_elastic_client = RuntimeSyncElastic()
    sync_elastic_client.init_app(engine.app, ws_client)
    engine.sync_elastic_client = sync_elastic_client
    engine.cas_client = cas_client
    engine.component_creator = ComponentCreator()
    GlobalVars.set("engine", engine)
    GlobalVars.set("http_db", engine.userdb)

    def create_interface(app, loop):
        for uri, policy in getattr(user_manager_bp_v1, "policy", {}).items():
            Policy.add_policy(user_manager_bp_v1.url_prefix + uri, policy)
        interface = RuntimeLemonSessionInterFace(engine.redis,
                                                 expiry=config.SESSION_EXPIRY,
                                                 prefix=config.SESSION_PREFIX)
        engine.session.init_app(app=app, interface=interface)

    async def run_restful(app, loop):
        app_uuid = config.APP_UUID
        try:
            await RestfulManager(app, app_uuid).start()
        except:
            pass

    async def _register_start_engine(app, loop):

        await engine.redis.init_app(app)
        await engine.pubsub.init_app(app)
        await engine.pm.init_app(app, engine)
        await engine.cas_client.init_client()
        create_interface(app, loop)
        await load_resource(app, loop)
        # restful 需要用到云函数, 所以要先 load_resource
        await run_restful(app, loop)

    async def run_sys_statemachine(app, loop):
        import uuid
        import base64
        sm_document_uuid = config.SYS_STATE_MACHINE_UUID
        sm_info = await engine.access.get_document_content_by_document_uuid(sm_document_uuid)
        if not sm_info:
            return
        # sid = base64.b64encode(uuid.UUID(sm_document_uuid).bytes).decode().rstrip("=").replace("+", "_").replace("/", "-")
        mid = base64.b32encode(uuid.UUID(sm_document_uuid).bytes).decode().rstrip("=")
        lsm = LemonStateMachine(machine_id=mid, **sm_info.document_content)
        
    async def schedule_start_workflow(scheduler, info):
        wf_name = info.get("wf_name")
        wf_uuid = info.get("wf_uuid")
        online_version = info.get("online_version")
        content = info.get("document_content", dict())
        versions = content.get("versions")
        cur_version = next(
            filter(lambda v: v["uuid"] == online_version, versions), None)
        if not cur_version:
            app_log.info(f"{wf_name} start schedule failed")
            return
        nodes = cur_version.get("nodes", list())
        start_node = next(
            filter(lambda n: n["type"] == NodeType.START, nodes), None)
        if not start_node:
            # 没有start_node是不正常的
            return
        need_schedule = start_node.get("need_schedule")
        if not need_schedule:
            # need_schedule与workflow表配置不一致
            return
        timer_list = start_node.get("timer_list", dict())
        func = system_start_all_tenant_workflow
        args = [config.APP_UUID, "system_user_uuid", wf_uuid,
                scheduler]

        for t in timer_list:
            trigger_type = t["type"]
            start_time = t.get("start_timestamp")
            timer_uuid = t["uuid"]
            job_id = timer_uuid
            job_kwargs = {
                "job_id": job_id,
                "scheduler": scheduler
            }
            args.append(job_id)
            kwargs = {
                "interval_type": t.get("interval_type"),
                "interval_value": t.get("interval_value"),
                "period_type": t.get("period_type"),
                "period_value": t.get("period_value"),
            }
            if isinstance(start_time, dict):
                value_editor = LemonValueEditor(**start_time)
                _lemon = LemonWrapper()
                value_editor.set_globals_without_sm(_lemon, None)
                start_time = await value_editor.value_async
            if trigger_type not in (0, 1, 2):
                app_log.info(f"{t}, trigger invalid")
                continue
            if not start_time:
                app_log.info(f"{start_time}, start_timestamp invalid")
                continue
            app_log.info(f"{wf_name} start_time:{start_time} start schedule...")
            scheduler.add_job(
                func, trigger_type, start_time, args=args,
                job_kwargs=job_kwargs, **kwargs)

    async def run_sys_rulechain(app, loop):
        app_uuid = config.APP_UUID
        sys_rc_list = []
        rulechain_list = await engine.access.list_sys_rulechain_by_app_uuid(
            app_uuid, with_sys=False, ignore_env=True)
        for rulechain in rulechain_list:
            rc_info = await engine.access.get_document_content_by_document_uuid(
                rulechain["document_uuid"])
            if not rc_info:
                continue
            sid = lemon_uuid()
            connector = ClientConnector(sid)
            rc = RuleChain(
                rule_dict=rc_info.document_content, connector=connector)
            sys_rc_list.append(rc)
        await asyncio.gather(*[rc.start() for rc in sys_rc_list])

        workflow_list = await engine.user_access.list_wf_by_app_uuid_schedule(
            app_uuid, need_schedule=True)
        scheduler = Scheduler()
        scheduler.start()

        app_tenant_info = await engine.user_access.list_app_tenant(app_uuid)

        # 超时未处理的推送
        await asyncio.gather(
            *[timeout_schedule_task_on_server_start(
                app_uuid, t["tenant_uuid"], scheduler)
                for t in app_tenant_info])

        # 定时发起工作流
        for w in workflow_list:
            try:
                await schedule_start_workflow(scheduler, w)
            except Exception:
                print_exc()

        # 监听节点消息广播
        asyncio.create_task(listen())

    async def run_vfs(app, loop):
        asyncio.create_task(upload_queue.start())

    async def listen():
        # 只监听本app的
        app_uuid = config.APP_UUID
        subscribe_channel = app_uuid + ":" + "*:message"
        subscriber = await engine.redis.redis.psubscribe(subscribe_channel)
        async for channel, message in subscriber[0].iter():
            asyncio.create_task(handle_message(channel, message))

    async def listen_with_stream():
        # 只监听本app的
        app_uuid = config.APP_UUID
        stream_key = app_uuid + ":" + RedisChannel.LOCALLOG
        latest_stream_id = int(time.time()*1000)
        latest_id = f'{latest_stream_id}-0'
        while True:
            try:
                messages = await engine.pubsub.read(stream=stream_key, latest_id=latest_id, timeout=0)
                for message in messages:
                    stream_key, msg_id, data = message
                    latest_id = msg_id
                    timestamp = int(msg_id.split('-')[0])
                    if latest_stream_id > timestamp:
                        continue
                    latest_stream_id = timestamp
                    asyncio.create_task(handle_stream_message(stream_key, data))
                else:
                    await asyncio.sleep(0.3)
            except:
                app_log.info(f"listen_with_stream:stream_key:{stream_key}")

    async def run_stream(app, loop):
        asyncio.create_task(listen())

    async def run_connector(app, loop):
        app_uuid = config.APP_UUID
        print(app_uuid)
        connector_config_list = await engine.access.list_connector_tenant_config(app_uuid, as_dict=True)

        ct = LemonConnectorManager(app, connector_config_list)
        await ct.prepare()
        await ct.start()

    async def _register_after_engine(app, loop):
        app_watermark_dict = await get_app_watermark(config.APP_UUID)
        config.APP_WATERMARK = app_watermark_dict
        if with_connector:
            await run_connector(app, loop)
        LemonModelQueryHelper._objs_ = engine.userdb.objs
        LemonModelQueryHelper._main_loop_ = loop
        loop.set_default_executor = CVE
        engine.loop = loop
        if start_mysql_ping:
            asyncio.create_task(mysql_ping(
                *[engine.db.objs, engine.userdb.objs]))

    app.register_listener(_register_start_engine, SanicListener.BEFORE_SERVER_START)
    # app.register_listener(subscribe_redis_resource, SanicListener.AFTER_SERVER_START)
    app.register_listener(init_app_start_info, SanicListener.AFTER_SERVER_START)
    app.register_listener(ws_client.start, SanicListener.AFTER_SERVER_START)
    if with_rulechain:
        app.register_listener(run_sys_rulechain, SanicListener.AFTER_SERVER_START)
    if with_vfs:
        app.register_listener(run_vfs, SanicListener.AFTER_SERVER_START)
    # if with_stream:
    #     app.register_listener(run_stream, SanicListener.AFTER_SERVER_START)
    app.register_listener(_register_after_engine, SanicListener.AFTER_SERVER_START)


def register_stop_engine(app: Sanic) -> None:
    async def _register_stop_engine(app, loop):
        await asyncio.gather(*[actor.close() for actor_id, actor in engine.actors.items()])
        await engine.redis.close_pool()
        await engine.pubsub.close_pool()

    app.register_listener(_register_stop_engine, SanicListener.BEFORE_SERVER_STOP)


async def load_resource(app, loop):
    from runtime.ext import ModuleWrapper, load_wrapper
    from apps.runtime_entity import BaseModel
    import resources

    module_add_name_dict = {0: "func", 1: "enum", 2: "const", 3: "model", 4: "wf"}
    all_module_dict = {}

    def get_image_table_json() -> dict:
        file_name = "/".join(__name__.split(".")) + ".py"
        project_base_path = os.path.abspath(__file__).rstrip(file_name)
        out_base_path = f"{project_base_path}/resources/image_table.json"
        with open(out_base_path, "r") as f:
            image_table = ujson.load(f)
        return image_table

    def add_module_obj(module_name_list, obj_uuid_module, module_wrapper, obj_type=0):
        module_add_name = module_add_name_dict.get(obj_type, "func")
        sys_table_dict = sys_table_handler.make_sys_from_user_model()
        # app_log.info(f"module_name_list: {module_name_list}, obj_uuid_module: {obj_uuid_module}")
        for name in module_name_list:
            module_name = "_".join([name, module_add_name])
            if obj_type == 3:
                module_obj = getattr(obj_uuid_module, name, None)
            else:
                module_obj = getattr(obj_uuid_module, module_name, None)
            app_log.info(f"module_obj: {module_obj}")
            if module_obj is not None:
                obj_type_dict = all_module_dict.setdefault(name, dict())
                obj_dict = obj_type_dict.setdefault(obj_type, ModuleWrapper(_name=name))
                app_log.debug((name, obj_dict))
                module_wrapper.add_module(name, obj_dict)
                keys = dir(module_obj)
                for key in keys:
                    if not key.startswith("__"):
                        obj = getattr(module_obj, key)
                        if obj and obj.__class__ != type:
                            # gen_log.info(f"obj: {obj}")
                            if obj_type == 3:  # model
                                try:
                                    if issubclass(obj, BaseModel) and obj is not BaseModel:
                                        if key not in sys_table_dict:
                                            key = obj._meta.model_name
                                        obj_dict.add_module(key, obj)
                                except Exception:
                                    pass
                            else:
                                if obj_type == 0:  # func
                                    if not isinstance(obj, FuncWrapper):
                                        continue
                                app_log.debug((key, obj))
                                obj_dict.add_module(key, obj)
                gen_log.info(f"module: {module_obj.__name__}")

    r_path = os.path.dirname(os.path.abspath(resources.__file__))
    gen_log.info(f"r_path: {r_path}")
    app_name_list = []
    default_app_name = os.environ.get("APP_NAME") or "simulator11111"
    default_app_name = default_app_name.lower()
    for name in os.listdir(r_path):
        if name != default_app_name:
            continue
        app_name_list.append(name)
    if not app_name_list:
        raise RuntimeError("app not found")

    try:
        app_name = app_name_list[0]
        app_name_key = "." + app_name
        app_path = "/".join([r_path, app_name_list[0]])
        gen_log.info(f"app_path: {app_path}")
        app_module = importlib.import_module(app_name_key, resources.__name__)
        gen_log.info(f"app_module: {app_module.__name__}")
        module_name_list = []
        obj_uuid_map_key = ".obj_uuid_map"
        models_key = ".models"
        for name in os.listdir(app_path):
            full_path = "/".join([app_path, name])
            if os.path.isdir(full_path) and not name.startswith("__"):
                module_name_list.append(name)
        obj_uuid_module = importlib.import_module(obj_uuid_map_key, app_module.__name__)
        gen_log.info(f"obj_uuid_module: {obj_uuid_module.__name__}")
        models_module = importlib.import_module(models_key, app_module.__name__)
        gen_log.info(f"models_module: {models_module.__name__}")
        add_module_obj(module_name_list, obj_uuid_module, engine.funcs, obj_type=0)
        add_module_obj(module_name_list, obj_uuid_module, engine.enums, obj_type=1)
        add_module_obj(module_name_list, obj_uuid_module, engine.consts, obj_type=2)
        add_module_obj(module_name_list, models_module, engine.models, obj_type=3)
        add_module_obj(module_name_list, obj_uuid_module, engine.workflows, obj_type=4)
        # app_log.info(f"funcs: {engine.funcs}")
        image_table = get_image_table_json()
        for module, obj_dict in engine.models.items():
            module_dict = engine.modules.setdefault(module, ModuleWrapper(_name=module))
            module_image = load_wrapper(image_table.get(module, dict()))
            # 通过lemon.模块名.image_table.文档名.图片名访问图片表图片
            module_dict.update({"image_table": module_image})
            if obj_dict and isinstance(obj_dict, dict):
                module_dict.update(obj_dict)
            enum_dict = engine.enums.get(module, dict())
            if enum_dict and isinstance(enum_dict, dict):
                module_dict.update(enum_dict)
            const_dict = engine.consts.get(module, dict())
            if const_dict and isinstance(const_dict, dict):
                module_dict.update(const_dict)
            func_dict = engine.funcs.get(module, dict())
            if func_dict and isinstance(func_dict, dict):
                module_dict.update(func_dict)
        # gen_log.info(f"modules: {engine.modules}")
        model_uuid_list = [
            "model_uuid_map", "relationship_uuid_map", "field_uuid_map", "node_uuid_map", 
            "calc_fields"
        ]
        obj_uuid_list = [
            "func_uuid_map", "enum_uuid_map", "const_uuid_map", "wf_uuid_map"
        ]
        for key in model_uuid_list:
            uuid_map = getattr(models_module, key, None)
            if isinstance(uuid_map, dict):
                # gen_log.info(f"uuid_map: {uuid_map}")
                engine.uuids.add_module(key, uuid_map)
        for key in obj_uuid_list:
            uuid_map = getattr(obj_uuid_module, key, None)
            if isinstance(uuid_map, dict):
                engine.uuids.add_module(key, uuid_map)
        # for m_name, obj_dict in engine.funcs.items():
        #     for o_name, obj in obj_dict.items():
        #         app_log.info(f"name: {o_name}, func: {obj}")
        #         obj.new_func()
        # for func_wrapper in engine.uuids.func_uuid_map.values():
        #     func = func_wrapper.new_func()
        #     app_log.info(f"name: {func.name}, func: {func}")
        py_module_uuid_map = getattr(obj_uuid_module, "py_module_uuid_map", None)
        if not py_module_uuid_map:
            return
        for py_module in py_module_uuid_map.values():
            engine.modules.get(py_module.module_name).update(
                {py_module.py_module_name: ModuleWrapper(**py_module.locals)}
            )


    except Exception as e:
        app_log.error(print_exc())
        app_log.error(f"error file: {e}")


async def subscribe_redis_resource(app, loop):
    from runtime.ext import ModuleWrapper
    class MemoryResources:
        name_ref = MemoryResource()
        obj_uuid_ref = dict()

    memory_resources = MemoryResources()
    GlobalVars.set('memory_resources', memory_resources)

    async def on_message(msg: dict):
        # app_log.info(msg)
        message = DocUpdateMessage.parse_obj(msg)
        if message.doc_type == DocumentType.FUNC:
            namespace = {"FuncWrapper": FuncWrapper}
            byte_code = compile_restricted(message.content)
            eval(byte_code, namespace)
            func_name = ""
            func_obj = None
            for key, val in namespace.items():
                if key == "FuncWrapper" or key.startswith("__"):
                    continue
                if isinstance(val, FuncWrapper):
                    func_name = key
                    func_obj = val
                    break
            module_dict = memory_resources.name_ref.setdefault(message.module_name, ModuleWrapper())
            if not module_dict:
                engine_module_dict = engine.modules.get(message.module_name, ModuleWrapper())
                module_dict.update(engine_module_dict)
            module_dict.update({func_name: func_obj})
            memory_resources.obj_uuid_ref.update({message.obj_uuid: func_obj})

    topic = f"{app.config.REDIS_CHANNEL_DOC_FULL_UPDATE_KEY}:{app.config.APP_UUID}:*"
    actor = LemonPubsubActor(topic, is_pattern=True)
    actor.on_message = on_message


async def init_app_start_info(app, loop):

    key = "init_app_lock:" + app.config.APP_UUID
    try:
        init_app_lock = await engine.redlock.lock(key, lock_timeout=30)
        async with init_app_lock:
            await init_redis_info(app)
    except Exception as e:
        import traceback
        traceback.print_exc()
        app_log.error(f"init_app_lock error: {e}")
    asyncio.create_task(listen_server(app))


async def listen_server(app):
    # 实现订阅app-server消息, 进行跨进程的处理, 订阅与rulechain中不同的频道
    app_uuid = app.config.APP_UUID
    subscribe_channel = "server:" + app_uuid + ":" + RedisChannel.APP
    subscriber = await engine.redis.redis.psubscribe(subscribe_channel)
    async for channel, message in subscriber[0].iter():
        asyncio.create_task(handle_message(channel, message))


@timeit_async
async def init_redis_info(app):
    await init_app_role_tag(app)
    return


async def init_app_role_tag(app):
    lemon_redis = LemonRedis()
    app_uuid = app.config.APP_UUID
    with temp_unlock(lemon_redis):
        all_tags = await engine.access.list_tag(app_uuid)
        db_all_tags = []
        documents_uuid = {}
        for tag in all_tags:
            db_all_tags.append(tag["id"])
            documents_uuid.update({tag["id"]: tag["document_uuid"]})
        document_contents = await engine.access.list_document_content_by_app_uuid_document_type(app_uuid, 30)
        tag_setting_dict = {}
        for dc in document_contents:
            c = dc.get("document_content", {})
            setting = c.get("setting", {})
            for tag_id in {k: v for k, v in documents_uuid.items() if v == dc.get("document_uuid")}:
                tag_setting_dict.update({tag_id: setting})
        db_all_tags_set = set(db_all_tags)
        key = "all_permission_config_tags"
        permission_config = await engine.user_access.get_system_config(key=key)
        existed_tags_set = set()
        if permission_config:
            permission_config_value = permission_config.get("json_value", {})
            existed_tags_set = set(permission_config_value.get("tags", []))
        if not existed_tags_set and not db_all_tags:
            return
        await process_db_tag_change(app_uuid, db_all_tags_set, existed_tags_set, tag_setting_dict)
        if permission_config:
            permission_config_value.update({"tags": db_all_tags})
            await engine.user_access.update_system_config(**permission_config)
        else:
            permission_config = {
                    "key": key,
                    "config_value": "",
                    "json_value": {"tags": db_all_tags},
                    "description": "所有权限资源标签"
                }
            await engine.user_access.add_system_config(**permission_config)


async def process_db_tag_change(app_uuid, db_all_tags_set, existed_tags_set, tag_setting_dict):
    """
    设计时的tag发生变化(新增/删除), 需要更新到运行时配置中
    """
    app_tenant = await engine.user_access.list_app_tenant(app_uuid)
    db_default_select_tags_set = db_all_tags_set & {
        k for k in tag_setting_dict if tag_setting_dict[k].get("default_select", True)}
    new_tags_id = db_default_select_tags_set - existed_tags_set
    table_name = make_runtime_table_name(
        app_uuid, None, PermissionConfig._meta.class_table_name)
    model = make_model_copy(PermissionConfig, table_name)

    for t in app_tenant:
        tenant_uuid = t["tenant_uuid"]
        async with current_user_system_ctx(app_uuid, tenant_uuid, lsm=None):
            await del_tag(model, db_all_tags_set)
            await add_tag(app_uuid, model, new_tags_id, db_default_select_tags_set, tenant_uuid)


async def del_tag(model, all_tags_id):
    query = model.delete().where(model.to_tag.not_in(all_tags_id))
    query._run_model_event = False
    r = await engine.userdb.objs.execute(query)
    app_log.info(f"del_tag: {r}")


async def add_tag(app_uuid, model, new_tags_id, db_all_tags_set, tenant_uuid):
    """数据库里有记录的关联, 也新增上关联"""
    user_role_list = await engine.access.list_user_role_by_app_uuid(app_uuid=app_uuid)
    user_role_ids = {ur["id"]: ur for ur in user_role_list}
    keys = ["_".join([str(id), "permission_config"]) for id in user_role_ids]
    permission_config = await engine.user_access.list_system_config(keys, tenant_uuid=tenant_uuid)
    permission_config_dict = {c["key"]: c["json_value"] for c in permission_config}

    data_list = []
    for to_role in user_role_ids:
        key = "_".join([str(to_role), "permission_config"])
        if key not in permission_config_dict:
            need_add_tagd = db_all_tags_set
            system_config_data = {
                "key": "_".join([str(to_role), "permission_config"]),
                "config_value": "",
                "json_value": {"set": True},
                "description": "角色权限资源相关参数",
                "tenant_uuid": tenant_uuid
            }
            await engine.user_access.add_system_config(**system_config_data)
        else:
            need_add_tagd = new_tags_id
        for to_tag in need_add_tagd:
            data_list.append({model.to_role.name: to_role, model.to_tag.name: to_tag})
    if data_list:
        query = model.insert_many(data_list)
        query._run_model_event = False
        await engine.userdb.objs.execute(query)


async def get_db_permissions(app_uuid):
    # TODO 是否应该让开发时访问lemon库
    import resources
    app_name = os.environ.get("APP_NAME")
    sys_module = importlib.import_module(
        "." + app_name.lower() + ".系统模块.lemon_model", resources.__name__)
    user_role: UserRole = sys_module.lemon_userrole
    tag: Tag = sys_module.lemon_tag

    table_name = make_runtime_table_name(
        app_uuid, None, PermissionConfig._meta.class_table_name)
    model = make_model_copy(PermissionConfig, table_name)
    fields = (
        model.id,
        model.tenant_uuid,
        tag.tag_uuid,
        user_role.role_uuid,
        tag.action
    )
    query = model.select(*fields).join(tag, on=(tag.id == model.to_tag)).join(
        user_role, on=(user_role.id == model.to_role)).dicts()
    query._run_model_event = False
    query.select_all = True
    return await engine.userdb.objs.execute(query)
