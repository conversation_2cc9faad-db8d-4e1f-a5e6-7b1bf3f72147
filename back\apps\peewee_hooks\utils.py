from peewee import BaseQuery
from apps.entity import <PERSON>M<PERSON>l, Document, DocumentContent
from typing import Optional, Union, Coroutine
from asyncio import Task
import asyncio
from apps.ide_const import DocUpdateType
from contextlib import asynccontextmanager
from contextvars import ContextVar
from baseutils.utils import LemonContextVar

def get_doc_info_from_query(sender: DocumentContent, query: BaseQuery):
    insert = getattr(query, "_insert", None)
    update = getattr(query, "_update", None)
    where = getattr(query, "_where", None)
    document_uuid, document_content, update_type = None, None, None
    if insert:
        document_uuid = insert.get(sender.document_uuid)
        document_content = insert.get(sender.document_content)
        update_type = DocUpdateType.INSERT
    elif update:
        document_content = update.get(sender.document_content)
        document_uuid = getattr(where, 'rhs', None)
        update_type = DocUpdateType.UPDATE
    return document_uuid, document_content, update_type


use_interceptor = ContextVar("use_interceptor", default=True)
run_in_hook = ContextVar("run_in_hook", default=False)


@asynccontextmanager
async def without_interceptor():
    use_interceptor.set(False)
    try:
        yield
    finally:
        use_interceptor.set(True)


def run_hook_task(task: Union[Coroutine, Task]):
    if run_in_hook.get(None):
        # hook 里不需要触发hook
        return None
    else:
        async def wrapper():
            token = run_in_hook.set(True)
            try:
                return await task
            finally:
                run_in_hook.reset(token)

        cache_tasks = LemonContextVar.atomic_cache_tasks.get()
        cache_tasks.append(asyncio.ensure_future(wrapper()))
        # loop = asyncio.get_event_loop()
        # return asyncio.run_coroutine_threadsafe(wrapper(), loop)
