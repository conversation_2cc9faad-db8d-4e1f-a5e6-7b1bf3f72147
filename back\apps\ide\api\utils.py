import json
from io import BytesIO

from PIL import Image

from apps.base_utils import check_lemon_uuid
from baseutils.const import Code
from apps.entity import ImageTable
from apps.engine import engine
from apps.helper import DesignOss
from apps.ide_const import IDECode, ImageType
from apps.utils import LemonDictResponse as LDR
from runtime.const import LemonRuntimeErrorCode as LREC


# 处理图片
async def process_image(document_uuid, image_uuid, image_type,
                        image, is_create):
    if not all([document_uuid, check_lemon_uuid(image_uuid)]):
        return LDR(Code.ARGS_ERROR)

    image_type_invalid_error = check_image_type(image_type)
    if image_type_invalid_error:
        return LDR(IDECode.NOT_SUPPORT_ON_IMAGES_TYPE)
    if image[1][4:12] == b'ftypavif':
        return LDR(LREC.NOT_SUPPORT_ON_AVIF_ENCODED_IMAGES)
    document = await engine.access.get_document_by_document_uuid(document_uuid)
    if not document:
        return LDR(IDECode.DOCUMENT_NOT_EXISTS)

    image_obj = None
    image_body = image.body
    if image_type != ImageType.SVG:
        image_obj = Image.open(BytesIO(image_body))
    image_sql_dict = gen_image_table_dict(
        document, image_uuid, image_type, image_obj, is_create)

    # 处理存OSS
    headers = {
        "x-oss-meta-filetype": "image"
    }
    image_path = image_sql_dict.get(ImageTable.url.name)
    await DesignOss().put_oss_object(
        image_path, image_body, headers, local_file=False)

    return image_sql_dict


# 检查图像类型
def check_image_type(image_type):
    if image_type not in ImageType.ALL:
        return Code.IMAGE_TYPE_INVALID


# 获取图像路径
def gen_image_path(document, image_uuid, image_type):
    app_uuid = document.app_uuid
    module_uuid = document.module_uuid
    document_uuid = document.document_uuid
    path = DesignOss().gen_oss_design_path(
        app_uuid=app_uuid, module_uuid=module_uuid, 
        document_uuid=document_uuid)
    image_type_str = ImageType.DICT.get(image_type)
    image_whole_name = image_uuid + image_type_str
    return "/".join([path, image_whole_name])


# 获取图像数据字典
def gen_image_table_dict(document, image_uuid, image_type, 
                         image_obj=None, is_create=True):
    image_path = gen_image_path(document, image_uuid, image_type)
    image_dict = {
        ImageTable.image_type.name: image_type,
        ImageTable.width.name: 0,
        ImageTable.height.name: 0,
        ImageTable.url.name: image_path,
    }
    if image_obj:
        image_dict.update({
            ImageTable.width.name: image_obj.width,
            ImageTable.height.name: image_obj.height,
        })
    if is_create:
        app_uuid = document.app_uuid
        module_uuid = document.module_uuid
        document_uuid = document.document_uuid
        image_dict.update({
            ImageTable.app_uuid.name: app_uuid,
            ImageTable.module_uuid.name: module_uuid,
            ImageTable.document_uuid.name: document_uuid,
            ImageTable.image_uuid.name: image_uuid,
        })
    return image_dict


# 处理存数据库
async def save_image(image_uuid, image_sql_dict, is_create):
    if is_create:
        query_func = engine.access.create_image(**image_sql_dict)
    else:
        query_func = engine.access.update_image_by_image_uuid(
            image_uuid, **image_sql_dict)            
    async with engine.db.objs.atomic():
        await query_func


# 获取签名url
def get_domain_url_by_image_path(image_sql_dict):
    image_path = image_sql_dict.get(ImageTable.url.name)
    url = engine.oss_handler.bucket.sign_url("GET",
                                             image_path,
                                             3600*24*365*10,
                                             slash_safe=True)
    domain_url = DesignOss().gen_oss_domain_url(url)
    return domain_url
