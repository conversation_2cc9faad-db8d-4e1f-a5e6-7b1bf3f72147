class Config(object):
    REDIS_HOST = "redis-{{app_env}}"  # redis 地址
    REDIS_PORT = 6379  # redis 端口
    REDIS_ADDRESS = (REDIS_HOST, REDIS_PORT)
    MYSQL_HOST = "mysql-{{app_env}}"  # mysql 地址
    MYSQL_PORT = 3306  # mysql 端口
    MYSQL_USER = "{{mysql_user}}"# mysql 用户
    MYSQL_PASSWORD = "{{mysql_password}}" # mysql 用户密码
    ENDPOINT = "{{endpoint}}"
    LOCAL_DEPLOY = True
    OSS_STATUS = {{oss_status}}
    RESPONSE_TIMEOUT = {{api_response_timeout}}
    PRINT_DOMAIN = "print-{{app_env}}"
    WATERMARK_API_HOST_PORT = "http://127.0.0.1:6900"
    APP_DOMAIN = "{{server_address}}"
    DOMAIN = "{{server_host}}"
    MYSQL_PORT_EXPOSE = "{{mysql_port}}"
    REQUEST_MAX_SIZE = {{request_max_size}}
    CUSTOM_APP_DOMAIN="{{custom_server_address}}"
    LOG_SYSTEM = False
    MINIO_ACCESS_KEY = "minio"
    MINIO_SECRET_KEY = "minio123"
    MINIO_HOST = "minio-{{app_env}}"
    MINIO_PORT = 9000
    KAFKA_HOSTS = {{kafka_hosts}}
