# -*- coding:utf-8 -*-

import sys
import abc
import copy
import time
import weakref
import asyncio
import traceback
from typing import Dict, List, AnyStr, Union
from functools import partial

import ujson
from actorio.actor import Actor
from transitions.extensions.asyncio import HierarchicalAsyncMachine as HAMachine

from baseutils.log import app_log
from baseutils.utils import LemonContextVar
from apps.utils import lemon_uuid
from apps.ide_const import StateMachine, TriggerType

from runtime.engine import engine
from runtime.const import LemonStateMachineType
from runtime.utils import LemonMessage
from runtime.core.utils import (
    SM_VARIABLE_KEY_LIST, build_topic, LemonCommand
)
from runtime.core.utils import SystemVariableProxy
from runtime.core.actor import LemonPubsubActor
from runtime.core.event import Event
from runtime.core.variable import Variable, VariableProxy
from runtime.core.timer import LemonTimer
from runtime.core.state import LemonState, EndState
from runtime.core.trigger import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rigger, TimerTrigger
from runtime.core.transition import Transition
from runtime.core.action import run_action_list
from runtime.core.func import LemonSMValueEditor
from runtime.core.connector import BaseClientConnector
from runtime.core.namespace import lemon_wrapper
from runtime.log import runtime_log


HAMachine.separator = ":"
PERFIX_KEY_EVENT = "event_"
PREFIX_KEY_TIMER = "timer_"

class Machine(HAMachine):
    async def callbacks(self, funcs, event_data):
        """ Triggers a list of callbacks """
        for func in funcs:
            await event_data.machine.callback(func, event_data)


# 状态机中创建页面
async def create_page(sid, page_uuid, data_context=None, page_context=None, parent_machine_id=None):
    client = engine.clients.get(sid, None)
    if client is not None:
        await client.create_page(
            page_uuid, data_context, page_context=page_context, 
            parent_machine_id=parent_machine_id
        )


def add_event(events, event_uuid, state, trigger, timer=False):
    """
    将 触发器 绑定到一个以 事件名称 为key的self.events字典中
    """
    trigger.event_set.add(event_uuid)
    if timer:
        event_uuid_key = PREFIX_KEY_TIMER + event_uuid
    else:
        event_uuid_key = PERFIX_KEY_EVENT + event_uuid
    event_dict = events.setdefault(event_uuid_key, dict())
    trigger_list = event_dict.setdefault(state.name, list())
    trigger_list.append(trigger)


def init_state_list(state_list, states, events):
    for s_dict in state_list:
        state = LemonState(**s_dict)
        states.append(state)
        for t_dict in state.trigger_list:
            trigger: Union[EventTrigger, TimerTrigger] = LemonTrigger(**t_dict)
            state.triggers.append(trigger)
            if trigger.type == TriggerType.TIMER:
                state.timer_triggers.append(trigger)
                add_event(events, trigger.timer_uuid, state, trigger, timer=True)
            elif trigger.type == TriggerType.EVENT:
                state.event_triggers.append(trigger)
                for event_dict in trigger.event_list:
                    event_uuid = event_dict.get("event", None)
                    bind_list = event_dict.get("bind_list", [])
                    arg_dict = trigger.event_arg_dict.setdefault(event_uuid, {})
                    for bind in bind_list:
                        event_arg_name = bind.get("event_arg_name")
                        variable_uuid = bind.get("variable_uuid")
                        arg_dict.update({event_arg_name: variable_uuid})
                    add_event(events, event_uuid, state, trigger)


class BaseCommondHandler(object):

    def __init__(self, lsm):
        self.lsm: BaseLemonStateMachine = lsm

    @property
    def lsm(self):
        return None if self._lsm is None else self._lsm()

    @lsm.setter
    def lsm(self, value):
        self._lsm = None if value is None else weakref.ref(value)

    def clean(self):
        self.lsm = None

    async def run_command(self, msg):
        if msg.command:
            command = msg.command
            command_str = "handle_" + command
            command_func = getattr(self, command_str, None)
            func_callable = callable(command_func)
            app_log.info(f"command: {command}, callable: {func_callable}, self: {self}")
            if func_callable:
                await command_func(msg)
        elif msg.action:
            app_log.info(f"command: model_update, action: {msg.action}")
            await self.handle_action_model_update(msg)

    async def handle_action_model_update(self, msg):
        app_log.info(f"model_update: {msg}")

    async def handle_event(self, msg):
        await self.lsm.component.event(msg)

    async def handle_editor_result(self, msg):
        await self.lsm.component.editor_result(msg)


class SystemVariableAccessor(object):

    def __get__(self, instance, instance_type=None):
        if instance is not None:
            instance._sm_proxy.obj = instance
            return instance._sm_proxy
        return None


class StateMachineMetaClass(type):

    def __new__(cls, name, bases, attrs):
        """
        cls: 需要创建的类
        name: 需要创建的类名
        bases: 需要创建的类的所有父类
        attrs: 需要创建的类的所有属性、方法组成的字典
        """
        sm_dict = attrs.get("sm_dict")
        if isinstance(sm_dict, dict) and sm_dict:
            event_list = sm_dict.get("event_list")
            variable_list = sm_dict.get("variable_list")
            attrs["event_list"] = event_list
            attrs["variable_list"] = variable_list
            attrs["timer_list"] = sm_dict.get("timer_list")
            event_uuid_dict, event_name_dict = {}, {}
            for e_dict in event_list:
                event = Event(e_dict)
                event_uuid_dict.update({event.uuid: event})
                event_name_dict.update({event.name: event})
            attrs["event_uuid_dict"] = event_uuid_dict
            attrs["event_name_dict"] = event_name_dict
            start_state_dict = sm_dict.get("start_state")
            state_list = [start_state_dict]
            state_list.extend(sm_dict.get("state_list"))
            attrs["state_list"] = state_list
            class_states, events = [], {}
            init_state_list(state_list, class_states, events)
            attrs["class_states"] = class_states
            attrs["events"] = events
            global_variables = []
            for v_dict in variable_list:
                variable = Variable(v_dict)
                global_variables.append(variable)
            attrs["global_variables"] = global_variables
        cls = super(StateMachineMetaClass, cls).__new__(cls, name, bases, attrs)
        return cls


class BaseLemonStateMachine(metaclass=StateMachineMetaClass):

    lsm_type = LemonStateMachineType.BASIC
    is_stopping = False
    is_stopped = False
    event_uuid_dict = {}
    event_name_dict = {}
    global_variables = []
    class_states: LemonState = []
    sm_dict = dict()
    events = dict()
    # _sm = SystemVariableProxy()
    _sm = SystemVariableAccessor()
    connector = BaseClientConnector()
    run_condition = True  # 是否执行触发器 条件

    def __init__(
            self, machine_id=None, root_machine_id=None,
            parent_machine_id=None, parent=None, p_context=None,
            init_start=True, **kwargs):
        self.machine_id = lemon_uuid() if machine_id is None else machine_id
        self.init_data()
        self.init_utils()
        self.root_machine_id = root_machine_id
        self.parent_machine_id = parent_machine_id
        self.p_context = dict() if p_context is None else p_context
        self.parent = parent
        self.command_handler = BaseCommondHandler(self)
        self.build_topic_func = build_topic
        self.initialize(init_start, kwargs)

    def initialize(self, init_start=True, kwargs=None):
        if not self.sm_dict and kwargs:
            self.event_list = kwargs.get("event_list")
            self.variable_list = kwargs.get("variable_list")
            self.timer_list = kwargs.get("timer_list")
            start_state_dict = kwargs.get("start_state")
            self.state_list = [start_state_dict]
            self.state_list.extend(kwargs.get("state_list", []))
            self.init_event_list()
            self.init_variable_list()
            init_state_list(self.state_list, self.states, self.events)
            for state in self.states:
                state.init_data(init_variable_list=True)
                self.state_uuid_dict.update({state.uuid: state})
                self.state_name_dict.update({state.name: state})
        else:
            self.clone_global_variables()
            self.clone_states()
        self.init_event_topics()
        self.init_timer_list()
        self.init_timer_triggers()
        self.result_topic = self.build_result_topic()
        if init_start:
            self.init_start()

    def init_data(self):
        self.timer_uuid_dict: Dict[AnyStr, LemonTimer] = dict()
        self.timer_name_dict: Dict[AnyStr, LemonTimer] = dict()
        self.global_variable_uuid_dict: Dict[AnyStr, Variable] = dict()
        self.global_variable_name_dict: Dict[AnyStr, Variable] = dict()
        self.states: List[LemonState] = []
        self.state_uuid_dict: Dict[AnyStr, LemonState] = dict()
        self.state_name_dict: Dict[AnyStr, LemonState] = dict()
        self.current_state: LemonState = None
        self.current_event: Event = None
        self.child_variable_dict = dict()  # 子状态机结束时，存放子状态机的变量
        self._on_before = None
        self._on_after = None
        self._on_failed = None
        self._on_null = None
        self.event_topics = list()
        self.timer_topics = list()

    def init_utils(self):
        self._editor = LemonSMValueEditor()  # 计算状态机内部值编辑器数据
        self._editor.lsm = self
        self._sm_proxy = SystemVariableProxy(SM_VARIABLE_KEY_LIST)
        self._lemon = lemon_wrapper.clone()
        self._lemon.system.update_user(self.connector.current_user)
        self._lemon.sm = self._sm
        self._lemon.utils.lemon_editor = self._editor
        self._globals = VariableProxy()

    async def clean(self):
        pass
        # self.event_list = list()
        # self.timer_list = list()
        # self.init_data()
        # self.run_on_event_task.cancel()
        # if self.command_handler is not None:
        #     self.command_handler.clean()
        #     self.command_handler = None
        # self._parent = None
        # self.p_context = dict()
        # self.connector = None
        # self._lemon.system = None
        # self._lemon.sm = None
        # self._lemon = None
        # self._sm = None
        # self._globals = None
        # app_log.info(f"sys.getrefcount(self.component): {sys.getrefcount(self.component)}")

    @property
    def parent(self):
        return None if self._parent is None else self._parent()

    @parent.setter
    def parent(self, value):
        self._parent = None if value is None else weakref.ref(value)

    @property
    def lemon(self):
        return self._lemon

    @property
    def editor(self):
        return self._editor

    @property
    def sm(self):
        return self._sm

    @property
    def globals(self):
        if not self._globals:
            self._globals.update(self.global_variable_name_dict)
        return self._globals

    @property
    def locals(self):
        return self.current_state.context

    def get_system_variable(self, key):
        """
        这里如果需要的系统变量不仅仅只是一个字符串
        如果需要返回对象，那这些对象还需要代理继续处理，保证对象能只读
        """
        value = getattr(self, key, None)
        # if isinstance(value, LemonState):
        #     value = value.name
        if isinstance(value, Event):
            value = value.name
        return value

    def _update_variable_value(self, variable, value, replace=True):
        # 这里目前只处理 submit_row_list 的数据， 这个数据一定是个 list
        update = False
        source_value = copy.deepcopy(variable.value)
        app_log.info(f"variable: {variable}, s_value: {source_value}, value: {value}")
        if replace:
            source_value = value
        else:
            if isinstance(source_value, list):
                source_value.extend(value)
            elif isinstance(source_value, dict):
                source_value.update(value)
            else:
                source_value = value
        if isinstance(source_value, list):
            # source_value = list(set(source_value))  # 按 PK 排序
            if source_value != variable.value:
                app_log.info(f"variable: {variable}, source_value: {source_value}, value: {value}")
                variable.value = source_value
                update = True
        else:
            if source_value != variable.value:
                variable.value = source_value
                update = True
        app_log.info(f"variable: {variable}, value: {variable.value}, update: {update}")
        return update

    async def _get_event(self, event_uuid) -> Event:
        event = self.event_uuid_dict.get(event_uuid)
        app_log.info(f"event: {event}, event_uuid: {event_uuid}")
        return event

    async def on_entry(self):
        # 更新当前状态
        # app_log.info(f"on entry: {self.current_state.name}")
        self.current_state.entry_timestamp = int(time.time())
        action_list = self.current_state.entry_list
        if action_list:
            return await run_action_list(
                action_list, self, self.current_state)
        return None

    async def on_exit(self):
        # app_log.info(f"on exit: {self.current_state.name}")
        action_list = self.current_state.exit_list
        if action_list:
            return await run_action_list(
                action_list, self, self.current_state)
        return None

    def build_event_topic(self, *args):
        # 接收事件
        key = "event"
        return self.build_topic_func(self.machine_id, key, *args)

    def build_timer_topic(self, *args):
        # 接收定时器事件
        key = "timer"
        return self.build_topic_func(self.machine_id, key, *args)

    def build_result_topic(self):
        # 发送状态机通知
        key = "result"
        return self.build_topic_func(self.machine_id, key)

    def build_command_topic(self):
        # 接收状态机命令
        key = "command"
        return self.build_topic_func(self.machine_id, key)

    @property
    def on_before(self):
        # 开始转换状态前的回调
        return self._on_before

    @on_before.setter
    def on_before(self, func):
        self._on_before = func

    @property
    def on_after(self):
        # 转换成功后的回调
        return self._on_after

    @on_after.setter
    def on_after(self, func):
        self._on_after = func

    @property
    def on_failed(self):
        # 转换失败后的回调， 条件匹配不成功（或默认条件未指定）
        return self._on_failed

    @on_failed.setter
    def on_failed(self, func):
        self._on_failed = func

    @property
    def on_null(self):
        # 状态未定义触发器的回调
        return self._on_null

    @on_null.setter
    def on_null(self, func):
        self._on_null = func

    def set_variable_value(self, variable, value):
        if not isinstance(variable, Variable):
            variable = self.global_variable_uuid_dict.get(variable)
            if not isinstance(variable, Variable):
                variable = self.current_state.variable_uuid_dict.get(variable)
        if isinstance(variable, Variable):
            variable.value = value

    def event_bind_variable(self, event_uuid, event_args, trigger):
        event_arg_dict = trigger.event_arg_dict
        # 此处拿到触发器上，所有 事件的参数 与 变量 的绑定关系
        # 有可能 事件的参数 没有绑定 变量
        arg_dict = event_arg_dict.get(event_uuid, dict())
        # app_log.info(f"set variables, arg_dict: {arg_dict}, event_arg_dict: {event_arg_dict}")
        for arg_name, value in event_args.items():
            # 此处的 variable 有可能为None，因为不是每个事件的参数，都绑定了变量
            variable_uuid = arg_dict.get(arg_name)
            variable = self.global_variable_uuid_dict.get(variable_uuid)
            if not variable:
                variable = self.current_state.variable_uuid_dict.get(variable_uuid)
            try:
                self.set_variable_value(variable, value)
            except Exception as e:
                app_log.error(e)

    async def before_transition(self):
        # app_log.info(f"on_before: {self.on_before}")
        if callable(self.on_before):
            await self.on_before(self)
        await self.on_exit()

    async def after_transition(self):
        # app_log.info(f"name: {self.current_state.name}")
        # 清理上个状态的变量
        self.current_event = None
        # app_log.info(f"on_after: {self.on_after}")
        if callable(self.on_after):
            await self.on_after(self)

        await self.try_auto_transition()
        if isinstance(self.current_state, EndState):
            # TODO: 应该 stop
            app_log.info("current_state: EndState")

    async def _do_transition(self, trigger: LemonTrigger, transition=True):
        await trigger.run_trigger_actions(self.current_state, self)
        if transition:
            dest_state = trigger.transition.dest_states[0]
            state = self.state_uuid_dict.get(dest_state)
            if state:
                self.current_state = self.state_uuid_dict.get(dest_state)
                await self.after_transition()

    async def try_auto_transition(self):
        # 这里认为 自动触发器 在状态中有且只有一个
        LemonContextVar.current_lsm.set(self)
        await self.on_entry()
        triggers = self.current_state.triggers
        for trigger in triggers:
            if trigger.type == TriggerType.AUTO:
                await self._do_transition(trigger)
                break

    async def do_transition(self, trigger):
        # app_log.debug(f"current: {self.current_state.event_set}, trigger: {trigger.event_set}")
        success = False
        if self.current_state.event_set == trigger.event_set:
            await self._do_transition(trigger, transition=self.run_condition)
            success = True
            self.current_state.event_set = set()
        return success

    async def handle_on_timer_message(self, timer_uuid):
        # app_log.info(f"timer uuid: {timer_uuid}")
        event_uuid = timer_uuid
        event_uuid_key = PREFIX_KEY_TIMER + event_uuid
        # app_log.debug(f"In self.events: {event_uuid_key in self.events}")
        event_dict: Dict[AnyStr, List[LemonTrigger]] = self.events.get(
            event_uuid_key, dict())
        trigger_state_name = self.current_state.name
        trigger_list = event_dict.get(trigger_state_name, list())
        # app_log.debug(f"trigger_list count: {len(trigger_list)}")
        timer_trigger_list = [t for t in trigger_list if t.type == TriggerType.TIMER]
        if timer_trigger_list:
            self.current_state.event_set.add(event_uuid)
            for trigger in timer_trigger_list:
                # 转换成功后，self.current_state 就会变化
                # 下面的判断 就可能不会成立
                if trigger_state_name == self.current_state.name:
                    success = await self.do_transition(trigger)
                    if success:
                        break
        else:
            # app_log.info(f"on_null: {self.on_null}")
            if callable(self.on_null):
                await self.on_null(self)

    async def handle_on_event_message(self, event_uuid, msg):
        LemonContextVar.current_lsm.set(self)
        await self._handle_on_event_message(event_uuid, msg)

    async def _handle_on_event_message(self, event_uuid, msg):
        # app_log.debug(f"event uuid: {event_uuid}")
        event = await self._get_event(event_uuid)
        if not event:
            app_log.debug(f"event: {event_uuid} , not found, exit.")
            return None

        self.current_event = event
        data = dict() if msg.data is None else msg.data
        event_args = dict()

        # 这里需要将 事件的所有 参数 及 参数默认值 拿到
        # 随后根据 消息中的值，替换 参数默认值
        data_event_args = data.get("event_args", dict())
        # app_log.debug(f"event.arg_name_dict: {event.arg_name_dict}")
        # 过滤传递的事件参数 （注意这里忽略了所有事件参数上未被定义，但用户传参的数据）
        for arg_name, arg in event.arg_name_dict.items():
            if arg_name in data_event_args:
                value = data_event_args[arg_name]
            else:
                value = await self.editor(arg.default_dict)
            event_args.update({arg_name: value})
            # app_log.info(f"arg: {arg_name}, value: {value}")
        """
        # 找到所有与 事件 相关联的 状态下的触发器
        # 并判断当前状态 是否与 事件关联的状态相符
        """
        event_uuid_key = PERFIX_KEY_EVENT + event_uuid
        # app_log.debug(f"In self.events: {event_uuid_key in self.events}")
        event_dict: Dict[AnyStr, List[LemonTrigger]] = self.events.get(
            event_uuid_key, dict())
        trigger_state_name = self.current_state.name
        # app_log.debug(f"trigger state name: {trigger_state_name}")
        # app_log.debug(f"event_dict: {event_dict}")
        trigger_list = event_dict.get(trigger_state_name, list())
        # app_log.debug(f"trigger_list count: {len(trigger_list)}")
        event_trigger_list = [t for t in trigger_list if t.type == TriggerType.EVENT]
        if event_trigger_list:
            self.current_state.event_set.add(event_uuid)
            for trigger in event_trigger_list:
                # 转换成功后，self.current_state 就会变化
                # 下面的判断 就可能不会成立
                if trigger_state_name == self.current_state.name:
                    self.event_bind_variable(event_uuid, event_args, trigger)
                    success = await self.do_transition(trigger)
                    if success:
                        break
        else:
            if callable(self.on_null):
                app_log.info(f"on_null: {self.on_null}")
                await self.on_null(self)

    async def on_event_message(self, msg: LemonMessage):
        """
        收到事件消息后，通过事件名称
        取出当前状态所有相关的触发器，检查触发器的定义的事件，是否已全部满足
        如果满足则进行转换
        """
        LemonContextVar.current_lsm.set(self)
        app_log.info(f"topic: {msg.topic}, data: {msg.data}")
        topic_split = msg.topic.split(":")
        event_type, event_uuid = topic_split[-2], topic_split[-1]
        current_connector = LemonContextVar.current_connector.get()
        if not current_connector:
            LemonContextVar.current_connector.set(self.connector)
        current_root_form = LemonContextVar.current_root_form.get()
        if current_root_form is None:
            current_root_form = lemon_uuid()
            app_log.info(f"start new context_atomic: {current_root_form}")
        app_log.info(f"root_form_uuid: {current_root_form}")
        async with engine.userdb.objs.atomic(current_root_form):
            if event_type == "timer":
                await self.handle_on_timer_message(event_uuid)
            elif event_type == "event":
                await self.handle_on_event_message(event_uuid, msg)

    async def on_lsm_message(self, msg):
        LemonContextVar.current_lsm.set(self)
        await self.command_handler.run_command(msg)

    async def timer_publish(self, timer_uuid: str, message: LemonMessage):
        topic = self.build_timer_topic(timer_uuid)
        # await self.connector.publish(topic, message)
        asyncio.create_task(self.connector.call(message, topic=topic))

    def add_state(self, state):
        machine = getattr(self, "machine", None)
        if machine is not None:
            machine.add_states(state)

    def init_event_list(self):
        for e_dict in self.event_list:
            event = Event(e_dict)
            self.event_uuid_dict.update({event.uuid: event})
            self.event_name_dict.update({event.name: event})
            topic = self.build_event_topic(event.uuid)
            self.event_topics.append(topic)

    def init_event_topics(self):
        for event_uuid in self.event_uuid_dict:
            topic = self.build_event_topic(event_uuid)
            self.event_topics.append(topic)

    def clone_global_variables(self):
        for gv in self.global_variables:
            variable = gv.clone(self)
            if variable.name in self.p_context:
                variable.set_value(self.p_context.get(variable.name))
            self.global_variable_uuid_dict.update({variable.uuid: variable})
            self.global_variable_name_dict.update({variable.name: variable})

    def clone_states(self):
        for state in self.class_states:
            new_state = state.clone(self)
            self.state_uuid_dict.update({new_state.uuid: new_state})
            self.state_name_dict.update({new_state.name: new_state})
            self.states.append(new_state)

    def init_timer_list(self):
        for t_dict in self.timer_list:
            timer = LemonTimer(**t_dict)
            self.timer_uuid_dict.update({timer.uuid: timer})
            self.timer_name_dict.update({timer.name: timer})
            topic = self.build_timer_topic(timer.uuid)
            self.timer_topics.append(topic)

    def init_variable_list(self):
        for v_dict in self.variable_list:
            variable = Variable(v_dict, lsm=self)
            # app_log.info(f"variable: {variable}, value: {variable.value}")
            # context 为父状态机 创建 子状态机时，传入的默认参数
            if variable.name in self.p_context:
                variable.set_value(self.p_context.get(variable.name))
            self.global_variable_uuid_dict.update({variable.uuid: variable})
            self.global_variable_name_dict.update({variable.name: variable})

    def init_timer_triggers(self):
        for state in self.states:
            for trigger in state.timer_triggers:
                timer: LemonTimer = self.timer_uuid_dict.get(trigger.timer_uuid, None)
                func = partial(trigger.send_timer_event, self, trigger.timer_uuid)
                # 如果不需要发消息，定时器可以直接回调状态机的 on_timer_call
                timer.add_on_timer(func)

    def init_start(self):
        self.add_topic_callbacks()
        self.start_timer()

    def build_topic_func_list(self):
        tf_list = []
        command_topic = self.build_command_topic()
        # app_log.info(f"command_topic: {command_topic}")
        tf_list.append((command_topic, self.on_lsm_message))
        topics = list()
        topics.extend(self.event_topics)
        topics.extend(self.timer_topics)
        # app_log.info(f"event topics: {self.event_topics}")
        # app_log.info(f"timer topics: {self.timer_topics}")
        on_event_func = self.on_event_message
        [tf_list.append((topic, on_event_func)) for topic in topics]
        return tf_list

    def add_topic_callbacks(self):
        topic_func_list = self.build_topic_func_list()
        self.connector.add_callbacks(topic_func_list)

    def start_timer(self):
        for _, timer in self.timer_uuid_dict.items():
            timer.start()

    def init_variables(self):
        for name, value in self.p_context.items():
            if name in self.global_variable_name_dict:
                variable = self.global_variable_name_dict.get(name)
                variable.set_value(value)

    async def async_start_machine(self, auto_transition=True):
        self.current_state = self.states[0]
        if auto_transition:
            await self.try_auto_transition()

    async def variable_publish(self, variable: Variable):
        pass

    async def start(self):
        self.is_stopping = False
        self.is_stopped = False
        self.init_variables()
        self.start_timer()

    async def stop(self):
        if not self.is_stopping:
            self.is_stopping = True
            for _, timer in self.timer_uuid_dict.items():
                timer.stop()
            # 清除云函数上设置的变量
            for global_name in self.globals.__dict__.copy():
                if global_name not in self.global_variable_name_dict.keys():
                    setattr(self.globals, global_name, None)
            self.is_stopped = True
            app_log.debug(f"m_id: {self.machine_id} stop done.")
            LemonContextVar.current_lsm.set(None)
