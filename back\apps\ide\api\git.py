from sanic_openapi import doc
import uj<PERSON>
from peewee import JO<PERSON>
from apps.permission import Permission
from apps.middlewares import LemonVCHTTPMethodView as HTTPMethodView, LemonHTTPMethodView
from apps.utils import (
    process_args, check_lemon_name, check_document_name, lemon_uuid, check_lemon_uuid,
    get_ext_tenant, restore_adapter
)
from apps.utils import (LemonDictResponse as LDR, publish_document_update)
from apps.entity import APPBranch, APPUserBranch, APP
from baseutils.const import Code, DOC, Action, SyncTORepoType
from apps.ide_const import IDECode
from gitapi.const import GitCode
from apps.engine import engine
from sanic.response import json, raw
from .util import bp, url_prefix
from collections import OrderedDict


class GetGitStatus(LemonHTTPMethodView):
    class GetGitStatusObj:
        app_uuid = doc.String("app uuid")
        branch_uuid = doc.String("branch uuid")
    @doc.consumes(GetGitStatusObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取版本控制状态")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            app_uuid = request.json.get("app_uuid")
            branch_uuid = request.json.get("branch_uuid")

        model = APPBranch
        query = model.select().where(model.app_uuid==app_uuid)
        branch_list = await engine.access.list_obj(model, query, as_dict=True)
        if not branch_list:
            return json(LDR(IDECode.GIT_PROJECT_NOT_FOUND))

        b_model = APPUserBranch
        fields = (
            b_model.app_uuid,
            b_model.branch_uuid,
            b_model.is_active,
            b_model.user_uuid,
            model.branch_name
        )
        query = model.select(*fields).join(b_model, on=((model.branch_uuid==b_model.branch_uuid)&(model.app_uuid==b_model.app_uuid)), join_type=JOIN.LEFT_OUTER
            ).where(model.app_uuid==app_uuid, b_model.user_uuid==user_uuid)
        if branch_uuid:
            query = query.where(b_model.branch_uuid==branch_uuid)
        branch_list = await engine.access.list_obj(model, query, as_dict=True)
        if not branch_list:
            return json(LDR(IDECode.GIT_PROJECT_NOT_FOUND))
        return json(LDR(data=list(branch_list)))

class FetchBranchDivergences(HTTPMethodView):

    class FetchOriginStatusObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")

    @doc.consumes(FetchOriginStatusObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取分支状态")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
            user_uuid = request.ctx.session.get("user_uuid")
        
        result = await engine.git_request.ftech_origin(app_uuid, branch_uuid, user_uuid)
        return json(LDR(data=result))

class PullOrigin(HTTPMethodView):

    class PullOriginObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")

    @doc.consumes(PullOriginObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("拉取更新")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
        res = await engine.git_request.sync_task_to_repo(SyncTORepoType.PULL, task_type=1)
        return json(LDR(data=res))


class GetStagedDoc(HTTPMethodView):

    class GetStagedDocObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        document_uuid = doc.String("document uuid")

    @doc.consumes(GetStagedDocObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取暂存")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
            document_uuid = request.json.get("document_uuid")
        full_path = None
        if document_uuid:
            document_info = await engine.access.get_document_detail_by_document_uuid(document_uuid=document_uuid, need_delete=True)
            if not document_info:
                return json(LDR(Code.ARGS_ERROR))
            full_path = document_info["full_path"]
        paths = [full_path] if full_path else None
        res = await engine.git_request.sync_task_to_repo(SyncTORepoType.GET_STAGED_CHANGES, paths=paths)
        def make_module_document_name(path): 
            if "/" not in path:
                return path
            path_split = path.split("/")
            module_name = path_split[0]
            document_name = path_split[-1]
            return module_name  + "|" + document_name
        paths = { make_module_document_name(r[2]): r for r in res}
        # paths = {}
        # for change in res:
        #     change_type, file_path, file_rename, content_before, content = change
        #     path = file_path
        #     if change_type == "R":
        #         path = file_rename
        #     if path.endswith("/"):
        #         continue
        #     # 不同模块下文件名不能重复
        #     path_split = path.split("/")
        #     module_name = path_split[0]
        #     document_name = path_split[-1]
        #     module_document_name = module_name  + "|" + document_name

        #     paths[module_document_name] = change
        
        docs = []
        if paths:
            res = await engine.access.list_document_by_module_document_name(app_uuid, list(paths.keys()))
            for doc in res:
                change_type, file_path, file_rename, content_before, content = paths[doc["module_document"]]
                doc.update({"change_type": change_type})
                if change_type == "R":
                    doc.update({"old_document_name": file_path})
                if content_before or content:
                    doc.update(dict(content_before=content_before, content=content))

                docs.append(doc)
        return json(LDR(data=docs))

class CommitAndSync(HTTPMethodView):
    class CommitDocsObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        type = doc.String("type")
        scopes = doc.List("scopes")
        subject = doc.String("subject")
        body = doc.String("body")

    @doc.consumes(CommitDocsObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("提交更新, commit->pull->push")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
            commit_type = request.json.get("type")
            scopes = request.json.get("scopes", [])
            subject = request.json.get("subject")
            body = request.json.get("body")
        res = await engine.git_request.sync_task_to_repo(SyncTORepoType.COMMIT_AND_SYNC, task_type=1, commit_type=commit_type, scopes=scopes, subject=subject,
                                body=body)
        return json(LDR(data=res))

class GetDocHistory(HTTPMethodView):

    class GetDocHistoryObj(object):
        app_uuid = doc.String("应用UUID")
        document_uuid = doc.String("document uuid")
        branch_uuid = doc.String("branch uuid")
        type = doc.String("历史类型, 0:全部 1：git 2：本地")
        current_page = doc.Integer("当前页数, default 1")
        page_size = doc.Integer("每页数量, default 10")

    @doc.consumes(GetDocHistoryObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取文档历史")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            document_uuid = request.json.get("document_uuid")
            branch_uuid = request.json.get("branch_uuid")
            history_type = request.json.get("type", 0)
            page_size = request.json.get("page_size", 10)
            current_page = request.json.get("current_page", 1)
        if current_page < 1:
            return json(LDR(Code.ARGS_ERROR))
        commit_history, local_history, output = [], [], []
        if history_type in [0, 1]:
            if getattr(request.ctx, "version_control", False):
                document_info = await engine.access.get_document_detail_by_document_uuid(document_uuid=document_uuid)
                if not document_info:
                    return json(LDR(Code.ARGS_ERROR))
                file_path = document_info["full_path"]
                commit_history = await engine.git_request.sync_task_to_repo(SyncTORepoType.DOC_HISTORY, file_path=file_path,
                                        page_size=page_size*current_page, current_page=1)
        if history_type in [0, 2]:
            local_history = await engine.access.list_document_localhistory(document_uuid, page_size, current_page)
        if history_type == 0:
            output = list(commit_history) + list(local_history)
            output.sort(key=lambda x: x["timestamp"], reverse=True)

        elif history_type == 1:
            output = list(commit_history)[(current_page-1)*page_size:current_page*page_size]
        elif history_type == 2:
            output = list(local_history)
        return json(LDR(data=output))

class GetHistoryContent(HTTPMethodView):
    class GetHistoryContentObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        type = doc.String("历史类型")
        version = doc.String("版本, 类型是git时commit id,类型是local时时间戳")
        document_uuid = doc.String("document uuid")

    @doc.consumes(GetHistoryContentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取文档历史内容")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
            history_type = request.json.get("type", 1)
            document_uuid = request.json.get("document_uuid")
            version = request.json.get("version")
        if history_type == 2:
            localhistory = await engine.access.get_document_localhistory(document_uuid, version)
            if localhistory:
                data = {"content_before": ujson.dumps(localhistory.document_content, indent=2, ensure_ascii=False)}
                return json(LDR(data=data))
            return json(LDR(IDECode.INVALID_LOCALHISTORY_VERSION))
        elif history_type == 1:
            document_info = await engine.access.get_document_detail_by_document_uuid(document_uuid=document_uuid)
            if not document_info:
                return json(LDR(Code.ARGS_ERROR))
            file_path = document_info["full_path"]
            res = await engine.git_request.sync_task_to_repo(SyncTORepoType.COMMIT_MODIFIED, file_path=file_path,
                                    version=version)
            if res:
                res = res[0]
            return json(LDR(data=res))
        return json(LDR(Code.ARGS_ERROR))
class CheckoutDoc(HTTPMethodView):
    class CheckoutDocVersionObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        document_uuid = doc.String("document uuid")
        version = doc.String("版本")

    @doc.consumes(CheckoutDocVersionObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("恢复历史文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
            document_uuid = request.json.get("document_uuid")
            version = str(request.json.get("version"))
        document = await engine.access.get_document_by_document_uuid(document_uuid)
        if not document:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        if version.isdigit():
            res = await engine.access.restore_document_content(document_uuid, int(version))
            document_content = res.get("document_content")
            data = {
                "document_content": document_content,
                "document_uuid": document_uuid,
                "document_version": document.document_version + 1
            }
            await publish_document_update(engine, document.app_uuid, document.module_uuid, 
                                          document.document_uuid, data, force_update=True)
            document_check_service = engine.service.document_check.create_service(document.document_type)
            if document_check_service:
                json_data = document.to_dict().copy()
                json_data.update({"document_content": res.get("document_content")})
                await document_check_service.check(json_data, document.document_type, check_updata=True, 
                                                   ignore_version=True, check_references=True)
            return json(LDR())
        document_info = await engine.access.get_document_detail_by_document_uuid(document_uuid=document_uuid)
        if not document_info:
            return json(LDR(Code.ARGS_ERROR))
        file_path = document_info["full_path"]
        res = await engine.git_request.sync_task_to_repo(SyncTORepoType.CHECKOUT, file_path=file_path,
                                version=version, document_uuid=document_uuid)
        return json(LDR(data=res))   

class CreateGit(HTTPMethodView):
    class CreateGitObj(object):
        app_uuid = doc.String("应用UUID")
        repo_type = doc.String("仓库类型, 0或不传：lemon gitlab")
        git_host = doc.String("git地址, 默认服务器配置")


    @doc.consumes(CreateGitObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("开启版本控制")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            login_as = request.ctx.session.get("login_as")
            team_permission = request.ctx.session.get("team_permission")
            sid = request.ctx.session.sid
            branch_uuid = request.json.get("branch_uuid")
            app_uuid = request.json.get("app_uuid")
            repo_type = request.json.get("repo_type", 0)
            git_host = request.json.get("git_host", engine.config.GITLAB_HOST)
        version_control = getattr(request.ctx, "version_control", False)
        if not version_control:
            if login_as is None or login_as == user_uuid:
                is_admin = await engine.access.is_app_admin(app_uuid, user_uuid)
            else:
                is_admin = False
                app_combine = await engine.access.list_combine_by_app_uuid(app_uuid)
                for combine in app_combine:
                    if combine["team_uuid"] == login_as:
                        is_admin = team_permission in [1,2]
                        break
            if not is_admin:
                return json(LDR(Code.PERMISSION_REQUIRED))

        json_body = {
            "app_uuid": app_uuid,
            "repo_type": repo_type,
            "git_host": git_host,
            "sid": sid,
            "user_uuid": user_uuid
        }
        if branch_uuid:
            json_body.update({"branch_uuid": branch_uuid})
        json_result = await engine.git_request.post("create_project.json", json_body)
        return json(LDR(data=json_result))

class GetBranchHistory(HTTPMethodView):
    class GetBranchHistoryObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        current_page = doc.String("current page")
        page_size = doc.String("page size")
        author = doc.String("author")
        message = doc.String("message")

    @doc.consumes(GetBranchHistoryObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取分支历史")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
            page_size = request.json.get("page_size", 10)
            current_page = request.json.get("current_page", 1)
            author = request.json.get("author")
            message = request.json.get("message")
        res = await engine.git_request.sync_task_to_repo( 
                                SyncTORepoType.DOC_HISTORY, file_path="", history_type=1,
                                page_size=page_size, current_page=current_page, author=author, message=message)
        return json(LDR(data=res))

class GetCommitModified(HTTPMethodView):
    class GetBranchHistoryObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        current_page = doc.String("current page")
        page_size = doc.String("page size")
        version = doc.String("version")

    @doc.consumes(GetBranchHistoryObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取提交修改")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            version = request.json.get("version")
        res = await engine.git_request.sync_task_to_repo( 
                                SyncTORepoType.COMMIT_MODIFIED, file_path=None, version=version, name_only=True)
        def make_module_document_name(path): 
            if "/" not in path:
                return path
            path_split = path.split("/")
            module_name = path_split[0]
            document_name = path_split[-1]
            return module_name  + "|" + document_name
        paths = { make_module_document_name(r["path"]): r for r in res}
        data = await engine.access.list_document_by_module_document_name(app_uuid, list(paths.keys()))
        out = []
        if data:
            for d in data:
                d.update({"change_type": paths[d["module_document"]].get("change_type")})
                out.append(d)
        return json(LDR(data=out))

class GetUnmergedDetail(HTTPMethodView):
    class GetUnmergedContentObj:
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        document_uuid = doc.String("document uuid")
    @doc.consumes(GetUnmergedContentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取冲突详情")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
            document_uuid = request.json.get("document_uuid")
        document_info = await engine.access.get_document_detail_by_document_uuid(document_uuid=document_uuid)
        if not document_info:
            return json(LDR(Code.ARGS_ERROR))
        file_path = document_info["full_path"]
        res = await engine.git_request.sync_task_to_repo(SyncTORepoType.GET_UNMERGED_DETAIL, file_path=file_path)
        return json(LDR(data=res))   


class SolveConflict(HTTPMethodView):
    class SolveConflictObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        type = doc.String("历史类型")
        version = doc.String("版本")

    @doc.consumes(SolveConflictObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("接收合并冲突")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            branch_uuid = request.json.get("branch_uuid")
        
        return json(LDR())



class CheckoutBranch(HTTPMethodView):
    class GetBranchObj(object):
        app_uuid = doc.String("应用UUID")
        branch_uuid = doc.String("branch uuid")
        type = doc.String("历史类型")
        version = doc.String("版本")

    @doc.consumes(GetBranchObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("切换分支")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            user_uuid = request.ctx.session.get("user_uuid")
            branch_uuid = request.json.get("branch_uuid")
        
        model = APPUserBranch
        app = await engine.access.get_app_by_app_uuid(app_uuid)
        if not app:
            return json(LDR(Code.APP_NOT_EXISTS))
        
        data = await engine.git_request.sync_task_to_repo(SyncTORepoType.GET_STAGED_CHANGES)
        if data:
            return json(LDR(GitCode.FILES_TO_COMMIT_CHECKOUT_BRANCH))
        query = model.select().where(model.app_uuid==app_uuid, model.user_uuid==user_uuid, model.branch_uuid==branch_uuid)
        user_branch = await engine.access.select_obj_by_query(model, query)
        init_repo = False if user_branch else True
        res = await engine.git_request.checkout_branch(app_uuid, app.app_name, branch_uuid, init_repo)
        return json(LDR(data=res))

class ListBranch(LemonHTTPMethodView):
    class ListBranchObj(object):
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ListBranchObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出分支")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
        model = APPBranch
        query = model.select().where(model.app_uuid==app_uuid)
        branch_list = await engine.access.list_obj(model, query, as_dict=True)
        return json(LDR(data=list(branch_list)))


class ListUserBranch(LemonHTTPMethodView):
    class GetUserBranch:
        app_uuid = doc.String("app uuid")
    @doc.consumes(GetUserBranch, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取用户分支")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            user_uuid = request.ctx.session.get("user_uuid")
        model = APPUserBranch
        b_model = APPBranch
        fields = (
            model.branch_uuid,
            model.is_active,
            b_model.branch_name,
            model.status
        )
        query = model.select(*fields).join(b_model, on=(model.branch_uuid==b_model.branch_uuid)).where(model.app_uuid==app_uuid, model.user_uuid==user_uuid)
        branch_list = await engine.access.list_obj(model, query, as_dict=True)
        return json(LDR(data=list(branch_list)))

class QueryGitTask(LemonHTTPMethodView):
    class QueryGitTaskObj:
        taskid = doc.String("taskid")
    @doc.consumes(QueryGitTaskObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取git task状态")
    async def post(self, request):
        with process_args():
            taskid = str(request.json.get("taskid", ""))

        data = await engine.git_request.query_task(taskid)
        return json(LDR(data=data))