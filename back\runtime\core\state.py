# -*- coding:utf-8 -*-

import time
import weakref
from typing import Dict, List, AnyStr


from baseutils.log import app_log
from baseutils.utils import clone_obj
from apps.utils import lemon_uuid
from apps.ide_const import StateType, HandleType, HandlerType, HandleCondition
from runtime.utils import Configurable
from runtime.core.variable import Variable, VariableProxy
from runtime.core.trigger import LemonTrigger, TimerTrigger, EventTrigger


class BaseState():

    def __init__(self, **kwargs):
        self.uuid = kwargs.get("uuid")
        self.type = kwargs.get("type", StateType.AUTO)
        self.entry_list = kwargs.get("entry_list", list())
        self.exit_list = kwargs.get("exit_list", list())
        self.variable_list = kwargs.get("variable_list", list())
        self.trigger_list = kwargs.get("trigger_list", list())
        self.event_triggers: List[EventTrigger] = []
        self.timer_triggers: List[TimerTrigger] = []
        self.triggers: List[LemonTrigger] = list()
        self.name = kwargs.get("name")
        self.context: VariableProxy = None
        self.init_data()
        self._lsm = None

    def __repr__(self):
        return "<%s('%s')('%s')@%s>" % (type(self).__name__, self.uuid, self.name, id(self))

    def clone(self, lsm):
        obj: BaseState = clone_obj(self, **{"lsm": lsm})
        obj.init_data(init_variable_list=True)
        return obj

    @property
    def lsm(self):
        return None if self._lsm is None else self._lsm()

    @lsm.setter
    def lsm(self, value):
        self._lsm = None if value is None else weakref.ref(value)

    def init_data(self, init_variable_list=False):
        self.event_set = set()
        self.entry_timestamp = None
        self.variable_uuid_dict: Dict[AnyStr, Variable] = dict()
        self.variable_name_dict: Dict[AnyStr, Variable] = dict()
        if init_variable_list:
            for v_dict in self.variable_list:
                variable = Variable(v_dict, lsm=self.lsm)
                self.variable_uuid_dict.update({variable.uuid: variable})
                self.variable_name_dict.update({variable.name: variable})
            self.context = VariableProxy(self.variable_name_dict)


class AutoState(BaseState):

    pass


class StartState(BaseState):

    def __init__(self, *args, **kwargs):
        kwargs.update({"name": "开始"})
        super().__init__(*args, **kwargs)


class EndState(BaseState):

    def __init__(self, *args, **kwargs):
        kwargs.update({"name": "结束"})
        super().__init__(*args, **kwargs)


class ChildState(BaseState):

    def __init__(self, *args, **kwargs):
        self.child_sm = kwargs.get("child_sm", list())
        self.recovery = kwargs.get("recovery", True)
        super().__init__(*args, **kwargs)


class ManualState(BaseState):

    def __init__(self, *args, **kwargs):
        self.hander_list = kwargs.get("handler_list", list())
        self.handle_type = kwargs.get("handle_type", HandleType.EACH)
        self.handle_condition = kwargs.get("handle_condition", HandleCondition.COUNT)
        self.handle_value = kwargs.get("handle_value", 1)
        self.page = kwargs.get("page")
        super().__init__(*args, **kwargs)


class LemonState(Configurable, BaseState):

    @classmethod
    def configurable_base(cls, *args, **kwargs):
        state_type = kwargs.get("type", StateType.AUTO)
        base_class = AutoState
        if state_type == StateType.START:
            base_class = StartState
        elif state_type == StateType.END:
            base_class = EndState
        elif state_type == StateType.CHILD:
            base_class = ChildState
        elif state_type == StateType.MANUAL:
            base_class = ManualState
        return base_class


if __name__ == "__main__":
    print(time.time())
    sm = "smsm"
    state_dict = {"uuid": lemon_uuid(), "name": "state", "type": StateType.CHILD}
    state = LemonState(sm, **state_dict)
    print(state)
    print(time.time())
