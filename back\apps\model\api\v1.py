# -*- coding:utf-8 -*-

from sanic import Blueprint
from sanic.exceptions import Unauthorized
from sanic.response import text
from sanic.views import HTTPMethodView
from sanic_openapi import doc

from baseutils.log import app_log

from apps.model.const import API

API_NAME_VERSION = "_".join([API.NAME, API.V1])
url_prefix = "/" + "/".join([API.NAME, API.V1])
bp = Blueprint(API_NAME_VERSION, url_prefix=url_prefix)


def protected():
    def decorator(f):
        def decorated_function(request, *args, **kwargs):
            raise Unauthorized("test")

        return decorated_function

    return decorator


class SimpleView(HTTPMethodView):

    # decorators = [protected]

    @doc.tag(url_prefix)
    @protected()
    def get(self, request):
        return text("I am get method")

    @doc.tag(url_prefix)
    def post(self, request):
        return text("I am post method")

    @doc.tag(url_prefix)
    def put(self, request):
        return text("I am put method")

    @doc.tag(url_prefix)
    def patch(self, request):
        return text("I am patch method")

    @doc.tag(url_prefix)
    def delete(self, request):
        return text("I am delete method")

    @doc.tag(url_prefix)
    def options(self, request):  # This will not be documented.
        return text("I am options method")


bp.add_route(SimpleView.as_view(), "/")
