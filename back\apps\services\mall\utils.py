from apps.services.document.page import pin_machine_and_variables
from apps.ide_const import NodeSystemActionType
from baseutils.const import SYSConfig
from apps.entity import RelationshipBasic
from apps.utils import replace_relationship_uuid


async def gen_lemon_fixed_uuids(extra_info):  # TODO 这个方法 需要写到v1中, 因为他太高层了
    """
    获取app执行中, 全部的柠檬固定的uuid或其他不可能改变的值
    包括: 系统模块, 系统应用, 系统用户, 事件controller,
    return:
        fixed_uuids[dict]: 柠檬固定的uuid或其他不可能改变的值 与 更新后的值的map
    """
    from apps.engine import engine
    uuid_template_to_app = extra_info

    uuid_template_to_app.update({k: k for k in pin_machine_and_variables})
    uuid_template_to_app.update({k: k for k in NodeSystemActionType.ALL_EVENT.keys()})
    uuid_template_to_app.update({
        SYSConfig.APP_UUID: SYSConfig.APP_UUID,
        SYSConfig.MODULE_UUID: SYSConfig.MODULE_UUID,
        SYSConfig.USER_UUID: SYSConfig.USER_UUID
    })

    sys_relationships = await engine.access.list_sys_relationship()
    sys_fields = await engine.access.list_field_by_module_uuid(SYSConfig.MODULE_UUID)

    field_fixed_keys = [
        "field_uuid", "model_uuid", "document_uuid"
    ]
    for field in sys_fields:
        for key in field_fixed_keys:
            uuid_template_to_app.update({field[key]: field[key]})

    rel_fixed_keys = [
        "relationship_uuid", "source_model", "target_model"
    ]

    for rel in sys_relationships:
        if rel.get("source_model") == rel.get("target_model"):
            relationship_uuid_source = replace_relationship_uuid(rel, RelationshipBasic, replace=False)
            relationship_uuid_target = replace_relationship_uuid(rel, RelationshipBasic, from_source=False)
            uuid_template_to_app.update({relationship_uuid_source: relationship_uuid_source})
            uuid_template_to_app.update({relationship_uuid_target: relationship_uuid_target})
        for key in rel_fixed_keys:
            uuid_template_to_app.update({rel[key]: rel[key]})

    return uuid_template_to_app


async def gen_app_publisher(engine, app_uuid=None, middle_user=None):
    if not middle_user and app_uuid:
        app_info = await engine.access.get_app_by_app_uuid(app_uuid)
        middle_user = app_info.user_uuid
    if not middle_user:
        return None, None
    publisher_info = await engine.access.get_app_publisher(middle_user)
    if publisher_info:
        publisher = publisher_info.get("id")
        publisher_name = publisher_info.get("name")
    else:
        publisher = None
        publisher_name = None
    return publisher, publisher_name
