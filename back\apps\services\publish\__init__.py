
import asyncio
import base64
from copy import copy, deepcopy
import traceback
import time
import os
import stat
import pickle
from typing import List

import ujson
import types
import uuid
import re
import urllib.parse
import yaml
from io import BytesIO
from PIL import Image
from collections import defaultdict, OrderedDict
from threading import Lock
from functools import partial, wraps
from aiohttp import ClientSession
from aiofiles import open as async_open
from distutils.version import StrictVersion
from tools.settings import LocalConfig
from peewee_async import Manager, PooledMySQLDatabase
from peewee import (JOIN, Proxy, Entity, Context, RawQuery, DatabaseError,
                    SQL, fn, NodeList, Expression, OP)
from peeweedbevolve import auto_detect_migrator, drop_foreign_key
import subprocess
from sanic import exceptions

# from baseutils.utils import translate_text
from apps.base_utils import check_lemon_name, lemon_uuid, make_model_copy
from apps.utils import BaseErrorHandler, get_sys_table_rel_table_name, make_runtime_table_name, copy_tenant_data
from baseutils.const import (Code, DocumentCheckError, LemonPublishError,
                        PermissionAction, SYSConfig, SystemNamespace,
                        SysModelFunc, UUID_TenantUser, UUID_User,
                        UUID_Department)
from apps.engine import engine
from apps.entity import (
    APP, APPExtension, DocumentContent, ModelBasic, ModelField, CheckMessage, APPPublish,
    RelationshipBasic, UserRoleContent, Sandbox, Localhistory, Document, Workflow, SubEnv, Module)
from apps.ide_const import (
    DocumentType, PythonKeyWords, LemonDesignerErrorCode as LDEC, RelationshipType)
from apps.user_entity import sys_table_handler, WorkflowInstance, NodeInstance, NodeTask, FieldAutoIncrement
from baseutils.log import app_log
from baseutils.peeweedbevolve_wrap import get_foreign_keys_by_table, _execute_, evolve_models
from apps.utils import (
    LemonErrorHandler, LemonJsonEncoder, MySQLErrorHandler,
    DocumentCheckErrorHandler, pre_process_self_associate,
    get_current_workspace, set_current_workspace, list_system_relationship)
from apps.config import Config as app_config
from baseutils.tracing import TracingContext
from .utils import (runtime_sys_table, jinja2_env, get_free_port, async_run,
                    local_deploy_template, runtime_sys_table_add, gen_real_model_view_sql,
                    update_ucenter_info, process_role_member_relationship, update_wf_tables_fk, process_redis_cache)
from .model import ModelGenerator
from . import config
from .config import project_base_path, CMD_LIST, COPY_LIST, DeployStatus
from .wx import gen_mini_prog_qrcode
from apps.helper import DesignOss
from baseutils.async_elastic import AsyncElastic
from apps.metrics.business_metrics import DesignAppMetrics
from apps.services.iconfont.utils import gather_iconfont


const_time_list = []
theme_key_pc_to_mobile_map = {
    "primary-color": "brand-primary",
    "link-color": "color-link",
    "success-color": "brand-success",
    "warning-color": "brand-warning",
    "error-color": "brand-error",
    "font-size-base": "font-size-base",
    "text-color": "color-text-base",
    "text-color-secondary": "color-text-secondary",
    "disabled-color": "color-text-disabled",
    "border-color-base": "border-color-base"
}


class PublishQueue:
    in_publishing = None
    publish_queue = OrderedDict()

    @classmethod
    def push(cls, item):
        if item.app_uuid not in cls.publish_queue:
            cls.publish_queue[item.app_uuid] = item
        return list(cls.publish_queue).index(item.app_uuid)

    @classmethod
    def get_one(cls):
        if cls.in_publishing:
            return
        if cls.publish_queue:
            return cls.publish_queue.popitem(0)

    @classmethod
    def clear_in_publish(cls, item):
        item_id = item.app_uuid
        if item_id in cls.publish_queue:
            del cls.publish_queue[item_id]
        cls.in_publishing = None

    @classmethod
    def publish_one(cls):
        item = cls.get_one()
        if item:
            cls.in_publishing = item[1]
            cls.in_publishing._publish()


async def ignore_func():
    return None


async def log_cost_time(func_name, func, *args, **kwargs):
    start_timestamp = time.time()
    app_log.info(f"{func_name} start")
    result = await func(*args, **kwargs)
    app_log.info(f"{func_name} end")
    end_timestamp = time.time()
    time_step = end_timestamp - start_timestamp
    if time_step > 1:
        const_time_list.append(f"{func_name}:{time_step}")
    app_log.info("publish step [%s] time cost %.3fs", func_name, time_step)
    DesignAppMetrics.collect_app_publish_steps(func.__name__, time_step)
    return result


def log_step(func):
    @wraps(func)
    def with_log(*args, **kwargs):
        if asyncio.iscoroutinefunction(func):
            result = log_cost_time(func.__name__, func, *args, **kwargs)
            return result
        else:
            start_timestamp = time.time()
            result = func(*args, **kwargs)
            end_timestamp = time.time()
            time_step = end_timestamp - start_timestamp
            if time_step > 1:
                const_time_list.append(f"{func.__name__}:{time_step}")
            DesignAppMetrics.collect_app_publish_steps(func.__name__, time_step)
            return result
    return with_log


def publish_for_local(func):
    @wraps(func)
    async def async_noop(*args, **kwargs):
        # 这是一个异步的空函数
        pass

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        if self.publish_for_local:
            return func(self, *args, **kwargs)
        else:
            return async_noop()
    return wrapper


class PublishService(object):
    _instance_lock = Lock()
    _instance = dict()

    def __new__(cls, user_uuid, app_uuid, **kwargs):
        if kwargs.get("support_k8s") or engine.app.config.ENV != "Development":
            return object.__new__(cls)
        if app_uuid not in cls._instance:
            with PublishService._instance_lock:
                PublishService._instance[app_uuid] = object.__new__(cls)
        return PublishService._instance[app_uuid]

    def __init__(self, user_uuid, app_uuid, **kwargs) -> None:
        if getattr(self, "status", None) is not None:
            return
        self.sandbox_uuid = kwargs.get("sandbox_uuid", "")
        self.sandbox_pk = kwargs.get("sandbox_pk", "")
        self.sub_env = kwargs.get("sub_env", 'None')
        self.sub_app_version = None
        self.group_uuid = kwargs.get("group_uuid", "")
        self.group_id = kwargs.get("group_id", "")
        self.tenant_uuid = kwargs.get("tenant_uuid", "")
        self.status = 0
        self.workspace_info = kwargs.get("workspace_info", dict())
        self.env = f"sandbox{self.sandbox_pk}" if self.sandbox_uuid else kwargs.get(
            "env", "")
        self.env = f"group{self.group_id}" if self.group_uuid else self.env
        self.database_name = self.sandbox_uuid or user_uuid
        self.database_name = self.group_uuid or self.database_name
        
        self.user_uuid = self.sandbox_uuid or user_uuid
        self.storage = kwargs.get("storage", 1)  # storage 1 柠檬云 2   docker
        self.publish_for_local = True if self.storage == 2 else False
        self.user_uuid = self.group_uuid or self.user_uuid
        if self.group_uuid and self.publish_for_local:
            self.user_uuid = self.group_uuid + "local"
            self.database_name = self.group_uuid  + "local"
        self.local_database_name = self.database_name
        self.real_user_uuid = user_uuid.rstrip(
            "test") if len(user_uuid) > 32 else user_uuid
        self.app_user = kwargs.get("app_user", "")
        self.app_uuid = app_uuid

        self.deploy_secret = ""
        self.app_env = self.app_uuid + self.env
        self.db = None
        self.app_revision = 0
        self.app_name = None
        self.app = engine.app
        self.config = getattr(
            config, f"{engine.app.config.ENV}Config", config.DevelopmentConfig)
        self.middle_user_path = f"{self.config.runtime_app_path}/{self.database_name}"
        self.step = 0
        self.decode_context = {}
        self.decode_context["modules"] = {}
        self.build_front = False
        self.build_pc = False
        self.build_app = False
        self.ext_tenant = kwargs.get("ext_tenant") or None
        self.build_type = kwargs.get('build_type')
        self.success_wait = 10
        self.app_ext_tenants = kwargs.get("app_ext_tenants", [])  # 全部扩展租户
        self.k8s_support = kwargs.get("support_k8s", False)
        self.proxy = kwargs.get("proxy")
        self.publish_key = "|".join(["publish", self.app_env])
        self.cas_url = kwargs.get("cas_url", "localhost:6500")
        self.k8s_start = False
        self.online_key = "ingress-services"
        self.sandbox_online_key = "sandbox-ingress-services"
        self.app_sandbox_online_key = f"{self.app_uuid}-sandbox-ingress"
        self.offline_key = "offline-ingress-services"
        self.p_token = kwargs.get("p_token")
        self.version_number = kwargs.get("version_number", "")
        self.async_db = kwargs.get("sync_db", False)
        self.publish_app = kwargs.get("publish_app", True)
        self.publish_pc = kwargs.get("publish_pc",  True)
        self.base_image_id = ""
        # self.copy_back_code = False
        self.success = False

        self.worker_processes = 4 if self.publish_for_local else 1
        self.vfs_processes = 1
        self.cloud_worker_processes = 1
        self.cloud_vfs_processes = 1
        self.user_home = "/root"
        self.workspace_path = f"{self.user_home}/publish_workspace"
        # 本地部署资源目录
        self.local_deploy_path = f"{self.workspace_path}/local_deploy"
        self.deploy_image_uuid = lemon_uuid()
        self.icon_dict = {}  # 本地部署需要保存应用在oss上的图片
        self.need_check = kwargs.get("need_check", True)
        self.init_publish_file_path()
        self.system_tables: dict = dict()
        self.table_dict = dict()
        self.database = None
        self.agent_deploy = False
        self.image_workspace = ""
        self.runtime_front_path = ""
        self.init_subenvironment(kwargs)
        self.sub_env_info: SubEnv = None
        self.web_title = ""
        self.web_welcome_text = ""
        self.default_tenant_uuid = None  # 默认租户, 但仅当需要添加默认租户时才有
        self.version_status_update_history = {}

    def init_subenvironment(self, kwargs):
        if self.sandbox_uuid and self.sub_env != 'None':
            self.database_name = self.real_user_uuid + self.sub_env
            self.sub_app_version = f"sub{self.sandbox_pk}"

    async def gen_deploy_secret(self):
        self.deploy_secret = lemon_uuid()
        deploy_info = {"app_uuid": self.app_uuid, "app_env": self.app_env}
        await engine.redis.setex(self.deploy_secret,  3600, deploy_info)

    def init_publish_file_path(self):
        self.server_host = self.config.domain_name
        self.server_port = 8443
        self.server_protocol = "https"
        self.static_dir_name = "app_list"  # 存放应用安装包
        self.version_dir_name = "app_static_list"  # 存放应用版本信息
        self.electron_dir_name = "electron"

    def sub_env_ignore(default_func=ignore_func):
        def _sub_env_ignore(func):
            @wraps(func)
            def func_ignore(self, *args, **kwargs):
                if self.sub_env != 'None':
                    return default_func()
                return func(self, *args, **kwargs)
            return func_ignore
        return _sub_env_ignore

    @log_step
    async def get_app_version(self):
        app = await engine.access.get_app_by_app_uuid(self.app_uuid)
        self.app_entity = app
        self.app_title = app.app_name
        self.app_icon = app.app_icon
        res = await async_run(f"cd {project_base_path} && git rev-parse --short HEAD")
        current_version = res[1].rstrip().decode()

        environment = self.env or "pro"
        base_info = {
            APPPublish.app_uuid.name: self.app_uuid,
            APPPublish.environment.name: environment
        }
        publish_info: APPPublish = await engine.access.get_obj(APPPublish, **base_info)
        lemon_version = "v0.1"
        if publish_info:
            app_version = publish_info.app_version
            app_revision = publish_info.app_revision
            lemon_version = publish_info.lemon_version
        else:
            if self.sandbox_uuid:
                sandbox_info = await engine.access.get_sandbox_by_sandbox_uuid(
                    self.sandbox_uuid)
                app_version = sandbox_info.app_version
                app_revision = sandbox_info.app_revision
            else:
                if self.env == "test":
                    app_version = app.app_version_test
                    app_revision = app.app_revision_test
                else:
                    app_version = app.app_version
                    app_revision = app.app_revision
        # app_publish_info = await engine.access.get_obj(APPExtension, **{"app_uuid": self.app_uuid, "tenant_uuid": ""})
        # self.front_version = app_publish_info.deployment.get("front_version", app.app_version) if app_publish_info else app.app_version
        self.front_version = app_version
        self.front_lemon_version = lemon_version
        app_log.info((current_version, self.front_version))
        if current_version != self.front_version and self.build_type not in [-1, -2]:
            app_log.info("build_front")
            self.build_front = True
            self.front_version = current_version
        # 小程序
        nvg = await engine.access.get_navigation_by_platform(self.app_uuid, 3)
        self.has_mobile = True if nvg else False
        self.app_version = current_version
        self.last_version = app_version
        if self.sub_app_version:
            # 子环境只维护一个版本的文档
            self.app_revision = self.sub_app_version
        else:
            if not self.ext_tenant and not self.publish_for_local:
                self.app_revision = app_revision + 1
            else:
                self.app_revision = app_revision
        self.app_name = app.app_name.lower()
        local_config_json = await engine.access.get_app_deploy_config_json(self.app_uuid)
        custom_title = local_config_json.get("title")
        self.web_title = self.handle_env_name(custom_title) if custom_title else self.handle_env_name(self.app_name)
        self.web_welcome_text = local_config_json.get("welcome_text", "")
        if self.ext_tenant:
            self.app_path = f"{self.middle_user_path}/{self.app_name}/{self.ext_tenant}"
            self.app_source_path = f"{self.middle_user_path}/{self.app_name}_{self.app_revision}"
        else:
            self.app_path = f"{self.middle_user_path}/{self.app_name}_{self.app_revision}"
        self.build_front_path = os.path.dirname(project_base_path) + "/client"
        self.deploy_path = os.path.dirname(project_base_path) + "/deploy"
        self.app_path_resources = f"{self.app_path}/back/resources"
        # 不同版本前端静态文件目录
        self.runtime_front_path = f"{self.config.runtime_front_path}/{self.version_dir_name}/{self.app_env}/{self.front_version}"
        app_log.info(["runtime_front_path", self.runtime_front_path])
        self.runtime_front_dist = f"{self.config.runtime_front_path}/"
        if not os.path.exists(self.runtime_front_path):
            self.build_front = True
            self.front_version = current_version
        if self.has_mobile and not os.path.exists(f"{self.runtime_front_path}_m"):
            app_log.info("build_app")
            self.build_front = True
            # self.build_app = True
            self.front_version = current_version
        if not os.path.exists(f"{self.runtime_front_path}_pc"):
            app_log.info("build_pc")
            self.build_front = True
            # self.build_pc = True
            self.front_version = current_version
        # 只要用户勾选了打包app和客户端就一定会执行打包过程
        self.build_app = True
        self.build_pc = True
        self.get_sys_tables()

    @log_step
    @sub_env_ignore()
    async def check(self):
        # return
        if self.k8s_support:
            server_path = os.path.dirname(project_base_path) + "/back/apps"
            vc_user = self.workspace_info.get("user_uuid")
            vc_branch = self.workspace_info.get("branch_uuid")
            cmd = f"cd {server_path} && python server.py publish_app_check --app_uuid={self.app_uuid} "
            if vc_user is not None and vc_branch is not None:
                cmd += f"--vc_user={vc_user} --vc_branch={vc_branch} "
            cmd += "2> /var/log/publish.log"
            result = await async_run(cmd)
            app_log.info(result)
            if result[0] != 0:
                log_info = await async_run("cat /var/log/publish.log")
                raise Exception(f"check_document failed, {log_info}")
            # await self.check_document()
        else:
            await self.check_document()
        if self.need_check:
            check_message_list = await engine.access.list_check_message_by_app_uuid(
                self.app_uuid, any_problem=CheckMessage.error,
                any_problem_message=CheckMessage.error_message,
                model=CheckMessage)
            for d_info in check_message_list:
                one_message = d_info.pop("error_message", [])
                need_stop_publish = list(one_message)
                error_message_list = []
                for error_message in need_stop_publish:
                    error_message.update(d_info)
                    error_message_list.append(error_message)
                e = DocumentCheckError(error_message=error_message_list)
                raise e

    async def check_document(self, check_commit=False, vc_user=None, vc_branch=None):
        if vc_user is not None and vc_branch is not None:
            set_current_workspace(user_uuid=vc_user, branch_uuid=vc_branch)
        # # 需要检查导航
        # all_documents = await engine.access.list_document_by_module_uuid_join_check_message(self.app_uuid, ignore_version=True)
        # # check_commit=True需要从导航文档创建出导航
        # await engine.service.document_check.check_all_document2(self.app_uuid, all_documents, check_commit=True, gather=False)
        app_info_docs = await engine.access.list_document_by_module_uuid_join_check_message(
            self.app_uuid, "", ignore_version=True)
        if app_info_docs:
            await engine.service.document_check.check_all_document2(
                self.app_uuid, app_info_docs, check_commit=True, gather=False)
        modules = await engine.access.list_module_by_app_uuid(
            self.app_uuid, ext_tenant=self.ext_tenant)
        document_funcs, check_funcs = [], []
        for module in modules:
            if isinstance(module, dict) and module.get("module_uuid"):
                document_funcs.append(engine.access.list_document_by_module_uuid_join_check_message(
                    self.app_uuid, module.get("module_uuid"), ignore_version=True))
        all_documents = await asyncio.gather(*document_funcs)
        for all_document in all_documents:
            if all_document:
                await engine.service.document_check.check_all_document2(
                    self.app_uuid, all_document,
                    check_commit=check_commit, gather=True, priority='low')

    @log_step
    async def handle_runtime_database(self):
        query = RawQuery(
            sql=f"CREATE DATABASE IF NOT EXISTS `{self.database_name}`",
            params=None, _database=engine.db.database)
        await engine.db.objs.execute(query)
        self.db = PooledMySQLDatabase(
            self.database_name,
            host=self.app.config.MYSQL_HOST,
            port=self.app.config.MYSQL_PORT,
            user=self.app.config.MYSQL_USER,
            password=self.app.config.MYSQL_PASSWORD,
            charset=self.app.config.MYSQL_DB_CHARSET
        )
        db_proxy = Proxy()
        db_proxy.initialize(self.db)
        self.database = db_proxy
        self.objs = Manager(self.database)
        self.database.set_allow_sync(False)

    async def get_runtime_database(self):
        db = PooledMySQLDatabase(
            self.database_name,
            host=self.app.config.MYSQL_HOST,
            port=self.app.config.MYSQL_PORT,
            user=self.app.config.MYSQL_USER,
            password=self.app.config.MYSQL_PASSWORD,
            charset=self.app.config.MYSQL_DB_CHARSET
        )
        db_proxy = Proxy()
        db_proxy.initialize(self.db)
        database = db_proxy
        objs = Manager(self.database)
        database.set_allow_sync(False)
        return db, database, objs

    @log_step
    @sub_env_ignore()
    async def del_old_runtime_sys_table(self):
        from playhouse.migrate import MySQLMigrator
        from collections.abc import Iterable

        with self.database.allow_sync():
            await self.app.loop.run_in_executor(
                None, partial(self.db.connection().ping, True))
            table_all = await self.app.loop.run_in_executor(
                None, self.db.get_tables)
        pattern = r"{app_uuid}_\d+_|{part_app_uuid}_\d+_".format(
            app_uuid=self.app_uuid, part_app_uuid=self.app_uuid[:16])
        app_log.info(pattern)
        app_table_all = [str(t)
                         for t in table_all if re.search(pattern, str(t))]
        to_drop = [table for table in app_table_all if int(
            table.split("_")[1]) < (self.app_revision - 2)]
        migrator = MySQLMigrator(self.db)
        to_run = [migrator.make_context().literal(
            'DROP TABLE ').sql(Entity(table)) for table in to_drop]
        to_run = [next(obj for obj in migration if isinstance(obj, Context)) if isinstance(
            migration, Iterable) else migration for migration in to_run]
        to_run = [ctx.query() for ctx in to_run]

        app_log.debug(to_run)
        await asyncio.gather(*[self.objs.execute(RawQuery(
            sql=query_sql, params=None, _database=self.database))
            for query_sql, _ in to_run])

    async def copy_pro_data_to_test(self):
        # 复制生产环境数据到测试环境
        if self.env == "test" and self.async_db is True:
            app_uuid = self.app_uuid
            r_model = RelationshipBasic
            query = r_model.select(r_model.relationship_uuid).where(
                r_model.app_uuid == app_uuid,
                r_model.is_delete == 0, r_model.relationship_type == 1).dicts()
            model_list, r_list = await asyncio.gather(
                engine.access.list_model_by_app_uuid(app_uuid, with_sys=False),
                engine.db.objs.execute(query)
            )
            app_log.info(f"copy_data_query: {query}, {r_list}")
            database_uuid = self.database_name
            mysql_host = engine.config.MYSQL_HOST
            mysql_pwd = engine.config.MYSQL_PASSWORD
            mysql_user = engine.config.MYSQL_USER
            mysql_port = engine.config.MYSQL_PORT
            db_tmp_host = engine.config.DB_TMP_HOST
            tmp_db_dir = engine.config.DB_TMP_PATH
            table_list = [model_info.get("model_uuid")
                          for model_info in model_list]
            if r_list:
                r_uuild_list = [r_info.get("relationship_uuid")
                                for r_info in r_list]
                table_list.extend(r_uuild_list)
            for table_name in table_list:
                sql_dir = f"{tmp_db_dir}/{database_uuid}/{table_name}.sql"
                import_cmd = f"mysql -u{mysql_user} -p{mysql_pwd} -h{mysql_host} -P{mysql_port} {database_uuid}  <{sql_dir}"
                app_log.info(f"copy_table: {table_name}: cmd:{import_cmd}")
                result = await async_run(f"ssh root@{db_tmp_host} \"{import_cmd}\"")
                app_log.info(result)

    def handle_document_content(self, data):
        document_content = data.get("document_content", {})
        document_content.pop("pageStyles", None)
        return data

    def handle_table_default(self, data):
        return data

    def get_table_handle(self, table):
        if table == DocumentContent:
            return self.handle_document_content
        else:
            return self.handle_table_default

    # 为本地部署导出运行时系统表的数据
    @log_step
    @publish_for_local
    async def export_runtime_data(self):
        mysql_host = engine.config.MYSQL_HOST
        mysql_pwd = engine.config.MYSQL_PASSWORD
        mysql_user = engine.config.MYSQL_USER
        sql_data_path = f"{self.local_deploy_path}/sql_data/runtime"
        await self.create_dir(sql_data_path)
        table_list = []
        export_cmd = f"mysqldump --single-transaction  --quick -u{mysql_user} -p{mysql_pwd} -h{mysql_host} {self.database_name}"
        for table in runtime_sys_table:
            children_table = runtime_sys_table[table]
            _table_name = table._meta.table_name
            table_name = make_runtime_table_name(
                self.app_uuid, self.app_revision, _table_name)
            table_list.append(table_name)
            sql_path = f"{sql_data_path}/{table_name}.sql"
            table_export_cmd = f"{export_cmd} {table_name}>{sql_path}"
            await async_run(table_export_cmd)
            for k, v in children_table.items():
                for t in v:
                    _table_name = t._meta.table_name
                    table_name = make_runtime_table_name(
                        self.app_uuid, self.app_revision, _table_name)
                    table_list.append(table_name)
                    sql_path = f"{sql_data_path}/{table_name}.sql"
                    table_export_cmd = f"{export_cmd} {table_name}>{sql_path}"
                    await async_run(table_export_cmd)
        load_template = local_deploy_template.get_template(
            "load_runtime_data.sh")
        
        runtime_config = await load_template.render_async(**{"table_list": table_list, "user_uuid": self.local_database_name})
        with open(f"{self.local_deploy_path}/load_runtime_data.sh", "w") as target:
            target.write(runtime_config)
        await async_run(f"chmod +x  {self.local_deploy_path}/load_runtime_data.sh")
        await self.export_uncenter_data()

    async def export_uncenter_data(self):

        # 根据database区分目录
        await self.diff_directory_by_database()
        # 导出lemon库中相关的表
        lemon_sql_files, update_lemon_sql_files = await self.export_lemon_table()
        self.ucenter_table_list = []
        # 导出lemon_tenant_center库中的所有表
        update_tenant_center_sql_files = []
        update_runtime_tenant_center_sql_files = []
        tenant_center_sql_files, tenant_center_table_list, \
            runtime_tenant_center_sql_files, all_user_list = await self.export_lemon_tenant_center()
        # 导出部门表
        await self.export_department_table(
            tenant_center_sql_files, tenant_center_table_list)
        # 导出lemon_ucenter库中的所有表
        update_ucenter_sql_files = []

        ucenter_db, ucenter_sql_files = await self.export_lemon_ucenter()
        # 导出用户表
        views = await self.export_user_table(all_user_list, ucenter_sql_files, ucenter_db)

        # 获取相关视图创建语句
        lemon_ucenter_sql_liist = self.get_create_view_sql(views)
        # 创建lemon_ucenter相关的sql文件
        await self.create_lemon_ucenter_sql(
            lemon_ucenter_sql_liist, ucenter_sql_files, lemon_sql_files,
            tenant_center_sql_files, runtime_tenant_center_sql_files, update_ucenter_sql_files, update_lemon_sql_files,
            update_tenant_center_sql_files, update_runtime_tenant_center_sql_files)

    async def export_lemon_table(self):
        lemon_sql_files = []  # lemon库中涉及到的sql文件
        update_lemon_sql_files = []  # lemon库中要更新的sql文件
        lemon_table_list = ["lemon_app", "lemon_userrole",
                            "lemon_rulechain", "lemon_workflow", "lemon_apppublish"]
        lemon_table_list_without_app_uuid = ["lemon_publishgroup"]
        update_lemon_table_list = ["lemon_apppublish", "lemon_app"]
        for table_name in lemon_table_list + lemon_table_list_without_app_uuid:
            sql_path = f"{self.lemon_sql_path}/{table_name}.sql"
            table_export_cmd = f"{self.lemon_export_cmd} {table_name} --where=\"app_uuid='{self.app_uuid}'\">{sql_path}"
            if table_name in lemon_table_list_without_app_uuid:
                table_export_cmd = f"{self.lemon_export_cmd} {table_name}>{sql_path}"
            result = await async_run(table_export_cmd)
            file_name = f"{table_name}.sql"
            lemon_sql_files.append(file_name)
            if table_name in update_lemon_table_list:
                update_lemon_sql_files.append(file_name)
            app_log.info(result)
        lemon_db = engine.db.real_database
        lemon_sql_list = []
        with lemon_db.allow_sync():
            views = await self.app.loop.run_in_executor(None, lemon_db.get_views)
            for view in views:
                if view.name in ["lemon_app_manager"]:
                    create_sql = f'CREATE OR REPLACE VIEW `{view.name}` AS ({view.sql})'
                    lemon_sql_list.append(create_sql)
        sql_template = local_deploy_template.get_template("database.sql")
        sql_file = await sql_template.render_async(**{"sql_list": lemon_sql_list})
        with open(f"{self.lemon_sql_path}/database_lemon.sql", "w") as target:
            target.write(sql_file)
        lemon_sql_files.append("database_lemon.sql")
        return lemon_sql_files, update_lemon_sql_files

    async def export_lemon_tenant_center(self):
        tenant_center_sql_files = []
        tenant_center_table_list = []
        runtime_tenant_center_sql_files = []
        all_user_list = []
        tenant_id_tables = ["tenant", "tenant_member", "department_member"]
        app_id_tables = ["access_key", "gateway", "terminal"]
        for table_name in tenant_id_tables:
            sql_path = f"{self.tenant_sql_path}/{table_name}.sql"
            table_export_cmd = f'{self.tenant_center_cmd} {table_name} --where="tenant_uuid IN({self.tenants_condition})">{sql_path}'
            tenant_center_sql_files.append(f"{table_name}.sql")
            result = await async_run(table_export_cmd)
            app_log.info(result)
        for table_name in app_id_tables:
            sql_path = f"{self.tenant_sql_path}/{table_name}.sql"
            table_export_cmd = f'{self.tenant_center_cmd} {table_name} --where="app_uuid=\'{self.app_uuid}\'">{sql_path}'
            tenant_center_sql_files.append(f"{table_name}.sql")
            result = await async_run(table_export_cmd)
            app_log.info(result)
        tenant_tables = []
        tenant_name_old_to_new = {}
        tenant_table_list = [
            "tenant_user", "tenant_department", "tenant_department_member"]
        for tenant in self.tenants:
            for name in tenant_table_list:
                tenant_name_old_to_new.update({
                    "_".join([tenant, name]): "_".join([self.app_uuid[:8], tenant[:8], name])
                })
        for tenant in self.tenants:
            for tenant_table in tenant_table_list:
                table_name = "_".join([tenant, tenant_table])
                sql_path = f"{self.tenant_sql_path}/{table_name}.sql"
                tenant_center_sql_files.append(f"{table_name}.sql")
                table_export_cmd = f"{self.tenant_center_cmd} {table_name}>{sql_path}"
                result = await async_run(table_export_cmd)
                app_log.info(result)

                tenant_tables.append(table_name)
                file_path = f"{self.tenant_sql_path}/runtime_{table_name}.sql"
                export_cmd = f"{self.tenant_center_cmd} {table_name}>{file_path}"
                result = await async_run(export_cmd)
                app_log.info(result)
                with open(f"{file_path}", "r") as s:
                    content = s.read()
                    for _old, _new in tenant_name_old_to_new.items():
                        content = content.replace(_old, _new)
                with open(f"{self.tenant_sql_path}/runtime_{table_name}_copy.sql", "w") as s:
                    s.write(content)
                runtime_tenant_center_sql_files.append(
                    f"runtime_{table_name}_copy.sql")

                tenant_center_table_list.append(table_name)
                sql = f"""SELECT * FROM lemon_tenant_center.{table_name}  as `t1`"""
                tenant_user_sql = RawQuery(
                    sql=sql, params=None, _database=self.database).dicts()
                user_list = await self.objs.execute(tenant_user_sql)
                user_list = [user.get(UUID_TenantUser.user_uuid.value)
                            for user in user_list if user.get(UUID_TenantUser.user_uuid.value)]
                all_user_list.extend(user_list)

        sql_template = local_deploy_template.get_template(
            "tenant_table_dump.sh")
        sql_file = await sql_template.render_async(
            **{"tenant_tables": tenant_tables, "database_name": self.database_name})
        with open(f"{self.local_deploy_path}/tenant_table_dump.sh", "w") as target:
            target.write(sql_file)
        await async_run(f"chmod +x  {self.local_deploy_path}/tenant_table_dump.sh")

        sql_template = local_deploy_template.get_template(
            "process_tenant_table.sh")
        sql_file = await sql_template.render_async(
            **{"tenant_tables": ujson.dumps(tenant_tables),
                "tenant_name_old_to_new": ujson.dumps(tenant_name_old_to_new)})
        with open(f"{self.local_deploy_path}/process_tenant_table.sh", "w") as target:
            target.write(sql_file)
        await async_run(f"chmod +x  {self.local_deploy_path}/process_tenant_table.sh")

        sql_template = local_deploy_template.get_template(
            "tenant_table_load.sh")
        sql_file = await sql_template.render_async(
            **{"tenant_tables": tenant_tables, "database_name": self.database_name})
        with open(f"{self.local_deploy_path}/tenant_table_load.sh", "w") as target:
            target.write(sql_file)
        await async_run(f"chmod +x  {self.local_deploy_path}/tenant_table_load.sh")

        tenant_center_table_list.extend(tenant_id_tables)
        tenant_center_table_list.extend(app_id_tables)
        return tenant_center_sql_files, tenant_center_table_list, runtime_tenant_center_sql_files, all_user_list

    async def export_department_table(
            self, tenant_center_sql_files, tenant_center_table_list):
        department_table_name = UUID_Department.table_name.value
        tenant_uuid_column = UUID_Department.tenant_uuid.value
        sql_path = f"{self.tenant_sql_path}/{department_table_name}.sql"
        table_export_cmd = f'{self.tenant_center_cmd} {department_table_name} --where="{tenant_uuid_column} IN({self.tenants_condition})">{sql_path}'
        tenant_center_sql_files.append(f"{department_table_name}.sql")
        result = await async_run(table_export_cmd)
        tenant_center_table_list.append(department_table_name)
        app_log.info(result)

    async def export_lemon_ucenter(self):
        ucenter_sql_files = []
        import ucenter.config
        uc_config = getattr(
            ucenter.config, f"{self.app.config.ENV}Config", ucenter.config.DevelopmentConfig)
        ucenter_db = PooledMySQLDatabase(
            uc_config.MYSQL_DB_LEMON,
            host=uc_config.MYSQL_HOST,
            port=uc_config.MYSQL_PORT,
            user=uc_config.MYSQL_USER,
            password=uc_config.MYSQL_PASSWORD,
            charset=uc_config.MYSQL_DB_CHARSET
        )
        tenant_id_tables = ["department_member"]
        self.ucenter_table_list.extend(tenant_id_tables)
        app_id_tables = ["app_editor", "app_tenant"]
        self.ucenter_table_list.extend(app_id_tables)
        team_id_tables = ["team", "teamplayer"]
        self.ucenter_table_list.extend(team_id_tables)
        for table_name in tenant_id_tables:
            sql_path = f"{self.ucenter_sql_path}/{table_name}.sql"
            table_export_cmd = f'{self.ucenter_export_cmd} {table_name} --where="tenant_uuid IN({self.tenants_condition})">{sql_path}'
            ucenter_sql_files.append(f"{table_name}.sql")
            result = await async_run(table_export_cmd)
            app_log.info(result)
        for table_name in app_id_tables:
            sql_path = f"{self.ucenter_sql_path}/{table_name}.sql"
            table_export_cmd = f'{self.ucenter_export_cmd} {table_name} --where="app_uuid=\'{self.app_uuid}\'">{sql_path}'
            ucenter_sql_files.append(f"{table_name}.sql")
            result = await async_run(table_export_cmd)
            app_log.info(result)
        for table_name in team_id_tables:
            sql_path = f"{self.ucenter_sql_path}/{table_name}.sql"
            table_export_cmd = f'{self.ucenter_export_cmd} {table_name} --where="team_uuid=\'{self.app_user}\'">{sql_path}'
            ucenter_sql_files.append(f"{table_name}.sql")
            result = await async_run(table_export_cmd)
            app_log.info(result)
        return ucenter_db, ucenter_sql_files

    async def export_user_table(
            self, all_user_list, 
            ucenter_sql_files, ucenter_db):
        user_table_name = UUID_User.table_name.value
        user_uuid_column = UUID_User.user_uuid.value
        user_condition_list = [f"'{user_uuid}'" for user_uuid in all_user_list]
        user_condition = ",".join(user_condition_list)
        sql_path = f"{self.ucenter_sql_path}/{user_table_name}.sql"
        table_export_cmd = f'{self.ucenter_export_cmd} {user_table_name} --where="{user_uuid_column} IN({user_condition})">{sql_path}'
        ucenter_sql_files.append(f"{user_table_name}.sql")
        result = await async_run(table_export_cmd)
        app_log.info(result)
        self.ucenter_table_list.append(user_table_name)
        views = await self.app.loop.run_in_executor(None, ucenter_db.get_views)
        return views

    def get_create_view_sql(self, views):
        ucenter_view_list = ["app_info", "app_manager"]
        view_sql_list = []
        belong_id_list = ["lemon_userrole_"+self.app_uuid]
        # for user in [self.database_name.replace("test", "")]:
        #     belong_id_list.append(user)
        #     belong_id_list.append(user + "test")

        # TODO role_member视图创建有问题
        role_member_view_list = []

        real_model = self.system_tables.get("role_member")
        real_model = make_model_copy(
            real_model, real_model._meta.table_name, self.database)
        real_model._meta.schema = self.database.database
        role_member_view_sql = gen_real_model_view_sql(
            real_model, "role_member", self.database_name)
        view_sql_list.append(role_member_view_sql)

        userrole_sql = (f'CREATE OR REPLACE VIEW `lemon_userrole_{self.app_uuid}` AS (SELECT * FROM '
                        f'`{self.database_name}`.`{make_runtime_table_name(self.app_uuid, self.app_revision, "lemon_userrole")}`)')
        view_sql_list.append(userrole_sql)
        for view in views:
            if view.name in ucenter_view_list:
                sql = f'CREATE OR REPLACE VIEW `{view.name}` AS ({view.sql})'
                view_sql_list.append(sql)
        return view_sql_list

    @publish_for_local
    async def create_lemon_ucenter_sql(
            self, lemon_ucenter_sql_liist, ucenter_sql_files, lemon_sql_files,
            tenant_center_sql_files, runtime_tenant_center_sql_files, update_ucenter_sql_files, update_lemon_sql_files,
            update_tenant_center_sql_files, update_runtime_tenant_center_sql_files):
        sql_template = local_deploy_template.get_template("database.sql")
        sql_file = await sql_template.render_async(**{"sql_list": lemon_ucenter_sql_liist})
        with open(f"{self.ucenter_view_sql_path}/view.sql", "w") as target:
            target.write(sql_file)
        view_files = ["view.sql"]
        sql_template = local_deploy_template.get_template("load_view.sh")
        sql_file = await sql_template.render_async(**{"view_files": view_files, "user_uuid": self.local_database_name})
        with open(f"{self.local_deploy_path}/load_view.sh", "w") as target:
            target.write(sql_file)
        await async_run(f"chmod +x  {self.local_deploy_path}/load_view.sh")
        sql_template = local_deploy_template.get_template("load_sys_table.sh")
        sql_file = await sql_template.render_async(
            **{"ucenter_sql_files": ucenter_sql_files, "lemon_sql_files": lemon_sql_files,
               "tenant_center_sql_files": tenant_center_sql_files,
               "runtime_tenant_center_sql_files": runtime_tenant_center_sql_files,
               "user_uuid": self.local_database_name})
        with open(f"{self.local_deploy_path}/load_sys_table.sh", "w") as target:
            target.write(sql_file)
        await async_run(f"chmod +x  {self.local_deploy_path}/load_sys_table.sh")
        sql_template = local_deploy_template.get_template(
            "update_sys_table.sh")
        sql_file = await sql_template.render_async(
            **{"update_ucenter_sql_files": update_ucenter_sql_files, "update_lemon_sql_files": update_lemon_sql_files,
               "update_tenant_center_sql_files": update_tenant_center_sql_files,
               "update_runtime_tenant_center_sql_files": update_runtime_tenant_center_sql_files,
               "user_uuid": self.local_database_name})
        with open(f"{self.local_deploy_path}/update_sys_table.sh", "w") as target:
            target.write(sql_file)
        await async_run(f"chmod +x  {self.local_deploy_path}/update_sys_table.sh")

        sql_template = local_deploy_template.get_template(
            "migrate_old_data.sh")
        sql_file = await sql_template.render_async(
            **{"update_ucenter_sql_files": update_ucenter_sql_files, "update_lemon_sql_files": update_lemon_sql_files,
               "update_tenant_center_sql_files": update_tenant_center_sql_files,
               "update_runtime_tenant_center_sql_files": update_runtime_tenant_center_sql_files,
               "user_uuid": self.local_database_name})
        with open(f"{self.local_deploy_path}/migrate_old_data.sh", "w") as target:
            target.write(sql_file)
        await async_run(f"chmod +x  {self.local_deploy_path}/migrate_old_data.sh")
    async def diff_directory_by_database(self):
        
        sql = f"""SELECT * FROM lemon_ucenter.app_tenant  as `t1`
                WHERE (
                `t1`.`app_uuid` = "{self.app_uuid}"
                )
            """
        app_log.info(sql)
        mysql_host = engine.config.MYSQL_HOST
        mysql_pwd = engine.config.MYSQL_PASSWORD
        mysql_user = engine.config.MYSQL_USER

        sql_data_path = f"{self.local_deploy_path}/sql_data"
        self.ucenter_sql_path = f"{sql_data_path}/ucenter"
        self.ucenter_view_sql_path = f"{sql_data_path}/ucenter_view"
        self.tenant_sql_path = f"{sql_data_path}/tenant"
        self.lemon_sql_path = f"{sql_data_path}/lemon"
        await self.create_dir(self.ucenter_sql_path)
        await self.create_dir(self.tenant_sql_path)
        await self.create_dir(self.lemon_sql_path)
        await self.create_dir(self.ucenter_view_sql_path)
        app_tenant_sql = RawQuery(
            sql=sql, params=None, _database=self.database).dicts()
        app_tenants = await self.objs.execute(app_tenant_sql)
        self.lemon_export_cmd = f"mysqldump --single-transaction  --quick -u{mysql_user} -p{mysql_pwd} -h{mysql_host} lemon"
        self.ucenter_export_cmd = f"mysqldump --single-transaction  --quick -u{mysql_user} -p{mysql_pwd} -h{mysql_host} lemon_ucenter"
        self.tenant_center_cmd = f"mysqldump --single-transaction  --quick -u{mysql_user} -p{mysql_pwd} -h{mysql_host} lemon_tenant_center"
        self.tenants = [tenant.get("tenant_uuid") for tenant in app_tenants]
        tenant_condition_list = [f"'{tenant}'" for tenant in self.tenants]
        self.tenants_condition = ",".join(tenant_condition_list)

    @log_step
    async def copy_source_code(self):
        app_log.info(self.app_path)
        if not os.path.exists(self.app_path):
            os.makedirs(self.app_path)
        if self.publish_for_local:
            await async_run(f"cp -r {engine.config.STATIC_PATH_PREFIX}/code_encrypt/back {self.app_path}/")
        else:
            if self.ext_tenant:
                await async_run(f"cp -r {self.app_source_path}/back {self.app_path}/")
            else:
                await async_run(f"cp -r {project_base_path} {self.app_path}")
        # 运行时只需要复制部分代码
        copy_files = ["apps", "baseutils", "connector", "resources", "runtime", "requirements.txt",
                      "tools", "ucenter", "packages", "tests", "templates", "gitapi", "agent", "log_system"]
        file_list = os.listdir(f"{self.app_path}/back")
        for file in file_list:
            if file not in copy_files:
                await async_run(f"rm -r {self.app_path}/back/{file}")
        if self.k8s_support:
            # 文件过大导致做镜像太慢
            await async_run(f"rm -r {self.app_path}/back/packages/fonts")
        # await async_run(f"cp -rd {os.path.dirname(project_base_path)}/client {self.build_front_path}")
        # await async_run(f"cp -rd {os.path.dirname(project_base_path)}/deploy {self.deploy_path}")

    @log_step
    async def gen_models(self):
        table_dict = {k: t._meta.table_name for k,
                      t in self.table_dict.items()}
        system_table_names = {
            k: t._meta.meta_table_name for k, t in self.system_tables.items()}
        model_generator = ModelGenerator(
            self.app, self.database_name, self.app_uuid, runtime_db=self.db,
            out_base_path=self.app_path_resources, ext_tenant=self.ext_tenant,
            table_dict=table_dict, system_table_names=system_table_names, middle_user_uuid=self.user_uuid,
            base_app_path=self.app_path, app_revision=self.app_revision, sub_env=self.sub_env,
            sub_env_info=self.sub_env_info, sandbox_uuid=self.sandbox_uuid,
            default_tenant_uuid=self.default_tenant_uuid, group_uuid=self.group_uuid)
        if self.publish_for_local:
            # 本地部署
            await model_generator.runtime_model_info_dump()
            await model_generator.runtime_model_info_load()
            await model_generator.gen()
        else:
            # await model_generator.runtime_model_info_dump()
            await model_generator.gen()

    def make_last_runtime_table_name(self, cur_table_name):
        try:
            table_name = cur_table_name.split("_", 2)[-1]
        except Exception:
            last_table_name = ""
        else:
            last_table_name = make_runtime_table_name(
                self.app_uuid, self.app_revision-1, table_name)
        return last_table_name

    @log_step
    async def copy_runtime_data_relationships(self):
        """模型作为source model, 指向了用户的表的情况"""
        relationship_list = await engine.access.list_relationship_by_app_uuid(
            self.app_uuid, with_sys=True, as_dict=False)
        relationship_list = list(relationship_list)
        table_dict = {k: t._meta.table_name for k,
                      t in self.table_dict.items()}
        model_field_dict = dict()
        for r in relationship_list:
            origin_target_model = r.target_model
            origin_source_model = r.source_model
            r.target_model = get_sys_table_rel_table_name(
                r.target_model, table_dict)
            r.source_model = get_sys_table_rel_table_name(
                r.source_model, table_dict)
            if origin_source_model != r.source_model and origin_target_model == r.target_model:
                if r.relationship_type in [RelationshipType.ONE_TO_MANY, RelationshipType.ONE_TO_ONE]:
                    fields = model_field_dict.setdefault(r.source_model, [])
                    fields.append(r.relationship_uuid)

        # 查询last_model
        with self.database.allow_sync():
            table_all = await self.app.loop.run_in_executor(
                None, self.db.get_tables)
        func_list = list()
        for model, fields in model_field_dict.items():
            last_model = self.make_last_runtime_table_name(model)
            if last_model and last_model in table_all:
                # fields = [f'IFNULL({field}, NULL)' for field in fields]
                func = self.do_runtime_sys_copy(model, last_model, fields)
                func_list.append(func)
        await asyncio.gather(*func_list)

    async def do_runtime_sys_copy(self, model, last_model, fields):
        # 顺序执行的原因是, 单个外键字段由于不存在的原因失败后,其他字段能拷贝成功
        update_data = {}
        if not model:
            return
        for f in fields:
            try:
                query = f"SELECT id, {f} FROM {last_model} WHERE {f} IS NOT NULL"
                query = RawQuery(query, params=None,
                                 _database=self.database).dicts()
                r = await self.objs.execute(query)
                app_log.info(list(r))
            except Exception:
                pass
            else:
                for d in r:
                    line_update_data = update_data.setdefault(d.pop("id"), {})
                    line_update_data.update(d)
        if not update_data:
            return

        updates = []
        for pk, line_update in update_data.items():
            updates.append(
                f"UPDATE `{model}` SET {', '.join(f'`{k}`={v}' for k, v in line_update.items())} WHERE `id` = {pk}")
        update_sql = ";\n".join(updates)
        app_log.info(update_sql)
        query = RawQuery(update_sql, params=None, _database=self.database)
        await self.objs.execute(query)

    @log_step
    async def gen_const(self):
        from ..ide.render import ConstRenderService
        from apps.services.document import ConstTable
        from apps.entity import ConstTable as model
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.const_uuid,
            model.const_name,
            model.const_type,
            model.value,
            model.description
        )
        # const_list = await engine.access.list_const_by_app_uuid(self.app_uuid, fields=fields)
        # const_list = list(const_list)
        module_uuid_map = dict()
        module_const_map = dict()
        module_dict = dict()
        modules, const_list = await asyncio.gather(
            engine.access.list_module_by_app_uuid(
                self.app_uuid, with_sys=True, as_dict=True),
            engine.access.list_const_by_app_uuid(self.app_uuid, fields=fields))
        const_list = list(const_list)
        modules = {m["module_uuid"]: m for m in modules}
        for const in const_list:
            app_log.debug(const)
            module_uuid = const["module_uuid"]
            if not module_uuid_map.get(module_uuid):
                module = modules[module_uuid]
                module_name = module["module_name"]
                module_uuid_map[module_uuid] = module_name
                module_const_map[module_name] = list()
            module_const_map[module_uuid_map[module_uuid]].append(const)

        for module in module_const_map:
            if module not in module_dict:
                module_dict[module] = {}
            # if "consts" not in module_dict[module]:
            #     module_dict[module]["consts"] = {}
            module_path = f"{self.app_path_resources}/{self.app_name}/{module}"
            const_list = []
            for const in module_const_map[module]:
                const_table = ConstTable(uuid=const["const_uuid"], name=const["const_name"],
                                         description=const["description"], value=const["value"], const_type=const["const_type"])
                const_table.const_name = const_table.name
                const_table.const_type = const_table.type
                const_list.append(const_table)
                module_dict[module][const_table.uuid] = const_table.const_name
            app_log.info(module_path)
            if not self.ext_tenant:
                service = ConstRenderService(module_path, const_list)
                # await service.save()
                await self.app.loop.run_in_executor(None, service.save)
        return module_dict

    @log_step
    async def gen_enum(self):
        from ..ide.render import EnumRenderService, EnumTable
        from apps.entity import EnumTable as model
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.enum_uuid,
            model.enum_name,
            model.value,
            model.description
        )

        # enum_list = await engine.access.list_enum_by_app_uuid(self.app_uuid, fields=fields)
        # enum_list = list(enum_list)
        module_uuid_map = dict()
        module_enum_map = dict()
        module_dict = dict()
        modules, enum_list = await asyncio.gather(
            engine.access.list_module_by_app_uuid(
                self.app_uuid, with_sys=True, as_dict=True),
            engine.access.list_enum_by_app_uuid(self.app_uuid, fields=fields))
        enum_list = list(enum_list)
        modules = {m["module_uuid"]: m for m in modules}
        for enum in enum_list:
            module_uuid = enum["module_uuid"]
            if not module_uuid_map.get(module_uuid):
                module = modules[module_uuid]
                module_name = module["module_name"]
                module_uuid_map[module_uuid] = module_name
                module_enum_map[module_name] = list()
            # 枚举项名称导致发布错误，在这里检查枚举项名称，有错误就抛出异常
            enum_name = enum.get("enum_name")
            if PythonKeyWords.KWDICT.get(enum_name) or \
                    not check_lemon_name(enum_name, chinese=True, size=40):
                raise LemonPublishError(LDEC.ENUM_NAME_FAILED, element_uuid=enum["enum_uuid"],
                                        element_type_num=4)
            enum_items = enum.get("value")
            for enum_item in enum_items:
                enum_item_name = enum_item.get("name")
                if PythonKeyWords.KWDICT.get(enum_item_name) or \
                        not check_lemon_name(enum_item_name, chinese=True, size=40):
                    raise LemonPublishError(LDEC.ENUM_ITEM_NAME_FAILED, element_uuid=enum["enum_uuid"],
                                            element_type_num=4,
                                            true_element_uuid={
                                                enum["enum_uuid"]: enum_item["uuid"]},
                                            true_element_name={enum["enum_uuid"]: enum_item_name})
            ######
            module_enum_map[module_uuid_map[module_uuid]].append(enum)
        for module in module_enum_map:
            if module not in module_dict:
                module_dict[module] = {}
            # if "enums" not in module_dict[module]:
            #     module_dict[module]["enums"] = {}
            module_path = f"{self.app_path_resources}/{self.app_name}/{module}"
            enum_list = []
            for enum in module_enum_map[module]:
                enum_table = EnumTable(
                    uuid=enum["enum_uuid"], name=enum["enum_name"], description=enum["description"], value=enum["value"])
                enum_table.enum_name = enum_table.name
                enum_list.append(enum_table)
                module_dict[module][enum_table.uuid] = enum_table.name
            if not self.ext_tenant:
                service = EnumRenderService(module_path, enum_list)
                # service.save()
                await self.app.loop.run_in_executor(None, service.save)
        return module_dict
    @log_step
    async def gen_py_modules(self):
        from ..ide.render import PyModuleRenderService
        modules: List[Module] = await engine.access.list_module_by_app_uuid(app_uuid=self.app_uuid, as_dict=False, with_sys=True)
        module_py_modules_dict = {}
        for module in modules:
            document_content_list: List[DocumentContent] = await engine.access.list_document_content_by_app_uuid_document_type(
                app_uuid=self.app_uuid, document_type=DocumentType.PY_MODULE, as_dict=False,
                additional_conditions=[Document.module_uuid == module.module_uuid]
            )
            module_path = f"{self.app_path_resources}/{self.app_name}/{module.module_name}"
            obj_list = [(dc.document_uuid, dc.document_content, module.to_dict()) for dc in document_content_list]
            render_service = PyModuleRenderService(module_path, obj_list)
            await self.app.loop.run_in_executor(None, render_service.save)
            module_py_modules_dict[module.module_name] = {dc.document_uuid: dc.document_content.get("name") for dc in document_content_list}
        return module_py_modules_dict
    @log_step
    async def gen_func(self):
        from ..ide.render import FuncRenderService, Func
        from apps.entity import Func as model
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.func_uuid,
            model.func_name,
            model.icon_type,
            model.icon,
            model.install_sm_toolbox,
            model.sm_tool_name,
            model.sm_tool_category,
            model.description,
            model.func,
            model.arg_list,
            model.return_list,
            model.from_py_module
        )

        # func_list = await engine.access.list_func_by_app_uuid(self.app_uuid, fields=fields, with_sys=True)
        # func_list = list(func_list)
        # app_log.debug(func_list)
        module_uuid_map = dict()
        module_func_map = dict()
        module_dict = dict()
        modules, func_list = await asyncio.gather(
            engine.access.list_module_by_app_uuid(
                self.app_uuid, with_sys=True, as_dict=True),
            engine.access.list_func_by_app_uuid(self.app_uuid, fields=fields, with_sys=True))
        func_list = list(func_list)
        modules = {m["module_uuid"]: m for m in modules}
        if SYSConfig.MODULE_UUID in modules:
            for func in SysModelFunc.ALL:
                base_func = deepcopy(SysModelFunc.BASE_FUNC)
                base_func.update(func)
                base_func["app_uuid"] = self.app_uuid
                base_func["document_uuid"] = lemon_uuid()
                func_list.append(base_func)  # 添加const里定义的func_list
        for func in func_list:
            module_uuid = func["module_uuid"]
            if not module_uuid_map.get(module_uuid):
                module = modules[module_uuid]
                module_name = module["module_name"]
                module_uuid_map[module_uuid] = module_name
                module_func_map[module_name] = list()
            module_func_map[module_uuid_map[module_uuid]].append(func)
        for module, module_funcs in module_func_map.items():
            if module not in module_dict:
                module_dict[module] = {}
            # if "funcs" not in module_dict[module]:
            #     module_dict[module]["funcs"] = {}
            module_path = f"{self.app_path_resources}/{self.app_name}/{module}"
            func_list = []
            for func in module_funcs:
                module_uuid = func["module_uuid"]
                func_table = Func(uuid=func["func_uuid"], name=func["func_name"], description=func["description"],
                                  func=func["func"], arg_list=func["arg_list"],
                                  return_list=func["return_list"],
                                  icon=func["icon"], icon_type=func["icon_type"],
                                  install_sm_toolbox=func["install_sm_toolbox"],
                                  sm_tool_name=func["sm_tool_name"],
                                  sm_tool_category=func["sm_tool_category"],
                                  from_py_module=func.get("from_py_module", False),
                                  document_uuid=func.get("document_uuid", ""))
                func_table.func_uuid = func_table.uuid
                func_table.func_name = func_table.name
                module_info_dict = {
                    "module_uuid": module_uuid, "module_name": module}
                func_list.append((func_table, module_info_dict))
                module_dict[module][func_table.uuid] = func_table.name
            app_log.debug((module_path, func_list))
            if not self.ext_tenant:
                service = FuncRenderService(module_path, func_list)
                # service.save()
                await self.app.loop.run_in_executor(None, service.save)
        return module_dict

    @log_step
    async def gen_workflows(self):
        from ..ide.render import WfRenderService
        from apps.services.document.workflow import Workflow
        from apps.entity import Workflow as model
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.wf_uuid,
            model.wf_name,
            model.description
        )

        module_uuid_map = dict()
        module_wf_map = dict()
        module_dict = dict()
        modules, wf_list = await asyncio.gather(
            engine.access.list_module_by_app_uuid(
                self.app_uuid, with_sys=True, as_dict=True),
            engine.access.list_workflow_by_app_uuid(self.app_uuid, fields=fields, with_sys=True))
        wf_list = list(wf_list)
        modules = {m["module_uuid"]: m for m in modules}
        for wf in wf_list:
            module_uuid = wf["module_uuid"]
            if not module_uuid_map.get(module_uuid):
                module = modules[module_uuid]
                module_name = module["module_name"]
                module_uuid_map[module_uuid] = module_name
                module_wf_map[module_name] = list()
            module_wf_map[module_uuid_map[module_uuid]].append(wf)
        for module, module_wfs in module_wf_map.items():
            if module not in module_dict:
                module_dict[module] = {}
            module_path = f"{self.app_path_resources}/{self.app_name}/{module}"
            wf_list = []
            for wf in module_wfs:
                wf_table = Workflow(
                    name=wf["wf_name"], description=wf["description"])
                wf_table.wf_uuid = wf["wf_uuid"]
                wf_table.wf_name = wf_table.name
                wf_list.append(wf_table)
                module_dict[module][wf_table.wf_uuid] = wf_table.wf_name
            app_log.debug((module_path, wf_list))
            service = WfRenderService(module_path, wf_list)
            await self.app.loop.run_in_executor(None, service.save)
        return module_dict

    async def gen_pic_table(self):
        pass

    @log_step
    async def gen_json(self):
        theme_json = await self.gen_theme_json()
        logo = None
        if theme_json:
            logo = theme_json.get("logo")
        await self.gen_front_app_setting(self.app_name, self.app_icon, logo)

    @log_step
    async def gen_back_front_const(self):
        from apps.ide_const import DocumentType
        from apps.entity import Document, DocumentContent
        query = Document.select(Document.document_type, Document.document_uuid, DocumentContent.document_content).join(
            DocumentContent, join_type=JOIN.RIGHT_OUTER, on=(Document.document_uuid == DocumentContent.document_uuid)).where(
            Document.app_uuid == self.app_uuid, (Document.document_type == DocumentType.CONST) | (Document.document_type == DocumentType.ENUM))
        result = await engine.access.list_obj(Document, query)
        const_list = []
        enum_list = []
        for r in result:
            if r["document_type"] == DocumentType.CONST:
                r_const_list = r["document_content"].get("const_list")
                if r_const_list:
                    const_list.extend(r_const_list)
            elif r["document_type"] == DocumentType.ENUM:
                r_enum_list = r["document_content"].get("enum_list")
                enum_list.extend(r_enum_list)
        # const_json = {"const_list": const_list}
        # enum_json = {"enum_list": enum_list}
        # 将一些常量写到run_time下以便前端运行时获取
        model_template = jinja2_env.get_template("front_const.py")
        app_json = {"const_list": const_list, "enum_list": enum_list}
        if app_config:
            runtime_config = await model_template.render_async(**app_json)
            with open(f"{self.app_path}/back/runtime/front_const.py", "w") as target:
                target.write(runtime_config)

    @log_step
    async def gen_theme_json(self):
        from apps.index.const import DefaultDocument
        theme_json = DefaultDocument.THEME
        theme = await engine.access.list_document_by_app_uuid_document_type(app_uuid=self.app_uuid, document_type=DocumentType.THEME, as_dict=False)
        if theme:
            theme = list(theme)[0]
            content = await engine.access.get_document_content_by_document_uuid(theme.document_uuid)
            if content:
                theme_json = content.document_content
        if self.build_type not in [-1, -2] and not self.build_front:
            last_front_version = f"{self.config.runtime_front_path}/{self.app_env}/{self.last_version}"
            try:
                with open(f"{last_front_version}/theme.json") as target:
                    last_theme_json = ujson.load(target)
            except:
                app_log.error(traceback.format_exc())
                app_log.info("build_front")
                self.build_front = True
            else:
                if last_theme_json != theme_json:
                    app_log.info("build_front")
                    self.build_front = True
                else:
                    return theme_json
        if self.k8s_support:
            current_version_dir = self.runtime_front_path
            if not os.path.exists(current_version_dir):
                await self.create_dir(current_version_dir)
            with open(f"{current_version_dir}/theme.json", "w") as target:
                ujson.dump(theme_json, target)
        front_json_path = self.build_front_path + "/runtime/config"
        with open(f"{front_json_path}/theme.json", "w") as target:
            ujson.dump(theme_json, target)
        with open(f"{self.build_front_path}/runtime/src/json/theme.json", "w") as target:
            ujson.dump(theme_json, target)
        if self.has_mobile:
            mobile_theme = self._convert_mobile_less(theme_json)
            with open(f"{front_json_path}/theme_mobile.less", "w") as f:
                f.write(mobile_theme)
        return theme_json

    def _convert_mobile_less(self, theme_json):
        mobile_theme = str()
        for key, value in theme_json.items():
            mobile_key = theme_key_pc_to_mobile_map.get(key)
            if mobile_key:
                mobile_theme += f"@{mobile_key}: {value};\n"
        return mobile_theme

    @log_step
    async def gen_front_app_setting(self, app_name, app_icon, logo):
        self.icon_dict = {"icon": app_icon, "logo": logo}
        if self.build_type not in [-1, -2] and not self.build_front:
            last_front_version = f"{self.config.runtime_front_path}/{self.app_env}/{self.last_version}"
            try:
                with open(f"{last_front_version}/defaultSettings.js") as target:
                    last_front_default_setting = target.read()
            except:
                app_log.error(traceback.format_exc())
                app_log.info("build_front")
                self.build_front = True
            else:
                res = re.match(
                    r"[\s\S]*title:[ ]*'(?P<title>\w+)'[\s\S]*logo:[ ]*'(?P<logo>.*)'[\s\S]*iconfontUrl:[ ]*'(?P<iconfontUrl>.*)'", last_front_default_setting)
                if res:
                    current_setting = res.groupdict()
                    if current_setting.get("title") != app_name or \
                        (logo and current_setting.get("logo") != logo) or \
                            (app_icon and current_setting.get("iconfontUrl") != app_icon):
                        app_log.info("build_front")
                        self.build_front = True
                    else:
                        return
                else:
                    app_log.info("build_front")
                    self.build_front = True
        # 本地环境修改b配置文件
        if self.k8s_support:
            front_setting_path = self.build_front_path + "/runtime/config"
            with open(f"{front_setting_path}/defaultSettings.js") as target:
                front_default_setting = target.read()
            default_setting = re.sub(
                r"title:[ ]*'(\w+)'", f"title: '{app_name}'", front_default_setting)
            if logo:
                default_setting = re.sub(
                    r"logo:[ ]*'(.*)'", f"logo: '{logo}'", default_setting)
            if app_icon:
                default_setting = re.sub(
                    r"iconfontUrl:[ ]*'(.*)'", f"iconfontUrl: '{app_icon}'", default_setting)
            with open(f"{front_setting_path}/defaultSettings.js", "w") as target:
                target.write(default_setting)
            if self.k8s_support:
                current_version_dir = self.runtime_front_path
                if not os.path.exists(current_version_dir):
                    await self.create_dir(current_version_dir)
                with open(f"{current_version_dir}/defaultSettings.js", "w") as target:
                    target.write(default_setting)
            app_log.info(default_setting)

    @log_step
    async def gen_front(self):
        app_log.info(
            f"-------------------[gen front: {self.app_version} {self.front_version}]-----------------")
        await asyncio.gather(self.gen_mobile(), self.gen_pc())
        if not os.path.exists(self.runtime_front_path):
            await self.create_dir(self.runtime_front_path)

    @log_step
    async def gen_mobile(self):
        app_log.info(f"publish_app: {self.publish_app}")
        if self.has_mobile and self.publish_app and self.build_app:
            front_path = self.build_front_path + "/runtime"
            mobile_front_path = front_path + "_mobile"
            await async_run(f"cp -rd {front_path} {mobile_front_path}")
            await asyncio.gather(self._gen_front('app'))
            if self.has_mobile and not os.path.exists(f"{self.runtime_front_path}_m"):
                await self.create_dir(f"{self.runtime_front_path}_m")

    @log_step
    async def gen_pc(self):
        app_log.info(f"publish_pc: {self.publish_pc}")
        # await self._gen_front("pc")
        if self.publish_pc and self.build_pc:
            front_path = self.build_front_path + "/runtime"
            desktop_front_path = front_path + "_desktop"
            await async_run(f"cp -rd {front_path} {desktop_front_path}")
            await self._gen_front("desktop")
            if self.has_mobile and not os.path.exists(f"{self.runtime_front_path}_pc"):
                await self.create_dir(f"{self.runtime_front_path}_pc")

    @property
    def desktop_front_path(self):
        runtime_front_path = f"{engine.config.STATIC_PATH_PREFIX}/{self.electron_dir_name}/{self.app_env}"
        return runtime_front_path

    @property
    def deploy_static_path(self):
        deploy_path = f"{engine.config.STATIC_PATH_PREFIX}/local_deploy/{self.app_env}"
        return deploy_path

    @log_step
    async def _gen_front(self, name):
        domain_name = self.config.domain_name
        build_param = "build"
        # 静态文件或者安装包存放目录
        runtime_front_path = self.runtime_front_path
        build_out = "dist"
        if name == "mobile":
            runtime_front_path = f"{runtime_front_path}_m"
            domain_name = f"m{domain_name}"
            build_param = f"{build_param}-m"
            build_out = "mobile_dist"
            if self.app.config.ENV != "Production":
                build_param += "d"
        elif name == 'app':
            runtime_front_path = f"{engine.config.STATIC_PATH_PREFIX}/{self.static_dir_name}/{self.app_env}"
            domain_name = f"m{domain_name}"
            build_param = f"{build_param}-app"
            build_out = self.build_front_path + \
                "/lemonCvd/platforms/android/app/build/outputs/apk/debug/app-debug.apk"
            if self.app.config.ENV != "Production":
                build_param += "d"
        elif name == "desktop":
            runtime_front_path = self.desktop_front_path
            build_param = f"{build_param}-pc"
            build_out = "pc_dist"
        front_path = self.build_front_path + "/runtime"
        # 运行时已经共用静态文件了, 发布的时候不用build网页版
        if name == "pc":
            front_path = front_path + "_pc"
        elif name == "mobile":
            front_path = front_path + "_mobile"
        elif name == "desktop":
            front_path = front_path + "_desktop"
        with open(f"{front_path}/config/path.js", "w") as f:
            f.write(f"export const publicPath = '/{self.app_env}/';\n")
            if name == "pc":
                f.write(
                    f"export const wsPath = '{self.ws_address}/{self.app_env}';\n")
            elif name == "desktop":
                f.write(
                    f"export const wsPath = '{self.ws_address}/{self.app_env}';\nexport const pcUrl = '{self.server_address}/{self.app_env}';\n")
            elif name == "mobile":
                f.write(
                    f"export const wsPath = '{self.ws_address}/{self.app_env}';\n")
            elif name == 'app':
                f.write(
                    f"export const wsPath = '{self.ws_address}/{self.app_env}';\nexport const appUrl = '{self.server_address}/{self.app_env}';\n")

        if not os.path.exists(runtime_front_path):
            await self.create_dir(runtime_front_path)
        if name == 'app':
            www_path = f"{self.build_front_path}/lemonCvd/www"
            cmd1 = f"cd {front_path} && cp -rf {engine.config.STATIC_PATH_PREFIX}/runtime_app {www_path}"
            result = await async_run(cmd1)
            if result[0] == 0:
                apk_title = self.app_name
                if self.env == "test":
                    apk_title += "（测试环境）"
                api_path = f"{self.mobile_address}/{self.app_env}"
                model_template = jinja2_env.get_template("cordova_config.js")
                front_app_config = {
                    "app_uuid": self.app_uuid,
                    "app_name": self.app_name,
                    "app_env": self.env,
                    "api_path": api_path,
                    "ws_path": f"{self.mobile_ws_address}/{self.app_env}"
                }
                cordova_config = await model_template.render_async(**front_app_config)
                with open(f"{www_path}/cordova_config.js", "w") as target:
                    target.write(cordova_config)
                result = await async_run(f"cp {self.image_workspace}/mobile_style.css {www_path}/page_style.css")
                app_log.info(result)
                # await self.replace_app_index(www_path, api_path)
                with open(f"{self.deploy_path}/client/version", "r") as f:
                    delply_version = f.readline().strip()
                codepush_env = self.app_env
                if self.publish_for_local:
                    codepush_env += "local"
                codepush_cmd = f"cd {self.deploy_path}/cordova \
                    && zx ./codepush.mjs {engine.app.config.ENV} --app_uuid={codepush_env} \
                        --platform=Android --app_name={apk_title} --content_src='{self.app_address}/{self.app_env}/' \
                            --icon={self.app_icon} --appVersion={delply_version}.{self.front_version}"
                result = await async_run(codepush_cmd)
                app_log.info(f"codepush result: {result}")
                if result[0] == 0:
                    result = await async_run(
                        f"cp {build_out} {runtime_front_path}/{self.app_env}.apk")
        elif name == "desktop":
            desktop_config = {
                "app_uuid": self.app_uuid,
                "app_name": self.app_name,
                "app_env": self.env,
                "api_path": f"{self.server_address}/{self.app_env}",
                            "ws_path": f"{self.ws_address}/{self.app_env}",
                            "entry": f"{self.desktop_address}/{self.app_env}/",
                            "updateConfig": {
                                "url": f"https://{domain_name}/static/design/{self.electron_dir_name}/{self.app_env}/"
                            }
            }
            app_name = self.app_name
            if self.env:
                app_name = f"{app_name}({self.env})"
            build_config = {
                "productName": app_name,
                "appId": f"com.lemonstudio.{self.app_env}",
            }
            with open(f"{front_path}/electronResources/app_config.json", "r") as source:
                old_app_config = ujson.load(source)
            with open(f"{front_path}/electronResources/app_config.json", "w") as target:
                old_app_config.update(desktop_config)
                ujson.dump(old_app_config, target)
            with open(f"{front_path}/package.json", "r") as source:
                old_package_config = ujson.load(source)
                nsis = old_package_config.get('build').get('nsis', {})
                nsis["shortcutName"] = app_name
                build_config['nsis'] = nsis
                old_package_config["build"].update(build_config)
            with open(f"{front_path}/package.json", "w") as target:
                ujson.dump(old_package_config, target)
            if self.app_icon:
                image_type = os.path.splitext(self.app_icon)[-1]
                image_path = f"{front_path}/icon{image_type}"
                if "http" in self.app_icon:
                    icon_cmd = f"wget {self.app_icon} --no-check-certificate -O {image_path}"
                    result = await async_run(icon_cmd)
                    app_log.info(result)
                else:
                    await DesignOss().get_oss_object(
                        "", self.app_icon, to_file=image_path)
                cmd4 = f"convert {image_path}  -resize '256x256!' {front_path}/app/icons/icon.ico"
                result = await async_run(cmd4)
                app_log.info(result)
            cmd7 = f"cd {front_path} && npm run pc-dist-win64 "
            result = await async_run(cmd7)
            if result[0] == 0:
                last_yml = f"{front_path}/pc_installer/latest.yml"
                with open(last_yml, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    exe_name = data["path"]
                    cmd = f"cd {front_path}/pc_installer/ && cp -r '{exe_name}' latest.yml {runtime_front_path}/"
                    result = await async_run(cmd)
                    app_log.info(result)
        else:
            return None

        app_log.info(result)
        if result[0] != 0:
            raise Exception(f"build front failed, {result[2]}")

    async def handle_module_role2user_role(self, user_role_map, user_role_attr_map, module_role_map):
        """

        构造user_role与module_role的对照关系
        更新UserRoleContent, 去除被删除的module_role
        """
        user_role_list = await engine.access.list_user_role_by_app_uuid(self.app_uuid)
        module_role_list = await engine.access.list_module_role_by_app_uuid(self.app_uuid)
        app_log.info(f"user_role: {list(user_role_list)}")
        # app_log.info(f"module_role: {list(module_role_list)}")
        # 删除module_role时,没有移除在user_role中的引用,在这儿进行删除引用
        exist_module_role = {r["role_uuid"]: r for r in module_role_list}
        user_role2module_role = defaultdict(list)
        task_list = list()
        for user_role in user_role_list:
            # await self.handle_module_user_role(user_role, user_role_attr_map,user_role_map,
            #                                 module_role_map,user_role2module_role,
            #                                 exist_module_role)
            task_list.append(
                self.handle_module_user_role(user_role, user_role_attr_map, user_role_map,
                                             module_role_map, user_role2module_role,
                                             exist_module_role))
        await asyncio.gather(*task_list)
        # app_log.info(f"module_role_map: {module_role_map}")
        # app_log.info(f"user_role2module_role: {user_role2module_role}")
        return user_role2module_role

    async def handle_module_user_role(self, user_role, user_role_attr_map,
                                      user_role_map, module_role_map,
                                      user_role2module_role, exist_module_role):
        model = UserRoleContent
        user_role_uuid = user_role["role_uuid"]
        user_role_map[user_role_uuid] = dict()
        user_role_attr_map[user_role_uuid] = {"name": user_role["role_name"],
                                              "is_admin": 1 if user_role["is_admin"] else 0}
        # app_log.info(f"{user_role_uuid}, 222222")
        user_role_content_list = await engine.access.list_user_role_content_by_user_role(
            user_role_uuid=user_role_uuid)
        for user_role_content in user_role_content_list:
            user_module_roles = []
            # app_log.info(f"{user_role_content}, 3333333")
            for module_role in user_role_content["module_role"]:
                if module_role["uuid"] in exist_module_role:
                    user_module_roles.append(module_role)
                else:
                    continue
                module_role_map.setdefault(module_role["uuid"], [])
                module_role_map[module_role["uuid"]].append(user_role_uuid)
                user_role2module_role[user_role_uuid].append(
                    module_role["uuid"])
            # 处理module_role可能被删除的情况
            if len(user_module_roles) != len(user_role_content["module_role"]):
                q = model.update(module_role=user_module_roles).where(
                    model.module_uuid == user_role_content["module_uuid"],
                    model.user_role == user_role_uuid)
                await engine.access.update_obj_by_query(model, q)

    @log_step
    async def gen_app_roles(self):
        """
        遍历module_role,生成基于module_role的权限, 生成每个module_role的权限
        最后再遍历module_role与user_role对照, 处理成全部的
        """

        def _set_relation_permission(relation, fields, permission, model_uuid):
            # 1 -> 1, 1 -> * 时外键的权限，
            fields.update({relation["relationship_uuid"]
                          : {"permission": permission}})
            fields.update({".".join([relation["model_uuid"], model_uuid]): {
                          "permission": permission}})     # 1 -> *， * -> * backref权限
            fields.update({".".join([model_uuid, relation["model_uuid"]]): {
                          "permission": permission}})     # * -> *    ref权限
            if relation["relationship_type"] == 1:  # * -> * 时第三方表的权限
                permission = bin(PermissionAction.INSERT | PermissionAction.UPDATE |
                                 PermissionAction.SELECT | PermissionAction.DELETE)
                fields.update({relation["relationship_uuid"]: {
                              "permission": permission}})
                fields.update({base64.b64encode(uuid.uuid5(uuid.UUID(relation["relationship_uuid"]), relation["target_model"]).bytes).decode().rstrip("=").replace("+", "_").replace("/", "-"):
                               {"permission": permission}})
                fields.update({base64.b64encode(uuid.uuid5(uuid.UUID(relation["relationship_uuid"]), relation["source_model"]).bytes).decode().rstrip("=").replace("+", "_").replace("/", "-"):
                               {"permission": permission}})
                fields.update({".".join([model_uuid, relation["relationship_uuid"]]): {
                              "permission": permission}})

        def _process_relationship(r_list, fields, permission, model_uuid, self_associations=None):
            self_associations = self_associations or dict()
            for relation in r_list:
                need_set_permission = [relation]
                if relation["relationship_uuid"] in self_associations:
                    # 判断r是不是自关联, 如果是, 将对应的source/target 添加到 [r]
                    true_relation = copy(relation)
                    s: dict = self_associations[true_relation["relationship_uuid"]]
                    r_key = "relationship_uuid_" + \
                        ("source" if relation["is_source"] else "target")
                    true_relation.update({
                        "relationship_uuid": s[r_key]
                    })
                    need_set_permission.append(true_relation)
                for r in need_set_permission:
                    _set_relation_permission(r, fields, permission, model_uuid)

        import json
        module_role_map = {}
        resource_map = defaultdict(set)
        user_role_map = {}
        user_role_attr_map = {}
        filter_map = {}

        module_role_permission_map = {}  # {module_role_id: {}}
        # {module_role_id:{model_uuid: [condition]}}  模块角色的condition
        module_condition_map = {}

        # 处理user_role权限user_role_attr_map
        # 处理module_role与user_role对照关系:  module_role: [user_roles]
        # user_role2module_role = await self.handle_module_role2user_role(user_role_map, user_role_attr_map, module_role_map)

        # module_role_list = await engine.access.list_module_role_by_app_uuid(app_uuid=self.app_uuid)

        # model_document = await engine.access.list_document_content_by_app_uuid_document_type(
        #     app_uuid=self.app_uuid, document_type=DocumentType.MODEL)
        # # 构造自关联字典
        # self_associations = pre_process_self_associate(model_document)
        user_role2module_role, module_role_list, model_document, model_list, app_setting, field_list, r_list = await asyncio.gather(
            self.handle_module_role2user_role(
                user_role_map, user_role_attr_map, module_role_map),
            engine.access.list_module_role_by_app_uuid(app_uuid=self.app_uuid),
            engine.access.list_document_content_by_app_uuid_document_type(
                app_uuid=self.app_uuid, document_type=DocumentType.MODEL),
            engine.access.list_model_by_app_uuid(app_uuid=self.app_uuid),
            engine.access.get_app_setting(app_uuid=self.app_uuid),
            engine.access.list_field_by_app_uuid(
                self.app_uuid, with_sys=True, need_sys=True),
            engine.access.list_relationship_by_app_uuid(
                self.app_uuid, with_sys=True)

        )
        # 构造自关联字典
        self_associations = pre_process_self_associate(model_document)
        # model_list = await engine.access.list_model_by_app_uuid(
        #     app_uuid=self.app_uuid)
        model_dict = dict()
        model_basic_dict = dict()
        for model in model_list:
            m_uuid = model.get(ModelBasic.model_uuid.name, "")
            model_dict.update({m_uuid: model})
            model["relations"] = []
            model["fields"] = []
            model_basic_dict[m_uuid] = model
        for relationship in r_list:
            source_model_uuid = relationship.get(
                RelationshipBasic.source_model.name)
            target_model_uuid = relationship.get(
                RelationshipBasic.target_model.name)
            source_model = model_basic_dict.get(source_model_uuid)
            target_model = model_basic_dict.get(target_model_uuid)
            if source_model:
                s_relationship = copy(relationship)
                s_relationship["model_uuid"] = target_model_uuid
                s_relationship["is_source"] = True
                source_model["relations"].append(s_relationship)
            if target_model:
                relationship["is_source"] = False
                relationship["model_uuid"] = source_model_uuid
                target_model["relations"].append(relationship)
        for field in field_list:
            model_uuid = field.get(ModelField.model_uuid.name)
            filed_model = model_basic_dict.get(model_uuid)
            if filed_model:
                filed_model["fields"].append(field)

        for module_role in module_role_list:
            # 单独处理每个module_role的权限
            module_role_uuid = module_role["role_uuid"]
            if module_role_uuid not in module_role_map:
                # 没有被user_role选中的不处理
                continue

            module_role_permission_map.setdefault(module_role_uuid, {})
            one_module_permission = module_role_permission_map[module_role_uuid]

            # 处理页面
            for page_uuid, page_content in module_role["pages"].items():
                permission = int(page_content["action"], 2)
                if page_uuid in one_module_permission:
                    one_module_permission[page_uuid] |= permission
                else:
                    one_module_permission[page_uuid] = permission
                # TODO 要把每个page_uuid与[user_role]的对照关系添加到resource_map

            # 处理云函数
            for func_uuid, func_content in module_role["funcs"].items():
                permission = int(func_content["action"], 2)
                if func_uuid in one_module_permission:
                    one_module_permission[func_uuid] |= permission
                else:
                    one_module_permission[func_uuid] = permission
                # TODO func_uuid与[user_role]的对照关系添加到resource_map

            for model_uuid, model_content in module_role["models"].items():
                if model_uuid == "undefined":
                    # 过滤本地脏数据
                    continue

                # 处理模型
                permission = int(model_content.get("action", '0'), 2)
                if model_uuid in one_module_permission:
                    one_module_permission[model_uuid] |= permission
                else:
                    one_module_permission[model_uuid] = permission
                # TODO model_uuid与[user_role]的对照关系添加到resource_map

                # 处理condition
                filter_ = model_content.get("filter", {})
                condition = filter_.get("condition", [])
                if condition:
                    module_condition_map.setdefault(module_role_uuid, {})
                    models_conditions = module_condition_map[module_role_uuid]
                    if models_conditions.get(model_uuid):
                        models_conditions[model_uuid].extend(condition)
                    else:
                        models_conditions.update({model_uuid: condition})

                # 处理fields权限
                fields = model_content.get("fields")
                r_list = list()
                # model作为target和source的全部加入
                m_dict = model_dict.get(model_uuid)
                # app_log.info(f"model_uuid: {model_uuid}")
                need_sys = True if m_dict else False
                # relations = await engine.access.list_relationship_by_source_model(
                #     model_uuid=model_uuid, need_sys=need_sys, model_dict=m_dict)
                # r_list.extend(relations)
                # relations = await engine.access.list_relationship_by_target_model(
                #     model_uuid=model_uuid)
                # r_list.extend(relations)
                r_list = model_basic_dict.get(
                    model_uuid, {}).get("relations", [])
                if fields:
                    if not isinstance(fields, dict):  # 全字段权限设置
                        permission = fields
                        # field_list = await engine.access.list_field_by_model_uuid(model_uuid=model_uuid, need_sys=True)
                        field_list = model_basic_dict.get(
                            model_uuid, {}).get("fields", [])
                        fields = {field["field_uuid"]: {
                            "permission": permission} for field in field_list}
                        system_fields = list_system_relationship([m_dict])
                        r_list.extend(system_fields)
                        _process_relationship(
                            r_list, fields, permission, model_uuid, self_associations=self_associations)
                    else:  # 具体字段设置
                        for r in r_list:
                            r: dict
                            if r["relationship_uuid"] in fields:
                                # r如果是自关联, 把source/target时一起计算权限
                                real_r_list = [r]
                                if r["relationship_uuid"] in self_associations:
                                    real_r_list = list(
                                        filter(lambda x: x["relationship_uuid"] == r["relationship_uuid"], r_list))
                                permission = fields.get(
                                    r["relationship_uuid"], {}).get("permission")
                                _process_relationship(
                                    real_r_list, fields, permission, model_uuid, self_associations=self_associations)

                # 关联权限
                if fields:
                    for field_uuid, field_content in fields.items():
                        permission = int(field_content["permission"], 2)
                        if field_uuid in one_module_permission:
                            one_module_permission[field_uuid] |= permission
                        else:
                            one_module_permission[field_uuid] = permission

        try:
            for user_role_uuid, module_role_list in user_role2module_role.items():
                user_role_map[user_role_uuid] = dict()
                filter_map[user_role_uuid] = dict()

                for module_role_uuid in module_role_list:
                    one_module_role_permission = module_role_permission_map.get(
                        module_role_uuid)
                    for resource_id in one_module_role_permission:
                        # 各resource与[user_role]对照, 此处为set, 序列化时会认为是list
                        resource_map[resource_id].add(user_role_uuid)

                        # 各user_role中resource的具体权限
                        permission = one_module_role_permission[resource_id]
                        if resource_id in user_role_map[user_role_uuid]:
                            user_role_map[user_role_uuid][resource_id] |= permission
                        else:
                            user_role_map[user_role_uuid].update(
                                {resource_id: permission})

                    module_condition_map: dict
                    filter_map: dict
                    # 处理模型的condition
                    one_module_condition_map = module_condition_map.get(
                        module_role_uuid, {})
                    for model_uuid, conditions in one_module_condition_map.items():
                        # TODO 不同module的, 可能是同一个model_uuid
                        if model_uuid in filter_map[user_role_uuid]:
                            old_conditions_str = filter_map[user_role_uuid][model_uuid]
                            old_conditions: list = json.loads(
                                old_conditions_str)
                            old_conditions.extend(conditions)
                            conditions_str = json.dumps(old_conditions)
                        else:
                            conditions_str = json.dumps(conditions)
                        filter_map[user_role_uuid][model_uuid] = conditions_str

        except:
            app_log.error(traceback.print_exc())
            raise Exception("process permission_map error")

        # app_setting = await engine.access.get_app_setting(app_uuid=self.app_uuid)
        # app_log.debug(f"resource_map: {resource_map}")
        permission_map_content = list()
        permission_map_content.append(
            f"permission_check = {app_setting.permission_check}\n")
        permission_map_content.append(f"anonymous = {app_setting.anonymous}\n")
        permission_map_content.append(
            f"anonymous_role = '{app_setting.anonymous_role}'\n")
        permission_map_content.append(
            f"resource_map = {json.dumps(resource_map, cls=LemonJsonEncoder)}\n")
        permission_map_content.append(
            f"user_role_map = {json.dumps(user_role_map, cls=LemonJsonEncoder)}\n")
        permission_map_content.append(
            f"filter_map = {json.dumps(filter_map, cls=LemonJsonEncoder)}\n")
        permission_map_content.append(
            f"user_role_attr_map = {json.dumps(user_role_attr_map, cls=LemonJsonEncoder)}\n")
        permission_map_content.append(
            f"module_role_depend_map = {json.dumps(module_role_map, cls=LemonJsonEncoder)}\n")

        with open(f"{self.app_path_resources}/{self.app_name}/permission_map.py", "w") as target:
            target.writelines(permission_map_content)

    @log_step
    def gen_decode_context(self):
        self.decode_context["app_uuid"] = self.app_uuid
        self.decode_context["app_env"] = self.app_env
        self.decode_context["app_revision"] = self.app_revision
        self.decode_context["app_version"] = self.app_version
        self.decode_context["front_version"] = self.front_version
        self.decode_context["middle_user_path"] = self.middle_user_path
        self.decode_context["middle_user_uuid"] = self.database_name
        self.decode_context["server_port"] = get_free_port()
        self.decode_context["app_path"] = self.app_path
        self.decode_context["app_name"] = self.app_name
        self.decode_context["front_path"] = self.runtime_front_path
        self.decode_context["domain_name"] = self.config.domain_name
        self.decode_context["front_root_path"] = self.config.runtime_front_path
        self.decode_context["env_name"] = self.env if self.sub_env == 'None' else self.sub_env
        self.decode_context["real_env_name"] = self.env
        self.decode_context["sub_env"] = self.sub_env
        self.decode_context["worker_processes"] = self.worker_processes
        self.decode_context["cloud_worker_processes"] = self.cloud_worker_processes
        self.decode_context["vfs_processes"] = self.vfs_processes
        self.decode_context["cloud_vfs_processes"] = self.cloud_vfs_processes
        self.decode_context["nginx_upstream"] = self.worker_processes > 1 or self.cloud_worker_processes > 1
        self.decode_context["vfs_upstream"] = self.publish_for_local
        self.decode_context["publish_for_local"] = self.publish_for_local
        self.decode_context["tenant_uuid"] = self.tenant_uuid
        app_log.info(
            f"nginx_upstream: {self.decode_context['nginx_upstream']}")
        self.decode_context["process_port_list"] = [
            str(7001 + i) for i in range(self.worker_processes)]
        self.decode_context["cloud_process_port"] = [
            str(7001 + i) for i in range(self.cloud_worker_processes)]
        self.decode_context["vfs_port_list"] = [
            str(7600 + i) for i in range(self.vfs_processes)]
        self.decode_context["cloud_vfs_port"] = [
            str(7600 + i) for i in range(self.cloud_vfs_processes)]
        if self.ext_tenant:
            self.decode_context["program_name"] = str(uuid.uuid5(
                uuid.UUID(self.app_env), self.ext_tenant)).replace('-', '')
        else:
            self.decode_context["program_name"] = self.app_env
            self.decode_context["default_server_port"] = self.decode_context["server_port"]

    @log_step
    async def get_runtime_config(self):
        app_config = await engine.access.get_app_config_by_app_uuid(self.app_uuid)
        if app_config:
            app_setting = app_config.app_setting
            self.decode_context["app_setting"] = app_setting
        else:
            self.decode_context["app_setting"] = {}

    @log_step
    @sub_env_ignore()
    async def process_workflow_status(self):

        def _process_all_version(all_versions):
            new_online_version, last_online_version, new_online_status = "", "", 0
            for version in all_versions[::-1]:
                version_uuid = version.get("uuid")
                version_status = version.get("status")
                if online_version == version_uuid:
                    if version_status == 2:
                        # 即将上线版本已经是上线状态, 不处理
                        break
                    # 即将上线的版本状态设置为上线
                    new_online_status = version["status"]
                    new_online_version = version_uuid
                    version["status"] = 2
                elif version_status == 2:
                    # 之前上线的版本状态设置为下线
                    version["status"] = 1
                    last_online_version = version["uuid"]
            return last_online_version, new_online_version, new_online_status

        workflow_list = await engine.access.list_document_content_by_app_uuid_document_type(
            self.app_uuid, document_type=[DocumentType.WORKFLOW, DocumentType.APPROVALFLOW])
        self.version_status_update_history.clear()
        for workflow_dict in workflow_list:
            document_uuid = workflow_dict.get(Document.document_uuid.name)
            document_content = workflow_dict.get(
                DocumentContent.document_content.name, {})
            online_version = document_content.get("online_version")  # 即将上线的版本
            versions = document_content.get("versions", [])

            last_online_version, new_online_version, new_online_old_status = _process_all_version(versions)
            app_log.info(
                f"new online, document: {document_uuid}, online: {new_online_version}, last: {last_online_version}")
            if new_online_version:
                update_dict = {
                    DocumentContent.document_content.name: document_content
                }
                update_query = DocumentContent.update(**update_dict).where(
                    DocumentContent.document_uuid == document_uuid)
                await engine.access.update_obj_by_query(DocumentContent, update_query, need_delete=True)
                # add_rollbacks
                cur_wf_update = self.version_status_update_history.setdefault(document_uuid, {})
                cur_wf_update.update({
                    "last_online_version": last_online_version,
                    "new_online_version": new_online_version,
                    "new_online_old_status": new_online_old_status
                })

        app_log.info(f"Update {DocumentContent.__name__}")

    @log_step
    async def gen_uuid_map(self):
        # app_log.info(self.decode_context)
        model_template = jinja2_env.get_template("obj_uuid_map.py")
        uuid_map = await model_template.render_async(**self.decode_context)
        with open(f"{self.app_path_resources}/{self.app_name}/obj_uuid_map.py", "w") as target:
            target.write(uuid_map)

    @log_step
    async def gen_k8s_runtime_conf(self):
        model_template = jinja2_env.get_template("runtime.yaml")
        self.decode_context["cas_url"] = self.cas_url
        self.decode_context["app_env"] = self.app_env
        self.decode_context["app_uuid"] = self.app_uuid
        self.decode_context["runtime_image"] = self.config.runtime_image
        self.decode_context["image_version"] = self.config.image_version
        self.decode_context["publish_namespace"] = self.config.publish_namespace
        # self.decode_context["base_image_id"] = self.base_image_id
        self.decode_context["local_deploy"] = self.publish_for_local
        self.decode_context["deploy_image_name"] = f"{self.config.deploy_image}/{self.app_env}"
        self.decode_context["app_revision"] = self.app_revision
        app_config = self.decode_context.get("app_setting")
        if self.env:
            config_dict = app_config.get("env_config", {}).get(self.env, {})
            node_select_dict = config_dict.get("node_select", {})
            resources_limits_dict = config_dict.get("resources_limits", {})
            worker_processes = config_dict.get("worker_processes")
            vfs_processes = config_dict.get("vfs_processes")
        else:
            node_select_dict = app_config.get("node_select", {})
            resources_limits_dict = app_config.get("resources_limits", {})
            worker_processes = app_config.get("worker_processes")
            vfs_processes = app_config.get("vfs_processes")
        if isinstance(worker_processes, int):
            self.cloud_worker_processes = worker_processes
        if isinstance(vfs_processes, int):
            self.cloud_vfs_processes = vfs_processes
        node_select = []
        for select_key, select_value in node_select_dict.items():
            node_select.append((select_key, select_value))
        self.decode_context["node_select"] = node_select
        resources_limits = []
        for limit_key, limit_value in resources_limits_dict.items():
            resources_limits.append((limit_key, limit_value))
        lemon_env = "test"
        # 判断应用是否要部署到稳定环境
        if app_config.get("pro_env", None):
            if not self.env:
                lemon_env = "pro"
        self.decode_context["lemon_env"] = lemon_env
        self.decode_context["resources_limits"] = resources_limits
        container_time = "a" + str(int(time.time()))
        self.decode_context["container_time"] = container_time
        output = await model_template.render_async(**self.decode_context)
        with open("/root/runtime.yaml", "w") as target:
            target.write(output)

    @log_step
    async def gen_k8s_supervisor_conf(self):
        model_template = jinja2_env.get_template("supervisor_runtime.conf")
        self.decode_context["LEMON_ENV"] = self.app.config.ENV
        output = await model_template.render_async(**self.decode_context)
        with open(f"{self.image_workspace}/app.conf", "w") as target:
            target.write(output)
        model_template = jinja2_env.get_template("supervisor_web.conf")
        output = await model_template.render_async(**self.decode_context)
        with open(f"{self.image_workspace}/supervisor_web.conf", "w") as target:
            target.write(output)
        model_template = jinja2_env.get_template("supervisor_agent.conf")
        output = await model_template.render_async(**self.decode_context)
        with open(f"{self.image_workspace}/agent.conf", "w") as target:
            target.write(output)

        model_template = jinja2_env.get_template("supervisor_applet.conf")
        output = await model_template.render_async(**self.decode_context)
        with open(f"{self.image_workspace}/supervisor_applet.conf", "w") as target:
            target.write(output)

        if self.publish_for_local:
            model_template = local_deploy_template.get_template(
                "supervisor_web.conf")
            self.decode_context["LEMON_ENV"] = self.app.config.ENV
            output = await model_template.render_async(**self.decode_context)
            with open(f"{self.image_workspace}/supervisor_web_local.conf", "w") as target:
                target.write(output)
            upstream_template = local_deploy_template.get_template(
                "runtime_nginx_upstream")
            output = await upstream_template.render_async(**self.decode_context)
            with open(f"{self.image_workspace}/nginx_upstream_local.conf", "w") as target:
                target.write(output)
            model_template = local_deploy_template.get_template(
                "supervisor_vfs.conf")
            output = await model_template.render_async(**self.decode_context)
            with open(f"{self.image_workspace}/supervisor_vfs_local.conf", "w") as target:
                target.write(output)

            model_template = local_deploy_template.get_template(
                "supervisor_stream.conf")
            output = await model_template.render_async(**self.decode_context)
            with open(f"{self.image_workspace}/supervisor_stream_local.conf", "w") as target:
                target.write(output)
            model_template = local_deploy_template.get_template(
                "supervisor_applet.conf")
            output = await model_template.render_async(**self.decode_context)
            with open(f"{self.image_workspace}/supervisor_applet_local.conf", "w") as target:
                target.write(output)
    # async def get_runtime_image_id(self):
    #     if self.k8s_support:
    #         image_name = f"{self.config.runtime_image}:{self.config.image_version}"
    #         cmd = f"docker images -q --filter reference={image_name}"
    #         result = await async_run(cmd)
    #         image_id = result[1].rstrip().decode()
    #         app_log.info(f"image_version:{image_id}")
    #         self.base_image_id = image_id

    @log_step
    async def gen_runtime_dockerfile(self):
        # await self.get_runtime_image_id()
        model_template = jinja2_env.get_template("runtime_dockerfile")
        decode_context = {"CMD_LIST": CMD_LIST, "COPY_LIST": COPY_LIST}
        decode_context.update(self.decode_context)
        output = await model_template.render_async(decode_context)
        with open(f"{self.image_workspace}/Dockerfile", "w") as target:
            target.write(output)
        if self.agent_deploy:
            model_template = jinja2_env.get_template("tagImage.sh")
            decode_context = {"baseImage": self.secret_image_name,
                              "appImage": self.image_name}
            output = await model_template.render_async(decode_context)
            with open(f"{self.image_workspace}/tagImage.sh", "w") as target:
                target.write(output)
            await async_run(f"chmod +x {self.image_workspace}/tagImage.sh")

    @log_step
    async def gen_local_deploy_runtime_dockerfile(self):
        model_template = local_deploy_template.get_template(
            "runtime_dockerfile")
        output = await model_template.render_async(**self.decode_context)
        with open(f"{self.image_workspace}/LocalDockerfile", "w") as target:
            target.write(output)
        model_template = local_deploy_template.get_template("update_runtime")
        output = await model_template.render_async(**self.decode_context)
        with open(f"{self.image_workspace}/UpateDockerfile", "w") as target:
            target.write(output)

    async def get_online_service(self):
        service_list = await engine.redis.redis.zrange(self.online_key)
        if not service_list:
            service_list = list()
        return service_list

    async def get_offline_service(self):
        service_list = await engine.redis.redis.zrange(self.offline_key)
        if not service_list:
            service_list = list()
        return service_list

    @log_step
    async def gen_k8s_nginx_conf(self, local=False):
        online_list, offline_list = await asyncio.gather(self.get_online_service(), self.get_offline_service())
        data_info = {"online_list": online_list, "offline_list": offline_list,
                     "domain": self.config.domain_name, "runtime_ingress": self.config.runtime_ingress}
        # model_template = jinja2_env.get_template("k8s_location.nginx")
        # pc_output = await model_template.render_async(**data_info)
        if local:
            self.nginx_conf_dir = self.config.nginx_conf_path
        else:
            self.nginx_conf_dir = "/root"
        # with open(f"{self.nginx_conf_dir}/pc", "w") as target:
        #     target.write(pc_output)
        model_template = jinja2_env.get_template("k8s_location_m.nginx")
        mobile_output = await model_template.render_async(**data_info)
        with open(f"{self.nginx_conf_dir}/mobile", "w") as target:
            target.write(mobile_output)

    async def k8s_online(self):
        await self.gen_k8s_runtime_conf()
        await self.start_runtime()
        await engine.redis.zadd(self.online_key, member=self.app_env)
        await engine.redis.redis.zrem(self.offline_key, member=self.app_env)

    async def k8s_offline(self, app_env=None):
        if app_env is None:
            app_env = self.app_env
        await self.stop_runtime()
        await engine.redis.zadd(self.offline_key, member=self.app_env)
        await engine.redis.redis.zrem(self.online_key, member=self.app_env)

    async def sanbox_offline(self):
        await self.stop_runtime()
        await engine.redis.redis.zrem(self.sandbox_online_key, member=self.app_env)
        await engine.redis.redis.zrem(self.app_sandbox_online_key, member=self.app_env)

    async def manage_k8s_app_status(self, offline=False):
        await self.get_runtime_config()
        if offline:
            await self.k8s_offline()
            self.app_env = self.app_uuid + "test"
            await self.k8s_offline()
            sandbox_online_list = await engine.redis.redis.zrange(self.app_sandbox_online_key)
            for sandbox_service in sandbox_online_list:
                self.app_env = sandbox_service
                await self.sanbox_offline()
            await engine.redis.redis.delete(self.app_sandbox_online_key)
        else:
            await self.k8s_online()
            self.app_env = self.app_uuid + "test"
            await self.k8s_online()
        await self.update_ingress_service()
        await engine.access.update_app(app_uuid=self.app_uuid, status=0 if offline else 1)

    async def scp_nginx_conf(self):
        result = await async_run(f"cd {self.nginx_conf_dir} && scp  pc mobile nginx-server:{os.path.dirname(self.config.nginx_conf_path)}/sites-enabled")
        app_log.info(result)
        result = await async_run("ssh nginx-server \"nginx -s reload \"")
        app_log.info(result)

    @log_step
    @sub_env_ignore()
    # @publish_for_local
    async def update_runtime_info(self):

        # CONFIG = getattr(runtime.config, f"{self.app.config.ENV}Config", runtime.config.DevelopmentConfig)
        import ucenter.config
        uc_config = getattr(
            ucenter.config, f"{self.app.config.ENV}Config", ucenter.config.DevelopmentConfig)
        if self.group_uuid:
            # 发布组 先不需要处理视图信息
            return
        ucenter_db = PooledMySQLDatabase(
            uc_config.MYSQL_DB_LEMON,
            host=uc_config.MYSQL_HOST,
            port=uc_config.MYSQL_PORT,
            user=uc_config.MYSQL_USER,
            password=uc_config.MYSQL_PASSWORD,
            charset=uc_config.MYSQL_DB_CHARSET
        )
        await update_ucenter_info(
            ucenter_db, self.app, self.app_uuid, self.database_name, self.app_revision, self.database,
            self.system_tables, self.env)
        await self.set_default_tenant()

    @log_step
    @sub_env_ignore()
    async def get_default_tenant(self):
        """
        获取默认租户, 这里延迟添加默认租户, 因为添加默认租户依赖之后要创建的role_member视图
        """
        from apps.runtime_entity import AppTenant
        sql = f"""SELECT * FROM lemon_tenant_center.tenant  as `t1`
                INNER JOIN lemon_tenant_center.tenant_member as `t2` on (`t1`.`tenant_uuid` = `t2`.`tenant_uuid`)
                WHERE (
                `t2`.`is_admin` = TRUE and
                `t2`.`is_delete` = FALSE and 
                `t2`.`is_active` = TRUE and
                `t1`.`sys_create` = TRUE and 
                `t1`.`creator` = "{self.real_user_uuid}" and 
                `t2`.`member_uuid` = "{self.real_user_uuid}"
                )
            """
        app_log.info(sql)
        default_tenant_sql = RawQuery(
            sql=sql, params=None, _database=self.database).dicts()
        default_tenant = await self.objs.execute(default_tenant_sql)
        # app_log.info(default_tenant)
        if default_tenant:
            default_tenant = default_tenant[0]
            tenant_uuid = default_tenant.get("tenant_uuid")
            app_tenant_info = await engine.access.get_obj(
                AppTenant, need_delete=True, **{AppTenant.tenant_uuid.name: tenant_uuid,
                                                AppTenant.app_uuid.name: self.app_uuid})
            # app_log.info(app_tenant_info)
            if not app_tenant_info:
                self.default_tenant_uuid = tenant_uuid
                self.default_tenant_credential_no = default_tenant.get("credential_no")

    @log_step
    @sub_env_ignore()
    async def set_default_tenant(self):
        if self.default_tenant_uuid:
            ucenter_server_host = os.getenv("CAS_URL") or "localhost:6500"
            add_tenant_url = "http://" + ucenter_server_host + "/api/index/v1/add_tenant.json"
            app_log.info(f"add_tenant_url: {add_tenant_url}")
            timestamp_now = int(time.time())
            app_env = 1 if self.env == "test" else 0
            request_body = {
                "is_test": True,
                "service_start_at": timestamp_now,
                "service_end_at": timestamp_now + 3600 * 24 * 365 * 3,  # 3年
                "additional": "系统自动创建测试租户",
                "credential_no": self.default_tenant_credential_no,
                "app_uuid": self.app_uuid,
                "creator_as_sys_manager": True,
                "add_default_tenant": True,
                "app_Env": app_env,
                "database_name": self.database_name
            }
            async with ClientSession() as session:
                await session.post(add_tenant_url,
                                    json=request_body,
                                    headers={"Authorization": "Bearer "+self.p_token})

    '''
        复制运行时容器内静态文件
    '''
    @log_step
    async def copy_runtime_static(self):
        runtime_static_path = f"{self.image_workspace}/runtime_static"
        await self.create_dir(runtime_static_path)
        static_path = self.config.runtime_front_path + "/static"
        mobile_static = f"{static_path}/design/mobile/runtime"
        desktop_static = f"{static_path}/design/runtime"
        web_static = f"{self.config.runtime_front_path}/pc/runtime"
        app_static = f"{static_path}/design/runtime_app"
        mobile_static_index = f"{runtime_static_path}/mobile_index.html"
        desktop_static_index = f"{runtime_static_path}/pc_index.html"
        web_static_index = f"{runtime_static_path}/web_index.html"
        app_static_index = f"{runtime_static_path}/app_index.html"
        mobile_static_remote = f"{runtime_static_path}/mobile_remote.js"
        web_static_remote = f"{runtime_static_path}/web_remote.js"
        desktop_static_remote = f"{runtime_static_path}/pc_remote.js"
        result = await async_run(f"cp -r {mobile_static}/index.html {mobile_static_index}")
        app_log.info(result)
        result = await async_run(f"cp -r {desktop_static}/index.html {desktop_static_index}")
        app_log.info(result)
        result = await async_run(f"cp -r {web_static}/index.html {web_static_index}")
        result = await async_run(f"cp -r {app_static}/index.html {app_static_index}")
        result = await async_run(f"cp -r {mobile_static}/remoteEntry.js {mobile_static_remote}")
        app_log.info(result)
        result = await async_run(f"cp -r {web_static}/remoteEntry.js {web_static_remote}")
        app_log.info(result)
        result = await async_run(f"cp -r {desktop_static}/remoteEntry.js {desktop_static_remote}")
        app_log.info(result)
        await self.replace_index_html(f"{mobile_static_index}", title=self.web_title)
        await self.gen_container_nginx("mobile")
        await self.replace_index_html(f"{desktop_static_index}",  "/desktop", title=self.web_title)
        await self.gen_container_nginx("pc")
        await self.replace_index_html(f"{web_static_index}", title=self.web_title)
        await self.replace_index_html(f"{app_static_index}", replace_url=False, title=self.web_title, 
                                      replace_cordova=True)
        # 替换remote.js
        await self.replace_remote_js(f"{mobile_static_remote}")
        await self.replace_remote_js(f"{web_static_remote}")
        await self.replace_remote_js(f"{desktop_static_remote}", "/desktop")
        await self.gen_container_nginx("web")
        await self.gen_container_nginx("app")
        await self.gen_nginx_upstream()

    """生成运行时页面自定义样式文件"""
    async def create_page_style(self):
        if not self.k8s_support:
            return None
        style_list = await engine.access.list_css_style_by_app_uuid(self.app_uuid)
        pc_style_list = []
        mobile_style_list = []
        for page_style_info in style_list:
            page_uuid = page_style_info.get("page_uuid")
            page_styles = page_style_info.get("pageStyles", {})
            pc_style = page_styles.get("pc")
            mobile_sytle = page_styles.get("mobile")
            if pc_style:
                pc_style_list.append((page_uuid, pc_style))
            if mobile_sytle:
                mobile_style_list.append((page_uuid, mobile_sytle))
        model_template = jinja2_env.get_template("page_style.less")
        pc_style_file = await model_template.render_async(**{"style_list": pc_style_list})
        with open(f"{self.image_workspace}/pc_style.less", "w") as target:
            target.write(pc_style_file)
        mobile_style_file = await model_template.render_async(**{"style_list": mobile_style_list})
        with open(f"{self.image_workspace}/mobile_style.less", "w") as target:
            target.write(mobile_style_file)
        result = await async_run(f"ls {self.image_workspace}")
        app_log.info(result)
        result = await async_run(f"cd {self.image_workspace} && lessc pc_style.less > pc_style.css")
        app_log.info(result)
        result = await async_run(f"cd {self.image_workspace} && lessc mobile_style.less > mobile_style.css")
        app_log.info(result)
        result = await async_run(f"ls {self.image_workspace}")
        app_log.info(result)

    @log_step
    async def create_custom_icon_js(self):
        if not self.k8s_support:
            return None
        svg_tag = await gather_iconfont(self.app_uuid)
        app_log.info('start_create_custom_icon_js')
        js_template = jinja2_env.get_template("customIcon.js")
        constom_icon_file = await js_template.render_async(**{"svg_tag": svg_tag.decode("utf-8")})
        with open(f"{self.custom_static_path}/customIcon_{self.app_revision}.js", "w") as target:
            target.write(constom_icon_file)

    """生成运行时模块主题自定义样式文件"""
    @log_step
    async def create_module_theme_style(self):
        if not self.k8s_support:
            return None
        theme = await engine.access.list_document_by_app_uuid_document_type(
            app_uuid=self.app_uuid, document_type=DocumentType.THEME, as_dict=False)
        theme = list(theme)[0]
        content = await engine.access.get_document_content_by_document_uuid(theme.document_uuid)
        module_theme_selected_uuid = content.document_content.get(
            "theme_uuid", '')
        pc_style_list = []
        mobile_style_list = []
        if module_theme_selected_uuid:
            fields_style_list = await engine.access.list_css_style_by_document_uuid(module_theme_selected_uuid)
            for fields_style_info in fields_style_list:
                field_styles = fields_style_info.get("main_folder")
                for style_info in field_styles:
                    pc_style = field_styles.get(
                        style_info, {}).get("PC", {}).get("style")
                    mobile_sytle = field_styles.get(
                        style_info, {}).get("Mobile", {}).get("style")
                    if pc_style:
                        pc_style_list.append(pc_style)
                    if mobile_sytle:
                        mobile_style_list.append(mobile_sytle)
        model_template = jinja2_env.get_template("module_theme_style.less")
        pc_style_file = await model_template.render_async(**{"style_list": pc_style_list})
        with open(f"{self.image_workspace}/pc_style.less", "w") as target:
            target.write(pc_style_file)
        mobile_style_file = await model_template.render_async(**{"style_list": mobile_style_list})
        with open(f"{self.image_workspace}/mobile_style.less", "w") as target:
            target.write(mobile_style_file)
        result = await async_run(f"cd {self.image_workspace} && lessc pc_style.less > module_theme_pc_style.css")
        app_log.info(result)
        result = await async_run(
            f"cd {self.image_workspace} && lessc mobile_style.less > module_theme_mobile_style.css")
        app_log.info(result)

    """生成运行时页面自定义url文件"""
    async def create_page_url_dict(self):
        url_setting_list = await engine.access.list_page_with_url_by_app_uuid(
            self.app_uuid)
        url_dict = {}
        page_list = []
        for page_url_info in url_setting_list:
            page_uuid = page_url_info.get("page_uuid")
            document_name = page_url_info.get("document_name")
            url_setting = page_url_info.get("custom_path", {}) or {}
            module_name = page_url_info.get("module_name")
            status = url_setting.get("show", False)
            page_url = None
            if status:
                page_url = url_setting.get("path")
                if page_url:
                    url_dict[page_url] = {"page_uuid":  page_uuid, "name": document_name, "module_name": module_name,
                                          "url": page_url}
            if not page_url:
                page_dict = {"page_uuid":  page_uuid, "name": document_name,
                             "module_name": module_name, "url": ""}
                page_list.append(page_dict)
        json_data = ujson.dumps(url_dict)
        page_url_path = f"{self.app_path_resources}/page_url.json"
        async with async_open(page_url_path, "w") as f:
            await f.write(json_data)
        json_data = ujson.dumps(page_list)
        page_list_path = f"{self.app_path_resources}/page_list.json"
        async with async_open(page_list_path, "w") as f:
            await f.write(json_data)

    @log_step
    async def check_extension_requirement(self):
        # extension_package = await engine.access.get_app_extension_package(self.app_uuid)
        extension_package = await engine.access.get_all_extension_package(self.app_uuid)
        app_log.info(f"check_extension_requirement extension_package={extension_package}")
        extension_packages = extension_package.get("extension_packages", {}).get("packages", [])
        decode_context = {"runtime_image": self.config.runtime_image,
                          "image_version": self.config.image_version}
        requirement_template = jinja2_env.get_template("extension_package.txt")
        build_path = f"/tmp/{self.app_env}/check"
        await self.create_dir(build_path)
        requirement_file = await requirement_template.render_async(**{"package_list": extension_packages})
        with open(f"{build_path}/extension_package.txt", "w") as target:
            target.write(requirement_file)
        dockerfile_template = jinja2_env.get_template("extension_dockerfile")
        extension_dockerfile = await dockerfile_template.render_async(**decode_context)
        with open(f"{build_path}/extension_dockerfile", "w") as target:
            target.write(extension_dockerfile)
        cmd = f"cd {build_path} && docker build -f extension_dockerfile -t extension_check:{self.app_uuid} ."
        result = await async_run(cmd)
        await async_run(f"rm -rf {build_path}")
        app_log.info(result)
        if result[0] != 0:
            return "\r\n".join(re.findall(r"ERROR:(.*?)\[", result[1].decode()))
        else:
            return ""

    @log_step
    async def pip_package_in_docker(self, extension_packages):

        package_name = extension_packages.get("name")
        package_version = extension_packages.get("version")
        lemon_env = self.app.config.ENV
        decode_context = {"runtime_image": self.config.runtime_image,
                          "image_version": self.config.image_version,
                          "env": lemon_env, 'package_name': package_name,
                          "package_version": package_version}
        import shutil
        import os

        source_path = "/root/lemon/back/"
        build_path = f"/tmp/{self.app_env}/check"
        await self.create_dir(build_path)
        if not os.path.exists(f"{build_path}/back"):
            destination_path = os.path.join(build_path, 'back')
            # 复制文件
            shutil.copytree(source_path, destination_path)
        dockerfile_template = jinja2_env.get_template("pip_extension_dockerfile")
        # {% comment %} RUN pip install "{{package_name}}=={{package_version}}" --trusted-host ************** --index-url http://**************:30141/root/pypi/+simple/ {% endcomment %}
        extension_dockerfile = await dockerfile_template.render_async(**decode_context)
        with open(f"{build_path}/pip_extension_dockerfile", "w") as target:
            target.write(extension_dockerfile)
        cmd = f"cd {build_path} && docker build -f pip_extension_dockerfile -t extension_check:{self.app_uuid} ."
        result = await async_run(cmd)
        await async_run(f"rm -rf {build_path}")
        app_log.info(result)
        if result[0] != 0:
            return "\r\n".join(re.findall(r"ERROR:(.*?)\[", result[1].decode()))
        else:
            return ""

    async def handle_extension_package(self):
        # extension_package = await engine.access.get_app_extension_package(self.app_uuid)
        extension_package = await engine.access.get_all_extension_package(self.app_uuid)
        app_log.info(f"handle_extension_package extension_package = {extension_package}")
        extension_packages = extension_package.get("extension_packages", {}).get("packages", [])
        sys_packages = extension_package.get("extension_packages", {}).get("sys_Packages", {})
        self.decode_context["extension_packages"] = extension_packages
        requirement_template = jinja2_env.get_template("extension_package.txt")
        requirement_file = await requirement_template.render_async(**{"package_list": extension_packages})
        app_log.info(f"handle_extension_package app_path_resources= {self.app_path_resources}")
        with open(f"{self.app_path_resources}/extension_package.txt", "w") as target:
            target.write(requirement_file)
        namespaces = set()
        for package in extension_packages:
            namespaces |= {namespace.get("name")
                           for namespace in package.get("namespaces", [])}
        for sys_namespaces in sys_packages.values():
            namespaces |= {namespace.get("name")
                           for namespace in sys_namespaces}
        namespaces_data = pickle.dumps(namespaces)
        namespace_path = f"{self.app_path_resources}/extension_namespace"
        async with async_open(namespace_path, "wb") as f:
            await f.write(namespaces_data)

    async def create_app_image_dict(self):
        module_images = await engine.access.list_image_table_by_app_uuid(
            self.app_uuid)
        module_dict = dict()
        for module_info in module_images:
            document_dict = defaultdict(dict)
            image_list = module_info.get("image_list", list())
            module_name = module_info.get("module_name")
            for image_info in image_list:
                document_name = image_info.get("document_name")
                image_name = image_info.get("image_name")
                image_url = image_info.get("url")
                document_dict[document_name][image_name] = {"url": image_url}
            module_dict[module_name] = document_dict
        json_data = ujson.dumps(module_dict)
        image_table_path = f"{self.app_path_resources}/image_table.json"
        async with async_open(image_table_path, "w") as f:
            await f.write(json_data)

    async def create_relationship_dict(self):
        relation_list = await engine.access.list_relationship_by_app_uuid(self.app_uuid)
        relation_dict = {}
        for relation in relation_list:
            r_uuid = relation.get("relationship_uuid")
            extra = relation.get("extra")
            if extra:
                relation_dict[r_uuid] = {"r_uuid": r_uuid, "source_required": extra.get("source_required", False),
                                         "target_required": extra.get("target_required", False),
                                         "source_model": relation.get("source_model"),
                                         "target_model": relation.get("target_model")}
        json_data = ujson.dumps(relation_dict)
        page_url_path = f"{self.app_path_resources}/app_relationship.json"
        async with async_open(page_url_path, "w") as f:
            await f.write(json_data)

    async def replace_index_html(self, file_path, prefix="", title="", replace_url=True, replace_cordova=False):
        # if self.env:
        #     replace_str = "/runtime_t"
        # else:
        replace_str = "/runtime"
        # 本地部署需要通过前缀区分客户端类型
        if self.publish_for_local:
            new_index = prefix + f"/{self.app_env}"
        else:
            new_index = f"/{self.app_env}"
        with open(file_path, 'r') as f:
            content = f.read()
            if title:
                content = content.replace('<title>柠檬</title>', f'<title>{title}</title>')
            if replace_url:
                content = content.replace(replace_str, new_index)
            if self.web_welcome_text:
                content = content.replace(
                    'a30604281da156eda7c58211c5763073', self.web_welcome_text)
            # if replace_cordova:
            #     content = content.replace('<noscript>cordova</noscript>', '<script src="./android_public/cordova.js"></script>')
            #     content = content.replace('<noscript>cordova_index</noscript>', '<script src="./android_public/cordova_index.js"></script>')
            content = content.replace('customIcon', f'customIcon_{self.app_revision}')
            content = content.replace('page_style.css?t=', f'page_style.css?t={self.app_revision}_')
            content = content.replace('module_theme_pc_style.css?t=', f'module_theme_pc_style.css?t={self.app_revision}_')
            content = content.replace('module_theme_mobile_style.css?t=', f'module_theme_mobile_style.css?t={self.app_revision}_')
        with open(file_path, 'w') as f1:
            f1.write(content)

    async def replace_remote_js(self, file_path, prefix=""):
        replace_str = "/runtime/"
        # 本地部署需要通过前缀区分客户端类型
        if self.publish_for_local:
            new_index = prefix + f"/{self.app_env}/"
        else:
            new_index = f"/{self.app_env}/"
        with open(file_path, 'r') as f:
            content = f.read()
            content = content.replace(replace_str, new_index)
        with open(file_path, 'w') as f1:
            f1.write(content)

    async def replace_app_index(self, file_path, api_path):
        replace_str = "/remotePrefix"
        index_path = f"{file_path}/index.html"
        with open(index_path, 'r') as f:
            content = f.read()
            content = content.replace(replace_str, api_path)
        with open(index_path, 'w') as f1:
            f1.write(content)

    async def gen_container_nginx(self, client_type):
        if self.sandbox_uuid:
            ingress_domain = self.sandbox_ingress_domain
        else:
            ingress_domain = self.config.ingress_domain
        proxy_connect_timeout = self.decode_context.get("proxy_connect_timeout") or LocalConfig.PROXY_CONNECT_TIMEOUT
        proxy_read_timeout = self.decode_context.get("proxy_read_timeout") or LocalConfig.PROXY_READ_TIMEOUT
        proxy_send_timeout = self.decode_context.get("proxy_send_timeout") or LocalConfig.PROXY_SEND_TIMEOUT
        send_timeout = self.decode_context.get("send_timeout") or LocalConfig.SEND_TIMEOUT
        decode_context = {"client_type": client_type, "app_env": self.app_env,
                          "local_deploy": self.publish_for_local, "ingress_domain": ingress_domain,
                          "worker_processes": self.worker_processes,
                          "nginx_upstream": self.decode_context["nginx_upstream"],
                          "vfs_upstream": self.decode_context["vfs_upstream"],
                          "proxy_connect_timeout": proxy_connect_timeout,
                          "proxy_read_timeout": proxy_read_timeout,
                          "proxy_send_timeout": proxy_send_timeout,
                          "send_timeout": send_timeout
                          }
        model_template = jinja2_env.get_template("runtime_container_nginx")
        output = await model_template.render_async(**decode_context)
        conf_path = f"{self.image_workspace}/{client_type}"
        with open(conf_path, "w") as target:
            target.write(output)
        if self.publish_for_local and client_type in ["pc", "web"]:
            await async_run(f" cp -ra {conf_path} {self.local_deploy_path}/nginx")

    async def gen_nginx_upstream(self):
        model_template = jinja2_env.get_template("runtime_nginx_upstream")
        output = await model_template.render_async(**self.decode_context)
        conf_path = f"{self.image_workspace}/nginx_upstream"
        with open(conf_path, "w") as target:
            target.write(output)

    async def create_log_template(self, log_type):
        app_env = self.env or "pro"
        index_list = [self.app_uuid, "runtime", app_env, log_type]
        index_name = "-".join(index_list)
        elastic = AsyncElastic()
        elastic.init_app(self.app)
        resp = await elastic.client.indices.exists_index_template(name=index_name)
        if not resp.body:
            index_patterns = f"{index_name}-*"
            template = {
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 1,
                    "index.lifecycle.name": "delete-runtme-expired-log-index",
                    "index.lifecycle.rollover_alias": index_name
                }
            }

            resp = await elastic.client.indices.put_index_template(name=index_name,
                                                                   index_patterns=index_patterns,
                                                                   template=template,
                                                                   priority=500)
        resp = await elastic.client.cat.indices(index=f"{index_name}-*", format="json")
        # app_log.info(resp)
        if not resp.body:
            aliases = {
                index_name: {
                    "is_write_index": True
                }
            }
            resp = await elastic.client.indices.create(index=index_name+"-00001", aliases=aliases)
            # app_log.info("test")
        await elastic.close()

    @log_step
    async def create_elastic_template(self):
        type_list = ["db", "triggle", "func", "workflow", "system"]
        # func_list = [self.create_log_template(log_type) for log_type in type_list]
        # await asyncio.gather(*func_list)

    @log_step
    async def delete_runtime_log(self):
        try:
            if self.env == "test":
                elastic = AsyncElastic()
                elastic.init_app(self.app)
                delete_type_list = ["db"]
                for delete_type in delete_type_list:
                    index_list = [self.app_uuid,
                                  "runtime", "test", delete_type]
                    index_name = "-".join(index_list)
                    resp = await elastic.client.cat.indices(index=f"{index_name}-*", format="json")
                    app_log.info('es delete index')
                    app_log.info(resp.body)
                    for index_info in resp.body:
                        index_name = index_info.get("index")
                        await elastic.delete_index(index_name)
                    await self.create_log_template(delete_type)
                    # app_log.info(resp)
                    # await elastic.delete_index(index_name)
                await elastic.close()
        except:
            app_log.info(traceback.format_exc())

    @property
    def image_name(self):
        if self.publish_for_local:
            image_name = f"{self.config.runtime_image}-local:{self.app_env}"
        else:
            image_name = f"{self.config.runtime_image}:{self.app_env}"
        return image_name

    @property
    def secret_image_name(self):
        return f"{self.config.runtime_image}:{self.deploy_secret}"

    @log_step
    async def on_done(self):
        front_path = os.path.dirname(project_base_path) + "/client/runtime"
        # await async_run(f"cd {front_path} && git checkout {front_path} && git clean -fd {front_path} && git checkout {os.path.dirname(project_base_path)}/client/lemonCvd/")
        await self.update_publish_info(self.app_uuid, "")
        await self.record_publish_info()
        await self.update_sube_env_info()
        # 在更新完版本发布时间后再导出本地部署所需信息
        await self.export_runtime_data()
        await self.build_deploy_workspace()
        if self.k8s_support:
            if not self.agent_deploy:
                result = await async_run(f"docker tag  {self.image_name}base {self.image_name}")
                result = await async_run(f"docker push {self.image_name}")
                app_log.info(result)
            if self.publish_for_local:
                result = await async_run(f"docker push {self.image_name}base ")
                await self.gen_local_deploy_runtime_dockerfile()
                self.deploy_image_uuid
                result = await async_run(f"cd {self.image_workspace} && docker build -f LocalDockerfile -t  {self.config.deploy_image}/{self.app_env}:{self.app_revision} .")
                app_log.info(result)
                result = await async_run(f"docker push {self.config.deploy_image}/{self.app_env}:{self.app_revision}")
                app_log.info(result)
                # 更新config文件
                result = await async_run(f"cd {self.image_workspace} && docker build  -f UpateDockerfile -t  {self.image_name} .")
                app_log.info(result)
                result = await async_run(f"docker push {self.config.runtime_image}:{self.app_env}")
                result = await async_run(f"cd  {self.workspace_path} && zip -r {self.app_env}.zip local_deploy")
                app_log.info(result)
                result = await self.create_dir(self.deploy_static_path)
                result = await async_run(f"cp {self.workspace_path}/{self.app_env}.zip {self.deploy_static_path}")
                app_log.info(result)
            if not self.agent_deploy:
                result = await async_run("cd /root && kubectl apply -f runtime.yaml")
                app_log.info(result)
            else:
                app_status = await self.deploy_by_agent()
                #  当无法使用agent更新时尝试通过更新镜像的方法更新，以获取最新agent代码，防止死循环
                if app_status.get("status") != DeployStatus.INIT.value:
                    app_log.info(f"agent deploy fail: {app_status}")
                    result = await async_run(f"cd /root && mv runtime.yaml {self.image_workspace}")
                    cmd1 = f"source {self.image_workspace}/tagImage.sh"
                    cmd2 = f"kubectl apply -f {self.image_workspace}/runtime.yaml"
                    cmd3 = f"rm -rf {self.image_workspace} && rm -rf {self.image_workspace}.tar.gz"
                    cmd = "&&".join([cmd1, cmd2, cmd3])
                    parms = {"command": cmd}
                    await self.start_update_image(parms)
                    await asyncio.sleep(20)
                result = [0]
            if result[0] == 0:
                self.k8s_start = True
                if self.sandbox_uuid:
                    service_list = await engine.redis.redis.zrange(self.sandbox_online_key)
                    if not self.app_env in service_list:
                        await engine.redis.zadd(self.sandbox_online_key, member=self.app_env)
                        await engine.redis.zadd(self.app_sandbox_online_key, member=self.app_env)
                        await self.update_ingress_service(self.env)
                else:
                    service_list = await engine.redis.redis.zrange(self.online_key)
                    if not self.app_env in service_list:
                        await engine.redis.zadd(self.online_key, member=self.app_env)
                        await self.update_ingress_service(self.env)
        else:
            await async_run(f"ln -s {self.config.nginx_conf_path}/apps {os.path.dirname(self.config.nginx_conf_path)}/sites-enabled/apps")
            await async_run("supervisorctl update")
            await async_run("nginx -s reload")
        await self.del_old_runtime_sys_table()
        asyncio.create_task(self.delete_runtime_log())

        await self.db.close_async()
        await engine.redis.delete_form_stage_keys(
            app_env_name=self.env, key=self.app_uuid)
        try:
            for path in os.listdir(self.middle_user_path):
                app_log.debug(path)
                if not path.startswith(self.app_name):
                    continue
                if "_" not in path:
                    continue
                path_revision = path.split("_")[-1]
                if not path_revision.isdigit():
                    continue
                path_revision = int(path_revision)
                if path_revision > int(self.app_revision) - 2:
                    continue
                path = self.middle_user_path + "/" + path
                app_log.debug(path)
                if os.path.isdir(path):
                    await async_run(f"rm {path} -rf")
            app_front_path = f"{self.config.runtime_front_path}/\
                {self.version_dir_name}/{self.app_env}"
            for path in os.listdir(app_front_path):
                if path.startswith(self.last_version) or\
                    path.startswith(self.app_version) or \
                        path in ["pc", "mobile"]:
                    continue
                path = f"{app_front_path}/{path}"
                app_log.debug(path)
                if os.path.isdir(path):
                    await async_run(f"rm {path} -rf")
        except Exception as e:
            app_log.error(e)

    @log_step
    async def update_publish_info(self, app_uuid, tenant_uuid):
        data = dict(
            app_uuid=app_uuid,
            tenant_uuid=tenant_uuid,
            deployment={"server_port": self.decode_context["server_port"],
                        "app_path": self.decode_context["app_path"],
                        "front_version": self.front_version,
                        }
        )
        query = APPExtension.select().where(
            APPExtension.app_uuid == app_uuid,
            APPExtension.tenant_uuid == tenant_uuid)
        app_ext = await engine.db.objs.execute(query)
        if app_ext:
            query = APPExtension.update(
                deployment=data['deployment'],
                is_delete=False).where(
                    APPExtension.app_uuid == app_uuid,
                    APPExtension.tenant_uuid == tenant_uuid)
            await engine.db.objs.execute(query)
        else:
            query = APPExtension.insert(**data)
            await engine.db.objs.execute(query)

    @log_step
    async def on_ext_done(self):
        await self.update_publish_info(self.app_uuid, self.ext_tenant)
        await async_run("supervisorctl update")
        await async_run("nginx -s reload")
        await self.db.close_async()

    @log_step
    async def gen_wx_prog_qrcode(self):
        env = self.env or "pro"
        app_log.info("-=-=-=-=--=------------------------")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": self.app_uuid})
        if not wx_app:
            return
        appid = wx_app.appid
        code_version = await engine.access.get_applet_code_version(environment=env)
        m_domain_name = os.getenv("PublishDomainName") or self.config.m_domain_name
        ext_json_path = self.config.ext_json_path
        app_log.info(f"gen_wx_prog_qrcode env {env} m_domain_name {m_domain_name}")
        # 打开并读取json文件
        with open(ext_json_path, 'r') as f:
            ext_json = ujson.load(f)
        ext_json.update({"extAppid": appid, "ext": {"name": self.app_name, "domain_name": m_domain_name}})
        try:
            result = await engine.wx_api.app_post(
                appid=appid, url="https://api.weixin.qq.com/wxa/commit",
                body={
                    "template_id": code_version.template_id, "ext_json":  ujson.dumps(ext_json),
                    "user_version": code_version.version, "user_desc": code_version.description
                })
            app_log.info(f"gen_wx_prog_qrcode result {result}")
        except:
            app_log.error(traceback.format_exc())
        wxacode_path = engine.config.STATIC_PATH_PREFIX + \
            "/image_list/miniprog/" + self.app_uuid
        wxacode_file = wxacode_path + "/" + appid + ".jpeg"
        wxacode_url = f"https://{m_domain_name}/static/design/image_list/miniprog/{self.app_uuid}/{appid}.jpeg"
        if os.path.exists(wxacode_file):
            self.decode_context["mini_prog_qr_url"] = wxacode_url
            app_log.info(self.decode_context["mini_prog_qr_url"])
            return
        try:
            qrdata = await gen_mini_prog_qrcode(appid, self.app.config.ENV)
        except:
            app_log.error(traceback.format_exc())
        else:
            # app_log.info(qrdata)
            if qrdata is not None:
                image_obj = Image.open(BytesIO(qrdata))
                if not os.path.exists(wxacode_path):
                    os.makedirs(wxacode_path)
                    os.chown(wxacode_path, 65534, 65534)
                image_obj.save(wxacode_file)
                self.decode_context["mini_prog_qr_url"] = wxacode_url
                app_log.info(wxacode_url)

    @log_step
    async def backup_document_content(self):
        # return None
        current_workspace = get_current_workspace()
        user_uuid = ""
        branch_uuid = ""
        if current_workspace:
            user_uuid = current_workspace.user_uuid
            branch_uuid = current_workspace.branch_uuid
        # async with engine.access.with_workspace(None):
        #     branchs = await engine.access.list_app_branch(self.app_uuid)
        # if not branchs:
        #     return
        # if self.app_entity and self.app_entity.version_control and not user_uuid:
        #     # 开启版本控制后，暂时只backup开发环境
        #     return
        model = Document
        c_model = DocumentContent
        l_model = Localhistory
        f_app_uuid = SQL(f"'{self.app_uuid}' as app_uuid")
        f_timestamp = SQL(f"{int(time.time())} as timestamp")
        f_user_uuid = SQL(f"'{user_uuid}' as user_uuid")
        f_branch_uuid = SQL(f"'{branch_uuid}' as branch_uuid")
        fields = (
            model.document_uuid,
            c_model.document_content,
            f_timestamp,
            f_user_uuid,
            f_branch_uuid,
            f_app_uuid,
        )
        l_query = l_model.select(fn.MAX(l_model.timestamp).alias('timestamp'), l_model.document_uuid, l_model.document_content, l_model.user_uuid, l_model.branch_uuid).where(
            l_model.user_uuid == user_uuid, l_model.branch_uuid == branch_uuid, l_model.app_uuid == self.app_uuid
        ).group_by(l_model.document_uuid, l_model.user_uuid, l_model.branch_uuid)
        query = model.select(*fields).join(c_model, join_type=JOIN.INNER,
                                           on=((model.document_uuid == c_model.document_uuid) & (
                                               model.user_uuid == c_model.user_uuid) & (model.branch_uuid == c_model.branch_uuid))
                                           ).join(l_query, join_type=JOIN.LEFT_OUTER,
                                                  on=((model.document_uuid == l_query.c.document_uuid) & (
                                                      model.user_uuid == l_query.c.user_uuid) & (model.branch_uuid == l_query.c.branch_uuid))
                                                  ).where((model.app_uuid == self.app_uuid) & (model.user_uuid == user_uuid) & (model.branch_uuid == branch_uuid) & (
                                                      # SQL('1=1')
                                                      (c_model.document_content != l_query.c.document_content) | (
                                                          Expression(l_query.c.document_content, OP.IS, None))
                                                  ))
        app_log.info(f"backup_query {query}")
        document_list = await engine.access.list_obj(model, query)
        # app_log.info(f"backup doc {list(document_list)}")
        model = Localhistory
        if document_list:
            async with engine.access.with_workspace(None):
                await engine.access.insert_many_obj(model, document_list, on_conflict_replace=True)

        # 删除多余的history
        history_count = fn.COUNT(model.id).alias("history_count")
        history_pks = fn.CONCAT('[', fn.GROUP_CONCAT(NodeList((model.id, SQL(
            "SEPARATOR ','")))), ']').python_value(lambda x: ujson.loads(x) if x else [])
        fields = (
            history_count,
            history_pks.alias("history_pks")
        )
        # ? 可能需要先用子query order by一下
        query = model.select(*fields).where(model.app_uuid == self.app_uuid
                                            ).group_by(model.branch_uuid, model.user_uuid, model.document_uuid
                                                       ).having(history_count > 10)
        app_log.info(f"filter to delete query {query}")
        documents = await engine.access.list_obj(model, query)
        to_delete = [pk for doc in documents for pk in doc["history_pks"][:(
            len(doc["history_pks"]) - 10)]]
        query = model.delete().where(model.id.in_(to_delete))
        async with engine.access.with_workspace(None):
            await engine.db.objs.execute(query)

    @log_step
    async def stop_runtime(self):
        app_log.info("stop")
        if not self.k8s_support:
            await async_run(f"supervisorctl stop {self.app_env}")
        else:
            await self.gen_k8s_runtime_conf()
            if self.agent_deploy:
                await self.stop_by_agent()
            else:
                result = await async_run("cd /root && kubectl delete -f runtime.yaml")
                app_log.info(result)
            env_box_list = await engine.access.list_sandbox_by_subenv(self.app_uuid, self.env or "pro")
            for sand_box in env_box_list:
                result = await async_run(f"kubectl delete -n {self.config.publish_namespace} deployment {self.app_uuid}sandbox{sand_box.id}")
                app_log.info(result)

    @log_step
    async def start_runtime(self):
        app_log.info("start")
        if not self.k8s_support:
            if self.success:
                await self.incr_step()
                self.status = 2
                await asyncio.sleep(self.success_wait)
            await async_run(f"supervisorctl start {self.app_env}")
        else:
            if not self.k8s_start:
                result = await async_run("cd /root && kubectl apply -f runtime.yaml")
                app_log.info(result)
            max_num = 120
            while max_num > 0:
                alive = await self.check_app_alive()
                if alive:
                    if self.success:
                        await self.incr_step()
                        self.status = 2
                    app_log.info("container start")
                    break
                max_num -= 1
                await asyncio.sleep(0.5)
            if max_num <= 0:
                self.status = 3
                app_log.info("container status error")

    @log_step
    async def deploy_by_agent(self):
        json_body = {"secret": self.deploy_secret, "app_env": self.app_env}
        url_info = self.get_app_url(self.app_env)
        url = url_info.get("url") + "api/agent/v1/deploy_app.json"
        try:
            async with ClientSession(trust_env=True) as session:
                app_log.info(url)
                result = await session.post(url, json=json_body)
                result_text = await result.text()
                app_log.info(result_text)
                result_json = await result.json()
                app_log.info(result_json)
                success = result_json.get("success")
                if success:
                    app_status = result_json.get("data")
                else:
                    app_status = {}
        except:
            app_log.error(traceback.format_exc())
            app_status = {}
        return app_status

    async def stop_by_agent(self):
        json_body = {"secret": self.deploy_secret}
        url_info = self.get_app_url(self.app_env)
        url = url_info.get("url") + "api/agent/v1/stop_app.json"
        try:
            async with ClientSession(trust_env=True) as session:
                app_log.info(url)
                result = await session.post(url, json=json_body)
                result_text = await result.text()
                app_log.info(result_text)
                result_json = await result.json()
                app_log.info(result_json)
                success = result_json.get("success")
                if success:
                    return True
        except:
            app_log.error(traceback.format_exc())
            return False

    async def get_agent_status(self):
        url_info = self.get_app_url(self.app_env)
        url = url_info.get("url") + "api/agent/v1/get_runtime_status.json"
        try:
            async with ClientSession(trust_env=True) as session:
                app_log.info(url)
                result = await session.get(url)
                result_text = await result.text()
                app_log.info(result_text)
                result_json = await result.json()
                return result_json
        except:
            app_log.error(traceback.format_exc())
            return {}

    async def check_app_alive(self):

        json_body = {"app_uuid": self.app_uuid}
        url_info = self.get_app_url(self.app_env)
        url = url_info.get("url") + "api/runtime/v1/check_app_alive.json"
        try:
            async with ClientSession(trust_env=True) as session:
                app_log.info(url)
                result = await session.post(url, json=json_body)
                result_text = await result.text()
                app_log.info(result_text)
                result_json = await result.json()
                app_log.info(result_json)
                success = result_json.get("success")
                if success:
                    return True
        except:
            app_log.error(traceback.format_exc())
            return False

    async def process_publish_error_msg(self, e):

        async def _process_database_error(e, message):
            error_handler = MySQLErrorHandler(e)
            element_uuid, element_type_num = error_handler.handle()
            element_data: list = await engine.access.get_error_message_by_element(
                element_uuid, element_type_num)
            for e in element_data:
                e.update({"element_uuid": element_uuid})
            error_list = error_handler.update_publish_message(
                message, element_data)
            return error_list

        async def _process_sanic_exception(e, message: dict):
            message.update({"error_message": e.args[0].message})
            return [message]

        async def _process_lemon_publish_error(e, message: dict):
            error_handler = LemonErrorHandler(e)
            element_uuid, element_type_num = error_handler.handle()
            element_data: list = await engine.access.get_error_message_by_element(
                element_uuid, element_type_num)
            for e in element_data:
                true_uuid = error_handler.true_element_uuid.get(
                    e["element_uuid"])
                true_name = error_handler.true_element_name.get(
                    e["element_name"])
                if true_name is not None:
                    e.update({
                        "element_uuid": true_uuid,
                        "element_name": true_name
                    })
            error_list = error_handler.update_publish_message(
                message, element_data)
            return error_list

        async def _process_document_check_error(e, message: dict):
            error_handler = DocumentCheckErrorHandler(e)
            error_message_list = error_handler.handle()
            error_list = error_handler.update_publish_message(
                message, error_message_list)
            return error_list

        async def _process_exception(e, error_message):
            # 尝试把无法分辨的错误信息 处理成LemonErrorHandler
            error_list = list()
            exception_obj = BaseErrorHandler.from_exception(e)
            if isinstance(exception_obj, LemonPublishError):
                error_list = await _process_lemon_publish_error(exception_obj, error_message)
            else:
                err_msg = copy(error_message)
                for arg in e.args:
                    if isinstance(arg, str):
                        err_msg["error_message"] += "\n\n" + arg
                error_list.append(err_msg)
            return error_list

        async def update_error_message(e, error_message):
            error_list = list()
            if isinstance(e, LemonPublishError):
                error_list = await _process_lemon_publish_error(e, error_message)
            elif isinstance(e, exceptions.SanicException):
                error_list = await _process_sanic_exception(e, error_message)
            elif isinstance(e, DatabaseError):
                error_list = await _process_database_error(e, error_message)
            elif isinstance(e, DocumentCheckError):
                error_list = await _process_document_check_error(e, error_message)
            else:
                error_list = await _process_exception(e, error_message)
            return error_list

        async def translate_error_message(error_list):
            # 效率慢了点, 但在异常处理中, 认为效率不太重要
            for error_info in error_list:
                msg = error_info["error_message"]

                # 对于其中出现的每一个uuid, 就当作他可能是 模型 / 字段 / 关联 / 枚举, 直到找到正确的或者没有为止
                all_uuids = set(re.findall(r"[0-9a-f]{32}", msg))
                result_list = []
                if all_uuids:
                    for type_num in [3, 2, 1, 4]:  # 认为模型 / 字段 / 关联 / 枚举的顺序比较合理, 可能能减少查询次数
                        r = await engine.access.get_error_message_by_element(all_uuids, type_num)
                        result_list.extend(r)
                        all_uuids -= {d["element_uuid"] for d in r}
                result_dict = {d["element_uuid"]: d["element_name"] for d in result_list}
                # msg = translate_text(msg)  # XXX 感觉英文翻译了会更看不懂
                error_info["error_message"] = re.sub(
                    r"[0-9a-f]{32}", lambda x: result_dict.get(x.group(0), x.group(0)), msg)

        error_message = {
            "error_code": 100,
            "module_uuid": "",
            "module_name": "",
            "model_uuid": "",
            "element_attr": "应用发布错误",
            "element_name": "",
            "element_uuid": self.app_uuid,
            "document_name": "",
            "document_type": "",
            "document_uuid": "",
            "error_message": "发布报错无法确认,请确保错误列表无误",
            "error_position": ""
        }
        error_list = await update_error_message(e, error_message)
        app_log.error(f"error_info_publish: {error_list}")
        await translate_error_message(error_list)
        await engine.access.update_app_publish_message(app_uuid=self.app_uuid,
                                                       message_info=error_list, app_revision=self.app_revision,
                                                       message_level=2
                                                       )

    async def roll_back_wf_status(self):
        if not self.version_status_update_history:
            return
        wf_contents = await engine.access.list_document_content_by_document_uuid_list(
            list(self.version_status_update_history.keys()))
        for c in wf_contents:
            wf_version_update = self.version_status_update_history.get(c["document_uuid"])
            document_content = c.get(
                DocumentContent.document_content.name, {})
            versions = document_content.get("versions", [])
            for version in versions:
                if version.get("uuid") == wf_version_update.get("new_online_version"):
                    version["status"] = wf_version_update.get("new_online_old_status")
                elif version.get("uuid") == wf_version_update.get("last_online_version"):
                    version["status"] = 2
            update_dict = {
                DocumentContent.document_content.name: document_content
            }
            update_query = DocumentContent.update(**update_dict).where(
                DocumentContent.document_uuid == c["document_uuid"])
            await engine.access.update_obj_by_query(DocumentContent, update_query, need_delete=True)
        self.version_status_update_history.clear()

    # 本地部署工作空间
    @log_step
    @publish_for_local
    async def build_deploy_workspace(self):
        load_template = local_deploy_template.get_template(
            "docker-compose.yml")
        runtime_config = await load_template.render_async(**self.decode_context)
        with open(f"{self.local_deploy_path}/docker-compose.yml", "w") as target:
            target.write(runtime_config)
        load_template = local_deploy_template.get_template("init-compose.yml")

        runtime_config = await load_template.render_async(**self.decode_context)
        with open(f"{self.local_deploy_path}/init-compose.yml", "w") as target:
            target.write(runtime_config)
        local_deploy_path = f"{project_base_path}/apps/services/publish/local_deploy"
        image_build_path = f"{self.image_workspace}"
        # nginx 配置
        web_conf_template = local_deploy_template.get_template("web.conf")
        web_conf = await web_conf_template.render_async(**self.decode_context)
        with open(f"{self.local_deploy_path}/nginx/web.conf", "w") as target:
            target.write(web_conf)
        await async_run(f"cp {local_deploy_path}/nginx.conf {self.local_deploy_path}/nginx/")
        # 运行时配置文件
        local_config_template = local_deploy_template.get_template(
            "local_config.py")
        local_config_py = await local_config_template.render_async(**self.decode_context)
        with open(f"{self.local_deploy_path}/web/local_config.py", "w") as target:
            target.write(local_config_py)
        await async_run(f"cp {local_deploy_path}/supervisor_ucenter.conf {image_build_path}/ucenter.conf")
        build_deploy_path = f"{image_build_path}/local_deploy"
        await self.create_dir(build_deploy_path)
        await async_run(f"cp {local_deploy_path}/new_version.sh {build_deploy_path}/new_version.sh")
        await async_run(f"chmod +x {build_deploy_path}/new_version.sh")
        await async_run(f"cp {self.local_deploy_path}/process_tenant_table.sh {build_deploy_path}/process_tenant_table.sh")
        await async_run(f"chmod +x {build_deploy_path}/process_tenant_table.sh")
        await async_run(f"cp -r {local_deploy_path}/bin {self.local_deploy_path}/bin")
        load_template = local_deploy_template.get_template("config.sh")
        config_file = await load_template.render_async(**self.decode_context)
        with open(f"{self.local_deploy_path}/bin/config.sh", "w") as target:
            target.write(config_file)
        load_template = local_deploy_template.get_template(
            "container_healthy.sh")
        check_file = await load_template.render_async(**self.decode_context)
        with open(f"{self.local_deploy_path}/bin/container_healthy.sh", "w") as target:
            target.write(check_file)
        await async_run(f"cp -r {project_base_path}/apps/ext_config.py {self.image_workspace}/")
        # 本地部署云端配置文件
        load_template = local_deploy_template.get_template(
            "local_config_copy.py")
        replace_config_list = [(replace_key, getattr(self.app.config, replace_key, ""))
                               for replace_key in self.app.config.ENCRYPT_REPLACE_LIST]
        config_file = await load_template.render_async({"replace_config_list": replace_config_list})
        with open(f"{self.image_workspace}/local_config.py", "w") as target:
            target.write(config_file)
        await async_run(f"mv  {self.local_deploy_path}/load_runtime_data.sh {self.local_deploy_path}/bin/load_runtime_data.sh")
        await async_run(f"mv  {self.local_deploy_path}/load_sys_table.sh {self.local_deploy_path}/bin/load_sys_table.sh")
        await async_run(f"mv  {self.local_deploy_path}/update_sys_table.sh {self.local_deploy_path}/bin/update_sys_table.sh")
        await async_run(f"mv  {self.local_deploy_path}/migrate_old_data.sh {self.local_deploy_path}/bin/migrate_old_data.sh")
        await async_run(f"mv  {self.local_deploy_path}/load_view.sh {self.local_deploy_path}/bin/load_view.sh")
        await async_run(f"mv  {self.local_deploy_path}/tenant_table_dump.sh {self.local_deploy_path}/bin/tenant_table_dump.sh")
        await async_run(f"mv  {self.local_deploy_path}/process_tenant_table.sh {self.local_deploy_path}/bin/process_tenant_table.sh")
        await async_run(f"mv  {self.local_deploy_path}/tenant_table_load.sh {self.local_deploy_path}/bin/tenant_table_load.sh")
        await async_run(f"chmod +x {self.local_deploy_path}/bin/*")
        await async_run(f"cp -r {local_deploy_path}/README.MD {self.local_deploy_path}/")
        self.app_icon = ""
        oss_path = f"{self.local_deploy_path}/design_file"
        await self.create_dir(f"{oss_path}/design/icon")
        bucket_url = f"oss://{self.app.config.OSS_BUCKET_NAME}"
        app_icon = self.icon_dict.get("icon")
        app_log.info([self.icon_dict, "icon_info"])
        if app_icon:
            icon_url = f"{bucket_url}/{app_icon}"
            await async_run(f"/root/ossutil64 cp {icon_url} {oss_path}/design/icon/")
        app_logo = self.icon_dict.get("logo")
        if app_logo:
            logo_url = f"{bucket_url}/{app_logo}"
            await async_run(f"/root/ossutil64 cp {logo_url} {oss_path}/design/icon/")
        await async_run(f"/root/ossutil64 cp -r {bucket_url}/design/{self.app_uuid}/ {oss_path}/design/{self.app_uuid}")

    async def clean_workspace(self):
        if self.k8s_support:
            server_path = os.path.dirname(project_base_path) + "/back/apps"
            front_path = os.path.dirname(project_base_path) + "/client/"
            await async_run(f"cd {front_path} && git checkout {front_path} && git clean -fd {front_path} && git checkout {os.path.dirname(project_base_path)}/client/lemonCvd/")
            # await async_run(f"cp -r {self.image_workspace}/ /tmp")
            await async_run(f"git clean -fd {server_path}")
            await async_run(f"rm -rf {os.path.dirname(project_base_path)}/client/lemonCvd/www")
            await async_run(f"rm -r {self.config.runtime_app_path}")
            await async_run(f"rm -rf {self.user_home}/lemon/client/runtime_desktop")
            await async_run(f"rm -rf {self.user_home}/lemon/client/runtime_mobile")
            await async_run(f"rm -rf {self.workspace_path}")
            await async_run(f"chown -R nobody:nogroup {self.runtime_front_path}")
            await async_run(f"chown -R nobody:nogroup {self.runtime_front_path}_m")
            await async_run(f"chown -R nobody:nogroup {self.runtime_front_path}_pc")
            await async_run(f"chown -R nobody:nogroup {self.config.runtime_front_path}/{self.app_env}")
            await async_run(f"chown -R nobody:nogroup {self.deploy_static_path}")
            # await async_run(f"rm -rf /root/lemon/client/runtime_pc")

    async def init_status(self):
        if self.k8s_support:
            # 该文件会在重新制作运行时基础镜像时更新
            file_path = self.config.runtime_front_path + "/images/runtimeImage"
            result = await async_run(f"cat {file_path}")
            if result[0] == 0:
                base_image_id = result[1].decode().strip()
            else:
                base_image_id = lemon_uuid()
            self.decode_context["base_image_id"] = base_image_id
            await self.gen_deploy_secret()
            agent_status = await self.get_agent_status()
            runtime_image_id = agent_status.get(
                "data", {}).get("base_image_id", "")
            app_log.info(
                f"images_uuid_check: {base_image_id}, {runtime_image_id}")
            case1 = (agent_status and runtime_image_id ==
                     base_image_id and not self.publish_for_local)
            self.agent_deploy = True if case1 else False
            self.image_workspace = f"{engine.app.config.IMAGE_WORKSPACE}/{self.app_env}/{self.deploy_secret}"
            app_log.info(f"agent_deploy={self.agent_deploy}, case1={case1}, publish_for_local={self.publish_for_local}")
        else:
            self.agent_deploy = False
            self.image_workspace = f"{engine.app.config.IMAGE_WORKSPACE}/{self.app_env}"
        self.custom_static_path = f"{self.image_workspace}/custom_public"

    async def init_sub_env_info(self):
        if self.sub_env != "None":
            base_info = {
                SubEnv.sandbox_uuid.name: self.sandbox_uuid,
                SubEnv.sub_env.name: self.sub_env
            }
            self.sub_env_info = await engine.access.get_obj(SubEnv, **base_info)

    async def create_dir(self, path):
        # 发布节点有两种挂载方式，一种是nfs，一种是本地目录会导致权限问题
        dir_parts = path.split("/")
        # 构建每一级目录的路径
        current_path = "/"
        app_log.info(f"create_dir {path}")
        for part in dir_parts:
            if part:  # 跳过空字符串（例如路径开头的/）
                current_path = os.path.join(current_path, part)
                # 检查目录是否存在，如果不存在则创建
                if not os.path.exists(current_path):
                    os.makedirs(current_path)
                    # 设置目录权限为777（注意：这通常是不安全的）
                    app_log.info(f"mkdir {current_path}")
                    os.chmod(current_path, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)
        return ""

    async def build_workspace(self):
        if self.k8s_support:
            await self.create_dir(self.workspace_path)
            # await async_run(f"rm -rf {self.image_workspace}/*")
            parent_dir = os.path.dirname(self.image_workspace)
            if not os.path.exists(parent_dir):
                # 不这样的话其它节点的Pod没权限
                await self.create_dir(parent_dir)
            await self.create_dir(self.image_workspace)
            await self.create_dir(self.custom_static_path)
            # 构建本地部署目录
            if self.publish_for_local:
                await self.create_dir(self.local_deploy_path)
                pdf_dir = "/nginx/www/html/static/runtime/pdf"
                await self.create_dir(f"{self.local_deploy_path}{pdf_dir}")
                await self.create_dir(f"{self.local_deploy_path}/images")
                await self.create_dir(f"{self.local_deploy_path}/web")
                custom_dir = f"{self.local_deploy_path}/nginx/www/html/custom"
                await self.create_dir(custom_dir)
                await async_run(f"cp -ra {engine.config.CUSTOM_STATIC_PATH_PREFIX}/{self.app_uuid} {custom_dir}")
                pdfjs_dir = f"{self.local_deploy_path}/nginx/www/html/static/runtime/"
                await async_run(f"cp -ra {engine.config.RUNTIME_STATIC_PATH_PREFIX}/pdfjs {pdfjs_dir}")

    def get_sys_tables(self):
        self.system_tables = sys_table_handler.make_sys_from_user_model(
            self.app_uuid, self.app_revision)

    async def run_steps(self):
        app_log.info("============start build app=========")
        start_time = time.time()
        # await async_run(f"cd {front_path} && git checkout {front_path} && git clean -fd {front_path} && git checkout {os.path.dirname(project_base_path)}/client/lemonCvd/")
        try:
            await self.init_status()
            await self.init_sub_env_info()
            await self.clean_workspace()
            await self.build_workspace()
            workspce_info = self.workspace_info
            app_log.info(f"{workspce_info=}")
            set_current_workspace(
                user_uuid=workspce_info.get("user_uuid"),
                branch_uuid=workspce_info.get("branch_uuid"),
                app_uuid=workspce_info.get("app_uuid", self.app_uuid))
            # self.step += 1
            await self.get_app_local_config()
            await self.incr_step()
            # await self.get_runtime_image_id()
            await self.get_runtime_config()
            await self.get_app_version()
            await self.stop_runtime()

            # await self.check()
            # self.step += 1
            # await self.incr_step()
            # # 创建运行时数据库,异步Manager
            # await self.handle_runtime_database()
            # await self.copy_pro_data_to_test()
            # # 代码拷贝到新文件夹
            # await self.copy_source_code()
            # # self.step += 1
            # 写表实体
            # await self.gen_models()
            await self.create_custom_icon_js()
            await self.create_page_style()
            await self.create_module_theme_style()
            await asyncio.gather(self.build_front_step(), self.gen_back_step())
            # 因为前端和后端发布并行执行的，所以结束后要加两步
            await self.incr_step()
            await self.incr_step()
            # await self.gen_conf()
            await self.on_done()
            # self.step += 1
        except Exception as e:
            app_log.info(f"eeeee: {e}")
            app_log.error(traceback.format_exc())
            self.status = 3
            # await self.update_publish_status()
            await self.roll_back_wf_status()
            await self.process_publish_error_msg(e)
            # if self.database:
            #     with self.database.allow_sync():
            #         self.recover_on_fail()
            await asyncio.sleep(15)
        else:
            await self.run_publish_script()
            error_message = []
            await engine.access.update_app_publish_message(
                app_uuid=self.app_uuid, message_info=error_message,
                app_revision=self.app_revision, message_level=2)
            await engine.access.update_app_revision(self.app_uuid, self.app_revision, env=self.env)
            self.success = True
            # await self.update_publish_status()
            app_log.info(
                f"============build app {self.app_name}:{self.app_revision} done in {time.time()-start_time}s=========")
            # del self._instance[self.app_uuid]
        finally:
            await self.start_runtime()
            await self.update_publish_status()
            await self.clean_workspace()
            await async_run(f"rm {self.app_path}/p.cnf")
            await async_run(f"rm {self.app_path}/tenanttable.sql")
            await async_run(f"rm {self.app_path}/tenanttable_backup.sql")
            app_log.info(f"timestep_list: {const_time_list}")
            const_time_list.clear()
            self.step = 0

            DesignAppMetrics.collect_app_publish(
                self.success, self.storage, self.publish_pc, self.publish_app, self.env,
                self.async_db, time.time() - start_time
            )

            delattr(self, "status")
            await asyncio.sleep(1)
            if self.agent_deploy and self.success:
                cmd = f"rm -rf {self.image_workspace}.tar.gz"
                parms = {
                    "command": f"source {self.image_workspace}/tagImage.sh && rm -rf {self.image_workspace} && {cmd}"}
                await self.start_update_image(parms)
            elif self.k8s_support:
                parms = {
                    "command": f"rm -rf {self.image_workspace}"}
                await self.start_update_image(parms)
            PublishQueue.clear_in_publish(self)
            PublishQueue.publish_one()
            if hasattr(self, "run_app"):
                self.run_app.stop()
            # if os.path.exists(self.build_front_path):
            #      await async_run(f"rm -r {self.build_front_path}")

    async def record_publish_info(self):
        environment = self.env or "pro"
        base_info = {
            APPPublish.app_uuid.name: self.app_uuid,
            APPPublish.environment.name: environment
        }
        publish_info = await engine.access.get_obj(APPPublish, **base_info)
        if self.sub_env == 'None':
            app_revision = self.app_revision
        else:
            if publish_info:
                app_revision = publish_info.app_revision + 1
            else:
                app_revision = 1
        update_info = {
            APPPublish.app_version.name: self.app_version,
            APPPublish.app_revision.name: app_revision,
            APPPublish.version_number.name: self.version_number,
            APPPublish.version_time.name: int(time.time()),
            APPPublish.lemon_version.name: engine.config.LEMON_VERSION
        }
        if publish_info:
            query = APPPublish.update(
                **update_info).where(publish_info._pk_expr())
            await engine.access.update_obj_by_query(APPPublish, query)
        else:
            base_info.update(update_info)
            await engine.access.create_obj(APPPublish, **base_info)

    async def update_sube_env_info(self):
        if self.sub_env == "None":
            if self.sandbox_uuid:
                sandbox_update = Sandbox.update({Sandbox.sub_env.name: ""}).where(
                    Sandbox.sandbox_uuid == self.sandbox_uuid)
                await engine.access.update_obj_by_query(Sandbox, sandbox_update)
            return None
        base_info = {
            SubEnv.sandbox_uuid.name: self.sandbox_uuid,
            SubEnv.sub_env.name: self.sub_env
        }
        update_info = {
            SubEnv.version_time.name: int(time.time()),
        }
        if self.sub_env_info:
            query = SubEnv.update(
                **update_info).where(self.sub_env_info._pk_expr())
            await engine.access.update_obj_by_query(SubEnv, query)
        else:
            base_info.update(update_info)
            await engine.access.create_obj(SubEnv, **base_info)
        sandbox_update = Sandbox.update({Sandbox.sub_env.name: self.sub_env}).where(
            Sandbox.sandbox_uuid == self.sandbox_uuid)
        await engine.access.update_obj_by_query(Sandbox, sandbox_update)

    def recover_on_fail(self):
        """发布失败后, 尝试恢复原系统表关联"""
        tables = self.table_dict.values()
        if tables:
            to_drop = list()
            runtime_sys_table_add_names = {
                t._meta.table_name for t in runtime_sys_table_add}
            for table in tables:
                if getattr(table._meta, "meta_table_name", None) in runtime_sys_table_add_names:
                    continue
                to_drop.append(table)
            foreign_keys_by_table = get_foreign_keys_by_table(
                self.db, extra_condition_expr=None)
            not_changed_tables = {
                table._meta.table_name: table for table in set(tables) - set(to_drop)}
            to_drop_tables_dict = {
                table._meta.table_name: table for table in to_drop}
            migrator = auto_detect_migrator(self.db)
            alter_statements = []
            for table_name, fks in foreign_keys_by_table.items():
                for fk in fks:
                    if fk.table in not_changed_tables and fk.dest_table in to_drop_tables_dict:
                        alter_statements += drop_foreign_key(
                            self.db, migrator, table_name, fk.name)
                        alter_statements += self.make_fk_constraint_sql(fk)
            if alter_statements:
                _execute_(self.db, alter_statements)

    def make_fk_constraint_sql(self, fk):
        last_target_table_name = self.make_last_runtime_table_name(
            fk.dest_table)
        sql = f"""
            ALTER TABLE `{fk.table}` ADD CONSTRAINT 
            `{fk.name}` FOREIGN KEY (`{fk.column}`) REFERENCES 
            `{last_target_table_name}` (`id`) ON DELETE {fk.delete_rule}
            """
        return [(sql, [])]

    async def build_front_step(self):
        # 常量和枚举类写入前端
        if self.k8s_support:
            await self.gen_json()
        if self.build_front:
            if engine.app.config.ENV != "Development" and self.build_type not in [-1, -2]:
                app_log.info(f'---------------{self.build_type}')
                await self.gen_front()

    """所有操作运行时back目录文件的操作"""
    @log_step
    async def create_back_dir(self):
        # 代码拷贝到新文件夹
        await self.copy_source_code()
        const_dict, enum_dict, func_dict, py_modules_dict, workflows_dict = await asyncio.gather(
            self.gen_const(),
            self.gen_enum(),
            self.gen_func(),
            self.gen_py_modules(),
            self.gen_workflows()
        )
        
        await self.gen_models()
        _, _, _, _, _ = await asyncio.gather(
            self.gen_app_roles(),
            self.create_page_url_dict(),
            self.create_relationship_dict(),
            self.create_app_image_dict(),
            self.handle_extension_package()
        )
        module_list = list(const_dict.keys()) + \
            list(enum_dict.keys()) + list(func_dict.keys()) + \
            list(workflows_dict.keys())
        for module in set(module_list):
            module_dict = {}
            module_dict["funcs"] = func_dict.get(module, {})
            module_dict["enums"] = enum_dict.get(module, {})
            module_dict["consts"] = const_dict.get(module, {})
            module_dict["workflows"] = workflows_dict.get(module, {})
            module_dict["py_modules"] = py_modules_dict.get(module, {})
            self.decode_context["modules"][module] = module_dict
        await self.gen_back_front_const()
        await self.gen_uuid_map()

    async def start_update_image(self, parms):
        url = engine.config.UPDATE_IMAGE_URL + \
            "?" + urllib.parse.urlencode(parms)
        app_log.info(url)
        async with ClientSession(trust_env=True) as session:
            result = await session.post(url)
            app_log.info(result)

    @log_step
    async def build_runtime_image(self):
        await self.create_back_dir()
        if self.k8s_support:
            result = await async_run(f"cp -r {self.app_path}/back {self.image_workspace}")
            app_log.info(result)
            result = await async_run(f"ls {self.image_workspace}")
            app_log.info(result)
            if not os.path.exists(f"{self.image_workspace}/pc_style.css"):
                raise Exception()
            await self.copy_runtime_static()
            await self.gen_runtime_dockerfile()
            await self.gen_k8s_supervisor_conf()
            if self.agent_deploy:
                parms = {
                    "command": f"cd {self.image_workspace} && docker build  -t  {self.secret_image_name} ."}
                await self.start_update_image(parms)
                cmd = f"cd {self.image_workspace} && tar -czvf {self.image_workspace}.tar.gz ./"
                result = await async_run(cmd)
                app_log.info(result)
            else:
                result = await async_run(f"cd {self.image_workspace} && docker build  -t  {self.config.runtime_image}:{self.app_env}base .")
                app_log.info(result)
                if result[0] != 0:
                    result = await async_run(f"ls {self.image_workspace}")
                    raise Exception(f"build_image failed, {result}")

    @log_step
    async def gen_back_step(self):
        # 创建运行时数据库,异步Manager
        self.app.add_task(self.check())
        await self.incr_step()  # 先检查再拷贝数据，防止开启版本控制后，测试环境没有check_commit导致发布完后缺数据
        await self.handle_runtime_database()
        await self.copy_pro_data_to_test()
        self.gen_decode_context()
        # await self.copy_runtime_data()
        await self.get_default_tenant()
        await asyncio.gather(self.process_workflow_status(),
                             self.build_runtime_image(),
                             #  self.backup_document_content(),
                             self.create_elastic_template(),
                             self.tag_back_image()
                             )
        # await self.copy_runtime_data_relationships()  # TODO
        await self.gen_wx_prog_qrcode()
        await self.update_runtime_info()

    @property
    def publish_state(self):
        state = {"status": self.status, "step": self.step}
        if self.status == 2:
            if self.publish_for_local:
                url_info = self.get_local_deploy_url(self.app_env)
                app_log.info(f"publish_state_url_info={url_info}")
            else:
                url_info = self.get_app_url(self.app_env)
            state.update(url_info)
        app_log.info(f"publish_state_state={state}")
        return state

    def get_app_name(self):
        if self.env == 'test':
            app_name = self.app_name + "(测试环境)"
        elif 'sandbox' in self.env:
            app_name = self.app_name + "(开发环境)"
        else:
            app_name = self.app_name
        return app_name

    def handle_env_name(self, name):
        if self.env == 'test':
            name = name + "(测试环境)"
        elif 'sandbox' in self.env:
            name = name + "(开发环境)"
        else:
            pass
        return name

    def get_app_url(self, app_uuid, appletid=None, url_suffix=""):
        domain_name = os.getenv("PublishDomainName") or self.config.domain_name
        static_domain_name = self.config.static_domain
        desktop_front_path = f"{engine.config.STATIC_PATH_PREFIX}/{self.electron_dir_name}/{app_uuid}"
        yaml_path = f"{desktop_front_path}/latest.yml"
        apk_file = f"{engine.config.STATIC_PATH_PREFIX}/{self.static_dir_name}/{app_uuid}/{app_uuid}.apk"
        local_deploy_path = f"{engine.config.STATIC_PATH_PREFIX}/local_deploy/{app_uuid}"
        wxacode_url = self.decode_context.get("mini_prog_qr_url", "")
        app_log.info(wxacode_url)
        if not wxacode_url and appletid:
            app_path = app_uuid.replace(self.env, "")
            wxacode_path = engine.config.STATIC_PATH_PREFIX + \
                "/image_list/miniprog/" + app_path
            wxacode_file = wxacode_path + "/" + appletid + ".jpeg"
            if os.path.exists(wxacode_file):
                wxacode_url = f"https://{static_domain_name}/static/design/image_list/miniprog/{app_path}/{appletid}.jpeg"
        if os.path.exists(f"{local_deploy_path}/{app_uuid}.zip"):
            docker_url = f"https://{static_domain_name}/static/design/local_deploy/{app_uuid}/{app_uuid}.zip"
        else:
            docker_url = ""
        if os.path.exists(yaml_path):
            with open(yaml_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                exe_name = data["path"]
                exe_name = urllib.parse.quote(exe_name)
                desktop_url = f"https://{static_domain_name}/static/design/{self.electron_dir_name}/{app_uuid}/{exe_name}"
        else:
            desktop_url = ""
        if os.path.exists(apk_file):
            app_url = f"https://{static_domain_name}/static/design/{self.static_dir_name}/{app_uuid}/{app_uuid}.apk"
        else:
            app_url = ""
        data = {
            # "url": f"http://{self.config.domain_name}:8443/{app_uuid}",
            "url": f"https://{domain_name}:8443/{app_uuid}/{url_suffix}",
            "mobile_url": f"https://m{domain_name}/{app_uuid}/user/login",
            "app_url": app_url,
            "desktop_url": desktop_url,
            "docker_url": docker_url,
            "wxacode_url": wxacode_url
        }
        return data

    def get_local_deploy_url(self, app_uuid):
        static_domain_name = self.config.static_domain
        desktop_front_path = f"{engine.config.STATIC_PATH_PREFIX}/{self.electron_dir_name}/{app_uuid}"
        yaml_path = f"{desktop_front_path}/latest.yml"
        apk_file = f"{engine.config.STATIC_PATH_PREFIX}/{self.static_dir_name}/{app_uuid}/{app_uuid}.apk"
        local_deploy_path = f"{engine.config.STATIC_PATH_PREFIX}/local_deploy/{app_uuid}"
        wxacode_url = self.decode_context.get("mini_prog_qr_url", "")
        if os.path.exists(f"{local_deploy_path}/{app_uuid}.zip"):
            docker_url = f"https://{static_domain_name}/static/design/local_deploy/{app_uuid}/{app_uuid}.zip"
            if os.path.exists(yaml_path):
                with open(yaml_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    exe_name = data["path"]
                    exe_name = urllib.parse.quote(exe_name)
                    desktop_url = f"https://{static_domain_name}/static/design/{self.electron_dir_name}/{app_uuid}/{exe_name}"
            else:
                desktop_url = ""
            if os.path.exists(apk_file):
                app_url = f"https://{static_domain_name}/static/design/{self.static_dir_name}/{app_uuid}/{app_uuid}.apk"
            else:
                app_url = ""
            custom_server_address = self.decode_context.get("custom_server_address", "")
            local_mobile_address = self.decode_context.get("local_mobile_address", "")
            is_use_domain_name = self.decode_context.get("is_use_domain_name", False)
            custom_url = f"{custom_server_address}/{app_uuid}/"
            custom_mobile_url = f"{local_mobile_address}/{app_uuid}/user/login?build_type=2"

            if not is_use_domain_name:
                _url = f"{self.server_address}/{app_uuid}/"
                mobile_url = f"{self.mobile_address}/{app_uuid}/user/login?build_type=2"
                custom_mobile_url = ""
                custom_url = ""
            else:
                mobile_url = f"{custom_server_address}/{app_uuid}/user/login?build_type=2"
                _url = f"{custom_server_address}/{app_uuid}/"
            data = {
                # "url": f"http://{self.config.domain_name}:8443/{app_uuid}",
                "url": _url,
                "custom_url": custom_url,
                "custom_mobile_url": custom_mobile_url,
                "mobile_url": mobile_url,
                "app_url": app_url,
                "desktop_url": desktop_url,
                "docker_url": docker_url,
                "wxacode_url": wxacode_url
            }
            app_log.info(f"get_local_deploy_url_info={data}")
        else:
            docker_url = ""
            data = {}
        return data

    async def get_runtime_url(self, real_user_uuid=None):
        await self.get_app_local_config()
        test_app_uuid = self.app_uuid + "test"
        service_list = await engine.redis.redis.zrange(self.online_key)
        wx_app = await engine.access.get_authorizer_wx(**{
            "app_uuid": self.app_uuid})
        appletid = wx_app.appid if wx_app else None
        pro_data, test_data = {}, {}
        if self.app_uuid in service_list:
            pro_data = self.get_app_url(self.app_uuid, appletid)
        if test_app_uuid in service_list:
            test_data = self.get_app_url(test_app_uuid, appletid)
        data = {"pro": pro_data, "test": test_data}
        if real_user_uuid:
            sandbox_url_list = []
            sandbox_list = await engine.access.list_sandbox(
                self.app_uuid, real_user_uuid)
            self.publish_for_local = False
            for sandbox in sandbox_list:
                sandbox_name = sandbox.get("sandbox_name")
                sandbox_uuid = sandbox.get("sandbox_uuid")
                sandbox_env = "sandbox" + str(sandbox.get("id"))
                deploy_name = self.app_uuid + sandbox_env
                sandbox_data = self.get_app_url(deploy_name, appletid)
                sandbox_data.update({"env": sandbox_env,
                                     "sandbox_name": sandbox_name,
                                     "sandbox_uuid": sandbox_uuid})
                sandbox_url_list.append(sandbox_data)
            data["sandbox"] = sandbox_url_list
        # 切换本地部署环境生成链接
        self.publish_for_local = True
        await self.get_app_local_config()
        if pro_data:
            local_pro_data = self.get_local_deploy_url(self.app_uuid)
            pro_data["local"] = local_pro_data
        if test_data:
            self.env = "test"
            await self.get_app_local_config()
            local_test_data = self.get_local_deploy_url(test_app_uuid)
            test_data["local"] = local_test_data
        return data

    def publish(self):

        if self.ext_tenant or self.build_type in [-1, -2]:
            return self._publish()
        res = PublishQueue.push(self)
        PublishQueue.publish_one()
        state = self.publish_state
        state.update({"queue": res})
        return state

    def _publish(self):

        if self.status:
            return self.publish_state
        TracingContext.set_trace_id(prefix=f"publish:{self.app_uuid}:{self.env}:")
        self.status += 1
        self.app.add_task(self.run_steps())
        return self.publish_state

    def get_supervisor_confs(self, offline=True) -> dict:
        suffix = "" if offline else ".offline"
        conf_name = f"{self.config.supervisor_conf_path}/{self.app_env}.conf" + suffix
        conf_names = {self.database_name: conf_name}
        for ext_tenant_uuid in self.app_ext_tenants:
            conf_name = f"{self.config.supervisor_conf_path}/{str(uuid.uuid5(uuid.UUID(self.app_env), ext_tenant_uuid)).replace('-', '')}.conf" + suffix
            conf_names.update({ext_tenant_uuid: conf_name})
        return conf_names

    def gen_online_context(self):
        self.decode_context["app_env"] = self.app_env
        self.decode_context["app_name"] = self.app_name
        self.decode_context["front_version"] = self.front_version
        self.decode_context["front_root_path"] = self.config.runtime_front_path
        self.decode_context["front_path"] = f"{self.config.runtime_front_path}/{self.app_env}/{self.front_version}"

    async def offline(self):
        self.gen_online_context()

    async def online(self, supervisor_names: dict, app_publish_info):
        self.gen_online_context()

        for user, name in supervisor_names.items():
            free_port = get_free_port()
            with open(name, "r") as su:
                content = su.readlines()
                port_line = content[6]  # 指定运行端口所在行!
                port_part_all = port_line[port_line.index("--port "):]
                port_part_last = port_part_all[port_part_all.index(";"):]
                new_port_part = f"--port {free_port}      " + port_part_last
                content[6] = port_line[:port_line.index(
                    "--port ")] + new_port_part
            supervisor_conf = "".join(content)
            with open(name, "w") as su:
                su.write(supervisor_conf)

            if user == "":
                self.decode_context["default_server_port"] = free_port
                self.decode_context["server_port"] = free_port
            else:
                self.decode_context["tenants"][user]["server_port"] = free_port

    def handle_supervisor_conf(self, offline=True):
        supervisor_conf_names = self.get_supervisor_confs(offline=offline)

        effective_conf = {}
        for user, conf_name in supervisor_conf_names.items():
            conf_name: str
            app_log.info(f"{user}, {conf_name}")
            if os.path.exists(conf_name):
                new_name = conf_name + \
                    ".offline" if offline else conf_name.replace(
                        ".offline", "")
                os.rename(conf_name, new_name)
                effective_conf.update(
                    {"" if user == self.database_name else user: new_name})
            else:
                if user != self.database_name:
                    # 移除app_ext_tenants中不用进行上下线的tenant_uuid
                    self.app_ext_tenants.remove(user)
        return effective_conf

    async def manage_app_status(self, offline=True):
        """
        上下线管理
        操作影响主版本和所有扩展用户的版本
        删除App时进行下线操作

        """

        # 处理supervisor_conf, 当online情况时, 此处处理不完整, 没有server_port信息
        supervisor_names: dict = self.handle_supervisor_conf(offline)

        funcs = [engine.access.list_obj(APPExtension, APPExtension.select().
                                        where(APPExtension.app_uuid == self.app_uuid))]
        funcs.insert(0, engine.access.get_app_by_app_uuid(self.app_uuid))

        app, app_publish_info = await asyncio.gather(*funcs)
        if not app_publish_info:
            return Code.APP_NEVER_PUBLISHED_NOT_ALL_MANAGE_STATUS

        self.app_name = (app.app_name).lower()
        self.front_version = app_publish_info[0].get("deployment").get(
            "front_version", app.app_version) or app.app_version

        try:
            if engine.config.K8S_SUPPORT:
                await self.send_offline_message(offline)
                await engine.access.update_app(app_uuid=self.app_uuid, status=0 if offline else 1)
                return Code.OK
            else:
                if offline:
                    # 处理 nginx_conf, 清除对应行, 写入静态页面的重定向
                    await self.offline()

                else:
                    await self.online(supervisor_names=supervisor_names, app_publish_info=app_publish_info)
        except Exception:
            # TODO 缺少回滚机制,需要添加手动回滚
            app_log.error(traceback.print_exc())
            return Code.ERROR
        else:
            await async_run("supervisorctl update")
            await async_run("nginx -s reload")
            await engine.access.update_app(app_uuid=self.app_uuid, status=0 if offline else 1)
            return Code.OK

    async def send_offline_message(self, offline):
        offline = 1 if offline else 0
        parms = {"app_uuid": self.app_uuid,
                 "offline": offline, "app_env": self.app_env}
        url = engine.config.OFFLINE_URL + "?" + urllib.parse.urlencode(parms)
        app_log.info(url)
        async with ClientSession(trust_env=True) as session:
            result = await session.post(url)
            app_log.info(result)

    def build_ingress_service(self, app_name):
        service_data = {
            'pathType': 'Prefix',
            'path': f'/{app_name}(/|$)(.*)',
            'backend': {
                'service': {
                    'name': f'app-{app_name}',
                    'port': {
                        'number': 7000
                    }
                }
            }
        }
        return service_data

    @property
    def sandbox_ingress_domain(self):
        return self.config.ingress_domain.replace("runtime", "sandbox")

    @log_step
    async def update_ingress_service(self, env="all"):
        app_log.info("start update_ingress_service")
        await asyncio.sleep(1)
        new_path_list = []
        new_path_list_t = []
        new_path_list_sandbox = []
        service_list = await engine.redis.redis.zrange(self.online_key)
        sandbox_service_list = await engine.redis.redis.zrange(self.sandbox_online_key)
        for app_name in service_list:
            service_data = self.build_ingress_service(app_name)
            if app_name.endswith("test"):
                new_path_list_t.append(service_data)
            else:
                new_path_list.append(service_data)
        new_path_list_sandbox = list(
            map(self.build_ingress_service, sandbox_service_list))
        host = "*." + self.config.ingress_domain
        host_test = host
        host_sanbox = "*." + self.sandbox_ingress_domain
        ingress_name = "runtime-ingress"
        ingress_name_test = ingress_name + "-test"
        ingress_name_sandbox = ingress_name + "-sandbox"
        ingress_class = "runtime-nginx"
        ingress_class_t = "runtime-nginx-t"
        if env == "all":
            await self.apply_ingress_file(new_path_list, host, ingress_name, ingress_class)
            await self.apply_ingress_file(new_path_list_t, host_test, ingress_name_test,
                                          ingress_class_t)
            await self.apply_ingress_file(new_path_list_sandbox, host_sanbox, ingress_name_sandbox,
                                          ingress_class_t)
        elif env == "test":
            await self.apply_ingress_file(new_path_list_t, host_test, ingress_name_test,
                                          ingress_class_t)
        elif "sandbox" in env:
            await self.apply_ingress_file(new_path_list_sandbox, host_sanbox, ingress_name_sandbox,
                                          ingress_class_t)
        else:
            await self.apply_ingress_file(new_path_list, host, ingress_name, ingress_class)

    async def apply_ingress_file(self, path_list, host, name, ingress_class):
        import yaml
        ingress_file = f"{project_base_path}/apps/services/publish/ingress.yaml"
        with open(ingress_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
            runtime_rule = data["spec"]["rules"][0]
            runtime_rule["http"]['paths'] = path_list
            data["metadata"]["name"] = name
            data["metadata"]["annotations"]["kubernetes.io/ingress.class"] = ingress_class
            data["metadata"]["namespace"] = self.config.publish_namespace
            runtime_rule["host"] = host
        with open("/root/ingress.yaml", "w", encoding='utf-8') as f:
            yaml.dump(data, f)
        result = await async_run("cd /root && kubectl apply -f ingress.yaml")
        app_log.info(result)

    async def update_publish_status(self, timeout=30.0):
        app_log.info("update_publish_status")
        if self.k8s_support:
            state = self.publish_state
            app_log.info(
                f"{self.publish_key} publish_state{state} timeout{timeout}")
            await engine.redis.redis.hmset_dict(self.publish_key,  state)
            await engine.redis.redis.expire(self.publish_key, timeout)

    async def incr_step(self):
        self.step += 1
        if self.status != 3:
            await self.update_publish_status(600.0)

    async def init_publish_status(self):
        # if engine.app.config.ENV == "Testing":
        #     timeout = 600.0
        # else:
        timeout = 600.0
        await engine.redis.redis.hmset_dict(
            self.publish_key,  {"step": 0, "status": 1})
        await engine.redis.redis.expire(self.publish_key, timeout)

        await engine.redis.redis.delete("_".join([self.publish_key, "fail"]))

    @log_step
    async def tag_back_image(self):
        if self.publish_for_local:
            print_image = self.app.config.PRINT_IMAGE
            deploy_print_image = f"{self.config.deploy_image}/{self.app_env}:print-{self.app_revision}"
            await async_run(f"docker pull {print_image}")
            await async_run(f"docker tag {print_image} {deploy_print_image}")
            await async_run(f"docker push {deploy_print_image}")
        return

    async def get_app_local_config(self):
        static_dir_name = "app_list"  # 存放应用安装包
        version_dir_name = "app_static_list"  # 存放应用版本信息
        electron_dir_name = "electron"
        if self.publish_for_local:
            local_config = await engine.access.get_app_local_config(
                self.app_uuid)
            prefix = ""
            if self.env == "test":
                prefix = "test_"
                env_config = local_config.get("test_env_config", {})
            else:
                env_config = local_config.get("pro_env_config", {})
            mysql_info = env_config.get("mysql", {})
            ngxin_info = env_config.get("nginx", {})
            kafka_info = env_config.get("Kafka", {})

            app_log.info(f"get_app_local_config local_config {local_config}, env {self.env}")
            local_ip = local_config.get(prefix + "local_ip", "")
            local_port = local_config.get(prefix + "local_port", "")
            worker_processes = local_config.get(prefix + "srv_proc_count", 4)
            ssl = env_config.get("ssl", False)
            ssl_certificate = env_config.get("ssl_certificate", "")
            ssl_certificate_key = env_config.get("ssl_certificate_key", "")
            self.worker_processes = worker_processes
            vfs_processes = local_config.get(prefix + "vfs_proc_count", 1)
            self.vfs_processes = vfs_processes
            self.server_host = local_ip
            self.server_port = local_port
            if ssl:
                self.server_protocol = "https"
            else:
                self.server_protocol = "http"
            self.decode_context['ssl'] = ssl
            self.decode_context['ssl_certificate'] = ssl_certificate
            self.decode_context['ssl_certificate_nginx'] = '/etc/nginx/pem/' + os.path.basename(ssl_certificate) if ssl_certificate else ''
            self.decode_context['ssl_certificate_key'] = ssl_certificate_key
            self.decode_context['ssl_certificate_key_nginx'] = '/etc/nginx/pem/' + os.path.basename(ssl_certificate_key) if ssl_certificate_key else ''
            self.version_dir_name = version_dir_name + "_local"
            self.static_dir_name = static_dir_name + "_local"
            self.electron_dir_name = electron_dir_name + "_local"
            web_server_config = env_config.get("web_server_config", [])  # LocalConfig.WEB_SERVER_CONFIG
            web_config_dict = {}
            for WEB_CONFIG in web_server_config:
                web_config_dict.update({WEB_CONFIG.get("attribute"): WEB_CONFIG.get("value")})
            if web_config_dict.get("ENDPOINT"):
                endpoint = web_config_dict.get("ENDPOINT")
                oss_status = True
            else:
                oss_status = False
                endpoint = LocalConfig.ENDPOINT
            request_max_size = web_config_dict.get("REQUEST_MAX_SIZE") or LocalConfig.REQUEST_MAX_SIZE
            api_response_timeout = web_config_dict.get("RESPONSE_TIMEOUT") or LocalConfig.RESPONSE_TIMEOUT
            # vim lemon/back/apps/services/publish/__init__.py 
            # local_database_name = self.user_uuid[:-5] if self.user_uuid.endswith("local") else self.user_uuid
            app_log.info(f"localdb={self.local_database_name},useruuid={self.user_uuid },groupuuid={self.group_uuid}")
            self.decode_context["local_database_name"] = self.local_database_name
            self.decode_context["file_storage_dir"] = env_config.get(
                "file_storage_dir") or LocalConfig.FILE_STORAGE_DIR
            self.decode_context["runtime_file_storage_dir"] = env_config.get(
                "runtime_file_storage_dir") or LocalConfig.RUNTIME_FILE_STORAGE_DIR
            custom_domain_name = env_config.get("domain_name") or local_ip
            self.decode_context["custom_domain_name"] = custom_domain_name
            self.decode_context["endpoint"] = endpoint
            self.decode_context["oss_status"] = oss_status
            self.decode_context["api_response_timeout"] = api_response_timeout
            self.decode_context["request_max_size"] = request_max_size
            # mysql
            self.decode_context["mysql_host"] = mysql_info.get("host") or LocalConfig.HOST
            self.decode_context["mysql_port"] = mysql_info.get("port") or LocalConfig.MYSQL_PORT
            self.decode_context["mysql_user"] = mysql_info.get("user") or LocalConfig.USER
            self.decode_context["mysql_password"] = mysql_info.get("password") or LocalConfig.PASSWORD
            self.decode_context["mysql_pwd"] = mysql_info.get("password") or LocalConfig.PASSWORD
            self.decode_context["mysql_max_size"] = mysql_info.get("max_size") or LocalConfig.MAX_CONNECT_SIZE
            # nginx
            self.decode_context["proxy_connect_timeout"] = ngxin_info.get(
                "proxy_connect_timeout") or LocalConfig.PROXY_CONNECT_TIMEOUT
            self.decode_context["proxy_read_timeout"] = ngxin_info.get(
                "proxy_read_timeout") or LocalConfig.PROXY_READ_TIMEOUT
            self.decode_context["proxy_send_timeout"] = ngxin_info.get(
                "proxy_send_timeout") or LocalConfig.PROXY_SEND_TIMEOUT
            self.decode_context["send_timeout"] = ngxin_info.get(
                "send_timeout") or LocalConfig.SEND_TIMEOUT
            if local_port:
                self.decode_context["custom_server_address"] = f"{self.server_protocol}://{custom_domain_name}:{local_port}"
            else:
                self.decode_context["custom_server_address"] = f"{self.server_protocol}://{custom_domain_name}"
            if env_config.get("domain_name", ""):
                self.decode_context["is_use_domain_name"] = True
            app_log.info(f"get_app_local_config decode_context= {self.decode_context}")
            # kafka
            rulechains = kafka_info.get("rulechain") or []
            kafka_hosts = []
            for rulechain in rulechains:
                if rulechain.get("IP") and rulechain.get("port"):
                    kafka_hosts.append(f"{rulechain.get('IP')}:{rulechain.get('port')}")
            self.decode_context["kafka_hosts"] = kafka_hosts
        else:
            self.static_dir_name = static_dir_name
            self.version_dir_name = version_dir_name
            self.electron_dir_name = electron_dir_name
        if self.server_protocol == "http":
            ws_protocol = "ws"
            protocol = "http"
        else:
            ws_protocol = "wss"
            protocol = "https"
        self.ws_address = f"{ws_protocol}://{self.server_host}"
        self.server_address = f"{protocol}://{self.server_host}"
        self.desktop_address = f"{protocol}://{self.config.domain_name_d}"
        self.app_address = f"{protocol}://{self.config.domain_name_app}"
        domain_name = os.getenv("PublishDomainName") or self.config.domain_name
        self.mobile_address = f"{protocol}://m{domain_name}"
        self.mobile_ws_address = f"{ws_protocol}://m{domain_name}"
        self.desktop_ws_address = f"{ws_protocol}://{self.config.domain_name_d}"
        if self.server_port:
            self.ws_address = f"{self.ws_address}:{self.server_port}"
            self.server_address = f"{self.server_address}:{self.server_port}"
            self.mobile_ws_address = f"{self.mobile_ws_address}:{self.server_port}"
            self.desktop_ws_address = f"{self.desktop_ws_address}:{self.server_port}"
        # 本地部署通过url中的路径区分客户端类型
        if self.publish_for_local:
            # self.server_address = env_config.get("domain_name") or self.server_address
            self.decode_context["local_mobile_address"] = self.decode_context["custom_server_address"]
            self.desktop_address = \
                f"{self.server_address}/desktop"
            self.mobile_address = \
                f"{self.server_address}"
            self.app_address = \
                f"{self.server_address}/app"
            self.mobile_ws_address = f"{self.ws_address}"
            self.desktop_ws_address = f"{self.ws_address}/desktop"
        self.decode_context["nginx_port"] = self.server_port
        self.decode_context["server_address"] = self.server_address
        self.decode_context["server_host"] = self.server_host
        
    @sub_env_ignore()
    async def run_publish_script(self):
        """
        发布后需要执行的脚本们, 每个版本要维护
        # TODO 要注意本地部署的影响
        """
        front_lemon_version = StrictVersion(self.front_lemon_version[1:])
        version_127 = StrictVersion("1.27")
        if front_lemon_version < version_127:
            await process_role_member_relationship(
                self.db, self.app, self.database, self.objs, self.database_name, self.app_uuid, self.app_revision)
        version_131 = StrictVersion("1.31")
        if front_lemon_version < version_131:
            await update_wf_tables_fk(self.database, self.objs, self.database_name, self.app_uuid, engine)
        version_133 = StrictVersion("1.33")
        app_log.info(front_lemon_version)
        if front_lemon_version < version_133:
            await process_redis_cache(self.app_uuid, engine, self.env)
