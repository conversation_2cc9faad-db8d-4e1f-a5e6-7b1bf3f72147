from apps.services import DocumentCheckerService
from apps.services.checker import checker
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.ide_const import ExtPack


class ExpansionpackDocumentCheckerService(DocumentCheckerService):
    # TODO 模块配置页面虽然是文档, 但目前没有需要进行文档检查的地方, 也只需要保存一份json

    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str,
            document_uuid: str, document_name: str,
            element: dict, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, *args, **kwargs)

    @checker.run
    def check_ext_package(self):
        pass
        # self.check_module_theme_exist()
