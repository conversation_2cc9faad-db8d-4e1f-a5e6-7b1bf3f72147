# -*- coding:utf-8 -*-

import time
import os
from typing import Optional

import peewee
import ujson
import inspect
import asyncio
import traceback
import collections
import peewee_async
import typing
from copy import copy
from peewee import (
    SQL, FixedCharField, Metadata, Table, Model as Model_, Entity, JOIN
)
from playhouse.shortcuts import model_to_dict
from playhouse.hybrid import hybrid_property
from RestrictedPython import compile_restricted_eval
from asgiref.sync import AsyncToSync
from collections import namedtuple, defaultdict
from functools import partial
from playhouse import shortcuts
from datetime import datetime, timedelta
from apps import COMPANY_KEY, Runtime

from baseutils.const import (
    UUID_SystemConfig, UUID_User, UUID_Department, UUID_TenantUser,
    UUID_TenantDepartment, UUID_TenantDepartmentMember,
    UUID_WorkflowInstance, UUID_NodeInstance, UUID_NodeTask, UUID_WFStore,
    SystemField, EventWhen, PermissionAction, SysModelFunc, UserTableStatus,
    SystemTable, UUID_UserRole, UUID_ExportTemplate, UUID_RoleMember,
    UUID_ModelBasic, UUID_Func, UUID_ModelCloudFunction, UUID_Resource, UUID_Tag, UUID_PermissionConfig,
    UUID_Navigation, UUID_NavigationItem
)
from apps.base_utils import (
    get_sys_field_uuid, get_memory_id, is_main_thread, LemonContextVar, GlobalVars,
    default_select_fields, placeholder_parse, is_normal_field, is_calc_field, is_foreign_key,
    is_system_field, load_json_object_from_json_arrayagg, load_data_from_json_object,
    python_agg_field, check_calc_field_value, ListWithError, is_primary_key,
    is_serial_with_post_gen_type, get_serial_value, is_serial_with_pre_gen_type,
    lemon_uuid, SYSTEM_MODEL_DICT, check_lemon_uuid, has_generated_field, is_generated_field,
    get_runtime_app_revision, get_runtime_app_uuid
)
from apps.ide_const import (
    PermissionDeploy, SerialRule, ValueEditorType, FieldType, RelationshipType,
    DictWrapper
)
from baseutils.log import app_log
from baseutils.utils import (
    FuncRunError, CVE, func_trigger_context, SYSTEM_MODEL_DICT, 
    make_tenant_sys_table_copy_signle
)
from apps.peewee_ext import (
    LemonCharField, LemonJsonField, LemonIntegerField, LemonBooleanField, LemonAutoField,
    LemonModelQueryHelper, LemonDelete, LemonFieldMixin, LemonDateTimeField,
    LemonExtFieldAccessor, BooleanField, LemonSystemForeignKeyField,
    calc_depend_all_field_map, convert_field_value, LemonMediumTextField
)
from apps.utils import get_runtime_table_copy, make_runtime_table_name, new_relationship_uuid
from apps.exceptions import FuncExecError, LemonPermissionError, ExprExecError, FuncFieldPermissionError
from apps.value_editor_utils import LemonBaseValueEditor
from runtime.protocol import func as func_proto

db_proxy = peewee.Proxy()


def create_sys_fields():
    return {
        "_creator": LemonCharField(
            max_length=100, constraints=[SQL('DEFAULT ""')],
            verbose_name=SystemField.CREATOR_NAME, null=True, is_system=True),
        "_create_time": LemonDateTimeField(
            default=int(time.time()), verbose_name=SystemField.CREATE_TIME.get("display_name"),
            is_system=True),
        "_last_modified_by": LemonCharField(
            max_length=100, constraints=[SQL('DEFAULT ""')],
            verbose_name=SystemField.MODIFY_NAME, null=True, is_system=True),
        "_owner": LemonIntegerField(
            verbose_name=SystemField.OWNER_NAME, null=True, is_system=True),
        "_reviser": LemonIntegerField(
            verbose_name=SystemField.REVISER_NAME, null=True, is_system=True),
        "_modified_time": LemonDateTimeField(
            default=int(time.time()), verbose_name=SystemField.MODIFIED_TIME.get("display_name"), is_system=True),
        "_status": LemonCharField(
            max_length=10, default="0",
            verbose_name=SystemField.STATUS.get("display_name"), constraints=[SQL('DEFAULT "0"')], is_system=True),
        "_wf_status": LemonCharField(
            null=True, max_length=10, default=None,
            verbose_name=SystemField.WF_STATUS.get("display_name"), is_system=True),
        "tenant_uuid": LemonCharField(
            max_length=32, default="", constraints=[SQL('DEFAULT ""')], is_system=True),
        "_approval_time": LemonDateTimeField(
            null=True, default=None,
            verbose_name=SystemField.APPROVAL_TIME.get("display_name"), is_system=True),
        "_rel_wf": LemonIntegerField(
            null=True, verbose_name=SystemField.REL_WF_NAME, is_system=True)
    }


class ClassProperty(property):
    def __get__(self, cls, owner):
        return self.fget.__get__(None, owner)()


class UUID:
    model_user = UUID_User
    model_department = UUID_Department
    tenant_user = UUID_TenantUser
    tenant_department = UUID_TenantDepartment
    tenant_department_member = UUID_TenantDepartmentMember
    workflow_instance = UUID_WorkflowInstance
    node_instance = UUID_NodeInstance
    node_task = UUID_NodeTask
    wf_store = UUID_WFStore
    system_config = UUID_SystemConfig
    lemon_userrole = UUID_UserRole
    role_member = UUID_RoleMember
    lemon_exporttemplate = UUID_ExportTemplate
    model_basic = UUID_ModelBasic
    func = UUID_Func
    model_cloud_function = UUID_ModelCloudFunction
    lemon_resource = UUID_Resource
    lemon_tag = UUID_Tag
    permission_config = UUID_PermissionConfig
    lemon_navigation = UUID_Navigation
    lemon_navigationitem = UUID_NavigationItem

    _ALL = [
        model_user, model_department, tenant_user, tenant_department,
        tenant_department_member, lemon_userrole, system_config,
        lemon_exporttemplate, model_basic, func, lemon_resource, lemon_tag, lemon_navigation, lemon_navigationitem]
    _NEED_TENANT = [role_member, model_cloud_function, permission_config]
    _ALL_WF = [workflow_instance, node_instance, node_task, wf_store]
    _META_TABLE_ALL = [model.meta_table_name.value for model in _ALL + _ALL_WF]
    _META_TABLE_NEED_TENANT = [model.meta_table_name.value for model in _NEED_TENANT]
    _TABLE_NAME_ALL = [model.table_name.value for model in _ALL + _ALL_WF + _NEED_TENANT]

    sys_model_resource_ids = {
        enum.value for enum_class in _ALL + _ALL_WF + _NEED_TENANT for enum in enum_class.__members__.values()}

    @ClassProperty
    @classmethod
    def ALL(cls):
        for model in cls._ALL:
            for uid in model:
                yield uid.value

    @ClassProperty
    @classmethod
    def TABLE_ALL_WF(cls):
        for model in cls._ALL_WF:
            yield model.table_name.value

    @ClassProperty
    @classmethod
    def TABLE_ALL(cls):
        for model in cls._ALL:
            yield model.table_name.value

    @ClassProperty
    @classmethod
    def META_TABLE_ALL(cls):
        return cls._META_TABLE_ALL

    @ClassProperty
    @classmethod
    def META_TABLE_NEED_TENANT(cls):
        return cls._META_TABLE_NEED_TENANT

    @ClassProperty
    @classmethod
    def TABLE_NAME_ALL(cls):
        return cls._TABLE_NAME_ALL

    @ClassProperty
    @classmethod
    def READ(cls):
        return [
            # cls.model_user.table_name.value,
            cls.model_user.user_name.value,
            cls.model_user.user_uuid.value,
            cls.model_user.mobile_phone.value,
            cls.model_user.full_name.value,
            cls.model_user.avatar.value,
            cls.tenant_user.user_uuid.value,
            cls.tenant_user.full_name.value,
            cls.tenant_user.job_number.value,
            cls.tenant_user.mobile_phone.value,
            cls.tenant_user.email.value,
            cls.tenant_user.created_at.value,
            cls.tenant_user.is_delete.value,
            ]


def get_read_only_table():
    read_only_table = [UUID_Resource, UUID_Tag]
    read_only_table_name = {}
    app_uuid = get_runtime_app_uuid()
    version = get_runtime_app_revision()
    for table in read_only_table:
        meta_table_name = table.meta_table_name.value
        display_name = table.display_name.value
        real_table_name = make_runtime_table_name(app_uuid, str(version), meta_table_name)
        read_only_table_name.update({real_table_name: display_name})
        for field in table:
            if field in [UUID_Resource.superior_resource]:
                # 标签分组的自关联
                relationship_uuid = field.value
                target_path = new_relationship_uuid(relationship_uuid)
                source_path = new_relationship_uuid(relationship_uuid, from_source=True)
                read_only_table_name.update({target_path: display_name})
                read_only_table_name.update({source_path: display_name})
            read_only_table_name.update({field.value: display_name})
    return read_only_table_name


def get_default_value(default_value_dict, column, editor_class=LemonBaseValueEditor):
    column_lemon_type = column.lemon_type
    must_editor_default = False
    if default_value_dict:
        default_value = editor_class(**default_value_dict).value
        if default_value is None:
            if not column.is_required:
                return None, None
        must_editor_default = True
    else:
        default_value = column.default
    default_name = default_value
    if column_lemon_type == FieldType.STRING:  # str
        if must_editor_default:
            default_name = default_value
        elif not isinstance(default_value, str):
            if column.is_required:
                default_name, default_value = "", ""
            else:
                default_name, default_value = "", None
    elif column_lemon_type == FieldType.FILE:  # file
        if not isinstance(default_value, str):
            default_name, default_value = "", ""
    elif column_lemon_type == FieldType.IMAGE:
        if not isinstance(default_value, list):  # 图片默认值是list
            default_name, default_value = [], []
    # int, float
    elif column_lemon_type in [FieldType.INTEGER, FieldType.DECIMAL]:
        if not isinstance(default_value, (int, float)):
            default_value, default_name = 0, 0
    elif column_lemon_type == FieldType.BOOLEAN:  # bool
        if isinstance(default_value, bool):
            default_name = "是" if default_value is True else "否"
        else:
            # default_value, default_name = True, "是"
            default_value, default_name = None, None
    elif column_lemon_type == FieldType.DATETIME:  # datetime
        if column.is_required and default_value is None:
            default_value = int(time.time())
        if default_value is not None:
            if not isinstance(default_value, (int, float)):
                default_value = int(time.time())
            datetime_format = "%Y/%m/%d %H:%M:%S"
            datetime_data = datetime.fromtimestamp(default_value)
            default_name = datetime_data.strftime(datetime_format)
    elif column_lemon_type == FieldType.ENUM:  # enum
        choices = column.choices
        if not isinstance(default_value, (int, str)):
            default_value = 0
        if isinstance(default_value, int):
            try:
                default_name = choices(default_value).name
            except Exception:
                app_log.error(f"enum data: {default_value}")
                default_value, default_name = 0, ""
        elif isinstance(default_value, str):
            try:
                choice = choices[default_value]
                default_value = choice.value
                default_name = choice.name
            except Exception:
                choice = None
                # 字段默认值选择枚举类型时会返回枚举的值
                for c in choices:
                    if c.value == default_value:
                        choice = c
                        break
                if choice:
                    default_value = choice.value
                    default_name = choice.name
                else:
                    default_value, default_name = None, None
    else:
        default_name = ""
        default_value = "" if default_value is None else default_value
    return default_name, default_value


def get_column_default(column, editor_class=LemonBaseValueEditor):
    default_value_dict = column.default_value_dict
    return get_default_value(default_value_dict, column, editor_class)


async def make_serial(column, engine, pre=True, count=1, eval_globals=None):
    current_user = LemonContextVar.current_user.get()
    serial_value, value = 1, [""]
    if current_user and count >= 1:
        app_uuid = current_user.app_uuid
        tenant_uuid = current_user.tenant_id
        timezone = current_user.timezone
        if column.serial_rule == SerialRule.PREFIX_DATE_SERIAL:
            today = datetime.utcnow() + timedelta(hours=timezone)
            date_str = today.strftime("%Y%m%d")
        else:
            date_str = ""
        serial_start_num = 1
        if column.serial_rule == SerialRule.PREFIX_SERIAL:
            if column.serial_start_num == "":
                serial_start_num = 1
            else:
                serial_start_num = int(column.serial_start_num)
                if serial_start_num < 0:
                    serial_start_num = 0
        field_uuid = column.column_name
        if column.serial_prefix_info:
            serial_prefix_info = column.serial_prefix_info
            eval_globals = eval_globals or DictWrapper()
            if eval_globals:
                obj = shortcuts.dict_to_model(
                    column._model_class, eval_globals, ignore_unknown=True)
            else:
                obj = None
            serial_prefix = await lemon_entity.async_eval_expr(
                serial_prefix_info, globals=eval_globals,
                obj=obj, with_exception=True)
        else:
            serial_prefix = ""
        func = get_serial_value(
            engine, app_uuid, tenant_uuid, field_uuid, date_str, serial_prefix,
            start_num=serial_start_num, pre=pre)
        if is_main_thread():
            serial_value, serial_pk, first = await func
        else:
            future = asyncio.run_coroutine_threadsafe(func, engine.loop)
            serial_value, serial_pk, first = future.result()

        # 有需求是要当达到最大值时，从头开始循环
        # if column.serial_num_length:
        #     max_value = int(int(column.serial_num_length) * "9")
        # else:
        #     max_value = 9999
        if column.serial_rule == SerialRule.PREFIX_SERIAL:
            # 用 最大值 减去 最小值 得出每次循环的数的个数(loop_value)
            # 用 serial_value 取余 loop_value
            # 余数为 0 则是最大值，反之则将 余数 + 最小值 - 1
            # serial_start_num = int(column.serial_start_num)
            # loop_value = max_value - serial_start_num + 1
            # serial_value = serial_value % loop_value
            # serial_value = max_value if serial_value == 0 else serial_start_num + serial_value - 1
            serial_real_value = serial_value
        else:
            # serial_value = serial_value % max_value
            # serial_value = max_value if serial_value == 0 else serial_value
            serial_real_value = serial_value
        value = []
        for i in range(count):
            if i != 0:
                serial_real_value = serial_real_value + 1
            v = str(serial_real_value).zfill(int(column.serial_num_length))
            v = serial_prefix + date_str + v
            value.append(v)
        serial_value = serial_real_value
    return (field_uuid, tenant_uuid, app_uuid, date_str, serial_prefix, serial_value, serial_pk, first), value


def get_field_value(
        query, res, row_type, get_value_func, system_model_alias_dict,
        model_eval_globals: DictWrapper, real_need_value_fields: list = None):
    value_fields_obj = real_need_value_fields or enumerate(query._returning)  # 可能只需要计算部分字段
    for idx, field in value_fields_obj:
        is_alias, field_alias = False, field
        if isinstance(field, peewee.Alias):
            is_alias = True
            if row_type in [peewee.ROW.CONSTRUCTOR, peewee.ROW.MODEL, peewee.ROW.NAMED_TUPLE]:
                field = field.unalias()
                name_key = field.name
            else:
                name_key = field._alias
                field = field.unalias()
        else:
            name_key = field.name

        if isinstance(field, peewee.Function):
            if field.name == "JSON_OBJECT":
                value = get_value_func(res, name_key, idx)
                if len(field.arguments) >= 2 and value:
                    # 主键是最后被加到 json_object 里的
                    primary_key_name, _ = field.arguments[-2], field.arguments[-1]
                    primary_key_value = value.get(primary_key_name)
                    field_source_uuid = get_field_model_source_uuid(
                        field.arguments[1], system_model_alias_dict)
                    model_eval_globals.setdefault(field_source_uuid, dict())
                    if primary_key_value is not None:
                        model_eval_globals[field_source_uuid].update(value)
            else:
                field = field.arguments[0]
                if isinstance(field, peewee.Function) and field.name == "JSON_OBJECT":
                    value_list = get_value_func(res, name_key, idx)
                    if len(field.arguments) >= 2 and value_list:
                        # 主键是最后被加到 json_object 里的
                        primary_key_name, _ = field.arguments[-2], field.arguments[-1]
                        field_source_uuid = get_field_model_source_uuid(
                            field.arguments[1], system_model_alias_dict)
                        primary_key_value_set = set()
                        for value in value_list:
                            primary_key_value = value.get(primary_key_name)
                            model_eval_globals.setdefault(field_source_uuid, list())
                            if primary_key_value is not None and primary_key_value not in primary_key_value_set:
                                primary_key_value_set.add(primary_key_value)
                                model_eval_globals[field_source_uuid].append(value)
            continue
        field_source_uuid = get_field_model_source_uuid(field, system_model_alias_dict)
        eval_globals = model_eval_globals.setdefault(field_source_uuid, dict())
        if isinstance(field, peewee.ForeignKeyField):
            value = get_value_func(res, name_key, idx)
            eval_globals.update({field.name: value})
        elif isinstance(field, (peewee.Field, peewee.Column)):
            value = get_value_func(res, name_key, idx)
            value = convert_field_value(field, value)
            eval_globals.update({field.name: value})
            if is_alias:
                eval_globals.update({field_alias._alias: value})
            if isinstance(field, peewee.Field):
                eval_globals.update({field.column_name: value})
                if name_key.endswith("_pk"):
                    eval_globals.update({name_key: value})


def get_type_events(model_class, event_type, when=EventWhen.BEFORE):
    type_events = model_class._events.get(event_type, {})
    events = type_events.get(when, [])
    return events


def get_model_events_sql(model_class, event_type=None, when=None):
    # app_name = os.environ.get("APP_NAME")
    # sys_module = importlib.import_module("." + app_name + ".系统模块", 'resources')
    # lemon_models = importlib.import_module(".lemon_model", sys_module.__name__)
    model_uuid = model_class._meta.origin_table_name
    app_log.info(f"origin_table_name: {model_uuid}")
    if not check_lemon_uuid(model_uuid):
        app_log.info(model_class)
        query = model_class.select().where(False).dicts()
        query._run_model_event = False
        return query
        # raise Exception(f"model_uuid error: {model_uuid}")
    # model = lemon_models.模型
    # func_model = lemon_models.云函数
    # model_event_model = lemon_models.模型事件
    model = SYSTEM_MODEL_DICT.get("模型")
    func_model = SYSTEM_MODEL_DICT.get("云函数")
    model_event_model = SYSTEM_MODEL_DICT.get("模型事件")
    fields = [model_event_model.call_at, model_event_model.when, model_event_model.data_as_param,
              model_event_model.error_tip, model.model_uuid, func_model.func_uuid, func_model.func_name,
              model_event_model.id]
    query = model_event_model.select(*fields).join(
        model, join_type=JOIN.LEFT_OUTER, on=(model.id == model_event_model.to_model)).join(
        func_model, join_type=JOIN.LEFT_OUTER, on=(model_event_model.to_func == func_model.id)).where(
        model.model_uuid == model_uuid)
    # 模型事件表通过query查询自己的events时不需要再次触发查询事件
    if model_class._meta.model_name in ["模型事件", "云函数"]:
        query._run_model_event = False
    if event_type == "select":
        query._use_cache = f"{GlobalVars.engine.app.config.APP_UUID}{GlobalVars.engine.app.config.APP_ENV_NAME}" + \
                f":sql_cache:{query}"
        query._redis = GlobalVars.engine.redis
    return query.dicts()

# 获取多个模型的模型事件
def gen_multi_model_events_sql(model_classes: dict):
    model_uuids = list()
    run_model_event = True
    use_cache = False
    for model_class, event_types in model_classes.items():
        model_uuid = model_class._meta.origin_table_name
        model_name = getattr(model_class._meta, "model_name", None)
        if not check_lemon_uuid(model_uuid):
            if model_name not in SYSTEM_MODEL_DICT:
                app_log.error(f"model_uuid error: {model_uuid}")
                continue
        if model_name in ["模型事件", "云函数"]:
            run_model_event = False
        if ModelEventType.SELECT in event_types:
            use_cache = True
        model_uuids.append(model_uuid)

    model = SYSTEM_MODEL_DICT.get("模型")
    func_model = SYSTEM_MODEL_DICT.get("云函数")
    model_event_model = SYSTEM_MODEL_DICT.get("模型事件")
    fields = [model_event_model.call_at, model_event_model.when, model_event_model.data_as_param,
                model_event_model.error_tip, model.model_uuid, func_model.func_uuid, func_model.func_name, model.model_name]
    query = model_event_model.select(*fields).join(
        model, join_type=JOIN.LEFT_OUTER, on=(model.id == model_event_model.to_model)).join(
        func_model, join_type=JOIN.LEFT_OUTER, on=(model_event_model.to_func == func_model.id)).order_by(model.model_uuid).where(
        model.model_uuid.in_(model_uuids))
    query._run_model_event = run_model_event
    if use_cache:
        query._use_cache = f"{GlobalVars.engine.app.config.APP_UUID}{GlobalVars.engine.app.config.APP_ENV_NAME}" + \
                f":sql_cache:{query}"
        query._redis = GlobalVars.engine.redis
    return query.dicts()


def collect_model_events(events: list):
    model_events = {}
    for event in events:
        event_type = event.get("call_at")
        event_when = event.get("when")
        event_func = event.get("func_uuid")
        event_data_as_param = event.get("data_as_param")
        event_error_tip = event.get("error_tip")
        event_type_data = model_events.setdefault(event_type, dict())
        event_when_data = event_type_data.setdefault(event_when, list())
        event_when_data.append((event_func, event_data_as_param, event_error_tip, {"event_id": event.get("id")}))
    return model_events


def get_model_events(model_events: dict, event_type, when=EventWhen.BEFORE):
    type_events = model_events.get(event_type, {})
    events = type_events.get(when, [])
    return events


def gen_sys_model_events(model_class, event_type, when=EventWhen.BEFORE):
    events, sys_events = list(), list()
    if model_class._meta.meta_table_name == "tenant_department":
        sys_events = SysModelFunc.DEP
        sys_events = [e for e in SysModelFunc.DEP if e.get("when") == when and
                      e.get("event_type") == event_type]
    elif model_class._meta.meta_table_name == "tenant_user":
        sys_events = SysModelFunc.USER
        sys_events = [e for e in SysModelFunc.USER if e.get("when") == when and
                      e.get("event_type") == event_type]
    elif model_class._meta.meta_table_name == "role_member":
        sys_events = SysModelFunc.ROLE_MEMBER
        sys_events = [e for e in SysModelFunc.ROLE_MEMBER if e.get("when") == when and
                      e.get("event_type") == event_type]
    for e in sys_events:
        events.append((e.get("func_uuid"), e.get("data_as_param", "0"), e.get("error_tip", dict())))
    return events


async def init_model(model_class, **data):
    model_event_type = ModelEventType.CREATE
    model_events_query = get_model_events_sql(model_class)
    model_events = await execute(model_events_query)
    model_events = collect_model_events(list(model_events))
    pre_events = get_type_events(model_class, model_event_type)
    pre_model_events = get_model_events(model_events, model_event_type)
    pre_events += pre_model_events
    await run_model_events(pre_events, model_event_type)
    model_obj = model_class(**data)
    when = EventWhen.AFTER
    post_events = get_type_events(model_class, model_event_type, when=when)
    post_model_events = get_model_events(model_events, model_event_type, when=when)
    post_events += post_model_events
    await run_model_events(post_events, model_event_type, when, models=[model_obj])
    return model_obj


def make_table_name(model_class):
    company = os.getenv(COMPANY_KEY) or "lemon"
    model_name = model_class.__name__
    return "_".join([company, model_name.lower()])


class ModelEventType:

    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    CANCEL = "cancel"
    CREATE = "create"
    SAVE = "save"
    SELECT = 'select'
    UNSUPPORTED = "unsupported"
    ALL = (INSERT, UPDATE, DELETE, CANCEL, SELECT)
    EVENT_ALL = [CREATE, SAVE, DELETE, CANCEL, SELECT]


class ModelEventTopic:
    def __init__(self, model=None, model_uuid=None, event_type=ModelEventType.ALL, pks=None) -> None:
        self.model = model
        if model:
            self.uuid = model._meta.table_name
        else:
            self.uuid = model_uuid
        if not isinstance(event_type, (list, tuple)):
            event_type = [event_type]
        self.event_type = event_type
        self.pks = pks or list()

    def to_str(self):
        # ? 是否需要区分不同database
        return f"{GlobalVars.engine.app.config.REDIS_CHANNEL_NOTIFY_FRONT}-{self.uuid}"


# {
#     "model": "",
#     "event_type": ModelEventType.INSERT,
#     "data": [{"pk": 1, "column_name": "value"}],
#     "timestamp": int(time.time())
# }
class ModelEvent:
    def __init__(self, data=None, event_type=None, pks=None, rows=None, model=None):
        self.event_type = event_type
        self.pks = pks or list()
        self.timestamp = int(time.time())
        self.model = model
        if self.model and inspect.isclass(self.model_obj) and issubclass(self.model, peewee.Model):
            self._model_uuid = self.model._meta.table_name
        else:
            self._model_uuid = None
        self.rows = []
        self.raw_rows = []
        if data:
            self.load(data)

    def __repr__(self) -> str:
        return "<%s@%s>" % (type(self).__name__, id(self))

    def _decode_row(self, row_data):
        row = {}
        for key, value in row_data.items():
            if key == "pk":
                column = self.model._meta.primary_key
            else:
                column = self.model._meta.columns.get(key)
            if column:
                row.update({column: value})
        return row

    def _decode_model(self):
        # todo:if sys model
        self.model = GlobalVars.engine.uuids.model_uuid_map.get(self._model_uuid)

    def load(self, data):
        if isinstance(data, str):
            data = ujson.loads(data)
        model_obj = data.get("model")
        app_log.info(f"model_obj: {model_obj}")
        if model_obj and inspect.isclass(model_obj) and issubclass(model_obj, peewee.Model):
            self._model_uuid = model_obj._meta.table_name
            self.model = model_obj
        else:
            self._model_uuid = model_obj
            self._decode_model()
        self.timestamp = data.get("timestamp")
        self.event_type = data.get("event_type")

        self.raw_rows = data.get("data", [])
        for row in self.raw_rows:
            pk = row.get("pk")
            if pk not in self.pks:
                self.pks.append(pk)
            self.rows.append(self._decode_row(row))

    def dump(self):
        return {
            "model": self._model_uuid,
            "event_type": self.event_type,
            "data": self.raw_rows,
            "timestamp": self.timestamp
        }


class LemonTable(Table):
    def __sql__(self, ctx):

        ext_model = getattr(self._model, "_ext_model", None)
        if ctx.scope == peewee.SCOPE_VALUES:
            # Return the quoted table name.

            ctx = ctx.sql(peewee.Entity(*self._path))
            # if ext_model is not None:
            #     (ctx.literal(" LEFT OUTER JOIN ").sql(ext_model)
            #     .literal(" ON ").sql(self._model.id==ext_model.relation_id))
            # print(44444, ctx.query())
            return ctx

        if self._alias:
            ctx.alias_manager[self] = self._alias

        if ctx.scope == peewee.SCOPE_SOURCE:
            # print(00000, ctx.query())
            ctx = self.apply_alias(ctx.sql(peewee.Entity(*self._path)))
            # print(1111, ctx.query())
            if ext_model is not None:
                # print(ctx.alias_manager)
                (ctx.literal(" LEFT OUTER JOIN ").sql(ext_model).literal(
                    " ON ").sql(self._model.id == ext_model.relation_id))
                # join = peewee.Join(ctx, self._model.ext_model, join_type=JOIN.RIGHT_OUTER)
                # ctx = join.__sql__(ctx)
            # print(ctx.query())
            # if self._on is not None:
            #     ctx.literal(' ON ').sql(self._on)
            # Define the table and its alias.
            return ctx
            return self.apply_alias(ctx.sql(peewee.Entity(*self._path)))
        else:
            # print(22222111, ctx.query(), ctx.scope, self)
            # Refer to the table using the alias.
            ctx = self.apply_column(ctx)
            # print(22222, ctx.query())
            return ctx


class LemonDesignMetadata(Metadata):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._origin_table_name = self.table_name
        self._table_name = self.table_name
        self._design_table = None

    @property
    def table_name(self):
        current_user = LemonContextVar.current_user.get(None)
        if current_user and current_user.use_design:
            return self._origin_table_name
        return self._table_name

    @table_name.setter
    def table_name(self, name):
        self._table_name = name

    @property
    def schema(self):
        current_user = LemonContextVar.current_user.get(None)
        if current_user and current_user.use_design:
            return 'lemon'
        return self._schema

    @schema.setter
    def schema(self, value):
        self._schema = value
        del self.table

    @property
    def entity(self):
        current_user = LemonContextVar.current_user.get(None)
        if current_user and current_user.use_design:
            return Entity(self.schema, self._origin_table_name)
        if self._schema:
            return Entity(self._schema, self.table_name)
        else:
            return Entity(self.table_name)

    @property
    def table(self):
        current_user = LemonContextVar.current_user.get(None)
        if current_user and current_user.use_design:
            if self._design_table is None:
                self._design_table = Table(
                    self.table_name,
                    [field.column_name for field in self.sorted_fields],
                    schema=self.schema,
                    _model=self.model,
                    _database=self.database)
            return self._design_table
        if self._table is None:
            self._table = Table(
                self.table_name,
                [field.column_name for field in self.sorted_fields],
                schema=self.schema,
                _model=self.model,
                _database=self.database)
        return self._table

    @table.setter
    def table(self, value):
        raise AttributeError('Cannot set the "table".')

    @table.deleter
    def table(self):
        self._table = None


class DesignBaseModel(Model_):
    class Meta:
        model_metadata_class = LemonDesignMetadata


class LemonMetadata(Metadata):

    _expr_class = None

    def __init__(self, *args, **kwargs):
        self.ext_type = 0  # 0非拓展，1依赖拓展 2独立拓展
        super().__init__(*args, **kwargs)
        self._ext = LemonExtFieldAccessor()
        self._ext_model = None
        self._delete_action = collections.defaultdict(tuple)
        self._events = dict()
        self._name_field = None
        self._display_name = kwargs.get("display_name", "")
        self._class_table_name = self.make_table_name()
        self.meta_table_name = kwargs.get("meta_table_name") or self._class_table_name
        # self.flow_table_name = self._class_table_name
        self._is_copy = kwargs.get("is_copy", False)
        self._origin_table_name = kwargs.get("origin_table_name", self.table_name)

    @property
    def table(self):
        if self._table is None:
            self._table = LemonTable(
                self.table_name,
                [field.column_name for field in self.sorted_fields],
                schema=self.schema,
                _model=self.model,
                _database=self.database)
        return self._table

    @table.setter
    def table(self, value):
        raise AttributeError('Cannot set the "table".')

    @table.deleter
    def table(self):
        self._table = None

    @property
    def name_field(self):
        return self._name_field

    @name_field.setter
    def name_field(self, value):
        self._name_field = value

    @property
    def display_name(self):
        return self._display_name

    @display_name.setter
    def display_name(self, value):
        self._display_name = value

    @property
    def class_table_name(self):
        return self._class_table_name

    @property
    def origin_table_name(self):
        return self._origin_table_name

    @origin_table_name.setter
    def origin_table_name(self, value):
        self._origin_table_name = value

    @property
    def is_copy(self):
        return self._is_copy

    @is_copy.setter
    def is_copy(self, value):
        self._is_copy = value

    def add_field(self, field_name, field, set_attribute=True):
        super().add_field(field_name, field, set_attribute)
        if not isinstance(field, peewee.MetaField):
            # 将系统字段添加到 combined 里
            # shortcuts.update_model_from_dict 就能识别 alias 后的系统字段了
            if field.name in SystemField.FIELD_DICT:
                system_field_name = get_sys_field_uuid(self.table_name, field.name)
                self.combined[system_field_name] = field

    def add_ext_field(self, field_name, field, tenant, set_attribute=True):
        ext_model = field.model
        if ext_model not in self.model_exts:
            self.model_exts[ext_model]

    def get_default_dict(self):
        dd = super().get_default_dict()
        # 系统表 不能设置 默认值
        if self.meta_table_name in UUID.META_TABLE_ALL:
            return dd
        engine = GlobalVars.engine
        if engine:
            for field in self.sorted_fields:
                if is_normal_field(field) and isinstance(field, LemonFieldMixin):
                    if is_serial_with_pre_gen_type(field):
                        # 仅当用户在云函数中实例模型类时，才创建流水号
                        if is_main_thread() is False:
                            func = AsyncToSync(make_serial)
                            _, serial_value = func(field, engine)
                            if serial_value:
                                dd[field.name] = serial_value[0]
                    else:
                        # app_log.info(f"field: {field}")
                        default_name, default_value = get_column_default(field, self._expr_class)
                        dd[field.name] = default_value
        return dd


class RuntimeModel(Model_):
    class Meta:
        model_metadata_class = LemonMetadata


class RuntimeBaseModel(RuntimeModel):

    def __init__(self, *args, **kwargs):
        self.__default_data__ = dict()
        self.__default_rel__ = dict()
        self.__snapshot_data__ = dict()
        self.__vars__ = DictWrapper()
        self._v_dirty = set()  # 是否校验过
        self._s_dirty = set()  # 快照
        self._d_dirty = set()  # 值是否发生了改变
        self.__v_dirty__ = dict()
        super().__init__(*args, **kwargs)

    def get_id(self, memory_pk=False):
        pk = super().get_id()
        if not pk and memory_pk is True:
            return get_memory_id(self)
        return pk

    @property
    def dirty_fields(self):
        f_list = []
        for f in self._meta.sorted_fields:
            if f.name in self._dirty or f.name in self._v_dirty:
                f_list.append(f)
        return f_list

    @classmethod
    def add_events(cls, events):
        pass

    @classmethod
    def add_delete_action(
            cls, fk, target, action, from_source=True, r_type=0,
            backref_name=None):
        pass

    @classmethod
    def _validate_field(cls, field):
        return True

    def to_dict(self, recurse=False, fields_from_query=None):
        value_dict = model_to_dict(self, recurse=recurse, fields_from_query=fields_from_query)
        return value_dict

    def get_data(self, name, use_dirty=True, fk=False, memory_pk=False):
        if name == self._meta.primary_key.name:
            pk = self.__data__.get(name)
            if pk is None and memory_pk is True:
                pk = get_memory_id(self)
            return pk
        field = self._meta.fields.get(name)
        valid = self._validate_field(field)
        if valid:
            if fk:
                return self.__rel__.get(name)
            if use_dirty:
                if name in self.__v_dirty__:
                    return self.__v_dirty__.get(name)
            return self.__data__.get(name)
        else:
            value = gen_no_permission_field_value(field)
            return value

    def check_validate_diff(self):
        for name, value in self.__v_dirty__.items():
            self.check_diff(name, value, diff=True)

    def check_diff(self, name, value, diff=False):
        if diff and self.__default_data__:
            # 与首次的赋值对比，不考虑是不是外键字段等因素。
            # 当值只要不一致，则添加到 self._d_dirty ，若一致，则移除
            if isinstance(value, peewee.Model):
                value = value.get_id()
            default_value = self.__default_data__.get(name)
            if isinstance(default_value, peewee.Model):
                default_value = default_value.get_id()
            if value == default_value:
                if name in self._d_dirty:
                    self._d_dirty.remove(name)
            else:
                self._d_dirty.add(name)
        else:
            self._d_dirty.add(name)

    def update_validate(self, validate_data, validate=False, diff=False):
        for name, value in validate_data.items():
            self.__v_dirty__.update({name: value})
            self._v_dirty.add(name)
            self._s_dirty.add(name)
            if validate is False:
                self.check_diff(name, value, diff)

    class Meta():
        database = db_proxy
        legacy_table_names = False
        evolve = False


user_model_common_columns = create_sys_fields()


class RuntimeUserModel(RuntimeBaseModel):

    class Meta():
        is_user_table = True


def gen_user_base_model():
    for key, value in user_model_common_columns.items():
        RuntimeUserModel._meta.add_field(key, value)


gen_user_base_model()


def ast_calc_field(field, expr_type="calc", field_uuid_map=None, max_recurion=0):
    # 分析表达式中参与计算的字段
    max_recurion += 1
    if max_recurion > 20:
        return {}
    field_map = {}
    field_map[field] = [set(), set()]  # (计算字段， 非计算字段)
    model = field.model
    if isinstance(field, peewee.FieldAlias) and not field.is_alias():
        model = field.source
    model_fields = model._meta.sorted_fields
    model_field_names, model_field_column_names, not_calc_fields = set(), dict(), set()
    for _field in model_fields:
        model_field_names.add(_field.name)
        model_field_column_names.update({_field.column_name: _field})
        if is_normal_field(_field, allow_meta_field=True):
            not_calc_fields.add(_field)
    # expr_variable = set()
    placeholders = set()
    expr, expr_attr = None, list()
    calculate_field = getattr(field, "calculate_field", False)
    if calculate_field is True:
        if expr_type == "calc":
            calculate_type = getattr(field, "calculate_type", None)
            app_log.info(f"field: {field}, calculate_type: {calculate_type}")
            if calculate_type == 0:
                if isinstance(field.calculate_function, dict):
                    expr = field.calculate_function.get("expr")
                    expr_attr = field.calculate_function.get("exprArr", list())
            elif calculate_type == 1:
                field_map[field][1] = not_calc_fields
                return field_map
        else:
            expr = field.check_function.get("expr")
            expr_attr = field.check_function.get("exprArr", list())
    if not expr or not expr_attr:
        return field_map

    # root = ast.parse(expr)
    # for node in ast.walk(root):
    #     if isinstance(node, ast.Name):
    #         expr_variable.add(node.id)
    try:
        placeholder_parse(expr, placeholders)
    except (BaseException, Exception):
        app_log.error(traceback.format_exc())
    expr_field = list()
    for attr in expr_attr:
        key = attr.get("key")
        value_editor_dict = attr.get("value")
        value_editor_type = value_editor_dict.get("type")
        if key in placeholders and value_editor_type == ValueEditorType.FIELD:
            value_editor_field = value_editor_dict.get("field")
            field_uuid_map = field_uuid_map or GlobalVars.engine.uuids.field_uuid_map
            column_model = field_uuid_map.get(value_editor_field)
            if column_model:
                # 带 "*" 代表 系统字段
                if "*" in value_editor_field:
                    value_editor_field = value_editor_field.split("*")[-1]
                column = column_model._meta.columns.get(value_editor_field)
                # column = model_field_column_names.get(value_editor_field)
                if column is not None:
                    # expr_variable.add(column.name)
                    expr_field.append(column)

    # expr_field_name = model_field_names & expr_variable
    # expr_field = [getattr(model, name) for name in expr_field_name]
    for field_ in expr_field:
        if getattr(field_, "calculate_field", False) is True:
            field_map[field][0].add(field_)
            field_map.update(ast_calc_field(
                field_, field_uuid_map=field_uuid_map,
                max_recurion=max_recurion))
        else:
            field_map[field][1].add(field_)
    return field_map


def topo_sort(graph):
    in_degrees = dict((u, 0) for u in graph)
    num = len(in_degrees)
    for u in graph:
        for v in graph[u][0]:
            if isinstance(in_degrees.get(v), int):
                in_degrees[v] += 1
            else:
                continue
    Q = [u for u in in_degrees if in_degrees[u] == 0]

    Seq = []
    while Q:
        u = Q.pop()
        Seq.append(u)
        for v in graph[u][0]:
            if in_degrees.get(v):
                in_degrees[v] -= 1
                if in_degrees[v] == 0:
                    Q.append(v)
            else:
                continue
    return Seq if len(Seq) == num else None


class DefaultGlobals:
    import re
    import ujson
    import time

    @classmethod
    def dicts(cls):
        _global = dict()
        _builtins = {
            "peewee": peewee,
            "re": cls.re,
            "json": cls.ujson,
            "time": cls.time,
            "str": str,
            "int": int
        }
        _global.update({"__builtins__": _builtins})
        _global.update({"_getattr_": getattr})
        return _global


class ExprValue:
    def __init__(self, **kwargs) -> None:
        self.expr = kwargs.get("expr", "")
        self.globals = DefaultGlobals.dicts()

    def update_globals(self, **kwargs):
        self.globals.update(kwargs)

    @property
    def value(self):
        compiled = compile_restricted_eval(self.expr)
        value = eval(compiled.code, self.globals, None)
        return value


async def lemon_delete(query):
    """Perform DELETE query asynchronously. Returns number of rows deleted.
    """
    assert isinstance(query, (peewee.Delete, LemonDelete)), \
        ("Error, trying to run delete coroutine"
         "with wrong query class %s" % str(query))

    cursor = await peewee_async._execute_query_async(query)
    rowcount = cursor.rowcount

    await cursor.release()
    return rowcount


class LemonModelSelect(LemonModelQueryHelper, peewee.ModelSelect):

    def __init__(self, model, fields_or_models, is_default=False):
        super(LemonModelSelect, self).__init__(
            model, fields_or_models, is_default)
        self.__returning = self._returning
        self.returning_reset = True
        self.many_to_many_join_map = {}

    def select(self, *fields_or_models):
        if fields_or_models or not self._is_default:
            self._is_default = False
            fields = peewee._normalize_model_select(fields_or_models)
            self = super(peewee.ModelSelect, self).select(*fields)
        return self.model._validate_returning(self)

    def select_extend(self, *columns):
        self = super().select_extend(*columns)
        return self.model._validate_extend_returning(self, *columns)

    @peewee.database_required
    def count(self, database, *, clear_limit=False):
        clone = self.order_by().alias('_wrapped')
        clone = wrapper_tenant_query(clone)  # TODO: 可能还要分析子查询？
        if clear_limit:
            clone._limit = clone._offset = None
        try:
            if clone._having is None and clone._group_by is None and \
               clone._windows is None and clone._distinct is None and \
               clone._simple_distinct is not True:
                clone = clone.select(SQL('1'))
        except AttributeError:
            pass
        clone = peewee.Select([clone], [peewee.fn.COUNT(SQL('1'))]).tuples()
        clone._objs_ = LemonModelQueryHelper._objs_
        clone._main_loop_ = LemonModelQueryHelper._main_loop_
        clone._database = database
        from types import MethodType
        clone._execute = MethodType(LemonModelQueryHelper._execute, clone)
        row = clone.peek(database)
        return row[0]

    @property
    def _returning(self):
        return self.__returning

    @_returning.setter
    def _returning(self, _returning):
        if getattr(self, "returning_reset", None):
            self = self.model._validate_returning(self)
        self.__returning = _returning


class LemonModelInsert(LemonModelQueryHelper, peewee.ModelInsert):

    pass


class LemonModelUpdate(LemonModelQueryHelper, peewee.ModelUpdate):

    pass


class LemonModelDelete(LemonModelQueryHelper, peewee._ModelWriteQueryHelper, LemonDelete):

    pass


class LemonEntity(object):

    def __init__(self, app=None) -> None:
        self._app = app
        self._pubsub = None
        self._expr_class = None
        self._permission_manager = None
        self._preprocess_class = None
        if app is not None:
            self.init_app(app)
        self.func_dict = dict()

    def init_app(self, app, pubsub=None, permission_manager=None, expr_class=None, preprocess_class=None):
        self._app = app
        self._pubsub = pubsub
        self._expr_class = expr_class or ExprValue
        self._permission_manager = permission_manager
        self._preprocess_class = preprocess_class
        if hasattr(self, "_model_class"):
            self._model_class._pubsub = pubsub
            self._model_class._expr_class = self._expr_class
            self._model_class._preprocess_class = self._preprocess_class
            self._model_class._meta.__class__._expr_class = self._expr_class
            # self._model_class._permission_manager = self._permission_manager
        app_log.info("init lemon entity")

    async def notify(
            self, model_uuid=None, pks=None, data=None, action=ModelEventType.UNSUPPORTED, timestamp=int(time.time())):
        if not self._pubsub:
            raise Exception("Error, pubsub not initialized")

        if action == ModelEventType.UNSUPPORTED:
            app_log.debug("no need to notify")
            return
        pub_data = dict(
                        event_type=action, pks=pks, timestamp=timestamp, model=model_uuid
                    )
        if data:
            pub_data["data"] = data
        model_event = ModelEvent(data=pub_data)
        app_log.info(f"pub model_event: {model_event}")
        # await ModelChangeListener.publish(model_event)
        await self._pubsub.publish(model_event)

    def get_expr_instance(self, expr, globals=None, obj=None, lsm=None):
        expr_instance = self._expr_class(**expr)

        if globals:
            expr_instance.update_globals(**globals)
        # 当前 lsm 的 obj 可能跟传进来的 obj 不一致
        # 比如通过 1端模型，计算多端模型数据时，当前的 lsm 是表单
        # 但 obj 却是 多端的模型数据， 所以恢复 lsm 的 source_obj
        if lsm:
            expr_instance.update_globals(**{"lemon": lsm.lemon, "ctx": lsm.lemon.ctx})
            if obj:
                # lsm 是列表，则会传进来；
                # lsm 是表单，则不用传，就用
                lsm.lemon.system.obj = obj
        return expr_instance

    def sync_eval_expr(self, expr, globals=None, obj=None, with_exception=False):
        lsm = LemonContextVar.current_lsm.get()
        source_obj = lsm.lemon.system.obj if lsm else None
        try:
            expr_instance = self.get_expr_instance(expr, globals, obj=obj, lsm=lsm)
            if hasattr(expr_instance, "calc_value"):
                asyncio.run_coroutine_threadsafe(expr_instance.calc_value(), GlobalVars.engine.loop)
            if with_exception:
                value = expr_instance.get_value_with_exception(with_exception=with_exception)
            else:
                value = expr_instance.value
        finally:
            if lsm:
                lsm.lemon.system.obj = source_obj
        return value

    async def async_eval_expr(self, expr, globals=None, obj=None, with_exception=False, _batch_execute=False):
        lsm = LemonContextVar.current_lsm.get()
        source_obj = lsm.lemon.system.obj if lsm else None
        try:
            if _batch_execute:
                return await self.batch_async_eval_expr(expr, obj, globals)
            else:
                expr_instance = self.get_expr_instance(expr, globals, obj=obj, lsm=lsm)
                if hasattr(expr_instance, "calc_value"):
                    # 有可能在云函数中运行此方法
                    if is_main_thread():
                        await expr_instance.calc_value()
                    else:
                        asyncio.run_coroutine_threadsafe(expr_instance.calc_value(), GlobalVars.engine.loop)
                return await expr_instance.get_value_async_with_exception(
                    with_exception=with_exception)
        finally:
            if lsm:
                lsm.lemon.system.obj = source_obj

    async def batch_async_eval_expr(self, expr, objs, globals=None):
        lsm = LemonContextVar.current_lsm.get()
        expr_instances = []
        loop = asyncio.get_running_loop()
        for index, obj in enumerate(objs):
            cur_globals = globals[index] if globals else None
            expr_instance = self.get_expr_instance(expr, cur_globals, obj=obj, lsm=lsm)
            if hasattr(expr_instance, "calc_value"):
                # 有可能在云函数中运行此方法
                if is_main_thread():
                    await expr_instance.calc_value()
                else:
                    asyncio.run_coroutine_threadsafe(expr_instance.calc_value(), GlobalVars.engine.loop)
            expr_instances.append(expr_instance)
        if expr_instances:
            func = partial(expr_instance.__class__.batch_get_value, expr_instances, True, objs)
            return await loop.run_in_executor(CVE, func)
        return []

    async def async_eval_func(self, func_uuid, dict_obj, with_exception=False, _batch_execute=False):

        def _process_error(res):
            if isinstance(res, FuncRunError):
                if with_exception and res.rollback:
                    raise FuncExecError("-1", (res.error_message, res.func_name))
                res = None
            return res

        if func_uuid not in self.func_dict:
            # 有可能在云函数中运行此方法
            if is_main_thread():
                func_wrapper = await GlobalVars.FuncRun.get_func(func_uuid)
            else:
                future = asyncio.run_coroutine_threadsafe(
                    GlobalVars.FuncRun.get_func(func_uuid), GlobalVars.engine.loop)
                func_wrapper = future.result()
            self.func_dict.update({func_uuid: func_wrapper})
        else:
            func_wrapper = self.func_dict.get(func_uuid)
        # app_log.info(f"current_lsm: {LemonContextVar.current_lsm.get()}")
        if _batch_execute:
            func = GlobalVars.FuncRun.batch_run(func_wrapper, None, _batch_execute=_batch_execute, batch_args=dict_obj)
        else:
            func = GlobalVars.FuncRun.run(func_wrapper, dict_obj, _batch_execute=_batch_execute)
        if is_main_thread():
            result = await func
        else:
            future = asyncio.run_coroutine_threadsafe(func, GlobalVars.engine.loop)
            result = future.result()
        if _batch_execute:
            if result != _process_error(result):
                return [None for _ in len(dict_obj)]
            return result
        else:
            return _process_error(result)

    def get_model_class(self):
        return LemonUserModel

    @property
    def Model(self):
        if not hasattr(self, "_model_class"):
            self._model_class = self.get_model_class()
            self._model_class._pubsub = self._pubsub
            self._model_class._expr_class = self._expr_class
            # self._model_class._permission_manager = self._permission_manager
        return self._model_class


lemon_entity = LemonEntity()


class LemonModelAlias(peewee.ModelAlias):

    def select(self, *selection, pk=True):
        if not selection:
            selection = self.get_field_aliases()
        query = LemonModelSelect(self, selection)
        query._query_pk = pk
        query = self.model.post_select(query)
        query = self.model._lemon_filter(query, model=self)
        return query


def get_model_class_from_obj(obj: Optional[Model_]):
    if not obj:
        return None
    return obj.__class__


def get_model_class_from_query(query: Optional[peewee.ModelSelect]):
    if not query:
        return None
    return getattr(query, "model", None)


async def _run_model_event(
        event_data, model_event_type, when=EventWhen.BEFORE, model_obj=None,
        need_throw_exception=True, query=None, model_class=None):
    model_class = model_class or get_model_class_from_obj(model_obj)
    trigger_context = func_proto.ModelEventTrigger(model_event=func_proto.ModelEvent(
        model_class=model_class, event_type=model_event_type, when=when
    ))
    with func_trigger_context(trigger_context):
        if len(event_data) == 3:
            event_func, data_as_param, error_tip = event_data
            event_id = None
        else:
            event_func, data_as_param, error_tip, event_info = event_data
            event_id = event_info.get("event_id")
        start_time = time.time()
        func_wrapper = await GlobalVars.FuncRun.get_func(event_func)
        args = [model_obj] if model_event_type != ModelEventType.SELECT else [query]
        lsm = LemonContextVar.current_lsm.get()
        if lsm:
            lsm.lemon.system.event_id = event_id
        stdout, result = await GlobalVars.FuncRun.run(func_wrapper, *args, need_stdout=True)
        raise_stdout = stdout if need_throw_exception else ""
        if isinstance(result, FuncRunError):
            if isinstance(error_tip, dict) and error_tip:
                eval_globals = {"obj": model_obj}
                message = await lemon_entity.async_eval_expr(
                    error_tip, globals=eval_globals, obj=model_obj)
            else:
                message = f"模型 {model_event_type} 事件执行出错"
            await GlobalVars.engine.elastic_client.model_event_log(
                stdout, func_wrapper, model_event_type, start_time, success=0)
            if result.rollback:
                raise FuncExecError(-1, message, raise_stdout)
        elif result is False and when == EventWhen.BEFORE:
            await GlobalVars.engine.elastic_client.model_event_log(
                stdout, func_wrapper, model_event_type, start_time, success=0)
            raise FuncExecError(-1, f"模型的之前动作 {model_event_type} 执行出错", stdout)
        else:
            await GlobalVars.engine.elastic_client.model_event_log(
                "", func_wrapper, model_event_type, start_time, success=1)
        if model_event_type == ModelEventType.SELECT:
            if when == EventWhen.BEFORE and isinstance(result, peewee.Query):
                return result
            return result
        return query


def _run_model_event_by_sync(event_data, model_event_type, when=EventWhen.BEFORE, model_obj=None, query=None):
    event_func, data_as_param, error_tip = event_data
    start_time = time.time()
    func_wrapper = GlobalVars.FuncRun.get_func_by_sync(event_func)
    args = [model_obj] if model_event_type != ModelEventType.SELECT else [query]
    stdout, result = GlobalVars.FuncRun.run_sync(func_wrapper, *args, need_stdout=True)
    if isinstance(result, FuncRunError):
        if isinstance(error_tip, dict) and error_tip:
            eval_globals = {"obj": model_obj}
            message = lemon_entity.sync_eval_expr(
                error_tip, globals=eval_globals, obj=model_obj)
        else:
            message = f"模型 {model_event_type} 事件执行出错"
        asyncio.run_coroutine_threadsafe(GlobalVars.engine.elastic_client.model_event_log(
            stdout, func_wrapper, model_event_type, start_time, success=0), GlobalVars.engine.loop)
        if result.rollback:
            raise FuncExecError(-1, message, stdout)
    elif result is False and when == EventWhen.BEFORE:
        asyncio.run_coroutine_threadsafe(GlobalVars.engine.elastic_client.model_event_log(
            stdout, func_wrapper, model_event_type, start_time, success=0), GlobalVars.engine.loop)
        raise FuncExecError(-1, f"模型的之前动作 {model_event_type} 执行出错", stdout)
    else:
        asyncio.run_coroutine_threadsafe(GlobalVars.engine.elastic_client.model_event_log(
            "", func_wrapper, model_event_type, start_time, success=1), GlobalVars.engine.loop)
    if model_event_type == ModelEventType.SELECT and isinstance(result, peewee.Query):
        return result
    return query


async def run_model_events(
        events, model_event_type, when=EventWhen.BEFORE, models=None, query=None,
        need_throw_exception=True, model_class=None):
    if isinstance(models, (list, tuple)) and models:
        query_data = None
        if isinstance(query, (peewee.Update, LemonModelUpdate)):
            query_data = query._update
        elif isinstance(query, (peewee.Insert, LemonModelInsert)):
            query_data = query._insert
        for index, model_obj in enumerate(models):
            if model_obj is not None:
                model_obj.clear_snapshot()
            for event_data in events:
                if (res := await _run_model_event(
                        event_data, model_event_type, when, model_obj, need_throw_exception=need_throw_exception,
                        query=query, model_class=model_class
                )) is not None:
                    query = res
            snapshot_data = model_obj.get_data_from_snapshot(key_as_field=True, need_many=False)
            app_log.info(f"snapshot_data: {snapshot_data}")
            if isinstance(query_data, dict):
                query_data.update(snapshot_data)
            elif isinstance(query_data, list) and query_data:
                query_data[index].update(snapshot_data)
    else:
        for event_data in events:
            if (res := await _run_model_event(
                event_data, model_event_type, when, need_throw_exception=need_throw_exception,
                query=query, model_class=model_class
            )) is not None:
                query = res
    return query


def run_model_events_by_sync(events, model_event_type, when=EventWhen.BEFORE, models=None):
    if isinstance(models, (list, tuple)) and models:
        for model_obj in models:
            for event_data in events:
                _run_model_event_by_sync(event_data, model_event_type, when, model_obj)
    else:
        for event_data in events:
            _run_model_event_by_sync(event_data, model_event_type, when)


class LemonUserModel(RuntimeUserModel):

    _pubsub = lemon_entity._pubsub
    _expr_class = lemon_entity._expr_class
    _preprocess_class = lemon_entity._preprocess_class
    # _permission_manager = lemon_entity._permission_manager

    _pre_modify_action_list = []
    _post_modify_action_list = []
    # _ext = LemonExtFieldAccessor()
    # _ext_model = None
    # _delete_action = defaultdict(list)

    def __init__(self, *args, run_model_event=True, **kwargs):
        self.model_events = {}
        self._run_model_event = run_model_event
        main_thread = is_main_thread()
        if self._run_model_event and main_thread is False:
            self.model_events = self.get_model_events()
        if main_thread is False:
            self.run_pre_events()
        super().__init__(*args, **kwargs)
        self._select_query_dict = dict()
        self._v_dirty_backref_set = set()
        self._v_replace_backref_set = set()
        self._dirty_backref_set = set()
        self._dirty_many_to_many_set = set()
        self._v_dirty_many_to_many_set = set()
        self._default_backref_set = set()
        self._model = self._meta.model
        # 级联保存时，判断由 “哪个模型对象” 发起的
        # 如果 “发起的这个模型对象” 在 backref.all 中，则忽略它
        self._parent_model_obj = None
        if main_thread is False:
            self.run_post_events()
        self.save_to_default()
        # self._add_instance()

    def _add_instance(self, force_update=True, check_is_diff=False):
        if force_update:
            current_connector = LemonContextVar.current_connector.get()
            if current_connector:
                if self._pk:
                    key = "_".join([self._meta.table_name, str(self._pk)])
                    update_to_instances = True
                    db_instances = current_connector.instances.db
                    instance = db_instances.get(key, None)
                    if check_is_diff and instance and instance.check_is_diff():
                        update_to_instances = False
                    if update_to_instances:
                        current_connector.instances.db.update({key: self})
                        current_connector.instances.memory.update({key: self})
                else:
                    current_connector.instances.memory.update({id(self): self})

    @classmethod
    def _check_meta_is_temp(cls):
        if cls._meta._is_temp:
            raise RuntimeError("临时存储模型，无法执行查询、插入、更新、删除")

    def get_model_events(self):
        context_model_events = LemonContextVar.context_model_events.get()
        model_uuid = self._meta.origin_table_name
        model_events = context_model_events.get(model_uuid)
        if model_events is not None:
            return model_events
        query = get_model_events_sql(self)
        model_events = collect_model_events(query.execute())
        context_model_events.update({model_uuid: model_events})
        return model_events

    def run_pre_events(self):
        if self._run_model_event is True:
            model_event_type = ModelEventType.CREATE
            pre_events = get_type_events(self, model_event_type)
            pre_model_events = get_model_events(self.model_events, model_event_type)
            pre_events = pre_events + pre_model_events
            run_model_events_by_sync(pre_events, model_event_type)

    def run_post_events(self):
        if self._run_model_event is True:
            model_event_type = ModelEventType.CREATE
            post_events = get_type_events(self, model_event_type, when=EventWhen.AFTER)
            post_model_events = get_model_events(
                self.model_events, model_event_type, when=EventWhen.AFTER)
            post_events = post_events + post_model_events
            run_model_events_by_sync(post_events, model_event_type, models=[self])

    @ClassProperty
    @classmethod
    def _permission_manager(cls):
        engine = getattr(GlobalVars, "engine", None)
        if engine:
            return getattr(GlobalVars.engine, "pm", None)
        return None

    @ClassProperty
    @classmethod
    def ext(cls):
        return cls._meta._ext

    @ClassProperty
    @classmethod
    def _ext_model(cls):
        return cls._meta._ext_model

    @ClassProperty
    @classmethod
    def _delete_action(cls):
        return cls._meta._delete_action

    @ClassProperty
    @classmethod
    def _events(cls):
        return cls._meta._events

    @classmethod
    async def _pre_modify(cls, event):
        for action in cls._pre_modify_action_list:
            func_wrapper = GlobalVars.FuncRun.get_func(action)
            result = await GlobalVars.FuncRun.run(func_wrapper, event)
            if not result:
                return False
        return True

    @classmethod
    async def _post_modify(cls, event):
        for action in cls._pre_modify_action_list:
            func_wrapper = GlobalVars.FuncRun.get_func(action)
            await GlobalVars.FuncRun.run(func_wrapper, event)

    @classmethod
    def _predicate_accessor_get(cls, resource_id):
        # app_log.debug(f"predicate accessor get <- {resource_id}")
        if resource_id:
            cls._permission_manager.predicate(resource_id, PermissionAction.SELECT)

    @classmethod
    def _predicate_accessor_set(cls, resource_id):
        app_log.debug(f"predicate accessor set -> {resource_id}")
        if resource_id:
            cls._permission_manager.predicate(resource_id, PermissionAction.UPDATE)

    @classmethod
    def alias(cls, alias=None):
        return LemonModelAlias(cls, alias)

    @classmethod
    def _process_field_permission(cls, query, *columns):

        new_returning = list()
        if not columns:
            # select_extend
            columns = copy(query._returning)
            new_returning.extend(columns)
        for field in columns:
            if isinstance(field, peewee.Alias):
                field_unalias = field.unalias()
            else:
                field_unalias = field
            # app_log.debug(field_unalias)
            if isinstance(field_unalias, peewee.Field):
                valid = cls._validate_field(field_unalias)
                if valid:
                    new_returning.append(field)
                else:
                    query._no_permission.add(field)
            elif isinstance(field_unalias, peewee.Column):
                if isinstance(field_unalias.source, peewee.Query):
                    source_query = cls._validate_returning(
                        field_unalias.source)
                    field_unalias.source = source_query
                    new_returning.append(field)
            elif isinstance(field_unalias, peewee.Function):
                # TODO 分析function里使用的字段，或者把字段权限校验放到field里去
                arguments = getattr(field_unalias, "arguments", tuple())
                for arg in arguments:
                    valid = cls._validate_field(arg)
                    if not valid:
                        # 认为args中如果有任一字段没权限,该field无权限
                        query._no_permission.add(field)
                        break
                else:
                    # TODO 这里并不能保证field是有权限的
                    app_log.debug(f"{field_unalias} not certain parsed")
                    new_returning.append(field)
            else:
                app_log.debug(f"{field_unalias} not parsed")
                new_returning.append(field)
        query.__returning = new_returning
        return query

    @classmethod
    def _validate_returning(cls, query):
        query._no_permission = set()
        return cls._process_field_permission(query)

    @classmethod
    def _validate_extend_returning(cls, query, *columns):
        return cls._process_field_permission(query, *columns)

    @classmethod
    def _validate_field(cls, field):
        valid = True
        if not isinstance(field, peewee.Field):
            return valid
        resource_id = field.column_name
        if resource_id in SystemField.FIELD_NAME_LIST + ["tenant_uuid"]:
            return valid
        try:
            if cls._permission_manager:
                if field.model and inspect.isclass(field.model) and not issubclass(field.model, LemonUserModel):
                    return True
                if resource_id in UUID.sys_model_resource_ids:
                    # app_log.info(f"sys_model_resource_id: {resource_id}, SELECT TRUE")
                    return True
                system_keys = list(user_model_common_columns.keys())
                system_keys.append("externpk")
                if resource_id in system_keys:
                    resource_id = field.model._meta.table_name + "*" + resource_id
                cls._permission_manager.predicate(resource_id, PermissionAction.SELECT)
        except LemonPermissionError:
            # select之后的value标记为 无权限
            valid = False
        return valid

    @classmethod
    def post_select(cls, query):
        query = cls._validate_returning(query)
        return query

    @classmethod
    def _unzip_join(cls, node, nodes_list=None):
        if nodes_list is None:
            nodes_list = list()
        # app_log.info(node.rhs)
        # app_log.info(node.lhs)

        nodes_list.insert(0, node.rhs)
        if isinstance(node.lhs, peewee.Join):
            cls._unzip_join(node.lhs, nodes_list)
        else:
            nodes_list.insert(0, node.lhs)
        # app_log.info(nodes_list)
        return nodes_list

    def _check_backref_is_diff(self):
        for name in self._v_dirty_backref_set:
            backref = getattr(self, name)
            if backref.is_diff:
                return True
        return False

    def _check_many_to_many_is_diff(self):
        for name in self._v_dirty_many_to_many_set:
            backref = getattr(self, name)
            if backref.is_diff:
                return True
        return False

    def check_is_diff(self):
        if (
            self._d_dirty or
            self._check_backref_is_diff() or
            self._check_many_to_many_is_diff()
        ):
            return True
        return False

    @classmethod
    def select(cls, *fields, pk=True):
        # todo: join权限
        is_default = not fields
        if not fields:
            fields = default_select_fields(cls)
            if cls._ext_model:
                ext_fields = []
                for field in cls._ext_model._meta.sorted_fields:
                    if field.name not in ["relation_id", "id"]:
                        ext_fields.append(field)
                fields = fields + ext_fields
        # query = peewee.ModelSelect(cls, fields, is_default=is_default)
        query = LemonModelSelect(cls, fields, is_default=is_default)
        query._query_pk = pk
        # query = super().select(*fields)
        query = cls.post_select(query)
        query = cls._lemon_filter(query)
        return query

    @classmethod
    def bulk_update(cls, model_list, fields, batch_size=None, update_modify_when_dirty=False):
        if isinstance(cls._meta.primary_key, peewee.CompositeKey):
            raise ValueError('bulk_update() is not supported for models with a composite primary key.')

        # First normalize list of fields so all are field instances.
        fields = [cls._meta.fields[f] if isinstance(f, str) else f for f in fields]
        # Now collect list of attribute names to use for values.
        attrs = [field.object_id_name if isinstance(field, peewee.ForeignKeyField) else field.name for field in fields]

        if batch_size is not None:
            batches = peewee.chunked(model_list, batch_size)
        else:
            batches = [model_list]

        n = 0
        for batch in batches:
            id_list = [model._pk for model in batch]
            update = {}
            for field, attr in zip(fields, attrs):
                accum = []
                for model in batch:
                    value = getattr(model, attr)
                    if not isinstance(value, peewee.Node):
                        value = field.to_value(value)
                    accum.append((model._pk, value))
                case = peewee.Case(cls._meta.primary_key, accum)
                update[field] = case

            n += (cls.update(update, update_modify_when_dirty=update_modify_when_dirty).where(
                cls._meta.primary_key.in_(id_list)).execute())
        return n

    @classmethod
    def update(
            cls, __data=None, notify=False, run_model_event=True,
            model_obj=None, update_modify_when_dirty=False, **update):
        # cls._check_meta_is_temp()
        current_user = get_current_user()
        user_uuid = "" if current_user is None else current_user.uuid
        user_pk = 0 if current_user is None else current_user.user_pk
        for key in user_model_common_columns:
            if key in update:
                if key not in ["_status", "_wf_status"]:  # TODO 这样无法限制用户编辑系统字段
                    update.pop(key)

        # 需求是：用户未更新子表单 或 子表 的数据时，不更新 修改人 信息
        # 以传入的 update_modify_when_dirty 为准
        update_modify_info = True
        if update_modify_when_dirty is True:
            update_modify_info = False
            if model_obj is not None:
                if model_obj.check_is_diff():
                    update_modify_info = True
        if update_modify_info:
            update['_modified_time'] = int(time.time())
            update['_last_modified_by'] = user_uuid
            update['_reviser'] = user_pk
        # query = peewee.ModelUpdate(cls, cls._normalize_data(__data, update, with_ext=False))
        query = LemonModelUpdate(cls, cls._normalize_data(__data, update, with_ext=False))
        query._notify = notify
        query._run_model_event = run_model_event
        query._model_obj = model_obj
        query._update_modify_info = update_modify_info
        if current_user is None:  # TODO 通过current_user来判断不限制当前租户可能有问题
            setattr(query, "select_all", True)
        if cls._ext_model:
            query._ext = LemonModelUpdate(cls._ext_model, __data, **update)
            query._ext._run_model_event = run_model_event
            query._ext._notify = notify
            query._ext._update_modify_info = update_modify_info
        resource_id_list = []
        for field in query._update:
            if field.name not in [
                    "_modified_time", "_last_modified_by", "_reviser", "id"]:
                resource_id = field.column_name
                if resource_id in user_model_common_columns.keys():
                    resource_id = query.model._meta.table_name + "*" + resource_id
                resource_id_list.append(resource_id)
        new_update_ = copy(query._update)
        for resource_id in resource_id_list:
            if cls._permission_manager:
                try:
                    cls._permission_manager.predicate(resource_id, PermissionAction.UPDATE)
                except LemonPermissionError:
                    [new_update_.pop(field) for field in query._update if field.column_name == resource_id]
        query._update = new_update_  # 当update的全部字段都无update权限,仍然update了一次
        return query

    @classmethod
    def _normalize_data(cls, data, kwargs, with_ext=True):
        normalized = {}
        if data:
            if not isinstance(data, dict):
                if kwargs:
                    raise ValueError('Data cannot be mixed with keyword '
                                     'arguments: %s' % data)
                return data
            for key in data:
                try:
                    if isinstance(key, peewee.Field):
                        if key.name in cls._meta.fields:
                            field = key
                        else:
                            continue
                    else:
                        field = cls._meta.combined[key]
                except KeyError:
                    if with_ext:
                        field = cls._ext_model._meta.combined[key]
                    else:
                        continue
                normalized[field] = data[key]
        if kwargs:
            for key in kwargs:
                try:
                    # 流水号字段，必须有值
                    # 下面的处理，是为了防止已经 insert 流水号数据
                    # 但 update 更新为空
                    # TODO: 根本的原因还是 表单的模型对象 与 表单数据没有同步
                    # TODO: 重构模型对象后，注意这里，是不是可以去掉了
                    field = cls._meta.combined[key]
                    if getattr(field, "is_serial", None) and not kwargs[key]:
                        continue
                    normalized[field] = kwargs[key]
                except KeyError:
                    try:
                        normalized[getattr(cls, key)] = kwargs[key]
                    except (BaseException, Exception):
                        if with_ext:
                            normalized[getattr(cls._ext_model, key)] = kwargs[key]
                        else:
                            continue
        return normalized

    @classmethod
    def insert(cls, __data=None, notify=False, run_model_event=True, model_obj=None, **insert):
        # cls._check_meta_is_temp()
        resource_id = cls._meta.table_name
        cls._permission_manager.predicate(resource_id, PermissionAction.INSERT)
        # query = peewee.ModelInsert(cls, cls._normalize_data(__data, insert, with_ext=False))

        query = LemonModelInsert(cls, cls._normalize_data(__data, insert, with_ext=False))
        query._notify = notify
        query._run_model_event = run_model_event
        query._model_obj = model_obj
        current_user = get_current_user()
        if current_user is None:  # TODO 通过current_user来判断不限制当前租户可能有问题
            setattr(query, "select_all", True)
        if cls._ext_model:
            # query._ext = peewee.ModelInsert(cls._ext_model, cls._ext_model._normalize_data(
            #     __data, insert, with_ext=False))
            query._ext = LemonModelInsert(
                cls._ext_model, cls._ext_model._normalize_data(__data, insert, with_ext=False))
            query._ext._notify = notify
            query._ext._run_model_event = run_model_event
        return query

    @classmethod
    def insert_many(cls, rows, fields=None, notify=False, run_model_event=True):
        # cls._check_meta_is_temp()
        query = LemonModelInsert(cls, insert=rows, columns=fields)
        query._notify = notify
        query._run_model_event = run_model_event
        return query

    @classmethod
    def replace_many(cls, rows, fields=None, notify=False, run_model_event=True):
        return (cls
                .insert_many(
                    rows=rows, fields=fields, notify=notify,
                    run_model_event=run_model_event)
                .on_conflict('REPLACE'))

    @classmethod
    def delete(cls, notify=False, run_model_event=True, model_obj=None):
        # cls._check_meta_is_temp()
        resource_id = cls._meta.table_name
        cls._permission_manager.predicate(resource_id, PermissionAction.DELETE)
        # if cls._ext_model:
        #     query = peewee.Join(
        #         cls, cls._ext_model, join_type=peewee.JOIN.LEFT_OUTER,
        #         on=(cls._meta.primary_key==cls._ext_model.relation_id))

        query = LemonModelDelete(cls)
        query._notify = notify
        query._run_model_event = run_model_event
        query._model_obj = model_obj
        app_log.info(f"========================{query}")
        # if cls._ext_model:
        #     query._ext = cls._ext_model.delete()
        return query

    def save_to_data(self):
        for key, value in self.__v_dirty__.items():
            if key in self.__data__:
                setattr(self, key, value)

    def save(
            self, force_insert=False, only=None, notify=False,
            run_model_event=True, cascade_run_model_event=True,
            save_to_default=True,
            update_modify_when_dirty=False,
            cascade_update_modify_when_dirty=False):
        # TODO 这里没办法。。只能复制一遍了
        # bug 1689 一对多，多端保存时先将一端数据保存
        # for fk_obj in self.__rel__.values():
        #     if fk_obj.id is None:
        #         fk_obj.save()
        # self._check_meta_is_temp()
        app_log.info(f"self.__data__: {self.__data__}")
        field_dict = self.__data__.copy()
        if self._meta.primary_key is not False:
            pk_field = self._meta.primary_key
            pk_value = self._pk
        else:
            pk_field = pk_value = None
        if only:
            field_dict = self._prune_fields(field_dict, only)
        elif self._meta.only_save_dirty and not force_insert:
            field_dict = self._prune_fields(field_dict, self.dirty_fields)
            if not field_dict:
                self._dirty.clear()
                return False

        self._populate_unsaved_relations(field_dict)
        rows = 1

        if pk_value is not None and not force_insert:
            if self._meta.composite_key:
                for pk_part_name in pk_field.field_names:
                    field_dict.pop(pk_part_name, None)
            else:
                field_dict.pop(pk_field.name, None)
            if not field_dict:
                raise ValueError('no data to save!')
            rows = self.update(
                notify=notify, run_model_event=run_model_event, model_obj=self,
                update_modify_when_dirty=update_modify_when_dirty, **field_dict
            ).where(self._pk_expr()).execute()
            self._add_instance(force_update=True)
        elif pk_field is not None:
            pk = self.insert(
                notify=notify, run_model_event=run_model_event,
                model_obj=self, **field_dict).execute()
            if pk is not None and (self._meta.auto_increment or
                                   pk_value is None):
                if not getattr(self._meta, "is_temp", False):
                    self._pk = pk
                atomic_instances = LemonContextVar.atomic_instances.get()
                atomic_instances.add(self)
                self._add_instance(force_update=True)
        else:
            self.insert(
                notify=notify, run_model_event=run_model_event,
                model_obj=self, **field_dict).execute()

        for backref_name in self._dirty_backref_set:
            backref = getattr(self, backref_name)
            # if rows == 0:
            #     app_log.info(f"backref_name: {backref_name} cleared, rows: {rows}")
            #     backref.clear()
            #     backref.validate_clear()
            for obj in backref.all:
                if self._parent_model_obj is obj:
                    continue
                obj.save(
                    run_model_event=cascade_run_model_event,
                    update_modify_when_dirty=cascade_update_modify_when_dirty,
                    cascade_update_modify_when_dirty=cascade_update_modify_when_dirty
                )
            for obj in backref.remove_all:
                if self._parent_model_obj is obj:
                    continue
                obj_pk = obj.get_id()
                if obj_pk is None:
                    continue
                obj_exist = obj._model.select(obj._model.id).where(obj._pk_expr()).execute()
                if not obj_exist:
                    continue
                setattr(obj, backref._accessor.field.name, None)
                obj.save(
                    run_model_event=cascade_run_model_event,
                    update_modify_when_dirty=cascade_update_modify_when_dirty,
                    cascade_update_modify_when_dirty=cascade_update_modify_when_dirty
                )
            # backref.clear()
            # backref.validate_clear()
        for backref_name in self._dirty_many_to_many_set:
            backref = getattr(self, backref_name)
            # 多对多需要保存中间表
            src_fk = backref.raccessor.src_fk
            dest_fk = backref.raccessor.dest_fk
            through_model = backref.raccessor.field.through_model
            through_model.delete().where(src_fk == self).execute()
            insert_data_list = []
            for obj in backref.all:
                if self._parent_model_obj is obj:
                    continue
                obj.save(
                    run_model_event=run_model_event,
                    update_modify_when_dirty=update_modify_when_dirty
                )
                insert_data_list.append({src_fk: self, dest_fk.name: obj})
            if insert_data_list:
                through_model.insert_many(insert_data_list).execute()
            # backref.clear()
            # backref.validate_clear()

        if save_to_default:
            self.save_to_default()
        # 强制从数据库中更新一次全部生成列的值
        generated_fields = []
        for field in self._model._meta.sorted_fields:
            if is_generated_field(field):
                generated_fields.append(field)
        if generated_fields:
            new_obj = self._model.select(*generated_fields).where(self._pk_expr()).execute()
            if new_obj:
                for field in generated_fields:
                    field_value = getattr(new_obj[0], field.name)
                    setattr(self, field.name, field_value)
                    app_log.info(
                        f"after save, update generated field: {field.name}, value: {field_value}")
        return rows

    def clear(self):
        self._dirty_backref_set.clear()
        self._dirty_many_to_many_set.clear()
        for backref_name in self._dirty_backref_set:
            backref = getattr(self, backref_name)
            [obj.clear() for obj in backref.all]
            [obj.clear() for obj in backref.remove_all]
        for backref_name in self._dirty_many_to_many_set:
            backref = getattr(self, backref_name)
            [obj.clear() for obj in backref.all]

    def clear_dirty(self):
        self._dirty_backref_set.clear()
        self._dirty_many_to_many_set.clear()
        self._dirty.clear()
        self._d_dirty.clear()

    def clear_validate(self):
        self._v_dirty_backref_set.clear()
        self._v_dirty_many_to_many_set.clear()
        self._v_replace_backref_set.clear()
        self.__v_dirty__.clear()
        self._v_dirty.clear()
        for backref_name in self._default_backref_set:
            backref = getattr(self, backref_name)
            backref._is_default_set.clear()
            backref._default_backrefs.clear()
        self._default_backref_set.clear()

    def clear_snapshot(self):
        self._s_dirty.clear()

    def take_snapshot_data(self):
        self.__snapshot_data__.clear()
        # self.__snapshot_data__.update(self.__data__)
        for backref_name in self._dirty_backref_set:
            backref = getattr(self, backref_name)
            self.__snapshot_data__.setdefault(backref_name, list())
            [self.__snapshot_data__[backref_name].append(obj) for obj in backref.all]
        for backref_name in self._dirty_many_to_many_set:
            backref = getattr(self, backref_name)
            self.__snapshot_data__.setdefault(backref_name, list())
            [self.__snapshot_data__[backref_name].append(obj) for obj in backref.all]

    def save_to_default(self):
        self.__default_data__.update(self.__data__)
        self.__default_rel__.update(self.__rel__)
        self.clear_dirty()
        self.clear_validate()

    def load_default_data(self):
        for name in self._v_dirty:
            if name in self.__default_data__:
                setattr(self, name, self.__default_data__[name])

        for backref_name in self._dirty_backref_set:
            backref = getattr(self, backref_name)
            backref.clear()
            backref.validate_clear()
        for backref_name in self._dirty_many_to_many_set:
            backref = getattr(self, backref_name)
            backref.clear()
            backref.validate_clear()

    def get_data_from_snapshot(self, key_as_field=False, need_many=True):
        data = {}
        for name in self._s_dirty:
            if (
                name in self._dirty_backref_set or
                name in self._dirty_many_to_many_set
            ):
                if need_many is False:
                    continue
                backref = getattr(self, name)
                value = [obj.get_id(memory_pk=True) for obj in backref.all]
            else:
                field = getattr(self._meta.model, name)
                if is_calc_field(field, calculated_column_is_calc=True) or is_primary_key(field):
                    continue
                value = self.get_data(name)
                if isinstance(value, peewee.Model):
                    value = value.get_id(memory_pk=True)
            if key_as_field:
                field = getattr(self._meta.model, name, None)
                if field:
                    data.update({field: value})
            else:
                data.update({name: value})
        return data

    def load_snapshot_data(self):
        for name in self._v_dirty:
            field = getattr(self._meta.model, name, None)
            if is_foreign_key(field):
                obj = self.get_data(name)
                setattr(self, name, obj)
        for backref_name in self._dirty_backref_set:
            backref = getattr(self, backref_name)
            if backref_name in self.__snapshot_data__:
                backref.replace(self.__snapshot_data__[backref_name])
            else:
                backref.clear()
                backref.validate_clear()
        for backref_name in self._dirty_many_to_many_set:
            backref = getattr(self, backref_name)
            if backref_name in self.__snapshot_data__:
                backref.replace(self.__snapshot_data__[backref_name])
            else:
                backref.clear()
                backref.validate_clear()

    def cancel(self):
        for backref_name in self._v_dirty_backref_set:
            backref = getattr(self, backref_name)
            backref.cancel()
        for backref_name in self._v_dirty_many_to_many_set:
            backref = getattr(self, backref_name)
            backref.cancel()
        self.clear_validate()
        self.clear_dirty()

    @classmethod
    def add_delete_action(
            cls, fk, target, action, from_source=True, r_type=0,
            backref_name=None):
        # 外键： （受影响的model ，删除行为）
        # 1：外键在当前model时，fk：（fk.rel_model{即target}, action）   2:外键不在当前model时    fk: (fk.model{即source}, action)
        cls._meta._delete_action.setdefault(fk, dict())
        cls._meta._delete_action[fk].update({
            from_source: (target, action, r_type, backref_name)
        })

    @classmethod
    def add_events(cls, events):
        for event in events:
            event_type = event.get("type")
            event_call_at = event.get("call_at")
            event_func = event.get("func")
            event_data_as_param = event.get("data_as_param")
            event_error_tip = event.get("error_tip")
            event_type_data = cls._meta._events.setdefault(event_type, dict())
            event_call_at_data = event_type_data.setdefault(event_call_at, list())
            event_call_at_data.append((event_func, event_data_as_param, event_error_tip))

    async def async_save(self, notify=False):
        query = self.insert(**dict(self.__data__))
        pk = await execute(query, notify=notify)
        if self._pk is None:
            self._pk = pk
        return self

    @classmethod
    def _lemon_filter(cls, query, model=None):
        filter_ = ()
        if cls._permission_manager:
            filter_ = cls._permission_manager.filter(cls._meta.table_name)
        if filter_:
            if model is None:
                model = cls
            # 避免循环引入
            from runtime.core.data_preprocess import DataPreprocess
            lsm = LemonContextVar.current_lsm.get()
            if lsm:
                preprocess = DataPreprocess(model, {"condition": filter_}, lsm=lsm)
                query = preprocess(query)
        return query

    @classmethod
    async def validate_field(cls, field, *, pk=None, data=None, obj=None):
        """
        pk 非None，表示更新校验, data 非空
        pk 为None，表示插入校验, data 非空
        ·凡哥说：计算字段从需求上讲没有校验的需求，校验表达式里也不会有计算字段·
        如果校验表达式里有计算字段，可能要改代码结构。。在python_value和db_value去计算
        """

        if not isinstance(field, LemonFieldMixin):
            return (False, "field type not support check.")
        if field.calculate_field:
            return (False, "计算字段不能校验")
        if not field.check_function and not field.is_unique:
            return (True, "")

        try:
            data = {} if data is None else data
            if field.is_unique:
                new_value = data[field.column_name]
                # 这里用 dicts 查询的方式，是因为不带 dicts 会 _add_instances
                # 但这里并没有查全部字段，所以模型对象的某些字段就没有数据
                fields = [field, field.model.id]
                query = field.model.select(*fields).where(field == new_value).dicts()
                res = await execute(query)
                if res and (len(res) > 1 or res[0].get("id") != pk):
                    return (False, f"({field.name}:{new_value}) 已存在")
            row_data = {}
            for f in cls._meta.sorted_fields:
                if is_normal_field(f):
                    column_name = f.column_name
                    if column_name not in data:
                        column_data = obj.get_data(f.name)
                        row_data.update({column_name: column_data})
            data.update(row_data)
            if field.check_function:
                if not field.check_expr_instance:
                    field.check_expr_instance = cls._expr_class(**field.check_function)
                    field.check_expr_instance.default_globals = copy(field.check_expr_instance.globals)
                    field.check_expr_instance.expr_field_map = ast_calc_field(field, expr_type="check")
                    if field.check_expr_instance.expr_field_map[field][0]:
                        return (False, "校验表达式里不能有计算字段")
                    field.check_expr_instance.expr_actual_field = set([
                        field_ for fields in field.check_expr_instance.expr_field_map.values() for field_ in fields[1]])
                else:
                    field.check_expr_instance.clear()
                    field.check_expr_instance.globals = copy(field.check_expr_instance.default_globals)
                # app_log.debug(field.check_expr_instance.expr_actual_field)
                for field_ in field.check_expr_instance.expr_actual_field:
                    if field_.name not in data and field_.null:
                        data.update({field_.column_name: field_.default})
                # app_log.debug(data)
                if not data:
                    return (False, field.check_error_message)
                # app_log.debug(data)
                lsm = LemonContextVar.current_lsm.get()
                if lsm:
                    field.check_expr_instance.set_globals(lsm)
                field.check_expr_instance.update_globals(**data)
                result = await field.check_expr_instance.value_async
                message = field.check_error_message
                return (result, message)
        except (BaseException, Exception):
            app_log.error(traceback.format_exc())
            return (False, field.check_error_message)
        else:
            return (True, "")

    @classmethod
    async def to_json(cls, query, **kwargs):
        res = await execute(query.dicts())
        return ujson.dumps(list(res), **kwargs)

    @classmethod
    async def from_json(cls, data, strict=False, alias=None):
        data = ujson.loads(data)
        columns = cls._meta.columns
        columns.update(cls._meta.fields)
        # app_log.debug(columns)
        calc_field_map = {}
        for field in columns.values():
            if getattr(field, "calculate_field", False) is True:
                calc_field_map.update(ast_calc_field(field))
        if calc_field_map:
            calc_field_sorted = topo_sort(calc_field_map)[::-1]
        else:
            calc_field_sorted = []
        if isinstance(data, dict):
            data = [data]

        obj_list = list()
        for row in data:

            if strict:
                obj = {}
                for key in row:
                    field = columns.get(key)
                    if field is not None:
                        if getattr(field, "calculate_field", False) is False:
                            value = field.python_value(row[key])
                            obj[field.name] = value
                            obj[field.column_name] = value
            else:
                obj = row
            if obj:
                for field in calc_field_sorted:
                    if field.calculate_type == 0:
                        expr_instance = cls._expr_class(**field.calculate_function)
                        expr_instance.update_globals(**obj)
                        value = expr_instance.value
                    else:
                        # TODO: 云函数时如何处理？
                        value = None
                    # todo: globals: {"field_column_name": value}
                    obj[field.name] = value
                    obj[field.column_name] = value
                inst = cls(**obj)
                obj_list.append(inst)
                # query = cls.insert(**obj)
                # await execute(query)
        return obj_list


class UserModel(lemon_entity.Model):

    id = LemonAutoField(**{"is_system": True})


class User(RuntimeBaseModel):
    # user uuid使用自增主键，blowfish/aes加密传出去
    user_uuid = LemonCharField(
        max_length=32, unique=True, constraints=[SQL("comment 'user_uuid'")],
        aka="user_uuid", column_name=UUID.model_user.user_uuid.value)
    user_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'user_name'")],
        aka="user_name", column_name=UUID.model_user.user_name.value)
    mobile_phone = LemonCharField(
        max_length=20, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'mobile_phone'")],
        aka="mobile_phone", column_name=UUID.model_user.mobile_phone.value)
    password_salt = LemonCharField(
        max_length=8, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'password_salt'")],
        aka="password_salt", column_name=UUID.model_user.password_salt.value)
    password_hash = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'password_hash'")],
        aka="password_hash", column_name=UUID.model_user.password_hash.value)
    avatar = LemonCharField(
        null=True, max_length=255, default=None, constraints=[SQL('DEFAULT NULL'), SQL("comment 'avatar'")],
        aka="avatar", column_name=UUID.model_user.avatar.value)
    wechat_id = LemonCharField(
        null=True, max_length=32, default=None, constraints=[SQL('DEFAULT NULL'), SQL("comment 'wechat_id'")],
        aka="wechat_id", column_name=UUID.model_user.wechat_id.value)
    wechat_name = LemonCharField(
        null=True, max_length=30, default=None, constraints=[SQL('DEFAULT NULL'), SQL("comment 'wechat_name'")],
        column_name=UUID.model_user.wechat_name.value)
    full_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'full_name'")], aka="full_name",
        column_name=UUID.model_user.full_name.value)
    created_at = LemonIntegerField(
        default=int(time.time()), constraints=[SQL("comment 'created_at'")],
        aka="created_at", column_name=UUID.model_user.created_at.value)
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_delete'")],
        aka="is_delete", column_name=UUID.model_user.is_delete.value)
    is_test = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_test'")],
        aka="is_test", column_name=UUID.model_user.is_test.value)
    is_job_number = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_job_number'")],
        aka="is_job_number", column_name=UUID.model_user.is_job_number.value)  # 字段弃用,无意义
    tenant_uuid = LemonCharField(
        max_length=32, unique=False, constraints=[SQL('DEFAULT ""'), SQL("comment 'tenant_uuid'")],
        aka="tenant_uuid", column_name=UUID.model_user.tenant_uuid.value)
    publisher_name = LemonCharField(
        max_length=30, null=True, constraints=[SQL("comment 'publisher_name'")],
        aka="full_name", column_name=UUID.model_user.publisher_name.value)
    publisher = LemonIntegerField(
        default=None, null=True, constraints=[SQL("comment 'publisher'")],
        aka="publisher", column_name=UUID.model_user.publisher.value)
    gitlab_token = LemonCharField(
        max_length=30, null=True, constraints=[SQL("comment 'gitlab token'")],
        aka="gitlab_token", column_name=UUID.model_user.gitlab_token.value)
    login_as = LemonCharField(
        max_length=32, null=True, default=None, constraints=[SQL("comment 'login_as'"), SQL('DEFAULT NULL')],
        aka="login_as", column_name=UUID.model_user.login_as.value)

    class Meta():
        aka = "user"
        table_name = UUID.model_user.table_name.value
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("mobile_phone", "tenant_uuid"), False),
            (("user_name", "tenant_uuid"), False),
        )


class Department(RuntimeBaseModel):

    tenant_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'tenant_uuid'")],
        aka="tenant_uuid", column_name=UUID.model_department.tenant_uuid.value)
    department_uuid = LemonCharField(
        max_length=32, unique=True, aka="department_uuid", constraints=[SQL("comment 'department_uuid'")],
        column_name=UUID.model_department.department_uuid.value)
    department_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'department_name'")],
        aka="department_name", column_name=UUID.model_department.department_name.value)
    department_level = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'department_level'")],
        aka="department_level", column_name=UUID.model_department.department_level.value)
    superior = LemonCharField(
        null=True, max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'superior'")],
        aka="superior", column_name=UUID.model_department.superior.value)
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_delete'")],
        aka="is_delete", column_name=UUID.model_department.is_delete.value)
    department = LemonCharField(
        default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'department'")],
        column_name=UUID.model_department.department.value,
        hierarchy_field=1, hierarchy_list=[{"field": UUID.model_department.department_uuid.value}])

    class Meta():
        aka = "department"
        table_name = UUID.model_department.table_name.value
        schema = "lemon_tenant_center"
        evolve = True
        indexes = (
            (("tenant_uuid", ), False),
        )


class TenantUser(UserModel):
    user_id = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'user_id'")],
        is_unique=True, is_required=False, verbose_name="外部标识符", null=True,
        column_name=UUID.tenant_user.user_id.value)  # 可填或自动生成uuid，跟随组织导入变化
    user_uuid = LemonCharField(
        max_length=32, default=lemon_uuid, constraints=[SQL('DEFAULT ""'), SQL("comment 'user_uuid'")],
        is_unique=True, is_required=True, verbose_name="唯一标识符",
        column_name=UUID.tenant_user.user_uuid.value)  # 唯一，自动生成，不可修改
    lemon_user_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'lemon_user_uuid'")],
        is_unique=True, is_required=True,
        column_name=UUID.tenant_user.lemon_user_uuid.value)  # 柠檬平台的 user_uuid
    full_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'full_name'")],
        is_required=True, verbose_name="姓名", column_name=UUID.tenant_user.full_name.value)  # 姓名, 等价User表FullName
    job_number = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'job_number'")],
        verbose_name="工号", column_name=UUID.tenant_user.job_number.value)  # 工号
    mobile_phone = LemonCharField(
        null=True, max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'mobile_phone'")],
        verbose_name="手机号", column_name=UUID.tenant_user.mobile_phone.value, aka="mobile_phone")  # 手机号
    email = LemonCharField(
        null=True, max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'email'")],
        verbose_name="邮箱", column_name=UUID.tenant_user.email.value, aka="email")  # 邮箱
    created_at = LemonDateTimeField(
        default=time.time,  constraints=[SQL('DEFAULT 0'), SQL("comment 'created_at'")],
        verbose_name="添加时间", column_name=UUID.tenant_user.created_at.value)  # 添加时间
    password_salt = LemonCharField(
        max_length=8, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'password_salt'")],
        aka="password_salt", column_name=UUID.tenant_user.password_salt.value)
    password_hash = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'password_hash'")],
        aka="password_hash", column_name=UUID.tenant_user.password_hash.value)
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_delete'")],
        column_name=UUID.tenant_user.is_delete.value)

    class Meta():
        aka = UUID.tenant_user.display_name.value
        model_name = UUID.tenant_user.display_name.value
        meta_table_name = "tenant_user"
        schema = "lemon_tenant_center"
        table_name = UUID.tenant_user.table_name.value
        evolve = True
        is_sys_table = True
        module_name = "系统模块"
        indexes = (
            (("user_uuid", ), True),
            (("full_name", ), False),
            (("job_number", ), True),
            (("mobile_phone", ), False),
        )

    @hybrid_property
    def 用户id(self):
        return self.user_id

    @用户id.setter
    def 用户id(self, value):
        self.user_id = value

    @hybrid_property
    def 姓名(self):
        return self.full_name

    @姓名.setter
    def 姓名(self, value):
        self.full_name = value

    @hybrid_property
    def 工号(self):
        return self.job_number

    @工号.setter
    def 工号(self, value):
        self.job_number = value

    @hybrid_property
    def 手机号(self):
        return self.mobile_phone

    @手机号.setter
    def 手机号(self, value):
        self.mobile_phone = value

    @hybrid_property
    def 邮箱(self):
        return self.email

    @邮箱.setter
    def 邮箱(self, value):
        self.email = value

    @hybrid_property
    def 添加时间(self):
        return self.created_at

    @添加时间.setter
    def 添加时间(self, value):
        self.created_at = value

    @hybrid_property
    def 唯一标识符(self):
        return self.user_uuid

    @唯一标识符.setter
    def 唯一标识符(self, value):
        self.user_uuid = value

    @hybrid_property
    def 外部标识符(self):
        return self.user_id

    @外部标识符.setter
    def 外部标识符(self, value):
        self.user_id = value


class BaseTenantMember(UserModel):
    member_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_admin = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_active = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        schema = "lemon_tenant_center"
        evolve = True
        is_sys_table = True
        indexes = (
            (("member_uuid", "tenant_uuid"), False),
        )


class TenantDepartment(UserModel):
    department_uuid = LemonCharField(
        max_length=32, unique=True, aka="department_uuid", constraints=[SQL("comment 'department_uuid'")],
        is_unique=True, is_required=True, default=lemon_uuid, verbose_name="部门id",
        column_name=UUID.tenant_department.department_uuid.value)
    department_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'department_name'")],
        is_required=True, verbose_name="部门名称", column_name=UUID.tenant_department.department_name.value)
    department_level = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'department_level'")],
        column_name=UUID.tenant_department.department_level.value)
    上级部门 = LemonSystemForeignKeyField(
        'self', backref='子部门', constraints=[SQL("comment '自关联外键'")], null=True, on_delete="CASCADE",
        verbose_name="上级部门", column_name=UUID.tenant_department.parent.value)  # 自关联外键
    department_id = LemonCharField(
        max_length=32, unique=True, aka="department_id", constraints=[SQL("comment 'department_id'")],
        is_unique=True, verbose_name="外部id", null=True,
        column_name=UUID.tenant_department.department_id.value)
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_delete'")], aka="is_delete",
        column_name=UUID.tenant_department.is_delete.value)

    @hybrid_property
    def parent(self):
        return self.上级部门

    @parent.setter
    def parent(self, value):
        self.上级部门 = value

    @hybrid_property
    def 部门名称(self):
        return self.department_name

    @部门名称.setter
    def 部门名称(self, value):
        self.department_name = value

    @hybrid_property
    def 部门id(self):
        return self.department_uuid

    @部门id.setter
    def 部门id(self, value):
        self.department_uuid = value

    @hybrid_property
    def 外部id(self):
        return self.department_id

    @外部id.setter
    def 外部id(self, value):
        self.department_id = value

    class Meta():
        aka = UUID.tenant_department.display_name.value
        model_name = UUID.tenant_department.display_name.value
        meta_table_name = "tenant_department"
        schema = "lemon_tenant_center"
        table_name = UUID.tenant_department.table_name.value
        module_name = "系统模块"
        evolve = True
        is_sys_table = True
        indexes = (
        )


class TenantDepartmentMember(UserModel):
    成员 = LemonSystemForeignKeyField(
        TenantUser, backref="系统用户部门表", verbose_name="成员", null=True,
        column_name=UUID.tenant_department_member.department.value, on_delete="CASCADE")  # 包含成员
    部门 = LemonSystemForeignKeyField(
        TenantDepartment, backref="系统用户部门表", verbose_name="部门", null=True,
        column_name=UUID.tenant_department_member.user.value, on_delete="CASCADE")  # 所在部门
    job = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'job'")],
        column_name=UUID.tenant_department_member.job.value, verbose_name="职务")
    is_admin = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'is_admin'")],
        column_name=UUID.tenant_department_member.is_admin.value, verbose_name="管理员")  # 0 普通成员 1 子管理员 2 主管
    is_major = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_major'")],
        column_name=UUID.tenant_department_member.is_major.value, verbose_name="主部门")
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_delete'")],
        column_name=UUID.tenant_department_member.is_delete.value)

    @hybrid_property
    def user(self):
        return self.成员

    @user.setter
    def user(self, value):
        self.成员 = value

    @hybrid_property
    def department(self):
        return self.部门

    @department.setter
    def department(self, value):
        self.部门 = value

    @hybrid_property
    def 职务(self):
        return self.job

    @职务.setter
    def 职务(self, value):
        self.job = value

    @hybrid_property
    def 主部门(self):
        return self.is_major

    @主部门.setter
    def 主部门(self, value):
        self.is_major = value
    
    @hybrid_property
    def 管理员(self):
        return self.is_admin

    @管理员.setter
    def 管理员(self, value):
        self.is_admin = value

    class Meta():
        aka = UUID.tenant_department_member.display_name.value
        model_name = UUID.tenant_department_member.display_name.value
        meta_table_name = "tenant_department_member"
        schema = "lemon_tenant_center"
        table_name = UUID.tenant_department_member.table_name.value
        module_name = "系统模块"
        evolve = True
        is_sys_table = True
        indexes = (
            (("成员", "部门", ), False),
        )


def is_method_defined_in_subclass(method, cls):
    # 获取定义方法的模块
    method_module = method.__code__.co_filename
    # 获取传入的类的模块
    cls_module = cls.__module__

    # 检查方法和类的模块是否相同
    # 如果相同，并且方法名称不在基类中，则可以认为是子类独有的
    return (method_module == cls_module and
            not hasattr(cls.__base__, method.__name__))


# 检查属性是通过子类定义的还是继承自父类
def is_property_overridden(property_name, child_cls, parent_cls):
    child_prop = getattr(child_cls, property_name)
    parent_prop = getattr(parent_cls, property_name, None)

    if parent_prop is not None:
        app_log.debug(f"parent_prop: {parent_prop}")

    # 检查属性的getter方法是否相同
    return parent_prop is None or child_prop.fget != parent_prop.fget


class CurrentUserBase:

    def init_vars(self, tenant_uuid, session_dict) -> None:

        self.__tenant_id = tenant_uuid
        self.__user_pk = session_dict.get("id")
        self.__uuid = session_dict.get("user_uuid")
        self.__temp_user = session_dict.get("temp_user", False)
        self.__user_name = session_dict.get("user_name")
        self.__app_uuid = session_dict.get("app_uuid")
        self.__timezone = session_dict.get("timezone", 8)  # TODO 目前session中没有timezone信息
        # 外部填写需要token发送到ucenter获取角色的toke
        self.__token = session_dict.get("token", "")
        self.__access_key = session_dict.get("access_key", False)
        self.__device_type = session_dict.get("device_type", 0)
        self.__is_anonymous = session_dict.get("is_anonymous", False)
        self.__roles = {}
        self.__departments = {}
        self.__is_sys_admin = False
        self.__department_admin = []

        # 是否使用设计时数据
        # 生产环境禁止使用设计时数据
        if os.environ.get("APP_ENV_NAME", "pro").rstrip(";") == 'pro':
            self.__use_design = False
        else:
            if __use_design := session_dict.get("__use_design", None):
                self.__use_design = __use_design
            else:
                use_design = session_dict.get("_use_design", [])
                current_env_info = [self.__app_uuid, session_dict.get("current_env", None), tenant_uuid]
                self.__use_design = current_env_info in use_design

        tenants = session_dict.get("tenants", {})
        if tenants:
            current_tenant = tenants.get(tenant_uuid, {})
        else:
            current_tenant = session_dict
        self.__roles = current_tenant.get("roles", {})
        self.__roles_permission = current_tenant.get("roles_permission", {})
        self.__all_tag_permission = current_tenant.get("all_tag_permission", {})
        self.__departments = current_tenant.get("departments", {})
        self.__is_sys_admin = current_tenant.get("is_sys_admin", False)
        self.__department_admin = current_tenant.get("department_admin", [])
        self.__module_role = current_tenant.get("module_role", None)

    @property
    def user_pk(self):
        return self.__user_pk

    @property
    def uuid(self):
        return self.__uuid

    @property
    def user_name(self):
        return self.__user_name

    @property
    def app_uuid(self):
        return self.__app_uuid

    @property
    def timezone(self):
        return self.__timezone

    @property
    def roles(self):
        return self.__roles

    @roles.setter
    def roles(self, value):
        self.__roles = value

    @property
    def roles_permission(self):
        return self.__roles_permission

    @property
    def all_tag_permission(self):
        return self.__all_tag_permission

    @property
    def departments(self):
        return self.__departments

    @property
    def is_anonymous(self):
        return self.__is_anonymous

    @property
    def is_sys_admin(self):
        return self.__is_sys_admin

    @property
    def tenant_id(self):
        return self.__tenant_id

    @property
    def department_admin(self):
        return self.__department_admin

    @property
    def token(self):
        return self.__token

    @property
    def access_key(self):
        return self.__access_key

    @property
    def device_type(self):
        return self.__device_type

    @property
    def use_design(self):
        return self.__use_design

    @property
    def temp_user(self):
        return self.__temp_user

    @property
    def module_role(self):
        return self.__module_role

    def as_dict(self):

        value_dict = {}
        value_dict.update(self.to_dict())
        value_dict.update({
            "id": self.user_pk,
            "tenant_uuid": self.tenant_id,
            "roles": self.roles})
        return value_dict

    @classmethod
    async def from_dict(cls, current_user_dict: dict):
        tenant_uuid = current_user_dict.get("tenant_uuid")
        return await CurrentUserModel.make_instance(tenant_uuid, current_user_dict)


class CurrentUserModel:

    @classmethod
    async def make_instance(cls, tenant_uuid, session_dict):
        inst = None
        model_class = None
        if tenant_uuid:
            pk = session_dict.get("id")
            tenant_user_model = SYSTEM_MODEL_DICT.get("系统用户表")
            model_class = make_tenant_sys_table_copy_signle(tenant_user_model, tenant_uuid)
            for name in dir(CurrentUserBase):
                # TODO 我还是需要明确知道需要拷贝哪些方法
                if not name.startswith("__") and callable(getattr(CurrentUserBase, name)):
                    setattr(model_class, name, getattr(CurrentUserBase, name))
                if isinstance(getattr(CurrentUserBase, name), property):
                    is_overridden = is_property_overridden(name, CurrentUserBase, object)
                    if is_overridden:
                        setattr(model_class, name, getattr(CurrentUserBase, name))
        if model_class:
            if isinstance(session_dict, dict) and session_dict.get("id"):
                query = model_class.select().where(model_class.id == pk)
                inst = await execute(query)
                inst = inst[0]
            else:
                inst = model_class()
        else:
            inst = CurrentUserBase()
            inst.user_uuid = session_dict.get("user_uuid")  # TODO 先这样写, 因为CurrentUser中的user_uuid可能被手动指定
        inst.init_vars(tenant_uuid, session_dict)
        inst.tenant_uuid = tenant_uuid
        return inst

    @classmethod
    async def from_dict(cls, current_user_dict: dict):
        tenant_uuid = current_user_dict.get("tenant_uuid")
        return await cls.make_instance(tenant_uuid, current_user_dict)


class CurrentUserModel2(TenantUser):

    def init_vars(self, tenant_uuid, session_dict) -> None:

        self.__tenant_id = tenant_uuid
        self.__user_pk = session_dict.get("id")
        self.__uuid = session_dict.get("user_uuid")
        self.__user_name = session_dict.get("user_name")
        self.__app_uuid = session_dict.get("app_uuid")
        self.__timezone = session_dict.get(
            "timezone", 8)  # TODO 目前session中没有timezone信息
        # 外部填写需要token发送到ucenter获取角色的toke
        self.__token = session_dict.get("token", "")
        self.__access_key = session_dict.get("access_key", False)
        self.__device_type = session_dict.get("device_type", 0)
        self.__roles = {}
        self.__departments = {}
        self.__is_sys_admin = False
        self.__department_admin = []

        # 是否使用设计时数据
        # 生产环境禁止使用设计时数据
        if os.environ.get("APP_ENV_NAME", "pro").rstrip(";") == 'pro':
            self.__use_design = False
        else:
            if __use_design := session_dict.get("__use_design", None):
                self.__use_design = __use_design
            else:
                use_design = session_dict.get("_use_design", [])
                current_env_info = [self.__app_uuid, session_dict.get("current_env", None), tenant_uuid]
                self.__use_design = current_env_info in use_design

        tenants = session_dict.get("tenants", {})
        if tenants:
            current_tenant = tenants.get(tenant_uuid, {})
        else:
            current_tenant = session_dict
        self.__roles = current_tenant.get("roles", {})
        self.__roles_permission = current_tenant.get("roles_permission", {})
        self.__all_tag_permission = current_tenant.get("all_tag_permission", {})
        self.__departments = current_tenant.get("departments", {})
        self.__is_sys_admin = current_tenant.get("is_sys_admin", False)
        self.__department_admin = current_tenant.get("department_admin", [])
        self.__locked = True  # TODO locked的声明必须在最后

    @classmethod
    def lemon_model(cls, tenant_user_uuid, tenant_uuid):
        """
        没有实际意义，只是占位，需要在运行时替换成可用的 lemon_model
        """
        pass

    @classmethod
    async def make_instance(cls, tenant_uuid, session_dict):

        if isinstance(session_dict, dict) and session_dict.get("id") and tenant_uuid:
            pk = session_dict.get("id")
            user_model = cls.lemon_model(SystemTable.tenant_user_uuid, tenant_uuid)
            if user_model and user_model._meta.is_copy:
                json_data = await user_model.to_json(query=user_model.select().where(user_model.id == pk))
                cls_list = await cls.from_json(json_data)
                instance = cls_list[0]
                instance.init_vars(tenant_uuid, session_dict)
                return instance
        instance = super().__new__(cls)
        instance.__init__(run_model_event=False)
        instance.user_uuid = session_dict.get("user_uuid")  # TODO 先这样写, 因为CurrentUser中的user_uuid可能被手动指定
        instance.init_vars(tenant_uuid, session_dict)
        return instance

    def unlock(self):
        self.locked = False

    def lock(self):
        self.locked = True

    @property
    def locked(self):
        return self.__locked

    @locked.setter
    def locked(self, value):
        self.__locked = value

    def __setattr__(self, name, value):
        if hasattr(self, "_CurrentUserModel__locked") and self.locked:
            raise Exception("Cannot modify attribute when locked")
        super().__setattr__(name, value)

    @property
    def user_pk(self):
        return self.__user_pk

    @property
    def uuid(self):
        return self.__uuid

    @property
    def user_name(self):
        return self.__user_name

    @property
    def app_uuid(self):
        return self.__app_uuid

    @property
    def timezone(self):
        return self.__timezone

    @property
    def roles(self):
        return self.__roles

    @property
    def roles_permission(self):
        return self.__roles_permission

    @property
    def all_tag_permission(self):
        return self.__all_tag_permission

    @property
    def departments(self):
        return self.__departments

    @property
    def is_sys_admin(self):
        return self.__is_sys_admin

    @property
    def tenant_id(self):
        return self.__tenant_id

    @property
    def tenant_uuid(self):
        return self.__tenant_id

    @tenant_uuid.setter
    def tenant_uuid(self, value):
        self.__tenant_id = value

    @property
    def department_admin(self):
        return self.__department_admin

    @property
    def token(self):
        return self.__token

    @property
    def access_key(self):
        return self.__access_key

    @property
    def device_type(self):
        return self.__device_type

    @property
    def use_design(self):
        return self.__use_design

    def as_dict(self):

        value_dict = {}
        value_dict.update(self.to_dict())
        value_dict.update({
            "tenant_uuid": self.tenant_id, "id": self.user_pk,
            "roles": self.roles})
        return value_dict

    @classmethod
    async def from_dict(cls, current_user_dict: dict):
        tenant_uuid = current_user_dict.get("tenant_uuid")
        return await cls.make_instance(tenant_uuid, current_user_dict)


class CurrentDepartment(TenantDepartment):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.job = kwargs.get("job")


class ModelBasic(UserModel):
    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.app_uuid.value)
    module_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.module_uuid.value)
    document_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.document_uuid.value)
    model_uuid = LemonCharField(
        max_length=32, default="", verbose_name="模型uuid", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.model_basic.model_uuid.value)
    model_name = LemonCharField(
        max_length=30, default="", verbose_name="模型名称", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.model_basic.model_name.value)
    display_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic._display_name.value)
    description = LemonCharField(
        max_length=255, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.description.value)
    is_temp = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')], column_name=UUID.model_basic.is_temp.value)
    fields = LemonJsonField(
        null=True, column_name=UUID.model_basic.fields.value)
    events = LemonJsonField(
        null=True, column_name=UUID.model_basic.events.value)
    position = LemonJsonField(
        null=True, column_name=UUID.model_basic.position.value)
    name_field = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.name_field.value)
    data_field = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.data_field.value)
    sort_field = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.sort_field.value)
    check_field = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.check_field.value)
    data_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.data_name.value)
    user_model = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.user_model.value)
    department_model = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.department_model.value)
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')], column_name=UUID.model_basic.is_delete.value)
    ext_tenant = LemonCharField(
        max_length=32, default=None, constraints=[SQL('DEFAULT ""')],
        null=True, column_name=UUID.model_basic.ext_tenant.value)
    is_import = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')], column_name=UUID.model_basic.is_import.value)
    import_binding = LemonJsonField(
        null=True, column_name=UUID.model_basic.import_binding.value)
    user_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.model_basic.user_uuid.value)
    branch_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.model_basic.branch_uuid.value)
    is_sys_model = LemonBooleanField(
        default=False, verbose_name="是否系统模型", constraints=[SQL('DEFAULT False')],
        column_name=UUID.model_basic.is_sys_model.value)
    select_enabled = LemonBooleanField(
        default=True, constraints=[SQL('DEFAULT True')], column_name=UUID.model_basic.select_enabled.value)

    class Meta():
        display_name = UUID.model_basic.display_name.value
        model_name = display_name
        module_name = "系统模块"
        ignore_tenant = True
        table_function = make_table_name
        evolve = True
        select_enabled = False
        version_control = True
        sys_table = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("model_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )

    @hybrid_property
    def 模型uuid(self):
        return self.model_uuid

    @hybrid_property
    def 模型名称(self):
        return self.model_name

    @hybrid_property
    def 是否系统模型(self):
        return self.is_sys_model


class Func(UserModel):
    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.app_uuid.value)
    module_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.module_uuid.value)
    document_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.document_uuid.value)
    func_uuid = LemonCharField(
        max_length=32, default="", verbose_name="云函数uuid", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.func.func_uuid.value)
    func_name = LemonCharField(
        max_length=30, default="", verbose_name="云函数名称", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.func.func_name.value)
    func = LemonMediumTextField(
        null=False, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.func.value)
    description = LemonCharField(
        max_length=255, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.description.value)
    icon_type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0')], column_name=UUID.func.icon_type.value)
    icon = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.icon.value)
    install_sm_toolbox = LemonBooleanField(
        default=True, constraints=[SQL('DEFAULT True')], column_name=UUID.func.install_sm_toolbox.value)
    sm_tool_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.sm_tool_name.value)
    sm_tool_category = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.sm_tool_category.value)
    arg_list = LemonJsonField(
        null=True, column_name=UUID.func.arg_list.value)
    return_list = LemonJsonField(
        null=True, column_name=UUID.func.return_list.value)
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')], column_name=UUID.func.is_delete.value)
    user_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.user_uuid.value)
    branch_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.func.branch_uuid.value)
    is_sys_func = LemonBooleanField(
        default=False, verbose_name="是否系统云函数", constraints=[SQL('DEFAULT False')],
        column_name=UUID.func.is_sys_func.value)
    from_py_module = LemonBooleanField(
        default=False, null=False, constraints=[SQL('DEFAULT False')],
        column_name=UUID.func.from_py_module.value
    )
    py_module_line_no = LemonIntegerField(
        default=0, null=False, constraints=[SQL('DEFAULT 0')],
        column_name=UUID.func.py_module_line_no.value
    )
    front_module = LemonCharField(
        max_length=30, default="", null=True, constraints=[SQL('DEFAULT NULL')], column_name=UUID.func.front_module.value)


    class Meta():
        display_name = UUID.func.display_name.value
        model_name = display_name
        module_name = "系统模块"
        ignore_tenant = True
        table_function = make_table_name
        evolve = True
        select_enabled = False
        version_control = True
        sys_table = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("func_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )

    @hybrid_property
    def 云函数uuid(self):
        return self.func_uuid

    @hybrid_property
    def 云函数名称(self):
        return self.func_name

    @hybrid_property
    def 是否系统云函数(self):
        return self.is_sys_func


class ModelCloudFunction(UserModel):
    call_at = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="事件类型",
        column_name=UUID.model_cloud_function.call_at.value)
    when = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="调用时间",
        column_name=UUID.model_cloud_function.func_type.value)
    error_tip = LemonJsonField(
        null=True, column_name=UUID.model_cloud_function.error_tip.value)
    data_as_param = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT "0"')],
        column_name=UUID.model_cloud_function.data_as_param.value)
    other_info = LemonJsonField(
        null=True, column_name=UUID.model_cloud_function.other_info.value)
    to_model = LemonSystemForeignKeyField(
        ModelBasic, backref="模型事件", verbose_name="模型", null=True,
        column_name=UUID.model_cloud_function.to_model.value, on_delete="CASCADE")
    to_func = LemonSystemForeignKeyField(
        Func, backref="模型事件", verbose_name="事件", null=True,
        column_name=UUID.model_cloud_function.to_func.value, on_delete="CASCADE")

    class Meta():
        display_name = UUID.model_cloud_function.display_name.value
        model_name = display_name
        sys_table = True
        evolve = True
        module_name = "系统模块"
        select_enabled = False
        version_control = True

    @hybrid_property
    def 事件类型(self):
        return self.call_at

    @事件类型.setter
    def 事件类型(self, value):
        self.call_at = value

    @hybrid_property
    def 调用时间(self):
        return self.when

    @调用时间.setter
    def 调用时间(self, value):
        self.when = value

    @hybrid_property
    def 数据对象(self):
        return self.data_as_param

    @数据对象.setter
    def 数据对象(self, value):
        self.data_as_param = value


class UserRole(UserModel):
    # 用户角色
    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_userrole.app_uuid.value)
    document_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_userrole.document_uuid.value)
    role_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="角色标识符",
        column_name=UUID.lemon_userrole.role_uuid.value)
    role_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""')], verbose_name="角色名",
        column_name=UUID.lemon_userrole.role_name.value)
    order = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0')], verbose_name="顺序",
        column_name=UUID.lemon_userrole.order.value)
    is_admin = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')], verbose_name="是否系统管理员",
        column_name=UUID.lemon_userrole.is_admin.value)
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')],
        column_name=UUID.lemon_userrole.is_delete.value)
    user_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_userrole.user_uuid.value)
    branch_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_userrole.branch_uuid.value)

    class Meta():
        display_name = UUID.lemon_userrole.display_name.value
        model_name = display_name
        module_name = "系统模块"
        ignore_tenant = True
        table_function = make_table_name
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid", "role_uuid"), False),
            (("role_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )

    @hybrid_property
    def 角色标识符(self):
        return self.role_uuid

    @hybrid_property
    def 角色名(self):
        return self.role_name

    @hybrid_property
    def 顺序(self):
        return self.order

    @hybrid_property
    def 是否系统管理员(self):
        return self.is_admin


class Resource(UserModel):
    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_resource.app_uuid.value)
    module_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_resource.module_uuid.value)
    document_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_resource.document_uuid.value)
    resource_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_resource.resource_uuid.value,
        verbose_name="分组唯一标识符")
    resource_name = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_resource.resource_name.value,
        verbose_name="分组名称")
    superior_resource = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL')],
        column_name=UUID.lemon_resource.superior_resource.value)
    resource_type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0')], verbose_name="类型",
        column_name=UUID.lemon_resource.resource_type.value)
    description = LemonCharField(
        max_length=255, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_resource.description.value,
        verbose_name="描述")
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')], column_name=UUID.lemon_resource.is_delete.value)

    class Meta:
        display_name = UUID.lemon_resource.display_name.value
        model_name = display_name
        module_name = "系统模块"
        ignore_tenant = True
        table_function = make_table_name
        evolve = True
        version_control = True
        sys_table = True
        is_sys_table = True
        indexes = (
            (("resource_uuid", ), True),
            (("resource_name", ), False),
            (("app_uuid",), False),
        )

    @hybrid_property
    def 分组唯一标识符(self):
        return self.resource_uuid

    @hybrid_property
    def 分组名称(self):
        return self.resource_name

    @hybrid_property
    def 类型(self):
        return self.resource_type

    @hybrid_property
    def 描述(self):
        return self.description


class Tag(UserModel):
    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_tag.app_uuid.value)
    module_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_tag.module_uuid.value)
    document_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_tag.document_uuid.value)
    resource_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_tag.resource_uuid.value)
    resource_id = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL'), SQL("comment 'resource_id'")],
        column_name=UUID.lemon_tag.resource_id.value,
        aka="resource_id")
    tag_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_tag.tag_uuid.value,
        verbose_name="标签唯一标识符")
    tag_name = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_tag.tag_name.value,
        verbose_name="标签名")
    action = LemonCharField(
        max_length=255, default="", constraints=[SQL('DEFAULT ""')], column_name=UUID.lemon_tag.action.value,
        verbose_name="操作")
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')], column_name=UUID.lemon_tag.is_delete.value)
    #  TODO  has_permission = action == all_action & action

    class Meta:
        display_name = UUID.lemon_tag.display_name.value
        model_name = display_name
        module_name = "系统模块"
        ignore_tenant = True
        table_function = make_table_name
        evolve = True
        version_control = True
        sys_table = True
        is_sys_table = True
        indexes = (
            (("resource_uuid", ), False),
            (("tag_uuid", ), True),
            (("tag_name", ), False),
            (("app_uuid",), False),
            (("document_uuid",), False),
        )

    @hybrid_property
    def 标签唯一标识符(self):
        return self.tag_uuid

    @hybrid_property
    def 标签名(self):
        return self.tag_name

    @hybrid_property
    def 操作(self):
        return self.action


class PermissionConfig(UserModel):

    to_role = LemonSystemForeignKeyField(
        UserRole, backref="角色标签", verbose_name="角色", null=True,
        column_name=UUID.permission_config.to_role.value, on_delete="CASCADE")
    to_tag = LemonSystemForeignKeyField(
        Tag, backref="标签角色", verbose_name="标签", null=True,
        column_name=UUID.permission_config.to_tag.value, on_delete="CASCADE")

    class Meta:
        display_name = UUID.permission_config.display_name.value
        model_name = display_name
        sys_table = True
        is_sys_table = True
        ignore_tenant = False
        evolve = True
        module_name = "系统模块"
        version_control = True
        indexes = (
            (("to_role", "to_tag", ), False),
        )


class Navigation(UserModel):
    app_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                              column_name=UUID.lemon_navigation.app_uuid.value)
    document_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                                   column_name=UUID.lemon_navigation.document_uuid.value)
    navigation_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                                     column_name=UUID.lemon_navigation.navigation_uuid.value,
                                     verbose_name="导航唯一标识符")
    platform = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')],
                                 column_name=UUID.lemon_navigation.platform.value,
                                 verbose_name="平台")
    navigation_style = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')],
                                         column_name=UUID.lemon_navigation.navigation_style.value,
                                         verbose_name="导航样式")
    position = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')],
                                 column_name=UUID.lemon_navigation.position.value)
    mainpage = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                              column_name=UUID.lemon_navigation.mainpage.value,
                              verbose_name="主页")
    mainpage_title = LemonCharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')],
                                    column_name=UUID.lemon_navigation.mainpage_title.value)
    userpage = LemonJsonField(null=True, column_name=UUID.lemon_navigation.userpage.value)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')],
                             column_name=UUID.lemon_navigation.is_delete.value)
    user_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                               column_name=UUID.lemon_navigation.user_uuid.value)
    branch_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                                 column_name=UUID.lemon_navigation.branch_uuid.value)
    active_key = LemonJsonField(null=True, default=list(),
                                column_name=UUID.lemon_navigation.active_key.value)

    class Meta:
        display_name = UUID.lemon_navigation.display_name.value
        model_name = display_name
        module_name = "系统模块"
        ignore_tenant = True
        table_function = make_table_name
        evolve = True
        version_control = True
        sys_table = True
        is_sys_table = True
        indexes = (
            (("app_uuid",), False),
            (("navigation_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )

    @hybrid_property
    def 导航唯一标识符(self):
        return self.navigation_uuid

    @hybrid_property
    def 平台(self):
        return self.platform

    @hybrid_property
    def 导航样式(self):
        return self.navigation_style

    @hybrid_property
    def 主页(self):
        return self.mainpage


class NavigationItem(UserModel):
    app_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                              column_name=UUID.lemon_navigationitem.app_uuid.value)
    document_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                                   column_name=UUID.lemon_navigationitem.document_uuid.value)
    navigation_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                                     column_name=UUID.lemon_navigationitem.navigation_uuid.value)
    item_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                               column_name=UUID.lemon_navigationitem.item_uuid.value,
                               verbose_name="导航项标识符")
    item_name = LemonCharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')],
                               column_name=UUID.lemon_navigationitem.item_name.value,
                               verbose_name="导航项名")
    item_title = LemonCharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')],
                                column_name=UUID.lemon_navigationitem.item_title.value,
                                verbose_name="导航标题")
    level = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')],
                              column_name=UUID.lemon_navigationitem.level.value,
                              verbose_name="层级")
    master = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                            column_name=UUID.lemon_navigationitem.master.value,
                            verbose_name="上级导航项")
    icon = LemonCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')],
                          column_name=UUID.lemon_navigationitem.icon.value,
                          verbose_name="图标")
    icon_type = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')],
                                  column_name=UUID.lemon_navigationitem.icon_type.value,
                                  verbose_name="图标类型")
    item_type = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')],
                                  column_name=UUID.lemon_navigationitem.item_type.value,
                                  verbose_name="类型")
    order = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')],
                              column_name=UUID.lemon_navigationitem.order.value)
    icon_color = LemonCharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')],
                                column_name=UUID.lemon_navigationitem.icon_color.value)
    icon_text = LemonIntegerField(null=True,
                                  column_name=UUID.lemon_navigationitem.icon_text.value,
                                  verbose_name="图标内容")
    page_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                               column_name=UUID.lemon_navigationitem.page_uuid.value,
                               verbose_name="页面标识符")
    page_url = LemonCharField(max_length=2048, default="", constraints=[SQL('DEFAULT ""')],
                              column_name=UUID.lemon_navigationitem.page_url.value,
                              verbose_name="外部链接地址")
    param = LemonJsonField(null=True, column_name=UUID.lemon_navigationitem.param.value)
    permission = LemonJsonField(null=True, column_name=UUID.lemon_navigationitem.permission.value)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')],
                             column_name=UUID.lemon_navigationitem.is_delete.value)
    ext_tenant = LemonCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True,
                                column_name=UUID.lemon_navigationitem.ext_tenant.value)
    user_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                               column_name=UUID.lemon_navigationitem.user_uuid.value)
    branch_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')],
                                 column_name=UUID.lemon_navigationitem.branch_uuid.value)
    item_position = LemonIntegerField(null=True,
                                      column_name=UUID.lemon_navigationitem.item_position.value)
    badge = LemonJsonField(null=True, column_name=UUID.lemon_navigationitem.badge.value)
    permission_config = LemonJsonField(null=True, default={},
                                       column_name=UUID.lemon_navigationitem.permission_config.value)

    class Meta:
        display_name = UUID.lemon_navigationitem.display_name.value
        model_name = display_name
        module_name = "系统模块"
        ignore_tenant = True
        table_function = make_table_name
        evolve = True
        version_control = True
        sys_table = True
        is_sys_table = True
        indexes = (
            (("app_uuid",), False),
            (("navigation_uuid",), False),
            (("item_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )

    @hybrid_property
    def 导航项标识符(self):
        return self.item_uuid

    @hybrid_property
    def 导航项名(self):
        return self.item_name

    @hybrid_property
    def 导航标题(self):
        return self.item_title

    @hybrid_property
    def 层级(self):
        return self.level

    @hybrid_property
    def 上级导航项(self):
        return self.master

    @hybrid_property
    def 图标(self):
        return self.icon

    @hybrid_property
    def 图标类型(self):
        return self.icon_type

    @hybrid_property
    def 类型(self):
        return self.item_type

    @hybrid_property
    def 图标内容(self):
        return self.icon_text

    @hybrid_property
    def 页面标识符(self):
        return self.page_uuid

    @hybrid_property
    def 外部链接地址(self):
        return self.page_url


def get_current_user() -> CurrentUserModel:
    current_connector = LemonContextVar.current_connector.get()
    if current_connector:
        current_user = current_connector.current_user
    else:
        current_user = LemonContextVar.current_user.get()
    if current_user is None:
        return globals().get("current_user")
    return current_user


def get_is_user_table(query):
    try:
        return get_model_is_user_table(query.model)
    except (BaseException, Exception):
        return False


def get_model_is_user_table(model):
    try:
        is_user_table = getattr(model._meta, "is_user_table", False)
        ignore_tenant = getattr(model._meta, "ignore_tenant", None)
        is_user_table = all([not ignore_tenant, is_user_table])
    except (BaseException, Exception):
        is_user_table = False
    return is_user_table


# 更新模型对象
def update_model_obj_data(query, update_data_dict):
    model_obj = getattr(query, "_model_obj", None)
    if update_data_dict and model_obj:
        for data in update_data_dict:
            model_obj.__data__.update({data.column_name: update_data_dict.get(data)})


def build_query_base_update_dict(query, current_user=None):
    current_user = get_current_user() if current_user is None else current_user
    tenant_id = "" if current_user is None else current_user.tenant_id
    user_uuid = "" if current_user is None else current_user.uuid
    user_pk = "" if current_user is None else current_user.user_pk
    query_update_dict = dict()
    if isinstance(query, (peewee.Insert, LemonModelInsert)):
        if query.model._meta.meta_table_name not in UUID.META_TABLE_ALL:
            query_update_dict.update({query.model.tenant_uuid: tenant_id})
        query_update_dict.update({
            query.model._creator: user_uuid,
            query.model._last_modified_by: user_uuid,
            query.model._owner: user_pk,
            query.model._reviser: user_pk,
            query.model._create_time: int(time.time()),
            query.model._modified_time: int(time.time())
        })
    elif isinstance(query, (peewee.Update, LemonModelUpdate)):
        update_modify_info = getattr(query, "_update_modify_info", True)
        if update_modify_info:
            query_update_dict.update({
                query.model._reviser: user_pk,
                query.model._last_modified_by: user_uuid,
                query.model._modified_time: int(time.time())
            })
    if query_update_dict:
        update_model_obj_data(query, query_update_dict)
    return query_update_dict


def add_tenant_user_where_expr(query, tenant_id):
    if isinstance(
            query, (peewee.Select, LemonModelSelect, peewee.Update, LemonModelUpdate, peewee.Delete, LemonModelDelete)):
        if query.model._meta.meta_table_name not in UUID.META_TABLE_ALL:
            query = query.where(query.model.tenant_uuid == tenant_id)
    if isinstance(query, (peewee.Select, LemonModelSelect)):
        # app_log.info(query._joins)
        if hasattr(query, "_joins"):
            for dest_list in query._joins.values():
                for dest in dest_list:
                    dest_model = dest[0]
                    if isinstance(dest_model, (peewee.ModelSelect, LemonModelSelect)):
                        pass
                        # ? 子查询是否需要filter tenant？
                        # app_log.info(query._from_list[0])
                        # dest_model = wrapper_tenant_query(dest_model)
                        # app_log.info(dest_model)
                    if isinstance(dest_model, peewee.Model):
                        is_user_table__ = getattr(dest_model._meta, "is_user_table", False)
                        if is_user_table__ and dest_model._meta.meta_table_name not in UUID.META_TABLE_ALL:
                            query = query.where(dest_model.tenant_uuid == tenant_id)
    return query


def wrapper_tenant_query(query, current_user=None):
    current_user = get_current_user() if current_user is None else current_user
    tenant_id = LemonContextVar.tenant_uuid.get() if current_user is None else current_user.tenant_id
    is_user_table = get_is_user_table(query)
    if is_user_table:
        query = add_tenant_user_where_expr(query, tenant_id)
        if isinstance(query, (peewee.Insert, LemonModelInsert)):
            query_insert = query._insert
            if isinstance(query_insert, dict):
                insert_query_update_dict = build_query_base_update_dict(query, current_user=current_user)
                query_insert.update(insert_query_update_dict)
            elif isinstance(query_insert, list):
                for insert in query_insert:
                    insert_query_update_dict = build_query_base_update_dict(query, current_user=current_user)
                    insert.update(insert_query_update_dict)
        elif isinstance(query, (peewee.Update, LemonModelUpdate)):
            update_query_update_dict = build_query_base_update_dict(query, current_user=current_user)
            query._update.update(update_query_update_dict)
        elif isinstance(query, peewee.RawQuery):
            # 暂不支持
            pass
    return query


def get_field_model_data(field):
    if isinstance(field, (peewee.Column, peewee.Field, peewee.Function)):
        if isinstance(field, peewee.Function):
            if field.name == "JSON_OBJECT":
                field = field.arguments[1]
            else:
                field = field.arguments[0]
        if hasattr(field, "model"):
            field_model = field.model
            field_model_uuid = field_model._meta.table_name
            if isinstance(field, peewee.FieldAlias):
                field_source = field.source
                field_source_uuid = field_source.alias
                if field_source_uuid is None:
                    field_source_uuid = field_model_uuid
            else:
                field_source = field_model
                field_source_uuid = field_model_uuid
            return field_model, field_model_uuid, field_source, field_source_uuid
    return None, None, None, None


def get_field_model_source_uuid(field, system_model_alias_dict):
    _, _, _, source_uuid = get_field_model_data(field)
    if source_uuid in system_model_alias_dict:
        return system_model_alias_dict[source_uuid]
    return source_uuid


def find_join_from_by_dest(query, dest_model):
    for source_model, join_item in query._joins.items():
        for item in join_item:
            dest = item[0]
            if dest == dest_model:
                return source_model
    return None


def build_select_query_returning(
        query, query_model, calc_field_map, user_select_map, user_select_unwrap_map,
        system_model_alias_dict, status_field_map, status_select_fields, query_pk=True):
    # TODO: calc_field_map 的格式, 已经不能满足 多次 join 同一张表的需求
    # 需要改造成以 不同 field.source 的 alias 为 key 的结构
    # 在后面的 execute 函数里里 for res in result 的循环时候
    # 需要再次遍历这个 calc_field_map 算出来 field_seq
    # 这样才能知道 这个计算字段 究竟是哪个 多次join相同模型 的别名模型的
    # 这样也能解决 user_select_alias_wrap_map 里的 字段.column_name 会重复
    # 无法正确还原 多次 join 相同模型 的字段的 alias 的问题
    # todo:select from subquery 时，获取最左端model
    if query_model is not None:
        query_pk_column = query_model.id
        # query_node = GlobalVars.engine.uuids.node_uuid_map.get(query_model._meta.table_name)
    else:
        # todo: query 必须select 最左端model的id
        query_pk_column = query.c.id

    """ *** 以下查询的字段都可能是关联表字段 *** """
    query_field_list = list()
    query_field_set = set()  # 用户查询的 原始字段列表 + 其他所需计算、系统字段 等等
    alias_field_name_set = set()  # 用户查询的 字段 别名集合
    alias_system_field_dict = dict()  # 用户查询的 系统字段 别名字典
    calc_model_fields = defaultdict(set)  # 用户查询的 计算字段 所依赖模型与字段
    # 如果有查询计算字段，则需要带上 query_model 的id，不然无法 dict_to_model
    # 如果查询中，没有普通字段，例如 distinct 就不能带 id 否则达不到 去重的效果
    find_calc_field = False
    many_to_many_join_map = getattr(query, "many_to_many_join_map", None) or {}
    for index, select_field in enumerate(query._returning[:]):
        field = select_field
        if select_field.is_alias():
            alias_field_name_set.add(select_field._alias)
            field = select_field.unalias()
        # elif isinstance(field, peewee.Field):
        #     select_field = select_field.alias(select_field.column_name)
        if isinstance(field, (peewee.Column, peewee.Field, peewee.Function)):
            function_field = False
            if isinstance(field, peewee.Function):
                if field.name == "JSON_OBJECTAGG":
                    # json_objectagg 函数里因为需要合并 相同pk 的数据
                    # 所以 arguments[0] 是 主键字段
                    # arguments[1] 才是查询字段
                    field = field.arguments[1]
                else:
                    field = field.arguments[0]
                if field.is_alias():
                    field = field.unalias()
                function_field = True
            # 字段有可能是从 别名后的模型 ModelAlias 中获取的
            # 这里需要判断 并拿到 别名后的模型
            # 额外查询的计算字段依赖字段、系统字段，都要从这个 别名后的模型 上获取
            field_model, field_model_uuid, field_source, field_source_uuid = get_field_model_data(
                field)
            if field_source_uuid is None:
                query_field_list.append(select_field)
                continue
            calc_field_map.setdefault(field_source_uuid, {})
            user_select_map.setdefault(field_source_uuid, [])
            user_select_unwrap_map.setdefault(field_source_uuid, [])
            if is_calc_field(field):
                model_calc_field_map = calc_depend_all_field_map.get(field_model_uuid, {})
                depend_fields = model_calc_field_map.get(field, {})
                calc_field_map[field_source_uuid].update({field: depend_fields})
                for calc_field in depend_fields[0]:
                    calc_depend_fields = model_calc_field_map.get(calc_field, {})
                    calc_field_map[field_source_uuid].update({calc_field: calc_depend_fields})
                calc_model_fields.setdefault(
                    field_source_uuid,
                    {
                        "source": field_source, "model": field_model,
                        "fields": set(), "need_all": False, "function_field": function_field
                    })
                if field.calculate_type == 0:
                    for normal_field in depend_fields[1]:
                        calc_model_fields[field_source_uuid]["fields"].add(normal_field)
                else:
                    calc_model_fields[field_source_uuid]["need_all"] = True
                user_select_map[field_source_uuid].append(select_field)
                user_select_unwrap_map[field_source_uuid].append(field)
                find_calc_field = True
            elif is_system_field(field):
                # 这里是用户选择的系统字段, 已经不会有 _creator 和 _last_modify_by
                # 在之前构造的 query 里已经处理过 这两种字段的情况
                system_field = getattr(field_source, field.name)
                system_field_alias_name = get_sys_field_uuid(field_source_uuid, field.name)
                if function_field:
                    system_field_alias = python_agg_field(system_field).alias(system_field_alias_name)
                else:
                    system_field_alias = system_field.alias(system_field_alias_name)
                alias_system_field_dict.update({system_field_alias_name: system_field_alias})
                query_field_list.append(select_field)
                user_select_map[field_source_uuid].append(select_field)
                user_select_unwrap_map[field_source_uuid].append(field)
                if field.name == "_status":
                    status_field_map.setdefault(field_source_uuid, set())
                    status_field_map[field_source_uuid].add(field.name)
                    status_field_map[field_source_uuid].add(system_field_alias_name)
                    status_select_fields.append((index, select_field))
            elif field_model in [User, Department]:
                query_field_list.append(select_field)
                # 字段模型是 系统表, 表明 用户的原始查询为 _creator 和 _last_modify_by
                # 这两个系统字段, 则需要将 用户的原始查询模型找到
                # 用原始模型的表名 或 alias 替换掉 系统表的 alias
                # 因为后面计算字段用的都是原始模型的表明 或 alias 来取不同 join path 数据的
                join_from_model = find_join_from_by_dest(query, field_source)
                if join_from_model:
                    if isinstance(join_from_model, peewee.ModelAlias):
                        join_from_model_source_uuid = join_from_model.alias
                    else:
                        join_from_model_source_uuid = join_from_model._meta.table_name
                    system_model_alias_dict.update({
                        field_source_uuid: join_from_model_source_uuid})
                    user_select_map.setdefault(join_from_model_source_uuid, [])
                    user_select_map[join_from_model_source_uuid].append(select_field)
                    user_select_unwrap_map.setdefault(join_from_model_source_uuid, [])
                    user_select_unwrap_map[join_from_model_source_uuid].append(field)
            else:
                query_field_list.append(select_field)
        else:
            query_field_list.append(select_field)
    if find_calc_field and query_pk:
        query_field_set.add(query_pk_column)

    for source_uuid, field_info in calc_model_fields.items():
        model = field_info.get("model")
        source = field_info.get("source")
        fields = field_info.get("fields")
        need_all = field_info.get("need_all")
        function_field = field_info.get("function_field")
        if need_all:
            fields = model._meta.sorted_fields
        model_uuid = model._meta.table_name
        model_object_args = list()
        query_fields = set()
        for field in fields:
            # 这里也一样，从 别名后的模型 获取计算字段的依赖字段、系统字段
            if is_system_field(field):
                column = getattr(source, field.name)
                if source == query_model:
                    system_field_alias_name = get_sys_field_uuid(source_uuid, field.name)
                    system_field_alias = column.alias(system_field_alias_name)
                    alias_system_field_dict.update({system_field_alias_name: system_field_alias})
                else:
                    system_field_alias_name = get_sys_field_uuid(model_uuid, field.name)
                    # model_object_args.extend([field.name, column])
                    model_object_args.extend([system_field_alias_name, column])
            if is_normal_field(field, allow_meta_field=True, allow_primary_key=True):
                if source == query_model:
                    field_alias_name = field.column_name
                else:
                    if field.name == model._meta.primary_key.name:
                        field_alias_name = model_uuid + "_pk"
                    else:
                        field_alias_name = field.column_name
                if source == query_model:
                    if field_alias_name not in alias_field_name_set:
                        # field_alias = field.alias(field_alias_name)
                        column = getattr(source, field.name)
                        field_alias = column.alias(field_alias_name)
                        query_fields.add(field_alias)
                else:
                    column = getattr(source, field.name)
                    # model_object_args.extend([field.name, column])
                    model_object_args.extend([field_alias_name, column])
        if model_object_args:
            # 这里都是 计算字段 以来的 普通字段 或 系统字段
            primary_key_name = model._meta.primary_key.name
            if source_uuid in many_to_many_join_map:
                join_info = many_to_many_join_map[source_uuid]
                m_model = join_info.get("m_model")
                primary_key_field = getattr(m_model, primary_key_name)
            else:
                primary_key_field = getattr(source, primary_key_name)
            model_object_args.extend([primary_key_name, primary_key_field])
            if function_field:
                # 初始查询中的字段 已经是聚合字段时，查询多端字段时会这样
                # 需要将以来字段的查询，也组合成一个 json_arrayagg
                func = partial(load_json_object_from_json_arrayagg, model_object_args)
                model_object_alias = peewee.fn.JSON_ARRAYAGG(
                    peewee.fn.JSON_OBJECT(*model_object_args)
                    ).python_value(func).alias(source_uuid + "_obj")
            else:
                # 初始查询中的字段 是非聚合字段
                # 直接查询依赖字段 构成一个 json_object
                func = partial(load_data_from_json_object, model_object_args)
                model_object_alias = peewee.fn.JSON_OBJECT(
                    *model_object_args).python_value(func).alias(source_uuid + "_obj")
            query_fields.add(model_object_alias)
        query_field_set = query_field_set | query_fields

    for alias_name, alias_field in alias_system_field_dict.items():
        if alias_name not in alias_field_name_set:
            query_field_set.add(alias_field)
    query._returning = query_field_list
    query._returning.extend(list(query_field_set))
    # app_log.debug(f"_returning: {query._returning}, query: {query}")
    return query


def gen_res_rewrite(res, row_type, field_name, value=None):
    if row_type in [peewee.ROW.CONSTRUCTOR, peewee.ROW.MODEL]:
        setattr(res, field_name, value)
    elif row_type == peewee.ROW.NAMED_TUPLE:
        res = namedtuple("Row", res._fields + (field_name, ))._make([r for r in res] + [value])
    elif row_type == peewee.ROW.DICT:
        res.update({field_name: value})
    return res


def _gen_get_value_func(row_type, res):
    get_value_func = None
    if row_type in [peewee.ROW.CONSTRUCTOR, peewee.ROW.MODEL, peewee.ROW.NAMED_TUPLE]:
        if isinstance(res, peewee.Model):
            # app_log.info(f"__data__: {res.__data__}")
            get_value_func = lambda res, name_key, idx: res.__data__.get(name_key)
        else:
            get_value_func = lambda res, name_key, idx: getattr(res, name_key, None)
    elif row_type == peewee.ROW.DICT:
        get_value_func = lambda res, name_key, idx: res.get(name_key, None)
    elif row_type == peewee.ROW.TUPLE:
        get_value_func = lambda res, name_key, idx: res[idx]
    return get_value_func


async def batch_calc_field_value(field, eval_globals, temp_dict=None, objs=None):
    if not objs:
        objs = []
        for data in eval_globals:
            objs.append((shortcuts.dict_to_model(field.model, data, ignore_unknown=True), ))
    temp_dict = temp_dict or dict()
    if field.calculate_type == 0:  # 表达式计算
        try:
            result = await lemon_entity.async_eval_expr(
                field.calculate_function, globals=eval_globals,
                obj=objs, with_exception=True, _batch_execute=True)
        except ExprExecError as e:
            result = [None for _ in eval_globals]
            message = "数据模型: %s\n字段: %s" % (field.model.__name__, field.name)
            message = message + "\n" + e.message
            app_log.error(message)
            temp_dict.update({field.name: message})
    else:
        try:
            result = await lemon_entity.async_eval_func(
                field.calculate_func, objs, with_exception=True, _batch_execute=True)
        except FuncExecError as e:
            result = [None for _ in eval_globals]
            func_message, func_name = e.message
            message = "出错的云函数：%s\n数据模型: %s\n字段: %s" % (func_name, field.model.__name__, field.name)
            message = message + "\n" + func_message
            app_log.error(message)
            temp_dict.update({field.name: message})
    for index, value in enumerate(result):
        if isinstance(value, ExprExecError):
            value = None
        else:
            value = check_calc_field_value(field, value)
        eval_globals[index].update({field.name: value})
        if isinstance(field, peewee.Field):
            eval_globals[index].update({field.column_name: value})
        result[index] = value
    return result


async def gen_calc_field_value(field, eval_globals, temp_dict=None, obj=None, with_exception=True):
    obj = obj or shortcuts.dict_to_model(
        field.model, eval_globals, ignore_unknown=True)
    temp_dict = temp_dict or dict()
    # app_log.info(f"obj: {obj}, to_dict: {obj.to_dict()}")
    # app_log.info(f"field.model: {field.model}, eval_globals: {eval_globals}")
    if field.calculate_type == 0:  # 表达式计算
        # app_log.debug(f"calc: {field.calculate_function}")
        try:
            value = await lemon_entity.async_eval_expr(
                field.calculate_function, globals=eval_globals,
                obj=obj, with_exception=with_exception)
        except ExprExecError as e:
            value = None
            message = "数据模型: %s\n字段: %s" % (field.model.__name__, field.name)
            message = message + "\n" + e.message
            app_log.error(message)
            temp_dict.update({field.name: message})
    else:
        try:
            value = await lemon_entity.async_eval_func(field.calculate_func, obj, with_exception=with_exception)
        except FuncExecError as e:
            value = None
            func_message, func_name = e.message
            message = "出错的云函数：%s\n数据模型: %s\n字段: %s" % (func_name, field.model.__name__, field.name)
            message = message + "\n" + func_message
            app_log.error(message)
            temp_dict.update({field.name: message})
    # app_log.debug(f"field: {field}, value: {value}")
    value = check_calc_field_value(field, value)
    eval_globals.update({field.name: value})
    if isinstance(field, peewee.Field):
        eval_globals.update({field.column_name: value})
    return value


def gen_select_calc_field_info(calc_field_map, user_select, user_select_unwrap):
    field_seq = []
    user_select_alias_wrap_map = dict()
    user_select_unwrap_name = []
    if calc_field_map:
        # 找出计算字段的依赖顺序
        field_seq = topo_sort(calc_field_map)[::-1]
        user_select_alias = [field for field in user_select if field.is_alias()]
        for select_field in user_select_alias:
            unalias_field = select_field.unalias()
            if isinstance(unalias_field, peewee.Function):
                if unalias_field.name == "JSON_OBJECTAGG":
                    unalias_field = unalias_field.arguments[1]
                else:
                    unalias_field = unalias_field.arguments[0]
            if select_field._alias.endswith("_pk"):
                user_select_alias_wrap_map.update({select_field._alias: select_field})
            else:
                user_select_alias_wrap_map.update({unalias_field.column_name: select_field})
        user_select_unwrap_name = [field.name for field in user_select_unwrap]
    return field_seq, user_select_alias_wrap_map, user_select_unwrap_name


def gen_no_permission_field_value(field: peewee.Field):
    lemon_field_type = getattr(field, "lemon_type", None)
    value = PermissionDeploy.NoPermissionFieldShow.get(lemon_field_type)
    if value is None:
        value = PermissionDeploy.NoPermissionFieldShow.get(-1)
    return value


def strip_calc_field(data: dict):
    update_data = dict()
    for field in data.keys():
        if is_calc_field(field, calculated_column_is_calc=True):
            continue
        update_data.update({field: data[field]})
    return update_data


def batch_strip_calc_field(data: list):
    update_data = list()
    need_check = True
    for index, part_data in enumerate(data):
        update_data.append(strip_calc_field(part_data))
        if need_check:
            if len(part_data) != len(update_data[index]):
                need_check = False
            else:
                # 批量时,如果没有计算字段,就不重复检查去除了
                update_data = data
                break
    return update_data


async def get_query_model_events(query, run_model_event=True):
    models_pre_events = defaultdict(dict)
    models_post_events = defaultdict(dict)
    to_collect_models = defaultdict(set)
    if LemonContextVar.context_to_collect_models.get() is None:
        LemonContextVar.context_to_collect_models.set({})
    if LemonContextVar.context_grouped_models_events.get() is None:
        LemonContextVar.context_grouped_models_events.set({})
    context_grouped_models_events: dict = LemonContextVar.context_grouped_models_events.get()
    context_to_collect_models: dict = LemonContextVar.context_to_collect_models.get()

    def add_to_collect_models(model, event_type):
        to_collect_models[model].add(event_type)

    def update_user_model_events(query_model, model_events, model_event_type: str):
        model_events = collect_model_events(list(model_events))
        sys_pre_events, sys_post_events = list(), list()
        if getattr(query_model._meta, "is_sys_table", False):
            sys_pre_events = gen_sys_model_events(query_model, model_event_type)
            sys_post_events = gen_sys_model_events(query_model, model_event_type, EventWhen.AFTER)
        pre_events = get_type_events(query_model, model_event_type)
        pre_model_events = get_model_events(model_events, model_event_type)
        post_events = get_type_events(query_model, model_event_type, EventWhen.AFTER)
        post_model_events = get_model_events(model_events, model_event_type, EventWhen.AFTER)
        pre_events = sys_pre_events + pre_events + pre_model_events
        post_events = sys_post_events + post_events + post_model_events
        if pre_events:
            events = models_pre_events[query_model].setdefault(model_event_type, list())
            events.extend(e for e in pre_events if e not in events)
        if post_events:
            events = models_post_events[query_model].setdefault(model_event_type, list())
            events.extend(e for e in post_events if e not in events)

    def analyze_query(query):
        sub_query_list = []
        if isinstance(query, peewee.RawQuery) or not run_model_event:
            return models_pre_events, models_post_events
        elif isinstance(query, peewee.ModelCompoundSelectQuery):
            sub_query_list.append(query.lhs)
            sub_query_list.append(query.rhs)
        elif isinstance(query, peewee.SelectQuery):
            query_model = getattr(query, "model", None)
            if query_model is not None and issubclass(query_model, UserModel) and getattr(
                    query_model._meta, "select_enabled", True):
                add_to_collect_models(query_model, ModelEventType.SELECT)
            if from_list := getattr(query, "_from_list", None):
                for from_item in from_list:
                    if isinstance(from_item, peewee.SelectQuery):
                        sub_query_list.append(from_item)
                    elif isinstance(from_item, peewee.Join):
                        sub_query_list.append(from_item)
            if where_expr := getattr(query, "_where", None):
                sub_query_list.append(where_expr)
            if returning := getattr(query, "_returning", None):
                for select_field in returning:
                    if isinstance(select_field, peewee.SelectQuery):
                        sub_query_list.append(select_field)
        elif isinstance(query, (peewee.Insert, peewee.Update)):
            query_model = getattr(query, "model", None)
            if query_model is not None and issubclass(query_model, LemonUserModel):
                add_to_collect_models(query_model, ModelEventType.SAVE)
            if data_rows := getattr(query, "_insert", None) or getattr(query, "_update", None):
                if isinstance(data_rows, dict):
                    data_rows = [data_rows]
                for row in data_rows:
                    sub_query_list.append(row)
                sub_query_list.append(value for value in row.values() if isinstance(value, peewee.SelectQuery))
            if where_expr := getattr(query, "_where", None):
                sub_query_list.append(where_expr)
        elif isinstance(query, peewee.Delete):
            query_model = getattr(query, "model", None)
            if query_model is not None and issubclass(query_model, LemonUserModel):
                add_to_collect_models(query_model, ModelEventType.DELETE)
            if where_expr := getattr(query, "_where", None):
                sub_query_list.append(where_expr)
        elif isinstance(query, peewee.Join):
            lhs, rhs = query.lhs, query.rhs
            if lhs is not None and isinstance(lhs, peewee.ModelBase) and issubclass(lhs, LemonUserModel) and getattr(
                    lhs._meta, "select_enabled", True):
                add_to_collect_models(lhs, ModelEventType.SELECT)
            if rhs is not None and isinstance(rhs, peewee.ModelBase) and issubclass(rhs, LemonUserModel) and getattr(
                    rhs._meta, "select_enabled", True):
                add_to_collect_models(rhs, ModelEventType.SELECT)
            if isinstance(lhs, (peewee.Join, peewee.SelectQuery)):
                sub_query_list.append(lhs)
            if isinstance(rhs, (peewee.Join, peewee.SelectQuery)):
                sub_query_list.append(query.rhs)
        elif isinstance(query, peewee.Expression):
            lhs, rhs, op, flat = query.lhs, query.rhs, query.op, query.flat
            if isinstance(lhs, (peewee.Expression, peewee.SelectQuery)):
                sub_query_list.append(lhs)
            if isinstance(rhs, (peewee.Expression, peewee.SelectQuery)):
                sub_query_list.append(rhs)

        for sub_query in sub_query_list:
            analyze_query(sub_query)
    
    analyze_query(query)

    if to_collect_models:
        add_to_collect_models(SYSTEM_MODEL_DICT.get("模型事件"), ModelEventType.SELECT)
        delta_collect_models = {k: v for k, v in to_collect_models.items() if k not in context_to_collect_models}
        if delta_collect_models:
            event_query = gen_multi_model_events_sql(delta_collect_models)
            all_model_events = await execute(event_query)
            for item in all_model_events or []:
                model_uuid = item['model_uuid']
                call_at = item['call_at']
                model_events_dict = context_grouped_models_events.setdefault(model_uuid, {})
                model_events_list = model_events_dict.setdefault(call_at, [])
                model_events_list.append(item)
        for model_class, event_types in to_collect_models.items():
            model_uuid = model_class._meta._origin_table_name if getattr(model_class._meta, "is_copy", False) \
                else model_class._meta.table_name
            model_event_types = context_to_collect_models.setdefault(model_class, set())
            for event_type in event_types:
                events_dict = context_grouped_models_events.setdefault(model_uuid, {})
                events = events_dict.setdefault(event_type, [])
                update_user_model_events(model_class, events, event_type)
                model_event_types.add(event_type)
    return models_pre_events, models_post_events


async def run_all_model_events(query, all_events,
                               event_when=EventWhen.BEFORE, models=None, need_throw_exception=True):
    for model, model_events in all_events.items():
        for model_event_type, events in model_events.items():
            query = await run_model_events(events, model_event_type, when=event_when, model_class=model,
                                           query=query, models=models, need_throw_exception=need_throw_exception)
    return query


async def execute(query: LemonModelSelect, notify=False, direct_insert_serial=False, scalar=False, **kwargs):
    _time_start = time.time()
    action = ModelEventType.UNSUPPORTED
    need_atomic = False
    need_throw_exception = GlobalVars.engine.config.get("NEED_THROW_EXCEPTION")
    notify = notify or getattr(query, "_notify", False)
    run_model_event = getattr(query, "_run_model_event", True)
    model_obj = getattr(query, "_model_obj", None)
    calc_field_map = {}  # 用户查询的 计算字段 所依赖的其他普通字段的模型 与 字段
    user_select_map = {}  # 用户查询的 原始字段 按 join 的 model alias 分组
    user_select_unwrap_map = {}  # 用户查询的 去除别名后的原始字段 按 join 的 model alias 分组
    # 当关联到系统表时, 会join系统表, 这时 field.source 就会变成 lemon_uuid()
    # 导致 后面的 get_field_value 找不到正确的 field.source
    # 下面这个 dict 就是让 join 了系统表的字段, 能还原正确的 field.source
    system_model_alias_dict = dict()
    # 转换 _status 工作流状态系统字段
    status_field_map = {}
    status_select_fields = []
    notify_data, models, atomic_querys = [], [], []
    current_user = get_current_user()
    query_model = getattr(query, "model", None)
    post_serial_result_list = []
    # if hasattr(query, "_ext"):
    #     # 拓展配置，已弃用
    #     atomic_querys.append(getattr(query, "_ext", None))
    try:
        is_user_table = getattr(query_model._meta, "is_user_table", False)
    except (BaseException, Exception):
        is_user_table = False

    if isinstance(query, (peewee.Select, peewee.ModelCompoundSelectQuery, LemonModelSelect)):
        # 计算字段的where
        coroutine = peewee_async.select
        query_pk = getattr(query, "_query_pk", True)
        if is_user_table and not isinstance(query, peewee.ModelCompoundSelectQuery):
            query = build_select_query_returning(
                query, query_model, calc_field_map, user_select_map, user_select_unwrap_map,
                system_model_alias_dict, status_field_map, status_select_fields,
                query_pk=query_pk)
    elif isinstance(query, (peewee.Update, LemonModelUpdate)):
        if isinstance(query._update, dict):
            query._update = strip_calc_field(query._update)
        elif isinstance(query._update, list):
            query._update = batch_strip_calc_field(query._update)
        coroutine = peewee_async.update
        action = ModelEventType.UPDATE
        need_atomic = True
    elif isinstance(query, (peewee.Insert, LemonModelInsert)):
        if isinstance(query._insert, dict):
            query._insert = strip_calc_field(query._insert)
        elif isinstance(query._insert, list):
            query._insert = batch_strip_calc_field(query._insert)
        coroutine = peewee_async.insert
        action = ModelEventType.INSERT
        need_atomic = True
        serial_post_gen_field = []
        if not direct_insert_serial:
            for field in query_model._meta.sorted_fields:
                if is_serial_with_post_gen_type(field):
                    serial_post_gen_field.append(field)
        for field in serial_post_gen_field:
            if isinstance(query._insert, dict):
                insert_serial = query._insert.get(field)
                expr_globals = DictWrapper()
                for _column, value in query._insert.items():
                    expr_globals.update({
                        _column.name: value, _column.column_name: value})
                serial_result, serial_value = await make_serial(
                        field, GlobalVars.engine, pre=False,
                        eval_globals=expr_globals)
                # 支持用户导入流水号字段,若用户自定义的流水号正好和系统生成的一致的话,就当改流水号已被使用
                if not insert_serial or insert_serial == serial_value[0]:
                    query._insert.update({field: serial_value[0]})
                    post_serial_result_list.append(serial_result)
                    if model_obj is not None:
                        setattr(model_obj, field.name, serial_value[0])
            elif isinstance(query._insert, list):
                count = len(query._insert)
                for index, insert in enumerate(query._insert):
                    insert_serial = insert.get(field.name)
                    serial_result, serial_value = await make_serial(
                        field, GlobalVars.engine, pre=False, count=count,
                        eval_globals=insert)
                    if not insert_serial or insert_serial == serial_value[0]:
                        # 批量插入时如果不删除原有数据会导致列重复,插入报错
                        insert.pop(field.name, 0)
                        insert.update({field: serial_value[index]})
                post_serial_result_list.append(serial_result)
    elif isinstance(query, (peewee.Delete, LemonDelete, LemonModelDelete)):
        coroutine = lemon_delete
        action = ModelEventType.DELETE
        need_atomic = True
        if not is_user_table:
            raise peewee.PeeweeException("系统表，不能删除")
    else:
        coroutine = peewee_async.raw_query
    if scalar:
        coroutine = partial(peewee_async.scalar, as_tuple=kwargs.get('as_tuple', False))
    # 添加租户id
    select_all = getattr(query, "select_all", False)
    if not select_all:
        query = wrapper_tenant_query(query, current_user)
    # 处理模型事件
    is_temp_model = False
    if query_model:
        is_temp_model = getattr(query_model._meta, "is_temp", False)
    pre_events, post_events = await get_query_model_events(query, run_model_event)
    app_log.info(f"pre_events: {pre_events}, post_events: {post_events}")
    # if (pre_events or post_events) and model_event_type == ModelEventType.SELECT:
    #     need_atomic = True

    if notify or pre_events or post_events:
        # 这样处理某些情况下取的pk可能不对
        # mysql只能分析binlog去分析数据变化，https://github.com/noplay/python-mysql-replication
        if action == ModelEventType.UPDATE or action == ModelEventType.DELETE:
            if action == ModelEventType.UPDATE:
                update_data = {field.column_name: value for field, value in query._update.items()}
            else:
                update_data = {}
            pk = query_model._meta.primary_key
            field_alias_list = [pk.alias("pk")]
            for field in query_model._meta.sorted_fields:
                if is_normal_field(field, allow_meta_field=True, allow_primary_key=True):
                    field_alias = field.alias(field.column_name)
                    field_alias_list.append(field_alias)
            select_query = query_model.select(*field_alias_list)
            select_query._where = query._where
            setattr(select_query, "select_all", getattr(query, "select_all", False))
            data_list = await peewee_async.select(select_query.dicts())
            update_pks = list()
            for index, row_data in enumerate(data_list):
                if index == 0 and model_obj is not None:
                    models.append(model_obj)
                else:
                    update_pks.append(row_data.get("pk"))
                    row_data.update(update_data)
                    notify_data.append(row_data)
                    models.append(shortcuts.dict_to_model(
                        query_model, row_data, ignore_unknown=True))
            # update / delete 不存在行时, 不执行模型事件
            run_model_event = all([models, run_model_event])
            app_log.debug(f"{update_pks} will change")
        elif action == ModelEventType.INSERT:
            insert_data = query._insert if isinstance(query._insert, list) else [query._insert]
            for index, row in enumerate(insert_data):
                row_data = dict()
                for name, value in row.items():
                    if isinstance(name, str):
                        field = getattr(query_model, name)
                    else:
                        field = name
                    row_data.update({field.column_name: value})
                notify_data.append(row_data)
                if index == 0 and model_obj is not None:
                    models.append(model_obj)
                else:
                    models.append(shortcuts.dict_to_model(
                        query_model, row_data, ignore_unknown=True))

    app_log.debug(f"execute_query: {query}")
    # await query_model._pre_modify
    # app_log.info(f"{atomic_querys}, {need_atomic}")
    # if atomic_querys or need_atomic:
    update_pks, update_rows = [], []
    # 运行模型的前置事件
    if run_model_event and pre_events:
        query = await run_all_model_events(query, pre_events, models=models, need_throw_exception=need_throw_exception)
    if isinstance(query, (peewee.Delete, LemonDelete, LemonModelDelete, peewee.Update, LemonModelUpdate)):
        # if isinstance(query, peewee.Update):
        #     data = {field.column_name: value for field, value in query._update.items()}
        # else:
        #     data = {}
        pk = query_model._meta.primary_key
        to_select = [pk] + [fk.alias(fk.column_name) for fk in query_model._meta.refs]
        select_query = query_model.select(*to_select)
        select_query._where = query._where
        update_rows = await peewee_async.select(select_query.dicts())
        update_rows = list(update_rows)
        update_pks = [p[pk.name] for p in update_rows]
        app_log.info(f"update_pks {update_pks}")
    if (
        isinstance(query, (peewee.Delete, LemonDelete, LemonModelDelete))
        and is_user_table and query_model._meta.ext_type != 1
    ):
        for fk, fk_item in query_model._delete_action.items():
            for from_source, item in fk_item.items():
                rel_model, del_action, r_type, backref_name = item
                rel_model = get_runtime_table_copy(rel_model)
                if r_type == RelationshipType.MANY_TO_MANY:
                    fk_model = fk.model
                    if query_model == fk_model:
                        continue
                    fk_model_pk = fk_model._meta.primary_key
                    rel_fk = getattr(fk_model, backref_name)
                    rel_pk = rel_model._meta.primary_key
                    model_pk = query_model._meta.primary_key
                    select_query = fk_model.select(*[fk_model_pk, fk, rel_fk]).where(
                        fk.in_(update_pks)).dicts()
                    result_rows = await execute(select_query)
                    fk_values, rel_values, pk_values = [], [], []
                    for row in result_rows:
                        fk_values.append(row.get(fk.name, None))
                        rel_values.append(row.get(rel_fk.name, None))
                        pk_values.append(row.get(fk_model_pk.name, None))
                    if not rel_values:
                        continue
                    if del_action == "CASCADE":
                        fk_model_delete = fk_model.delete().where(fk_model_pk.in_(pk_values))
                        await execute(fk_model_delete)
                        rel_model_delete = rel_model.delete().where(rel_pk.in_(rel_values))
                        await execute(rel_model_delete)
                    elif del_action == "RESTRICT":
                        rel_model_select = rel_model.select().where(rel_pk.in_(rel_values))
                        rel_model_result = await execute(rel_model_select)
                        if rel_model_result:
                            raise peewee.PeeweeException("有关联数据，不能删除")
                        fk_model_update = fk_model.update(
                            {fk.column_name: None}).where(fk_model_pk.in_(pk_values))
                        await execute(fk_model_update)
                    else:
                        fk_model_update = fk_model.update(
                            {fk.column_name: None}).where(fk_model_pk.in_(pk_values))
                        await execute(fk_model_update)
                else:
                    if from_source:
                        # query_model 是外键端
                        ref_pk = rel_model._meta.primary_key
                        # app_log.info(list(update_rows))
                        fk_values = [row.get(fk.column_name, None) for row in update_rows]
                        app_log.info(f"fk_values: {fk_values}, {fk.column_name}, ref_pk: {ref_pk}")
                        if not fk_values:
                            continue
                        if del_action == "CASCADE":
                            delete_action_query = rel_model.delete().where(ref_pk.in_(fk_values))
                        elif del_action == "RESTRICT":
                            delete_action_query = rel_model.select().where(ref_pk.in_(fk_values))
                        else:
                            continue  # set null 不做任何操作
                    else:
                        if not update_pks:
                            continue
                        rel_model = fk.model
                        if del_action == "CASCADE":
                            delete_action_query = rel_model.delete().where(fk.in_(update_pks))
                        elif del_action == "RESTRICT":
                            delete_action_query = rel_model.select().where(fk.in_(update_pks))
                        else:
                            continue  # set null 不做任何操作
                    delete_action_res = await execute(delete_action_query)
                    # 有关联数据，不能删除
                    if isinstance(delete_action_query, (peewee.Select, LemonModelSelect)) and delete_action_res:
                        raise peewee.PeeweeException("有关联数据，不能删除")
    if is_temp_model:
        if isinstance(query, (peewee.Select, peewee.ModelCompoundSelectQuery, LemonModelSelect, peewee.RawQuery)):
            result = (await coroutine(query))
        else:
            result = 0
    else:
        result = (await coroutine(query))
    # for ext_query in atomic_querys:
    #     app_log.info(f"type: {type(ext_query)}, ext_query: {ext_query}")
    #     if isinstance(ext_query, peewee.Node):
    #         ext_type = getattr(ext_query.model._meta, "ext_type", 0)
    #         if ext_type == 1:
    #             pk_field = ext_query.model.relation_id
    #         else:
    #             pk_field = ext_query.model._meta.primary_key
    #         if isinstance(query, (peewee.Insert, LemonModelInsert)):
    #             if isinstance(ext_query._insert, dict):
    #                 ext_query._insert.update({pk_field: result})
    #             elif isinstance(ext_query._insert, list):
    #                 for idx, insert in enumerate(ext_query._insert):
    #                     insert.update({pk_field: result + idx})
    #         elif isinstance(
    #                 query, (LemonModelDelete, LemonDelete, peewee.Delete, LemonModelUpdate, peewee.Update)):
    #             ext_query = ext_query.where(pk_field.in_(update_pks))
    #             app_log.info(f"ext_query: {ext_query}")
    #         ext_res = await execute(ext_query)
    #         app_log.info(f"ext_res: {ext_res}")
    # else:
    #     result = (await coroutine(query))

    app_log.info(f"execute_result: {result}")
    model_pk = result
    if notify:
        if action == ModelEventType.INSERT and not is_temp_model:
            for idx, row_data in enumerate(notify_data):
                model_pk = model_pk + idx
                row_data.update({"pk": model_pk})
                model = models[idx]
                setattr(model, model._meta.primary_key.name, model_pk)
        # model_uuid = query_model._meta.table_name
        # TODO: 暂时这么改，避免出现 warning
        # 但还是需要重构代码不使用 gather(*atomic_cache_tasks)
        # notify_task = asyncio.create_task(
        #     lemon_entity.notify(
        #         model_uuid=model_uuid, data=notify_data,
        #         action=action, timestamp=int(time.time())))
        # transaction_depth = query._database.transaction_depth_async()
        # if transaction_depth > 0:
        #     atomic_cache_tasks = LemonContextVar.atomic_cache_tasks.get()
        #     atomic_cache_tasks.append(notify_task)
        # else:
        #     await notify_task
    if post_events:
        # 运行模型的后置事件
        if action == ModelEventType.INSERT and not is_temp_model:  # post事件可能要用到pk
            for idx, model in enumerate(models):
                setattr(model, model._meta.primary_key.name, result+idx)
        if run_model_event and post_events:
            result = await run_all_model_events(result, post_events, event_when=EventWhen.AFTER, models=models,
                                                need_throw_exception=need_throw_exception)

    for serial_result in post_serial_result_list:
        field_uuid, tenant_uuid, app_uuid, date_str, serial_prefix, serial_value, serial_pk, first = serial_result
        if isinstance(serial_value, int):
            if first is True:
                await GlobalVars.engine.user_access.create_field_auto_increment(
                    field_uuid, tenant_uuid, app_uuid, date_str, serial_prefix, serial_value)
            else:
                await GlobalVars.engine.user_access.update_field_auto_increment_value(serial_pk, serial_value)

    temp_dict = {}
    if calc_field_map and not scalar:
        row_type = query._row_type or query.default_row_type
        result_calc = ListWithError()
        # 计算出 不同join 路径 所依赖的 数据
        # field_seq : 计算字段依赖的普通字段
        # select_alias_wrap_map : join 路径下 查询的原始字段
        # select_unwrap_name : join 路径下 查询的别名后的原始字段
        field_seq, select_alias_wrap_map, select_unwrap_name = {}, {}, {}
        for source_uuid, source_calc_field_map in calc_field_map.items():
            this_user_select = user_select_map.get(source_uuid, [])
            this_user_select_unwrap = user_select_unwrap_map.get(source_uuid, [])
            this_field_seq, this_alias_wrap_map, this_unwrap_name = gen_select_calc_field_info(
                source_calc_field_map, this_user_select, this_user_select_unwrap)
            field_seq.update({source_uuid: this_field_seq})
            select_alias_wrap_map.update({source_uuid: this_alias_wrap_map})
            select_unwrap_name.update({source_uuid: this_unwrap_name})

        eval_globals_all = list()
        eval_globals_all_source_key = dict()
        for res in result:
            # app_log.debug(f"{repr(res)}, {row_type}")
            get_value_func = _gen_get_value_func(row_type, res)
            eval_globals = DictWrapper()
            if get_value_func:
                get_field_value(query, res, row_type, get_value_func, system_model_alias_dict, eval_globals)
            eval_globals_all.append(eval_globals)
            for s_uuid, data in eval_globals.items():
                source_data = eval_globals_all_source_key.setdefault(s_uuid, [])
                source_data.append(data)

        for source_uuid, source_calc_field_map in calc_field_map.items():
            source_field_seq = field_seq.get(source_uuid, [])
            user_select_alias_wrap_map = select_alias_wrap_map.get(source_uuid, {})
            user_select_unwrap_name = select_unwrap_name.get(source_uuid, {})
            for field in source_field_seq:
                field_eval_globals_data = eval_globals_all_source_key.get(source_uuid, [])
                if source_uuid != query_model._meta.table_name:
                    if field_eval_globals_data and isinstance(field_eval_globals_data[0], dict):
                        value_list = await batch_calc_field_value(field, field_eval_globals_data, temp_dict)
                    else:
                        value_list = list()
                        for field_eval_globals in field_eval_globals_data:
                            v = await batch_calc_field_value(field, field_eval_globals, temp_dict)
                            value_list.append(v)
                    app_log.info(f"field: {field}, value: {value_list}")
                else:
                    value_list = await batch_calc_field_value(field, field_eval_globals_data, temp_dict)
                    app_log.info(f"field: {field}, value: {value_list}")
                field_name, field_column_name = field.name, field.column_name

                if field_name not in user_select_unwrap_name:
                    continue
                # 当前计算字段,可能在用户查询的字段列表中,就要还原用户的 alias 配置
                if field_column_name in user_select_alias_wrap_map:
                    field_column_name = user_select_alias_wrap_map[field_column_name]._alias
                else:
                    field_column_name = field_name
                for index, value in enumerate(value_list):
                    if row_type in [peewee.ROW.CONSTRUCTOR, peewee.ROW.MODEL]:
                        setattr(result[index], field_column_name, value)
                    elif row_type == peewee.ROW.NAMED_TUPLE:
                        result = namedtuple("Row", result[index]._fields + (field_column_name, ))._make(
                            [r for r in result[index]] + [value])
                    elif row_type == peewee.ROW.DICT:
                        result[index].update({field_column_name: value})
                    elif row_type == peewee.ROW.TUPLE:
                        # TODO: 删除用户未select字段和unalias
                        field_idx = query._returning.index(field)
                        result = result[index][:field_idx] + (value,) + result[index][field_idx:]

    for key, value in temp_dict.items():
        result_calc.error_list.append({"name": key, "message": value})

    if getattr(query, "_no_permission", set()) and not scalar:
        if getattr(query, "_in_user_func", None):
            _no_permission_fields = query._no_permission
            _no_permission_field_name = ", ".join([f.name for f in _no_permission_fields])
            raise FuncFieldPermissionError(_no_permission_field_name)
        modify_result = list()
        for res in result:
            for field in query._no_permission:
                unalias_field = field
                if field.is_alias():
                    field_name = field._alias
                    unalias_field = field.unalias()
                else:
                    field_name = field.name
                value = gen_no_permission_field_value(unalias_field)
                res = gen_res_rewrite(res, row_type, field_name, value)
            modify_result.append(res)
        result = modify_result
        query._no_permission.clear()
    app_log.info(f"query execute time: {time.time() - _time_start}")
    return result


