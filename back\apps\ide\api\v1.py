# -*- coding:utf-8 -*-

from functools import partial
from itertools import groupby
from keyword import kwlist
import re
import os
import time
import copy
import asyncio
import hashlib
from typing import Optional

import pkg_resources
import urllib.parse

from playhouse.shortcuts import model_to_dict
from sanic.request import Request

from apps.ide.api.utils import get_domain_url_by_image_path, process_image, save_image
from peewee import JOIN
from aiohttp import ClientSession
import zipfile
import uuid
import requests
from baseutils.celery_task_utils import make_collection_key, make_in_progress_collection_key
import importlib
import pkgutil
from sanic.response import json, raw, HTTPResponse
from sanic_openapi import doc
from packaging import version
import venv
import subprocess
from apps.entity import Packages, Func
from baseutils.log import app_log
from apps.utils import (
    LemonDictResponse as LDR, PageFinder, RelationshipFinder,
    process_navigation_item, get_current_workspace, gen_model_resource_info
)
from baseutils.utils import async_run
# import subprocess
# import sys
from apps.utils import (
    process_args, check_lemon_name, check_document_name, lemon_uuid,
    restore_adapter, get_login_user_uuid, set_current_workspace,
    process_tag_grouby_resource_uuid, process_resource_tree
)
from baseutils.const import Code, DOC, Action, ImportResult, ImportType, SystemTable, SyncTORepoType
from apps.entity import (
    APP, CheckMessage, Document as DocumentModel,
    DocumentReference, EnumTable, Extension, ImportRecord, ModelField, ModuleRole, Navigation,
    NavigationItem, RelationshipBasic, StateMachine, UserRole, UserRoleContent,
    RestfulTable as RestfulTableModel, ExtensionDetail
)
from apps.entity import Module as ModuleModel, Page as PageModel, LabelPrint as LabelPrintModel, PublishGroup, AppGroup
from apps.entity import (
    ModelBasic, DocumentContent, Func as FuncModel, Workflow as WorkflowModel,
    Print as PrintModel
)
from apps.entity import ImageTable as ImageTableModel
from apps.entity import ToolCategory as ToolCategoryModel, DocumentDraft
from apps.entity import UserTool, User
from apps.runtime_entity import Team
from apps.engine import engine
from apps.config import Config as app_config
from apps.permission import Permission
from apps.ide_const import (
    ComponentType, DeviceType, DocumentType,
    IDECode, InputAdapter, InputType, Module, Document,
    ValueEditorVariables
)
from apps.ide_const import (
    ToolCategoryType, EntityType, ToolClass, ToolType, IconType, DragType,
    IDEViewDoc, ImportStatus
)
from apps.services.publish import PublishService
from apps.services.mall.extension import Template, UIComponent, ImportHelper, IMPORT_AGENT_DICT, clear_timeout_helper
from apps.services.mall.const import ReleaseType, ScopeType
from apps.services.iconfont.utils import sync_iconfont, gather_iconfont
from apps.middlewares import LemonHTTPMethodView as HTTPMethodView, LemonVCHTTPMethodView as VCHTTPMethodView
from apps.helper import DesignOss
from baseutils.editor_locker import LockError
import ujson
from baseutils.utils import LemonContextVar
import traceback
import aioredlock
from apps.base_utils import GlobalVars, install_package, get_shortcut_condition_dict
from apps.exceptions import LemonDesignError

from .util import url_prefix
from functools import cmp_to_key


async def get_document_data(request):
    with process_args():
        app_uuid = request.json.get("app_uuid")
        document_uuid = str(request.json.get("document_uuid"))
        ext_tenant = request.json.get("ext_tenant")
    if not document_uuid:
        return json(LDR(Code.ARGS_ERROR))
    async with engine.db.objs.atomic():
        document = await engine.access.get_document_by_document_uuid(document_uuid, ext_tenant=ext_tenant)
        if not document:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        if document.app_uuid != app_uuid and document.app_uuid != engine.config.SYS_APP_UUID:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        if document.module_uuid == engine.config.SYS_MODULE_UUID:
            async with engine.access.with_workspace(None):
                document_content = await engine.access.get_document_content_by_document_uuid(document.document_uuid)
        else:
            document_content = await engine.access.get_document_content_by_document_uuid(document.document_uuid)
        if document:
            document_dict = document.to_dict()
        if document_dict:
            document_type = document_dict.get("document_type")
            document_name = document_dict.get("document_name")

        # 以前的文档修改名称不会同步修改通用名称，新文档不会出现这个问题，这部分代码让旧文档也能有这个功能过渡到新文档。
        # if document_content:
        #     document_content_dict = document_content.to_dict()
        #     document_content_dict["document_content"].update({"name": document_name})
        #     await engine.access.update_document_content_by_document_uuid(**document_content_dict)
        # if document_type == EntityType.PAGE:
        #     update_data = dict(page_name=document_name)
        #     await engine.access.update_page_by_document_uuid(document_uuid=document_uuid, **update_data)
        # elif document_type == EntityType.FUNC:
        #     update_data = dict(func_name=document_name)
        #     model = Func
        #     query = model.update(**update_data).where(model.document_uuid==document_uuid)
        #     await engine.access.update_obj_by_query(model, query)
        # elif document_type == EntityType.WORKFLOW:
        #     update_data = dict(wf_name=document_name)
        #     model = WorkflowModel
        #     query = model.update(**update_data).where(model.document_uuid==document_uuid)
        #     await engine.access.update_obj_by_query(model, query)
        # elif document_type == EntityType.RULECHAIN:
        #     update_data = dict(rule_name=document_name)
        #     model = RuleChainModel
        #     query = model.update(**update_data).where(model.document_uuid==document_uuid)
        #     await engine.access.update_obj_by_query(model, query)
        # elif document_type == EntityType.PRINT:
        #     update_data = dict(print_name=document_name)
        #     model = PrintModel
        #     query = model.update(**update_data).where(model.document_uuid==document_uuid)
        #     await engine.access.update_obj_by_query(model, query)


    data = {
        DocumentModel.document_uuid.name: document_uuid,
        DocumentModel.document_version.name: document.document_version,
        "is_extension": 1 if document.ext_tenant else 0
    }
    if document_content is None:
        data.update({DocumentContent.document_content.name: dict(), 'timestamp': time.time()})
    else:
        data.update({DocumentContent.document_content.name: document_content.document_content,
                        "timestamp": document_content.timestamp})
    # return json(LDR(data=data))
    return data


class ListModule(VCHTTPMethodView):

    class ListModuleObj(object):
        app_uuid = doc.String("应用UUID")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(ListModuleObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用模块")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            ext_tenant = request.json.get("ext_tenant")

        result = await engine.access.list_module_by_app_uuid(app_uuid, with_sys=True, ext_tenant=ext_tenant)
        data = dict(
            app_uuid=app_uuid,
            children=list(result)
        )
        return json(LDR(data=data))


class CreateModule(VCHTTPMethodView):

    class CreateModuleObj(object):
        app_uuid = doc.String("应用UUID")
        module_name = doc.String("模块名称")

    @doc.consumes(CreateModuleObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("新建应用模块")
    @Permission.policy(("app_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = str(request.json.get("app_uuid", ""))
            module_name = str(request.json.get("module_name", "")).strip()
        if module_name in kwlist:
            return json(LDR(IDECode.MODULE_NAME_FORBID_PYTHON_KEYWORDLIST))
        if module_name == "系统模块":
            return_code = IDECode.MODULE_NAME_FORBID_SYS_MODULE
            return_code.message = return_code.message.format(name=module_name)
            return json(LDR(return_code))
        result = await engine.service.query.create_module(app_uuid, module_name, user_uuid)
        self.add_git_action(request, partial(self.sync_module, module_name))
        return result

    async def sync_module(self, module_name):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_MODULE, module_name)

class RenameModule(VCHTTPMethodView):

    class RenameModuleObj(object):
        module_uuid = doc.String("模块UUID")
        module_name = doc.String("模块名称")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(RenameModuleObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("重命名应用模块")
    @Permission.policy(("module_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            ext_tenant = request.json.get("ext_tenant")
            module_uuid = str(request.json.get("module_uuid", ""))
            module_name = str(request.json.get("module_name", "")).strip()
        if not all([module_uuid, module_name]):
            return json(LDR(Code.ARGS_ERROR))
        module_name = check_lemon_name(module_name, chinese=True)
        if not module_name:
            return json(LDR(IDECode.MODULE_NAME_FAILED))
        if module_name in kwlist:
            return json(LDR(IDECode.MODULE_NAME_FORBID_PYTHON_KEYWORDLIST))
        module = await engine.access.get_module_by_module_uuid(module_uuid)
        if not module:
            return json(LDR(IDECode.MODULE_NOT_EXISTS))

        app_uuid = module.app_uuid

        module_exists = await engine.access.get_module_by_app_uuid_module_name(app_uuid, module_name)
        app_log.info(f"module_exists: {module_exists}")
        if module_exists:
            return json(LDR(IDECode.MODULE_NAME_EXISTS))
        source_module_name = module.module_name
        if source_module_name != module_name:
            module_dict = {
                ModuleModel.module_name.name: module_name
            }
            await engine.access.update_module_by_module_uuid(module_uuid, **module_dict)
        request.ctx.GIT_ACTIONS = [partial(self._git_action, source_module_name, module_name)]
        return json(LDR())

    async def _git_action(self, name, new_name):
        await engine.git_request.sync_task_to_repo(SyncTORepoType.RENAME, name, new_name)


class ListNestDocument(VCHTTPMethodView):
    class ListNestDocumentObj():
        module_uuid = doc.String("模块UUID")
        document_uuid = doc.String("最内层的文档的uuid")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListNestDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出嵌套的文档内容")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            module_uuid = request.json.get("module_uuid", "")
            document_uuid = str(request.json.get("document_uuid", "")) # 最内层的文档的uuid
            ext_tenant = request.json.get("ext_tenant")

        if document_uuid == "":
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))

        if module_uuid:
            query_set = await engine.access.list_document_by_module_uuid(module_uuid=module_uuid)
            self.query_set_list = list(query_set)
        else:
            # 导航、主题配置等报错信息跳转
            document = await engine.access.get_document_by_document_uuid(document_uuid=document_uuid)
            query_set = await engine.access.list_document_by_module_uuid(module_uuid=document.module_uuid, app_uuid=app_uuid)
            self.query_set_list = list(query_set)

        result = []
        while document_uuid:
            # 从内向外组建出嵌套json格式
            document_dict = self.get_document_by_document_uuid(document_uuid=document_uuid)
            ret = self.list_document_by_document_puuid(document_dict['document_puuid'])
            for i in ret:
                if i['document_uuid'] == document_dict['document_uuid']:
                    i['children'] = result
                    break
            else:
                app_log.error(f"{document_dict['document_name']}没有被包含")
            result = ret
            document_uuid = document_dict.get('document_puuid', False)
        return json(LDR(data=result))
    
    def get_document_by_document_uuid(self, document_uuid):
        for r in self.query_set_list:
            if r['document_uuid'] == document_uuid:
                return r
            
    def list_document_by_document_puuid(self, document_puuid):
        ret = []
        for r in self.query_set_list:
            if r['document_puuid'] == document_puuid:
                ret.append(r)
        return ret


class ListDocument(VCHTTPMethodView):

    class ListDocumentObj():
        module_uuid = doc.String("模块UUID")
        document_uuid = doc.String("文档UUID，为空表示列模块下的第一层目录")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(ListDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出模块或文件夹内容")
    # @Permission.policy(("module_uuid", Action.SELECT),)
    @Permission.policy()
    async def post(self, request):
        with process_args():
            module_uuid = str(request.json.get("module_uuid"))
            document_uuid = str(request.json.get("document_uuid", ""))
            ext_tenant = request.json.get("ext_tenant")
        if not module_uuid:
            return json(LDR(Code.ARGS_ERROR))

        order_by_expr = DocumentModel.order.asc()
        result = await engine.access.list_document_by_module_document_puuid(
            module_uuid, document_uuid, ext_tenant=ext_tenant, order_by_expr=order_by_expr)
        output = list(result)
        version_control = getattr(request.ctx, "version_control", False)
        if version_control and module_uuid != engine.config.SYS_MODULE_UUID:
            doc_name_dict = dict()
            dir_path = None
            if document_uuid:
                document_info = await engine.access.get_document_detail_by_document_uuid(document_uuid)
                if document_info:
                    dir_path = document_info["full_path"]
            else:
                module = await engine.access.get_module_by_module_uuid(module_uuid)
                dir_path = module.module_name if module else None
            if dir_path:
                staged_docs = await self.get_staged_docs(dir_path)
                for doc in staged_docs:
                    change_type, file_path, new_file_path, content_before, content = doc
                    document_name = new_file_path.split("/")[-1]
                    doc_name_dict[document_name] = change_type
                for res in output:
                    change_type = doc_name_dict.get(res["document_name"])
                    res.update({"change_type": change_type})
        data = dict(
            module_uuid=module_uuid,
            document_uuid=document_uuid,
            children=output
        )
        return json(LDR(data=data))

    async def get_staged_docs(self, path):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.GET_STAGED_CHANGES, paths=[path])
        # order_list = await self.get_order_list(document_uuid, module_uuid)
        # result, _, flag = order_document(data["children"], order_list)
        # if flag:# 长度不一致考虑更新排序表
        #     await self.update_order_list(document_uuid, module_uuid, result)
        # data["children"] = result
        # return json(LDR(data=data))
    
    async def update_order_list(self, document_uuid, module_uuid, result):
        father_uuid = document_uuid if document_uuid else module_uuid
        if father_uuid is None:
            app_log.debug("father_uuid is None")
            
        
        document_uuid_order_list =  [] if result is None else [r['document_uuid'] for r in list(result)]
        
        await engine.access.update_document_order_by_father_uuid(father_uuid=father_uuid,
                                                                 document_uuid_order_list=document_uuid_order_list)
    
    async def get_order_list(self, document_uuid, module_uuid):
        """
        获取目录或者模块下的文档顺序
        """
        
        father_uuid = document_uuid if document_uuid else module_uuid
        if father_uuid is None:
            app_log.debug("father_uuid is None")

        document_order = await engine.access.get_document_order_by_father_uuid(father_uuid=father_uuid)

        # order表中没有结果，新建记录
        if not document_order:
            if not document_uuid:
                result = await engine.access.list_document_by_module_uuid_document_path(module_uuid=module_uuid, document_path="/")
            else:
                result = await engine.access.list_document_by_module_document_puuid(module_uuid=module_uuid, document_puuid=document_uuid)
                
            data = {
                "father_uuid" : father_uuid,
                "document_uuid_order_list" : [] if result is None else [r['document_uuid'] for r in list(result)]
            }
            await engine.access.create_document_order(**data)
            return data['document_uuid_order_list']
           
        return document_order.document_uuid_order_list


class ListPage(VCHTTPMethodView):
    
    class ListPageObj():
        module_uuid = doc.String("模块UUID")
        document_uuid = doc.String("文档UUID，为空表示列模块下的第一层目录")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(ListPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出页面或目录")
    @Permission.policy(("module_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            module_uuid = str(request.json.get("module_uuid"))
            document_uuid = request.json.get("document_uuid")
            ext_tenant = request.json.get("ext_tenant")
        if not module_uuid:
            return json(LDR(Code.ARGS_ERROR))
        result = await engine.access.list_page_by_module_document_puuid(
            module_uuid, document_uuid, ext_tenant=ext_tenant)
        data = dict(
            module_uuid=module_uuid,
            document_uuid=document_uuid,
            children=list(result)
        )
        return json(LDR(data=data))

    
class ListAPPDocument(VCHTTPMethodView):

    class ListAPPDocumentObj():
        app_uuid = doc.String("应用UUID")
        document_type = doc.String("文档类型")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(ListAPPDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出相应类型文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_document_groupby_module(request)






class MoveAndOrderDocument(VCHTTPMethodView):
    class MoveDocumentObj:
        is_one_scope = doc.Boolean('移动后范围是否改变 true/false')

        source_father_type = doc.String('原先文档上级类型 module/dir')
        source_document_uuid = doc.String('文档uuid')

        target_type = doc.String('目标类型 module/dir')
        head_document_uuid = doc.String('排在head_document_uuid之下 排在最上面则为-1')
        document_puuid = doc.String('目录uuid')

    async def remove_one_from_orderlist(self, child_document_obj, father_uuid):
        """
        从document_uuid_order_list中删除child_document_obj的uuid
        """
        document_order = await engine.access.get_document_order_by_father_uuid(
            father_uuid=father_uuid)
        app_log.debug("document_order -> {}".format(document_order))
        order_list = document_order.document_uuid_order_list
        app_log.debug("order_list -> {}".format(order_list))
        if child_document_obj.document_uuid not in order_list:
            return None
        order_list.remove(child_document_obj.document_uuid)
        await engine.access.update_document_order_by_father_uuid(father_uuid,
                                                                 document_uuid_order_list=order_list)

    async def move_doc_to_module(self, child_document_obj):
        """
        移动文档到模块下
        """
        document_path = "/"
        data = {
            "document_puuid": '',
            "document_path": document_path
        }
        result = await engine.access.update_document_by_document_uuid(
            document_uuid=child_document_obj.document_uuid, ext_tenant=None, **data)
        
        source_document_path = child_document_obj.document_path
        source_path = source_document_path + str(child_document_obj.id) + "/"
        target_path = document_path + str(child_document_obj.id) + "/"
        await engine.access.update_document_path_by_source_target(
            source_path, target_path, child_document_obj.module_uuid)

    async def move_doc_to_dir(self, child_document_obj, document_puuid):
        """
        移动文档到目录下
        """
        # parent_document 必须存在
        parent_document_obj = await engine.access.get_document_by_document_uuid(document_puuid)
        if not parent_document_obj:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))

        # 判断模块是不是sys_module
        module = await engine.access.get_module_by_module_uuid(child_document_obj.module_uuid)
        if not module:
            return json(LDR(IDECode.MODULE_NOT_EXISTS))
        if module.module_type == Module.TYPE.SYS:
            return json(LDR(IDECode.SYS_MODULE_DOCUMENT_FORBID_MOVE))

        # parent_document_obj的type必须是目录
        if parent_document_obj.document_type != Document.TYPE.DIR:
            return json(LDR(IDECode.PARENT_DOCUMENT_NOT_DIR))

        # 深度检测
        document_path = parent_document_obj.document_path + \
                        str(parent_document_obj.id) + "/"
        document_path_depth = document_path.count("/")
        if child_document_obj.document_type == Document.TYPE.DIR:
            if document_path_depth > 7:
                return json(LDR(IDECode.DOCUMENT_PATH_MAX_DEPTH))
        else:
            if document_path_depth > 8:
                return json(LDR(IDECode.DOCUMENT_PATH_MAX_DEPTH))

        data = {
            "document_puuid": document_puuid,
            "document_path": document_path
        }
        result = await engine.access.update_document_by_document_uuid(
            document_uuid=child_document_obj.document_uuid, ext_tenant=None, **data)
        
        source_document_path = child_document_obj.document_path
        source_path = source_document_path + str(child_document_obj.id) + "/"
        target_path = document_path + str(child_document_obj.id) + "/"
        await engine.access.update_document_path_by_source_target(
            source_path, target_path, child_document_obj.module_uuid)

    # async def order_doc(self, father_type, child_document_obj, tail_document_uuid, document_puuid, is_one_scope):
    #     """
    #     对文档进行排序
    #     """
    #     father_uuid = None
    #     if not is_one_scope:
    #         if father_type == 'dir':
    #             father_uuid = document_puuid
    #         elif father_type == 'module':
    #             father_uuid = child_document_obj.module_uuid
    #     else:
    #         if father_type == 'dir':
    #             father_uuid = child_document_obj.document_puuid
    #         elif father_type == 'module':
    #             father_uuid = child_document_obj.module_uuid
    #     document_order = await engine.access.get_document_order_by_father_uuid(father_uuid=father_uuid)
    #     if document_order is None:
    #         app_log.debug(document_order)
    #     try:
    #         order_list = document_order.document_uuid_order_list
    #         if is_one_scope:  # 同一目录下则删除原先uuid
    #             order_list.remove(child_document_obj.document_uuid)
    #         if tail_document_uuid != '-1':
    #             order_list.insert(order_list.index(tail_document_uuid), child_document_obj.document_uuid)
    #         else:
    #             order_list.append(child_document_obj.document_uuid)
    #     except ValueError:
    #         return json(LDR(data={"message": "值错误"}))
    #     except TypeError:
    #         return json(LDR(data={"message": "类型错误"}))
    #     await engine.access.update_document_order_by_father_uuid(father_uuid=father_uuid,
    #                                                              document_uuid_order_list=order_list)

    async def order_doc_by_head(self, father_type, child_document_obj, head_document_uuid, document_puuid, is_one_scope):
        """
        对文档进行排序
        """
        father_uuid = None
        if not is_one_scope:
            if father_type == 'dir':
                father_uuid = document_puuid
            elif father_type == 'module':
                father_uuid = child_document_obj.module_uuid
        else:
            if father_type == 'dir':
                father_uuid = child_document_obj.document_puuid
            elif father_type == 'module':
                father_uuid = child_document_obj.module_uuid
        document_order = await engine.access.get_document_order_by_father_uuid(father_uuid=father_uuid)
        app_log.info("father_uuid -> {}".format(father_uuid))
        if document_order is None:
            app_log.debug(document_order)
        try:
            order_list = document_order.document_uuid_order_list
            if is_one_scope:  # 同一目录下则删除原先uuid
                order_list.remove(child_document_obj.document_uuid)
            if head_document_uuid != '-1':
                if order_list.index(head_document_uuid) + 1 <= len(order_list):
                    order_list.insert(order_list.index(head_document_uuid) + 1, child_document_obj.document_uuid)
            else:
                order_list.insert(0, child_document_obj.document_uuid)
        except ValueError:
            return json(LDR(data={"message": "值错误"}))
        except TypeError:
            return json(LDR(data={"message": "类型错误"}))
        await engine.access.update_document_order_by_father_uuid(father_uuid=father_uuid,
                                                                 document_uuid_order_list=order_list)

    @doc.consumes(MoveDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("移动文档到目录或者模块下")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            is_one_scope = bool(request.json.get('is_one_scope'))

            source_father_type = str(request.json.get('source_father_type', ''))
            source_document_uuid = str(request.json.get('source_document_uuid', ''))

            target_type = str(request.json.get('target_type', ''))
            # tail_document_uuid = str(request.json.get('tail_document_uuid', ''))
            head_document_uuid = str(request.json.get('head_document_uuid', ''))
            document_puuid = str(request.json.get('document_puuid', ''))

        # 参数检查
        if request.json.get('is_one_scope') == None:
            return json(LDR(Code.ARGS_ERROR))
        if is_one_scope:
            if not all([source_father_type, source_document_uuid, head_document_uuid]):
                return json(LDR(Code.ARGS_ERROR))
        else:
            if not all([source_father_type, source_document_uuid, head_document_uuid, target_type]):
                return json(LDR(Code.ARGS_ERROR))
            if target_type == 'dir':
                if not document_puuid:
                    return json(LDR(Code.ARGS_ERROR))

        # 子文档必须存在
        child_document_obj = await engine.access.get_document_by_document_uuid(source_document_uuid)
        if not child_document_obj:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))

        module = await engine.access.get_module_by_module_uuid(child_document_obj.module_uuid)
        if module.module_type == Module.TYPE.SYS:
            return json(LDR(IDECode.SYS_MODULE_DOCUMENT_FORBID_MOVE))
        
        if is_one_scope:
            # 仅仅排序文档
            await self.order_doc_by_head(source_father_type, child_document_obj, head_document_uuid, document_puuid, is_one_scope)
            return json(LDR(data={"message": "ok"}))
        else:
            #  移动文档和排序文档
            if source_father_type == 'dir':
                father_uuid = child_document_obj.document_puuid
                await self.remove_one_from_orderlist(child_document_obj, father_uuid)
                if target_type == 'dir':
                    await self.move_doc_to_dir(child_document_obj, document_puuid)
                elif target_type == 'module':
                    await self.move_doc_to_module(child_document_obj)
                await self.order_doc_by_head(target_type, child_document_obj, head_document_uuid, document_puuid, is_one_scope)
            elif source_father_type == 'module':
                father_uuid = child_document_obj.module_uuid
                await self.remove_one_from_orderlist(child_document_obj, father_uuid)
                if target_type == 'dir':
                    await self.move_doc_to_dir(child_document_obj, document_puuid)
                    await self.order_doc_by_head(target_type, child_document_obj, head_document_uuid, document_puuid, is_one_scope)
            return json(LDR(data={"message": "ok"}))
        
class MoveDocumentNew(VCHTTPMethodView):
    
    class MoveDocumentNewObj:
        app_uuid = doc.String('app_uuid')
        parent = doc.String('移动后,父目录document_uuid,如果直接在模块下,为null')
        behind = doc.String('移动后,在哪个文档下面,如在最上面,为null,不过应不允许移动到模块安全和数据模型之上')
        front = doc.String('移动后,在哪个文档上面,如在最下面,为null')
        document_uuid = doc.String('被移动文档uuid')
        target_module_uuid = doc.String('移动后模块的module_uuid')

    @doc.consumes(MoveDocumentNewObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("移动文档到目录或者模块下")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            parent = request.json.get("parent")
            behind = request.json.get("behind")
            front = request.json.get("front")
            document_uuid = request.json.get("document_uuid")
            target_module_uuid = request.json.get("target_module_uuid")
            move_type = request.json.get("move_type")
        if not (parent or any([behind, front])):
            return json(LDR(IDECode.INVALID_ACTION))
        is_module = True if parent == target_module_uuid else False
        if not behind and not front:
            result = await engine.access.list_document_by_document_puuid(parent, is_module)
            # result = result or await engine.access.list_document_by_module_uuid(parent)
            output = list(result)
            output = sorted(output, key=lambda output: output['order'])
            if output:
                behind = output[-1].get("document_uuid")
        async with engine.db.objs.atomic():
            itself = await engine.access.get_document_by_document_uuid(document_uuid)
            code_return = await self.move_document(
                app_uuid, parent, behind, front, document_uuid, target_module_uuid, request)
        if itself.document_type == EntityType.DIR:
            itself = await engine.access.get_document_by_document_uuid(document_uuid)
            # all_document_dict = await self.get_all_document_inside_dir(itself.document_uuid, defaultdict(dict))
            await self.move_all_document_inside_dir(itself.document_uuid, target_module_uuid, request)
        code_return = json(LDR()) if code_return is True else code_return
        return code_return

    async def move_all_document_inside_dir(self, dir_uuid, target_module_uuid, request):
        list_document = await engine.access.list_document_by_document_puuid(dir_uuid)
        for document_info in list_document:
            parent_uuid = document_info.get("document_puuid")
            itself_uuid = document_info.get("document_uuid")
            await self.update_inside_dir_document(parent_uuid, itself_uuid)
            if document_info.get("document_type") == EntityType.DIR:
                await self.move_all_document_inside_dir(itself_uuid, target_module_uuid, request)

    async def update_inside_dir_document(self, parent_uuid, itself):
        update_info = dict()
        async with engine.db.objs.atomic():
            parent = await engine.access.get_document_by_document_uuid(parent_uuid)
            itself = await engine.access.get_document_by_document_uuid(itself)
        path = parent.document_path + str(parent.id) + "/"
        update_info.update({DocumentModel.document_path.name: path})
        await engine.access.update_document_by_document_uuid(itself.document_uuid, **update_info)

    async def lock_with_document_name(self, d_name, module_uuid=None, app_uuid=None, auto_unlock=True):

        app_log.info(f"a: {app_uuid}, m: {module_uuid}, n: {d_name}")
        resource_id = hashlib.md5("_".join([app_uuid, module_uuid, d_name]).encode()).hexdigest()
        app_log.info(f"resource_id: {resource_id}")
        try:
            lock: aioredlock.Lock = await GlobalVars.engine.resource_lock.lock(
                resource_id, lock_timeout=1)
            app_log.info(f"lock: {lock}")
            if LemonContextVar.atomic_resource_locks.get() is None:
                LemonContextVar.atomic_resource_locks.set([])
            atomic_resource_locks = LemonContextVar.atomic_resource_locks.get()
            atomic_resource_locks.append((lock, auto_unlock))
            return lock
        except (BaseException, Exception):
            app_log.info(traceback.format_exc())
            # return None

    async def move_document(
            self, app_uuid, parent_uuid, behind, front, itself,
            target_module_uuid, request):
        async with engine.db.objs.atomic():
            parent = await engine.access.get_document_by_document_uuid(parent_uuid)
            behind = await engine.access.get_document_by_document_uuid(behind)
            front = await engine.access.get_document_by_document_uuid(front)
            itself = await engine.access.get_document_by_document_uuid(itself)
        if not itself:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        if not target_module_uuid:
            return json(LDR(Code.ARGS_ERROR))
        if parent_uuid != itself.module_uuid or itself.document_puuid:
            is_module = True if parent_uuid == target_module_uuid else False
            result = await engine.access.list_document_by_document_puuid(parent_uuid, is_module)
            target_document_name_list = [document_i.get("document_name") for document_i in list(result)]
            document_uuid_list = [document_i.get("document_uuid") for document_i in list(result)]
            if itself.document_name in target_document_name_list and (
                    itself.document_uuid not in document_uuid_list):
                return json(LDR(IDECode.DOCUMENT_NAME_EXISTS))
        document_lock: aioredlock.Lock = await self.lock_with_document_name(
            itself.document_name, target_module_uuid, app_uuid)
        if document_lock is None:
            raise LemonDesignError(IDECode.DOCUMENT_NAME_EXISTS)

        model_update_info = dict()
        if itself.module_uuid != target_module_uuid and target_module_uuid:
            itself_module_info = await engine.access.get_module_by_module_uuid(itself.module_uuid)
            if itself_module_info.extend_from:
                return json(LDR(IDECode.EXTEND_MODULE_FORBID_MOVE_OUT))
            module_list = await engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
            module_uuid_list = [m_info.get("module_uuid") for m_info in list(module_list)]
            if target_module_uuid not in module_uuid_list:
                return json(LDR(IDECode.MODULE_NOT_EXISTS))
            need_update_model_dict = {
                EntityType.PAGE: PageModel, EntityType.FUNC: FuncModel, EntityType.PRINT: PrintModel,
                EntityType.IMAGE: ImageTableModel, EntityType.WORKFLOW: WorkflowModel,
                EntityType.LABEL_PRINT: LabelPrintModel, EntityType.RESTFUL: RestfulTableModel,
                EntityType.ENUM: EnumTable, EntityType.APPROVALFLOW: WorkflowModel
                }
            need_update_model = need_update_model_dict.get(itself.document_type)
            if need_update_model:
                app_log.info(f"model_update_info: module_uuid: {need_update_model}")
                model_update_info.update({need_update_model.module_uuid.name: target_module_uuid})

        version_control = getattr(request.ctx, "version_control", False)
        if version_control:
            itself_info = await engine.access.get_document_detail_by_document_uuid(itself.document_uuid)
            old_git_path = itself_info["full_path"] if itself_info else None

        update_info = dict()
        behind_order = getattr(behind, "order", 0)  # 当移动到最前时,认为behind_order是 0 
        front_order = getattr(front, "order", behind_order+8)  # 当移动到最后时, 认为after_order是  behind_order+4 # TODO 排脑袋决定的, 有风险
        itself_order = (float(behind_order) + float(front_order)) / 2.0
        if parent:
            # 移动到目录下
            puuid = parent.document_uuid
            path = parent.document_path + str(parent.id) + "/"
        else:
            puuid = ""
            path = "/"
        update_info.update({
            DocumentModel.document_puuid.name: puuid, 
            DocumentModel.document_path.name: path, 
            DocumentModel.order.name: itself_order,
            DocumentModel.module_uuid.name: target_module_uuid
        })
        if path.count("/") > 8:
            return json(LDR(IDECode.DOCUMENT_PATH_MAX_DEPTH))
        async with engine.db.objs.atomic():
            await engine.access.update_document_by_document_uuid(itself.document_uuid, **update_info)
            if model_update_info:
                await engine.access.update_model_by_document_uuid(itself.document_uuid, itself.document_type, **model_update_info)
            # TODO itself_order长度到达临界点时,触发重新排序
            decimal_part_str = str(itself_order).split(".")[1].rstrip("0")
            if len(decimal_part_str) >= 8:
                # 触发重新排序,将order置为整数类型
                # TODO 应只触发scope内的重新排序
                await self.order_all_document(app_uuid)
        if version_control:
            if old_git_path:
                new_path_info = await engine.access.get_document_detail_by_document_uuid(itself.document_uuid)
                new_git_path = new_path_info["full_path"]
                self.add_git_action(request, partial(self._git_action, old_git_path, new_git_path))
        return True

    async def order_module_document(self, module_uuid, document_puuid):
        """scope内排序"""
        ...
                
    async def order_all_document(self, app_uuid):
        order_by_expr = (DocumentModel.order.asc(), DocumentModel.id.asc())
        fields = DocumentModel._meta.sorted_fields
        result = await engine.access.list_document_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=None, order_by_expr=order_by_expr, 
            as_dict=False, fields=fields)
        app_log.info(len(list(result)))
        order_map = dict()
        async with engine.db.objs.atomic():
            for r in list(result):
                if r.document_type in [Document.TYPE.THEME]:
                    continue
                module_uuid = r.module_uuid
                puuid = r.document_puuid
                key = module_uuid+"_"+puuid
                cur_order = order_map.setdefault(key, 0)
                
                cur_order += 1
                r.order = cur_order
                # app_log.info(r.pk)
                
                q = r.update(order=cur_order).where(r._pk_expr())
                app_log.info(q)
                await engine.db.objs.execute(q)
                order_map[key] = cur_order
        app_log.info(order_map)
            
    async def _git_action(self, name, new_name):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.RENAME, name, new_name)


    
class CreateDocument(VCHTTPMethodView):

    class CreateDocumentObj():
        app_uuid = doc.String("应用UUID")
        module_uuid = doc.String("模块UUID")
        document_name = doc.String("文档或文件夹名称")
        document_type = doc.Integer(
            "文档类型  0：文件夹 1：模型文件 2：状态机文件 3：云函数文件 4：页面文件 5: 图片表文件 6：常量表文件 7：JSON表文件 8：枚举表文件")
        document_puuid = doc.String("父目录UUID，为空表示在模块根目录下")
        document_content = doc.String("文档内容")
        ext_tenant = doc.String("租户uuid，为空创建非拓展")
    
    @doc.consumes(CreateDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("创建文档或文件夹")
    @Permission.policy(("module_uuid", Action.MODIFY),)
    async def post(self, request):
        real_user_uuid, user_uuid = get_login_user_uuid(request)
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            module_uuid = str(request.json.get("module_uuid"))
            document_name = str(request.json.get("document_name")).strip()
            document_type = int(request.json.get("document_type"))
            document_puuid = str(request.json.get("document_puuid", ""))
            document_content = request.json.get("document_content")
            ext_tenant = request.json.get("ext_tenant")
        if not all([app_uuid, module_uuid, document_name]):
            return json(LDR(Code.ARGS_ERROR))
        document_name = check_document_name(document_name)
        if not document_name:
            return json(LDR(IDECode.DOCUMENT_NAME_FAILED))
        if document_type not in Document.TYPE.ALL:
            return json(LDR(IDECode.DOCUMENT_TYPE_NOT_EXISTS))
        if document_type in [Document.TYPE.SECURITY, Document.TYPE.MODEL]:
            return json(LDR(IDECode.DOCUMENT_TYPE_NOT_CREATABLE))
        if ext_tenant:
            if document_type not in [Document.TYPE.MODEL, Document.TYPE.PAGE, Document.TYPE.SECURITY, Document.TYPE.WORKFLOW]:
                return json(LDR(IDECode.DOCUMENT_TYPE_NOT_EXISTS))

        document_path = "/"
        if document_puuid == "":
            module = await engine.access.get_module_by_app_module_uuid(app_uuid, module_uuid)
        else:
            module = await engine.access.get_module_by_app_module_uuid(app_uuid, module_uuid)
            p_document = await engine.access.get_document_by_document_uuid(document_puuid)
            if not p_document:
                return json(LDR(IDECode.PARENT_DOCUMENT_NOT_EXISTS))
            if p_document.document_type != Document.TYPE.DIR:
                return json(LDR(IDECode.PARENT_DOCUMENT_NOT_DIR))
            document_path = p_document.document_path + str(p_document.id) + "/"
            document_path_depth = document_path.count("/")
            # 第七层目录只能创建文档
            if document_type == Document.TYPE.DIR:
                if document_path_depth > 7:
                    return json(LDR(IDECode.DOCUMENT_PATH_MAX_DEPTH))
            else:
                if document_path_depth > 8:
                    return json(LDR(IDECode.DOCUMENT_PATH_MAX_DEPTH))
        if not module:
            return json(LDR(IDECode.MODULE_NOT_EXISTS))
        
        document_uuid = lemon_uuid()
        now_timestamp = int(time.time())
        document_dict = dict(
            app_uuid=app_uuid,
            module_uuid=module_uuid,
            document_uuid=document_uuid,
            document_name=document_name,
            document_type=document_type,
            document_path=document_path,
            document_puuid=document_puuid,
            create_timestamp=now_timestamp,
            update_timestamp=now_timestamp,
            error_list=list()
        )
        document = await engine.access.create_document(user_uuid, ext_tenant=ext_tenant, **document_dict)
        return_document_dict = copy.deepcopy(document_dict)
        return_document_dict.update({
            "is_extension": 1 if document.ext_tenant else 0,
            "document_name": document.document_name
            })
        if ext_tenant:
            return_document_dict.update({"additional": {"extendable": True}})
        if isinstance(document_content, dict):
            document_content.update({"name": document.document_name})
            if document_content.get("title"):
                document_content.update({"title": document.document_name})
            if document_content.get("page_title"):
                document_content["page_title"].update({
                    "value": document.document_name
                })
            json_data = {
                "app_uuid": app_uuid,
                "module_uuid": module_uuid,
                "document_uuid": document_uuid,
                "document_version": 1,
                "document_content": document_content
            }
            document_check_service = engine.service.document_check.create_service(document_type)
            error_list = await document_check_service.check(json_data, document_type, json_return=False)
            return_document_dict.update({"error_list": error_list})
        # # 如果是状态机，增加初始化数据
        # return_document_dict.update({DocumentContent.document_content.name: dict()})
        # if document_type == Document.TYPE.SM:
        #     state_start = StartState(
        #         uuid=lemon_uuid(),
        #         name="",
        #         description="",
        #         trigger_list=list()
        #     )
        #     sm = StateMachine(
        #         uuid=lemon_uuid(),
        #         name="",
        #         description="",
        #         event_list=list(),
        #         variable_list=list(),
        #         timer_list=list()
        #     )
        #     sm.add_start_state(state_start)
        #     document_content = sm.to_dict()
        #     return_document_dict.update({DocumentContent.document_content.name: document_content})
        #     sm_request_json = dict(
        #         module_uuid=module_uuid,
        #         document_uuid=document_uuid,
        #         document_version=1,
        #         document_content=document_content
        #     )
        #     document_check_service = StateMachineDocumentCheckService(sm_request_json)
        #     await document_check_service.check()
        request.ctx.GIT_ACTIONS = [partial(self._git_action, document_uuid)]
        return json(LDR(data=return_document_dict))

    async def _git_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, document_uuid=document_uuid)


class DeleteDocument(VCHTTPMethodView):

    class DeleteDocumentObj():
        document_uuid = doc.String("文档或文件夹UUID")
        ext_tenant = doc.String("租户uuid，为空创建非拓展")
    
    @doc.consumes(DeleteDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除文档或文件夹")
    @Permission.policy(("document_uuid", Action.DELETE),)
    async def post(self, request):
        # todo: 删除文档时也要删除数据模型等
        with process_args():
            document_uuid = str(request.json.get("document_uuid"))
            app_uuid = str(request.json.get("app_uuid"))
        if not document_uuid:
            return json(LDR(Code.ARGS_ERROR))
        
        document = await engine.access.get_document_by_document_uuid(document_uuid)
        if not document:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))

        document_type = document.document_type
        if document_type == Document.TYPE.SECURITY:
            return json(LDR(IDECode.DOCUMENT_TYPE_NOT_ALLOW_DEL))
        elif document_type == Document.TYPE.DIR:
            child_document = await engine.access.get_document_by_document_puuid(
                document_uuid)
            if child_document:
                return json(LDR(IDECode.DOCUMENT_DIR_NOT_CLEAN))
        async with engine.db.objs.atomic():
            await engine.access.delete_document_by_document_uuid(document_uuid, document_type)
            await self.check_document_reference_list(app_uuid, document_uuid)
            await engine.access.delete_document_links_by_document_uuid(document_uuid)
            if document_type in {Document.TYPE.PY_MODULE}:
                await engine.access.delete_func_by_document_uuid(document_uuid)
        return json(LDR())

    async def _git_action(self, document_uuid):
        document_info = await engine.access.get_document_detail_by_document_uuid(document_uuid, need_delete=True)
        if not document_info:
            return
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.DELETE, document_info["full_path"])

    async def check_document_reference_list(self, app_uuid: str, document_uuid: str):
        reference_list = await engine.access.list_document_by_link_uuid(
            document_uuid, as_dict=False)
        if not reference_list:
            return
        to_check_list = set(reference.document_uuid for reference in reference_list)
        # 排除自己引用自己
        to_check_list.discard(document_uuid)
        all_document = await engine.access.list_document_content_by_document_uuid_list(list(to_check_list))
        await engine.service.document_check.check_all_document2(
            app_uuid, all_document, check_commit=True, gather=False)


class DeleteModule(VCHTTPMethodView):
    
    class DeleteModuleObj():
        module_uuid = doc.String("模块id")
        ext_tenant = doc.String("租户uuid，为空创建非拓展")
    
    @doc.consumes(DeleteModuleObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除模块")
    @Permission.policy(("module_uuid", Action.DELETE),)
    async def post(self, request):
        # todo: 删除文档时也要删除数据模型等
        with process_args():
            module_uuid = str(request.json.get("module_uuid"))
        if not module_uuid:
            return json(LDR(Code.ARGS_ERROR))
        
        module = await engine.access.get_module_by_module_uuid(module_uuid)
        if not module:
            return json(LDR(IDECode.MODULE_NOT_EXISTS))
        if not module.extend_from:
            child_document = await engine.access.get_common_document_by_module_uuid(module_uuid)
            if child_document:
                return json(LDR(IDECode.MODULE_NOT_CLEAN))
        await engine.access.delete_module_by_module_uuid(module_uuid)
        self.add_git_action(request, partial(self._git_action, module.module_name))
        return json(LDR())

    async def _git_action(self, module_name):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.DELETE, module_name)

class RenameDocument(VCHTTPMethodView):

    class RenameDocumentObj():
        document_uuid = doc.String("文档或文件夹UUID")
        document_name = doc.String("文档或文件夹名称")
        ext_tenant = doc.String("租户uuid，为空创建非拓展")
    
    @doc.consumes(RenameDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("重命名文档或文件夹")
    @Permission.policy(("document_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            document_uuid = str(request.json.get("document_uuid"))
            new_document_name = str(request.json.get("document_name")).strip()
            app_uuid = str(request.json.get("app_uuid"))
        if not all([document_uuid, new_document_name]):
            return json(LDR(Code.ARGS_ERROR))
        new_document_name = check_document_name(new_document_name)
        if not new_document_name:
            return json(LDR(IDECode.DOCUMENT_NAME_FAILED))
        
        document = await engine.access.get_document_by_document_uuid(document_uuid)
        module_uuid = document.module_uuid
        if not document:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        if new_document_name == document.document_name:
            return json(LDR(IDECode.DOCUMENT_NAME_SAME))
        if new_document_name.lower() != document.document_name.lower():
            other_document = await engine.access.get_document_by_module_uuid_document_name(
                module_uuid, new_document_name)
            if other_document:
                return json(LDR(IDECode.DOCUMENT_NAME_EXISTS))

        update_data = dict(
            document_name=new_document_name,
            update_timestamp=int(time.time())
        )
        document_dict = document.to_dict()
        document_type = document_dict.get("document_type")
        if document_type == EntityType.FUNC:
            if not check_lemon_name(new_document_name, chinese=True):
                return json(LDR(IDECode.FUNC_NAME_FAILED))
            if new_document_name in kwlist:
                return json(LDR(IDECode.FUNC_NAME_FORBIT_PYTHON_KEYWORD))

        if document_type == EntityType.PY_MODULE:
            if not check_lemon_name(new_document_name, chinese=True):
                return json(LDR(IDECode.PY_MODULE_NAME_FAILED))
            if new_document_name in kwlist:
                return json(LDR(IDECode.PY_MODULE_NAME_FORBID_PYTHON_KEYWORD))

        async with engine.db.objs.atomic():
            doc_old_path = None
            if getattr(request.ctx, "version_control", False):
                doc_info = await engine.access.get_document_detail_by_document_uuid(document_uuid)
                if doc_info:
                    doc_old_path = doc_info["full_path"]
            # 更新document表
            await engine.access.update_document_by_document_uuid(
                document_uuid, **update_data)
            if doc_old_path:
                doc_path = os.path.dirname(doc_old_path)
                new_path = os.path.join(doc_path, new_document_name)
                self.set_git_action_type(request, 1)
                self.add_git_action(request, partial(self.rename_action, doc_old_path, new_path))
            document_dict.update(update_data)
            document_dict.pop("id")
            document_dict.pop(DocumentModel.is_delete.name)
            if document_type == 0:
                return json(LDR(data=document_dict))
            # 重命名后的文档检查
            document_info = await engine.access.get_document_content_by_document_uuid(document_uuid)
            if document_info:
                document_info = document_info.to_dict()
                document_content = document_info.get("document_content")
                document_content.update({"name": new_document_name})
            json_data = {
                "app_uuid": app_uuid,
                "module_uuid": module_uuid,
                "document_uuid": document_uuid,
                "document_version": document.document_version,
                "document_content": document_content
            }
            await engine.access.update_document_content_by_document_uuid(
                document_uuid, **{"document_content": document_content})
            self.add_git_action(request, partial(self.sync_content_action, document_uuid))
            document_check_service = engine.service.document_check.create_service(document_type)
            error_list = await document_check_service.check(
                json_data, document_type, with_result=True,
                force_in_current_proc=True, json_return=False)
            document_info = await engine.access.get_document_content_by_document_uuid(document_uuid)
            document_content = document_info.document_content
            document_dict.update({
                "error_list": error_list, "document_content": {
                    "document_uuid": document_uuid,
                    "document_content": document_content
                }})
        return json(LDR(data=document_dict))

    async def rename_action(self, s_name, new_name):
        return await engine.git_request.sync_task_to_repo(
            SyncTORepoType.RENAME, s_name, new_name)

    async def sync_content_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(
            SyncTORepoType.SYNC_DOC, document_uuid)


class GetDocument(VCHTTPMethodView):

    class GetDocumentObj():
        document_uuid = doc.String("文档UUID")
    
    @doc.consumes(GetDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取文档信息")
    @Permission.policy(("document_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            document_uuid = str(request.json.get("document_uuid"))
        if not document_uuid:
            return json(LDR(Code.ARGS_ERROR))

        document = await engine.access.get_document_by_document_uuid(document_uuid)
        data = {
            DocumentModel.document_uuid.name: document.document_uuid,
            DocumentModel.document_name.name: document.document_name,
            DocumentModel.document_puuid.name: document.document_puuid,
            DocumentModel.document_type.name: document.document_type,
            DocumentModel.document_version.name: document.document_version,
            DocumentModel.update_timestamp.name: document.update_timestamp,
            DocumentModel.create_timestamp.name: document.create_timestamp, 
            DocumentModel.module_uuid.name: document.module_uuid
        }
        return json(LDR(data=data))


class GetAppConfigDocument(VCHTTPMethodView):

    class GetAppConfigDocumentObj():
        document_uuid = doc.String("文档UUID")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(GetAppConfigDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取文档内容")
    @Permission.policy(("document_uuid", Action.SELECT),)
    async def post(self, request):
        data = await get_document_data(request)
        document_content = data.get("document_content", {})
        if not document_content.get("extension_pack"):
            document_content["extension_pack"] = {"packages": [], "sysPackages": {}}
        if not document_content.get("app_deploy_config"):
            document_content["app_deploy_config"] = {
                "sync": False, "version": "", "env_type": None, "sandbox_uuid": "", "build_platform": []
                }
        result = json(LDR(data=data)) if isinstance(data, dict) else data
        return result

class GetDocumentContent(VCHTTPMethodView):

    class GetDocumentContentObj():
        document_uuid = doc.String("文档UUID")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    @doc.consumes(GetDocumentContentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取文档内容")
    @Permission.policy(("document_uuid", Action.SELECT),)
    async def post(self, request):
        data = await get_document_data(request)
        result = json(LDR(data=data)) if isinstance(data, dict) else data
        return result


class ModelNameExists(VCHTTPMethodView):

    class ModelNameExistsObj():
        module_uuid = doc.String("模块UUID")
        model_name = doc.String("数据模型名称")
    
    @doc.consumes(ModelNameExistsObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("检查数据模型名称")
    @Permission.policy(("module_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            module_uuid = str(request.json.get("module_uuid"))
            model_name = str(request.json.get("model_name"))
        if not all([module_uuid]):
            return json(LDR(Code.ARGS_ERROR))
        if not check_lemon_name(model_name, chinese=True, size=40):
            return json(LDR(IDECode.MODULE_NAME_FAILED))
        
        model = await engine.access.get_model_by_module_uuid_model_name(
            module_uuid, model_name)
        if model:
            return json(LDR(data=1))
        return json(LDR(data=0))


class SaveDocumentContent(VCHTTPMethodView):

    class SaveDocumentContentObj():
        module_uuid = doc.String("模块UUID")
        document_uuid = doc.String("文档UUID")
        document_version = doc.String("文档版本")
        document_content = doc.String("文档内容")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(SaveDocumentContentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("保存文档内容")
    @Permission.policy(("document_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            module_uuid = str(request.json.get("module_uuid"))
            document_uuid = str(request.json.get("document_uuid"))
            document_content = request.json.get("document_content")
            ext_tenant = request.json.get("ext_tenant")

        cur_document = await engine.access.get_document_by_document_uuid(
            document_uuid, ext_tenant=ext_tenant)
        document_type = cur_document.document_type
        if document_type == DocumentType.MODEL:
            # 防止模型文档被清空
            models = document_content.get("models", [])
            if not models:
                model_list = await engine.access.list_model_by_module_uuid(module_uuid)
                if model_list:
                    return json(LDR(IDECode.MODEL_DOCUMENT_CLEAN_ERROR))
        if engine.service.document_check.create_service(document_type):
            await engine.access.update_document_content_by_document_uuid(
                document_uuid, document_content=document_content)
        return json(LDR(Code.OK))


class CheckDocument(VCHTTPMethodView):

    class CheckModelDocumentObj():
        module_uuid = doc.String("模块UUID")
        document_uuid = doc.String("文档UUID")
        document_version = doc.String("文档版本")
        document_content = doc.String("文档内容")
        branch_uuid = doc.String("branch uuid")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(CheckModelDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("检查数据文档变更")
    @Permission.policy(("document_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            document_uuid = str(request.json.get("document_uuid"))
            ext_tenant = request.json.get("ext_tenant")
            timestamp = request.json.get("timestamp")
        if not timestamp:
            return json(LDR(Code.ARGS_ERROR))

        cur_document = await engine.access.get_document_by_document_uuid(
            document_uuid, ext_tenant=ext_tenant)
        if app_uuid and cur_document.app_uuid != app_uuid:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        document_type = cur_document.document_type
        document_check_service = engine.service.document_check.create_service(document_type)
        if document_check_service:
            # 已经把修改保存到数据库，不应该等待check结果，检查完成ws推送再获取结果
            result = await document_check_service.check(request.json, document_type,
                                                        force_run_in_pool=True,
                                                        # with_result=True,
                                                        json_return=False)
            request.ctx.GIT_ACTIONS = [partial(self._get_action, document_uuid)]
            if isinstance(result, HTTPResponse):
                return result
            document_draft = await engine.access.get_document_draft_by_document_uuid(document_uuid)
            return json(LDR(data={"timestamp": document_draft.timestamp.timestamp()}))
        else:
            return json(LDR(Code.ERROR))

    async def _get_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, document_uuid)


class CreateWorkflowContent(VCHTTPMethodView):
    
    class CreateWorkflowContentObj():
        module_uuid = doc.String("模块UUID")
        document_uuid = doc.String("文档UUID")
        document_version = doc.String("文档版本")
        document_content = doc.String("文档内容")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(CreateWorkflowContentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("创建简易工作流文档")
    @Permission.policy(("module_uuid", Action.MODIFY),)
    async def post(self, request):
        """创建简易工作流content, 由于没有创建Document, 因此无法进行正常的删除等操作
        Workflow表中会记录document_uuid, 可以查询
        通过 get_document_content_by_workflow_uuid
        """
        with process_args():
            document_uuid = str(request.json.get("document_uuid"))
            ext_tenant = request.json.get("ext_tenant")
        document_type = DocumentType.WORKFLOW
        document_check_service = engine.service.document_check.create_service(document_type)
        if document_check_service:
            json_return = False
            error_list = await document_check_service.check(request.json, document_type, json_return=json_return)
            self.add_git_action(request, partial(self._git_action, document_uuid))
        return json(LDR(Code.OK))

    async def _git_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, document_uuid)
    
class GetSimpleWorkflowContent(VCHTTPMethodView):

    @Permission.transcation()
    async def post(self, request):
        with process_args():
            document_uuid = str(request.json.get("document_uuid"))
            ext_tenant = request.json.get("ext_tenant")
        document_content = await engine.access.get_document_content_by_document_uuid(document_uuid)
        data = {
            DocumentModel.document_uuid.name: document_uuid,
            DocumentModel.document_version.name: 1,
            "is_extension": 0,
        }
        if document_content is None:
            data.update({DocumentContent.document_content.name: dict()})
        else:
            data.update({DocumentContent.document_content.name: document_content.document_content})
        return json(LDR(data=data))


class UploadMallFile(HTTPMethodView):
    class UploadMallFileObj():
        file_name = doc.String("文件名称", name="file_name")
        file = doc.File("文件", name="file")
    
    @doc.consumes(UploadMallFileObj.file, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(UploadMallFileObj.file_name, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("上传商城文件")
    async def post(self, request):
        with process_args():
            app_uuid = str(request.form.get("app_uuid"))
            extension_uuid = str(request.form.get("extension_uuid"))
            file_data = request.files.get("file")
        
        headers = {
            "x-oss-meta-filetype": "lemonui"
        }
        tmp_oss_url = "/".join(["design/mall", app_uuid, file_data.name])
        design_oss = DesignOss()
        await design_oss.put_oss_object(
            tmp_oss_url, file_data.body, headers, local_file=False)
        to_dir = engine.config.CUSTOM_STATIC_PATH_PREFIX + "/" + app_uuid + "/" + "temp"
        if not os.path.exists(to_dir):
            os.makedirs(to_dir)
        to_file = to_dir + "/" + file_data.name
        await design_oss.get_oss_object(url=tmp_oss_url, to_file=to_file)

        # 解压
        f = zipfile.ZipFile(to_file)
        for b in f.namelist():
            if b == "config/config.json": 
                f.extract(b, engine.config.CUSTOM_STATIC_PATH_PREFIX)
        f.close()
        
        with open(
            engine.config.CUSTOM_STATIC_PATH_PREFIX + "/config/config.json",
            mode="r", encoding="utf-8") as fr:
            config = ujson.load(fr)
        
        version = config.get("version")
        tool_uuid = lemon_uuid()
        if not version:
            return json(LDR(IDECode.UI_COMPONENT_VERSION_NOT_EXIST))  # config文件不正确
            
        file_path = "/".join(["design/mall", app_uuid, extension_uuid, tool_uuid + ".lemonui"])

        await DesignOss().put_oss_object(
            file_path, file_data.body, headers, local_file=False)
        result = {
            "ui_package_url": file_path, 
            "version": version, 
            "tool_uuid": tool_uuid, 
            "component_id": config.get("id"),
            "component_type": config.get("category"),
            "component_name": config.get("component_name"),
            "extension_uuid": extension_uuid, 
            "package_name": file_data.name
        }
        return json(LDR(data=result))


class UploadFile(HTTPMethodView):

    class UploadFileObj():
        document_uuid = doc.String("文档 UUID", name="document_uuid")
        file = doc.File("文件", name="file")
        tmp = doc.String("上传的是否是临时文件", name="tmp")

    @doc.consumes(UploadFileObj.file, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(UploadFileObj.document_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("上传文件或图片")
    @Permission.policy(("document_uuid", Action.MODIFY), request_body_key="form")
    async def post(self, request):
        with process_args():
            document_uuid = str(request.form.get("document_uuid"))
            component_uuid = str(request.form.get("component_uuid"))
            file_list = request.files.getlist("file")
            tmp = str(request.form.get("tmp"))
        tmp = True if tmp == "1" else False
        osshelper = DesignOss(document_uuid, component_uuid)
        url_list = await osshelper.upload_file(file_list, tmp=tmp)
        return json(LDR(data={"url_list": url_list}))

class MoveTmpFile(HTTPMethodView):
    
    class MoveTmpFileObj():
        document_uuid = doc.String("文档 UUID", name="document_uuid")
        url_list = doc.List("文件url 列表", name="url_list")
    
    @doc.consumes(MoveTmpFileObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("将临时文件变成正式文件")
    @Permission.policy(("document_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            document_uuid = str(request.json.get("document_uuid"))
            url_list = request.json.get("url_list")
        osshelper = DesignOss(document_uuid)
        url_list = await osshelper.move_file_from_tmp(url_list)
        return json(LDR(data={"url_list": url_list}))

class DeleteFile(HTTPMethodView):
    
    class DeleteFileObj():
        document_uuid = doc.String("文档 UUID", name="document_uuid")
        url_list = doc.List("文件url 列表", name="url_list")
    
    @doc.consumes(DeleteFileObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("批量删除文件")
    @Permission.policy(("document_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            document_uuid = str(request.json.get("document_uuid"))
            url_list = request.json.get("url_list")
        osshelper = DesignOss(document_uuid)
        result = await osshelper.delete_file_list(url_list)
        return json(LDR(data=result))


class GetFileUrl(HTTPMethodView):
    
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            url = str(request.json.get("url", ""))
        url = engine.oss_handler.bucket.sign_url("GET", url, 360, slash_safe=True)
        domain_url = DesignOss().gen_oss_domain_url(url)
        app_log.info(f"oss resource url: {domain_url}")
        return json(LDR(data={"url": domain_url}))
    
    async def get(self, request):
        with process_args():
            url = str(request.args.get("url", ""))
            real_user_uuid, user_uuid = get_login_user_uuid(request)
        assert url.startswith("design")
        # TODO 检查用户拥有资源所属APP的权限
        object_stream = await DesignOss().get_oss_object(request, stream=True)
        if not object_stream:
            return json(LDR(Code.ERROR))
        # object_body = object_stream.body
        # content_type = object_stream.content_type
        return object_stream


class UploadMallImage(HTTPMethodView):
    class UploadMallImageObj():
        image_name = doc.String("图片名称", name="image_name")
        image_type = doc.Integer("图片类型（SVG必须） 1：JPG 2：PNG 3：BMP 4：GIF 5：SVG", name="image_type")
        image = doc.File("图片", name="image")
    
    @doc.consumes(UploadMallImageObj.image, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(UploadMallImageObj.image_type, content_type=DOC.FORM_DATA, location="formData")
    @doc.consumes(UploadMallImageObj.image_name, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("上传商城图片")
    async def post(self, request):
        with process_args():
            image_name = str(request.form.get("image_name"))
            app_uuid = str(request.form.get("app_uuid"))
            extension_uuid = str(request.form.get("extension_uuid"))
            image_type = int(request.form.get("image_type", -1))
            image = request.files.get("image")
        image_path = "/".join(["design/mall", app_uuid, extension_uuid, image_name])
        headers = {
            "x-oss-meta-filetype": "image"
        }
        await DesignOss().put_oss_object(
            image_path, image.body, headers, local_file=False)
        return json(LDR(data={"image_path": image_path}))


class UploadImage(VCHTTPMethodView):

    class UploadImageObj():
        document_uuid = doc.String("文档UUID", name="document_uuid")
        image_uuid = doc.String("图片UUID", name="image_uuid")
        image_type = doc.Integer(
            "图片类型（SVG必须） 1：JPG 2：PNG 3：BMP 4：GIF 5：SVG",
            name="image_type")
        image = doc.File("图片", name="image")
    
    @doc.consumes(
            UploadImageObj.image, content_type=DOC.FORM_DATA,
            location="formData", required=True)
    @doc.consumes(UploadImageObj.image_type, content_type=DOC.FORM_DATA,
                  location="formData")
    @doc.consumes(UploadImageObj.image_uuid, content_type=DOC.FORM_DATA,
                  location="formData", required=True)
    @doc.consumes(UploadImageObj.document_uuid, content_type=DOC.FORM_DATA,
                  location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("上传图片表图片")
    @Permission.policy(("document_uuid", Action.MODIFY),
                       request_body_key="form")
    async def post(self, request):
        with process_args():
            document_uuid = str(request.form.get("document_uuid"))
            image_uuid = str(request.form.get("image_uuid"))
            image_type = int(request.form.get("image_type", -1))
            image = request.files.get("image")
        image_instance = await engine.access.get_image_by_image_uuid(
            image_uuid)
        is_create = image_instance is None

        image_sql_dict = await process_image(document_uuid, image_uuid,
                                             image_type, image, is_create)
        if image_sql_dict.get("url"):
            await save_image(image_uuid, image_sql_dict, is_create)
        return json(LDR(data=image_sql_dict))


class UploadModuleThemeImage(VCHTTPMethodView):

    class UploadModuleThemeImageObj():
        document_uuid = doc.String("文档UUID", name="document_uuid")
        image_uuid = doc.String("图片UUID", name="image_uuid")
        image_type = doc.Integer(
            "图片类型（SVG必须） 1：JPG 2：PNG 3：BMP 4：GIF 5：SVG",
            name="image_type")
        image = doc.File("图片", name="image")
    
    @doc.consumes(UploadModuleThemeImageObj.image, content_type=DOC.FORM_DATA,
                  location="formData", required=True)
    @doc.consumes(
        UploadModuleThemeImageObj.image_type, content_type=DOC.FORM_DATA,
        location="formData")
    @doc.consumes(
        UploadModuleThemeImageObj.image_uuid, content_type=DOC.FORM_DATA,
        location="formData", required=True)
    @doc.consumes(
        UploadModuleThemeImageObj.document_uuid, content_type=DOC.FORM_DATA,
        location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("上传模块主题图片")
    @Permission.policy(("document_uuid", Action.MODIFY),
                       request_body_key="form")
    async def post(self, request):
        with process_args():
            document_uuid = str(request.form.get("document_uuid"))
            image_uuid = str(request.form.get("image_uuid"))
            image_type = int(request.form.get("image_type", -1))
            image = request.files.get("image")
        image_instance = await engine.access.get_image_by_image_uuid(
            image_uuid)
        is_create = image_instance is None
        
        image_sql_dict = await process_image(document_uuid, image_uuid,
                                             image_type, image, is_create)

        domain_url = get_domain_url_by_image_path(image_sql_dict)
        domain_url = domain_url.replace("http://", "https://")
        image_sql_dict.update({ImageTableModel.url.name: domain_url})

        await save_image(image_uuid, image_sql_dict, is_create)
        return json(LDR(data=image_sql_dict))   
    

class GetImageData(HTTPMethodView):

    # @Permission.transcation()
    async def post(self, request):
        with process_args():
            url = str(request.json.get("url", ""))
        url = engine.oss_handler.bucket.sign_url("GET", url, 360, slash_safe=True)
        domain_url = DesignOss().gen_oss_domain_url(url)
        app_log.info(f"oss resource url: {domain_url}")
        return json(LDR(data={"url": domain_url}))
    
    async def get(self, request):
        with process_args():
            url = str(request.args.get("url", ""))
            real_user_uuid, user_uuid = get_login_user_uuid(request)
        assert url.startswith("design")
        # TODO 检查用户拥有资源所属APP的权限
        
        object_stream = await DesignOss().get_oss_object(request)
        if not object_stream:
            return json(LDR(Code.ERROR))
        object_body = object_stream.body
        content_type = object_stream.content_type
        return raw(object_body, headers={"content-type": content_type})
        

class ListAPPModel(VCHTTPMethodView):

    class ListAPPModelObj():
        app_uuid = doc.String("应用UUID")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListAPPModelObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有数据模型")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_model_groupby_module(request, with_sys=True)


class ListAPPFunc(VCHTTPMethodView):

    class ListAPPFuncObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPFuncObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有云函数")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_func_groupby_module(request)
    

class ListAPPExcelDoc(VCHTTPMethodView):
    
    class ListAPPExcelDocObj():
        app_uuid = doc.String("应用UUID")
        model_uuid = doc.String("模型id")
    
    @doc.consumes(ListAPPExcelDocObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有excel导入文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_excel_doc_groupby_module(request)

class ExcelTemplateJump(VCHTTPMethodView):
    
    class PrintTemplateJumpObj():
        document_uuid = doc.String("文档uuid")
    
    @doc.consumes(PrintTemplateJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击excel模板跳转到excel模板所在位置")
    @Permission.policy(("document_uuid", Action.SELECT),)

    async def post(self, request):
        with process_args():
            document_uuid = request.json.get("document_uuid", "")
        model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.document_name 
        )
        query = model.select(*fields).where(model.document_uuid==document_uuid)
        select_field = await engine.access.select_obj_by_query(model, query=query)
        return json(LDR(data=select_field))

class ListAPPExportDoc(VCHTTPMethodView):
    
    class ListAPPExportDocObj():
        app_uuid = doc.String("应用UUID")
        model_uuid = doc.String("模型id")
    
    @doc.consumes(ListAPPExportDocObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有excel导入文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_export_template_doc_groupby_module(request)

class ExportTemplateJump(VCHTTPMethodView):
    
    class ExportTemplateJumpObj():
        document_uuid = doc.String("文档uuid")
    
    @doc.consumes(ExportTemplateJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击excel模板跳转到excel模板所在位置")
    @Permission.policy(("document_uuid", Action.SELECT),)

    async def post(self, request):
        with process_args():
            document_uuid = request.json.get("document_uuid", "")
        model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.document_name 
        )
        query = model.select(*fields).where(model.document_uuid==document_uuid)
        select_field = await engine.access.select_obj_by_query(model, query=query)
        return json(LDR(data=select_field))
    
    
class RESTfulJump(VCHTTPMethodView):
    
    class RESTfulJumpJumpObj():
        restful_uuid = doc.String("RESTful UUID")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(
        RESTfulJumpJumpObj, content_type=DOC.JSON_TYPE,
        location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击restful API跳转到API所在位置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            restful_uuid = request.json.get("restful_uuid", "")
        model = RestfulTableModel
        document_model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.restful_uuid,
            document_model.document_name 
        )
        query = model.select(*fields).join(
            document_model,
            on=(model.document_uuid == document_model.document_uuid)).where(
                model.restful_uuid == restful_uuid)
        app_log.info(f"search field: {query}")
        select_field = await engine.access.select_obj_by_query(
            model, query=query)
        return json(LDR(data=select_field))


class ListAPPStateMachine(VCHTTPMethodView):

    class ListAPPStateMachineObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPStateMachineObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有状态机")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_sm_groupby_module(request)

class ListAPPRuleChain(VCHTTPMethodView):
    class ListAPPRuleChainObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPRuleChainObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有状态机")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_rc_groupby_module(request)    

class ListAPPAllRuleChain(VCHTTPMethodView):

    class ListAPPAllRuleChainObj:
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ListAPPAllRuleChainObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用所有规则引擎")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        data = await engine.access.list_rulechain_by_app_uuid(app_uuid, as_dict=True)
        return json(LDR(data=list(data)))

class ListAPPAllStateMachine(VCHTTPMethodView):

    class ListAPPAllStateMachineObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPAllStateMachineObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用所有状态机")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        model = StateMachine
        fields = (
            model.app_uuid, model.module_uuid, model.document_uuid, 
            model.sm_uuid, model.sm_name, model.display_name, model.variable_list
        )
        data = await engine.access.list_sm_by_app_uuid(app_uuid, fields=fields, as_dict=True)
        return json(LDR(data=list(data)))


class ListAPPEvent(VCHTTPMethodView):

    class ListAPPEventObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPEventObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有事件")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_event_groupby_module(request)


class ListAPPConst(VCHTTPMethodView):

    class ListAPPConstObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPConstObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有常量")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_const_groupby_module(request)


class ListAPPEnum(VCHTTPMethodView):

    class ListAPPEnumObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPEnumObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有枚举")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_enum_groupby_module(request)


class ListAPPImage(VCHTTPMethodView):

    class ListAPPImageObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPImageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有图片")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_image_groupby_module(request)


class ListAPPPage(VCHTTPMethodView):

    class ListAPPPageObj():
        app_uuid = doc.String("应用UUID")
        open_type = doc.List("页面打开类型（可选）： 0 弹窗；1 跳转；2 抽屉")
        form_count = doc.Integer("需要几个表单的页面（可选）")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListAPPPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有页面")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_page_groupby_module(request)

class ListAPPModuleDir(VCHTTPMethodView):

    class ListAPPModuleDirObj():
        app_uuid = doc.String("应用UUID")
        open_type = doc.List("页面打开类型（可选）： 0 弹窗；1 跳转；2 抽屉")
        form_count = doc.Integer("需要几个表单的页面（可选）")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListAPPModuleDirObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有页面")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_module_dir_groupby_module(request)

class ListAPPWorkflow(VCHTTPMethodView):

    class ListAPPWorkflowObj():
        app_uuid = doc.String("应用UUID")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListAPPWorkflowObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有工作流")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_workflow_groupby_module(request)


class ListAPPApprovalflow(VCHTTPMethodView):

    class ListAPPApprovalflowObj():
        app_uuid = doc.String("应用UUID")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListAPPApprovalflowObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有审批流")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_approvalflow_groupby_module(request)


class ListAPPPrint(VCHTTPMethodView):

    class ListAPPPrintObj():
        app_uuid = doc.String("应用UUID")
        form_count = doc.Integer("需要几个表单的页面（可选）")
        model_uuid = doc.String("打印模板的表单模型UUID（可选）")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListAPPPrintObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有打印模板")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_print_groupby_module(request)


class ListAPPLabelPrint(VCHTTPMethodView):

    class ListAPPLabelPrintObj():
        app_uuid = doc.String("应用UUID")
        model_uuid = doc.String("标签打印数据源UUID（可选）")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListAPPLabelPrintObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有标签打印")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_label_print_groupby_module(request) 


class ListAPPRestful(VCHTTPMethodView):

    class ListAPPRestfulObj():
        app_uuid = doc.String("应用UUID")
        basic_auth = doc.Boolean("基本认证，可选，默认 true")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListAPPRestfulObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有RESTful接口")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_restful_groupby_module(
            request)


class GetAppModel(VCHTTPMethodView):

    class GetAppModelObj():
        app_uuid = doc.String("应用UUID")
        model_uuid = doc.String("数据模型UUID")
        show_many = doc.Boolean("显示多端关联（可选）")
    
    @doc.consumes(GetAppModelObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用的数据模型")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            model_uuid = str(request.json.get("model_uuid"))
            show_many = request.json.get("show_many", True)
            app_uuid = str(request.json.get("app_uuid"))
        if not model_uuid:
            return json(LDR(Code.ARGS_ERROR))
        model = await engine.access.select_model_by_model_uuid(model_uuid)
        fields = await engine.access.list_field_by_model_uuid(model_uuid)
        fields = list(fields)
        r_list = list()
        target_list = await engine.access.list_relationship_by_source_model(
            model_uuid, show_many=show_many, 
            find_name_field=True, model_dict=model)
        source_list = await engine.access.list_relationship_by_target_model(
            model_uuid, show_many=show_many, app_uuid=app_uuid)
        r_list.extend(target_list)
        r_list.extend(source_list)
        model.update({"fields": fields, "relationships": r_list})
        return json(LDR(data=model))


class GetAppFunc(VCHTTPMethodView):

    class GetAppFuncObj():
        app_uuid = doc.String("应用UUID")
        func_uuid = doc.String("云函数UUID")
    
    @doc.consumes(GetAppFuncObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用的云函数")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            func_uuid = str(request.json.get("func_uuid"))
        if not func_uuid:
            return json(LDR(Code.ARGS_ERROR))
        func = await engine.access.select_func_by_func_uuid(func_uuid)
        return json(LDR(data=func))


class GetAppEvent(VCHTTPMethodView):

    class GetAppEventObj():
        app_uuid = doc.String("应用UUID")
        event_uuid = doc.String("事件UUID")
    
    @doc.consumes(GetAppEventObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用的事件")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            event_uuid = str(request.json.get("event_uuid"))
        if not event_uuid:
            return json(LDR(Code.ARGS_ERROR))
        event = await engine.access.select_event_by_event_uuid(event_uuid)
        return json(LDR(data=event))


class CreateToolCategory(HTTPMethodView):

    class CreateToolCategoryObj():
        app_uuid = doc.String("应用UUID")
        category_name = doc.String("工具分类名称")
        description = doc.String("工具分类描述")
        entity_type = doc.Integer("工具实体类型 " + IDEViewDoc.ENTITY_TYPE)
    
    @doc.consumes(CreateToolCategoryObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("创建应用工具箱分类")
    @Permission.policy(("app_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            category_name = str(request.json.get("category_name"))
            description = str(request.json.get("description", ""))
            entity_type = int(request.json.get("entity_type"))
        category_name = check_lemon_name(category_name, chinese=True)
        if not all([app_uuid, category_name]) or entity_type not in EntityType.ALL:
            return json(LDR(Code.ARGS_ERROR))

        category_type = ToolCategoryType.USER
        
        category = await engine.access.get_tool_category_with_unique_key(
            category_type, app_uuid, entity_type, category_name)
        if category:
            return json(LDR(IDECode.TOOL_CATEGORY_EXISTS))
        
        category_uuid = lemon_uuid()
        category_sort = round(time.time(), 6)
        category_dict = {
            ToolCategoryModel.app_uuid.name: app_uuid,
            ToolCategoryModel.category_uuid.name: category_uuid,
            ToolCategoryModel.category_name.name: category_name,
            ToolCategoryModel.category_type.name: category_type,
            ToolCategoryModel.category_sort.name: category_sort,
            ToolCategoryModel.description.name: description,
            ToolCategoryModel.entity_type.name: entity_type
        }
        await engine.access.create_tool_category(**category_dict)
        return json(LDR(data=category_dict))


class ListToolCategory(HTTPMethodView):

    class ListToolCategoryObj():
        app_uuid = doc.String("应用UUID")
        entity_type = doc.Integer("实体类型 "+ IDEViewDoc.ENTITY_TYPE)
    
    @doc.consumes(ListToolCategoryObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用工具箱分类")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_tool_category(request)


class CreateTool(HTTPMethodView):

    class CreateToolObj():
        app_uuid = doc.String("应用UUID")
        category_uuid = doc.String("工具分类UUID")
        tool_name = doc.String("工具名称")
        tool_class = doc.String("工具种类：1 云函数  2 页面组件")
        description = doc.String("工具描述")
        content = doc.String("工具内容：云函数UUID 或 页面组件UUID")
        icon_type = doc.Integer("工具图标分类：0 系统图标  1 字体图标  2 图片表")
        icon = doc.String("工具图标UUID")
        drag_type = doc.String("工具绘制分类")
    
    @doc.consumes(CreateToolObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("创建应用工具")
    @Permission.policy(("app_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            category_uuid = str(request.json.get("category_uuid"))
            tool_name = str(request.json.get("tool_name"))
            tool_class = int(request.json.get("tool_class"))
            description = str(request.json.get("description", ""))
            content = str(request.json.get("content", ""))
            icon_type = int(request.json.get("icon_type"))
            icon = str(request.json.get("icon"))
            drag_type = str(request.json.get("drag_type"))
        tool_name = check_lemon_name(tool_name, chinese=True)
        if (not all([app_uuid, tool_name, category_uuid, icon, drag_type]) 
            or tool_class not in ToolClass.ALL 
            or icon_type not in IconType.ALL
            or drag_type not in DragType.ALL):
            return json(LDR(Code.ARGS_ERROR))
        
        category = await engine.access.get_tool_category_by_uuid(category_uuid)
        if not category:
            return json(LDR(IDECode.TOOL_CATEGORY_NOT_EXISTS))
        
        tool = await engine.access.get_user_tool_with_unique_key(category_uuid, tool_name)
        if tool:
            return json(LDR(IDECode.TOOL_EXISTS))

        tool_uuid = lemon_uuid()
        tool_dict = {
            UserTool.app_uuid.name: app_uuid,
            UserTool.category_uuid.name: category_uuid,
            UserTool.tool_uuid.name: tool_uuid,
            UserTool.tool_name.name: tool_name,
            UserTool.tool_class.name: tool_class,
            UserTool.description.name: description,
            UserTool.content.name: content,
            UserTool.icon_type.name: icon_type,
            UserTool.icon.name: icon,
            UserTool.drag_type.name: drag_type
        }
        await engine.access.create_user_tool(**tool_dict)
        return json(LDR(data=tool_dict))


class ListTool(HTTPMethodView):

    class ListToolObj():
        app_uuid = doc.String("应用UUID")
        entity_type = doc.Integer("实体类型 " + IDEViewDoc.ENTITY_TYPE)
    
    @doc.consumes(ListToolObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用工具")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        return await engine.service.query.list_tool_groupby_category(request)


class ListModelRelationship(VCHTTPMethodView):

    class ListModelRelationshipObj():
        app_uuid = doc.String("应用UUID")
        model_uuid = doc.String("模型UUID")
    
    @doc.consumes(ListModelRelationshipObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出模型的关联")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            model_uuid = str(request.json.get("model_uuid"))
            app_uuid = str(request.json.get("app_uuid"))
        r_list = list()
        model = await engine.access.select_model_by_model_uuid(model_uuid)
        if not model:
            return json(LDR(IDECode.MODEL_NOT_EXISTS))
        target_list = await engine.access.list_relationship_by_source_model(
            model_uuid, rename_self_joins=True, need_sys=False, model_dict=model)
        source_list = await engine.access.list_relationship_by_target_model(
            model_uuid, rename_self_joins=True, app_uuid=app_uuid)
        r_list.extend(target_list)
        r_list.extend(source_list)
        model.update({"relationships": r_list})
        return json(LDR(data=model))


class ListModelField(VCHTTPMethodView):

    class ListModelFieldObj():
        app_uuid = doc.String("应用UUID")
        model_uuid = doc.String("模型UUID")
        field_type = doc.List(
            "字段类型（可选） 3：枚举；10：文本；11：整数；12：小数；13：布尔；14：时间日期；20：文件")
        show_many = doc.Boolean("显示多端关联（可选）")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
    
    @doc.consumes(ListModelFieldObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出模型的字段和关联")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            model_uuid = str(request.json.get("model_uuid"))
            field_type = request.json.get("field_type", list())
            ext_tenant = request.json.get("ext_tenant")
            if field_type is not None:
                field_type = list(field_type)
            show_many = request.json.get("show_many", True)
            rename_self_joins = request.json.get("rename_self_joins", True)
        model = await engine.access.select_model_by_model_uuid(model_uuid, ext_tenant=ext_tenant)
        if not model:
            return json(LDR(IDECode.MODEL_NOT_EXISTS))
        need_sys = True
        fields = await engine.access.list_field_by_model_uuid_field_type(
            model_uuid, field_type, ext_tenant=ext_tenant, need_sys=need_sys)
        new_fields = list()
        if model_uuid in SystemTable.UUIDS:
            need_sys = False
        for f in fields:
            hierarchy_list = f.get(ModelField.hierarchy_list.name)
            f.update({"hierarchy_level": len(hierarchy_list)})
            new_fields.append(f)
        r_list = list()
        target_list = await engine.access.list_relationship_by_source_model(
            model_uuid, show_many=show_many, rename_self_joins=rename_self_joins, 
            need_sys=need_sys, model_dict=model)
        target_need_sys, models = False, []
        if model_uuid in [SystemTable.tenant_user_uuid, SystemTable.workflow_instance_table_name]:
            target_need_sys = True
            models = await engine.access.list_model_by_app_uuid(app_uuid)
        source_list = await engine.access.list_relationship_by_target_model(
            model_uuid, show_many=show_many, rename_self_joins=rename_self_joins,
            need_sys=target_need_sys, models=models, app_uuid=app_uuid)
        r_list.extend(target_list)
        r_list.extend(source_list)
        self_joins_relationship_uuids = set()
        final_r_list = list()
        if not rename_self_joins:
            # 这里是给模块安全用户选择用的, 进行去重
            for r in r_list:
                if r["relationship_uuid"] not in self_joins_relationship_uuids:
                    self_joins_relationship_uuids.add(r["relationship_uuid"])
                    final_r_list.append(r)
        else:
            final_r_list = r_list
        model.update({"fields": new_fields, "relationships": final_r_list})
        return json(LDR(data=model))


class GetShortcutSetting(VCHTTPMethodView):

    class GetShortcutSettingObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(GetShortcutSettingObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取关联模型信息")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            model_uuid = str(request.json.get("model_uuid"))
            tenant_user_uuid = SystemTable.tenant_user_uuid

            show_many = request.json.get("show_many", True)
            rename_self_joins = request.json.get("rename_self_joins", True)

            ext_tenant = request.json.get("ext_tenant")
        r_list = []
        rela_model_owner = ""
        result = {"SettingConditions": [], "FieldSorting": [], "FilteringGroupedResult": []}
        model = await engine.access.select_model_by_model_uuid(model_uuid, ext_tenant=ext_tenant)

        target_list = await engine.access.list_relationship_by_source_model(
            tenant_user_uuid, show_many=show_many, rename_self_joins=rename_self_joins,
            need_sys=False, model_dict=model)

        models = await engine.access.list_model_by_app_uuid(app_uuid)
        source_list = await engine.access.list_relationship_by_target_model(
            model_uuid, show_many=show_many, rename_self_joins=rename_self_joins,
            need_sys=True, models=models, app_uuid=app_uuid)
        r_list.extend(target_list)
        r_list.extend(source_list)
        for rela_dict in r_list:
            if rela_dict.get("source_model") == model_uuid:
                if rela_dict.get("frontref") == "_owner":
                    rela_model_owner = rela_dict.get("relationship_uuid")
                elif rela_dict.get("frontref") == "_reviser":
                    rela_model_reviser = rela_dict.get("relationship_uuid")
        # 系统字段没有创建人
        if not rela_model_owner:
            return json(LDR(data=result))
        is_owner_0 = get_shortcut_condition_dict(0, rela_model_owner, model_uuid)
        is_reviser_0 = get_shortcut_condition_dict(0, rela_model_reviser, model_uuid)
        is_sub_owner = get_shortcut_condition_dict("need_func", editor_type="_owner")
        is_sub_reviser = get_shortcut_condition_dict("need_func", editor_type="_reviser")
        SettingConditions = [
            {"name": "创建人是我", "conditon": [is_owner_0]},
            {"name": "修改人是我", "conditon": [is_reviser_0]},
            {"name": "创建人是我主部门的下属", "conditon": [is_sub_owner]},
            {"name": "修改人是我主部门的下属", "conditon": [is_sub_reviser]}
            ]
        result.update({"SettingConditions": SettingConditions})
        return json(LDR(data=result))


class GetRelationshipModel(VCHTTPMethodView):

    class GetRelationshipModelObj():
        app_uuid = doc.String("应用UUID")
        relationship_dict = doc.String("关联UUID字典")
    
    @doc.consumes(GetRelationshipModelObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取关联模型信息")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            relationship_dict = request.json.get("relationship_dict")
        source_relationship_list = relationship_dict.get("1")
        target_relationship_list = relationship_dict.get("0")
        source_model_list = await engine.access.select_model_by_relationship_list_is_source(
            source_relationship_list, True)
        target_model_list = await engine.access.select_model_by_relationship_list_is_source(
            target_relationship_list, False)
        source_model_dict = {model.get("relationship_uuid") + "-1" : model for model in source_model_list}
        target_model_dict = {model.get("relationship_uuid") + "-0" : model for model in target_model_list}
        source_model_dict.update(target_model_dict)
        return json(LDR(data=source_model_dict))


class GetEntityName(VCHTTPMethodView):

    class GetEntityNameObj():
        app_uuid = doc.String("应用UUID")
        entity_uuid_list = doc.List("实体UUID列表")
        entity_type = doc.Integer("实体类型 " + IDEViewDoc.ENTITY_TYPE_ALL)
    
    @doc.consumes(GetEntityNameObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("UUID转换名称")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            entity_uuid_list = list(request.json.get("entity_uuid_list"))
            entity_type = int(request.json.get("entity_type"))
        return await engine.service.query.get_entity_name(entity_uuid_list, entity_type)


class ListPageModel(VCHTTPMethodView):

    class ListPageModelObj():
        app_uuid = doc.String("应用UUID")
        page_uuid = doc.List("页面UUID")
        component_type = doc.Integer("要获取模型的数据容器的类型（可选）")
    
    @doc.consumes(ListPageModelObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面数据容器的模型信息")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            page_uuid = str(request.json.get("page_uuid"))
            component_type = request.json.get("component_type", None)
        model = PageModel
        fields = [
            model.app_uuid, model.module_uuid, model.page_uuid, model.page_name, model.container_model
        ]
        data = await engine.access.select_page_by_page_uuid(page_uuid, fields=fields)
        if not data:
            return json(LDR(Code.PAGE_NOT_FOUND))
        container_model = data.get(PageModel.container_model.name, dict())
        new_container_model = dict()
        if container_model is None:
            data.update({"container_model": new_container_model})
        else:
            for component_uuid, model_data in container_model.items():
                if isinstance(component_type, int):
                    this_component_type = model_data.get("component_type")
                    if this_component_type == component_type:
                        new_container_model.update({component_uuid: model_data})
                else:
                    new_container_model.update({component_uuid: model_data})
            data.update({"container_model": new_container_model})
        return json(LDR(data=data))

class GetPageFormModel(VCHTTPMethodView):

    class GetPageFormModelObj():
        app_uuid = doc.String("应用UUID")
        page_uuid = doc.List("页面UUID")
    
    @doc.consumes(GetPageFormModelObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面上仅有一个表单组件的表单模型UUID")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            page_uuid = str(request.json.get("page_uuid"))
        model = PageModel
        fields = [
            model.app_uuid, model.module_uuid, model.document_uuid, model.page_uuid, 
            model.page_name, model.form_count, model.container_model
        ]
        data = await engine.access.select_page_by_page_uuid(page_uuid, fields=fields)
        if not data:
            return json(LDR(Code.PAGE_NOT_FOUND))
        form_count = data.get(PageModel.form_count.name)
        if form_count != 1:
            return json(LDR(IDECode.PAGE_FORM_COUNT_NEED_ONE))
        container_model = data.get(PageModel.container_model.name, dict())
        if container_model is None:
            return json(LDR(IDECode.PAGE_FORM_MODEL_NOT_FOUND))
        else:
            model_uuid = ""
            for _, model_data in container_model.items():
                this_component_type = model_data.get("component_type")
                if this_component_type == ComponentType.FORM and model_data.get("is_outer_container"):
                    this_model_uuid = model_data.get("model")
                    if this_model_uuid:
                        model_uuid = this_model_uuid
                        break
            if model_uuid:
                del data[PageModel.container_model.name]
                data.update({"model_uuid": model_uuid})
                return json(LDR(data=data))
            else:
                return json(LDR(IDECode.PAGE_FORM_MODEL_NOT_FOUND))

class ListSMVariable(VCHTTPMethodView):

    class ListSMVariableObj():
        app_uuid = doc.String("应用UUID")
        sm_uuid = doc.String("状态机UUID")
    
    @doc.consumes(ListSMVariableObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出状态机变量")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            sm_uuid = str(request.json.get("sm_uuid"))
        data = await engine.access.list_sm_variable_by_sm_uuid(sm_uuid)
        return json(LDR(data=data))

class ListRCVariable(VCHTTPMethodView):

    class ListRCVariableObj():
        app_uuid = doc.String("应用UUID")
        rule_uuid = doc.String("规则引擎UUID")
    
    @doc.consumes(ListRCVariableObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出规则引擎变量")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            rule_uuid = str(request.json.get("rule_uuid"))
        data = await engine.access.list_rc_variable_by_rc_uuid(rule_uuid)
        return json(LDR(data=data))

class ListWFVariable(VCHTTPMethodView):

    class ListWFVariableObj():
        app_uuid = doc.String("应用UUID")
        wf_uuid = doc.String("工作流UUID")
        version_uuid = doc.String("工作流版本")
    
    @doc.consumes(ListWFVariableObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出工作流版本变量")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            wf_uuid = str(request.json.get("wf_uuid"))
            version_uuid = str(request.json.get("version_uuid"))
        data = await engine.access.list_wf_variable_by_wf_uuid(wf_uuid)
        if not data:
            return json(LDR(IDECode.WORKFLOW_NOT_EXISTS))
        children = data.get("children", dict())
        version_children = children.get(version_uuid, list())
        data.update({"children": version_children})
        return json(LDR(data=data))

class ListStateVariable(VCHTTPMethodView):

    class ListStateVariableObj():
        app_uuid = doc.String("应用UUID")
        state_uuid = doc.String("状态UUID")
    
    @doc.consumes(ListStateVariableObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出状态变量")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            state_uuid = str(request.json.get("state_uuid"))
        data = await engine.access.list_state_variable_by_state_uuid(state_uuid)
        return json(LDR(data=data))


class CreateEmptyPage(VCHTTPMethodView):

    class CreateEmptyPageObj():
        module_uuid = doc.String("应用UUID")
        p_page_uuid = doc.String("父页面UUID")
        open_type = doc.Integer("打开方式：0 弹窗；1 跳转；2 抽屉")
        page_title = doc.String("页面标题")

    @doc.consumes(CreateEmptyPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("自动创建空白页面")
    @Permission.policy(("module_uuid", Action.MODIFY),)
    async def post(self, request):
        component_type = ComponentType.PAGE
        create_service = engine.service.page_create.create_service(component_type)
        result = await create_service.create_document(request)
        self.add_git_action(request, partial(self._git_action, create_service.document.document_uuid))
        return result

    async def _git_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, document_uuid)


class CreateFormPage(VCHTTPMethodView):

    class CreateFormPageObj():
        module_uuid = doc.String("应用UUID")
        model_uuid = doc.String("模型UUID")
        p_page_uuid = doc.String("父页面UUID")
        open_type = doc.Integer("打开方式：0 弹窗；1 跳转；2 抽屉")
        page_title = doc.String("页面标题")
        editable = doc.Boolean("是否可编辑")

    @doc.consumes(CreateFormPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("自动创建表单页面")
    @Permission.policy(("module_uuid", Action.MODIFY),)
    async def post(self, request):
        component_type = ComponentType.FORM
        create_service = engine.service.page_create.create_service(component_type)
        create_service.find_name_field = True
        result = await create_service.create_document(request)
        self.add_git_action(request, partial(self._git_action, create_service.document.document_uuid))
        return result

    async def _git_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, document_uuid)

class CreateDatalistPage(VCHTTPMethodView):

    class CreateDatalistPageObj():
        module_uuid = doc.String("应用UUID")
        model_uuid = doc.String("模型UUID")
        p_page_uuid = doc.String("父页面UUID")
        open_type = doc.Integer("打开方式：0 弹窗；1 跳转；2 抽屉")
        page_title = doc.String("页面标题")

    @doc.consumes(CreateDatalistPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("自动创建数据列表页面")
    @Permission.policy(("module_uuid", Action.MODIFY),)
    async def post(self, request):
        component_type = request.parsed_json.get("request_component_type", None)
        create_service = engine.service.page_create.create_service(component_type)
        create_service.find_name_field = True
        result = await create_service.create_document(request)
        self.add_git_action(request, partial(self._git_action, create_service.document.document_uuid))
        return result

    async def _git_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, document_uuid)


class UpdateNavigation(VCHTTPMethodView):

    class UpdateNavigationObj():
        app_uuid = doc.String("应用UUID")
        ext_tenant = doc.String("租户uuid，可选，修改租户扩展时需加上")

    @doc.consumes(UpdateNavigationObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("更新导航数据")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = str(request.json.get("app_uuid", ""))
            navigations = request.json.get("navigations", [])
            ext_tenant = request.json.get("ext_tenant")

        func_list = []
        platform_set = set()
        
        for navigation in navigations:
            navigation_uuid = str(navigation.get("navigation_uuid", ""))
            navigation_style = int(navigation.get('navigation_style', 0))
            position = int(navigation.get("position", 0))
            platform = int(navigation.get("platform", 0))
            platform_set.add(platform)
            mainpage = str(navigation.get("mainpage", ""))
            userpage = navigation.get("userpage", [])
            items = navigation.get("attr", [])
            data = dict(
                    app_uuid=app_uuid,
                    navigation_uuid=navigation_uuid,
                    navigation_style=navigation_style,
                    position=position,
                    platform=platform,
                    mainpage=mainpage,
                    userpage=userpage
                )
            exist_navigation = await engine.access.get_navigation_by_platform(app_uuid, platform)
            if exist_navigation:
                model = Navigation
                query = model.update(**data).where(
                    model.app_uuid==app_uuid,
                    model.platform==platform
                )
                await engine.access.update_obj_by_query(model, query)
            else: 
                model = await engine.access.create_navigation(user_uuid, **data)
                navigation = model.to_dict()
            result = await engine.access.list_navigationitem(
                app_uuid, navigation["navigation_uuid"], ext_tenant=ext_tenant)
            
            navigationitem_uuid_list = [r["item_uuid"] for r in result]
            create_item_func = partial(
                self.create_item_data, app_uuid, navigation_uuid)
            insert_item_data = []
            update_item_data = []
            self.get_navigation_final_items(
                items, insert_item_data, update_item_data, exist_uuid=navigationitem_uuid_list, 
                create_item_func=create_item_func)
            final_exist_navigationitem_uuid = {item["item_uuid"] for item in update_item_data}
            to_delete_item_uuid = set(navigationitem_uuid_list) - final_exist_navigationitem_uuid
            delete_item_data = [
                {"is_delete": True, "item_uuid": uuid} for uuid in to_delete_item_uuid]
            func_list.extend(
                [engine.access.create_navigationitem(user_uuid, **d) for d in insert_item_data])
            func_list.extend(
                self.gen_update_navigationitem_func(app_uuid, update_item_data))
            func_list.extend(
                self.gen_update_navigationitem_func(app_uuid, delete_item_data))
            
        exist_navigations = await engine.access.list_navigation_by_app_uuid(app_uuid)
        exist_platform = set([nav["platform"] for nav in exist_navigations])
        to_del_platform = (exist_platform|platform_set) - platform_set
        for to_del in to_del_platform:
            model = Navigation
            data = dict(
                is_delete = True
            )
            query = model.update(**data).where(model.app_uuid==app_uuid, model.platform==to_del)
            func_list.append(engine.access.update_obj_by_query(model, query))
        async with engine.db.objs.atomic():
            for func in func_list:
                await func

        return json(LDR(Code.OK))
    
    def gen_update_navigationitem_func(self, app_uuid, update_data):
        model = NavigationItem
        funcs = []
        for data in update_data:
            query = model.update(**data).where(
                model.app_uuid==app_uuid, model.item_uuid==data["item_uuid"])
            funcs.append(engine.access.update_obj_by_query(model, query))
        return funcs
    
    @staticmethod
    def get_navigation_final_items(items, to_insert, to_update, exist_uuid, create_item_func):
        for item in items:
            item: dict
            data = create_item_func(item=item)
            if data["item_uuid"] in exist_uuid:
                to_update.append(data)
            else:
                to_insert.append(data)
            children = item.get("children", [])
            UpdateNavigation.get_navigation_final_items(
                children, to_insert, to_update, exist_uuid, create_item_func)
                

    def create_item_data(self, app_uuid, navigation_uuid, item):
        res = dict(
                app_uuid        = app_uuid,
                navigation_uuid = navigation_uuid,
                item_name       = item.get("item_name", ""),
                item_uuid       = item.get("item_uuid", ""),
                order           = item.get("order", 0),
                level           = item.get("level", 0),
                master          = item.get("master", ""),
                icon_type       = item.get("icon_type", 0),
                icon            = item.get("icon", ""),
                item_type       = item.get('item_type', 0),
                page_uuid       = item.get("page_uuid", ""),
                page_url        = item.get("page_url", ""),
                param           = item.get("param", {}),
                permission      = item.get("permission", []), 
                icon_color      = item.get("icon_color", "")
            )
        return res

class GetNavigation(VCHTTPMethodView):

    class GetNavigationObj():
        app_uuid = doc.String("应用UUID")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(GetNavigationObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取导航")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            ext_tenant = request.json.get("ext_tenant")

        def handle_modulerole(modulerole):
            r = {}
            
            # 数据库存的格式不适合从page_uuid查询到role, 需要额外的处理
            # TODO 需要从数据库查询出符合要求的格式
            for m in modulerole:
                pages = m.get(ModuleRole.pages.column_name, {})
                for _, d in pages.items():
                    page_uuid = d.get("uuid")
                    action = True if d.get("action") == "00010000" else False
                    role_name = m.get("role_name")
                    r.setdefault(page_uuid, [])
                    if action:
                        r[page_uuid].append(role_name)
            return r

        navigations = await engine.access.list_navigation_by_app_uuid(app_uuid)
        modulerole = await engine.access.list_module_role_by_app_uuid(app_uuid)
        document_obj = await engine.access.get_document(
            **{"app_uuid": app_uuid, "document_type": DocumentType.NAVIGATION})
        modulerole = handle_modulerole(modulerole=modulerole)
        if not navigations:
            return json(LDR(data={}))
        app_log.info(navigations)
        data = list(navigations)
        for navigation in data:
            result = await engine.access.list_navigationitem(
                app_uuid, navigation["navigation_uuid"], ext_tenant=ext_tenant) 
            attrs = await process_navigation_item(
                result, engine, need_permission_check=False, modulerole=modulerole)
            navigation["attr"] = attrs
        out = {"navigations": data, "document_version": document_obj.document_version}
        app_log.info(out)
        return json(LDR(data=out))

class PublishApp(VCHTTPMethodView):

    class PublishObj():
        app_uuid = doc.String("应用UUID")
        ext_tenant = doc.String("租户uuid，可选，发布租户拓展时需加上")
        publish_type = doc.String('发布类型，调试-1：只发布后端')
        branch_uuid = doc.String("branch uuid")
    
    @doc.consumes(PublishObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("发布应用")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = str(request.json.get("app_uuid", ""))
            ext_tenant = request.json.get("ext_tenant", "")
            build_type = int(request.json.get("build_type", 0))
            env = str(request.json.get("env", ""))
            sandbox_uuid = request.json.get("sandbox_uuid", "")
            sync_db = request.json.get("sync", False)
            version_number = str(request.json.get("version_number", ""))
            publish_app = request.json.get("publish_app", True)
            storage = request.json.get("storage", 1)
            publish_pc = request.json.get("publish_pc", True)
            token = str(request.token)
            sub_env = str(request.json.get("sub_env"))
            ignore_error = request.json.get("ignore_error", False)
            group_uuid = request.json.get("group_uuid", "")
            if not app_uuid:
                return json(LDR(Code.ARGS_ERROR))
        publish_for_local = True if storage == 2 else False
        if publish_for_local:
            app_info = await engine.access.select_app_revision(app_uuid, env)
            if list(app_info):
                app_info = list(app_info)[0]
            else:
                return json(LDR(Code.TENANT_USER_NOT_ACTIVED_OR_DELETE))
            app_revision_name = APP.app_revision_test.name if env else APP.app_revision.name
            app_revision = app_info.get(app_revision_name, 0)
            if app_revision < 1:
                return json(LDR(Code.LOCAL_DEPLOYMENT_NEED_CLOUD_PUBLISHING))
        need_check = (not ignore_error) and not (env == '' and sandbox_uuid == '')  # 正式环境不能忽略错误
        if need_check:
            check_message_list = await engine.access.list_check_message_by_app_uuid(
                app_uuid, any_problem=CheckMessage.error, 
                any_problem_message=CheckMessage.error_message, 
                model=CheckMessage)
            if check_message_list:
                return json(LDR(Code.PUBLISH_EXIST_ERROR_LIST))
        
        app_obj:APP = await engine.access.get_app(app_uuid=app_uuid)
        app_update_info = {
            APP.publish_app.name: publish_app, 
            APP.publish_pc.name: publish_pc
        }
        query = APP.update(**app_update_info).where(app_obj._pk_expr())
        await engine.access.update_obj_by_query(APP, query)
        if sandbox_uuid:
            sandbox_info = await engine.access.get_sandbox_by_sandbox_uuid(sandbox_uuid, app_uuid)
            sandbox_pk = sandbox_info.get_id()
        else:
            sandbox_pk = ""
            if getattr(request.ctx, "version_control", False):
                set_current_workspace(user_uuid="", branch_uuid="", app_uuid=app_uuid)
        group_uuid = group_uuid or app_obj.middle_user
        user_uuid = group_uuid + env
        tenant_uuid = ""
        if app_obj.middle_user != group_uuid:
            query = PublishGroup.select(PublishGroup.id).where(PublishGroup.group_uuid == group_uuid)
            group = await engine.access.select_obj_by_query(PublishGroup, query, as_dict=False)
            if not group:
                raise "group not found"
            group_id = group.id
            tenant = await engine.access.get_tenant_by_group(group_uuid)
            tenant_uuid = tenant.get("tenant_uuid")
        else:
            group_uuid = ""
            group_id = ""
        if publish_for_local:
            user_uuid += "local"
        if engine.config.K8S_SUPPORT:
            publish_service = PublishService(
                user_uuid, app_uuid, ext_tenant=ext_tenant, build_type=build_type,
                env=env, support_k8s=True, p_token=token, version_number=version_number,
                sandbox_pk=sandbox_pk, sandbox_uuid=sandbox_uuid, need_check=need_check,
                group_id=group_id, group_uuid=group_uuid)
            publish_key = publish_service.publish_key
            data = await engine.redis.redis.hgetall(publish_key)
            app_log.info(f"publish_data{data} {publish_key}")
            if not data:
                await publish_service.init_publish_status()
                app_info = await engine.access.get_app_by_app_uuid(app_uuid)
                user_uuid = app_info.middle_user + env
                if publish_for_local:
                    user_uuid += "local"
                app_user = app_info.user_uuid
                app_log.info(f"{user_uuid}")
                data = {"step": 0, "status": 1}
                current_workspace = get_current_workspace()
                if current_workspace:
                    workspace_info = {"app_uuid": current_workspace.app_uuid, "branch_uuid": current_workspace.branch_uuid, 
                                "user_uuid": current_workspace.user_uuid}
                else:
                    workspace_info = dict()
                publish_info = {"publish_app": publish_app, "publish_pc": publish_pc,
                                "sync_db": sync_db, "storage": storage,
                                "sandbox_uuid": sandbox_uuid, "sandbox_pk": sandbox_pk, 
                                "workspace_info": workspace_info, "app_user": app_user, "need_check": need_check,
                                "sub_env": sub_env, "group_id": group_id, "group_uuid": group_uuid,
                                "tenant_uuid": tenant_uuid}
                publish_info = ujson.dumps(publish_info)
                parms = {"user_uuid": user_uuid, "app_uuid": app_uuid, "ext_tenant": ext_tenant,
                         "build_type": str(build_type), "app_env": env, "p_token": token, 
                         "version_number": version_number, "publish_info": publish_info}
                if publish_app or publish_pc:
                    url = engine.config.PUBLISH_PC_URL + "?" + urllib.parse.urlencode(parms)
                else:
                    url = engine.config.PUBLISH_URL + "?" + urllib.parse.urlencode(parms)
                app_log.info(url)
                async with ClientSession(trust_env=True) as session:
                    result = await session.post(url)
                    app_log.info(result)
            else:
                data["step"] = int(data["step"])
                data["status"] = int(data["status"])
                wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
                appletid = wx_app.appid if wx_app else None
                url_info = publish_service.get_app_url(publish_service.app_env, appletid)
                data.update(url_info)
        else:
            publish_service = PublishService(
                user_uuid, app_uuid, ext_tenant=ext_tenant, build_type=build_type,
                env=env, p_token=token, version_number=version_number, sync_db=sync_db,
                sandbox_pk=sandbox_pk, need_check=need_check, sub_env=sub_env, sandbox_uuid=sandbox_uuid,
                tenant_uuid=tenant_uuid, group_id=group_id, group_uuid=group_uuid)
            app_log.info(publish_service)
            data = publish_service.publish()
        return json(LDR(data=data))
    
class RestartApp(HTTPMethodView):
    class RestartAppObj:
        app_uuid = doc.String("应用id")
        env = doc.String("应用环境")
               
    @doc.consumes(RestartAppObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("重启应用运行时")
    @Permission.policy(("app_uuid", Action.SELECT))
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            env = request.json.get("env")
        if env:
            deployment_name = app_uuid + env
        else:
            deployment_name = app_uuid
        parms = {"deployment_name": deployment_name, "app_uuid": app_uuid}
        restart_url = engine.config.JENKINS_URL + "/job/restart_app/buildWithParameters" + "?"
        url = restart_url + urllib.parse.urlencode(parms)
        app_log.info(url)
        async with ClientSession(trust_env=True) as session:
                result = await session.post(url)
                app_log.info(result)
        return json(LDR(Code.OK))
    
    
class CheckAppAlive(HTTPMethodView):
    class CheckAppAliveObj:
        app_uuid = doc.String("应用id")
        env = doc.String("应用环境")
               
    @doc.consumes(CheckAppAliveObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("检查应用运行时是否启动成功")
    @Permission.policy(("app_uuid", Action.SELECT))
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            env = request.json.get("env", "")
            if not app_uuid:
                return json(LDR(Code.ARGS_ERROR))
        # 当不是k8s发布方式时，相同app_uuid publish_service返回的是同一个对象，env会不生效
        publish_service = PublishService("", app_uuid, ext_tenant="", build_type="", 
                                            env=env, p_token="", support_k8s=True)
        resut = await publish_service.check_app_alive()
        return json(LDR(data=resut))
    

class GetPublishStatus(VCHTTPMethodView):
    
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = str(request.json.get("app_uuid", ""))
            ext_tenant = request.json.get("ext_tenant", "")
            sandbox_uuid = request.json.get("sandbox_uuid", "")
            build_type = int(request.json.get("build_type", 0))
            env = str(request.json.get("env", ""))
            version_number = str(request.json.get("version_number", ""))
            token = str(request.token)
            group_uuid = str(request.json.get("group_uuid", ""))
            if not app_uuid:
                return json(LDR(Code.ARGS_ERROR))
        # TODO 粗糙版
        # step: 0-5s
        # status: 0未开始 1发布中 2发布成功 3发布报错
        user_uuid = user_uuid + env
        support_k8s = engine.config.K8S_SUPPORT
        if sandbox_uuid:
            sandbox_info = await engine.access.get_sandbox_by_sandbox_uuid(sandbox_uuid, app_uuid)
            sandbox_pk = sandbox_info.get_id()
        else:
            sandbox_pk = ""
        app = await engine.access.get_app_by_app_uuid(app_uuid)
        group_uuid = group_uuid or app.middle_user
        user_uuid = group_uuid + env
        if app.middle_user != group_uuid:
            query = PublishGroup.select(PublishGroup.id).where(PublishGroup.group_uuid == group_uuid)
            group = await engine.access.select_obj_by_query(PublishGroup, query, as_dict=False)
            if not group:
                raise "group not found"
            group_id = group.id
        else:
            group_uuid = ""
            group_id = ""
        publish_service = PublishService(
            user_uuid, app_uuid, ext_tenant=ext_tenant, build_type=build_type, 
            env=env, support_k8s=support_k8s, p_token=token, version_number=version_number, 
            sandbox_uuid=sandbox_uuid, sandbox_pk=sandbox_pk, group_uuid=group_uuid, group_id=group_id)
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None
        publish_service.app_name = app.app_name.lower()
        url_suffix = ""
        if group_uuid:
            tenant = await engine.access.get_tenant_by_group(group_uuid)
            url_suffix = tenant.get("tenant_uuid") + "/welcome"
        if support_k8s:
            publish_key = publish_service.publish_key
            data = await engine.redis.redis.hgetall(publish_key)
            app_log.info(f"app_publish_data={data} {publish_key}")
            if not data:
                # 认为是发布成功,但成功的消息已过期,额外构造成功
                # app_name = publish_service.get_app_name()
                url_info = publish_service.get_app_url(publish_service.app_env, appletid, url_suffix=url_suffix)
                data = {
                    "step": 5, 
                    "status": 2
                }
                data.update(url_info)
                app_log.info(["发布超时", data])
                    
            else:
                data["step"] = int(data["step"])
                data["status"] = int(data["status"])
                url_info = publish_service.get_app_url(publish_service.app_env, appletid, url_suffix=url_suffix)
                data.update(url_info)
        else:
            publish_service = PublishService(
                user_uuid, app_uuid, ext_tenant=ext_tenant, build_type=build_type, 
                env=env, p_token=token, version_number=version_number)
            app_log.info(publish_service)
            data = publish_service.publish_state
        return json(LDR(data=data))
    

class GetRuntimeUrl(HTTPMethodView):

    class PublishObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(PublishObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("发布应用")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            show_sandbox = request.json.get("show_sandbox", False)
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            if not app_uuid:
                return json(LDR(Code.ARGS_ERROR))
        # 当不是k8s发布方式时，相同app_uuid publish_service返回的是同一个对象，env会不生效
        publish_service = PublishService("", app_uuid, ext_tenant="", build_type="",
                                         env="", p_token="")
        await publish_service.get_app_version()
        if show_sandbox:
            url_info = await publish_service.get_runtime_url(real_user_uuid)
        else:
            url_info = await publish_service.get_runtime_url()

        return json(LDR(data=url_info))


class GetPackageInfo(HTTPMethodView):

    class PackageInfoObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(PackageInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用扩展包信息")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
        # Get installed packages from pkg_resources
        installed_packages_list = [
            {"name": i.key, "version": i.version} for i in pkg_resources.working_set]
        
        # Get system packages from sys.modules
        import sys
        system_packages = []
        for module_name, module in sys.modules.items():
            # Skip submodules (those with dots in the name)
            if '.' not in module_name:
                try:
                    # Try to get version if available
                    version = getattr(module, '__version__', sys.version) if module else sys.version
                    system_packages.append({"name": module_name, "version": version})
                except Exception:
                    # Skip modules that can't be accessed
                    pass
        
        # Combine both lists, avoiding duplicates
        installed_names = {pkg["name"] for pkg in installed_packages_list}
        combined_packages = installed_packages_list + [pkg for pkg in system_packages if pkg["name"] not in installed_names]
        
        pip_version = pkg_resources.get_distribution("pip").version
        return json(LDR(data={"packages": combined_packages, "pip": pip_version}))

class GetExtensionPackage(HTTPMethodView):

    class ExtensionPackageObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ExtensionPackageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取自定义扩展包信息")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            # document_uuid = str(request.json.get("document_uuid", ""))
            # module_uuid = str(request.json.get("module_uuid", ""))
        module_ext_pack = await engine.access.list_document_content_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=DocumentType.EXPANSION_PACK)
        app_ext_pack = await engine.access.get_app_deploy_config(app_uuid)
        list_module = await engine.access.list_module_by_app_uuid(app_uuid)

        result = await self.get_module_packages_list(module_ext_pack, app_ext_pack, list_module)
        return json(LDR(data=result))

    async def get_module_packages_list(self, module_ext_pack, app_ext_pack, list_module):

        result = []
        module_dict = {}
        module_ext_pack_list = list(module_ext_pack)
        list_module = list(list_module)

        def get_package_by_document(module_ext_pack):
            deploy_config = module_ext_pack.get("document_content", dict())
            extension_pack = deploy_config.get("extension_pack", dict())
            packages = extension_pack.get("packages", [])
            sys_Packages = extension_pack.get("sys_Packages", dict())
            return packages, sys_Packages

        app_packages, _ = get_package_by_document(app_ext_pack)
        # packages_list = [package.get("name", "") for package in app_packages]

        for module in list_module:
            module_uuid = module.get("module_uuid", "")
            module_name = module.get("module_name", "")
            module_dict.update({module_uuid: module_name})

        for module_ext_pack in module_ext_pack_list:
            module_packages, sys_Packages = get_package_by_document(module_ext_pack)
            # packages_list += [module_package.get("name", "") for module_package in module_packages]
            module_uuid = module_ext_pack.get("module_uuid", "")
            document_uuid = module_ext_pack.get("document_uuid", "")
            document_version = module_ext_pack.get("document_version", "")
            # update_timestamp = module_ext_pack.get("update_timestamp", "")
            module_name = module_dict.get(module_uuid)
            result.append({
                "packages": module_packages, "module_uuid": module_uuid, "module_name": module_name,
                "document_uuid": document_uuid, "timestamp": int(time.time()), "document_version": document_version,
                "module_uuid": module_uuid
                })
            # , "sys_Packages": sys_Packages

        packages_dict = {}
        packages_set = {}
        for package in app_packages:
            _name = package.get("name", "")
            _version = package.get("version", "")
            packages_set[_name] = set(_version)
            # package_dict.update({package.get("name", ""): package.get("version", "")}) 
        # packages_dict.update({lemon_uuid(): package_dict})

        for module_info in result:
            module_uuid = module_info.get("module_uuid")
            package_ = module_info.get("packages")
            package_dict = {}
            for package in package_:
                package_dict.update({package.get("name", ""): package.get("version", "")})
            packages_dict.update({module_uuid: package_dict})

        for module_info in result:
            packages = module_info.get("packages")
            module_name = module_info.get("module_name")
            module_uuid = module_info.get("module_uuid")
            # packages_set = {}
            for _uuid, _dict in packages_dict.items():
                if _uuid != module_uuid:
                    for name, _version in _dict.items():
                        if packages_set.get(name):
                            packages_set[name].add(_version)
                        else:
                            packages_set.update({name: set(_version)})
            for package in packages:
                package["module_name"] = module_name
                package["module_uuid"] = module_uuid
                package_name = package.get("name")
                if len(packages_set.get(package_name, "")) > 1:
                    package["is_repeat"] = 1
                else:
                    package["is_repeat"] = 0

        return result



class SaveModulePackageInApp(HTTPMethodView):

    class SaveModulePackageInAppObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(SaveModulePackageInAppObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("应用下修改模块扩展包信息")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            document_uuid = str(request.json.get("document_uuid", ""))
            timestamp = request.json.get("timestamp")
            packages = request.json.get("packages")
            document_version = request.json.get("document_version")
            # sys_Packages = request.json.get("sys_Packages", {})
            # module_uuid = request.json.get("module_uuid")

        res = None
        if packages is None or not document_uuid or not app_uuid:
            res = {"message": "check args error"}

        draft = await engine.access.get_obj(DocumentDraft, document_uuid=document_uuid)
        draft_timestamp = int(draft.timestamp.timestamp())

        # 当前仅接口会带timestamp，其他地方不会带
        if draft and timestamp and draft_timestamp > timestamp:
            return json(LDR(IDECode.DOCUMENT_VERSION_LOWER))
        for package in packages:
            package.pop("is_repeat")
            package.pop("module_uuid")
            package.pop("module_name")
            # package.pop("sys_Packages")
        document_content = draft.document_content
        document_content.get("extension_pack", dict()).update({"packages": packages})
        # await engine.access.update_or_create_document_draft_by_document_uuid(document_uuid, **{
        #     DocumentDraft.document_content.name: document_content,
        # })
        await self.update_document_and_content(document_uuid, document_content, document_version)
        return json(LDR(data=res))

    async def update_document_and_content(self, document_uuid, content, document_version):

        update_timestamp = int(time.time())

        async with engine.db.objs.atomic():
            obj = await engine.access.get_obj(DocumentContent, document_uuid=document_uuid)
            if not obj:
                await engine.access.create_document_content(**{
                    DocumentContent.document_uuid.name: document_uuid,
                    DocumentContent.document_content.name: content
                })
            await engine.access.update_or_create_document_draft_by_document_uuid(document_uuid, **{
                DocumentDraft.document_content.name: content,
            })

        async with engine.db.objs.atomic():
            next_document_version = document_version + 1
            document_update_data = {
                DocumentModel.update_timestamp.name: update_timestamp,
                DocumentModel.document_version.name: next_document_version
            }
            await engine.access.update_document_by_document_uuid(
                document_uuid, **document_update_data)
            document_content_update_data = {
                DocumentContent.document_content.name: content
            }

            await engine.access.update_document_content_by_document_uuid(
                document_uuid, **document_content_update_data)


class GetModuleByPackage(HTTPMethodView):

    class ModuleByPackageObj():
        # app_uuid = doc.String("应用UUID")
        package_name = doc.String("扩展包名")

    @doc.consumes(ModuleByPackageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取自定义扩展包信息")
    @Permission.policy(("app_uuid", Action.SELECT), auto_transaction=False,)
    async def post(self, request):
        with process_args():
            package_name = request.json.get("package_name", "")
            # module_name = request.json.get("module_name", "")
            package_version = request.json.get("package_version", "")
            app_uuid = request.json.get("app_uuid", "")

        package_name = str(package_name).lower()
        package_name, env_package_version = self.get_import_name(package_name)
        lemon_env = engine.app.config.ENV or ""

        if lemon_env == "Development":
            # check_dict = self.check_and_install_package(package_name, package_version, env=lemon_env)
            # is_legal_fage = check_dict.get("flag", False)
            result = self.get_submodules(package_name)
        else:
            # 查数据库
            _packages_info = await engine.access.get_packages_info_by_name_version(package_name, package_version)
            if not _packages_info:
                # 写入数据库
                publish_service = PublishService(
                    "", app_uuid, ext_tenant="", build_type="", env=lemon_env, p_token="")
                message = await publish_service.pip_package_in_docker({
                    "name": package_name, "version": package_version})
                # 查数据库
                app_log.info("package_select_start")
                _packages_info = await engine.access.get_packages_info_by_name_version(package_name, package_version)

            if list(_packages_info):
                packages_info = list(_packages_info)[0]
            else:
                packages_info = {}
                package_info = {
                    Packages.package_name.name: package_name,
                    Packages.package_version.name: package_version,
                    Packages.subpackage_info.name: []
                }
                await engine.access.update_or_create_packages(**package_info)
            app_log.info("package_select_end")
            result = packages_info.get("subpackage_info", []) or []
        sub_info = [{"name": package_name, "children": result}]
        if result:
            if isinstance(result[0], dict):
                if result[0].get("name") == IDECode.EXTENSION_PACKAGE_PIP_ERROR.message_init:
                    result = sub_info[0].get("children")

        return json(LDR(data={"name": package_name, "children": sub_info}))

    def get_submodules(self, package_name):
        try:
            # app_log.info(f"get_submodules package_name = {package_name}")
            package = importlib.import_module(package_name)
        except:
            package = None
        results = []
        if package and getattr(package, "__path__", None) and package_name not in ["kombu.transport.sqlalchemy"]:
            for loader, name, is_pkg in pkgutil.walk_packages(package.__path__):
                if not all(ch not in name for ch in [".", "__pip-runner__", "__main__", "test"]) or "test" in package_name:
                    break
                # app_log.info(f"name ={name}")
                full_name = package_name + '.' + name
                # if is_pkg: '.', '_', '-',
                if all(ch not in name for ch in [".", "__pip-runner__", "__main__", "test"]) and "test" not in package_name:
                    children = self.get_submodules(full_name)
                    # import_name = self.get_import_name(name)
                    results.append({"name": name, "children": children})
        return results

    def check_and_install_package(self, package_name, package_version="", env=""):

        result = {}
        try:
            # TODO版本号自定义
            package = importlib.import_module(package_name)
        except ImportError as e:
            package = None
        if package:
            try:
                is_package = getattr(package, '__path__', False)
            except ImportError as e:
                # 不是个包
                is_package = False
            if is_package:
                result = {"msg": "is a package", "flag": True}
            else:
                result = {"msg": "not a package", "flag": False}
        elif not package and env == "Development":
            try:
                install_package(package_name, package_version)
            except:
                # 安装失败 包名错误
                result = {"error": "package name error", "flag": False}
        return result

    def get_import_name(self, package_name):
        try:
            dist = pkg_resources.get_distribution(package_name)
            if dist.has_metadata('top_level.txt'):
                package_name = dist.get_metadata('top_level.txt').strip()
            _version = dist.version
        except:
            _version = ""
        package_name = package_name.split("\n")[0]
        return package_name, _version


class CheckExtensionPackage(HTTPMethodView):

    class PublishObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(PublishObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("检测拓展包兼容性")
    @doc.tag(url_prefix)
    @doc.summary("")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
        publish_service = PublishService("", app_uuid, ext_tenant="", build_type="",
                                         env="", p_token="")
        message = await publish_service.check_extension_requirement()

        return json(LDR(data={"message": message}))


class AddRuntimeAccount(HTTPMethodView):

    class AddRuntimeAccountObj():
        app_uuid = doc.String("应用UUID")
        role_uuid = doc.String("用户角色UUID")
        accounts = doc.String("账号信息, 换行符分割")

    @doc.consumes(AddRuntimeAccountObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("按角色添加用户, 账号仅用作测试")
    @doc.tag(url_prefix)
    @doc.summary("按角色添加用户")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            role_uuid = str(request.json.get("role_uuid", ""))
            account_list_str = str(request.json.get("accounts", ""))
            account_list = account_list_str.splitlines()
            if not account_list or not role_uuid:
                return json(LDR(Code.ARGS_ERROR))
        raise  # 2334 取消测试账号的概念
        data = dict(
            app_uuid=app_uuid,
            role_uuid=role_uuid
        )
        role_uuid_exist = await engine.access.get_user_role(**data)
        if not role_uuid_exist:
            return json(LDR(IDECode.USER_ROLE_NOT_EXISTS))
        if role_uuid_exist.is_admin:
            return json(LDR(IDECode.PERMISSION_POLICY))
        model = User
        query = model.select()
        for account in account_list:
            query = query.orwhere((model.user_name == account) | (model.mobile_phone == account))
        account_registed = await engine.access.list_obj(model, query)
        account_registed = [account["user_uuid"] for account in account_registed]
        runtime_account_list = await engine.access.list_runtime_account_by_app_uuid_user_role(app_uuid, role_uuid)
        runtime_account_list = [account["user_uuid"] for account in runtime_account_list]
        difference = list(set(account_registed).difference(runtime_account_list))
        for user_uuid in difference:
            await engine.access.add_runtime_account(**dict(app_uuid=app_uuid, user_uuid=user_uuid, user_role=role_uuid))
        return json(LDR(Code.OK))

class ListRuntimeAccount(HTTPMethodView):
    class ListRuntimeAccountObj:
        app_uuid = doc.String("应用UUID")
        role_uuid = doc.String("用户角色UUID")

    @doc.consumes(ListRuntimeAccountObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按角色获取用户列表")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            role_uuid = str(request.json.get("role_uuid", ""))
            if not role_uuid:
                return json(LDR(Code.ARGS_ERROR))
        account_list = await engine.access.list_runtime_account_by_app_uuid_user_role(app_uuid, role_uuid)
        if not account_list:
            return json(LDR(data=[]))
        model = User
        query = model.select(model.user_uuid, model.user_name, model.mobile_phone)
        for account_info in account_list:
            user_uuid = account_info["user_uuid"]
            query = query.orwhere(model.user_uuid==user_uuid)
        user_list = await engine.access.list_obj(model, query)
        return json(LDR(data=list(user_list)))
    
class DeleteRuntimeAccount(HTTPMethodView):
    class DeleteRuntimeAccountObj:
        app_uuid = doc.String("应用UUID")
        role_uuid = doc.String("用户角色uuid")
        account = doc.String("账号信息")

    @doc.consumes(DeleteRuntimeAccountObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("从用户角色中移除改账号")
    @doc.tag(url_prefix)
    @doc.summary("删除账号")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            role_uuid = str(request.json.get("role_uuid", ""))
            account_info = request.json.get("account", {})
            if not account_info or not role_uuid:
                return json(LDR(Code.ARGS_ERROR))
        account = account_info["user_name"] or account_info["mobile_phone"]
        user_uuid = account_info["user_uuid"]
        await engine.access.remove_runtime_account(app_uuid, user_uuid, role_uuid)
        return json(LDR(Code.OK))

class ListUserRole(VCHTTPMethodView):
    class ListUserRoleObj:
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ListUserRoleObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取用户角色")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
        user_role_list = await engine.access.list_user_role_by_app_uuid(app_uuid=app_uuid)
        user_role_list = list(user_role_list)
        app_module_list = await engine.access.list_module_by_app_uuid(app_uuid=app_uuid)
        module_role_uuid2name = dict()
        app_module_roles = await engine.access.list_document_content_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=DocumentType.SECURITY)
        for module_role in app_module_roles:
            content = module_role.get("document_content", {})
            roles = content.get("module_role_list", [])
            for r in roles:
                module_role_uuid2name.update({r["uuid"]: r["name"]})
        app_module_map = {module["module_uuid"]: module["module_name"] for module in app_module_list}
        module_sort_map = {module["module_uuid"]: module["id"] for module in app_module_list}
        def sort_module(d):
            module_uuid = d.get("module_uuid")
            return module_sort_map.get(module_uuid, 199)
        
        for user_role in user_role_list:
            app_module_map_copy = copy.deepcopy(app_module_map)
            permission_list = list()
            role_content_list = await engine.access.list_user_role_content_by_user_role(user_role["role_uuid"])
            for content in role_content_list:
                sys_module_content = []
                module_uuid = content["module_uuid"]
                if module_uuid in app_module_map_copy:   #如果不在，说明已经模块已经删除，是否需要更新is_delete?
                    module_name = app_module_map_copy.pop(module_uuid)
                    for mr in content["module_role"]:
                        module_role_name = module_role_uuid2name.get(mr["uuid"])
                        if not module_role_name:
                            continue
                        mr.update({"name": module_role_uuid2name[mr["uuid"]]})
                        sys_module_content.append(mr)
                    permission_list.append({"name": module_name, "content": sys_module_content, 
                                            "module_uuid": module_uuid})
            # TODO 这里是为了兼容老数据,模块没有添加到userrolecontent中, 
            # 等前端更新最新的数据, 这里就可以不保留了
            for module_uuid, module_name in app_module_map_copy.items():
                permission_list.append({"name": module_name, "module_uuid": module_uuid, "content": []})
            permission_list.sort(key=sort_module)

            user_role["permission"] = permission_list
            user_role["name"] = user_role.pop("role_name")
            user_role["uuid"] = user_role.pop("role_uuid")
        
        data = dict(
            user_role_list = user_role_list
        )
        app_log.debug(data)
        return json(LDR(data=data))
    
class UpdateUserRole(VCHTTPMethodView):
    class UpdateUserRoleObj:
        app_uuid = doc.String("应用UUID")
        user_role_list = doc.List("用户角色列表")

    @doc.consumes(UpdateUserRoleObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("更新用户角色")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            user_role_list = request.json.get("user_role_list", [])
        app_log.debug(user_role_list)
        app_module_list = await engine.access.list_module_by_app_uuid(app_uuid=app_uuid)
        app_module_map = {module["module_name"]: module["module_uuid"] for module in app_module_list}
        user_role_list_exist = await engine.access.list_user_role_by_app_uuid(app_uuid=app_uuid, need_delete=True)
        user_role_list_exist = list(user_role_list_exist)
        admin_user_role_uuid = [user_role["role_uuid"] for user_role in user_role_list_exist if user_role["is_admin"]]
        user_role_uuid_list_exist = [user_role["role_uuid"] for user_role in user_role_list_exist]
        app_log.debug(user_role_uuid_list_exist)
        model_r = UserRole
        model_c = UserRoleContent
        task_list = []

        for idx, user_role in enumerate(user_role_list):
            user_role_uuid = user_role["uuid"]
            data = {
                    model_r.app_uuid.name: app_uuid,
                    model_r.role_name.name: user_role["name"],
                    model_r.order.name: idx,
                    model_r.is_delete.name: False
                }
            if user_role_uuid in user_role_uuid_list_exist:
                query = model_r.update(**data).where(model_r.role_uuid==user_role_uuid)
                task_list.append(engine.access.update_obj_by_query(model_r, query, need_delete=True))
                user_role_uuid_list_exist.remove(user_role_uuid)
            else:
                data.update({model_r.role_uuid.name: user_role_uuid})
                task_list.append(engine.access.create_obj(model_r, **data))
            role_content_list_exist = await engine.access.list_user_role_content_by_user_role(user_role_uuid, need_delete=True)
            role_content_module_uuid_list_exist = [content["module_uuid"] for content in role_content_list_exist]
            
            for module_permission in user_role.get("permission", []):
                module_name = module_permission["name"]
                permission = module_permission["content"]
                if module_name not in app_module_map:   # 用户传了错误的模块名，或模块已删除
                    # 添加报错信息
                    continue
                module_uuid = app_module_map[module_name]
                module_role_list = await engine.access.list_module_role_by_module_uuid(module_uuid=module_uuid)
                module_role_uuid_map = {module_role["role_uuid"]: module_role["role_name"] for module_role in module_role_list}
                permission = [{"uuid": role["uuid"]} for role in permission if role["uuid"] in module_role_uuid_map]     #去掉不属于该模块的模块角色

                data = {
                    model_c.module_role.name: permission,
                    model_c.is_delete.name: False
                }
                
                if module_uuid in role_content_module_uuid_list_exist:
                    query = model_c.update(**data).where(model_c.user_role==user_role_uuid, model_c.module_uuid==module_uuid)
                    task_list.append(engine.access.update_obj_by_query(model_c, query, need_delete=True))
                    role_content_module_uuid_list_exist.remove(module_uuid)
                else:
                    data.update(
                        {model_c.user_role.name: user_role_uuid,
                        model_c.module_uuid.name: module_uuid}
                    )
                    task_list.append(engine.access.create_obj(model_c, **data))
            for module_uuid in role_content_module_uuid_list_exist:
                data = dict(
                    is_delete = True
                )
                query = model_c.update(**data).where(model_c.user_role==user_role_uuid, model_c.module_uuid==module_uuid)
                task_list.append(engine.access.update_obj_by_query(model_c, query))
        for user_role_uuid in user_role_uuid_list_exist:
            if user_role_uuid in admin_user_role_uuid:
                #系统管理员不可删除
                continue
            data = dict(
                is_delete = True
            )
            query = model_r.update(**data).where(model_r.role_uuid==user_role_uuid)
            task_list.append(engine.access.update_obj_by_query(model_r, query))
        async with engine.db.objs.atomic():
            for task in task_list:
                await task
        return json(LDR(Code.OK))

class ListModuleRole(VCHTTPMethodView):
    class ListModuleRoleObj:
        app_uuid = doc.String("应用UUID")
        module_name = doc.String("模块名")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(ListModuleRoleObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用模块角色")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            module_name = str(request.json.get("module_name", None))
            ext_tenant = request.json.get("ext_tenant")
            if module_name is None:
                return json(LDR(Code.ARGS_ERROR))
        module = await engine.access.get_module_by_app_uuid_module_name(app_uuid=app_uuid, module_name=module_name)
        data = await engine.access.list_module_role_by_module_uuid(module_uuid=module.module_uuid, ext_tenant=ext_tenant)
        data = list(data)
        return json(LDR(data=data))
    
class ListAPPModuleRole(VCHTTPMethodView):
    class ListAPPModuleRoleObj:
        app_uuid = doc.String("应用UUID")
        module_name = doc.String("模块名")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")

    @doc.consumes(ListAPPModuleRoleObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用模块角色")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
        
        fields = (
            ModuleRole.module_uuid, 
            ModuleRole.role_uuid, 
            ModuleRole.role_name, 
        )
        app_module_roles = await engine.access.list_module_role_by_app_uuid(
            app_uuid, fields=fields)
        app_log.info(list(app_module_roles))
        roles = dict()
        for role in app_module_roles:
            module_uuid = role["module_uuid"]
            module_role = roles.setdefault(module_uuid, list())
            module_role.append(role)
        app_log.info(roles)
        return json(LDR(data=roles))
            

class GetAPPSetting(VCHTTPMethodView):
    class GetAPPSettingObj:
        app_uuid = doc.String("应用UUID")
    
    class GetAPPSettingProduceObj:
        app_uuid = doc.String("应用UUID")
        permission_check = doc.Boolean("安全检查")
      
    @doc.consumes(GetAPPSettingObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用设置")
    @doc.produces(GetAPPSettingProduceObj)
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
        data = await engine.access.get_app_setting(app_uuid)
        data = data.to_dict()
        return json(LDR(data=data))

class UpdateAPPSetting(VCHTTPMethodView):
    class UpdataAppSettingObj:
        app_uuid = doc.String("应用UUID")
        permission_check = doc.Boolean("安全检查")
        
    @doc.consumes(UpdataAppSettingObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("更新应用设置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            permission_check = request.json.get("permission_check", True)
            anonymous = request.json.get("anonymous", False)
            anonymous_role = request.json.get("anonymous_role", "")

        data = dict(
            permission_check = permission_check,
            anonymous = anonymous,
            anonymous_role = anonymous_role
        )
        await engine.access.update_app_setting(app_uuid, **data)
        return json(LDR(Code.OK))

    
class CheckAllDocument(VCHTTPMethodView):

    class CheckAllDocumentObj():
        app_uuid = doc.String("应用UUID")
        module_uuid = doc.String("模块UUID（可选，为空则检查应用所有文档）")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
        
    @doc.consumes(CheckAllDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("检查应用或模块的所有文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            module_uuid = request.json.get("module_uuid", None)
            ignore_version = request.json.get("ignore_version", True)
            ext_tenant = request.json.get("ext_tenant")
        # return json(LDR())
        t1 = time.time()
        # module uuid 为None才检查所有文档，为""检查导航等文档
        all_document = await engine.access.list_document_by_module_uuid_join_check_message(
            app_uuid, module_uuid, ignore_version=ignore_version, ext_tenant=ext_tenant)
        app_log.info("search document-content-module cost: {}".format(time.time() - t1))
        if all_document:
            check_key = app_config.CHECK_ALL_PREFIX + app_uuid
            if await engine.redis.ttl(check_key) > 0:
                app_log.info(f"{app_uuid} is checking all documents")
                await asyncio.sleep(3)
            else:
                await engine.redis.setex(check_key, app_config.CHECK_ALL_EXPIRY, 1)
                try:
                    modifieds = await engine.service.document_check.check_all_document2(app_uuid, all_document, check_commit=True)
                    self.add_git_action(request, partial(self.sync_docs, [modified.get("document_uuid") for modified in modifieds]))
                finally:
                    await engine.redis.delete(check_key)
        app_log.info("whole cost: {}".format(time.time() - t1))
        return json(LDR())

    async def sync_docs(self, documents):
        return await asyncio.gather(engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, doc) for doc in documents)

    async def sync_app(self, app_uuid):
        # todo: sync modified doc,avoid add merge conflict file
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_APP, app_uuid)


class ListCheckMessage(VCHTTPMethodView):

    class ListCheckMessageObj():
        app_uuid = doc.String("应用UUID")
        document_uuid = doc.String("文档UUID（可选，为空则列出应用所有错误信息）")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
        
    @doc.consumes(ListCheckMessageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用或模块文档的错误信息")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            document_uuid = str(request.json.get("document_uuid", ""))
            ext_tenant = request.json.get("ext_tenant")
        model = CheckMessage
        error_message_list = await engine.access.list_check_message_by_app_uuid(
            app_uuid, document_uuid, ext_tenant=ext_tenant, any_problem=model.error, any_problem_message=model.error_message, model=model)
        warning_message_list = await engine.access.list_check_message_by_app_uuid(
            app_uuid, document_uuid, ext_tenant=ext_tenant, any_problem=model.warning, any_problem_message=model.warning_message, model=model)
        info_message_list = await engine.access.list_check_message_by_app_uuid(
            app_uuid, document_uuid, ext_tenant=ext_tenant, any_problem=model.info, any_problem_message=model.info_message, model=model)
        error_message_list = list(error_message_list)
        warning_message_list = list(warning_message_list)
        info_message_list = list(info_message_list)
        app_messages = await engine.access.list_app_publish_message(app_uuid=app_uuid, message_level=2, ext_tenant=ext_tenant, as_dict=False)
        publish_error_list = []
        for app_message in app_messages:
            messages = app_message.message
            if not messages:
                continue
            messages = [messages] if isinstance(messages, dict) else messages
            for message in messages:
                m = {
                    "app_uuid": app_uuid, 
                    "document_type": message.get("document_type"), 
                    "document_uuid": message.get("document_uuid"), 
                    "module_uuid": message.get("module_uuid"), 
                    "error_message": [message]
                }
                publish_error_list.append(m)
        data = {
            "publish_error": {"data": publish_error_list},
            "error": {"data": error_message_list},
            "warning": {"data": []},
            "info": {"data": []}
        }
        return json(LDR(data=data))

class GetAPPTheme(VCHTTPMethodView):
    class GetAPPThemeObj:
        app_uuid = doc.String("应用UUID")

    @doc.consumes(GetAPPThemeObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用主题配置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
        theme = await engine.access.list_document_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=DocumentType.THEME, as_dict=False)
        if not theme:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))

        theme = list(theme)[0]
        content = await engine.access.get_document_content_by_document_uuid(theme.document_uuid)
        if content is None:
            data = dict()
        else:
            document_content = content.document_content
            if document_content.get("theme_uuid"):
                content = await engine.access.get_document_content_by_document_uuid(
                    document_content.get("theme_uuid"))
                data = content.document_content.get("theme_variables")
            else:
                data = document_content
        return json(LDR(data=data))

class UpdateAPPTheme(VCHTTPMethodView):
    class UpdateAPPThemeObj:
        app_uuid = doc.String("应用UUID")
        content = doc.String("主题内容")

    @doc.consumes(UpdateAPPThemeObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用主题配置(已弃用)")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            content = request.json.get("content", {})
        theme = await engine.access.list_document_by_app_uuid_document_type(app_uuid=app_uuid, document_type=DocumentType.THEME, as_dict=False)
        if not theme:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        theme = list(theme)[0]
        data = {DocumentContent.document_content.name: content}
        async with engine.db.objs.atomic():
            await engine.db.objs.execute(theme.update(document_version=theme.document_version+1, update_timestamp=time.time()).where(theme._pk_expr()))
            await engine.access.update_document_content_by_document_uuid(theme.document_uuid, **data)
            self.add_git_action(request, partial(self._git_action, theme.document_uuid))
        return json(LDR(Code.OK))
    
    async def _git_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, document_uuid)
    
class GetAPPDeploy(HTTPMethodView):
    class GetAPPDeployObj:
        app_uuid = doc.String("应用UUID")

    @doc.consumes(GetAPPDeployObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用配置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
        theme = await engine.access.list_document_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=DocumentType.APP_DEPLOY, as_dict=False)
        if not theme:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))

        theme = list(theme)[0]
        content = await engine.access.get_document_content_by_document_uuid(theme.document_uuid)
        # TODO 解决方案包含模块 应该实时计算
        if content is None:
            data = dict()
        else:
            data = content.document_content
        return json(LDR(data=data))
    
    
class UpdateAPPDeploy(VCHTTPMethodView):
    class UpdateAPPDeployObj:
        app_uuid = doc.String("应用UUID")
        content = doc.String("主题内容")

    @doc.consumes(UpdateAPPDeployObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("更新应用配置信息")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid", ""))
            content = request.json.get("content", {})
        deploy = await engine.access.list_document_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=DocumentType.APP_DEPLOY, as_dict=False)
        if not deploy:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        deploy = list(deploy)[0]
        data = {DocumentContent.document_content.name: content}
        async with engine.db.objs.atomic():
            await engine.db.objs.execute(
                deploy.update(
                    document_version=deploy.document_version+1, 
                    update_timestamp=time.time()).where(deploy._pk_expr()))
            await engine.access.update_document_content_by_document_uuid(
                deploy.document_uuid, **data)
        return json(LDR(Code.OK))


class IsEditController(VCHTTPMethodView):
    class IsEditControllerObj:
        app_uuid = doc.String("应用UUID")

    @doc.consumes(IsEditControllerObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("是否有编辑权限")
    async def post(self, request):
        with process_args():
            resource_id = str(request.json.get("resource_id", ""))
            branch_uuid = request.json.get("branch_uuid", "")
            user_uuid = request.ctx.session.get("user_uuid", "")
            esid = request.headers.get("esid")
        if branch_uuid:
            # todo: 判断是否开启版本控制
            resource_id = resource_id + branch_uuid + user_uuid
        resource_id = hashlib.md5(("editor_locker_" + resource_id).encode()).hexdigest()
        lock = await engine.editor_lock_manager.get_lock(resource_id)
        if lock:
            result = lock.id.decode() == esid
        else:
            result = True
        data = {"is_edit_controller": result }
        return json(LDR(data=data))

class AccessEditController(VCHTTPMethodView):
    class AccessEditControllerObj:
        app_uuid = doc.String("应用UUID")

    @doc.consumes(AccessEditControllerObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("夺取编辑权限")
    async def post(self, request):
        with process_args():
            resource_id = str(request.json.get("resource_id", ""))
            branch_uuid = request.json.get("branch_uuid", "")
            user_uuid = request.ctx.session.get("user_uuid", "")
            esid = request.headers.get("esid")
        if branch_uuid and branch_uuid not in resource_id:
        # todo: 判断是否开启版本控制
            resource_id = resource_id + branch_uuid + user_uuid
        resource_id = hashlib.md5(("editor_locker_" + resource_id).encode()).hexdigest()
        lock = await engine.editor_lock_manager.get_lock(resource_id)
        if lock is not None:
            await engine.editor_lock_manager.unlock(lock)
        try:
            await engine.editor_lock_manager.lock(resource_id, lock_timeout=10*60, lock_identifier=esid)
        except LockError:
            return json(LDR(Code.ACCESS_EDIT_CONTROLLER_FAILED))
        return json(LDR(Code.OK))


class FindModelPath(VCHTTPMethodView):
    class FindModelPathObj:
        app_uuid = doc.String("应用UUID")
        model_uuid = doc.String("模型UUID")
        field_uuid_list = doc.String("字段UUID列表")

    @doc.consumes(FindModelPathObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取模型与字段的关联")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            model_uuid = request.json.get("model_uuid")
            field_uuid_list = request.json.get("field_uuid_list")

        field_list = await engine.access.list_field_by_app_uuid(app_uuid, with_sys=True, need_sys=True)
        model_list = await engine.access.list_model_by_app_uuid(app_uuid, with_sys=True)
        relationship_list = await engine.access.list_relationship_by_app_uuid(app_uuid)
        relationship_finder = RelationshipFinder(model_list, relationship_list, field_list)
        path_list = relationship_finder.find_field_path(model_uuid, field_uuid_list)
        return json(LDR(data=path_list))


class FindTargetModelPath(VCHTTPMethodView):
    class FindTargetModelPath:
        app_uuid = doc.String("应用UUID")
        source_model = doc.String("起始模型uuid")
        target_model = doc.String("终点模型uuid")

    @doc.consumes(FindTargetModelPath, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取模型与模型之间所有的关联")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            source_model = request.json.get("source_model")
            target_model = request.json.get("target_model")

        model_list = await engine.access.list_model_by_app_uuid(app_uuid, with_sys=True)
        relationship_list = await engine.access.list_relationship_by_app_uuid(app_uuid)

        def list_sort(v1, v2):
            l = [v1, v2]
            l.sort()
            return "_".join(l)
        
        # TODO 这里的map_使用RelationshipFinder类中的node_uuid_map进行构造
        # 模型和相邻模型无向图
        map_ = {}
        # 模型间关联关系数量
        map_count = {}
        # 存放使用过和未使用的关联关系
        relationship_map = {}
        # 外键关系name对照 和 模型name对照
        r_name, m_name = {}, {}
        t0 = time.time()
        app_log.info(f"relationship length: {len(relationship_list)}")
        # for rel in relationship_list:
        #     s = rel["source_model"]
        #     t = rel["target_model"]
        #     route = list_sort(s, t)
        #     map_count.setdefault(route, [])
        #     map_.setdefault(s, set())
        #     map_.setdefault(t, set())
        #     map_[s].add(t)
        #     map_[t].add(s)
        #     map_count[route].append(rel["relationship_uuid"])
            
        #     relationship_map.setdefault(route, {})
        #     # 为了区分使用过的外键和未使用的
        #     relationship_map[route].setdefault(1, [])
        #     relationship_map[route].setdefault(0, [])
        #     relationship_map[route][1].append(rel["relationship_uuid"])
            
        #     r_name[rel["relationship_uuid"]] = rel["display_name"]
        # for mod in model_list:
        #     m_name[mod["model_uuid"]] = mod["display_name"]

        # for k in map_.keys():
        #     map_[k] = list(map_[k])
        # app_log.info(f"handle  cost: {time.time() - t0}")

        # relationship_list = copy.deepcopy(relationship_list)
        # relationship_finder = RelationshipFinder(model_list, relationship_list, list())
        # # relationship_finder = copy.deepcopy(relationship_finder)
        # t1 = time.time()
        # path_list = relationship_finder.traverse(source_model, target_model, map_, map_count, relationship_map)
        # app_log.info(f"find relationship path cost: {time.time() - t1}")
        relationship_finder = RelationshipFinder(model_list, relationship_list, list())
        path_list = relationship_finder.get_model_path_list(source_model, target_model)
        return json(LDR(data=path_list))
        


class FindPageFieldPath(VCHTTPMethodView):
    class FindPageFieldPathObj:
        app_uuid = doc.String("应用UUID")
        page_uuid = doc.String("页面UUID")

    @doc.consumes(FindPageFieldPathObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面上所有字段与容器的关联路径")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            page_uuid = request.json.get("page_uuid")

        page_document = await engine.access.select_page_content_by_page_uuid(page_uuid)
        if not page_document:
            return json(LDR(IDECode.PAGE_NOT_FOUND))

        page_content = page_document.get(DocumentContent.document_content.name)
        field_list = await engine.access.list_field_by_app_uuid(app_uuid, with_sys=True, need_sys=True)
        model_list = await engine.access.list_model_by_app_uuid(app_uuid, with_sys=True)
        relationship_list = await engine.access.list_relationship_by_app_uuid(app_uuid)
        model_in_page = dict()
        page_finder = PageFinder(
            model_in_page, except_form=False, find_field=True, find_association=True,
            model_list=model_list, relationship_list=relationship_list, field_list=field_list)
        page_finder.find_func(page_content)
        relationship_finder = page_finder.relationship_finder
        container_path_dict = dict()
        model_field_dict = dict()
        # app_log.info(f"model_in_page: {page_finder.model_in_page}")
        for container_uuid, model_info in page_finder.model_in_page.items():
            model_uuid = model_info.get("model")
            field_dict = model_info.get("field", dict())
            app_log.info(f"field_dict: {field_dict}")
            # control_path_dict = dict()
            all_field_uuid_dict = model_field_dict.setdefault(model_uuid, dict())
            for control_uuid, field_uuid_dict in field_dict.items():
                if field_uuid_dict:
                    new_uuid_dict = {"_".join(
                        [control_uuid, field_id]): field for field_id, field in field_uuid_dict.items() if field_id}
                    all_field_uuid_dict.update(new_uuid_dict)

        for model_uuid, all_field_uuid_dict in model_field_dict.items():
            # app_log.info(f"model_uuid: {model_uuid}, field_uuid_dict: {all_field_uuid_dict}")
            field_path_dict = relationship_finder.find_field_path(model_uuid, all_field_uuid_dict)
            if model_uuid:
                container_path_dict.update({model_uuid: field_path_dict})
            # TODO 可能会出现{None: {}} 的情况, 需要优化报错提示
        return json(LDR(data=container_path_dict))


class GetAppFieldInfo(VCHTTPMethodView):
    class GetAppFieldInfoObj:
        app_uuid = doc.String("应用UUID")

    @doc.consumes(GetAppFieldInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面上所有字段与容器的关联路径")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
        model_list = await engine.access.list_model_by_app_uuid(app_uuid, with_sys=True)
        field_list = await engine.access.list_field_by_app_uuid(app_uuid, with_sys=True, need_sys=True)
        relationship_list = await engine.access.list_relationship_by_app_uuid(app_uuid, with_sys=True)
        data_info = gen_model_resource_info(
            model_list, field_list, relationship_list,
            ModelBasic, ModelField, RelationshipBasic)
        return json(LDR(data=data_info))


class GetDirPageCount(VCHTTPMethodView):
    class GetDirPageCount:
        app_uuid = doc.String("应用UUID")
        module_uuid = doc.String("模块UUID")
        document_puuid = doc.String("页面目录UUID")

    @doc.consumes(GetDirPageCount, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取当前目录下页面数量")
    @Permission.policy(("module_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            module_uuid = request.json.get("module_uuid")
            document_puuid = str(request.json.get("document_puuid", ""))            
        count = await engine.access.count_page_by_document_puuid(module_uuid , document_puuid)
        data = {"count": count}
        return json(LDR(data=data))
    
    
class ShowDocumentWorkFlow(VCHTTPMethodView):
    
    class ShowDocumentWorkFlowObj():
        document_uuid = doc.String("文档UUID")
        module_uuid = doc.String("模块UUID")
        ext_tenant = doc.String("租户uuid，可选，获取租户拓展时需加上")
        device_type = doc.String("设备类型 （PC 0  MOBILE 1  PAD 2")
        
    @doc.consumes(ShowDocumentWorkFlowObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取指定文档作为工作流发起页面的工作流")
    @Permission.policy(("module_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            device_type = request.json.get("device_type", DeviceType.PC)
            module_uuid = request.json.get("module_uuid")
            document_uuid = request.json.get("document_uuid")
        
        workflow_list = await engine.access.show_workflow_list(document_uuid)
        return json(LDR(data=list(workflow_list)))

class FieldJump(VCHTTPMethodView):

    class FieldJumpObj():
        field_uuid = doc.String("字段uuid")
        module_uuid = doc.String("模块uuid")
    
    @doc.consumes(FieldJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击字段跳转到字段的所在位置")
    @Permission.policy(("module_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            field_uuid = request.json.get("field_uuid", "")
        model = ModelField
        modelbasic = ModelBasic
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.model_uuid,
            model.field_uuid,
            model.field_name,
            model.field_type.alias("element_type"), 
            modelbasic.model_name
            # modelbasic.position
        )
        query = model.select(*fields).join(
            modelbasic, on=(model.model_uuid==modelbasic.model_uuid)).where(
                model.field_uuid==field_uuid)
        app_log.info(f"search field: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query)
        
        return json(LDR(data=select_field))


class ModelJump(VCHTTPMethodView):

    class ModelJumpObj():
        model_uuid = doc.String("数据模型uuid")
        module_uuid = doc.String("模块uuid")
    
    @doc.consumes(ModelJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击数据模型跳转到数据模型所在位置")
    @Permission.policy(("module_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            model_uuid = request.json.get("model_uuid", "")
        model = ModelBasic
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.model_uuid,
            model.model_name
            # modelbasic.position
        )
        query = model.select(*fields).where(
                model.model_uuid==model_uuid)
        app_log.info(f"search model: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query)
        return json(LDR(data=select_field))


class DocumentJump(VCHTTPMethodView):

    class DocumentJumpObj():
        page_uuid = doc.String("页面uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(DocumentJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击页面跳转到页面所在位置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            page_uuid = request.json.get("page_uuid", "")
        model = DocumentModel
        pagemodel = PageModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.document_name,
            model.document_type
        )
        query = model.select(*fields).join(
            pagemodel, on=(model.document_uuid==pagemodel.document_uuid)).where(
                pagemodel.page_uuid==page_uuid)
        app_log.info(f"search document: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query)
        
        return json(LDR(data=select_field))
    
    
class RelationShipJump(VCHTTPMethodView):
    
    class RelationShipJumpObj():
        relationship_uuid = doc.String("关联id")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(RelationShipJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击关联跳转到关联所在位置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            relationship_uuid = request.json.get("relationship_uuid", "")
        model = RelationshipBasic
        document_model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.relationship_uuid,
            document_model.document_name
        )
        query = model.select(*fields).join(
            document_model, on=(model.document_uuid==document_model.document_uuid)).where(model.relationship_uuid==relationship_uuid)
        app_log.info(f"search field: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query)
        return json(LDR(data=select_field))
    

class EnumJump(VCHTTPMethodView):
    
    class EnumJumpObj():
        enum_uuid = doc.String("枚举id")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(EnumJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击枚举跳转到枚举所在位置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            enum_uuid = request.json.get("enum_uuid", "")
        model = EnumTable
        document_model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.enum_uuid,
            document_model.document_name
           
        )
        query = model.select(*fields).join(
            document_model, on=(model.document_uuid==document_model.document_uuid)).where(
                model.enum_uuid==enum_uuid)
        app_log.info(f"search field: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query)
        return json(LDR(data=select_field))
    
    
class WorkflowJump(VCHTTPMethodView):
    
    class WorkflowJumpObj():
        wf_uuid = doc.String("工作流id")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(WorkflowJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击工作流跳转到工作流所在位置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            wf_uuid = request.json.get("wf_uuid", "")
        model = WorkflowModel
        document_model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.wf_uuid,
            document_model.document_name 
        )
        query = model.select(*fields).join(
            document_model, on=(model.document_uuid==document_model.document_uuid)).where(
                model.wf_uuid==wf_uuid)
        app_log.info(f"search field: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query)
        return json(LDR(data=select_field))
    
    
class CloudFuncJump(VCHTTPMethodView):
    
    class CloudFuncJumpObj():
        func_uuid = doc.String("云函数id")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(CloudFuncJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击云函数跳转到云函数所在位置")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            func_uuid = request.json.get("func_uuid", "")
        model = FuncModel
        document_model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.func_uuid,
            model.py_module_line_no,
            model.from_py_module,
            document_model.document_name 
        )
        query = model.select(*fields).join(
            document_model, on=(model.document_uuid==document_model.document_uuid)).where(
                model.func_uuid==func_uuid)
        app_log.info(f"search field: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query) or {}
        if select_field.get("from_py_module"):
            return json(LDR(data={**select_field, "document_type": DocumentType.PY_MODULE}))
        return json(LDR(data=select_field))
    
    
class ListFieldReference(VCHTTPMethodView):
    
    class ListFieldReferenceObj():
        field_uuid = doc.String("字段uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListFieldReferenceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出字段被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            field_uuid = request.json.get("field_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        result = await engine.access.list_field_reference_by_field_uuid(
            field_uuid, app_uuid)
        return json(LDR(data=list(result)))
    

class ListModelReference(VCHTTPMethodView):
    
    class ListModelReferenceObj():
        model_uuid = doc.String("模型uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListModelReferenceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出模型被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            model_uuid = request.json.get("model_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        result = await engine.access.list_model_reference_by_model_uuid(
            model_uuid, app_uuid)
        # 使用 groupby 的前提是有序，所以先对结果按模块排序，相同模块的文档排在一起
        sorted_result = sorted(list(result), key=lambda item: item['module_uuid'])
        result_group_by_module = list()
        # 按模块对查询结果分组
        for key, group in groupby(sorted_result, key=lambda item: item['module_uuid']):
            result_group_by_module.append(list(group))
        document_key_id = dict()
        document_key_document_uuid = dict()
        # 为文档排序做映射准备，由于文档路径使用文档 id 所以做了一个文档 id 映射，为了获取文档信息方便，做了一个文档 uuid 映射
        document_info = list(await engine.access.list_document_by_app_uuid(app_uuid))
        for document in document_info:
            document_key_id.update({str(document.get('id')): document})
            document_key_document_uuid.update({document.get('document_uuid'): document})
        # 对模块进行排序
        result_group_by_module = sorted(result_group_by_module, 
                                        key=lambda item: document_key_document_uuid.get(item[0]['document_uuid']).get('module_id'))
                
        # 排序策略
        def custom_compare(x, y):
            x_document = document_key_document_uuid.get(x.get("document_uuid"))
            y_document = document_key_document_uuid.get(y.get("document_uuid"))
            # 分割路径，可以通过索引直接获取对应层级文档 id
            x_path = [id for id in x_document.get("document_path").split("/") if id != '']
            y_path = [id for id in y_document.get("document_path").split("/") if id != '']
            if set(x_path) < set(y_path):
                # x 的路径是 y 的子集，说明 x 在外层，需要判断与 x 同一层级下的文档与 x 的 order。
                # 因为路径不记录当前文档，所以短的那个路径最后一个 id 也是上一层的文档，所以获取同一层用的是 len(x_path) 而不需要 -1
                x_order = document_key_document_uuid.get(x.get("document_uuid")).get("order")
                y_order = document_key_id.get(y_path[len(x_path)]).get("order")
                if x_order < y_order:
                    return -1
                elif x_order > y_order:
                    return 1
                else:
                    return -1
            elif set(x_path) > set(y_path):
                x_order = document_key_id.get(x_path[len(y_path)]).get("order")
                y_order = document_key_document_uuid.get(y.get("document_uuid")).get("order")
                if x_order < y_order:
                    return -1
                elif x_order > y_order:
                    return 1
                else:
                    return 1
            elif set(x_path) == set(y_path):
                # 两个文档在同一层级下
                x_order = document_key_document_uuid.get(x.get("document_uuid")).get("order")
                y_order = document_key_document_uuid.get(y.get("document_uuid")).get("order")
                if x_order < y_order:
                    return -1
                elif x_order > y_order:
                    return 1
                else:
                    return 0
            else:
                # 路径中存在不同的路径，需要一层层比，只要有一层路径不同就可以得到顺序结果
                level = 0
                y_level = len(y_path)
                for path_x in x_path:
                    if level < y_level:
                        path_y = y_path[level]
                        x_order = document_key_id.get(path_x).get("order")
                        y_order = document_key_id.get(path_y).get("order")
                        if x_order < y_order:
                            return -1
                        elif x_order > y_order:
                            return 1
                    level += 1
                app_log.info(f"ListModelReference 逻辑已全部走完：x_uuid：{x_document.get('document_uuid')}, y_uuid：{y_document.get('document_uuid')}，路径不同但 order 相同，优先考虑移动目录老逻辑导致的脏数据，重新移动目录即可")
        
        sorted_result = list()
        # 对每个模块中的文档排序
        for group in result_group_by_module:
            sorted_result.extend(sorted(group, key=cmp_to_key(custom_compare)))
                  
        return json(LDR(data=sorted_result))
    
    
class ListCloudFuncReference(VCHTTPMethodView):
    
    class ListCloudFuncReferenceObj():
        func_uuid = doc.String("云函数uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListCloudFuncReferenceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("云函数被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            reference_uuid = request.json.get("func_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        reference_field = DocumentReference.func_reference
        result = await engine.access.list_reference_by_reference_uuid(
            reference_uuid, reference_field, app_uuid)
        return json(LDR(data=list(result)))
   
    
class ListEnumReference(VCHTTPMethodView):
    
    class ListEnumReferenceObj():
        enum_uuid = doc.String("枚举uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListEnumReferenceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("枚举被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            reference_uuid = request.json.get("enum_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        reference_field = DocumentReference.enum_reference
        enum_list = await engine.access.select_enum_by_enum_uuid(reference_uuid)
        if enum_list:
            enum_info = enum_list[0]
            enum_itmes = enum_info.get("value")
            items_id_list = [enum_item.get("uuid") for enum_item in enum_itmes]
            if not items_id_list:
                return json(LDR(data=[]))
            item_reference_field = DocumentReference.enum_item_reference
            enum_reference = await engine.access.list_reference_by_reference_uuid(
                    reference_uuid, reference_field, app_uuid)
            item_reference = await engine.access.list_reference_by_reference_uuid(
                    items_id_list, item_reference_field, app_uuid)
            enum_reference = list(enum_reference)
            document_dict = {enum.get("document_uuid"): enum for enum in enum_reference}
            # 将枚举项的引用也更新到枚举的引用中
            for enum_item in item_reference:
                document_uuid = enum_item.get("document_uuid")
                document_info = document_dict.get(document_uuid)
                item_reference_dict = {}
                for enum_uuid, reference in enum_item.get(item_reference_field.name).items():
                    if enum_uuid in items_id_list:
                        item_reference_dict.update(reference)
                if document_info:
                    reference_dict = document_info.get(reference_field.name).get(reference_uuid, {})
                    reference_dict.update(item_reference_dict)
                    document_info[reference_field.name] = {reference_uuid: reference_dict}
                else:
                    enum_item.pop(item_reference_field.name)
                    enum_item[reference_field.name] = {reference_uuid: item_reference_dict}
                    enum_reference.append(enum_item)
            result = enum_reference
                
        else:
            result = []
        return json(LDR(data=result))
   
    
class ListDocumentReference(VCHTTPMethodView):
    
    class ListDocumentReferenceObj():
        document_uuid = doc.String("文档uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListDocumentReferenceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("页面被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            reference_uuid = request.json.get("document_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        reference_field = DocumentReference.page_reference
        result = await engine.access.list_reference_by_reference_uuid(
            reference_uuid, reference_field, app_uuid)
        return json(LDR(data=list(result)))
   
    
class ListPrintTemplateReference(VCHTTPMethodView):
    
    class ListPrintTemplateReferenceObj():
        template_uuid = doc.String("打印模板uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListPrintTemplateReferenceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("打印模板被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            reference_uuid = request.json.get("template_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        reference_field = DocumentReference.print_reference
        result = await engine.access.list_reference_by_reference_uuid(
            reference_uuid, reference_field, app_uuid)
        return json(LDR(data=list(result)))


class ListLabelPrintReference(VCHTTPMethodView):
    
    class ListLabelPrintReferenceObj():
        template_uuid = doc.String("标签打印uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListLabelPrintReferenceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("标签打印被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            reference_uuid = request.json.get("template_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        reference_field = DocumentReference.label_print_reference
        result = await engine.access.list_reference_by_reference_uuid(
            reference_uuid, reference_field, app_uuid)
        return json(LDR(data=list(result)))

    
class ListWorkFlowReference(VCHTTPMethodView):
    
    class ListWorkFlowReferenceObj():
        wf_uuid = doc.String("工作流uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListWorkFlowReferenceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("工作流被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            reference_uuid = request.json.get("wf_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        reference_field = DocumentReference.wf_reference
        result = await engine.access.list_reference_by_reference_uuid(
            reference_uuid, reference_field, app_uuid)
        return json(LDR(data=list(result)))
    
class ListRelationShipReference(VCHTTPMethodView):
    
    class ListRelationShipReferenceObj():
        relationship_uuid = doc.String("关联uuid")
        app_uuid = doc.String("应用uuid")
    
    @doc.consumes(ListRelationShipReferenceObj, content_type=DOC.JSON_TYPE, 
                  location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出关联被引用的地方")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            relationship_uuid = request.json.get("relationship_uuid", "")
            app_uuid = request.json.get("app_uuid", "")
        result = await engine.access.list_relationship_reference_by_relationship_uuid(
            relationship_uuid, app_uuid)
        result = list(result)
        return json(LDR(data=result))


class PrintTemplateJump(VCHTTPMethodView):
    
    class PrintTemplateJumpObj():
        document_uuid = doc.String("文档uuid")
    
    @doc.consumes(PrintTemplateJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击打印模板跳转到打印模板所在位置")
    @Permission.policy(("document_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            document_uuid = request.json.get("document_uuid", "")
        model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.document_name 
        )
        query = model.select(*fields).where(model.document_uuid==document_uuid)
        app_log.info(f"search field: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query)
        return json(LDR(data=select_field))


class LabelPrintJump(VCHTTPMethodView):
    
    class LabelPrintJumpObj():
        document_uuid = doc.String("文档uuid")
    
    @doc.consumes(LabelPrintJumpObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("点击标签打印跳转到标签打印所在位置")
    @Permission.policy(("document_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            document_uuid = request.json.get("document_uuid", "")
        model = DocumentModel
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.document_name 
        )
        query = model.select(*fields).where(model.document_uuid==document_uuid)
        app_log.info(f"search field: {query}")
        select_field = await engine.access.select_obj_by_query(model, query=query)
        return json(LDR(data=select_field))


class RestoreAdapterSetting(HTTPMethodView):
    class RestoreAdapterSettingObj():
        type = doc.String("输入方式")
        adapter = doc.String("选择适配器")
    
    @doc.consumes(RestoreAdapterSettingObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("使用默认模板创建适配器")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            input_type = request.json.get("type", 0)
            adapter = request.json.get("adapter", "")
        if input_type not in [InputType.external_adapter]:
            return json(LDR(data={}))
        adapter_template: list = InputAdapter.adapters.get(adapter)
        
        all_editors = restore_adapter(adapter_template)
        return json(LDR(data=all_editors))


class CopyDocument(VCHTTPMethodView):

    class CopyDocumentObj():
        app_uuid = doc.String("应用uuid")
        document_uuid = doc.String("文档uuid")
        module_uuid = doc.String("模块uuid")
        user_uuid = doc.String("用户uuid")
        ext_tenant = doc.String("租户uuid，为空创建非拓展")
    
    @doc.consumes(CopyDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("复制文档")
    @Permission.policy(("document_uuid", Action.MODIFY),)
    async def post(self, request):
        with process_args():
            document_uuid = request.json.get("document_uuid")
            user_uuid = request.json.get("user_uuid")
            ext_tenant = request.json.get("ext_tenant")
            document_puuid = request.json.get("document_puuid")
            
        if ext_tenant == "":
            ext_tenant = None
        document = await engine.access.get_document_by_document_uuid(document_uuid)
        document = document.to_dict()
        document_type = document.get("document_type")
        module_uuid = document.get("module_uuid")
        order = document.get("order")
        not_allowed_copy = [
            DocumentType.DIR, DocumentType.MODEL, DocumentType.SM, DocumentType.SECURITY, 
            DocumentType.THEME, DocumentType.CONNECTOR, DocumentType.MODULE_DEPLOY
        ]
        if document_type in not_allowed_copy:
            return json(LDR(Code.NOT_ALLOWED_COPY))
        document_content = await engine.access.get_document_content_by_document_uuid(document_uuid)
        document.pop("id")
        document.update({
            "document_uuid": lemon_uuid(), 
            "order": float(order) + 0.000000001  # 紧挨原文档下面
        })
        document.pop("document_version")
        await engine.access.create_document(user_uuid, **document)
        document = await engine.access.get_document_by_document_uuid(document.get("document_uuid"))
        document = document.to_dict()
        document.pop("id")
        # new_document_uuid = lemon_uuid()
        # document.update({"document_uuid": new_document_uuid})
        if document_content:
            document_content = self.update_document_content_uuid(document, document_content, ext_tenant)
            document_check_service = engine.service.document_check.create_service(document_type)
            document_check_service.is_copy = True
            if document_check_service:
                await document_check_service.check(document_content, document_type)
                self.add_git_action(request, partial(self._git_action, document.get("document_uuid")))
            else:
                return json(LDR(Code.ERROR))
        return json(LDR(data=document))

    def update_document_content_uuid(self, document, document_content, ext_tenant):
        document_name = document.get("document_name")
        document_content = document_content.to_dict() if document_content else document_content
        document_content.update({"module_uuid": document.get("module_uuid")})
        document_content.update({"document_uuid": document.get("document_uuid")})
        document_content.update({"ext_tenant": ext_tenant})
        document_content.update({"document_version": 1})
        document_content.update({"app_uuid": document.get("app_uuid")})
        if document_content["document_content"].get("name"):
            document_content["document_content"].update({"name": document_name})
        if document_content["document_content"].get("title"):
            document_content["document_content"].update({"title": document_name})
        if document_content["document_content"].get("page_title", {}).get("value"):
            document_content["document_content"]["page_title"].update({"value": document_name})
        if document_content["document_content"].get("uuid"):
            document_content["document_content"]["uuid"] = lemon_uuid()
        if document.get("document_number"):
            document_content["document_number"] = document.get("document_number")
        document_content.pop("id")
        return document_content

    async def _git_action(self, document_uuid):
        return await engine.git_request.sync_task_to_repo(SyncTORepoType.SYNC_DOC, document_uuid)


class SearchDocument(VCHTTPMethodView):

    class SearchDocumentObj():
        app_uuid = doc.String("应用uuid")
        search_name = doc.String("搜索名称")
    
    @doc.consumes(SearchDocumentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("搜索文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            search_name = request.json.get("search_name")

        space, semicolon = " ", ";"
        
        async def search_by(by=" "):
            search_name_list = list(filter(lambda x: x, search_name.split(by)))
            if not search_name_list:
                return True, []
            
            if len(search_name_list) == 1:
                search_data = await search_document(search_name_list[0])
                return True, search_data
            
            return False, search_name_list
        
        async def search_document(s_name):
            name_data, type_data = await engine.access.search_document_by_name(s_name, app_uuid)
            data = {
                "group_by_name": list(name_data),
                "group_by_type": list(type_data)
            }
            return data
        
        async def search_by_space():
            result, search_data = await search_by()
            if result:
                return search_data
            return []

        async def search_by_semicolon():
            result, search_data = await search_by(semicolon)
            if result:
                return search_data
            
            name_data, type_data = await engine.access.search_document_by_name(
                search_data, app_uuid)
            data = {
                "group_by_name": list(name_data),
                "group_by_type": list(type_data)
            }
            return data

        if space in search_name:
            data = await search_by_space()
        elif semicolon in search_name:
            data = await search_by_semicolon()
        else:
            data = await search_document(search_name)
    
        return json(LDR(data=data))


class SearchDocumentContent(VCHTTPMethodView):

    class SearchDocumentContentObj():
        app_uuid = doc.String("应用uuid")
        search_name = doc.String("搜索名称")
        document_type = doc.String("文档类型")

    @doc.consumes(SearchDocumentContentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("搜索文档内容")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            search_name = request.json.get("search_name")
            document_type = request.json.get("document_type")

        async def update_path(document_info):
            document_path = document_info.get("document_path")
            path_list = []
            if document_path:
                docuemnt_path_list = document_path.split("/")[1:-1]
                path_list.extend([new_document_data.get(int(document_id)) for document_id in docuemnt_path_list])
            document_info.update({"document_path": path_list})

        async def update_search_res(document_info, current_res_count):
            func_content = document_info.pop("func")
            line_regex = rf'(.*{regex_search_name}.*(?<!\\)\n?)|(.*(?<!\\)\n?)'
            lines = re.findall(line_regex, func_content)
            search_res = []
            for index, line in enumerate(lines):
                if line[0]:
                    # [行号, 行内容]
                    search_res.append([index+1, line[0]])
                    current_res_count += 1
                    if current_res_count >= max_res_count:
                        break
            if not search_res:
                return current_res_count, False
            document_info.update({"search_res": search_res})
            return current_res_count, True

        if search_name:
            # \ 在字符串中需要用两根 \ 表示，SQL 查询语句也需要用 \ 表示转义，1 根所以需要 4 根 \ 表示，
            # 同时 1 根 \ 存在数据库里是 2 根，所以需要 8 根 \ 表示
            # " 在数据库中会存成 \"，前端传来不会自动添加转义，需要手动添加，所以 " 需要 4 根 \ 加 " 表示
            # ' 在数据库中存的还是 '，前端传来会自动添加转义 \，所以不需要手动转义
            # app_log.info(f"{query}") 打印的 log \ 会比实际执行的 SQL 语句少一半，实际执行的以 query.sql() 为准
            database_search_name = search_name.replace("\\", r"\\\\").replace("_", r"\_").replace("%", r"\%").replace('"', r'\\"')
            search_data = await engine.access.search_document_content_by_document_type(
                app_uuid, document_type, database_search_name)
            search_data = list(search_data)
            document_data = list(await engine.access.list_document_by_app_uuid(app_uuid))
            new_document_data = {data.get("id"):{
                "document_name": data.get("document_name"),
                "document_type": data.get("document_type")
            } for data in document_data}

            max_res_count = 10000  # 最大搜索结果数
            is_max = False  # 是否达到最大搜索结果数
            current_res_count = 0
            search_res = []

            regex_search_name = re.escape(search_name)  # 搜索内容中属于正则表达式的特殊字符转义
            for document_info in search_data:
                await update_path(document_info)
                current_res_count, has_res = await update_search_res(document_info, current_res_count)
                # 搜索 \n 时，行末的 \n 会被数据库查询匹配到，但不会被正则表达式匹配，所以如果文件仅是因为数据库查询匹配到的不放入搜索结果
                if has_res:
                    search_res.append(document_info)
                    if current_res_count >= max_res_count:
                        is_max = True
                        break

        return json(LDR(data={"data": search_res, "is_max": is_max}))


class ListDocumentPath(VCHTTPMethodView):

    class ListDocumentPathObj():
        document_uuid = doc.String("文档uuid")
    
    @doc.consumes(ListDocumentPathObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取文档目录展开信息")
    @Permission.policy(("document_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            document_uuid = request.json.get("document_uuid")
        
        document = await engine.access.get_document_by_document_uuid(
            document_uuid)
        if not document:
            return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
        
        module_uuid = document.module_uuid
        module = await engine.access.get_module_by_module_uuid(module_uuid)
        module_name = module.module_name
        children = []
        data = {
            "module_uuid": module_uuid,
            "module_name": module_name,
            "children": children
        }

        model = DocumentModel
        # o_model = DocumentOrder
        document_data_dict, document_order_dict = {}, {}
        document_id_with_uuid_dict = {}
        document_path = document.document_path
        document_puuid_list = list(filter(lambda x: x, document_path.split("/")))
        order_father_uuid_list = [module_uuid]

        # 获取模块下的文档，但需要将
        fields = (
            model.id,
            model.document_uuid,
            model.document_name,
            model.document_type,
            model.document_version,
            model.document_puuid,
            model.ext_tenant.is_null(False).alias("is_extension")
        )
        order_by_expr = DocumentModel.order.asc()
        source_document_list = await engine.access.list_document_by_module_uuid(
            module_uuid, fields, order_by_expr=order_by_expr)
        module_document_list = []
        for document_info in source_document_list:
            document_puuid = document_info.get(model.document_puuid.name)
            if document_puuid:
                continue
            document_info.update({"children": []})
            document_id = document_info.pop(model.id.name)
            document_uuid = document_info.get(model.document_uuid.name)
            document_data_dict.update({document_uuid: document_info})
            module_document_list.append(document_info)

        # 获取文档的 路径上的目录
        document_list = await engine.access.list_document_by_document_id_list(
            document_puuid_list)
        for document_info in document_list:
            document_id = document_info.pop(model.id.name)
            document_uuid = document_info.get(model.document_uuid.name)
            document_id_with_uuid_dict.update({document_id: document_uuid})
            if document_uuid in document_data_dict:
                # 忽略掉第一级父目录，在获取模块下文档时已处理过了
                continue
            document_info.update({"children": []})
            document_data_dict.update({document_uuid: document_info})

        children.extend(module_document_list)
        document_list = await engine.access.list_document_by_document_puuid_list(
            list(document_id_with_uuid_dict.values()), order_by_expr=order_by_expr)
        for document_info in document_list:
            document_uuid = document_info.get(model.document_uuid.name)
            document_puuid = document_info.get(model.document_puuid.name)
            if document_puuid in document_data_dict:
                if document_uuid in document_data_dict:
                    document_info = document_data_dict.get(document_uuid)
                else:
                    document_info.update({"children": []})
                    document_data_dict.update({document_uuid: document_info})
                document_data_dict[document_puuid]["children"].append(
                    document_info)

        return json(LDR(data=data))
    
    
class ListAPPDocumentInfo(VCHTTPMethodView):
    
    @doc.summary("展示导航/应用安全/主题配置/部署配置/水印设置文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            
        document_types = [
            DocumentType.THEME, DocumentType.NAVIGATION, 
            DocumentType.APP_SECURITY, DocumentType.DEPLOY_CONFIG, 
            DocumentType.WATERMARK
        ]
        async with engine.db.objs.atomic():
            app_documents = await engine.access.list_document_by_app_uuid_document_type(
                app_uuid, document_type=document_types)
        
        # 主题配置增加所选择的模块主题
        for document in app_documents:
            if document.get("document_type") == DocumentType.THEME:
                content = await engine.access.get_document_content_by_document_uuid(document.get("document_uuid"))
                theme_uuid = content.document_content.get("theme_uuid", None)
                app_theme_documents = await engine.access.list_document_by_app_uuid_document_type(
                    app_uuid, document_type=DocumentType.MODULE_THEME)
                # 所选的模块主题已被删除不返回值
                module_theme = list(filter(lambda x:x["document_uuid"]==theme_uuid, app_theme_documents))
                if not module_theme:
                    theme_uuid = None
                document["theme_uuid"] = theme_uuid

        output = list(app_documents)
        if getattr(request.ctx, "version_control", False):
            staged_docs = await self.get_staged_docs('./')
            doc_name_dict = dict()
            for doc in staged_docs:
                change_type, file_path, new_file_path, content_before, content = doc
                document_name = new_file_path.split("/")[-1]
                doc_name_dict[document_name] = change_type
            for res in output:
                change_type = doc_name_dict.get(res["document_name"])
                res.update({"change_type": change_type})
        output.sort(key=lambda x: document_types.index(x.get("document_type")))
        return json(LDR(data=output))
            
    async def get_staged_docs(self, path):
        return await engine.git_request.sync_task_to_repo(
            SyncTORepoType.GET_STAGED_CHANGES, paths=[path])


class ExportExtensionTemplate(HTTPMethodView):
    
    class ExportExtensionTemplateObj():
        extension_uuid = doc.String("扩展uuid")
        app_uuid = doc.String("app_uuid")
        business = doc.String("所属行业")
        cover = doc.String("封面")
        preview = doc.String("预览")
        introduction = doc.String("简介")
        action = doc.String("操作: export/update/save")
    
    @doc.consumes(ExportExtensionTemplateObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("发布单个模块作为模板或更新模板")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            extension_uuid = request.json.get("extension_uuid")
            app_uuid = request.json.get("app_uuid")
            business = request.json.get("business")
            cover = request.json.get("cover")
            preview = request.json.get("preview")
            introduction = request.json.get("introduction")
            action = request.json.get("action")  # export(上传新文档) / save(更新配置) / update(更新版本)
            # TODO 校验参数
            # TODO 校验用户是否为发行商

        async with engine.db.objs.atomic():
            module_info = await engine.access.get_module_by_app_module_uuid(
                app_uuid, extension_uuid)
            extension_info: Extension = await engine.access.select_extension(
                app_uuid, extension_uuid, as_dict=False)
            publisher = await engine.access.get_app_publisher(user_uuid)
            publisher = publisher.get("id") if publisher else publisher
            
        if not module_info:
            return json(LDR(IDECode.EXTENSION_MODULE_NOT_FOUND))
            
        if action == "export":
            t = Template(app_uuid, extension_uuid, module_info.module_name, 
                         business, cover, preview, introduction, publisher=publisher, 
                         user_uuid=user_uuid)
        else:
            if not extension_info:
                return json(LDR(IDECode.EXTENSION_NOT_FOUND))
            if action == "save":
                t = Template(app_uuid, extension_uuid, module_info.module_name, 
                            business, cover, preview, introduction, publisher=extension_info.publisher,
                            extension_id=extension_info.id)
            else:
                t = Template(app_uuid, extension_uuid, extension_info.extension_name, 
                            extension_info.business, extension_info.cover, extension_info.preview, 
                            extension_info.introduction, publisher=extension_info.publisher,
                            extension_id=extension_info.id)
        return await t.handle(action)  
    
    
class ExportExtensionUIComponent(HTTPMethodView):
    
    class ExportExtensionUIComponentObj():
        extension_uuid = doc.String("扩展uuid")
        app_uuid = doc.String("app_uuid")
        business = doc.String("所属行业")
        cover = doc.String("封面")
        preview = doc.String("预览")
        introduction = doc.String("简介")
        action = doc.String("操作: export/update/save")
    
    @doc.consumes(ExportExtensionUIComponentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("发布ui组件或更新")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            extension_uuid = request.json.get("extension_uuid")
            app_uuid = request.json.get("app_uuid")
            business = request.json.get("business")
            cover = request.json.get("cover")
            preview = request.json.get("preview")
            introduction = request.json.get("introduction")
            extension_info_add = request.json.get("extension_info", dict())
            action = request.json.get("action")  # export(上传新文档) / save(更新配置) / update(更新版本) / debug(上传调试)
        extension_name = extension_info_add.get("component_name", "本地调试工具")
        current_version = extension_info_add.get("version")
        async with engine.db.objs.atomic():
            extension_info: Extension = await engine.access.select_extension(
                app_uuid, extension_uuid, as_dict=False)
            publisher = await engine.access.get_app_publisher(user_uuid)
            publisher = publisher.get("id") if publisher else publisher
        if action in ["export", "debug"]:
            if action == "debug":
                user_uuid = real_user_uuid
            c = UIComponent(
                app_uuid, extension_uuid=extension_uuid, extension_name=extension_name, 
                business=business, cover=cover, preview=preview, introduction=introduction, 
                current_version=current_version, user_uuid=user_uuid, 
                publisher=publisher, extension_info_add=extension_info_add
            )
        else:
            if not extension_info:
                return json(LDR(IDECode.EXTENSION_NOT_FOUND))
            if action == "save":
                c = UIComponent(
                    app_uuid, extension_uuid, extension_info.extension_name, business, cover, preview,
                    introduction, publisher=publisher, extension_id=extension_info.id
                )
            else:  
                c = UIComponent(
                    app_uuid, extension_uuid, extension_name,
                    extension_info.business, extension_info.cover, extension_info.preview,
                    extension_info.introduction, current_version=current_version,
                    publisher=extension_info.publisher, extension_info_add=extension_info_add,
                    extension_id=extension_info.id
                )  # 需要重新上传
        return await c.handle(action)
    

class CustomProcessExtension(HTTPMethodView):
    
    @doc.summary("用户处理扩展")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            extension_uuid = request.json.get("extension_uuid")
            action = request.json.get("action")  # update / import  # TODO 这里集合
        

class ProcessExtension(HTTPMethodView):
    
    class ProcessExtensionObj():
        extension_uuid = doc.String("扩展uuid")
        action = doc.String("操作: del/public/private")
    
    @doc.consumes(ProcessExtensionObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("发行商操作扩展")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            extension_uuid = request.json.get("extension_uuid")
            action = request.json.get("action")  # delete / public / private  
            if action not in ["delete", "public", "private"]:
                return json(LDR(Code.ARGS_ERROR))
        
        query = Extension.select().where(
            Extension.extension_uuid==extension_uuid)
        extension = await engine.access.select_obj_by_query(Extension, query, as_dict=False)
        publisher = await engine.access.get_app_publisher(user_uuid)
        publisher = publisher.get("id") if publisher else publisher
        if extension.release_type == ReleaseType.TEMPLATE:
            t = Template(extension.app_uuid, extension_uuid, extension.extension_name, 
                        extension.business, extension.cover, extension.preview, 
                        extension.introduction, publisher=publisher, extension_id=extension.id)
            return await t.handle(action)
        elif extension.release_type == ReleaseType.UI:
            t = UIComponent(extension.app_uuid, extension_uuid, extension.extension_name, 
                        extension.business, extension.cover, extension.preview, 
                        extension.introduction, publisher=publisher, extension_id=extension.id)
            return await t.handle(action)
        else:
            return json(LDR(IDECode.EXTENSION_NOT_FOUND))


class ImportExtensionTemplate(VCHTTPMethodView):

    async def post(self, request):
        with process_args():
            extension_uuid = request.json.get("extension_uuid")
            target_app_uuid = request.json.get("target_app_uuid")
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            new_module_name = request.json.get("module_name")
            import_to_cur_app = request.json.get("import_to_cur_app", False)
            condition: asyncio.Condition = request.ctx.condition

        app_obj = await engine.access.get_app_by_app_uuid(app_uuid)
        middle_user = app_obj.user_uuid
        record = await engine.access.get_obj(
            ImportRecord, app_uuid=app_uuid, user_uuid=user_uuid, extension_uuid=extension_uuid,
            action=ImportResult.WAITING)
        to_accept = record is not None
        scope_limit = list()
        if to_accept:
            scope_limit = [ScopeType.PRIVATE]
        else:
            if import_to_cur_app:
                scope_limit = [ScopeType.PRIVATE, ScopeType.DEBUG, ScopeType.PUBLIC]
            else:
                scope_limit = [ScopeType.PUBLIC]
        template: dict = await engine.access.select_extension(
            target_app_uuid, extension_uuid, scope_limit, need_detail=True)
        if not template:
            return json(LDR(IDECode.EXTENSION_NOT_FOUND, data={"step": 4, "status": ImportStatus.FAILED}))
        publisher_uuid = template.get("user_uuid")
        publisher_info = await engine.access.get_app_publisher(publisher_uuid)
        publisher_name = publisher_info.get("name") if publisher_info else None
        clear_timeout_helper()
        helper_id = app_uuid + "_" + extension_uuid
        if helper := IMPORT_AGENT_DICT.get(helper_id):
            helper: ImportHelper
            if helper.status not in [ImportStatus.SUCCEED, ImportStatus.FAILED]:
                return json(helper.state)
        helper = ImportHelper(
            app_uuid, middle_user, user_uuid, template, record, new_module_name, publisher_name)
        IMPORT_AGENT_DICT.update({
            helper_id: helper
        })
        asyncio.create_task(helper.handle_import())
        return json(helper.state)


class GetImportStatus(VCHTTPMethodView):

    async def post(self, request):
        with process_args():
            extension_uuid = request.json.get("extension_uuid")
            app_uuid = request.json.get("app_uuid")
        helper_id = app_uuid + "_" + extension_uuid
        helper: ImportHelper = IMPORT_AGENT_DICT.get(helper_id)
        if not helper:
            return json(LDR(data={"status": ImportStatus.SUCCEED, "step": 4}))
        if helper.status == ImportStatus.SUCCEED:
            clear_timeout_helper(helper_id)
        return json(helper.state)


class ImportExtensionUIComponent(HTTPMethodView):

    class ImportExtensionUIComponentObj():
        extension_uuid = doc.String("扩展uuid")
        target_app_uuid = doc.String("扩展app_uuid")
        app_uuid = doc.String("自身app_uuid")
    
    @doc.consumes(ImportExtensionUIComponentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("导入单个扩展UI组件")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            extension_uuid = request.json.get("extension_uuid")
            target_app_uuid = request.json.get("target_app_uuid")
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            import_to_cur_app = request.json.get("import_to_cur_app", False)

        record = await engine.access.get_obj(
            ImportRecord, app_uuid=app_uuid, user_uuid=user_uuid, extension_uuid=extension_uuid,
            action=ImportResult.WAITING)
        to_accept = record is not None  # 接受许可导入, 只能是私有
        scope_limit = []
        if import_to_cur_app:
            to_accept = False
        if to_accept:
            scope_limit = [ScopeType.PRIVATE]
        else:
            if not import_to_cur_app:
                scope_limit = [ScopeType.PUBLIC]
        component: dict = await engine.access.select_extension(
            target_app_uuid, extension_uuid, scope_limit, need_detail=True)

        if not component:
            return json(LDR(IDECode.EXTENSION_NOT_FOUND))

        # 安装自定义组件
        extension_info = component.get("extension_info", dict())
        ui_package_url: str = extension_info.get("ui_package_url")
        version_number = extension_info.get("version")
        component_type = extension_info.get("component_type")

        if not ui_package_url or not version_number:
            return json(LDR(Code.ERROR))

        user_tool = None
        if extension_info.get("local_debug"):
            tool_uuid = lemon_uuid()
            content = {
                "ui_package_url": extension_info.get("ui_package_url"),
                "component_type": component_type,
                "local_debug": True
            }
        else:
            file_name_without_fuffix = extension_info.get("component_id")
            file_name = file_name_without_fuffix + ".lemonui"

            part_path_url = await self.unzip_ui_component(app_uuid, ui_package_url, file_name, version_number)
            tool_uuid = lemon_uuid()
            content = {
                "id": extension_info.get("component_id"),
                "path_url": part_path_url,
                "version": version_number,
                "component_type": component_type,
                "local_debug": extension_info.get("local_debug", False)
            }
        user_tool = await engine.access.get_user_tool(
            **{"app_uuid": app_uuid, "package_uuid": extension_uuid})
        user_tool2 = await engine.access.get_user_tool(
            **{
                "category_uuid": "7183e6bdf1955d739313ff2979b9254d",  # XXX const中定义的导入组件uuid
                "tool_name": extension_info.get("component_name"),
                "app_uuid": app_uuid
            })

        if user_tool:
            return json(LDR(IDECode.EXTENSION_ALREADY_IMPORTED))
        elif user_tool2:
            return json(LDR(IDECode.ALREADY_EXIST_SAME_NAME_UI_COMPONENT))
        else:
            data = {
                "app_uuid": app_uuid,
                "package_uuid": extension_uuid,
                "category_uuid": "7183e6bdf1955d739313ff2979b9254d",  # const中定义的导入组件uuid
                "tool_uuid": tool_uuid,
                "tool_name": extension_info.get("component_name"),
                "tool_type": ToolType.USER,
                "content": content,
                "tool_class": extension_info.get("tool_class", ToolClass.PAGE),
                "description": extension_info.get("description", ""),
                "icon_type": extension_info.get("icon_type"),
                "icon_color": extension_info.get("icon_color", "#000000"),
                "icon": extension_info.get("icon"),
                "extend_from": component.get("id")
            }
            await engine.access.create_user_tool(**data)
        if to_accept:
            # 更新record
            await engine.db.objs.execute(
                record.update(
                    action=ImportResult.IMPORTED, action_time=int(time.time())
                    ).where(record._pk_expr()))
        else:
            # 新建record, accepted=True
            await engine.access.create_extension_import_record(
                app_uuid, user_uuid, extension_uuid, component.get("id"),
                action=ImportResult.IMPORTED,
                import_type=ImportType.ACTIVE_IMPORT, apply_user_uuid=user_uuid)

        return json(LDR())

    async def unzip_ui_component(self, app_uuid, ui_package_url, file_name, version_number):
        to_dir = engine.config.CUSTOM_STATIC_PATH_PREFIX + "/" + app_uuid
        if not os.path.exists(to_dir):
            os.makedirs(to_dir)
        to_file = to_dir + "/" + file_name
        await DesignOss().get_oss_object(url=ui_package_url, to_file=to_file)

        f = zipfile.ZipFile(to_file)
        file_name_without_fuffix = file_name.split(".")[0]
        unzip_to_path = "/".join([
            engine.config.CUSTOM_STATIC_PATH_PREFIX,
            app_uuid,
            file_name_without_fuffix,
            version_number
        ])
        if not os.path.exists(unzip_to_path):
            os.makedirs(unzip_to_path)
        for b in f.namelist():
            f.extract(b, unzip_to_path)
        f.close()
        return unzip_to_path[unzip_to_path.find("custom"):]

    def gen_fix_uuid(self, app_uuid, file_name_without_fuffix):
        return str(uuid.uuid5(uuid.UUID(app_uuid), file_name_without_fuffix)).replace("-", "")


class UpgradeExtensionUIComponent(HTTPMethodView):

    class UpgradeExtensionUIComponentObj():
        extension_uuid = doc.String("扩展uuid")
        app_uuid = doc.String("当前app_uuid")
    
    @doc.consumes(UpgradeExtensionUIComponentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("升级单个扩展UI组件")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            extension_uuid = request.json.get("extension_uuid")
            app_uuid = request.json.get("app_uuid")

        # 升级工具箱工具
        user_tool = await engine.access.get_user_tool(package_uuid=extension_uuid, is_delete=False)
        query = Extension.select().where(
            Extension.extension_uuid == extension_uuid, Extension.is_delete == False)
        extension = await engine.access.select_obj_by_query(Extension, query, as_dict=False)
        if not user_tool or not extension:
            return json(LDR(IDECode.ERROR))

        extension_detail_query = ExtensionDetail.select().where(ExtensionDetail.extension_id == extension.id)
        extension_detail = await engine.access.select_obj_by_query(
            ExtensionDetail, extension_detail_query, as_dict=False)
        if extension_detail:
            return json(LDR(IDECode.ERROR))

        user_tool_version = user_tool.content.get("version")
        if user_tool_version == extension.version:
            return json(LDR(IDECode.ERROR))

        extension_info = extension_detail.extension_info
        file_name_without_fuffix = extension_info.get("component_id")
        file_name = file_name_without_fuffix + ".lemonui"

        ui_package_url: str = extension_info.get("ui_package_url")
        extension_version = extension_info.get("version")
        component_type = extension_info.get("component_type")

        part_path_url = await self.unzip_ui_component(app_uuid, ui_package_url, file_name, version)
        content = {
            "id": extension_info.get("component_id"),
            "path_url": part_path_url,
            "version": extension_version,
            "component_type": component_type,
            "local_debug": extension_info.get("local_debug", False)
        }
        data = {
            "tool_name": extension_info.get("component_name"),
            "tool_type": ToolType.USER,
            "content": content,
            "tool_class": extension_info.get("tool_class", ToolClass.PAGE),
            "description": extension_info.get("description", ""),
            "icon_type": extension_info.get("icon_type"),
            "icon_color": extension_info.get("icon_color", "#000000"),
            "icon": extension_info.get("icon")
        }

        await engine.access.update_user_tool(package_uuid=user_tool.package_uuid, **data)
        return json(LDR())

    async def unzip_ui_component(self, app_uuid, ui_package_url, file_name, version):
        to_dir = engine.config.CUSTOM_STATIC_PATH_PREFIX + "/" + app_uuid
        if not os.path.exists(to_dir):
            os.makedirs(to_dir)
        to_file = to_dir + "/" + file_name
        await DesignOss().get_oss_object(url=ui_package_url, to_file=to_file)

        f = zipfile.ZipFile(to_file)
        file_name_without_fuffix = file_name.split(".")[0]
        unzip_to_path = engine.config.CUSTOM_STATIC_PATH_PREFIX + "/" + app_uuid + "/" + file_name_without_fuffix + "/" + version
        if not os.path.exists(unzip_to_path):
            os.makedirs(unzip_to_path)
        for b in f.namelist():
            f.extract(b, unzip_to_path)
        f.close()
        return unzip_to_path[unzip_to_path.find("custom"):]
        
        
class CheckUIComponentUpgrade(HTTPMethodView):
    
    class UpgradeExtensionUIComponentObj():
        extension_uuid = doc.String("扩展uuid")
        app_uuid = doc.String("当前app_uuid")
    
    @doc.consumes(UpgradeExtensionUIComponentObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("检查UI组件是否符合版本更新条件")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            extension_uuid = request.json.get("extension_uuid")
        
        result = {
            "imported": False
        }
        query = Extension.select().where(
            Extension.extension_uuid==extension_uuid, Extension.is_delete==False)
        extension = await engine.access.select_obj_by_query(Extension, query, as_dict=False)
        if not extension:
            return json(LDR(IDECode.EXTENSION_NOT_FOUND))
        user_tool = await engine.access.get_user_tool(app_uuid=app_uuid, package_uuid=extension_uuid)
        # TODO 检查版本是否不同
        if not user_tool:
            return json(LDR(data=result))
        
        user_tool_version = user_tool.content.get("version")
        if user_tool_version == extension.version:
            need_upgrade = False
        else:
            need_upgrade = True
        result.update({
            "need_upgrade": need_upgrade
        })
        return json(LDR(data=result))

    
class RefuseImportExtension(HTTPMethodView):
    class RefuseImportExtensionObj():
        extension_uuid = doc.String("扩展uuid")
        target_app_uuid = doc.String("扩展app_uuid")
        app_uuid = doc.String("自身app_uuid")
    
    @doc.consumes(RefuseImportExtensionObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("拒绝导入单个扩展模板")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            extension_uuid = request.json.get("extension_uuid")
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)

        record = await engine.access.get_obj(
            ImportRecord, app_uuid=app_uuid, user_uuid=user_uuid, extension_uuid=extension_uuid, 
            action=ImportResult.WAITING)
        if not record:
            return "没找到可导入的模板"
        async with engine.db.objs.atomic():
            await engine.db.objs.execute(
                record.update(
                    action=ImportResult.REFUSED, action_time=int(time.time())
                    ).where(record._pk_expr()))
        return json(LDR())
    
    
class ListImportRecord(HTTPMethodView):
    
    class ListImportRecordObj():
        extension_uuid = doc.String("扩展uuid")
        app_uuid = doc.String("自身app_uuid")
        action = doc.String("列出的记录  0:等待处理; 1:接受了的; 2:拒绝了的")
    
    @doc.consumes(ListImportRecordObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出导入记录")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            action = request.json.get("action", ImportResult.WAITING)
        
        app_obj = await engine.access.get_app_by_app_uuid(app_uuid)
        middle_user = app_obj.user_uuid  # 可是是用户的或团队的
        if middle_user != real_user_uuid:
            # 不是当前应用的所有者或团队管理员, 不能收到当前应用的导入记录
            teamplayer = await engine.access.get_teamplayer_by_user_uuid(
                middle_user, real_user_uuid, permission_list=[1,2])
            if not teamplayer:
                return json(LDR(data=list()))
            user_uuid = teamplayer.get("team_uuid")

        async with engine.db.objs.atomic():
            records = await engine.access.list_extension_import_record(
                app_uuid, user_uuid, action=action)
        return json(LDR(data=list(records)))
        
    
class ApplyImportExtensionTemplate(HTTPMethodView):
    
    class ApplyImportExtensionTemplateObj():
        extension_uuid = doc.String("扩展uuid")
        target_app_uuid = doc.String("扩展app_uuid")
        accept_app_name = doc.String("接收方app_name")
        accept_account = doc.String("接收方手机号")
    
    @doc.consumes(ApplyImportExtensionTemplateObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("请求导出单个扩展模板")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            extension_uuid = request.json.get("extension_uuid")
            target_app_uuid = request.json.get("target_app_uuid")
            accept_app_name = request.json.get("accept_app_name")
            accept_account = request.json.get("accept_account")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            # TODO check_args
        query = Extension.select().where(
            Extension.app_uuid==target_app_uuid, 
            Extension.extension_uuid==extension_uuid, 
            Extension.scope==ScopeType.PRIVATE
            )
        async with engine.db.objs.atomic():
            template: dict = await engine.access.select_obj_by_query(Extension, query)
            user_query = User.select().where(User.mobile_phone==accept_account)
            user_info: dict = await engine.access.select_obj_by_query(User, user_query)
            user_info = user_info or dict()
            team_obj = await engine.access.get_obj(Team, team_id=accept_account)
            accept_app_info: object = await engine.access.get_obj(APP, app_name=accept_app_name)
        if not all([user_info or team_obj, accept_app_info]):
            return json(LDR(IDECode.APP_NAME_ACCOUNT_ERROR))
        accept_user_uuid = accept_app_info.user_uuid
        accept_app_uuid = accept_app_info.app_uuid
        records = await engine.access.list_extension_import_record(
            app_uuid=accept_app_uuid, user_uuid=accept_user_uuid, action=ImportResult.WAITING, 
            extension_uuid=extension_uuid)
        if records:
            # 有未处理
            return json(LDR(IDECode.REPEAT_PUBLISHER_PUSH_TO_SAME_USER))
        if accept_user_uuid != user_info.get("user_uuid"):
            if team_obj is None or accept_user_uuid != team_obj.team_uuid:
                return json(LDR(IDECode.APP_NAME_ACCOUNT_ERROR))
        if template:
            async with engine.db.objs.atomic():
                await engine.access.create_extension_import_record(
                    accept_app_uuid, accept_user_uuid, extension_uuid, template.get("id"), 
                    action=ImportResult.WAITING, 
                    import_type=ImportType.PUBLIHSER_PUSH, apply_user_uuid=real_user_uuid)
        else:
            # 这个方式只能导出私有的
            return json(LDR(IDECode.EXTENSION_NOT_FOUND))
        return json(LDR())
        
    
class ListMallExtension(HTTPMethodView):
    
    class ListMallExtensionObj():
        document_uuid = doc.String("文档uuid")
    
    @doc.consumes(ListMallExtensionObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按要求列出商城扩展")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")  # 已导入的
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            publisher = request.ctx.session.get("publisher")
            page = request.json.get("page")
            release_type = request.json.get("release_type")  # 模块类型
            business = request.json.get("business")  # 行业
            search = request.json.get("search")  # 搜索条件
            imported = request.json.get("imported", None)  # 已导入的
            scope = request.json.get("scope", 0)  # 0:共有 1:私有 2:调试
            # TODO check_args()
    
        modules = await engine.access.list_app_imported_module(app_uuid, extend_from=None)
        extend_from = [m["extend_from"] for m in modules] 
        user_tools = await engine.access.list_app_user_tool(app_uuid)
        extend_from.extend([t["extend_from"] for t in user_tools])
        kwargs = {
            "page": page, 
            "search": search, 
            "release_type": release_type, 
            "business": business
        }  
        if scope == 0:
            kwargs.update({"scope": scope})
        elif scope == 1:
            kwargs.update({
                "scope": [0, 1], 
                "user_uuid": user_uuid
            })
        elif scope == 2:
            kwargs.update({
                "scope": scope, 
                "user_uuid": real_user_uuid
            })
        if imported:
            kwargs.update({"extend_from": extend_from})
        count, extensions = await engine.access.list_extension(**kwargs)
        for extension in extensions:
            extension["imported"] = extension.get("id") in extend_from or extension.get("extension_uuid") in extend_from
        return json(LDR(data={"count": count, "extensions": list(extensions)}))
    
class GetExtensionDeploy(HTTPMethodView):
    
    class GetExtensionDeployObj():
        extension_uuid = doc.String("扩展uuid")
    
    @doc.consumes(GetExtensionDeployObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取单个扩展配置")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            extension_uuid = request.json.get("extension_uuid")
            
        query = Extension.select().where(
            Extension.extension_uuid==extension_uuid)
        extension = await engine.access.select_obj_by_query(Extension, query, as_dict=False)
        if extension:
            if extension.release_type == ReleaseType.TEMPLATE:
                document = await engine.access.get_document_by_module_uuid_document_type(
                    extension_uuid, Document.TYPE.MODULE_DEPLOY, need_delete=True)
                content:DocumentContent = await engine.access.get_document_content_by_document_uuid(document.document_uuid)
                content = content.to_dict()
                return json(LDR(data=content))
            else:
                return json(LDR(IDECode.EXTENSION_RELEASE_TYPE_INVALID))
        else:
            return json(LDR(IDECode.EXTENSION_NOT_FOUND))
    
class ListReleasableExtension(HTTPMethodView):
    
    class ListReleasableExtensionObj():
        app_uuid = doc.String("app_uuid")
        release_type = doc.Integer("release_type")
    
    @doc.consumes(ListReleasableExtensionObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出可发布的模块")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            release_type = request.json.get("release_type", ReleaseType.TEMPLATE)
        contents = await engine.access.list_document_content_by_app_uuid_document_type(
            app_uuid, DocumentType.MODULE_DEPLOY)
        result = list()
        for content in contents:
            dc = content.get("document_content", {})
            if dc and not dc.get("on_shelf"):
                if dc.get("release_type") == release_type:
                    result.append(dc)
        return json(LDR(data=result))


class GetExtensionInfo(HTTPMethodView):
    
    class GetExtensionInfoObj():
        app_uuid = doc.String("app_uuid")
        extension_uuid = doc.String("扩展uuid")
        release_type = doc.String("发布类型")
    
    @doc.consumes(GetExtensionInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取扩展详情")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            extension_uuid = request.json.get("extension_uuid")
            release_type = request.json.get("release_type", ReleaseType.UI)
        fields = [
            Extension.extension_uuid, 
            Extension.extension_name, 
            Extension.version
        ]
        query = Extension.select(*fields).where(
            Extension.extension_uuid==extension_uuid, 
            Extension.release_type==release_type)
        if release_type == ReleaseType.UI:
            query = query.join(ExtensionDetail, on=(Extension.id==ExtensionDetail.extension_id))
            query = query.select_extend(ExtensionDetail.extension_info)
        extension = await engine.access.select_obj_by_query(Extension, query, as_dict=True)
        if extension:
            need_upgrade = False
            if release_type == ReleaseType.UI:
                user_tool = await engine.access.get_user_tool(
                    **{"app_uuid": app_uuid, "package_uuid": extension_uuid})
                if user_tool:
                    if user_tool.content.get("version") != extension.get("version"):
                        need_upgrade = True
            extension.update({"need_upgrade": need_upgrade})
        else:
            return json(LDR(IDECode.EXTENSION_NOT_FOUND))
        return json(LDR(data=extension))
    
    
class DeleteUserTool(HTTPMethodView):
    
    class DeleteUserToolObj():
        app_uuid = doc.String("app_uuid")
        tool_uuid = doc.String("tool_uuid")
        release_type = doc.String("发布类型")
    
    @doc.consumes(DeleteUserToolObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除导入工具")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            tool_uuid = request.json.get("tool_uuid")
        
        query = UserTool.delete().where(UserTool.tool_uuid==tool_uuid, UserTool.app_uuid==app_uuid)
        await engine.access.delete_obj_by_query(UserTool, query)
        return json(LDR())
    
class ListAPPModuleTheme(VCHTTPMethodView):

    class ListAPPModuleThemeObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPModuleThemeObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有模块主题")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        app_uuid = request.json.get("app_uuid")
        document_type = DocumentType.MODULE_THEME
        return await engine.service.query.list_document_groupby_module_by_document_type(
            app_uuid, document_type, alias_name="theme")


class ListAPPLayout(VCHTTPMethodView):

    class ListAPPLayoutObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListAPPLayoutObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("按模块列出应用所有应用布局")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        app_uuid = request.json.get("app_uuid")
        document_type = DocumentType.APP_LAYOUT
        return await engine.service.query.list_document_groupby_module_by_document_type(
            app_uuid, document_type, alias_name="applayout")


class ListValueEditorVariable(VCHTTPMethodView):

    class ListVariableObj():
        app_uuid = doc.String("应用UUID")
    
    @doc.consumes(ListVariableObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出所有值编辑器变量")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        data = [
            {
                "uuid": "system_variables",
                "name": "系统变量",
                "children": ValueEditorVariables.ALL
            }
        ]
        return json(LDR(data=data))


class ListResourceTag(VCHTTPMethodView):
    class ListResourceTagObj():
        app_uuid = doc.String("应用UUID")
        module_uuid = doc.String("模块UUID")
        action = doc.String("操作配置")

    @doc.consumes(ListResourceTagObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("树形列出资源标签")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            module_uuid = request.json.get("module_uuid")
            action = request.json.get("action")
        try:
            int(action, 2)
        except (ValueError, TypeError):
            app_log.info(traceback.format_exc())
            return json(LDR(IDECode.ACTION_INVALID))
        module_resources = await engine.access.list_all_resource_by_app_uuid_module_uuid(app_uuid, module_uuid)
        module_tags = await engine.access.list_module_resource_tag(app_uuid, module_uuid, action=action)
        tag_obj_list, resource_uuid_dict = process_tag_grouby_resource_uuid(module_tags)
        top_resource = process_resource_tree(module_resources, resource_uuid_dict, filter_with_tags=True)
        final_result = await \
            engine.access.list_module_resource_tag_groupby_module(app_uuid, module_uuid, top_resource)
        return final_result  


class ListTag(VCHTTPMethodView):
    class ListTagObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ListTagObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("2D列出标签")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            module_uuid = request.json.get("module_uuid")
        result = await engine.access.list_tag(app_uuid, module_uuid)
        result = {r["tag_uuid"]: r for r in result}
        return json(LDR(data=result))


class ListUnCheckedDocuments(VCHTTPMethodView):
    class ListObj:
        app_uuid = doc.String("应用UUID")
        document_list = doc.List(doc.String("文档uuid列表"))

    @doc.consumes(ListObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出未检查文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            document_list = request.json.get("document_list", [])
        document_uuids = set()
        if not document_list:
            collection_key = make_collection_key(app_uuid)
            inprogress_key = make_in_progress_collection_key(app_uuid)
            async for val, score in engine.redis.izscan(collection_key):
                document_uuids.add(val.split(':')[-1])
            async for val, score in engine.redis.izscan(inprogress_key):
                document_uuids.add(val.split(':')[-1])
        query = DocumentModel.select(DocumentModel.document_name, DocumentModel.document_uuid, DocumentModel.document_type,
                                     ModuleModel.module_name).join(ModuleModel, join_type=JOIN.LEFT_OUTER,
                                            on=(DocumentModel.module_uuid==ModuleModel.module_uuid)
                                            ).where(DocumentModel.document_uuid.in_(document_uuids), DocumentModel.app_uuid==app_uuid)
        result = await engine.access.list_obj(DocumentModel, query, as_dict=True)
        return json(LDR(data=list(result)))


class SyncIconFont(VCHTTPMethodView):
    class SyncIconObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(SyncIconObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("同步远程图标库")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
        result = await sync_iconfont(app_uuid)
        return json(LDR(data=result))

class ListIconFont(VCHTTPMethodView):
    class ListIconObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ListIconObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取远程图标库图标")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
        result = await engine.access.list_iconfont(app_uuid)
        return json(LDR(data=list(result)))

class ListCommonIconFont(VCHTTPMethodView):
    class ListIconObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ListIconObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取常用图标库图标")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        result = await engine.access.list_iconfont("system")
        return json(LDR(data=list(result)))
    

class GatherIconFont(VCHTTPMethodView):
    class SyncIconObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(SyncIconObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("搜集应用所有图标")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
        result = await gather_iconfont(app_uuid)
        return json(LDR(data=result))


class GetDocumentStatus(VCHTTPMethodView):
    class GetDocumentStatusObj():
        app_uuid = doc.String("应用UUID")
        document_uuid = doc.String("文档UUID")

    @doc.consumes(GetDocumentStatusObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取文档状态")
    @Permission.policy(("document_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            document_uuid = request.json.get("document_uuid")
        data = await engine.access.get_document_status_by_document_uuid(document_uuid)
        return json(LDR(data=data))

class FilterModuleFunc(VCHTTPMethodView):
    @doc.summary("筛选某模块中的特定云函数")
    @Permission.policy(("module_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            module_uuid = request.json.get("module_uuid")
            prefix = request.json.get("prefix")

        fields = (
            Func.app_uuid,
            Func.module_uuid,
            Func.document_uuid,
            Func.func_uuid,
            Func.func_name,
            Func.arg_list,
            Func.return_list
        )
        data = await engine.access.list_func_by_module_uuid(module_uuid, prefix=prefix, fields=fields)
        return json(LDR(data={"func_list": list(data)}))


class FilterModuleEnums(VCHTTPMethodView):
    @doc.summary("筛选某模块中的特定枚举")
    @Permission.policy(("module_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            module_uuid = request.json.get("module_uuid")
            prefix = request.json.get("prefix")
        data = await engine.access.list_enum_by_module_uuid(module_uuid, prefix=prefix)
        return json(LDR(data={"enum_list": list(data)}))


class FilterModuleModels(VCHTTPMethodView):
    @doc.summary("筛选某模块中的数据模型")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            module_uuid = request.json.get("module_uuid")
            prefix = request.json.get("prefix")
        data = await engine.access.list_model_by_module_uuid(module_uuid, prefix=prefix)
        return json(LDR(data={"model_list": list(data)}))


class ListDocumentsByType(VCHTTPMethodView):
    @doc.tag(url_prefix)
    @doc.summary("筛选应用中特定类型的文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            document_type = request.json.get("document_type")
        data = await engine.access.list_document_by_app_uuid_document_type(app_uuid, document_type)
        return json(LDR(data={"documents": list(data)}))


class GetDocumentByName(VCHTTPMethodView):
    @doc.tag(url_prefix)
    @doc.summary("根据文档名筛选文档")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            module_uuid = request.json.get("module_uuid")
            document_type = request.json.get("document_type")
            document_name = request.json.get('document_name')
        data: Optional[Document] = None
        if document_name:
            data = await engine.access.get_document_by_module_uuid_document_name(module_uuid, document_name)
        if document_type:
            data = await engine.access.get_document_by_module_uuid_document_type(module_uuid, document_type)
        return json(LDR(data=data and model_to_dict(data)))


class GetAllExtensionPackages(VCHTTPMethodView):
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
        all_packages_data = await engine.access.get_all_extension_package(app_uuid)
        return json(LDR(data=all_packages_data))


class GetLatestEditTimestamp(VCHTTPMethodView):
    @Permission.policy(("app_uuid", Action.SELECT), )
    @doc.summary("根据模块/文档类型分组获取最近更新的文档的更新时间")
    async def post(self, request: Request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            include_document_types = request.json.get("include_document_types", [])
            exclude_documents = request.json.get("exclude_documents", [])
            include_modules = request.json.get("include_modules", [])

        return json(LDR(data={"edit_records": await engine.access.get_latest_edit_by_document_type_module(
            app_uuid, include_document_types, exclude_documents, include_modules
        )}))


class GetFuncDescription(VCHTTPMethodView):
    @Permission.policy(("app_uuid", Action.SELECT), )
    @doc.summary("获取指定云函数的描述字段")
    async def post(self, request: Request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            module_name = request.json.get("module_name")
            func_name = request.json.get("func_name")

        module = await engine.access.get_module_by_app_uuid_module_name(app_uuid, module_name)
        if not module:
            return json(LDR(data={"description": None}))
        conditions = {"app_uuid": app_uuid, "module_uuid": module.module_uuid, "func_name": func_name}
        func = await engine.access.get_func(**conditions)
        if not func:
            app_log.info("func is not found: conditions: %s", conditions)
            return json(LDR(data={"description": None}))
        desc = await engine.access.get_func_description_by_document_uuid(func.document_uuid)
        return json(LDR(data={"description": desc}))


class AddPublishGroup(HTTPMethodView):

    class AddPublishGroupObj():
        app_uuid = doc.String("应用UUID")
        tenant_uuid = doc.String("租户uuid")
        group_name = doc.String('发布组 组名')
    
    @doc.consumes(AddPublishGroupObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("添加发布组")
    @Permission.policy(("app_uuid", Action.SELECT),)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = str(request.json.get("app_uuid", ""))
            tenant_uuid = str(request.json.get("tenant_uuid", ""))
            group_name = str(request.json.get("group_name", ""))

        if not all([tenant_uuid, group_name]):
            return json(LDR(Code.ARGS_ERROR))

        app = await engine.access.get_app(app_uuid=app_uuid)
        if not app:
            return json(LDR(Code.ARGS_ERROR))

        tenant = await engine.access.get_tenant(tenant_uuid)
        if not tenant:
            return json(LDR(Code.ARGS_ERROR))

        data = {
            "group_uuid": lemon_uuid(),
            "group_name": group_name,
            "tenant_id": tenant.get("id")
        }
        group = await engine.access.create_obj(PublishGroup, **data)

        await engine.access.create_obj(AppGroup, **{"app_id": app.id, "group_id": group.id})
        return json(LDR())
