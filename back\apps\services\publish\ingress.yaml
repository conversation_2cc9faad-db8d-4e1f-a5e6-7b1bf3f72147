
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: runtime-ingress
  namespace: design 
  annotations:
    kubernetes.io/ingress.class: "runtime-nginx"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/configuration-snippet: |
       access_log /var/log/nginx/runtime_server.access.log upstreaminfo if=$loggable;
       error_log  /var/log/nginx/runtime_server.error.log;
spec:
  rules:
  - host: "runtime.lemonstudio.tech"
    http:
      paths:
      - pathType: Prefix
        path: "/2619184d57a652829c539fdf16a64216(/|$)(.*)"
        backend:
          service:
            name: app-2619184d57a652829c539fdf16a64216
            port:
              number: 7000
      