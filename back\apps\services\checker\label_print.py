# -*- coding:utf-8 -*-

from baseutils.log import app_log
from apps.entity import <PERSON>Basic, LabelPrint as LabelPrintModel
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.ide_const import LabelPrintType, LabelPrintImageType, FieldType, LemonDesignerErrorCode as LDEC
from apps.ide_const import (
    LabelPrint, LabelPrintRectBox, LabelPrintStraightLine, LabelPrintText,
    LabelPrintMultiLine, LabelPrintBarCode, LabelPrintQRCode, LabelPrintImage
)
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker
import copy

class ComponentCheckerService(CheckerService):

    def _check_children(self, children, component_uuid_set, **kwargs):
        component_dict = {
            LabelPrintType.RECT_BOX: RectBoxCheckerService,
            LabelPrintType.STRAIGHT_LINE: StraightLineCheckerService,
            LabelPrintType.TEXT: TextCheckerService,
            LabelPrintType.MULTILINE_TEXT: MultilineTextCheckerService,
            LabelPrintType.BAR_CODE: BarCodeCheckerService,
            LabelPrintType.QR_CODE: QRCodeCheckerService,
            LabelPrintType.IMAGE: ImageCheckerService
        }

        for child in children:
            child_type = child.get('label_type')
            component_checker_class = component_dict.get(child_type)
            if component_checker_class is None or not issubclass(component_checker_class, ComponentCheckerService):
                continue
            kwargs.update({'app_field_dict': self.app_field_dict, 'app_relationship_dict': self.app_relationship_dict})
            component_checker_service = component_checker_class(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, child, **kwargs)
            component_checker_service.document_other_info = self.document_other_info
            try:
                component_checker_service.check_uuid()
                component_checker_service.check_uuid_unique(component_uuid_set)
                component_checker_service.check_all()
                component_checker_service._update_reference()

                if child_type in LabelPrintType.IMAGE_TYPE:
                    component_checker_service._check_image_field()
                if child_type in LabelPrintType.TEXT_TYPE:
                    component_checker_service._check_text_field()

            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(component_checker_service)
                raise e
            else:
                self.update_any_list(component_checker_service)

    def _update_reference(self):
        data_source = self.element.get('data_source')
        attr = self.get_attr_by_name('DATASOURCE')
        if data_source:
            self.update_reference_by_data_source(data_source, self.element, attr)

    def _check_image_field(self):
        data_source = self.element.get('data_source', {})
        image_type = data_source.get('type')
        attr = self.get_attr_by_name('DATASOURCE')

        if image_type == LabelPrintImageType.PICTURE_TABEL:
            if not data_source.get("image"):
                return_code = LDEC.IMAGE_NOT_EXIST
                self._add_error_list(attr, return_code=return_code, element_data=self.element)
        elif image_type == LabelPrintImageType.IMAGE_FIELD:
            field_info = data_source.get("field_info", {})
            field_uuid = field_info.get("uuid", "")
            if not field_uuid:
                return_code = LDEC.IMAGE_NOT_EXIST
                self._add_error_list(attr, return_code=return_code)
            else:
                field_basic_info = self.app_field_dict.get(field_uuid)
                if not field_basic_info:
                    return_code = LDEC.MODEL_FIELD_NOT_EXISTS
                    self._add_error_list(attr, return_code=return_code)
                else:
                    title = self.element.get("name")
                    element_uuid = self.element.get("uuid")
                    self.update_field_reference(
                        field_uuid, title, element_uuid, attr)
                    field_type = field_basic_info.get("field_type")
                    if field_type not in [FieldType.ENUM, FieldType.IMAGE]:
                        # 只能选图片 枚举
                        return_code = LDEC.IMAGE_TYPE_ERROR
                        self._add_error_list(attr, return_code=return_code)
        elif image_type == LabelPrintImageType.BAR_CODE:
            code_data = data_source.get('code_data')
            if not code_data:
                return_code = LDEC.CODE_NOT_EXIST
                self._add_error_list(attr, return_code=return_code, element_data=self.element)

    def _check_text_field(self):
        data_source = self.element.get('data_source', {})
        attr = self.get_attr_by_name('DATASOURCE')
        edit_value = data_source.get('edit_value', {})
        self._check_value_editor(edit_value, attr, self.app_field_dict, self.app_relationship_dict)


class RectBoxCheckerService(ComponentCheckerService):
    attr_class = LabelPrintRectBox.ATTR
    uuid_error = LDEC.LABEL_PRINT_RECT_BOX_UUID_ERROR
    uuid_unique_error = LDEC.LABEL_PRINT_RECT_BOX_UUID_UNIQUE_ERROR
    
    def initialize(self):
        super().initialize()
        self.element.setdefault('name','矩形框')


class StraightLineCheckerService(ComponentCheckerService):
    attr_class = LabelPrintStraightLine.ATTR
    uuid_error = LDEC.LABEL_PRINT_STRAIGHT_LINE_UUID_ERROR
    uuid_unique_error = LDEC.LABEL_PRINT_STRAIGHT_LINE_UUID_UNIQUE_ERROR

    def initialize(self):
        super().initialize()
        self.element.setdefault('name','直线')


class TextCheckerService(ComponentCheckerService):
    attr_class = LabelPrintText.ATTR
    uuid_error = LDEC.LABEL_PRINT_TEXT_UUID_ERROR
    uuid_unique_error = LDEC.LABEL_PRINT_TEXT_UUID_UNIQUE_ERROR
    
    def initialize(self):
        super().initialize()
        self.element.setdefault('name','文字')


class MultilineTextCheckerService(ComponentCheckerService):
    attr_class = LabelPrintMultiLine.ATTR
    uuid_error = LDEC.LABEL_PRINT_MULTILINE_TEXT_UUID_ERROR
    uuid_unique_error = LDEC.LABEL_PRINT_MULTILINE_TEXT_UUID_UNIQUE_ERROR

    def initialize(self):
        super().initialize()
        self.element.setdefault('name','多行文字')
      

class BarCodeCheckerService(ComponentCheckerService):
    attr_class = LabelPrintBarCode.ATTR
    uuid_error = LDEC.LABEL_PRINT_BAR_CODE_UUID_ERROR
    uuid_unique_error = LDEC.LABEL_PRINT_BAR_CODE_UUID_UNIQUE_ERROR

    def initialize(self):
        super().initialize()
        self.element.setdefault('name','条形码')


class QRCodeCheckerService(ComponentCheckerService):
    attr_class = LabelPrintQRCode.ATTR
    uuid_error = LDEC.LABEL_PRINT_QR_CODE_UUID_ERROR
    uuid_unique_error = LDEC.LABEL_PRINT_QR_CODE_UUID_UNIQUE_ERROR

    def initialize(self):
        super().initialize()
        self.element.setdefault('name','二维码')


class ImageCheckerService(ComponentCheckerService):
    attr_class = LabelPrintImage.ATTR
    uuid_error = LDEC.LABEL_PRINT_IMAGE_UUID_ERROR
    uuid_unique_error = LDEC.LABEL_PRINT_IMAGE_UUID_UNIQUE_ERROR
    
    def initialize(self):
        super().initialize()
        self.element.setdefault('name','图片')


class LabelPrintCheckerService(ComponentCheckerService):

    attr_class = LabelPrint.ATTR
    uuid_error = LDEC.LABEL_PRINT_UUID_ERROR
    uuid_unique_error = LDEC.LABEL_PRINT_UUID_UNIQUE_ERROR
    name_error = LDEC.LABEL_PRINT_NAME_FAILED
    name_unique_error = LDEC.LABEL_PRINT_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.children = self.element.get("label_context", list())
        self.component_uuid_set = set()
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
        self.data_source = self.element.get("data_source")
        self.a = self.children

    def build_insert_query_data(self):
        return {
            LabelPrintModel.app_uuid.name: self.app_uuid,
            LabelPrintModel.module_uuid.name: self.module_uuid,
            LabelPrintModel.document_uuid.name: self.document_uuid,
            LabelPrintModel.ext_tenant.name: self.ext_tenant,
            LabelPrintModel.label_uuid.name: self.element_uuid,
            LabelPrintModel.label_name.name: self.element_name,
        }

    def build_update_query_data(self):
        return {
            LabelPrintModel.label_name.name: self.element_name,
            LabelPrintModel.is_delete.name: False
        }

    def build_update_query(self):
        query_data = self.build_update_query_data()
        return LabelPrintModel.update(**query_data).where(
            LabelPrintModel.label_uuid==self.element_uuid)

    @staticmethod
    def build_delete_query(print_uuid):
        return LabelPrintModel.update(**{
                LabelPrintModel.is_delete.name: True
            }).where(LabelPrintModel.label_uuid==print_uuid)

    def check_modify(self, document_print_uuid_set):
        if self.element_uuid in document_print_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

    # 检查标签打印所有 children
    def check_children(self, **kwargs):
        self._check_children(self.children, self.component_uuid_set, **kwargs)

    @checker.run
    def check_field(self):
        model = self.element.get('data_source', {}).get('model')
        if model not in self.app_model_dict:
            return_code = LDEC.MODEL_NOT_EXISTS
            self._add_error_list(self.attr_class.DATASOURCE, return_code=return_code, element_data=self.element)

class LabelPrintDocumentCheckerService(DocumentCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str,
        element: dict, document_version: int,
        app_label_print_list: list, app_field_dict, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, LabelPrintModel, *args, **kwargs)
        self.document_version = document_version
        self.app_field_dict = app_field_dict
        self.app_print_uuid_set = set()
        self.module_print_name_set = set()
        self.document_print_uuid_set = set()
        self.app_model_dict = dict()
        self.app_relationship_dict = dict()
        self.build_app_relation_dict()
        
        for model_info in self.app_model_list:
            model_uuid = model_info.get(ModelBasic.model_uuid.name)
            self.app_model_dict[model_uuid] = model_info
        
        for label_print_dict in app_label_print_list:
            print_uuid = label_print_dict.get("label_uuid", "")
            print_name = label_print_dict.get("label_name", "")
            print_module_uuid = label_print_dict.get("module_uuid", "")
            print_document_uuid = label_print_dict.get("document_uuid", "")

            # 找到原文档中所有的标签打印 ，为了新增、更新、删除文档的 打印模板
            if print_document_uuid == self.document_uuid:
                self.document_print_uuid_set.add(print_uuid)
            else:
                # 排除当前文档所有的 print_uuid ，获取应用的所有 print_uuid
                self.app_print_uuid_set.add(print_uuid)
                # 排除当前文档所有的 print_name ，获取模块的所有 print_name
                if print_module_uuid == self.module_uuid:
                    self.module_print_name_set.add(print_name)

    def build_app_relation_dict(self):
        for relationship_dict in self.app_relationship_list:
            r_uuid = relationship_dict.get("relationship_uuid")
            self.app_relationship_dict[r_uuid] = relationship_dict

    @checker.run
    def check_print(self):
        if self.new_ext_doc:
            need_check = True
        else:
            is_ext = self.element.get("is_extension")
            need_check = True
            if self.ext_tenant and not is_ext:
                need_check = False
        label_print_checker_service = LabelPrintCheckerService(
            self.app_uuid, self.module_uuid, self.module_name,
            self.document_uuid, self.document_name, self.element,
            ext_tenant=self.ext_tenant, is_copy=self.is_copy, app_model_dict = self.app_model_dict,
            app_field_dict=self.app_field_dict, app_relationship_dict=self.app_relationship_dict)
        label_print_checker_service.document_other_info = self.document_other_info
        print_uuid = label_print_checker_service.element_uuid
        try:
            label_print_checker_service.check_uuid()
            if self.is_copy:
                label_print_checker_service.check_uuid_unique(self.app_print_uuid_set)
            else:
                if need_check:
                    label_print_checker_service.check_uuid_unique(self.app_print_uuid_set)
            label_print_checker_service.check_all()
            label_print_checker_service.check_children(app_field_dict=self.app_field_dict, 
                                                 app_relationship_list=self.app_relationship_list,
                                                 app_model_list=self.app_model_list)
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(label_print_checker_service)
            raise e
        else:
            self.update_any_list(label_print_checker_service)

        # 找到新增 或 更新的 标签打印
        if need_check:
            label_print_checker_service.check_modify(self.document_print_uuid_set)
        if label_print_checker_service.insert_query_list:
            self.document_insert_list.extend(label_print_checker_service.insert_query_list)
        if label_print_checker_service.update_query_list:
            self.document_update_list.extend(label_print_checker_service.update_query_list)

        # 找出删除的 标签打印 ，将其 is_delete 置为 True
        delete_print_uuid_set = self.document_print_uuid_set - set([print_uuid])
        for this_print_uuid in delete_print_uuid_set:
            query = label_print_checker_service.build_delete_query(this_print_uuid)
            self.document_delete_list.append(query)