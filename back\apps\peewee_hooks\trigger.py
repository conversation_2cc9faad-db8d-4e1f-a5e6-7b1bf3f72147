import deepdiff
from sanic import Sanic
from apps.ide_const import (DocumentType, DocUpdateMessage, DocUpdateType, DocDeltaMessage, DocItemDelta,
                            DeltaItemChangeType, DeltaItemType)
from apps.entity import DocumentContent, Document
from baseutils.log import app_log
from baseutils.celery import celery
from apps.engine import Engine
import re
import asyncio
import functools
from celery import Task
from typing import Union
from apps.peewee_hooks.utils import get_doc_info_from_query, run_hook_task
from apps.utils import publish_document_update

__CACHE__ = {}


class DebounceDocTask:
    instances = {}

    def __new__(cls, engine, document_uuid, task_name, **kwargs):
        uid = f"{document_uuid}_{task_name}"
        if uid in DebounceDocTask.instances:
            return DebounceDocTask.instances[uid]
        instance = super().__new__(cls)
        instance.__uid = uid
        DebounceDocTask.instances[uid] = instance
        return instance

    def __repr__(self) -> str:
        return f"doc task: {self.__uid}"

    def __init__(self, engine: Engine, document_uuid, task_name, wait=3):
        if getattr(self, 'lock', None) is not None:
            return
        self.document_uuid = document_uuid
        self.func = None
        self.wait = wait
        self.lock = asyncio.Lock()
        self.handler = None
        self.loop = asyncio.get_event_loop()
        self.engine = engine
        self.exec_task = None

    async def initialize(self):
        doc = __CACHE__.get(self.document_uuid)
        if not doc:
            document = await self.engine.access.get_document_by_document_uuid(self.document_uuid)
            content = await self.engine.access.get_document_content_by_document_uuid(self.document_uuid)
            doc = (document, content)
            __CACHE__[self.document_uuid] = doc
        return doc

    def clear(self):
        if self.handler:
            self.handler.cancel()
            self.handler = None
        if self.func:
            self.func = None
        if self.exec_task:
            self.exec_task.cancel()
            self.exec_task = None

    def set_delay(self):
        self.handler = self.loop.call_later(self.wait, self.delay_exec)

    def delay_exec(self):
        self.exec_task = self.loop.create_task(self._delay_exec())

    async def _delay_exec(self):
        await self.lock.acquire()
        if self.func:
            try:
                if self.engine.app.config.USE_CELERY:
                    func: Union[Task, None] = globals().get(f"{self.func.func.__name__}_daemon")
                    if func:
                        app_log.info(f'push celery task: {self.func.func}')
                        func.delay(*self.func.args)
                else:
                    app_log.info(f'run task: {self.func.func}')
                    future = asyncio.ensure_future(self.func())
                    await future
            finally:
                __CACHE__.pop(self.document_uuid, None)
                self.instances.pop(self.__uid, None)
        self.lock.release()

    def __call__(self, func, *args, **kwargs):
        try:
            self.clear()
            self.func = functools.partial(func, *args, **kwargs)
            self.set_delay()
        except Exception as e:
            app_log.error(e)
        finally:
            pass


def after_document_content_save(sender: DocumentContent, app: Sanic, engine: Engine, query, result):
    # 可以单独放到一个进程中去
    async def do_func_render(func: dict, module: dict):
        from apps.services.ide.render import FuncRender, Func
        func_table = Func(uuid=func["uuid"],
                          name=func["name"],
                          description=func["description"],
                          func=func["func"],
                          arg_list=func["arg_list"],
                          return_list=func["return_list"],
                          icon=func.get("icon"),
                          icon_type=func.get("icon_type"))
        func_table.func_uuid = func_table.uuid
        func_table.func_name = func_table.name
        return FuncRender(func_table, module).render()

    async def post_save_task(document_uuid, document_content, pub_type):
        if not document_content or not document_uuid:
            return
        doc = await engine.access.get_document_by_document_uuid(document_uuid)
        if not doc:
            return
        module = None
        if doc.module_uuid:
            module = await engine.access.get_module_by_module_uuid(doc.module_uuid)  # type: ignore
        app_log.debug(f"insert, module: {module}, doc: {doc}")
        data = DocumentContent(document_uuid=document_uuid, document_content=document_content)
        data = data.to_dict()
        data.update({Document.document_version.name: doc.document_version})
        await publish_document_update(engine, doc.app_uuid, doc.module_uuid, doc.document_uuid,
                                      data)
        if doc.document_type == DocumentType.FUNC:
            module_dict = {
                "module_uuid": module.module_uuid,
                "module_name": module.module_name
            }
            func_str = await do_func_render(document_content, module_dict)
            # 将func_str publish到redis
            key = f"{app.config.REDIS_CHANNEL_DOC_FULL_UPDATE_KEY}:{doc.app_uuid}:"
            msg = DocUpdateMessage(
                msg_type=pub_type,
                doc_uuid=doc.document_uuid,
                doc_type=doc.document_type,
                module_name=module.module_name,
                obj_uuid=document_content.get("uuid"),
                content=func_str,
            )
            app_log.info(f"after_document_content_save, {key}: {msg}")
            await engine.pubsub.publish_json(key, msg.dict())
        

    app_log.info(f"after_document_content_save, {sender}: {result}, {str(query)[:100]}")
    document_uuid, document_content, update_type = get_doc_info_from_query(sender, query)
    # todo: 放到atomic之后执行
    run_hook_task(post_save_task(document_uuid, document_content, update_type))


def after_document_delete(sender: Document, app: Sanic, engine: Engine, query, result):
    app_log.info(f"after_document_delete, {sender}: {result}")


@celery.task()
def analyze_diff_daemon(*args, **kwargs):
    asyncio.get_event_loop().run_until_complete(analyze_diff(*args, **kwargs))


async def analyze_diff(app_uuid, document_type, document_uuid, pre_document_content, document_content, engine=None):
    if document_type != DocumentType.MODEL:
        return
    pre_models = pre_document_content.get("models", [])
    post_models = document_content.get("models", [])
    pre_relations = pre_document_content.get("relationships", [])
    post_relations = document_content.get("relationships", [])
    model_diff = deepdiff.DeepDiff(pre_models, post_models,
                                   exclude_regex_paths=r"root\[\d+\]\[(?!'name'\]).*").to_dict()
    model_add = model_diff.get("iterable_item_added", {})
    model_change = model_diff.get("values_changed", {})
    model_remove = model_diff.get("iterable_item_removed", {})
    removed_model_uuids = []
    for path, value in model_remove.items():
        model_uuid = value.get("uuid")
        if model_uuid:
            removed_model_uuids.append(model_uuid)

    model_fields_diff = deepdiff.DeepDiff(pre_models, post_models,
                                          exclude_regex_paths=r"root\[\d+\]\[(?!'fields'\]).*").to_dict()
    model_field_add = model_fields_diff.get("iterable_item_added", {})
    model_field_change = model_fields_diff.get("values_changed", {})
    model_field_remove = model_fields_diff.get("iterable_item_removed", {})

    field_add_uuids = []
    field_remove_uuids = []
    field_change_uuids = []

    field_match = re.compile(r"root\[\d+\]\['fields'\].*")
    for path, value in model_field_add.items():
        if not field_match.match(path):
            continue

        field_uuid = value.get("uuid")
        model = post_models[int(re.search(r"root\[(\d+)\]", path).group(1))]
        model_uuid = model.get("uuid")
        if field_uuid and model_uuid:
            field_add_uuids.append((model_uuid, field_uuid))

    for path, value in model_field_remove.items():
        if not field_match.match(path):
            continue

        field_uuid = value.get("uuid")
        model = pre_models[int(re.search(r"root\[(\d+)\]", path).group(1))]
        model_uuid = model.get("uuid")
        if field_uuid and model_uuid:
            field_remove_uuids.append((model_uuid, field_uuid))

    for path, value in model_field_change.items():
        if not field_match.match(path):
            continue
        match = re.search(r"root\[\d+\]\['fields'\]\[(?P<index>\d+)\]\['(?P<change_key>\w+)'\]", path)
        if match:
            index = int(match.group("index"))
            change_key = match.group("change_key")
            if change_key in ["name", "type"]:
                model = pre_models[int(re.search(r"root\[(\d+)\]", path).group(1))]
                model_uuid = model.get("uuid")
                fields = model.get("fields", [])
                field = fields[index]
                field_uuid = field.get("uuid")
                if field_uuid and model_uuid:
                    field_change_uuids.append((field_uuid, model_uuid))

    relation_diff = deepdiff.DeepDiff(pre_relations, post_relations).to_dict()
    relation_add = relation_diff.get("iterable_item_added", {})
    relation_remove = relation_diff.get("iterable_item_removed", {})
    relation_change = relation_diff.get("values_changed", {})

    relation_add_uuids = []
    relation_remove_uuids = []
    relation_change_uuids = []

    for path, value in relation_add.items():
        relation_uuid = value.get("uuid")
        if relation_uuid:
            relation_add_uuids.append(relation_uuid)

    for path, value in relation_remove.items():
        relation_uuid = value.get("uuid")
        if relation_uuid:
            relation_remove_uuids.append(relation_uuid)

    for path, value in relation_change.items():
        relation = post_relations[int(re.search(r"root\[(\d+)\]", path).group(1))]
        relation_uuid = relation.get("uuid")
        if relation_uuid:
            relation_change_uuids.append(relation_uuid)

    engine = getattr(celery.ctx, "engine", engine)

    field_add_references = await asyncio.gather(*(engine.access.list_field_reference_by_field_uuid(uuid[0], app_uuid)
                                                  for uuid in field_add_uuids))
    field_remove_references = await asyncio.gather(
        *(engine.access.list_field_reference_by_field_uuid(uuid[0], app_uuid) for uuid in field_remove_uuids))
    field_change_references = await asyncio.gather(
        *(engine.access.list_field_reference_by_field_uuid(uuid[0], app_uuid) for uuid in field_change_uuids))
    model_remove_references = await asyncio.gather(*(engine.access.list_model_reference_by_model_uuid(uuid, app_uuid)
                                                     for uuid in removed_model_uuids))
    relation_add_references = await asyncio.gather(
        *(engine.access.list_relationship_reference_by_relationship_uuid(uuid, app_uuid)
          for uuid in relation_add_uuids))
    relation_remove_references = await asyncio.gather(
        *(engine.access.list_relationship_reference_by_relationship_uuid(uuid, app_uuid)
          for uuid in relation_remove_uuids))
    relation_change_references = await asyncio.gather(
        *(engine.access.list_relationship_reference_by_relationship_uuid(uuid, app_uuid)
          for uuid in relation_change_uuids))

    message = DocDeltaMessage(doc_type=document_type, doc_uuid=document_uuid, delta_items=[])
    for idx, references in enumerate(model_remove_references):
        if references is None:
            continue
        message.delta_items.append(
            DocItemDelta(delta_type=DeltaItemChangeType.REMOVE,
                         item_type=DeltaItemType.MODEL,
                         item_uuid=removed_model_uuids[idx],
                         references=list(references)))

    for idx, references in enumerate(field_add_references):
        if references is None:
            continue
        message.delta_items.append(
            DocItemDelta(delta_type=DeltaItemChangeType.ADD,
                         item_type=DeltaItemType.FIELD,
                         item_uuid=field_add_uuids[idx][0],
                         references=list(references)))

    for idx, references in enumerate(field_remove_references):
        if references is None:
            continue
        message.delta_items.append(
            DocItemDelta(delta_type=DeltaItemChangeType.REMOVE,
                         item_type=DeltaItemType.FIELD,
                         item_uuid=field_remove_uuids[idx][0],
                         references=list(references)))

    for idx, references in enumerate(field_change_references):
        if references is None:
            continue
        message.delta_items.append(
            DocItemDelta(delta_type=DeltaItemChangeType.UPDATE,
                         item_type=DeltaItemType.FIELD,
                         item_uuid=field_change_uuids[idx][0],
                         references=list(references)))

    for idx, references in enumerate(relation_add_references):
        if references is None:
            continue
        message.delta_items.append(
            DocItemDelta(delta_type=DeltaItemChangeType.ADD,
                         item_type=DeltaItemType.RELATIONSHIP,
                         item_uuid=relation_add_uuids[idx],
                         references=list(references)))

    for idx, references in enumerate(relation_remove_references):
        if references is None:
            continue
        message.delta_items.append(
            DocItemDelta(delta_type=DeltaItemChangeType.REMOVE,
                         item_type=DeltaItemType.RELATIONSHIP,
                         item_uuid=relation_remove_uuids[idx],
                         references=list(references)))

    for idx, references in enumerate(relation_change_references):
        if references is None:
            continue
        message.delta_items.append(
            DocItemDelta(delta_type=DeltaItemChangeType.UPDATE,
                         item_type=DeltaItemType.RELATIONSHIP,
                         item_uuid=relation_change_uuids[idx],
                         references=list(references)))

    if message.delta_items:
        app_log.info(message.json())
        await engine.pubsub.publish(engine.app.config.REDIS_CHANNEL_DOC_DELTA_UPDATE_KEY, message.json())


async def doc_save_task(app: Sanic, engine: Engine, document_uuid, document_content, update_type):
    if not document_content or not document_uuid:
        return
    if update_type == DocUpdateType.INSERT:
        return

    task = DebounceDocTask(engine, document_uuid, analyze_diff.__name__)
    app_log.info(task)
    doc, doc_content = await task.initialize()
    if doc and doc_content:
        if app.config.USE_CELERY:
            task(analyze_diff, doc.app_uuid, doc.document_type, doc.document_uuid, doc_content.document_content,
                document_content)
        else:
            task(analyze_diff, doc.app_uuid, doc.document_type, doc.document_uuid, doc_content.document_content,
                document_content, engine=engine)


def before_document_content_save(sender: DocumentContent, app: Sanic, engine: Engine, query):
    app_log.info(f"before_document_content_save, {sender}: {str(query)[:100]}")
    document_uuid, document_content, update_type = get_doc_info_from_query(sender, query)
    run_hook_task(doc_save_task(app, engine, document_uuid, document_content, update_type))
