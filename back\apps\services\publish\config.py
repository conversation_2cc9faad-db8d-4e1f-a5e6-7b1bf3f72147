import base64
import os
from enum import Enum

module_relative_path = "/".join(__name__.split(".")[:-1])
project_base_path = os.path.abspath(__file__).rstrip(
    "/".join(__name__.split(".")) + ".py")

TEMPLATE_PATH = "/".join([project_base_path,
                         "templates", module_relative_path])


PEEWEE_ALIAS = "_" + base64.b64encode(b"peewee").decode().rstrip("=")
PEEWEE_EXT_ALIAS = "_" + base64.b64encode(b"peewee_ext").decode().rstrip("=")
ENUM_ALIAS = "_" + base64.b64encode(b"enum").decode().rstrip("=")
HYBRID_ALIAS = "_" + base64.b64encode(b"hybrid_property").decode().rstrip("=")

COPY_LIST = [
    ["app.conf", "/etc/supervisor/conf.d/app.conf"],
    ["supervisor_web.conf", "/etc/supervisor/conf.d/web.conf"],
    ["supervisor_applet.conf", "/etc/supervisor/conf.d/applet.conf"],
    ["agent.conf", "/etc/supervisor/conf.d/agent.conf"],
    ["mobile", "/etc/nginx/sites-enabled/mobile"],
    ["pc", "/etc/nginx/sites-enabled/pc"],
    ["web", "/etc/nginx/sites-enabled/web"],
    ["app", "/etc/nginx/sites-enabled/app"],
    ["nginx_upstream", "/etc/nginx/sites-enabled/nginx_upstream"],
    ["runtime_static/mobile_index.html",
        "/var/www/html/static/runtime_static/mobile/runtime/index.html"],
    ["runtime_static/pc_index.html",
        "/var/www/html/static/runtime_static/pc/runtime/index.html"],
    ["runtime_static/web_index.html",
        "/var/www/html/static/runtime_static/web/runtime/index.html"],
    ["runtime_static/app_index.html",
        "/var/www/html/static/runtime_static/app/runtime/index.html"],
    ["runtime_static/mobile_remote.js",
        "/var/www/html/static/runtime_static/mobile/runtime/remoteEntry.js"],
    ["runtime_static/pc_remote.js",
        "/var/www/html/static/runtime_static/pc/runtime/remoteEntry.js"],
    ["runtime_static/web_remote.js",
        "/var/www/html/static/runtime_static/web/runtime/remoteEntry.js"],
    ["pc_style.css", "/var/www/html/static/runtime_static/web/runtime/page_style.css"],
    ["pc_style.css", "/var/www/html/static/runtime_static/pc/runtime/page_style.css"],
    ["module_theme_pc_style.css",
        "/var/www/html/static/runtime_static/web/runtime/module_theme_pc_style.css"],
    ["module_theme_pc_style.css",
        "/var/www/html/static/runtime_static/pc/runtime/module_theme_pc_style.css"],
    ["module_theme_mobile_style.css",
        "/var/www/html/static/runtime_static/app/runtime/module_theme_mobile_style.css"],
    ["module_theme_mobile_style.css",
     "/var/www/html/static/runtime_static/mobile/runtime/module_theme_mobile_style.css"],
    ["mobile_style.css", "/var/www/html/static/runtime_static/app/runtime/page_style.css"],
    ["mobile_style.css", "/var/www/html/static/runtime_static/mobile/runtime/page_style.css"],
    ["back", "/root/back"],
    ["custom_public", "/root/custom_public"]
]

CMD_LIST = ["ln -s /root/back_resource/packages/fonts /root/back/packages/fonts",
            "rm -rf /var/www/html/static/runtime_static/mobile/runtime/custom_public/customIcon* || echo 'no such file'",
            "cp -r /root/custom_public/* /var/www/html/static/runtime_static/mobile/runtime/custom_public/",
            "rm -rf /var/www/html/static/runtime_static/web/runtime/custom_public/customIcon* || echo 'no such file'",
            "cp -r /root/custom_public/* /var/www/html/static/runtime_static/web/runtime/custom_public/",
            "rm -rf /var/www/html/static/runtime_static/app/runtime/custom_public/customIcon* || echo 'no such file'",
            "cp -r /root/custom_public/* /var/www/html/static/runtime_static/app/runtime/custom_public/",
            "rm -rf /var/www/html/static/runtime_static/pc/runtime/custom_public/customIcon* || echo 'no such file'",
            "cp -r /root/custom_public/* /var/www/html/static/runtime_static/pc/runtime/custom_public/"]


class DeployStatus(Enum):
    DEPLOY = 0  # 部署中
    STOP = 1
    START = 2   # 正常运行
    RESTART = 3
    INIT = 4    # 启动中
    ERROR = 5  # 重启多次无效
    DEBUG = 6


class DevelopmentConfig:
    supervisor_conf_path = os.getenv("HOME", "/root") + "/runtime_apps"
    runtime_app_path = os.getenv("HOME", "/root") + "/runtime_apps"
    runtime_front_path = os.getenv("HOME", "/root") + "/runtime_apps"
    nginx_conf_path = os.getenv("HOME", "/root") + "/runtime_apps"
    domain_name = "ythoo.com"
    static_domain = ""
    runtime_ingress = ""
    harbor_domain = "harbor.lemonstudio.tech"
    runtime_image = ""
    image_version = "v2"
    publish_namespace = "design"
    ingress_domain = ""
    domain_name_d = ""
    domain_name_app = ""
    ext_json_path = os.getenv("HOME", "/root") + "/lemon/client/lemomWechat/ext.json"
    m_domain_name = "mythoo.com"

class TestingConfig:
    supervisor_conf_path = "/etc/supervisor/conf.d"
    runtime_app_path = "/root/runtime_apps"
    runtime_front_path = "/var/www/html"
    nginx_conf_path = "/etc/nginx/sites-available"
    domain_name = "tlemon.lemonstudio.tech"
    static_domain = domain_name
    domain_name_d = "tlemon-d.lemonstudio.tech"
    domain_name_app = "tlemon-app.lemonstudio.tech"
    ingress_domain = "runtime.lemonstudio.tech"
    runtime_ingress = f"http://{ingress_domain}:30280"
    harbor_domain = "harbor.lemonstudio.tech"
    runtime_image = f"{harbor_domain}/design-dev/lemon-runtime"
    deploy_image = f"{harbor_domain}/deploy-dev"
    image_version = "v2"
    publish_namespace = "design"
    ext_json_path = os.getenv("HOME", "/root") + "/lemon/client/lemomWechat/ext.json"
    m_domain_name = "mtlemon.lemonstudio.tech"


class LastTestingConfig(TestingConfig):
    runtime_image = f"{TestingConfig.harbor_domain}/design-dev/last-lemon-runtime"
    static_domain = "tlegacy.lemonstudio.tech"


class PreproductionConfig:
    supervisor_conf_path = "/etc/supervisor/conf.d"
    runtime_app_path = "/root/runtime_apps"
    runtime_front_path = "/var/www/html"
    nginx_conf_path = "/etc/nginx/sites-available"
    domain_name = "plemon.lemonstudio.tech"
    domain_name_d = "plemon-d.lemonstudio.tech"
    domain_name_app = "plemon-app.lemonstudio.tech"
    ingress_domain = "preruntime.lemonstudio.tech"
    runtime_ingress = f"http://{ingress_domain}:30280"
    harbor_domain = "harbor.lemonstudio.tech"
    runtime_image = f"{harbor_domain}/design-prepro/lemon-runtime"
    deploy_image = f"{harbor_domain}/deploy-prepro"
    image_version = "v2"
    publish_namespace = "predesign"
    static_domain = domain_name
    ext_json_path = os.getenv("HOME", "/root") + "/lemon/client/lemomWechat/ext.json"
    m_domain_name = "mplemon.lemonstudio.tech"


class LastPreproductionConfig(PreproductionConfig):
    runtime_image = f"{PreproductionConfig.harbor_domain}/design-prepro/last-lemon-runtime"
    static_domain = "plegacy.lemonstudio.tech"


class ProductionConfig:
    supervisor_conf_path = "/etc/supervisor/conf.d"
    runtime_app_path = "/root/runtime_apps"
    runtime_front_path = "/var/www/html"
    nginx_conf_path = "/etc/nginx/sites-available"
    domain_name = "lemon.lemonstudio.tech"
    domain_name_d = "lemon-d.lemonstudio.tech"
    domain_name_app = "lemon-app.lemonstudio.tech"
    # domain_name = "lemon.sweetlemon.io"
    ingress_domain = "runtime.lemonstudio.tech"
    runtime_ingress = f"http://{ingress_domain}:30280"
    harbor_domain = "harbor.lemonstudio.tech"
    runtime_image = f"{harbor_domain}/design/lemon-runtime-pro"
    deploy_image = f"{harbor_domain}/deploy-pro"
    image_version = "v2"
    publish_namespace = "design"
    static_domain = domain_name
    ext_json_path = os.getenv("HOME", "/root") + "/lemon/client/lemomWechat/ext.json"
    m_domain_name = f"m{domain_name}"


class LastProductionConfig(ProductionConfig):
    runtime_image = f"{ProductionConfig.harbor_domain}/design/last-lemon-runtime-pro"
    static_domain = "legacy.lemonstudio.tech"
