# -*- coding:utf-8 -*-

import json
import copy
import asyncio
import time
import uuid
from functools import partial
import traceback
import weakref
from typing import Optional, Type, Dict, Union, TypeVar, Tuple
import gc
import sys

import sanic.websocket
import ujson
import peewee
import websockets
from functools import wraps
import yagmail
from contextlib import asynccontextmanager, suppress
from dateutil.relativedelta import relativedelta
from pydantic import ValidationError, BaseModel

from baseutils.log import app_log
from baseutils.tracing import TracingContext
from baseutils.utils import LemonContextVar, CVE, make_tenant_sys_table_copy
from baseutils.const import EventWhen
from apps.entity import DocumentContent
from apps.utils import lemon_uuid, LemonRuntimeDictResponse as LDR, Singleton
from apps.ide_const import (
    ComponentType, DeviceType, DocumentType, NodeType, PageEventType, SearchSaveActionKey)
from apps.base_utils import (
    GlobalVars, POPUP_SUBMIT_QUEUE_DICT
)
from apps.exceptions import (
    <PERSON>upS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r, Lemon<PERSON><PERSON>back<PERSON>rror, RuntimeComponentCallbackNotFound
)
from apps.watermark.utils import WatermarkType
from runtime.metrics.business_metrics import RuntimeMetrics
from runtime.middleware import create_current_user
from runtime.engine import engine
from runtime.protocol.client_rpc import ClientRPCResponse, ClientRPCRequest, ClientRPCCommand, ClientRPCStatus
from runtime.utils import (
    LemonMessage, NumpyEncoder,
    find_real_node_dict, WeChatClientSingleton
)
from runtime.const import LemonRuntimeErrorCode as LREC, RedisChannel, WxChannel
from apps.user_entity import WorkflowInstance
from runtime.core.utils import build_topic, MessageBox, send_component_result_msg
from apps.base_entity import CurrentUserModel
from runtime.core.sm import RootLemonStateMachine
from runtime.core.connector import ClientConnector, ClientABC
from runtime.component.data_adapter import WorkflowAdapter
from runtime.component.page import Page
from tests.document.sm_root import sm_root
from runtime.component.connector import LemonConnector
from runtime.component.restful import Restful
from runtime.log import runtime_log
from aiohttp import ClientSession, hdrs
from apps.exceptions import FuncExecError
from contextlib import AsyncExitStack

TIMEOUT = 10
INTERVAL = 60
WAIT_CLOSE_TIMEOUT = 28800


class Client(ClientABC):

    def __init__(self, sid):
        self.is_closed = False
        self.sid = lemon_uuid() if sid is None or sid == "null" else sid
        self.loop = asyncio.get_event_loop()
        self.wait_close = None
        self.connection_close_callbacks = list()
        self.connector = ClientConnector(self.sid)
        self.connector.client = self
        self.client_rpc_write_queue = asyncio.Queue()
        self.client_rpc_response_future_mapping: Dict[str, asyncio.Future] = {}  # Dict[rid, Future]

    def call_on_close(self):
        app_log.info(f"call_on_close")
        asyncio.create_task(self.close())

    async def clean_popup_submit_queue(self):
        for queue in POPUP_SUBMIT_QUEUE_DICT.get(self.sid, {}).values():
            if queue:
                await self.submit_queue(queue, Exception)
        if self.sid in POPUP_SUBMIT_QUEUE_DICT:
            del POPUP_SUBMIT_QUEUE_DICT[self.sid]

    async def add_wait_close(self):
        app_log.info(f"self.wait_close: {self.wait_close}")
        await self.clean_popup_submit_queue()
        await self.connector.clean()
        if self.wait_close is None:
            self.wait_close = self.loop.call_later(
                WAIT_CLOSE_TIMEOUT, self.call_on_close)

    def cancel_wait_close(self):
        app_log.info(f"self.wait_close: {self.wait_close}")
        if self.wait_close is not None:
            self.wait_close.cancel()
            self.wait_close = None

    def connection_close(self):
        for func in self.connection_close_callbacks:
            func()
        self.connection_close_callbacks = list()

    async def send_error_result(self, *args, **kwargs):
        pass

    async def on_result(self):
        pass

    async def close(self) -> None:
        # if isinstance(self.connector, ClientConnector):
        #     LemonContextVar.current_connector.set(self.connector)
        #     await self.connector.close()
        #     self.connector = None
        #     LemonContextVar.current_connector.set(None)
        # 复用 连接
        engine.clients.delete(self.sid)

    async def do_send_messages(self, is_deliver=False):
        await self.connector.send_messages.put(None)
        values = {}
        message_summary = {
            "rid": "",
            "data": values,
            "is_summary": True,
            "is_deliver": is_deliver
        }
        while True:
            result = await self.connector.send_messages.get()
            if result is None:
                break
            try:
                need_summary = self.calc_need_summary(result)
                if need_summary:
                    self.do_summary(result, message_summary)
                else:
                    await self.connector.call(result)
            finally:
                self.connector.send_messages.task_done()
        try:
            await self.summary_call(message_summary)
        except (Exception, BaseException):
            app_log.error(traceback.format_exc())
        self.connector.clean_callbacks()
        app_log.info(f"len_send_messages: {len(self.connector.context_send_messages)}")

    async def _handle_message(self, message: LemonMessage):
        command = message.get("command")
        data = message.get("data")
        rid = message.get("rid", TracingContext.trace_id.get() or lemon_uuid())
        component = message.get("component", None)
        rdata = {"rid": rid}
        if component:
            rdata.update({"component": component})
        LemonContextVar.current_rdata.set(rdata)
        app_log.info(f"Command: {command}, Data: {data}")
        start = time.time()
        error_class: Optional[Type[Exception]] = None
        event = ""
        try:
            if command == "component_event":
                event = data.get("event", "")
        except:
            pass

        try:
            LemonContextVar.current_connector.set(self.connector)
            condition = asyncio.Condition()
            LemonContextVar.condition.set(condition)
            root_form_uuid = lemon_uuid()
            app_log.info(f"root_form_uuid: {root_form_uuid}")
            async with engine.userdb.objs.atomic(root_form_uuid):
                await self.run_command(command, data)
                # 云函数的执行过程中，可能会触发一些页面刷新等的操作
                # 这些操作需要在事件执行后，再触发，否则可能会出现事务过早结束
                # 连接、读取数据库失败的问题
                await self.connector.run_callbacks.put(None)  # 添加结束标记
                while True:
                    func_to_run = await self.connector.run_callbacks.get()
                    if func_to_run is None:
                        break
                    try:
                        if callable(func_to_run):
                            await func_to_run()
                    finally:
                        # 云函数使用 lemon.utils.page_refresh 后，会导致不再响应请求，临时注释掉
                        # while not self.connector.run_callbacks.empty():
                        #     # 确保本次 run_callbacks 被清空
                        #     await self.connector.run_callbacks.get()
                        self.connector.run_callbacks.task_done()
        except RuntimeComponentCallbackNotFound:
            app_log.error(traceback.format_exc())
            await self.send_error_result(component, return_code=LREC.WEBSOCKET_CALLBACK_NOT_FOUND)
        except (BaseException, Exception) as e:
            error_class = type(e)
            stdout = traceback.format_exc()
            app_log.error(stdout)
            if not isinstance(e, (LemonRollbackError, peewee.PeeweeException)):
                # 事务回滚的错误 和 peewee 自身的报错，不展示在页面上
                await runtime_log.error(stdout)
                component = data.get("component", "")
                await self.send_error_result(component, stdout=stdout)
        finally:
            async with condition:
                condition.notify_all()
            # 添加结束标记
            await self.do_send_messages()
        # RuntimeMetrics.report_ws_request(command, event, error_class, time.time() - start)
        app_log.info(f"Command: {command}, Done.")

    async def summary_call(self, message_summary):
        if message_summary.get("topic"):
            await self.connector.call(message_summary)

    async def handle_client_rpc_response(self, message: LemonMessage):
        rid = message.get("rid")
        future = self.client_rpc_response_future_mapping.get(rid)
        if future is None:
            app_log.error("[client rpc] future not found for rid: %s", rid)
            return

        try:
            res: ClientRPCResponse = ClientRPCResponse.parse_obj(message)
        except ValidationError as e:
            app_log.exception("[client rpc] validation error: %s", e)
            future.set_exception(e)
            return
        except Exception as e:
            app_log.exception("[client rpc] unhandled exception: %s", e)
            future.set_exception(e)
            return

        future.set_result(res)

    async def send_raw(self, msg: Union[bytes, str]):
        app_log.info("[NOOP send_raw] msg: %s", msg)
        raise NotImplementedError()

    async def send_client_rpc_request(self, request: ClientRPCRequest, need_result: bool = True) -> ClientRPCResponse:
        frozen_rid = request.rid = request.rid or uuid.uuid4()

        future = asyncio.get_event_loop().create_future()
        if need_result:
            self.client_rpc_response_future_mapping[frozen_rid] = future

        await self.send_raw(request.json())
        if not need_result:
            future.set_result(ClientRPCResponse(rid=frozen_rid, code=ClientRPCStatus.OK, message="", data={}))
        try:
            return await asyncio.wait_for(future, timeout=10)
        except asyncio.TimeoutError:
            app_log.info("[client rpc] timeout")
            raise
        finally:
            if not future.done():
                future.cancel()
            with suppress(KeyError):
                del self.client_rpc_response_future_mapping[frozen_rid]

    async def handle_message(self, message: LemonMessage):
        command = message.get("command", "")
        lock = None
        if command.startswith("response"):
            return await self.handle_client_rpc_response(message)
        if command not in ["popup_submit", "deliver"]:
            # popup_submit 弹窗交互，可能出现在某个事件的执行过程中
            # 此时，队列的 get 操作会阻塞，无法继续

            # 使用队列的原因：
            # ws 消息是异步接收的，但处理消息时，需要依次处理
            # 否则可能会出现 数据容器的数据 有依赖的问题
            lock = self.connector.ready
        if lock is None:
            await self._handle_message(message)
        else:
            async with lock:
                await self._handle_message(message)


class WSClient(Client):

    def __init__(
            self, request, ws, sid=None, token=None, tenant_uuid=None,
            app_uuid=None, device_type=DeviceType.PC, session_id=None,
            preview=False):
        super().__init__(sid)
        self.request = request
        self.ws: sanic.websocket.WebSocketConnection = ws
        self.token = token
        self.tenant_uuid = tenant_uuid
        self.app_uuid = app_uuid
        self.device_type = device_type
        self.root_component = None
        self.root_lsm = None
        self.root_context = dict()
        self.open = False
        self.session_id = session_id
        self.watermark_text = ""
        self.preview = preview

    def __hash__(self):
        return hash(str(self.sid))

    def init_params(self, **kwargs):
        [setattr(self, k, v) for k, v in kwargs.items()]

    async def send_raw(self, msg: Union[bytes, str]):
        app_log.info("[WSClient send_raw] msg_length: %s", len(msg))
        return await self.ws.send(msg)

    async def send_msg(self, msg: dict):
        rdata = LemonContextVar.current_rdata.get()
        if rdata:
            msg.update(rdata)
        await self.ws.send(json.dumps(msg, cls=NumpyEncoder))

    async def wx_handle_message(self, message: LemonMessage):
        command = message.get("command")
        lock = None
        if command != "popup_submit":
            # popup_submit 弹窗交互，可能出现在某个事件的执行过程中
            # 此时，队列的 get 操作会阻塞，无法继续

            # 使用队列的原因：
            # ws 消息是异步接收的，但处理消息时，需要依次处理
            # 否则可能会出现 数据容器的数据 有依赖的问题
            lock = self.connector.ready
        if lock is None:
            await self.send_msg(message)
        else:
            async with lock:
                await self.send_msg(message)

    async def on_result(self, msg: LemonMessage):
        await self.send_msg(msg)

    async def send_error_result(self, component="", stdout="", stderr="", return_code=None):
        return_code = return_code or LREC.WEBSOCKET_COMPONENT_RESULT_ERROR
        msg = self.build_error_result(component, stdout, stderr, return_code)
        await self.send_msg(msg)

    async def build_page_context(self, data_context=None, page_uuid=None):
        if not isinstance(data_context, dict):
            if isinstance(data_context, int):
                data_context = {"pk": data_context, "pk_list": [data_context]}
            else:
                data_context = dict()
        # 工作流的额外处理
        force_settings = await UtilsFunc.handle_force_setting(
            self, data_context)
        # 事件打开内部链接页面参数：可编辑性
        editable = data_context.get("editable")
        if page_uuid and editable is not None:
            control_settings = force_settings.setdefault("control_settings", {})
            control_settings.update(await UtilsFunc.set_control_editable(page_uuid, editable))
        page_context = copy.deepcopy(self.root_context)
        page_context.update({
            "control_settings": force_settings.get("control_settings", {})})
        workflow_form_pk = force_settings.get("workflow_form_pk", {})
        if workflow_form_pk:
            _, pk = workflow_form_pk.popitem()
            data_context.update({
                "pk": pk,
                "pk_list": [pk]
            })
        context_pk = data_context.get("pk")
        if context_pk:
            pk_list = data_context.get("pk_list", [])
            if not pk_list:
                data_context.update({"pk_list": [context_pk]})
        else:
            # TODO: 临时处理 切换面包屑 前端未发送 exit_page 的问题
            data_context.update({"pk": 0})
            pk_list = data_context.get("pk_list", [])
            if not pk_list:
                data_context.update({"pk_list": []})
        page_context.update(data_context)
        return page_context, force_settings

    async def get_page_content(self, page_uuid, page_content=None):
        current_user = LemonContextVar.current_user.get()
        if page_content is None:
            if (current_user and current_user.use_design) or (page_uuid not in engine.PAGE_CONTENT_DICT) \
                    or engine.config.DIRECT_ACCESS_DESIGN:
                tenant_uuid = self.connector.current_user.tenant_id
                page_document = await engine.access.select_page_content_by_page_uuid(
                    page_uuid, ext_tenant=tenant_uuid)
                if not page_document:
                    return {}
                # 拿到页面下所有的组件
                page_content = page_document.get(
                    DocumentContent.document_content.name)
            else:
                page_content = engine.PAGE_CONTENT_DICT.get(page_uuid)
        if engine.config.DIRECT_ACCESS_DESIGN or (current_user and current_user.use_design):
            return page_content
        engine.PAGE_CONTENT_DICT.update({page_uuid: page_content})
        return page_content

    def create_page_component(
            self, page_content, page_context, data_context, parent=None,
            root_form_uuid=None, form_nested=False, nested=False, depth=0):
        page_content = copy.deepcopy(page_content)
        page = engine.component_creator.create_component(
            component_type=ComponentType.PAGE, component_dict=page_content,
            p_context=page_context, data_context=data_context, parent=parent,
            root_form_uuid=root_form_uuid,
            form_nested=form_nested, nested=nested, depth=depth)
        return page

    async def create_root_page(self):
        pass

    async def get_page_dlist_option(self, page_uuid):
        """获取页面上所有数据列表/树形列表/电子表格的表头操作记忆"""

        tenant_uuid = self.tenant_uuid
        app_uuid = self.app_uuid
        current_user = self.connector.current_user
        user_uuid = current_user.user_uuid

        key_reg = "_".join([tenant_uuid, app_uuid, user_uuid, page_uuid])
        page_dlist_option = dict()

        data = await engine.redis.hgetall_datalist_option(key_reg)
        for full_uuid, value in data.items():
            # 看起来这样的写法是认为最后一个下划线之后的uuid是container_uuid, 但可能不是32位
            last_letter_position = full_uuid.rfind("_")
            container_uuid = full_uuid[last_letter_position + 1:]
            page_dlist_option.update({
                container_uuid: value.get(SearchSaveActionKey.PROPERTY)
            })
        return page_dlist_option

    async def start_or_entry_container(self, page_obj, container, force_settings):
        app_log.info(f"container: {container}, {container.uuid}")
        if container.lsm:
            container.update_p_context(page_obj.p_context)
            await container.entry()
        else:
            if container.component_type == ComponentType.FORM:
                workflow_form_pk: dict = force_settings.get(
                    "workflow_form_pk", {})
                if workflow_form_pk:
                    # TODO 是否有必要检查container是工作流的发起/处理页面中的唯一/顶层表单
                    _, pk = workflow_form_pk.popitem()
                    container.p_context.update({"pk": pk})
                await container.start(inited=True)
            else:
                await container.start()

    def create_page_obj(
            self, page_uuid, page_context, page_content, data_context,
            parent_machine_id=None, page_location=None) -> Page:
        create_from_cache = False
        if parent_machine_id is None:
            # containers = self.connector.get_page_containers()
            # page_obj: Page = self.connector.get_page_container(page_uuid)
            # page_obj: Page = containers.get(page_uuid)
            clear_instances = True
            page_obj: Page = self.connector.get_root_page_container(page_uuid)
            if page_obj:
                page_obj.p_context.update(page_context)
                page_obj.data_context.update(data_context)
                self.connector.root_page = page_obj
                create_from_cache = True
            else:
                parent = self.root_component
                page_obj = self.create_page_component(
                    page_content, page_context, data_context, parent=parent)
        else:
            if self.connector.root_page is None:
                return LDR(LREC.WEBSOCKET_HAS_NO_ROOT_PAGE)
            if page_uuid == self.connector.root_page.uuid:
                return LDR(LREC.WEBSOCKET_ROOT_PAGE_MUST_OPEN_ONCE)
            containers = self.connector.get_page_containers()
            container = containers.get(parent_machine_id)
            parent = container
            page_obj: Page = containers.get(page_uuid)
            # 判断当前页面是否为嵌套页面，并找出嵌套深度
            depth = container.depth  # 嵌套页面的深度；等于父页面的 depth
            nested = True  # 是否嵌套页面；弹出、抽屉页面
            memory_storage = container.memory_storage
            if memory_storage:
                # 父容器为 内存存储 时，不清空内存模型对象
                clear_instances = False
            elif (
                container.component_type == ComponentType.FORM and
                container.nested is False
                # 父容器为 非内存存储 的 页面级独立表单 时，不清空内存模型对象
            ):
                clear_instances = False
            else:
                # 其他 非内存存储 情况，都清空内存模型对象
                clear_instances = True
            if page_obj:
                page_obj.set_parent(parent)
                page_obj.parent_event = parent.parent_event
                page_obj.init_nested_data(nested)
                page_obj.init_p_context(page_context)
                page_obj.init_data_context(data_context)
                page_obj.init_root_form_data(
                    root_form_uuid=None, form_nested=False,
                    depth=depth, memory_storage=memory_storage)
                create_from_cache = True
            else:
                page_obj = self.create_page_component(
                    page_content, page_context, data_context, parent=parent,
                    root_form_uuid=None, form_nested=False,
                    nested=nested, depth=depth)
                app_log.info(f"depth: {depth}, m_storage: {memory_storage}")
        if page_obj and clear_instances:
            # 当用户打开一个 独立页面 或 非内存存储页面 时，清空内存中的模型对象
            page_obj.instances.db.clear()
            page_obj.instances.memory.clear()
        page_obj.page_location = page_location
        return create_from_cache, page_obj

    async def start_page_obj(
            self, page_obj: Page, create_from_cache, force_settings=None):
        LemonContextVar.current_lsm.set(page_obj.lsm)
        if create_from_cache:
            await page_obj.entry()
            event_type = PageEventType.CREATE
            need_throw_exception = engine.config.get("NEED_THROW_EXCEPTION")
            await page_obj._run_page_events(event_type, need_throw_exception=need_throw_exception)
            await page_obj.page_inited()
        else:
            await page_obj.start()
            # 启动页面下的容器组件
        for container in page_obj.container_tree.values():
            await self.start_or_entry_container(page_obj, container, force_settings)

    async def create_page(
            self, page_uuid, page_content=None, data_context=None,
            parent_machine_id=None, page_location=None):
        page_context, force_settings = await self.build_page_context(
            data_context, page_uuid=page_uuid)
        app_log.info(f"page_context: {page_context}")
        page_content = await self.get_page_content(page_uuid, page_content)
        create_from_cache, page_obj = self.create_page_obj(
            page_uuid, page_context, page_content, data_context,
            parent_machine_id, page_location)
        app_log.info(f"c: {create_from_cache}, m_id: {page_obj.machine_id}")
        await self.start_page_obj(
            page_obj, create_from_cache, force_settings)
        page_content.update({
            "page_dlist_option": await self.get_page_dlist_option(page_uuid),
        })
        return page_content

    async def handle_on_open(self, send_msg=True):
        result_str = "on_open"
        try:
            data = LDR(data={
                "sid": self.sid, "watermark": "",
                "watermark_data": self.watermark_text}, result=result_str)
            app_log.info("On open.")
            self.cancel_wait_close()
            LemonContextVar.current_connector.set(self.connector)
            LemonContextVar.tenant_uuid.set(self.tenant_uuid)
            current_user = await create_current_user(
                self.request, self.token, self.tenant_uuid, self.app_uuid,
                self.session_id, device_type=self.device_type, preview=self.preview)
            if not current_user:
                return_code = LREC.WEBSOCKET_TOKEN_NOT_FOUND
                await self.send_msg(LDR(return_code, data={}, result=result_str))
                return

            LemonContextVar.current_user.set(current_user)
            self.connector.current_user = current_user
            app_log.info(f"c_user: {self.connector.current_user.tenant_uuid}")
            await engine.ws_msg_client.add_client(self)
            if not self.open:
                await self.create_root_lsm()
            app_watermark = engine.app.config.APP_WATERMARK
            if app_watermark:
                watermark_type = app_watermark.get("type")
                watermark_show = app_watermark.get("show", False)
                watermark_content = app_watermark.get("content")
                if (
                        watermark_content and watermark_show
                        and
                        watermark_type == WatermarkType.STRING
                        and
                        isinstance(watermark_content, dict)
                ):
                    self.watermark_text = await self.root_component.lemon_editor(
                        watermark_content, to_str=False)
                    try:
                        ujson.dumps(self.watermark_text)
                    except (BaseException, Exception):
                        format_error = traceback.format_exc()
                        # app_log.error(format_error)
                        self.watermark_text = ""
                        await runtime_log.error(format_error)
            # 响应初次连接
            data = LDR(data={
                "sid": self.sid, "watermark": app_watermark,
                "watermark_data": self.watermark_text}, result=result_str)
            if send_msg:
                await self.send_msg(data)
            self.open = True
        except (BaseException, Exception):
            app_log.error(traceback.format_exc())
            return_code = LREC.WEBSOCKET_CONNECT_ERROR
            await self.send_msg(LDR(return_code, dict={}, result=result_str))

    async def create_root_lsm(self):
        self.root_component = engine.component_creator.create_component(
            component_type=ComponentType.ROOT, component_dict=dict(),
            connector=self.connector)
        self.root_component._instances = self.connector.instances
        # 创建 根状态机
        lsm = RootLemonStateMachine(
            component=self.root_component, **sm_root.to_dict())
        self.root_lsm = lsm
        await self.root_lsm.async_start_machine()
        self.root_component.lsm = lsm

    def build_component_event_topic(self, component_uuid, event_uuid):
        key = "event"
        return build_topic(self.sid, component_uuid, key, event_uuid)

    def build_component_command_topic(self, component_uuid):
        key = "command"
        return build_topic(self.sid, component_uuid, key)

    async def _call_on_message(self, msg):
        # 直接调用状态机上的回调处理
        # app_log.info(f"call_msg: {msg}")
        await self.connector.call(msg)

    async def submit_queue(self, queue, value):
        func = partial(queue.put, value)
        executor = CVE
        await self.loop.run_in_executor(executor, func)

    async def handle_popup_submit(self, command, data):
        uuid = data.get("uuid")
        value = data.get("value")
        close = data.get("close")
        request_id = data.get("request_id")
        data = {"request_id": request_id}
        queue = POPUP_SUBMIT_QUEUE_DICT.get(self.sid, dict()).get(uuid)
        if queue:
            if close is True:
                # 用户点击 X 关闭交互弹窗时，赋值为一个特殊的异常
                value = PopupSubmitCloseError()
            await self.submit_queue(queue, value)
            return_code = LREC.OK
        else:
            return_code = LREC.WEBSOCKET_POPUP_SUBMIT_ERROR
        result = LDR(return_code, data=data)
        await self.send_msg(result)

    async def handle_component_event(self, command, data):
        component_uuid = data.get("component")
        event_uuid = data.get("event")
        params = data.get("params")
        if isinstance(component_uuid, str) and component_uuid:
            topic = self.build_component_event_topic(
                component_uuid, event_uuid)
            app_log.info(f"event_topic: {topic}")
            msg = LemonMessage(
                data={"event_args": params, "command": command}, topic=topic)
            await self._call_on_message(msg)

    async def handle_component_command(self, command, data):
        component_uuid = data.get("component")
        command_name = data.get("command") or data.get("event")
        request_id = data.get("request_id")
        params = data.get("params")
        index = data.get("index", 0)
        handle = data.get("handle", "click")
        if isinstance(component_uuid, str) and component_uuid:
            topic = self.build_component_command_topic(component_uuid)
            app_log.info(f"command_topic: {topic}")
            msg = LemonMessage(
                data={"event_args": params}, command=command_name,
                request_id=request_id, topic=topic, index=index,
                handle=handle)
            await self._call_on_message(msg)

    async def handle_create_page(self, command, data):
        page_uuid = data.get("page")
        page_content = data.get("page_content")
        data_context = data.get("context", {})
        parent_machine_id = data.get("parent_machine_id", None)
        page_location = data.get("location", {})
        return_code = LREC.OK
        page_dict = dict()
        try:
            page_dict = await self.create_page(
                page_uuid, page_content=page_content,
                data_context=data_context,
                parent_machine_id=parent_machine_id,
                page_location=page_location)
        except FuncExecError as e:
            need_throw_exception = engine.config.get("NEED_THROW_EXCEPTION")
            if need_throw_exception:
                message, stdout = e.message, e.stdout
                send_data = MessageBox.gen_message("error", stdout, message)
                await send_component_result_msg(
                    self.sid, page_uuid, "message_box", send_data)
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            try:
                page_obj = self.connector.find_page_container(page_uuid)
                app_log.info(f"error, p_uuid: {page_uuid}, p_obj: {page_obj}")
                self.connector.delete_page_container(page_uuid)
                self.connector.delete_root_page_container(page_uuid)
                await self.exit_page(page_obj)
            except Exception:
                app_log.info("exit error")
                format_error = traceback.format_exc()
                app_log.error(format_error)
                await runtime_log.error(format_error)
            return_code = LREC.WEBSOCKET_CREATE_PAGE_ERROR
        result = LDR(return_code, data=page_dict, result="create_page")
        # await self.send_msg(result)
        command_key = "result"
        result["topic"] = build_topic(self.sid, page_uuid, command_key)
        await self.connector.send_messages.put(result)

    async def exit_page(self, page_obj=None):
        # 这里前端有时会发两次同一 page 的 exit
        # 第二次 exit 时 page_obj 为 None ， 会导致 根页面退出
        # page_obj = page_obj or self.connector.root_page
        if isinstance(page_obj, Page):
            LemonContextVar.current_lsm.set(page_obj.lsm)
            try:
                event_type = PageEventType.DESTROY
                need_throw_exception = engine.config.get("NEED_THROW_EXCEPTION")
                await page_obj._run_page_events(event_type, need_throw_exception=need_throw_exception)
            except FuncExecError as e:
                if need_throw_exception:
                    message, stdout = e.message, e.stdout
                    send_data = MessageBox.gen_message("error", stdout, message)
                    await send_component_result_msg(
                        self.sid, page_obj.uuid, "message_box", send_data)
            await page_obj.exit()
            LemonContextVar.current_lsm.set(None)

    async def handle_exit_page(self, command, data):
        page_uuid = data.get("page")
        clean_cache = data.get("clean_cache")
        if page_uuid:
            page_obj = self.connector.find_page_container(page_uuid)
            app_log.info(f"exit page, p_uuid: {page_uuid}, p_obj: {page_obj}")
            await self.exit_page(page_obj)
            if clean_cache:
                self.connector.delete_page_container(page_uuid=page_uuid)
                self.connector.delete_root_page_container(page_uuid)

    async def close(self):
        await engine.ws_msg_client.remove_client(self)
        await super().close()
        # root_page = self.connector.root_page
        # connector = self.connector
        # root_component = self.root_component
        # root_lsm = self.root_lsm
        self.connector.callbacks.clear()
        self.root_component.component_tree.clear()
        self.root_component.lsm = None
        self.connector = None
        self.root_component = None
        self.root_lsm = None
        # gc.collect()
        # app_log.info(f"sys.getrefcount(connector): {sys.getrefcount(connector)}")
        # app_log.info(f"sys.getrefcount(root_component): {sys.getrefcount(root_component)}")
        # app_log.info(f"sys.getrefcount(root_lsm): {sys.getrefcount(root_lsm)}")
        # app_log.info(f"sys.getrefcount(client): {sys.getrefcount(self)}")
        # import objgraph
        # objgraph.show_refs([root_page])
        # objgraph.show_backrefs([root_page])
        # if root_page is not None:
        #     app_log.info(f"sys.getrefcount(root_page): {sys.getrefcount(root_page)}")
        #     refers = gc.get_referrers(root_page)
        #     for ref in refers:
        #         app_log.info(f"ref root_page: {ref}")
        # refers = gc.get_referrers(connector)
        # for ref in refers:
        #     app_log.info(f"ref connector: {ref}")
        # refers = gc.get_referrers(root_component)
        # for ref in refers:
        #     app_log.info(f"ref root_component: {ref}")
        # refers = gc.get_referrers(root_lsm)
        # for ref in refers:
        #     app_log.info(f"ref root_lsm: {ref}")
        # refers = gc.get_referrers(self)
        # for ref in refers:
        #     app_log.info(f"ref client: {ref}")
        # app_log.info(f"sys.getrefcount(connector): {sys.getrefcount(connector)}")
        # app_log.info(f"sys.getrefcount(root_component): {sys.getrefcount(root_component)}")
        # app_log.info(f"sys.getrefcount(root_lsm): {sys.getrefcount(root_lsm)}")
        # app_log.info(f"sys.getrefcount(client): {sys.getrefcount(self)}")
        # if root_page is not None:
        #     app_log.info(f"sys.getrefcount(root_page): {sys.getrefcount(root_page)}")
        # gc.collect()

    def clean(self):
        self.request = None
        self.ws = None

    async def receiver(self) -> None:
        try:
            async with engine.userdb.objs.atomic():
                await self.handle_on_open()
            async for message in self.ws:
                if message == '2':
                    await self.pong()
                    continue
                try:
                    # 要让每个消息对应的处理 task 是独立的
                    # 使用 popup_submit 交互弹窗时，会阻塞当前线程
                    LemonContextVar.current_user.set(self.connector.current_user)
                    message = ujson.loads(message)
                    TracingContext.set_trace_id(message.get("rid"))
                    asyncio.create_task(self.handle_message(message))
                except (BaseException, Exception):
                    # 不能影响 其他事件的接收
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    _ = asyncio.create_task(runtime_log.error(format_error))
        except websockets.exceptions.ConnectionClosed:
            app_log.error("connection closed")
            try:
                await self.add_wait_close()
            except Exception:
                format_error = traceback.format_exc()
                app_log.error(format_error)
                await runtime_log.error(format_error)
        except Exception:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
        finally:
            pass
            # await self.feed.unregister(self)

    async def pong(self):
        await self.ws.send('2')

    async def keep_alive(self) -> None:
        receiver_task = asyncio.create_task(self.receiver())
        await asyncio.wait(
            {receiver_task}, timeout=0, return_when=asyncio.ALL_COMPLETED)
        while True:
            try:
                try:
                    pong_waiter = await self.ws.ping()
                    result = await pong_waiter
                    app_log.info(f"send ping. {result}")
                except asyncio.TimeoutError:
                    app_log.error("NO PONG!!")
                    # await self.feed.unregister(self)
                    break
                except BaseException:
                    app_log.error("PONG EXCEPTION!!")
                    app_log.error(traceback.format_exc())
                    break
                else:
                    app_log.info(f"ping: {self.sid}")
                    await asyncio.sleep(INTERVAL)
            except websockets.exceptions.ConnectionClosed:
                app_log.info(f"broken connection: {self.sid}")
                try:
                    await self.add_wait_close()
                    # await self.feed.unregister(self)
                except Exception:
                    app_log.info(traceback.format_exc())
                    break
                else:
                    break
            except BaseException:
                # asyncio.exceptions.CancelledError 继承的 BaseException
                app_log.error(traceback.format_exc())
                try:
                    await self.add_wait_close()
                    # await self.feed.unregister(self)
                except Exception:
                    app_log.info(traceback.format_exc())
                    break
                else:
                    break
        receiver_task.cancel()


class LemonConnectorManager:
    def __init__(self, app, connector_config_list):
        self._app = app
        self.connector_config_list = connector_config_list
        self.ct_instance_dict = {}

    async def prepare(self):
        try:
            connectors = {}
            connectors_tenants_config = {}
            for connector in self.connector_config_list:
                print(connector["document_uuid"])
                connector_uuid = connector["connector_uuid"]
                connector_content = await engine.access.get_connector_content_by_connector_uuid(connector_uuid)
                if not connector_content:
                    continue
                connector_content = connector_content["document_content"] or {}
                connectors.update({connector_uuid: connector_content})
                connector_tenant_config = await engine.access.get_document_content_by_document_uuid(
                    connector["document_uuid"])
                if not connector_tenant_config:
                    return

                connector_tenant_config = connector_tenant_config.document_content or {}
                if connector_uuid not in connectors_tenants_config:
                    connectors_tenants_config[connector_uuid] = dict()
                connectors_tenants_config[connector_uuid].update(
                    {connector["tenant_uuid"]: connector_tenant_config})

            for connector_uuid in connectors:
                connector_content = connectors[connector_uuid]
                connector_tenants_config = connectors_tenants_config.get(
                    connector_uuid)
                if not connector_tenants_config:
                    # 租户没有配置该连接器，表示不开启该连接器
                    continue
                ct_instance = LemonConnector(
                    self._app.config.APP_UUID, connector_content,
                    connector_tenants_config, loop=self._app.loop)
                self.ct_instance_dict.update({ct_instance.uuid: ct_instance})
            # app_config_ = self.app_config
            # ct_uuid = app_config_.get("ct_uuid")
            # ct_define = {}
            # if app_config_.get("ct_type") == 0:
            #     ct_define = sys_connector_define.get(ct_uuid)
            # elif app_config_.get("ct_type") == 2:
            #     ct_define_doc = await engine.access.get_document_content_by_document_uuid(ct_uuid)
            #     if ct_define_doc:
            #         ct_define = ct_define_doc.document_content or {}
            # # tenants_config = dict(filter(lambda y: y[1], map(lambda x: (x, self.tenants_config[x].get(ct_uuid)), self.tenants_config)))
            # app_log.debug(self.tenants_config)
            # ct_instance = LemonConnector(self._app.config.APP_UUID, ct_define, app_config_, self.tenants_config, loop=self._app.loop)
            # self.ct_instance_dict.update({ct_instance.uuid: ct_instance})
        except:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)

    async def start(self):
        # for instance in self.ct_instance_dict.values():
        #     self._pool.apply_async(instance.start)
        await asyncio.gather(
            *[instance.start() for instance in self.ct_instance_dict.values()])


class RestfulManager:
    def __init__(self, app, app_uuid) -> None:
        self.app_uuid = app_uuid
        # self.app = Sanic(name="restful")
        self.app = app

    def run_restful(self):
        self.app.run(host="0.0.0.0", port=7001)

    async def start(self):
        restful_list = await engine.access.list_document_content_by_app_uuid_document_type(self.app_uuid,
                                                                                           DocumentType.RESTFUL)
        for restful in restful_list:
            restful_define = restful["document_content"]
            if not restful_define:
                continue
            restful_instance = Restful(self.app, restful_define)
            await restful_instance.start()
            # instance_list.append(restful_instance)
        # Process(target=self.run_restful).start()


class UtilsFunc:
    """处理create_page时的特殊处理"""

    @staticmethod
    async def handle_force_setting(ws_client, data_context):
        result = {}

        wf_instance_uuid = data_context.get("wf_instance_uuid", None)
        if wf_instance_uuid:
            # 判断是否为工作流处理页面，并预设置可见性等信息  control_settings
            processed = data_context.get("processed")
            node_uuid = data_context.get("node_uuid")
            all_visible = data_context.get("all_visible")
            result.update(await UtilsFunc.handle_workflow_pre_setting(
                ws_client, wf_instance_uuid, processed, node_uuid, all_visible))
        return result

    @staticmethod
    async def handle_workflow_pre_setting(
            ws_client, wf_instance_uuid, processed=None, node_uuid=None,
            all_visible=False):
        control_settings = dict()
        workflow_form_pk = dict()
        if wf_instance_uuid:
            async with engine.userdb.objs.atomic():
                model = make_tenant_sys_table_copy(WorkflowInstance)
                fields = [
                    model.wf_dict, model.node_uuid,
                    model.form_pk, model.form_uuid
                ]
                wf_data = await engine.user_access.select_wf_instance_by_wf_instance_uuid(wf_instance_uuid,
                                                                                          fields=fields)
            wf_dict = wf_data.get(WorkflowInstance.wf_dict.name)
            node_uuid = node_uuid or wf_data.get(
                WorkflowInstance.node_uuid.name)
            form_uuid = wf_data.get(WorkflowInstance.form_uuid.name)
            form_pk = wf_data.get(WorkflowInstance.form_pk.name)
            workflow_form_pk = {form_uuid: form_pk}
            workflow_adapter = WorkflowAdapter(workflow_dict=wf_dict)
            all_node_dict = workflow_adapter.all_node_dict
            node_dict = all_node_dict.get(node_uuid, {})
            node_data = node_dict.get("data", dict())
            # 并行节点的话要找到其中实际运行中的节点
            if not node_dict or node_dict.get("type") == NodeType.PARALLEL:
                node_data = dict()
                find_real_node_dict(
                    all_node_dict.values(), node_uuid, node_data)
            form_settings = node_data.get("form_settings", {})
            device_type = DeviceType.DEVICE_TYPE.get(
                ws_client.device_type, "pc")
            device_form_settings = form_settings.get(device_type, list())
            page_uuid = None
            for s_dict in device_form_settings:
                control_uuid = s_dict.get("uuid")
                if control_uuid:
                    visible = s_dict.get("visible", True)
                    editable = s_dict.get("editable", True)
                    if processed:
                        editable = False
                    if all_visible:
                        visible = True
                    control_settings.update({control_uuid: {
                        "visible": visible, "editable": editable}})
            node_type = node_data.get("type")
            if processed or node_type == NodeType.COUNTERSIGN:
                if node_type == NodeType.START:
                    # 使用工作流的发起页面
                    page_uuid = workflow_adapter.get_start_form_page_uuid(
                        ws_client.device_type)
                else:
                    page_uuid = workflow_adapter.get_node_form_page_uuid(
                        ws_client.device_type, node_uuid)
                control_settings.update(await UtilsFunc.set_control_editable(
                    page_uuid, only_outer=True))
        return {
            "control_settings": control_settings,
            "workflow_form_pk": workflow_form_pk
        }

    async def set_control_editable(page_uuid, editable=False, only_outer=False):
        settings = dict()
        page_info = await engine.user_access.select_runtime_page_content_by_page_uuid(page_uuid)
        container_model = page_info.get("container_model", dict())
        for control_uuid, info in container_model.items():
            if info.get("is_outer_container") or not only_outer:
                settings.update({control_uuid: {"editable": editable}})
        return settings


def gen_client(sid):
    if engine.clients:
        client = engine.clients.get(sid)
        if client:
            return client
    client = Client(lemon_uuid())
    root_component = engine.component_creator.create_component(
        component_type=ComponentType.ROOT, component_dict=dict(),
        connector=client.connector)
    lsm = RootLemonStateMachine(component=root_component, **sm_root.to_dict())
    client.root_lsm = lsm
    engine.clients.add_client(client)
    return client


async def get_app_watermark(app_uuid):
    async with engine.userdb.objs.atomic():
        content = await engine.access.get_app_watermark(app_uuid)
    if content is None:
        data = dict()
    else:
        data = list(content)[0].get("document_content")
    return data


class WsMsgClient(object):

    def __init__(self) -> None:
        self.ws_dict = dict()

    async def add_client(self, ws_client: WSClient):
        try:
            current_user = ws_client.connector.current_user
            tenant_uuid = current_user.tenant_uuid
            user_uuid = current_user.user_uuid
            tenant_dict = self.ws_dict.setdefault(tenant_uuid, dict())
            user_ws_set = tenant_dict.setdefault(user_uuid, set())
            user_ws_set.add(ws_client)
        except:
            await runtime_log.error(traceback.format_exc())

    async def remove_client(self, ws_client: WSClient):
        try:
            current_user = ws_client.connector.current_user
            if current_user:
                tenant_uuid = current_user.tenant_uuid
                user_uuid = current_user.user_uuid
                tenant_dict = self.ws_dict.setdefault(tenant_uuid, dict())
                user_set = tenant_dict.setdefault(user_uuid, set())
                user_set.remove(ws_client)
            else:
                app_log.info("ws_client disconnected")
        except:
            await runtime_log.error(traceback.format_exc())

    async def send_message(self, tenant_uuid, user_uuid, msg_type: int, data: dict):
        tenant_dict = self.ws_dict.setdefault(tenant_uuid, dict())
        user_ws_set = tenant_dict.setdefault(user_uuid, set())
        msg = {"msg_type": msg_type, "data": data}
        for ws_client in user_ws_set:
            if not ws_client.ws.closed:
                await ws_client.send_msg(msg)


engine.ws_msg_client = WsMsgClient()


def send_message(channel, to, body, from_info, subject=""):
    if channel == RedisChannel.WECOM:
        return send_wecom(to, body, from_info)
    elif channel == RedisChannel.EMAIL:
        return send_email(to, subject, body, from_info)


async def check_url_request_bak(app_uuid):
    json_body = {"app_uuid": app_uuid}
    env_name = engine.app.config.APP_ENV_NAME
    APP_ENV_NAME = '' if env_name == 'test' else 'test'
    domain_name = "127.0.0.1"
    domain_name_cloud = engine.config.DOMAIN
    app_env = app_uuid + APP_ENV_NAME

    # url = f"http://{domain_name}:8001/" + "api/runtime/v1/check_app_alive.json"
    url = f"https://{domain_name_cloud}:8443/{app_env}/" + "api/runtime/v1/check_app_alive.json"
    try:
        async with ClientSession(trust_env=True) as session:
            app_log.info(url)
            result = await session.post(url, json=json_body)
            result_text = await result.text()
            app_log.info(f"check_url_request_bak result_text{result_text}")
            result_json = await result.json()
            app_log.info(f"check_url_request_bak result_json{result_json}")
            success = result_json.get("success")
            if success:
                return True
    except:
        app_log.error(traceback.format_exc())
        return False


def send_email(to, subject, body, from_info):
    """
    发送邮件
    :param to: 收件人邮箱地址，可以是字符串或列表（多个收件人）
    :param subject: 邮件主题
    :param body: 邮件正文
    :param sender: 发送人信息，字典格式，包括邮箱地址（user）、邮箱密码（password）、
                    SMTP服务器地址（host）
    :param attachments: 附件路径列表，默认为None
    """
    from_user = from_info["user"]
    password = from_info["password"]
    host = from_info["host"]
    with yagmail.SMTP(user=from_user, password=password, host=host) as yag:
        res = yag.send(to=to, subject=subject, contents=body)
    return res  # TODO 要判断是否正确发送


def send_wecom(to, body, corp_info: dict):
    corp_id = corp_info.get("corp_id")
    corp_secret = corp_info.get("corp_secret")
    agent_id = corp_info.get("agent_id")
    wechat_client = WeChatClientSingleton.get_client(corp_id, corp_secret)
    app_log.info(id(wechat_client))
    res = wechat_client.message.send_text(agent_id, [to], content=body)
    return res  # TODO


def send_h5(mesage: dict):
    pass


def send_applet(message: dict):
    pass


@asynccontextmanager
async def current_user_system_ctx(
        app_uuid, tenant_uuid, lsm, roles_uuid=None, module_role=None):
    from runtime.interfaces import process_role_permission
    cu = LemonContextVar.current_user.get()
    if cu:
        app_log.info(cu.user_uuid)

    engine = GlobalVars.engine
    if not roles_uuid:
        # 新建系统管理员, 使用系统管理员角色
        role = await engine.user_access.list_user_role_by_app(
            app_uuid, is_admin=True)
        role = list(role)[0]
        role_info = {role.get("role_uuid"): role.get("role_name")}
        is_sys_admin = True
    else:
        # 使用给定的应用角色权限
        roles_info = await engine.user_access.list_user_role_info(roles_uuid)
        is_sys_admin = False
        role_info = {
            role.get("role_uuid"): role.get("role_name") for role in roles_info
        }
    session_dict = dict()
    tenants = dict()
    system_user_uuid = "system_user_uuid"
    session_dict.update({
        "user_uuid": system_user_uuid,
        "temp_user": True,
        "app_uuid": app_uuid,
        "tenants": tenants
    })
    tenant_info = dict()
    tenant_info.update({
        "is_sys_admin": is_sys_admin,
        "roles": role_info,
        "user_uuid": system_user_uuid,
        "module_role": module_role
    })
    tenants.update({
        tenant_uuid: tenant_info
    })
    await process_role_permission(app_uuid, tenant_uuid, session_dict)
    current_user = await CurrentUserModel.make_instance(tenant_uuid, session_dict)
    LemonContextVar.current_user.set(current_user)
    current_lsm = LemonContextVar.current_lsm.get()
    if current_lsm:
        current_lsm.lemon.system.current_user = current_user
    if lsm:
        lsm.lemon.system.current_user = current_user
        LemonContextVar.current_lsm.set(lsm)
    app_log.info(f"set new current_user: {current_user.user_uuid}, roles: {role_info}")
    try:
        yield current_user
    finally:
        app_log.info(f"end set new current_user")
        if cu:
            app_log.info(f"{current_user.user_uuid}, roles: {role_info}")
        LemonContextVar.current_user.set(cu)
        if current_lsm:
            current_lsm.lemon.system.current_user = cu
        if lsm:
            lsm.lemon.system.current_user = cu


@asynccontextmanager
async def optional_context_manager(manager, condition):
    if condition:
        async with manager:
            yield
    else:
        yield


class UniverInteractive(object):

    def __init__(self) -> None:
        self.univer_dict = dict()

    async def univer_post(
            self, url: str = "", data: dict = {}, access_token: str = "") -> dict:
        async with ClientSession() as session:
            result = await session.post(
                url, data=data, headers={"Authorization": "Bearer "+access_token})
        if result.headers.get(hdrs.CONTENT_TYPE).startswith("application/json"):
            result_text = await result.text()
            json_result = json.loads(result_text)
        else:
            json_result = await result.read()
        app_log.info(f"UniverInteractive univer_post_result={json_result}")
        return json_result

    async def univer_get(
            self, url: str = "", data: dict = {}, access_token: str = "") -> dict:
        async with ClientSession() as session:
            result = await session.get(
                url, data=data, headers={"Authorization": "Bearer "+access_token})
        if result.headers.get(hdrs.CONTENT_TYPE).startswith("application/json"):
            result_text = await result.text()
            json_result = json.loads(result_text)
        else:
            json_result = await result.read()
        app_log.info(f"UniverInteractive univer_get_result={json_result}")
        return json_result


engine.univer_interactive = UniverInteractive()
