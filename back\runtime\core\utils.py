# -*- coding:utf -*-

import re
import ast
import time
import copy
import uuid
import random
import decimal
import traceback
import os
import weakref
import pickle
import asyncio
from functools import partial, reduce
import calendar
import datetime as dt
import ftplib
import contextvars
from datetime import datetime as datetime_sys
from typing import Any, Optional, Dict, Tuple
import requests
from queue import Queue
from collections.abc import Iterator
from confluent_kafka import Consumer
import urllib.parse
from baseutils.helper import OssHelper
import peewee
import ujson
import pytz
import uvloop
import ciso8601
from peewee import fn, NodeList, SQL
import dateutil
import aioredlock
import wechatpy
import pypinyin
import sys
import subprocess
import aliyunsdkcore
import openpyxl
from RestrictedPython import safe_builtins, limited_builtins, utility_builtins
from RestrictedPython.Eval import default_guarded_getitem
from RestrictedPython.Guards import (
    full_write_guard, guarded_iter_unpack_sequence, guarded_unpack_sequence
)
from RestrictedPython import RestrictingNodeTransformer
from RestrictedPython.compile import (
    _compile_restricted_mode, CompileResult, syntax_error_template
)

from baseutils.log import app_log
from baseutils.utils import (
    LemonContextVar, make_tenant_user_table_name, format_exception,
    build_general_prefix, FuncRunError
)
from baseutils.tracing import TracingContext, generate_trace_id
from apps import peewee_ext
from apps.base_utils import (
    join_search_path, make_tenant_sys_table_copy, zip_files,
    get_sys_field_uuid, python_agg_field
)
from apps.utils import (
    base_lemon_field, base_lemon_model, base_lemon_model_node, base_lemon_association,
    build_model_field_path, get_table_meta_class_name, get_sys_model_copy
)
from apps.exceptions import FuncExecError, FuncFieldPermissionError, IsMiddleError
from apps.base_utils import (
    lemon_uuid, GlobalVars, MODEL_COPY_DICT, POPUP_SUBMIT_QUEUE_DICT,
    get_runtime_app_revision, get_runtime_app_uuid
)
from baseutils.const import (
    SystemField, SystemTable, UUID_WorkflowInstance, UUID_NodeInstance, UUID_TenantUser, UUID_TenantDepartment,
    UUID_TenantDepartmentMember, UUID_NodeTask, UUID_WFStore, SystemNamespace)
from runtime.utils import (
    build_field_path, build_join as base_build_join, build_join_info, LemonMessage, gen_sys_model,
    get_tenant_id
)
from apps.ide_const import (
    AggreFunc, AggregationFunc, ComponentType, DataSourceType
)
from runtime.engine import engine
from apps.base_entity import UUID as SYS_ENTITY_UUID
from runtime.modelapi.entity_utils import join_user_table
from apps.user_entity import TenantUser, sys_table_handler
from apps.utils import make_runtime_table_name
from baseutils.utils import sign_oss_url_sync, CVE, copy_current_context
from tests.utils import UUID

SYSTEM_VARIABLE_KEY_LIST = [
    "machine_id", "root_machine_id", "page_machine_id", "parent_machine_id", "schema_id",
    "current_state", "previous_state", "current_action", "previous_action",
    "current_event", "wating_events", "current_transition"
]
EXTEND_VARIABLE_KEY_LIST = ["globals", "locals"]
SM_VARIABLE_KEY_LIST = list()
SM_VARIABLE_KEY_LIST.extend(SYSTEM_VARIABLE_KEY_LIST)
SM_VARIABLE_KEY_LIST.extend(EXTEND_VARIABLE_KEY_LIST)


def exec_vdom(vdom_dict):
    current_lsm = LemonContextVar.current_lsm.get()
    sid = current_lsm.sid
    machine_id = current_lsm.machine_id
    coro = send_component_result_msg(
        sid, machine_id, "script", vdom_dict)
    asyncio.run_coroutine_threadsafe(coro, engine.loop)


def get_main_subordinate_data(tenant_user, current_department, file_name="_owner"):
    current_lsm = LemonContextVar.current_lsm.get()
    model_class = current_lsm.lemon.system.current_obj_class
    # app_log.info(f"model_class: {model_class}")
    sys_user, sys_dep, sys_user_dep = gen_sys_model(tenant_uuid=tenant_user.tenant_uuid)
    # 获取当前当前用户主部门下最大admin
    max_admin = (sys_user_dep.select().where(
        (sys_user_dep.成员 == tenant_user.id) & (sys_user_dep.部门 == current_department.id)).order_by(
            sys_user_dep.is_admin.desc()).first())
    if not max_admin:
        return None
    # 获取所有下属id sys_user_dep.部门用户
    user_id_query = (sys_user_dep.select().where(
                (sys_user_dep.部门 == current_department.id) & (sys_user_dep.is_admin < max_admin.is_admin)))
    user_id_list = [user.成员.id for user in user_id_query]
    res_field = getattr(model_class, file_name, None)
    # 获取所有下属数据
    result = model_class.select().where(res_field.in_(user_id_list))
    app_log.info(f"result: {result}")
    return res_field.in_(user_id_list)

def build_topic(*args):
    t_list = [
        # engine.config.REDIS_CHANNEL_SM,
        # engine.config.MIDDLE_USER_UUID,
    ]
    t_list.extend(list(args))
    return ":".join(t_list)


def build_connector_topic(sid):
    return build_topic(*[sid, "*"])


def build_return_topic(sid, component_uuid, rid):
    return_key = "return"
    return build_topic(sid, component_uuid, rid, return_key)


def build_command_topic(sid, component_uuid):
    command_key = "command"
    return build_topic(sid, component_uuid, command_key)


async def send_topic_msg(topic, msg, with_current_user=True):
    if with_current_user:
        current_user = LemonContextVar.current_user.get()
        if current_user is not None:
            current_user_dict = current_user.as_dict()
            # app_log.info(f"current_user_dict: {current_user_dict}")
            msg.update({"current_user_dict": current_user_dict})
    await engine.pubsub.publish_json(topic, msg)


async def send_command_msg(machine_id, command, data, sid=None):
    command_key = "command"
    command_topic = build_topic(machine_id, command_key)
    command_msg = LemonMessage(command=command, sid=sid, data=data, topic=command_topic)
    app_log.info(f"send command: {command}, topic: {command_topic}")
    await send_topic_msg(command_topic, command_msg)


async def send_component_command_msg(sid, machine_id, command, data):
    command_key = "command"
    command_topic = build_topic(sid, machine_id, command_key)
    command_msg = LemonMessage(command=command, sid=sid, data=data, topic=command_topic)
    app_log.info(f"send command: {command}, topic: {command_topic}")
    await send_topic_msg(command_topic, command_msg)


async def send_component_result_msg(sid, machine_id, result, data):
    command_key = "result"
    command_topic = build_topic(sid, machine_id, command_key)
    command_msg = LemonMessage(result=result, sid=sid, data=data, topic=command_topic)
    app_log.info(f"send result: {result}, topic: {command_topic}")
    client = engine.clients.get(sid)
    app_log.info(f"send sid: {sid}, client: {client}")
    if client:
        await client.connector.call(command_msg)


def get_extension_namespace() -> dict:
    file_name = "/".join(__name__.split(".")) + ".py"
    project_base_path = os.path.abspath(__file__).rstrip(file_name)
    out_base_path = f"{project_base_path}/resources/extension_namespace"
    if os.path.exists(out_base_path):
        with open(out_base_path, "rb") as f:
            data = pickle.loads(f.read())
        return data
    else:
        return set()


loop = asyncio.new_event_loop()

# 此处需要添加上生成的每个应用
ALLOWED_IMPORT = {
    "_strptime",
    "time",
    "decimal",
    "traceback",
    "peewee",
    "resources.erp",
    "itertools",
    "_operator",
    "hashlib",
    "base64",
    "collections",
    "itertools",
    "operator",
    "warnings",
    "unicodedata",
    "smtplib",
    "email",
    "yagmail",
    "kafka3",
    "io",
    "mimetypes",
    "PyPDF2",
    "pdfplumber",
    "docx",
    "tempfile",
    "types",
    "inspect",
    "reactpy",
    "queue",
    "pathlib",
    "os",
    "lemon_excel",
    "openpyxl"
}
# 允许用户使用扩展包
ALLOWED_IMPORT |= get_extension_namespace()
ALLOWED_DATETIME = {
    "now",
    "date",
    "time",
    "datetime",
    "timedelta",
    "fromtimestamp",
    "utcfromtimestamp",
    "tzinfo"}
ALLOWED_TIME = {
    "sleep",
    "strftime",
    "strptime",
    "gmtime",
    "localtime",
    "ctime",
    "time",
    "mktime",
}
ALLOWED_PEEWEE = {
    "fn",
    # "NodeList"
}
DATETIME_RE = re.compile(
    r"(?P<year>\d{4})-(?P<month>\d{1,2})-(?P<day>\d{1,2})"
    r"[T ](?P<hour>\d{1,2}):(?P<minute>\d{1,2})"
    r"(?::(?P<second>\d{1,2})(?:\.(?P<microsecond>\d{1,6})\d{0,6})?)?"
    r"(?P<tzinfo>Z|[+-]\d{2}(?::?\d{2})?)?$"
)


def compile_restricted_async_function(
        p,  # parameters
        body,
        name,
        filename='<string>',
        globalize=None,  # List of globals (e.g. ['here', 'context', ...])
        flags=0,
        dont_inherit=False,
        policy=RestrictingNodeTransformer):
    """改写的async版本，但是policy必须为None
       不然在locals()里就找不到编译的func，暂时不知道原因。。。
    Compile a restricted code object for a function.

    Documentation see:
    http://restrictedpython.readthedocs.io/en/latest/usage/index.html#RestrictedPython.compile_restricted_function
    """
    # Parse the parameters and body, then combine them.
    try:
        body_ast = ast.parse(body, '<func code>', 'exec')
    except SyntaxError as v:
        error = syntax_error_template.format(
            lineno=v.lineno,
            type=v.__class__.__name__,
            msg=v.msg,
            statement=v.text.strip())
        return CompileResult(
            code=None, errors=(error,), warnings=(), used_names=())

    # The compiled code is actually executed inside a function
    # (that is called when the code is called) so reading and assigning to a
    # global variable like this`printed += 'foo'` would throw an
    # UnboundLocalError.
    # We don't want the user to need to understand this.
    if globalize:
        body_ast.body.insert(0, ast.Global(globalize))
    wrapper_ast = ast.parse('async def masked_function_name(%s): pass' % p,
                            '<func wrapper>', 'exec')
    # In case the name you chose for your generated function is not a
    # valid python identifier we set it after the fact
    function_ast = wrapper_ast.body[0]
    assert isinstance(function_ast, ast.AsyncFunctionDef)
    function_ast.name = name

    wrapper_ast.body[0].body = body_ast.body
    wrapper_ast = ast.fix_missing_locations(wrapper_ast)

    result = _compile_restricted_mode(
        wrapper_ast,
        filename=filename,
        mode='exec',
        flags=flags,
        dont_inherit=dont_inherit,
        policy=policy)

    return result


class TimeWrapper:
    """Wrap the time module."""

    # Class variable, only going to warn once per Home Assistant run
    warned = False

    # pylint: disable=no-self-use
    def sleep(self, *args, **kwargs):
        """Sleep method that warns once."""
        if not TimeWrapper.warned:
            TimeWrapper.warned = True
        time.sleep(*args, **kwargs)

    def __getattr__(self, attr):
        """Fetch an attribute from Time module."""
        attribute = getattr(time, attr)
        if callable(attribute):
            def wrapper(*args, **kw):
                """Wrap to return callable method if callable."""
                return attribute(*args, **kw)
            return wrapper
        return attribute


lemon_time = TimeWrapper()


def parse_datetime(dt_str: str) -> Optional[dt.datetime]:
    """Parse a string and return a datetime.datetime.
    This function supports time zone offsets. When the input contains one,
    the output uses a timezone with a fixed offset from UTC.
    Raises ValueError if the input is well formatted but not a valid datetime.
    Returns None if the input isn't well formatted.
    """
    try:
        return ciso8601.parse_datetime(dt_str)
    except (ValueError, IndexError):
        pass
    match = DATETIME_RE.match(dt_str)
    if not match:
        return None
    kws: Dict[str, Any] = match.groupdict()
    if kws["microsecond"]:
        kws["microsecond"] = kws["microsecond"].ljust(6, "0")
    tzinfo_str = kws.pop("tzinfo")

    tzinfo: Optional[dt.tzinfo] = None
    if tzinfo_str == "Z":
        tzinfo = pytz.utc
    elif tzinfo_str is not None:
        offset_mins = int(tzinfo_str[-2:]) if len(tzinfo_str) > 3 else 0
        offset_hours = int(tzinfo_str[1:3])
        offset = dt.timedelta(hours=offset_hours, minutes=offset_mins)
        if tzinfo_str[0] == "-":
            offset = -offset
        tzinfo = dt.timezone(offset)
    kws = {k: int(v) for k, v in kws.items() if v is not None}
    kws["tzinfo"] = tzinfo
    return dt.datetime(**kws)


def datetime(value: Any) -> datetime_sys:
    """Validate datetime."""
    if isinstance(value, datetime_sys):
        return value
    try:
        date_val = parse_datetime(value)
    except TypeError:
        date_val = None
    if date_val is None:
        raise ValueError(f"Invalid datetime specified: {value}")
    return date_val


'''
@description:
@param {*} field_uuid
@param {*} path 预留path参数，部分地方会根据条件选择通过 lemon_path_field 获取field
@return {*}
@author: lv.jimin
'''


def lemon_field(field_uuid, path=None):
    func = partial(base_lemon_field,
                   field_model_uuid_map=engine.uuids.field_uuid_map,
                   path=path)
    return func(field_uuid)


def base_field(field_uuid, path=None):
    func = partial(base_lemon_field,
                   field_model_uuid_map=engine.uuids.field_uuid_map,
                   path=None)
    return func(field_uuid)

# 多关联时模型join时会重命名, 所以需要从对应模型获取字段


def lemon_path_field(field_uuid, r_path):
    column = lemon_field(field_uuid)
    if not r_path:
        return column
    model = column.model
    model_uuid = model._meta.table_name
    model = model.alias(build_model_field_path(model_uuid, r_path))
    return getattr(model, column.name)


def rename_system_model_uuid(model_uuid):
    if model_uuid in SystemNamespace._ALL:
        app_uuid = get_runtime_app_uuid()
        version = get_runtime_app_revision()
        d = {k: v for v, k in SystemNamespace._ALL_DICT.items()}
        model_name = d.get(model_uuid)
        if model_name in ["role_member", "system_config"]:
            model_uuid = make_runtime_table_name(app_uuid, None, model_name)
        else:
            model_uuid = make_runtime_table_name(app_uuid, str(version), model_name)
    return model_uuid


def lemon_model(model_uuid, tenant_uuid=None):
    # 第一次通过系统表的 model_uuid 查找 模型类时，
    # 会创建 model copy 到 MODEL_COPY_DICT
    # 后面就有可能 通过 model copy 的实际表名 再次查找
    # 这时，应通过 MODEL_COPY_DICT 直接查询出来
    model_uuid = rename_system_model_uuid(model_uuid)
    model, _ = get_sys_model_copy(model_uuid)
    if model:
        return model
    func = partial(base_lemon_model,
                   model_uuid_map=engine.uuids.model_uuid_map)
    model = func(model_uuid)
    if model_uuid in SYS_ENTITY_UUID.TABLE_ALL:
        model = make_tenant_sys_table_copy(model, tenant_uuid)
    elif model_uuid in SYS_ENTITY_UUID.TABLE_ALL_WF:
        model = make_tenant_sys_table_copy(model, tenant_uuid)
    return model


def lemon_path_model(model_uuid, r_path):
    # 通过 model copy 的实际表名， 查找 node 时
    # 要通过 MODEL_COPY_DICT 转成 设计时的 model_uuid 后，再查找
    if model_uuid in MODEL_COPY_DICT:
        model_copy_info = MODEL_COPY_DICT.get(model_uuid, {})
        model_uuid = model_copy_info.get("table_name")
    model = lemon_model(model_uuid)
    if r_path:
        model_uuid = model._meta.table_name
        model = model.alias(build_model_field_path(model_uuid, r_path))
    return model


def lemon_model_node(model_uuid):
    for uuid_enum in [UUID_WorkflowInstance, UUID_NodeInstance, UUID_NodeTask, UUID_WFStore]:
        if model_uuid == uuid_enum.table_name.value:
            app_uuid = os.getenv("APP_UUID")
            if app_uuid:
                model_uuid = make_runtime_table_name(app_uuid, None, uuid_enum.meta_table_name.value)
                break
    _, model_uuid = get_sys_model_copy(model_uuid)
    func = partial(base_lemon_model_node,
                   node_uuid_map=engine.uuids.node_uuid_map)
    return func(model_uuid)


def lemon_association(association, model_uuid):
    func = partial(
        base_lemon_association, node_uuid_map=engine.uuids.node_uuid_map,
        relationship_uuid_map=engine.uuids.relationship_uuid_map)
    return func(association, model_uuid)


def lemon_association_copy_model(association, model_uuid):
    field_model, is_source, is_many, r_type = lemon_association(
        association, model_uuid)
    if field_model and field_model._meta.table_name in SystemTable.UUIDS:
        field_model = make_tenant_sys_table_copy(field_model)
    return field_model, is_source, is_many, r_type


def lemon_relationship(r_uuid):
    return engine.uuids.relationship_uuid_map.get(r_uuid)


def build_join(start_node, end_node, *nodes, path=None, query=None, auto_join=False, rename_model=False):
    func = partial(
        base_build_join, lemon_association=lemon_association, lemon_model_node=lemon_model_node,
        rename_model=rename_model
    )
    return func(start_node, end_node, *nodes, path=path, query=query, auto_join=auto_join)


def copy_query_joins(joins):
    return {key: copy.copy(value) for key, value in joins.items()}


def get_join_path(data_model, other_model, path, last=True):
    data_model_uuid = data_model._meta.table_name
    other_model_uuid = other_model._meta.table_name
    start_node = lemon_model_node(data_model_uuid)
    end_node = lemon_model_node(other_model_uuid)
    search_path = join_search_path(start_node, end_node, last=last, path=path)
    return search_path


async def build_data_container_filter_data(
        lsm, filter_data, print_type=None, print_content=None,
        print_container_data_source=None, from_event=True):
    data = {
        "machine_id": lsm.machine_id, "filter_data": filter_data,
        "print_type": print_type, "print_content": print_content,
        "print_container_data_source": print_container_data_source,
        "from_event": from_event
    }
    return data


async def build_form_filter_data(
        lsm, model_uuid, print_type=None,
        print_content=None, print_container_data_source=None,
        editor_source_data=None, pk=None, from_event=True):
    filter_data = {
        "model_uuid": model_uuid, "source_data": editor_source_data, "pk": pk
    }
    return await build_data_container_filter_data(
        lsm, filter_data, print_type=print_type, print_content=print_content,
        print_container_data_source=print_container_data_source,
        from_event=from_event)


async def build_chart_filter_data(lsm):
    filter_data = {}
    return await build_data_container_filter_data(
        lsm, filter_data)


def find_container_by_current_lsm():
    lsm = LemonContextVar.current_lsm.get()
    # app_log.info(f"lsm: {lsm}")
    if lsm:
        component = lsm.component
        return component
    return None


def find_page_by_current_lsm():
    container = find_container_by_current_lsm()
    if container:
        return container.page
    return None


def page_refresh(init=False):
    page = get_page()
    if page:
        asyncio.run_coroutine_threadsafe(
            page.refresh(init=init, call_later=True),
            GlobalVars.engine.loop).result()


def container_refresh(container=None, **kwargs):
    container = container or find_container_by_current_lsm()
    if container:
        asyncio.run_coroutine_threadsafe(
            container.refresh_result(
                call_later=True, **kwargs), GlobalVars.engine.loop).result()


def container_reset(container=None, **kwargs):
    container = container or find_container_by_current_lsm()
    if container and container.component_type == ComponentType.FORM:
        func = partial(container.new, **kwargs)
        asyncio.run_coroutine_threadsafe(
            container.put_callback(func), GlobalVars.engine.loop).result()


def page_print(control, pk):
    page = get_page()
    if page:
        asyncio.run_coroutine_threadsafe(
            page.page_print(control, pk), GlobalVars.engine.loop).result()


def container_validate(validate_list, container=None):
    """
    没传container时, 则认为使用当前的container
    执行批量的表单校验
    :param validate_list:
    :param container:
    """
    if not container:
        container = find_container_by_current_lsm()
    if not container:
        return {"code": 400, "msg": "container not found"}
    lsm = container.lsm
    lsm.globals.validate_field = None
    lsm.globals.validate_list = validate_list
    client = GlobalVars.engine.clients.get(lsm.sid)
    if not client:
        return {"code": 400, "msg": "client not found"}
    data = {
        "component": container.machine_id or container.uuid,
        "event": UUID.sm_form.event_validate,
        "params": {"validate_list": validate_list}
    }
    message = LemonMessage(command="component_event", data=data)
    asyncio.run_coroutine_threadsafe(client.handle_message(message), GlobalVars.engine.loop)
    return {"code": 200, "msg": "ok"}


def get_container_control_info(container=None):
    """
    通过get_container或get_container_by_name获取容器
    并返回所有输入控件的{name:uuid}键值对
    """
    container = container or get_container()
    input_control_dict = {}
    if container:
        for _, control in container.data_controls.items():
            if control.edit_column and control.editable_value is not False:
                input_control_dict.update({control.name: control.uuid})
    return input_control_dict


def get_container_control_fields(container=None) -> Dict[str, Any]:
    input_control_dict = {}
    container = container or get_container()
    if not container:
        return input_control_dict
    for _, control in container.data_controls.items():
        if control.edit_column and control.editable_value is not False:
            input_control_dict.update({
                control.name: control
            })
    return input_control_dict


def get_container():
    container = find_container_by_current_lsm()
    return container


def get_page():
    page = find_page_by_current_lsm()
    return page


def get_container_by_name(name, parent=None):
    parent = parent or get_page()
    container = parent.containers.get(name)
    return container


def get_obj_by_name(name, parent=None):
    container = get_container_by_name(name, parent=parent)
    if container:
        return container.get_obj()
    return None


def build_instance_key(table_name, pk):
    return "_".join([table_name, str(pk)])


def get_instances():
    c = find_container_by_current_lsm()
    if c and hasattr(c, "page") and hasattr(c.page, "instances"):
        instances = c.page.instances
    else:
        instances = dict()
    return instances


class LemonActor(object):

    def __init__(self, sid=None):
        self._sid = sid

    @property
    def sid(self):
        return self._sid

    @sid.setter
    def sid(self, value):
        self._sid = value

    # 向状态机发送事件
    async def publish_event(self, machine_id, event_uuid, event_args=None):
        if self.sid is None:
            raise ValueError()
        else:
            key = "event"
            event_topic = build_topic(self.sid, machine_id, key, event_uuid)
            if not isinstance(event_args, dict):
                event_args = dict()
            event_msg = LemonMessage(data={"event_args": event_args})
            app_log.info(f"publish_event: event_topic {event_topic}")
            await send_topic_msg(event_topic, event_msg)

    # 向状态机发送命令
    async def publish_command(self, machine_id, command, data):
        if self.sid is None:
            raise ValueError()
        else:
            await send_component_command_msg(self.sid, machine_id, command, data)


class LemonEvent(object):

    datalist_inited = UUID.sm_datalist.event_inited
    form_inited = UUID.sm_form.event_linkage
    chart_inited = UUID.sm_chart.event_inited
    timeline_inited = UUID.rc_timeline.event_init


class LemonCommand(object):

    GET_VARIABLES = "get_variables"

    EVENT = "event"

    ALL = [
        GET_VARIABLES, EVENT
    ]

class AttributeFreeMeta(type):
    def __new__(cls, name, bases, dct):
        dct['_guarded_writes'] = 1
        return super().__new__(cls, name, bases, dct)

class LemonBuiltins(dict):

    def __init__(self):
        self.update(safe_builtins)
        self.update(limited_builtins)
        self.update(utility_builtins)
        self.update({
            "time": lemon_time, "datetime": dt, "json": ujson, "re": re, "peewee": peewee,
            "enumerate": enumerate, "sum": sum, "copy": copy, "calendar": calendar, "sorted": sorted,
            "list": list, "dict": dict, "random": random, "max": max, "min": min, "all": all,
            "FuncExecError": FuncExecError, "getattr": getattr, "__import__": self.protected_import,
            "next": next, "abs": abs, "delattr": delattr, "hash": hash, "memoryview": memoryview,
            "set": set, "help": help, "setattr": setattr, "any": any,  "dir": dir, "hex": hex, "next": next,
            "slice": slice, "ascii": ascii, "divmod": divmod, "id": id, "object": object, "sorted": sorted,
            "bin": bin, "enumerate": enumerate, "input": input, "oct": oct, "staticmethod": staticmethod,
            "bool": bool, "int": int, "open": open, "str": str, "breakpoint": breakpoint, "isinstance": isinstance,
            "ord": ord, "sum": sum, "bytearray": bytearray, "filter": filter, "issubclass": issubclass, "pow": pow,
            "super": super, "bytes": bytes, "float": float, "iter": iter, "print": print, "tuple": tuple,
            "callable": callable, "format": format, "len": len, "property":  property, "type": type, "chr": chr,
            "frozenset": frozenset, "list": list, "range": range, "vars": vars, "classmethod": classmethod,
            "getattr": getattr, "locals": locals, "repr": repr, "zip": zip, "compile": compile, "globals": globals,
            "map": map, "reversed": reversed, "complex": complex, "hasattr": hasattr, "round": round, "uuid": uuid,
            "reduce": reduce, "peewee_ext": peewee_ext,
            "decimal": decimal, "dateutil": dateutil, "dateutil.relativedelta": dateutil.relativedelta,
            "BaseException": Exception, "protected_import": self.protected_import, "requests": requests,
            "aliyunsdkcore": aliyunsdkcore, "wechatpy": wechatpy, "pypinyin": pypinyin, "ftplib": ftplib,
            "__metaclass__": AttributeFreeMeta})

    def protected_import(self, name, *args, **kwargs):
        if name not in ALLOWED_IMPORT:
            raise KeyError(f"Not allowed to import {name}")
        if name == "time":
            module = lemon_time
        elif name == "lemon_excel":
            module = __import__('packages.excel_formula', fromlist=[''])
        else:
            module = __import__(name, *args, **kwargs)
        return module


class LemonGlobals(dict):

    def __init__(self):
        self.builtins = LemonBuiltins()
        self.update({
            "__builtins__": self.builtins,
            "_getattr_": self.protected_getattr,
            "_write_": full_write_guard,
            "_getiter_": iter,
            "_getitem_": default_guarded_getitem,
            "_iter_unpack_sequence_": guarded_iter_unpack_sequence,
            "_unpack_sequence_": guarded_unpack_sequence
        })

    def protected_getattr(self, obj, name, default=None):
        """Restricted method to get attributes."""
        # app_log.debug((obj, name))
        if name.startswith("async_"):
            raise KeyError("Not allowed to access async methods")
        if (
            obj is dt
            and name not in ALLOWED_DATETIME
            or isinstance(obj, TimeWrapper)
            and name not in ALLOWED_TIME
            or obj is peewee
            and name not in ALLOWED_PEEWEE
        ):
            raise KeyError(
                f"Not allowed to access {obj.__class__.__name__}.{name}")
        return getattr(obj, name, default)


class SystemVariableProxy(object):

    def __init__(self, k_list, *args, **kwargs):
        self._k_list = k_list
        self._obj = None

    def __repr__(self):
        return "<%s@%s>" % (type(self).__name__, id(self))

    @property
    def obj(self):
        return None if self._obj is None else self._obj()

    @obj.setter
    def obj(self, obj):
        self._obj = None if obj is None else weakref.ref(obj)

    def __getattr__(self, k):
        if self.obj:
            v = self.obj.get_system_variable(k)
            return v
        return None

    def __setattr__(self, name: str, value: Any) -> None:
        if name in ["_k_list", "_obj"]:
            super().__setattr__(name, value)
        elif name in self._k_list:
            raise AttributeError
        super().__setattr__(name, value)


class MessageBox(object):

    def __init__(self, sid=None, machine_id=None):
        self.sid = sid
        self.machine_id = machine_id

    @classmethod
    def gen_message(
            cls, alert_type, message="", title="", buttonContent="",
            show_button=True, show_title=True):
        if not buttonContent:
            show_button = False
        send_data = {
            "modal_type": alert_type, "show_title": show_title,
            "show_button": show_button, "title": title,
            "content": message, "buttonContent": buttonContent
        }
        return send_data

    def showinfo(self, message=None, title=None, *args, **options):
        self.send_alert_message("info", message, title, *args, **options)

    def showwarning(self, message=None, title=None, *args, **options):
        self.send_alert_message("warning", message, title, *args, **options)

    def showerror(self, message=None, title=None, *args, **options):
        self.send_alert_message("error", message, title, *args, **options)

    def send_alert_message(
            self, alert_type, message="", title="", buttonContent="",
            show_button=True, show_title=True):
        send_data = self.gen_message(
            alert_type, message=message, title=title,
            buttonContent=buttonContent,
            show_button=show_button, show_title=show_title)
        # print(message) # 云函数日志目前只支持用print输出
        app_log.info(message)
        coro = send_component_result_msg(
            self.sid, self.machine_id, "message_box", send_data)
        asyncio.run_coroutine_threadsafe(coro, engine.loop)


class Console:
    sid: Optional[str] = None
    machine_id: Optional[str] = None

    def __init__(self, sid=None, machine_id=None):
        self.sid = sid
        self.machine_id = machine_id

    def log(self, *args):
        self.send_message("log", args)

    def exception(self, message: str, *args):
        type_, value_, tb = sys.exc_info()
        error_message = format_exception(type_, value_, tb)
        message = "\n".join([message, error_message])
        self.send_message("log", [message] + list(args))

    def send_message(self, func: str, params: Any):
        send_data = {"func": func, "params": ujson.dumps(params)}
        coro = send_component_result_msg(
            self.sid, self.machine_id, "console", send_data)
        asyncio.run_coroutine_threadsafe(coro, engine.loop)


class PopupSubmit(object):

    def __init__(self, sid=None, machine_id=None):
        self.sid = sid
        self.machine_id = machine_id

    def new_button(
            self, title, value, button_type="default", shape="default",
            size="middle", is_danger=False, is_ghost=False,
            button_height=None, button_width=None):
        button = {
            "title": title, "value": value, "button_type": button_type,
            "shape": shape, "size": size, "is_danger": is_danger,
            "is_ghost": is_ghost
        }
        if size == "customize":
            button_height = button_height or 30
            button_width = button_width or 65
            button.update({
                "buttonheight": button_height, "buttonwidth": button_width})
        return button

    def send_submit(self, title="", message="", *buttons):
        uuid = lemon_uuid()
        buttons = buttons or []
        buttons = buttons[:100]  # 限制最多显示100个按钮
        send_data = {
            "uuid": uuid, "title": title, "message": message,
            "buttons": buttons
        }
        coro = send_component_result_msg(
            self.sid, self.machine_id, "popup_submit", send_data)
        asyncio.run_coroutine_threadsafe(coro, engine.loop)
        queue = Queue()
        POPUP_SUBMIT_QUEUE_DICT.setdefault(self.sid, dict())
        POPUP_SUBMIT_QUEUE_DICT[self.sid][uuid] = queue
        value = queue.get()
        del POPUP_SUBMIT_QUEUE_DICT[self.sid][uuid]
        # if value is Exception:
        #     raise RuntimeError(f"获取提交数据失败")
        return value


class LemonLock(object):

    LockError = aioredlock.LockError

    def rename_key(self, key, with_tenant=True):
        app_uuid = engine.config.APP_UUID
        current_user = LemonContextVar.current_user.get()
        if current_user and with_tenant:
            tenant_uuid = current_user.tenant_uuid
            return "_".join([app_uuid, tenant_uuid, key])
        return "_".join([app_uuid, key])

    def run_coroutine_with_result(self, coroutine):
        future = asyncio.run_coroutine_threadsafe(coroutine, engine.loop)
        return future.result()

    def lock(self, name, timeout, with_tenant=True):
        name = self.rename_key(name, with_tenant=with_tenant)
        f = engine.redlock.lock(name, lock_timeout=timeout)
        return self.run_coroutine_with_result(f)

    def unlock(self, lock):
        f = engine.redlock.unlock(lock)
        return self.run_coroutine_with_result(f)

    def extend(self, name, timeout, with_tenant=True):
        name = self.rename_key(name, with_tenant=with_tenant)
        f = engine.redlock.extend(name, timeout)
        return self.run_coroutine_with_result(f)

    def is_locked(self, name, with_tenant=True):
        name = self.rename_key(name, with_tenant=with_tenant)
        f = engine.redlock.is_locked(name)
        return self.run_coroutine_with_result(f)


async def _run_in_atomic(func, *args, **kwargs):
    current_connector = LemonContextVar.current_connector.get()
    new_atomic_uuid = lemon_uuid()
    async with engine.userdb.objs.atomic(new_atomic_uuid):
        app_log.info(f"start new atomic: {new_atomic_uuid}")
        result = await GlobalVars.FuncRun.run(func, *args, **kwargs)
        if isinstance(result, FuncRunError) and result.rollback:
            raise FuncExecError(-1, result.error_message)
    if current_connector:
        await current_connector.client.do_send_messages(is_deliver=True)
    return result


def run_in_atomic_nowait(func, *args, **kwargs):
    async_func = _run_in_atomic(func, *args, **kwargs)
    future = asyncio.run_coroutine_threadsafe(async_func, engine.loop)
    return future


def run_in_atomic(func, *args, **kwargs):
    return run_in_atomic_nowait(func, *args, **kwargs).result()


def run_out_atomic(func, *args, **kwargs):
    var_excludes = {
        LemonContextVar.current_root_form.name: None,
        LemonContextVar.atomic_cache_tasks.name: [],
        LemonContextVar.atomic_resource_locks.name: [],
        LemonContextVar.atomic_instances.name: [],
        LemonContextVar.current_transaction.name: None,
        TracingContext.trace_id.name: generate_trace_id()
    }
    ctx = copy_current_context(excludes=var_excludes)
    fut = CVE.submit_without_context(ctx.run, partial(func, *args, **kwargs))
    return fut


async def _run_async_query(query):
    result = await engine.userdb.objs.execute(query)
    return result


def run_async_query(query):
    async_func = _run_async_query(query)
    future = asyncio.run_coroutine_threadsafe(async_func, engine.loop)
    return future.result()


class OSSBucket(OssHelper):

    def __init__(self, engine=None):
        self.engine = engine

    def sign_url(
            self, path, method="GET", expires=600, slash_safe=True, headers=None):
        if self.engine.config.LOCAL_DEPLOY:
            file_name = path.split("/")[-1]
            encoded_path = urllib.parse.quote(path, safe="")
            ext_download_link = f"{engine.config.CUSTOM_APP_DOMAIN}/{engine.config.APP_UUID}/api/vfs/v1/get/{file_name}?url={encoded_path}"
            return ext_download_link
        return sign_oss_url_sync(
            self.engine, path, method, expires, slash_safe, headers)

    def zipfiles_by_fileurls(self, urls: list = [], refilename: str = lemon_uuid(), change_filename: bool = True) -> str:

        local_deploy = self.engine.config.LOCAL_DEPLOY
        bucket = self.engine.oss_handler.bucket
        tmp_dir = "/tmp/ziptmpad3c03575f945574b70da489d9"
        subprocess.run(['mkdir', '-p', tmp_dir])
        if len(urls) == 1:
            src_file = urls[0]
            src_file_ext = src_file.split(".")[-1]
            file_name = f"{refilename}.{src_file_ext}" or src_file.split("/")[-1] if change_filename else os.path.basename(src_file)
            dest_file = f"tmp/{file_name}"
            if local_deploy:
                file_path = "/".join([tmp_dir, file_name])
                asyncio.run(bucket.get_object_to_file(src_file, file_path))
                dest_file = file_path.lstrip('/')
                asyncio.run(self.put_oss_object(dest_file, file_path, local_file=True))
            else:
                bucket.copy_object(bucket.bucket_name, src_file, dest_file)
            ext_download_link = self.sign_url(dest_file, "GET", 360, slash_safe=True)
            return ext_download_link
        local_zip_tmp_dir = f"{tmp_dir}/{refilename}.zip"
        file_list = []
        for index, url in enumerate(urls):
            file_ext = url.split(".")[-1]
            file_name = f"{refilename}_{index}.{file_ext}" if change_filename else os.path.basename(url)
            file_path = "/".join([tmp_dir, file_name])
            if local_deploy:
                asyncio.run(bucket.get_object_to_file(url, file_path))
            else:
                bucket.get_object_to_file(url, file_path)
            file_list.append((file_path, file_name))
        zip_files(file_list, local_zip_tmp_dir)
        oss_zip_path = local_zip_tmp_dir.lstrip('/')
        if local_deploy:
            asyncio.run(self.put_oss_object(oss_zip_path, local_zip_tmp_dir, local_file=True))
        else:
            bucket.put_object_from_file(oss_zip_path, local_zip_tmp_dir, {})
        ext_download_link = self.sign_url(oss_zip_path, "GET", 360, slash_safe=True)
        subprocess.run(['rm', '-rf', tmp_dir])
        return ext_download_link


def build_order_by_case(column, value):
    order_by_case = None
    if isinstance(value, list) and value:
        case_expr = []
        start_index = 100000000  # 一亿  MySQL 是按照位数排序的
        for index, v in enumerate(value):
            if isinstance(v, dict):
                v = v.get("value")
            case_expr.append(((column == v), start_index+index))
        if case_expr:
            order_by_case = peewee.Case(None, case_expr, "a")
    return order_by_case


def build_column_info(
        path_column_dict, data_model, column_dict=None,
        is_group_by=False, alias_columns=None, exist_model_set=None,
        relation_column_dict=None, hierarchy_column_dict=None,
        self_referential_control_dict=None, subtable_relation_column_dict=None, group_columns=None):
    column_dict = column_dict or dict()
    control_obj = column_dict.get("control_obj")
    agg_func = column_dict.get("aggre_func")
    separator = column_dict.get("separator")
    handle_column = column_dict.get("handle")
    handle_column_name = handle_column.name if handle_column else ""
    if alias_columns is None:
        alias_columns = []
    if exist_model_set is None:
        exist_model_set = set()
    if relation_column_dict is None:
        relation_column_dict = dict()
    if hierarchy_column_dict is None:
        hierarchy_column_dict = dict()
    if self_referential_control_dict is None:
        self_referential_control_dict = dict()
    if subtable_relation_column_dict is None:
        subtable_relation_column_dict = dict()
    for field_uuid, column_info in path_column_dict.items():
        column = column_info.get("column")
        if not column:
            continue
        column_model = column.model
        path_list = column_info.get("path_list", [])
        for path in path_list:
            if column.name != "id":
                # 层次字段没有作为关联字段选择时，无需额外查询
                if column.hierarchy_field == 1 and column_model != data_model:
                    column_model_uuid = column_model._meta.table_name
                    column_model_path = build_model_field_path(
                        column_model_uuid, path)
                    hierarchy_column_dict.update(
                        {column_model_path: [column, path]})
                    continue

                # TODO: 这里的代码，以后要重构成，每个组件独立完成
                # 给组件传入 data_model, data_query, alias_columns
                if control_obj:
                    self_referential_control_dict.update(
                        control_obj.self_referential_dict)
                    # 这里不需要 continue 以为有其他列也可能选了 这个字段

            # if column_model not in all_models:
            #     all_models.update({column_model: path})
            # 自关联时, column_model 和 data_model 相同, 但还是要 join
            if column_model == data_model and not path:
                if handle_column_name == column.name:
                    if group_columns:
                        agg_column = python_agg_field(column)
                    else:
                        agg_column = get_agg_field(
                            column, agg_func, separator=separator)
                else:
                    agg_column = get_agg_field(column, None)
                alias_columns.append(agg_column.alias(field_uuid))
            else:
                column_model_uuid = column_model._meta.table_name
                path_model_uuid = build_model_field_path(
                    column_model_uuid, path)
                exist_model_set.add(path_model_uuid)
                self_referential = is_self_referential_field(column, path)
                this_relation_column_dict = relation_column_dict
                if control_obj and control_obj.component_type in [
                        ComponentType.DATALIST, ComponentType.CARDLIST,
                        ComponentType.DATAGRID] and is_group_by:
                    # 子表的数据如果过多，直接与主表的 query join 会很慢
                    this_relation_column_dict = subtable_relation_column_dict
                model_column_info = this_relation_column_dict.setdefault(
                    path_model_uuid, {
                        "path": [], "columns": [], "model_uuid": column_model_uuid,
                        "self_referential": self_referential, "field_uuid_dict": {},
                        "handle_column_dict": {}})
                if handle_column_name == column.name:
                    model_column_info["handle_column_dict"].update(
                        {column.column_name: {"agg_func": agg_func,
                                              "separator": separator}})
                model_column_info["model_uuid"] = column_model_uuid
                model_columns = model_column_info.get("columns")
                if column.column_name != field_uuid:
                    model_column_info["field_uuid_dict"].update(
                        {column.column_name: field_uuid})
                if not model_columns:
                    model_column_info["path"] = path
                model_columns.append(column)
                model_columns = list(set(model_columns))
                model_column_info["columns"] = model_columns


'''
@name: wning
@Date: 2021-05-24 19:52:36
@param {*} data_model
@param {*} column_dict
@param {*} with_join 当校验发起关联联动时，不应将当前数据源的模型 join 起来，会导致数据查询不出来
@return {*}
'''


def build_relation_query_old(data_model, column_dict, with_join=True):
    relation_model_query_dict = {}
    data_model_uuid = data_model._meta.table_name
    data_model_node = lemon_model_node(data_model_uuid)
    for model_uuid, column_info in column_dict.items():
        relation_model = lemon_model(model_uuid)
        relation_model_id_alias = relation_model.id.alias(model_uuid + "_pk")
        relation_alias_columns = [data_model.id] if with_join else []
        relation_alias_columns.append(relation_model_id_alias)
        path_list = column_info.get("path")
        columns = column_info.get("columns", [])
        start_node = data_model_node
        end_node = lemon_model_node(model_uuid)
        is_many, last_path, model_query = build_join_info(
            start_node, end_node, path=path_list,
            lemon_association=lemon_association, lemon_model_node=lemon_model_node)
        if not with_join:
            model_query = relation_model.select()
        for column in columns:
            column_name = column.column_name
            # if column_name == relation_model.id.name:
            #     continue
            alias_column = column.alias(column_name)
            relation_alias_columns.append(alias_column)
        relation_alias_columns = list(set(relation_alias_columns))
        model_query = model_query.select(
            *relation_alias_columns).order_by(relation_model.id)
        relation_model_query_dict.update(
            {model_uuid: {"query": model_query, "is_many": is_many}})
        app_log.info(f"model: {model_uuid}, query: {model_query}")
    return relation_model_query_dict


# auto_alias = False, 传进来的column是Alias
def build_relation_query(
        data_model, column_dict, with_join=True, auto_alias=True,
        rel_model_pk=None, ignore_is_many=False):
    relation_model_query_dict = {}
    data_model_uuid = data_model._meta.table_name
    data_model_node = lemon_model_node(data_model_uuid)
    for model_path_uuid, column_info in column_dict.items():
        self_referential = column_info.get("self_referential")
        model_uuid = column_info.get("model_uuid") or model_path_uuid
        app_log.info(
            f"model_path_uuid: {model_path_uuid}, column_info: {column_info}")
        relation_model = lemon_model(model_uuid)
        # 自关联 需要讲 relation_model 别名掉
        if self_referential:
            relation_model = relation_model.alias(model_path_uuid)

        local_with_join = with_join
        pk_list = column_info.get("pk_list", [])
        if isinstance(pk_list, list) and pk_list:
            # pk_list 有值，说明新增了关联数据
            # 这时不能再join 主表单模型，否则数据会查不到
            local_with_join = False
        model_uuid_alias = model_uuid + "_pk"
        relation_model_id_alias = relation_model.id.alias(model_uuid_alias)
        model_path_uuid_alias = model_path_uuid + "_pk"
        relation_model_path_id_alias = relation_model.id.alias(
            model_path_uuid_alias)
        relation_alias_columns = [data_model.id] if local_with_join else []
        relation_alias_columns.append(relation_model_id_alias)
        relation_alias_columns.append(relation_model_path_id_alias)
        relation_alias_names = [data_model.id.name] if local_with_join else []
        relation_alias_names.append(model_uuid_alias)
        relation_alias_names.append(model_path_uuid_alias)
        path_list = column_info.get("path")
        columns = column_info.get("columns", [])
        field_uuid_dict = column_info.get("field_uuid_dict", {})
        start_node = data_model_node
        end_node = lemon_model_node(model_uuid)
        is_many, last_path, model_query = build_join_info(
            start_node, end_node, path=path_list,
            lemon_association=lemon_association, lemon_model_node=lemon_model_node)
        if is_many and ignore_is_many:
            continue
        if last_path is None:
            continue
        order_column = relation_model.id
        if not local_with_join or rel_model_pk and model_uuid in rel_model_pk:  # 自关联
            model_query = relation_model.select()
            order_by_case = build_order_by_case(relation_model.id, pk_list)
            if order_by_case is not None:
                order_column = order_by_case
        elif last_path.is_middle:
            middle_model = last_path.m_node.model
            middle_model_uuid = middle_model._meta.table_name
            if middle_model_uuid in SystemTable.UUIDS:
                middle_model = make_tenant_sys_table_copy(middle_model)
            order_column = middle_model.id

        if isinstance(pk_list, list) and pk_list:
            # 添加了子表数据，又刷新了页面
            # 不知道表单上哪些数据要从数据库取，哪些要保留，只能先留着关联数据了
            new_pk_list = []
            for pk_info in pk_list:
                if isinstance(pk_info, dict):
                    new_pk_list.append(pk_info.get("value"))
                else:
                    new_pk_list.append(pk_info)
            model_query = model_query.where(relation_model.id.in_(new_pk_list))

        for column in columns:
            if auto_alias:
                if self_referential:
                    column = getattr(relation_model, column.name)
                column_name = column.column_name
                # if column_name == relation_model.id.name:
                #     continue
                if column.name in SystemField.FIELD_DICT:
                    # 系统字段的column_name并不是field_uuid会导致组件获取不到数据
                    column_name = get_sys_field_uuid(
                        column.model._meta.table_name, column_name)
                    column_name = field_uuid_dict.get(column_name, column_name)
                column_name = build_field_path(path_list, column_name)
                alias_column = column.alias(column_name)
            else:
                if self_referential:
                    new_column = getattr(relation_model, column.unalias().name)
                    column = new_column.alias(column._alias)
                column_name = column._alias
                alias_column = column
            relation_alias_columns.append(alias_column)
            relation_alias_names.append(column_name)
        relation_alias_columns = list(set(relation_alias_columns))
        relation_alias_names = list(set(relation_alias_names))
        model_query = model_query.select(
            *relation_alias_columns).order_by(order_column)
        relation_model_query_dict.update(
            {model_path_uuid: {
                "query": model_query, "is_many": is_many,
                "model_uuid": model_uuid, "names": relation_alias_names,
                "model_id": relation_model.id, "pk_list": pk_list}})
        app_log.info(f"model: {model_uuid}, query: {model_query}")
    return relation_model_query_dict


'''
@description: 聚合时使用一条sql语句
@param {*} data_model
@param {*} column_dict
@param {*} data_query
@param {*} with_join
@return {*}
@author: lv.jimin
'''


def build_agg_query_by_relation(data_model, column_dict, data_query, with_join=True,
                                group_columns=None):
    data_model_uuid = data_model._meta.table_name
    data_model_node = lemon_model_node(data_model_uuid)
    grouy_columns = data_query._group_by or []
    grouy_columns = ["|".join([column.source.alias, column.field.column_name])
                     if hasattr(column, "source")
                     else column.column_name
                     for column in grouy_columns]
    for model_path_uuid, column_info in column_dict.items():
        model_uuid = column_info.get("model_uuid") or model_path_uuid
        # app_log.info(
        #     f"model_path_uuid: {model_path_uuid}, column_info: {column_info}")
        path_list = column_info.get("path")
        columns = column_info.get("columns", [])
        field_uuid_dict = column_info.get("field_uuid_dict", {})
        start_node = data_model_node
        end_node = lemon_model_node(model_uuid)
        app_log.info(
            f"agg_build_join start_node:{start_node}, end_node:{end_node}, path: {path_list}")
        is_many, last_path, data_query = build_join_info(
            start_node, end_node, path=path_list,
            lemon_association=lemon_association, lemon_model_node=lemon_model_node, query=data_query, rename_model=True)
        relation_model = lemon_model(model_uuid)
        relation_model = relation_model.alias(model_path_uuid)
        try:
            if last_path.is_middle:
                if last_path.r_uuid in SystemTable.UUIDS:
                    middle_model = make_tenant_sys_table_copy(
                        last_path.m_node.model)
                    middle_model_uuid = middle_model._meta.table_name
                else:
                    middle_model = last_path.m_node.model
                    middle_model_uuid = last_path.r_uuid
                middle_model_path_uuid = build_model_field_path(
                    middle_model_uuid, path_list)
                middle_model = middle_model.alias(middle_model_path_uuid)
            else:
                middle_model = None
        except Exception as e:
            columns = column_info.get("columns", [])
            fields_name = [f"name：{column.model._meta.model_name}.{column.name}，uuid：{field_uuid_dict.get(column.column_name, column.column_name)}"
                        for column in columns]
            message = f"start_node：{start_node.model_name}，end_node：{end_node.model_name}\n"
            message += "字段：" + ",".join(fields_name) + "\n"
            raise IsMiddleError(message)
        if is_many:
            id_agg = python_agg_field(relation_model.id, middle_model=middle_model)
        else:
            id_agg = relation_model.id
        handle_column_dict = column_info.get("handle_column_dict")
        model_uuid_alias = model_path_uuid + "_pk"
        relation_model_id_alias = id_agg.alias(model_uuid_alias)
        relation_alias_columns = [data_model.id] if with_join else []
        relation_alias_columns.append(relation_model_id_alias)
        for column in columns:
            column = getattr(relation_model, column.name)
            column_name = column.column_name
            model_field_name = "|".join([model_path_uuid, column_name])
            # if column_name == relation_model.id.name:  # 当前可以选 主键 字段了
            #     continue
            if column.name in SystemField.FIELD_DICT:
                # 系统字段的column_name并不是field_uuid会导致组件获取不到数据
                column_name = field_uuid_dict.get(column_name, column_name)
            column_name = build_field_path(path_list, column_name)
            # 分组字段不能选聚合函数
            if model_field_name in grouy_columns:
                alias_column = column.alias(column_name)
            else:
                handle_column_info = handle_column_dict.get(
                    column.column_name, {})
                agg_func = handle_column_info.get("agg_func")
                if agg_func is not None:
                    separator = handle_column_info.get("separator")
                # if is_many:
                    agg_column = get_agg_field(column, agg_func=agg_func,
                                               middle_model=middle_model, separator=separator)
                    alias_column = agg_column.alias(column_name)
                elif is_many or group_columns:
                    agg_column = python_agg_field(
                        column, middle_model=middle_model)
                    alias_column = agg_column.alias(column_name)
                else:
                    alias_column = column.alias(column_name)
            relation_alias_columns.append(alias_column)
        relation_alias_columns = list(set(relation_alias_columns))
        data_query = data_query.select_extend(*relation_alias_columns)
    # app_log.info(f"agg_query_info: {data_query}")
    return data_query


def build_model_join(start_model, end_model, path_list, data_query, rename_model=True):
    start_model_uuid = start_model._meta.table_name
    end_model_uuid = end_model._meta.table_name
    start_node = lemon_model_node(start_model_uuid)
    end_node = lemon_model_node(end_model_uuid)
    is_many, last_path, data_query = build_join_info(
        start_node, end_node, path=path_list,
        lemon_association=lemon_association, lemon_model_node=lemon_model_node, query=data_query,
        rename_model=rename_model)
    return data_query


def check_select_column(columns):
    exist_name_dict = dict()
    new_columns = list()
    for column in columns:
        if hasattr(column, "_alias"):
            name = getattr(column, "_alias")
            exist_column = exist_name_dict.get(name)
            if exist_column:
                if type(exist_column) != column:
                    # 这里没用，remove 判断不出来是不是同一个字段
                    # new_columns.remove(exist_column)
                    exist_name_dict[name] = column
                    new_columns.append(column)
            else:
                exist_name_dict[name] = column
                new_columns.append(column)
        else:
            name = column.column_name
            if not exist_name_dict.get(name):
                exist_name_dict[name] = column
                new_columns.append(column)

    return new_columns


def build_hierarchy_columns(column, path_list, alias_columns, alias_names=None):
    column_name = column.column_name
    for hierarchy in column.hierarchy_list:
        hierarchy_field = hierarchy.get("field", None)
        hierarchy_path = hierarchy.get("path", [])
        if hierarchy_field:
            path_list.extend(hierarchy_path)
            hierarchy_column = lemon_field(hierarchy_field)
            hierarchy_model = hierarchy_column.model
            hierarchy_model_pk = "_".join([hierarchy_field, "pk"])
            hierarchy_model_id_alias = hierarchy_model.id.alias(
                hierarchy_model_pk)
            hierarchy_field_key = "_".join([column_name, hierarchy_field])
            hierarchy_column_alias = hierarchy_column.alias(
                hierarchy_field_key)
            alias_columns.extend(
                [hierarchy_model_id_alias, hierarchy_column_alias])
            if isinstance(alias_names, list) and alias_names:
                alias_names.extend([hierarchy_model_pk, hierarchy_field_key])


'''
@name: wning
@Date: 2021-05-24 19:49:26
@param {*} data_model
@param {*} column_dict
@param {*} with_join 当校验发起关联联动时，不应将当前数据源的模型 join 起来，会导致数据查询不出来
@return {*}
'''


def build_hierarchy_query(
        data_model, column_dict, with_join=True, is_many=None, ignore_is_many=False):
    hierarchy_query_dict = dict()
    data_model_uuid = data_model._meta.table_name
    data_model_node = lemon_model_node(data_model_uuid)
    for field_uuid, column_info in column_dict.items():
        column, path = column_info
        if column.hierarchy_field != 1:
            continue
        hierarchy_alias_columns = [data_model.id] if with_join else []
        hierarchy_alias_names = [data_model.id.name] if with_join else []
        column_model = column.model
        column_model_uuid = column_model._meta.table_name
        start_node = data_model_node if with_join else lemon_model_node(
            column_model_uuid)
        model_query = start_node.model.select()

        # 构造 where 条件
        end_node = lemon_model_node(column_model_uuid)
        model_is_many, last_path, _query = build_join_info(
            data_model_node, end_node, path=path,
            lemon_association=lemon_association, lemon_model_node=lemon_model_node)
        if is_many is not None:
            model_is_many = is_many
        if model_is_many and ignore_is_many:
            continue
        frontref, backref = last_path.frontref, last_path.backref
        where_expr = None
        if last_path.is_middle:
            # frontref 和 backref 均为 m_model 模型上的外键字段
            # data_model 一定为目标模型
            if with_join:
                where_expr = (backref is not None)
        elif last_path.is_source:
            # data_model 为目标模型
            # frontref 是 column_model 模型上的外键字段
            # backref 是 data_model 的主键
            pass
        else:
            # data_model 为外键模型
            # frontref 是 data_model 模型上的外键字段
            # backref 是 column_model 的主键
            if with_join:
                where_expr = (frontref is not None)
        column_name = column.column_name
        hierarchy_column_alias = column.alias(column_name)
        column_model_pk = "_".join([column_model_uuid, "pk"])
        column_model_id_alias = column_model.id.alias(column_model_pk)
        hierarchy_alias_columns.extend(
            [hierarchy_column_alias, column_model_id_alias])
        hierarchy_alias_names.extend([column_name, column_model_pk])
        path_list = []
        if path and with_join:
            # 主模型 与 层次字段 也有 path ，所以需要 加上
            # 如果是 校验的情况，不需要 加上
            path_list.extend(path)
        build_hierarchy_columns(
            column, path_list, hierarchy_alias_columns, hierarchy_alias_names)
        if path_list:
            model_query = build_join(
                start_node, None, path=path_list, query=model_query)
        if where_expr is not None:
            model_query = model_query.where(where_expr)
        model_query = model_query.select(
            *hierarchy_alias_columns).order_by(column_model.id)
        hierarchy_query_dict.update(
            {field_uuid: {"query": model_query, "is_many": model_is_many, "names": hierarchy_alias_names}})
    return hierarchy_query_dict


async def get_hierarchy_data(
        data_model, pk_field, value, hierarchy_query_dict):
    hierarchy_data_dict = {}
    for field_uuid, query_info in hierarchy_query_dict.items():
        hierarchy_source_data = {}
        query = query_info.get("query")
        is_many = query_info.get("is_many")
        query = query.where(pk_field.in_(value)).dicts()
        app_log.info(f"hierarchy_query: {query}")
        data_list = await engine.userdb.objs.execute(query)
        for data_dict in data_list:
            # app_log.debug(f"data_dict: {data_dict}")
            if is_many:
                for data_uuid, data in data_dict.items():
                    if data_uuid == data_model.id.name:
                        continue
                    source_data_list = hierarchy_source_data.setdefault(data_uuid, [])
                    source_data_list.append(data)
            else:
                hierarchy_source_data.update(data_dict)
        hierarchy_data_dict.update({field_uuid: hierarchy_source_data})
    return hierarchy_data_dict


def calc_self_referential_path_join(
        data_model, control_obj=None, data_source=None):
    start_node = lemon_model_node(data_model._meta.table_name)
    end_node = lemon_model_node(data_model._meta.table_name)
    if not isinstance(data_source, dict):
        if control_obj.component_type == ComponentType.TEXT:
            data_source = control_obj.handle_data_source
        else:
            data_source = control_obj.data_source
    self_referential_path = data_source.get("association", {}).get("path", [])
    last_path = join_search_path(
        start_node, end_node, last=True, path=self_referential_path)
    frontref, _, _ = last_path.frontref, last_path.backref, last_path.r_type
    return self_referential_path, frontref


def build_self_referential_query(
        data_model, self_referential_control_dict, with_join=True, new_node_type=None):
    self_referential_query_dict = {}
    for field_uuid, control_info in self_referential_control_dict.items():
        control_obj = control_info.get("control_obj")
        column = control_info.get("column")
        column_path = control_info.get("path", [])
        self_referential = control_info.get("self_referential", False)
        column_model = column.model
        data_model_node = lemon_model_node(data_model._meta.table_name)
        column_node = lemon_model_node(column_model._meta.table_name)
        model_node = data_model_node if with_join else column_node
        is_many, last_path, data_query = build_join_info(
            model_node, column_node, path=column_path,
            lemon_association=lemon_association,
            lemon_model_node=lemon_model_node)
        # self_referential_path = control_obj.data_source.get(
        #     "association", {}).get("path", [])
        self_referential_path, frontref = calc_self_referential_path_join(
            column_model, control_obj)
        # data_query = build_join(
        #     column_node, column_node, path=self_referential_path,
        #     auto_join=True, query=data_query)
        if with_join:
            # 这里就应该查询 关联模型 的 id (column_model.id)
            # 这样才能正确处理 关联的级联数据有多条的问题
            # 还需要加上外键不为 None 的条件
            # 因为外键为 None 时，关联表没有数据，但当前表有数据
            # 所以 join 的时候，会出现一条关联表 id 为 None 的数据
            if new_node_type is None:
                data_query = data_query.select(
                    column_model.id,
                    column_model.id.alias("value"),
                    frontref.alias("pid"),
                    column.alias("name")
                ).where(last_path.frontref.is_null(False))
            else:
                data_query = data_query.select(
                    column_model.id,
                    column_model.id.alias("value"),
                    frontref.alias("pid"),
                    column.alias("name")
                )
        else:
            # 当输入组件联动时，不需要 with_join 查询的是 关联模型的 id
            data_query = column_model.select(
                column_model.id,
                column_model.id.alias("value"),
                frontref.alias("pid"),
                column.alias("name")
            )
        self_referential_query_dict.update({
            field_uuid: {
                "query": data_query,
                "is_many": is_many,
                "frontref": frontref,
                "edit_column": column,
                "control_obj": control_obj,
                "self_referential": self_referential
            }})
    return self_referential_query_dict


def is_self_referential_field(column, path, model=None, show_level_type=True):
    if show_level_type is None:
        return False
    if path and isinstance(path, list):
        column_model = model or column.model
        last_path = path[-1]
        relation_result = lemon_relationship(last_path)
        if relation_result:
            source_model = relation_result[0]
            self_referential = relation_result[3]
            # 系统表column_model是拷贝过的
            source_model = lemon_model(source_model._meta.table_name)
            if column_model == source_model and self_referential:
                return True
    return False


def select_aggregation(agg_type, select_field):
    if agg_type == AggregationFunc.SUM:
        return fn.SUM(select_field)
    elif agg_type == AggregationFunc.AVG:
        return fn.AVG(select_field)
    elif agg_type == AggregationFunc.MAX:
        return fn.MAX(select_field)
    elif agg_type == AggregationFunc.MIN:
        return fn.MIN(select_field)
    elif agg_type == AggregationFunc.COUNT:
        return fn.COUNT(select_field)
    else:
        pass


def select_aggrefunc(agg_type, select_field, separator=","):
    if agg_type == AggreFunc.SUM:
        return fn.SUM(select_field)
    elif agg_type == AggreFunc.AVG:
        return fn.AVG(select_field)
    elif agg_type == AggreFunc.MAX:
        return fn.MAX(select_field)
    elif agg_type == AggreFunc.MIN:
        return fn.MIN(select_field)
    elif agg_type == AggreFunc.COUNT:
        return fn.COUNT(select_field)
    elif agg_type == AggreFunc.CONCAT:
        return fn.GROUP_CONCAT(NodeList((select_field, SQL(f"SEPARATOR '{separator}'"))))
    else:
        pass


def get_agg_field(field_lemon, agg_func=None, middle_model=None, separator=","):
    if hasattr(field_lemon, "aggre_function"):
        field_agg = getattr(field_lemon, "aggre_function")
    else:
        field_agg = None
    if field_agg:
        parm_field_uuid = field_agg.get("params")[0]
        condition_field = lemon_field(parm_field_uuid)
        calc = field_agg.get("calc")
        agg_field = select_aggregation(calc, condition_field)
        if not agg_field:
            return condition_field
        else:
            return agg_field
    else:
        if agg_func is not None:
            # agg_field = select_aggrefunc(agg_func, field_lemon, separator)
            # if agg_field:
            #     return agg_field
            # else:
            return python_agg_field(field_lemon, middle_model=middle_model)
        else:
            return field_lemon


def build_save_dir(*arg):
    save_dir = "/" + engine.config.MIDDLE_USER_UUID + "/" + "/".join(list(arg))
    return save_dir


class CurrentEnv:

    _env = None
    _env_set = False
    _storage = None
    _storage_set = False

    @property
    def name(self):
        if self._env_set:
            return self._env
        app_env = engine.config.APP_ENV_NAME
        if app_env == "pro":
            self._env = "正式环境"
        elif app_env == "test":
            self._env = "测试环境"
        elif "sandbox" in app_env:
            self._env = "开发环境"
        else:
            self._env = "未知环境"
        self._env_set = True
        return self._env

    @property
    def storage(self):
        if self._storage_set:
            return self._storage
        if engine.config.LOCAL_DEPLOY:
            self._storage = 1
        else:
            self._storage = 0
        self._storage_set = True
        return self._storage


def load_wrapper(data_dict):
    return {key: load_wrapper(value) if isinstance(value, dict) else value for key, value in data_dict}


class KafkaConsumer():

    separator = "-"
    default_config_kwargs = {
        'auto.offset.reset': 'earliest',
        'enable.auto.commit': 'true',
    }

    def __init__(self, *topics, with_prefix=True, **kwargs):
        self.general_prefix = self.init_general_prefix(with_prefix)
        config_kwargs = self.init_config_kwargs(**kwargs)
        self.consumer = Consumer(**config_kwargs)
        topics = self.init_topics(*topics)
        if topics:
            self.consumer.subscribe(topics)

    def init_general_prefix(self, with_prefix=True):
        general_prefix = ""
        if with_prefix:
            app_uuid = engine.config.APP_UUID
            app_env = os.getenv("APP_ENV_NAME", "").rstrip(";")
            env = os.getenv("REAL_ENV_NAME", app_env).rstrip(";") or "pro"
            tenant_uuid = ""
            current_user = LemonContextVar.current_user.get()
            if current_user:
                tenant_uuid = current_user.tenant_uuid
            general_prefix = build_general_prefix(app_uuid, env, tenant_uuid)
        return general_prefix

    def init_config_kwargs(self, **kwargs):
        group_id = kwargs.get("group.id", "group1")
        if self.general_prefix:
            group_id = "-".join([self.general_prefix, group_id])
        config_kwargs = {
            'bootstrap.servers': ",".join(engine.config.KAFKA_HOSTS),
            'group.id': group_id
        }
        config_kwargs.update(self.default_config_kwargs)
        for k, v in kwargs.items():
            if k in self.default_config_kwargs:
                config_kwargs[k] = v
        app_log.info(f"consumer config: {config_kwargs}")
        return config_kwargs

    def init_topics(self, *topics):
        if self.general_prefix:
            topics = [self.separator.join([
                self.general_prefix, t]) for t in topics]
        else:
            topics = list(topics)
        app_log.info(f"consumer topics: {topics}")
        return topics

    def __getattr__(self, key):
        return getattr(self.consumer, key)

    def __iter__(self):
        return ConsumerIterator(self)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.consumer.close()


class ConsumerIterator(Iterator):

    def __init__(self, consumer: KafkaConsumer):
        self.consumer = consumer

    def __next__(self):
        return self.consumer.poll(1)


def async_sleep(seconds):
    asyncio.run_coroutine_threadsafe(
        asyncio.sleep(seconds), engine.loop).result()


async def _async_add_task(delay, func, *args, **kwargs):
    if isinstance(delay, (int, float)):
        await asyncio.sleep(delay)
    await _run_in_atomic(func, *args, **kwargs)


def async_add_task_later(delay, func, *args, **kwargs):
    fut = asyncio.run_coroutine_threadsafe(
        _async_add_task(delay, func, *args, **kwargs),
        engine.loop)
    container = find_container_by_current_lsm()
    container.add_task(fut)


def async_add_task(func, *args, **kwargs):
    return async_add_task_later(None, func, *args, **kwargs)
