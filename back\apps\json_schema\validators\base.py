from jsonschema import Draft202012Validator

from jsonschema.protocols import Valida<PERSON>
from jsonschema.exceptions import ValidationError
from loguru import logger
from typing import Any, TypeVar, Callable, List
from apps.json_schema.monkey import create
from apps.json_schema.keywords import KEYWORDS_STORE
from apps.json_schema.utils import ReferenceData
from collections import OrderedDict


F = Callable[[Validator, Any, Any, Any], Any]


class ValidatorCtx:
    def __init__(self):
        self.project_data = {}
        self.document_data = {}


class BaseValidator:
    def __init__(self, app_ctx, version: str = '1.0') -> None:
        self._validator = None
        self.version = version
        self.validators = dict()
        self.schema = None
        self.resolver = None
        self.app_ctx = app_ctx
        self.docment_type = None

    @property
    def validator(self) -> Validator:
        if not self._validator:
            self.compile()
        return self._validator

    def compile(self):
        validator_cls = Draft202012Validator
        # self._validator = extend(validator_cls, self.validators)
        # return
        all_validators = dict(validator_cls.VALIDATORS)
        all_validators.update(self.validators)
        all_validators.update(KEYWORDS_STORE)
        validator = create(
            meta_schema=validator_cls.META_SCHEMA,
            validators=all_validators,
            version=self.version,
            type_checker=validator_cls.TYPE_CHECKER,
            format_checker=validator_cls.FORMAT_CHECKER,
            id_of=validator_cls.ID_OF,
        )
        self._validator = validator(self.schema, resolver=self.resolver, app_ctx=self.app_ctx)
        self._validator.doc_type = self.docment_type

    def validate(self, data: Any):
        return self.validator.iter_errors(data)

    @property
    def references(self) -> List[ReferenceData]:
        if not self._validator:
            return []
        return self._validator.doc_ctx.reference_list
