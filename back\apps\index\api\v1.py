# -*- coding:utf-8 -*-
import os
import asyncio
import re
import uuid
import random
import traceback
from datetime import datetime
import time
from aiohttp import ClientSession
import ujson
from urllib.parse import quote
from io import BytesIO
from aiohttp import FormData
from requests_toolbelt.multipart.encoder import MultipartEncoder
from PIL import Image
from operator import itemgetter
from sanic.response import HTT<PERSON>esponse
from base64 import b64encode, b64decode
from sanic import Blueprint
from sanic.response import json, redirect
from sanic.views import HTTPMethodView
from sanic_openapi import doc
from tools.settings import LocalConfig
from baseutils.log import app_log
from apps.utils import LemonDictResponse as LDR, replace_uuid_in_dict
from apps.utils import process_args, check_lemon_name, lemon_uuid, get_dominant_color, check_user_policy, get_login_user_uuid
from baseutils.const import Code, DOC, PublishPermission
from apps.local_ext_config import WeChartConfig
from apps.ide_const import DocumentType, IDECode, ImageType, ImageStorageType, ModelType, ModuleType, \
    NodeSystemActionType, WorkflowStatus, WxMediaType, AuditStatusType
from apps.entity import APPSetting, Document, DocumentContent, ExtPolicy, APP, Combine, ModelBasic, ModelField, Module, \
    Navigation, NavigationItem, Page, Policy, Print, LabelPrint, Publisher, RelationshipBasic, Sandbox, SandboxUser, UserRole, \
    UserRoleContent, Workflow, AuthorizedWX, SandboxUser
from apps.engine import engine
from apps import exceptions as lemon_exceptions
from apps.services.publish import PublishService
from apps.services.document.page import pin_machine_and_variables
from apps.permission import Permission
from apps.index.const import API, DefaultDocument
from apps.helper import DesignOss


API_NAME_VERSION = "_".join([API.NAME, API.V1])
url_prefix = "/api/" + "/".join([API.NAME, API.V1])
bp = Blueprint(API_NAME_VERSION, url_prefix=url_prefix)


class GetAPP(HTTPMethodView):
    class GetAPPObj:
        app_uuid = doc.String("app uuid")

    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用信息")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")

        result = await engine.access.get_app_by_app_uuid_join_combine(app_uuid, real_user_uuid)
        await self.process_publish_db(app_uuid, result)
        app_publish_info = await self.get_app_publish_info(app_uuid)
        result.update(app_publish_info)
        publisher_info = await engine.access.get_app_publisher(result.get("user_uuid"))
        publisher_name = None
        if publisher_info:
            publisher_name = publisher_info.get("name")
        bind_wx = 1 if await engine.access.get_bind_wx_by_app_uuid(app_uuid) else 0
        result.update({"publisher_name": publisher_name, "bind_wx": bind_wx})
        return json(LDR(data=result))

    async def process_publish_db(self, app_uuid, result):
        publish_group = await engine.access.list_app_publish_group(app_uuid)
        publish_group = list(publish_group)
        publish_group.insert(0, {
            "group_name": "默认分组",
            "group_uuid": result.get("middle_user")
        })
        result.update({"publish_group": publish_group})
        app_log.info([type(publish_group), publish_group])

    async def get_app_publish_info(self, app_uuid):
        app_publish_info = {
            "lemon_version": "",
            "app_version": "",
            "app_revision": "",
            "version_number": "",
            "version_time": "",
            "lemon_version_test": "",
            "app_version_test": "",
            "app_revision_test": "",
            "test_version_number": "",
            "test_version_time": ""
        }
        info_list = await engine.access.list_app_publish_info(app_uuid)
        for info in info_list:
            publish_info = {}
            if info.get("environment") == "pro":
                for k, v in info.items():
                    if k in app_publish_info:
                        publish_info[k] = v
            elif info.get("environment") == "test":
                publish_info.update({
                    "lemon_version_test": info["lemon_version"],
                    "app_version_test": info["app_version"],
                    "app_revision_test": info["app_revision"],
                    "test_version_number": info["version_number"],
                    "test_version_time": info["version_time"]
                })
            app_publish_info.update(publish_info)
        return app_publish_info


class ListAPP(HTTPMethodView):

    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用")
    async def post(self, request):
        real_user_uuid, user_uuid = get_login_user_uuid(request)
        if user_uuid == real_user_uuid:
            result = await engine.access.list_app_with_editor(user_uuid, hide_when_design=True)
            combine_app = await engine.access.list_app_by_combine_user(
                [user_uuid], is_owner=0, hide_when_design=True, team_uuid=None)
            list_app = list(result)
            dic_app = {r["app_uuid"]: r for r in result}
            for c in combine_app:
                if c["app_uuid"] in dic_app:
                    dic_app[c["app_uuid"]]["is_owner"] = 1
                else:
                    # 重复可能是扩展用户导致,给编辑权限
                    c["is_owner"] = 1
                    list_app.append(c)
        else:
            result = await engine.access.list_app_by_combine_user(
                [real_user_uuid], hide_when_design=True, team_uuid=user_uuid)
            list([r.update({"is_owner": 1}) for r in result])
            list_app = result
        return json(LDR(data=list(list_app)))


class ListAppManage(HTTPMethodView):
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用管理")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
        order_by_query = APP.create_time.desc()
        team_uuid = None if real_user_uuid == user_uuid else user_uuid
        combiner_list = await engine.access.list_combine_by_user_uuid(
            real_user_uuid, is_owner=None, team_uuid=team_uuid, team_must_null=True)
        app_uuid_list, combiner_dic = list(), dict()
        for combiner in combiner_list:
            app_uuid_ = combiner["app_uuid"]
            app_uuid_list.append(app_uuid_)
            combiner_dic.update({app_uuid_: combiner})

        app_uuid_list = [x["app_uuid"] for x in combiner_list]
        apps_list = await engine.access.list_app_by_app_uuid_list(
            app_uuid_list, order_by_query=order_by_query, count_combine=True)
        for app_ in apps_list:
            this_combine = combiner_dic.get(app_["app_uuid"], {})
            app_["user_is_owner"] = this_combine.get("is_owner", False)
            app_["visible"] = this_combine.get("visible", True)
        apps_list = list(apps_list)
        # apps_list.sort(key=itemgetter("user_is_owner", "status", "visible", "create_time"), reverse=True)        
        apps_list.sort(key=itemgetter("user_is_owner", "create_time"), reverse=True)
        return json(LDR(data=apps_list))


class ListAppCombiner(HTTPMethodView):
    class ListAppCombinerObj(object):
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ListAppCombinerObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用协作者")
    @check_user_policy(policy_type=1)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
        combiner_list = await engine.access.list_combine_by_app_uuid_join_user(
            app_uuid, is_owner=0, hide_mobile=True)
        app_log.info(combiner_list)
        return json(LDR(data=list(combiner_list)))


class AddAppCombiner(HTTPMethodView):
    class AddAppCombinerObj(object):
        app_uuid = doc.String("应用UUID")
        combiner_list = doc.List("应用UUID")

    @doc.consumes(AddAppCombinerObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("新增应用协作者")
    @check_user_policy(policy_type=1)  # TODO 团队应用添加
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            combiner_list = request.json.get("combiner_list")
            permission = request.json.get("permission", 0)
            team_uuid = request.json.get("team_uuid")
        exists_combiner = await engine.access.list_combine_by_user_uuid(combiner_list, app_uuid=app_uuid,
                                                                        need_delete=True)
        deleted_combiner = real_update_combiner = {x.get("user_uuid") for x in exists_combiner if x.get("is_delete")}
        real_add_combiner = set(combiner_list) - {x.get("user_uuid") for x in exists_combiner}
        app_log.info(f"real_update_combiner: {real_update_combiner}")
        app_log.info(f"real_add_combiner: {real_add_combiner}")
        insert_data = [{Combine.app_uuid.name: app_uuid,
                        Combine.user_uuid.name: x,
                        Combine.is_owner.name: False,
                        Combine.visible.name: True,
                        Combine.permission.name: permission,
                        Combine.team_uuid.name: team_uuid} for x in real_add_combiner]
        result = []
        if insert_data:
            await engine.access.insert_many_obj(Combine, insert_data)
            result = insert_data
        real_update_combiner = list(real_update_combiner)
        if real_update_combiner:
            need_update_combiner = [x for x in exists_combiner if x.get("is_delete")]
            await engine.access.bulk_update_combine(need_update_combiner, need_delete=True,
                                                    **{"is_delete": False, "visible": True})
            result.extend([x for x in exists_combiner if x.get("is_delete")])
        return json(LDR(data=result))


class DelAppCombiner(HTTPMethodView):
    class DelAppCombinerObj(object):
        app_uuid = doc.String("应用UUID")
        combiner_list = doc.List("应用UUID")

    @doc.consumes(DelAppCombinerObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除应用协作者")
    @check_user_policy(policy_type=1)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            combiner_list = request.json.get("combiner_list")
        exists_combiner = await engine.access.list_combine_by_user_uuid(combiner_list, app_uuid=app_uuid,
                                                                        is_owner=False)
        await engine.access.bulk_update_combine(exists_combiner, **{"is_delete": True, "visible": True})
        return json(LDR(data=list(exists_combiner)))


class ExitAppCombiner(HTTPMethodView):
    class ExitAppCombinerObj(object):
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ExitAppCombinerObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("推出写作")
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
        old_combiner = await engine.access.list_combine_by_user_uuid(user_uuid, is_owner=0, app_uuid=app_uuid)
        old_combiner = list(old_combiner)
        if not old_combiner:
            return json(LDR(Code.ERROR))
        old_combiner = old_combiner[0]
        model = Combine
        data = {
            model.is_delete.name: True,
            model.visible.name: True
        }
        await engine.access.update_combine(app_uuid=app_uuid, user_uuid=user_uuid, **data)
        return json(LDR(Code.OK))


class ListRecentDeleteApp(HTTPMethodView):
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出近期被删APP")
    @check_user_policy(policy_type=1)
    async def post(self, request):
        # TODO 协作者的相关修改,可能需要重新写
        real_user_uuid, user_uuid = get_login_user_uuid(request)
        order_by_query = APP.delete_time.desc()
        time_limit = 30 * 24 * 3600
        result = await engine.access.list_app_by_user_uuid(user_uuid, order_by_query=order_by_query,
                                                           time_limit=time_limit)
        list_app = list(result)
        return json(LDR(data=list_app))


class RecoverDeletedApp(HTTPMethodView):
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("恢复近期被删APP")
    @check_user_policy(policy_type=1)
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
        time_limit = 30 * 24 * 3600
        earliest_delete_time = time.time() - time_limit
        app_info = await engine.access.get_app(**{"app_uuid": app_uuid, "is_delete": 1})
        if not app_info:
            return json(LDR(Code.APP_NOT_EXISTS))
        if app_info.delete_time < earliest_delete_time:
            return json(LDR(Code.APP_NOT_EXISTS))
        await engine.access.update_app(app_uuid, **{"is_delete": 0})
        return json(LDR(Code.OK))


class HideAppInDesign(HTTPMethodView):
    class HideAppInDesignObj:
        app_uuid = doc.String("应用id")
        visible = doc.Boolean("是否可见")

    @doc.consumes(HideAppInDesignObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("设置该用户App在设计时是否可见")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            visible = request.json.get("visible")  # 是否可见
        combine = await engine.access.list_combine_by_user_uuid(real_user_uuid, app_uuid=app_uuid)
        if combine:
            combine = list(combine)[0]
            await engine.access.update_combine(app_uuid, real_user_uuid, **{"visible": visible})
        return json(LDR(Code.OK))


class ListAPPUserPolicy(HTTPMethodView):
    class ListAPPPolicyObj:
        app_uuid = doc.String("应用id")

    @doc.consumes(ListAPPPolicyObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用用户权限角色")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            if app_uuid is None:
                return json(LDR(Code.ARGS_ERROR))
        result = await engine.access.list_app_policy_by_user(app_uuid, user_uuid)
        result = list(result)
        return json(LDR(data=result))


class UploadAppIcon(HTTPMethodView):
    class UploadAppIconObj():
        image_type = doc.Integer("图片类型（SVG必须） 1：JPG 2：PNG 3：BMP 4：GIF 5：SVG", name="image_type")
        image = doc.File("图片", name="image")
        dominant = doc.Integer("图标主色数，默认0", name="dominant")

    @doc.consumes(UploadAppIconObj.image, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(UploadAppIconObj.image_type, content_type=DOC.FORM_DATA, location="formData")
    @doc.consumes(UploadAppIconObj.dominant, content_type=DOC.FORM_DATA, location="formData")
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("上传应用图标")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            image_type = int(request.form.get("image_type", ImageType.JPG))
            dominant = int(request.form.get("dominant", 0))
            image = request.files.get("image")
        image_obj = None
        image_body = image.body
        image_width, image_height = 0, 0
        if image_type != ImageType.SVG:
            image_obj = Image.open(BytesIO(image_body))
            image_width = image_obj.width
            image_height = image_obj.height
            image_format = image_obj.format
            image_type = ImageType.TYPE_TO_DICT.get(image_format, ImageType.JPG)
        image_uuid = lemon_uuid()
        image_path = self.gen_image_path(image_uuid, image_type)

        # 处理存OSS
        headers = {
            "x-oss-meta-filetype": "image"
        }
        await DesignOss().put_oss_object(
            image_path, image_body, headers, local_file=False)
        image_create_dict = {
            "width": image_width,
            "height": image_height,
            "url": image_path,
            # "s_url": image_path,
            "dominant": []
        }

        if dominant and image_type != ImageType.SVG:
            colors = get_dominant_color(image_obj, numcolors=dominant)
            colors = ["#" + "".join([hex(x)[2:] if x > 15 else f"0{hex(x)[2:]}" for x in color]) for color in colors]
            image_create_dict.update({"dominant": colors})
        return json(LDR(data=image_create_dict))

    def gen_image_path(self, image_uuid, image_type):
        image_type_str = ImageType.DICT.get(image_type)
        image_whole_name = image_uuid + image_type_str
        return "/".join(["design", "icon", image_whole_name])


class CreateAPP(HTTPMethodView):
    class CreateAPPObj(object):
        app_name = doc.String("应用名称")
        app_description = doc.String("应用描述")
        app_color = doc.String("应用颜色")
        app_icon = doc.String("应用图标")
        version_control = doc.Integer("版本控制")

    @doc.consumes(CreateAPPObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("新建应用")
    async def post(self, request):
        real_user_uuid, user_uuid = get_login_user_uuid(request)
        app_log.info(user_uuid)
        with process_args():
            is_default_app = request.json.get("default_app", False)
            need_modules_advice: bool = request.json.get("need_advice", False)
            version_control = request.json.get("version_control", 0)
            if is_default_app:
                user_name = request.json.get("user_name", False)
                app_uuid = request.json.get("app_uuid")
                async with engine.db.objs.atomic():
                    await self.create_default_app(user_uuid, user_name, app_uuid)
                return json(LDR(IDECode.OK))
            else:
                app_name = str(request.json.get("app_name", ""))
                # 避免使用了默认应用想使用的name
                # 这样做的原因是创建默认app时, 不想查表去找一个没使用过的app_name
                # TODO 但这样并不能绝对保证 app_name可用
                # 且缺少重名之后的重新寻找名字的措施
                if app_name.endswith("示例应用"):
                    return json(LDR(IDECode.APP_NAME_ENDS_ERROR))
            app_name = str(request.json.get("app_name", ""))
            app_description = str(request.json.get("app_description", ""))
            app_color = str(request.json.get("app_color", ""))
            app_icon = request.json.get("app_icon", "")
            app_name = check_lemon_name(app_name, chinese=True)
            if not app_name:
                return json(LDR(IDECode.APP_NAME_FAILED))
        data = dict(app_name=app_name)
        app = await engine.access.get_app(**data)
        if app:
            return json(LDR(Code.APP_NAME_EXISTS))

        app_uuid = lemon_uuid()
        app_dict = dict(
            app_uuid=app_uuid,
            app_name=app_name,
            app_description=app_description,
            app_color=app_color,
            app_icon=app_icon
        )
        if not app_color:
            primary_color = "#1890ff"
        else:
            primary_color = app_color
        theme_content = {}
        theme_content.update(DefaultDocument.THEME)
        theme_content.update({"primary-color": primary_color})
        data = {
            "app_uuid": app_uuid, 
            "module_uuid": "", 
            "document_puuid": "", 
            "document_path": "", 
        }
        main_module_uuid = lemon_uuid()
        async with engine.db.objs.atomic():
            await engine.access.create_app(user_uuid, **app_dict)
            await self.create_document(user_uuid, data, theme_content, main_module_uuid)
            await engine.service.query.create_module(
                app_uuid, "主模块", user_uuid, module_uuid=main_module_uuid)
            if real_user_uuid != user_uuid:
                await self.create_team_app_combiner(app_uuid, real_user_uuid, user_uuid)
                await self.create_sandbox(app_uuid, real_user_uuid)
        if version_control == 1:
            asyncio.create_task(self.create_git(app_uuid, user_uuid, request.ctx.session.sid))
        if need_modules_advice:
            from gpt_helper.const import GCTaskType
            from gpt_helper.utils.task_utils import publish_task
            await publish_task(app_uuid, task_type=GCTaskType.APP_MODULE_LIST, input_=app_description)
        return json(LDR(data=app_dict))

    async def create_git(self, app_uuid, user_uuid, sid):
        json_body = {
            "app_uuid": app_uuid,
            "repo_type": 0,
            "git_host": engine.config.GITLAB_HOST,
            "sid": sid,
            "user_uuid": user_uuid
        }
        await engine.git_request.post("create_project.json", json_body)

    async def create_sandbox(self, app_uuid, user_uuid):
        sandbox_uuid = lemon_uuid()
        data = {
            "sandbox_uuid": sandbox_uuid,
            "app_uuid": app_uuid,
            "sandbox_name": "默认沙箱"
        }
        await engine.access.create_obj(Sandbox, **data)

        sandbox_user_data = {
            "sandbox_uuid": sandbox_uuid,
            "user_uuid": user_uuid
        }
        await engine.access.create_obj(SandboxUser, **sandbox_user_data)

    async def create_team_app_combiner(self, app_uuid, user_uuid, team_uuid):
        data = {
            Combine.app_uuid.name: app_uuid,
            Combine.user_uuid.name: user_uuid,
            Combine.is_owner.name: False,
            Combine.visible.name: True,
            Combine.permission.name: 0,  # 可见可编辑 
            Combine.team_uuid.name: team_uuid,
            Combine.publish_permission.name: PublishPermission.PRO + PublishPermission.TEST
        }
        await engine.access.create_obj(Combine, **data)

    async def create_document(self, user_uuid, data, content, main_module_uuid):
        document_type_to_name = {
            DocumentType.NAVIGATION: "导航",
            DocumentType.APP_SECURITY: "应用安全",
            DocumentType.THEME: "主题配置",
            DocumentType.DEPLOY_CONFIG: "部署配置",
            DocumentType.WATERMARK: "水印设置"
        }
        document_type_to_content = {
            DocumentType.NAVIGATION: {"navigations": []},
            DocumentType.APP_SECURITY: {
                "user_role_list": [{
                    "uuid": lemon_uuid(),
                    "name": "系统管理员",
                    "permission": [
                        {
                            "content": list(),
                            "module_uuid": main_module_uuid,
                            "name": "主模块"
                        }
                    ],
                    "is_admin": True,
                }],
                "app_setting": {
                    "anonymous": False,
                    "anonymous_role": "",
                    "permission_check": False
                }
            },
            DocumentType.THEME: content,
            DocumentType.DEPLOY_CONFIG: {
                "status": 0, "local_config": LocalConfig.local_config_default},
            DocumentType.WATERMARK: {"show": False}
        }

        for document_type in [
                DocumentType.NAVIGATION, DocumentType.APP_SECURITY,
                DocumentType.THEME, DocumentType.DEPLOY_CONFIG,
                DocumentType.WATERMARK]:
            document_uuid = lemon_uuid()
            document_name = document_type_to_name.get(document_type)
            data.update({
                "document_uuid": document_uuid,
                "document_name": document_name,
                "document_version": 0,
            })
            document_content = document_type_to_content.get(document_type)
            await self._create_document(user_uuid, document_type, data, document_content)

    async def _create_document(self, user_uuid, document_type, data, document_content):
        data.update({
            "document_type": document_type
        })
        await engine.access.create_document(user_uuid, **data)
        document_check_service = engine.service.document_check.create_service(document_type)
        data.update({
            "document_version": 1,
            "document_content": document_content
        })
        if document_check_service:
            await document_check_service.check(data, document_type, force_in_current_proc=True)

    async def create_default_app(self, user_uuid, user_name, app_uuid):
        template_app_uuid = engine.config.TEMPLATE_APP_UUID
        create_time = int(time.time())
        uuid_template_to_app = {template_app_uuid: app_uuid}
        uuid_template_to_app.update({k: k for k in pin_machine_and_variables})
        uuid_template_to_app.update({k: k for k in NodeSystemActionType.ALL_EVENT.keys()})

        search_func_list = [
            engine.access.get_app_by_app_uuid(template_app_uuid),
            engine.access.get_app_setting(template_app_uuid),
            engine.access.list_user_role_by_app_uuid(
                template_app_uuid, fields=UserRole._meta.sorted_fields),
            engine.access.list_user_role_content_by_app_uuid(
                template_app_uuid, fields=UserRoleContent._meta.sorted_fields),
            engine.access.list_module_by_app_uuid_module_type(
                template_app_uuid, module_type=None, need_all_fields=True),
            engine.access.list_document_by_app_uuid_document_type(
                template_app_uuid, document_type=None,
                fields=Document._meta.sorted_fields, order_by_expr=Document.id),
            engine.access.list_document_content_by_app_uuid_document_type(
                template_app_uuid, document_type=None, only_content=True,
                fields=DocumentContent._meta.sorted_fields),
            engine.access.list_app_policy(template_app_uuid),
            engine.access.list_combine_by_app_uuid(template_app_uuid),
            engine.access.list_page_by_app_uuid(
                template_app_uuid, fields=Page._meta.sorted_fields),
            engine.access.list_model_basic(template_app_uuid),
            engine.access.list_model_field(template_app_uuid),
            engine.access.list_relationship_basic(template_app_uuid),
            engine.access.list_navigation_by_app_uuid(template_app_uuid),
            engine.access.list_navigationitem(
                template_app_uuid, fields=NavigationItem._meta.sorted_fields),
            engine.access.list_print_by_app_uuid(
                app_uuid=template_app_uuid, fields=Print._meta.sorted_fields),
            engine.access.list_label_print_by_app_uuid(
                app_uuid=template_app_uuid, fields=Print._meta.sorted_fields),
            engine.access.list_workflow_by_app_uuid(
                app_uuid=template_app_uuid, fields=Workflow._meta.sorted_fields)
        ]
        result_list = []
        for func in search_func_list:
            result_list.append(await func)
        app_info, app_setting, user_role, user_role_content, modules, document, \
            document_content, policy, combine, page, model_basic, model_field, relationship_basic, \
            navigation, navigation_item, print, label_print, workflow = result_list
        template_user_uuid = app_info.user_uuid
        uuid_template_to_app.update({template_user_uuid: user_uuid})
        app_info, app_setting = [app_info.to_dict()], [app_setting.to_dict()]

        infos = {
            "app_info": {"info": list(app_info), "model": APP},
            "app_setting": {"info": list(app_setting), "model": APPSetting},
            "user_role": {"info": list(user_role), "model": UserRole},
            "user_role_content": {"info": list(user_role_content), "model": UserRoleContent},
            "modules": {"info": list(modules), "model": Module},
            "document": {"info": list(document), "model": Document},
            "document_content": {"info": list(document_content), "model": DocumentContent},
            "policy": {"info": list(policy), "model": Policy},
            "combine": {"info": list(combine), "model": Combine},
            "page": {"info": list(page), "model": Page},
            "model_basic": {"info": list(model_basic), "model": ModelBasic},
            "model_field": {"info": list(model_field), "model": ModelField},
            "relationship_basic": {"info": list(relationship_basic), "model": RelationshipBasic},
            "navigation": {"info": list(navigation), "model": Navigation},
            "navigation_item": {"info": list(navigation_item), "model": NavigationItem},
            "print": {"info": list(print), "model": Print},
            "label_print": {"info": list(label_print), "model": LabelPrint},
            "workflow": {"info": list(workflow), "model": Workflow}
        }
        self.process_workflow_versions(infos)

        for name, value in infos.items():
            value: dict
            model_infos: list = value.get("info", [])
            for one_line_info in model_infos:
                one_line_info.pop("id", None)
                replace_uuid_in_dict(one_line_info, uuid_template_to_app)
                if name == "app_info":  # app_info保证只有一个值
                    one_line_info["app_name"] = user_name + "的示例应用"
                    one_line_info["middle_user"] = user_uuid
                for k in ["create_time", "create_timestamp", "update_time", "update_timestamp"]:
                    if one_line_info.get(k):
                        one_line_info[k] = create_time
                if one_line_info.get("document_version"):
                    one_line_info["document_version"] = 1
            model_entity = value.get("model")
            if model_infos:
                await engine.access.insert_many_obj(model_entity, model_infos)

    def process_workflow_versions(self, infos):
        document = infos.get("document", {})
        document_content = infos.get("document_content", {})
        document_info = document.get("info", [])
        document_content_info = document_content.get("info", [])
        wf_document_info = [x for x in document_info
                            if x.get("document_type") == DocumentType.WORKFLOW]
        wf_document_uuids = [x.get("document_uuid") for x in wf_document_info]
        wf_document_content_info = [x for x in document_content_info if x.get("document_uuid") in wf_document_uuids]
        app_log.info(wf_document_content_info)
        for info in wf_document_content_info:
            dc = info.get("document_content", {})
            online_version = dc.get("online_version", "")
            dc["show_version"] = online_version
            versions = dc.get("versions", [])
            new_versions = [x for x in versions if x["uuid"] == online_version]  # 应该只有一个
            for v in new_versions:
                v["status"] = 0  # 草稿
                v["version"] = 1  # 认为是第一个版本
            dc["versions"] = new_versions


class UpdateApp(HTTPMethodView):
    class updateappobj(object):
        app_uuid = doc.String("app uuid")
        app_name = doc.String("应用名称")
        app_description = doc.String("应用描述")
        app_color = doc.String("应用颜色")
        app_icon = doc.String("应用图标")

    @doc.consumes(updateappobj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("修改应用")
    async def post(self, request):
        real_user_uuid, user_uuid = get_login_user_uuid(request)
        with process_args():
            app_uuid = request.json.get("app_uuid")
            app_name = str(request.json.get("app_name", ""))
            app_description = str(request.json.get("app_description", ""))
            app_color = str(request.json.get("app_color", ""))
            app_icon = request.json.get("app_icon", "")
            app_name = check_lemon_name(app_name, chinese=True)
            if app_name.endswith("示例应用"):
                return json(LDR(IDECode.APP_NAME_ENDS_ERROR))
            if not app_name or not app_uuid:
                return json(LDR(IDECode.APP_NAME_FAILED))
        data = dict(app_uuid=app_uuid)
        app = await engine.access.get_app(**data)
        if not app:
            return json(LDR(Code.APP_NOT_EXISTS))
        if app.app_name != app_name:
            data = dict(app_name=app_name)
            app_ = await engine.access.get_app(**data)
            if app_:
                return json(LDR(Code.APP_NAME_EXISTS))

        query = app.update(app_name=app_name, app_description=app_description,
                           app_color=app_color, app_icon=app_icon).where(app._pk_expr())
        await engine.db.objs.execute(query)
        return json(LDR(Code.OK))


class GetIconDominantColor(HTTPMethodView):
    class GetIconDominantColorObj:
        icon_url = doc.String("图标链接")

    @doc.consumes(GetIconDominantColorObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("修改应用")
    async def post(self, request):
        real_user_uuid, user_uuid = get_login_user_uuid(request)
        with process_args():
            icon_url = request.json.get("icon_url", "")
            if not icon_url:
                return json(LDR(Code.ARGS_ERROR))  
        request.args.update({"url": icon_url})
        object_stream = await DesignOss.get_oss_object(request)
        if not object_stream:
            return json(LDR(Code.ERROR))
        # TODO svg格式
        if object_stream:
            object_body = object_stream.body
            image_obj = Image.open(BytesIO(object_body))
            colors = get_dominant_color(image_obj)
        else:
            return json(LDR(Code.ICON_NOT_EXISTS))
        colors = ["#" + "".join([hex(x)[2:] if x > 15 else f"0{hex(x)[2:]}" for x in color]) for color in colors]
        return json(LDR(data=colors))


class GetResourceExtPolicy(HTTPMethodView):
    class GetRsourceExtPolicyObj:
        app_uuid = doc.String("app uuid")
        resource_id = doc.String("resource id, str or list")

    @doc.consumes(GetRsourceExtPolicyObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用资源拓展权限")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            resource_id = request.json.get("resource_id")
            if not app_uuid or not resource_id:
                return json(LDR(Code.ARGS_ERROR))
        if not (await engine.access.is_app_admin_or_combiner(app_uuid, user_uuid)):
            raise lemon_exceptions.PermissionRequired("Permission required")
        if isinstance(resource_id, str):
            resource_id = [resource_id]

        result = await engine.access.list_resource_ext_policy(app_uuid, resource_id)
        if not result:
            for r in resource_id:
                if r in ["navigation", "app-permission"]:
                    await engine.access.update_resource_ext_policy(app_uuid, resource_id, False, None)
            result = await engine.access.list_resource_ext_policy(app_uuid, resource_id)
        return json(LDR(data=list(result)))


class ListModuleExtPolicy(HTTPMethodView):
    class ListAPPDocumentPolicyObj:
        app_uuid = doc.String("app uuid")

    @doc.consumes(ListAPPDocumentPolicyObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用模块拓展权限")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            if app_uuid is None:
                return json(LDR(Code.ARGS_ERROR))
        if not (await engine.access.is_app_admin(app_uuid, user_uuid)):
            raise lemon_exceptions.PermissionRequired("Permission required")
        result = await engine.access.list_module_by_app_uuid(app_uuid, with_policy=True)
        data = dict(
            app_uuid=app_uuid,
            children=list(result)
        )
        return json(LDR(data=data))


class ListDocumentExtPolicy(HTTPMethodView):
    class ListDocumentExtPolicyObj:
        app_uuid = doc.String("应用id")
        module_uuid = doc.String("模块UUID")
        document_uuid = doc.String("文档UUID，为空表示列模块下的第一层目录")

    @doc.consumes(ListDocumentExtPolicyObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出模块或文件夹拓展权限")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            module_uuid = str(request.json.get("module_uuid"))
            document_uuid = str(request.json.get("document_uuid"))
            app_uuid = request.json.get("app_uuid")
            if app_uuid is None:
                return json(LDR(Code.ARGS_ERROR))
        if not module_uuid:
            return json(LDR(Code.ARGS_ERROR))
        if not (await engine.access.is_app_admin(app_uuid, user_uuid)):
            raise lemon_exceptions.PermissionRequired("Permission required")
        result = await engine.access.list_document_by_module_document_puuid(
            module_uuid, document_uuid, with_policy=True)
        data = dict(
            module_uuid=module_uuid,
            document_uuid=document_uuid,
            children=list(result)
        )
        return json(LDR(data=data))


class UpdateResourceExtPolicy(HTTPMethodView):
    class UpdateDocumentExtPolicyObj:
        app_uuid = doc.String("app uuid")
        resource_id = doc.String("resource id, str or list")
        visible = doc.Boolean("可见性")
        additional = doc.String('{"extendable": true, "new": [1,4]}')

    @doc.consumes(UpdateDocumentExtPolicyObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("修改文档拓展权限")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            resource_id = request.json.get("resource_id")
            visible = request.json.get("visible")
            additional = request.json.get("additional")
            if not app_uuid or not resource_id:
                return json(LDR(Code.ARGS_ERROR))
        if not (await engine.access.is_app_admin(app_uuid, user_uuid)):
            raise lemon_exceptions.PermissionRequired("Permission required")

        await engine.access.update_resource_ext_policy(app_uuid, resource_id, visible, additional)
        return json(LDR(Code.OK))


class GetWXPreAuthUrl(HTTPMethodView):
    class GetWXPreAuthUrlObj:
        redirect_uri = doc.String("回调链接")
        app_uuid = doc.String("app_uuid")
        auth_type = doc.Integer(
            "要授权的帐号类型：1 则商户点击链接后，手机端仅展示公众号、2 表示仅展示小程序，3 表示公众号和小程序都展示。如果为未指定，则默认小程序和公众号都展示")

    @doc.consumes(GetWXPreAuthUrlObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取微信预授权链接")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            redirect_uri = request.json.get("redirect_uri")
            auth_type = int(request.json.get("auth_type", 3))
            auth_type = 3
        # 取消缓存pre auth code, pre auth code被其他人错误的使用的情况需要申请新的
        # cache_id = "wx_pre_auth_code_" + app_uuid
        # pre_auth_code = await engine.redis.get_cache(cache_id)
        # if not pre_auth_code:
        result = await engine.wx_api.component_post(
            url="https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode")
        # result = await get_wx_pre_auth_code(engine.redis)
        if result.get("errcode"):
            app_log.error(f"get_wx_pre_auth_obj: {result}")
            return json(LDR(data=result))
        pre_auth_code = result.get("pre_auth_code")
        expires_in = result.get("expires_in")
        # await engine.redis.set_cache(cache_id, pre_auth_code, expiry=expires_in-120)
        app_log.info(pre_auth_code)
        redirect_uri = quote(redirect_uri, safe="")
        url = f"https://{WeChartConfig.WEB_DOMAIN}/get-weixin-code.html?appid={WeChartConfig.COMPONENT_APPID}&preAuthCode={pre_auth_code}&redirect_uri={redirect_uri}&authType={auth_type}&isPreAuth=1"
        data = {"url": url}
        return json(LDR(data=data))


class WXVerify(HTTPMethodView):
    async def get(self, request):

        from apps.utils import WXBehalfHelper
        wh = WXBehalfHelper()
        res = wh.verify_lemon_url(
            request.args.get("msg_signature"),
            request.args.get("timestamp"),
            request.args.get("nonce"),
            request.args.get("echostr"),
        )
        app_log.info(res)
        from sanic.response import text
        return text(res)

    async def post(self, request):
        from apps.utils import WXBehalfHelper
        wh = WXBehalfHelper()
        res = wh.decrypt_suite_info(
            request.args.get("msg_signature"),
            request.args.get("timestamp"),
            request.args.get("nonce"),
            request.body,
        )
        app_log.info(res)
        from sanic.response import text
        return text(res)


class AuthorizeWXAccess(HTTPMethodView):
    class AccessWXTokenObj:
        app_uuid = doc.String("app_uuid")
        auth_code = doc.String("授权码")
        confirmed = doc.String("客户是否已确认")
        
    @doc.consumes(AccessWXTokenObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("授权微信或小程序")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            auth_code = request.json.get("auth_code")
            confirmed = request.json.get("confirmed")
        # cache_id = "wx_pre_auth_code_" + app_uuid
        # await engine.redis.delete_cache(cache_id)
        result = await engine.wx_api.component_post(url="https://api.weixin.qq.com/cgi-bin/component/api_query_auth",
                                                    body={"authorization_code": auth_code})
        # result = await authorize_wx_access(auth_code, engine.redis)
        app_log.info(result)
        if result.get("errcode") and not confirmed:
            app_log.error(f"get_wx_pre_auth_code_error: {result}")
            return json(LDR(data=result))
        authorization_info = result.get("authorization_info")
        appid = authorization_info.get("authorizer_appid")
        app = await engine.access.get_app_by_app_uuid(app_uuid)
        authorizer_wx = await engine.access.get_authorizer_wx_app_by_appid(appid)
        if authorizer_wx and not confirmed:
            # 避免之前的refresh_token失效
            data = {"refresh_token": authorization_info.get("authorizer_refresh_token")}
            await engine.access.create_or_update_authorizer_wx_app(**data)
            exist_app = await engine.access.get_app_by_app_uuid(authorizer_wx.app_uuid)
            code = IDECode.WX_APPID_EXIST
            code.message = code.message.format(app_name=exist_app.app_name)
            return json(LDR(code))
        access_token = authorization_info.get("authorizer_access_token")
        func_info = authorization_info.get("func_info")  # 权限集
        # todo:校验权限集   公众号/小程序可以自定义选择部分权限授权给第三方平台，因此第三方平台开发者需要通过该接口来获取公众号/小程序具体授权了哪些权限
        expires_in = authorization_info.get("expires_in")
        resource_id = "wx_access_token_" + appid
        await engine.redis.set_cache(resource_id, access_token, expiry=expires_in, with_conf_prefix=False)
        data = dict(
            app_uuid=app_uuid,
            appid=appid,
            refresh_token=authorization_info.get("authorizer_refresh_token"),
            func_info=func_info,
            is_delete=False
        )
        #  设置小程序开发域名等
        # todo: 预生产域名
        result = await engine.wx_api.app_post(appid=appid, url="https://api.weixin.qq.com/wxa/modify_domain", body={
            "action": "set",
            "requestdomain": WeChartConfig.REQUEST_DOMAIN,
            "wsrequestdomain": WeChartConfig.WS_REQUEST_DOMAIN
        })
        app_log.info(result)
        result = await engine.wx_api.app_post(appid=appid, url="https://api.weixin.qq.com/wxa/setwebviewdomain", body={
            "action": "set",
            "webviewdomain": WeChartConfig.REQUEST_DOMAIN
        })
        app_log.info(result)
        if result["errcode"] == 89231:
            await engine.wx_api.app_post(appid=appid, url="https://api.weixin.qq.com/cgi-bin/open/unbind",
                                         body={"open_appid": WeChartConfig.COMPONENT_APPID})
            return json(LDR(IDECode.PERSIONAL_APPLET_NOT_SUPPORT))
        code_version = await engine.access.get_applet_code_version()
        result = await engine.wx_api.app_post(appid=appid, url="https://api.weixin.qq.com/wxa/commit", body={
            "template_id": code_version.template_id, "ext_json": ujson.dumps({
                "extAppid": appid,
                "ext": {
                    "name": app.app_name
                }
            }),
            "user_version": code_version.version, "user_desc": code_version.description
        })
        app_log.info(result)
        await engine.access.create_or_update_authorizer_wx_app(**data)
        return json(LDR(Code.OK))


class GetWxAllCategoryName(HTTPMethodView):
    class GetWxAllCategoryNameObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(GetWxAllCategoryNameObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取小程序类目名称信息")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None
        if appletid:
            result = await engine.wx_api.get_category(appletid)
        else:
            result = IDECode.APP_WX_NOT_BIND_EXIST
        return json(LDR(data=result))


class GetMaterial(HTTPMethodView):
    class GetMaterialObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(GetMaterialObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorzation", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.consumes("")
    @doc.tag(url_prefix)
    @doc.summary("获取永久素材")
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            media_id = request.json.get("media_id")

        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None
        if appletid:
            result = await engine.wx_api.get_material(appletid, media_id=media_id)
        else:
            result = IDECode.APP_WX_NOT_BIND_EXIST
        # with open("/home/<USER>/lemon/test.jpg", "wb") as f:
        #     f.write(b64decode(b64encode(result)))
        return json(LDR(data=b64encode(result)))

class UpLoadMedia(HTTPMethodView):
    class UpLoadMediaObj:
        app_uuid = doc.String("app_uuid")
        # media_type = doc.String("media_type")
        media_url = doc.String("media_url")

    @doc.consumes(UpLoadMediaObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorzation", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.consumes("")
    @doc.tag(url_prefix)
    @doc.summary("上传提审素材")

    def get_info_by_url(self, media_url):
        file_name = media_url.split("/")[-1]
        file_suffix = media_url.split(".")[-1]
        if file_suffix in WxMediaType.video_type:
            file_type = "video"
        elif file_suffix in WxMediaType.image_type:
            file_type = "image"
        else:
            file_type = None
        return file_name, file_type

    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            # media_type = request.json.get("media_type")
            media_url = request.json.get("media_url")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None

        design_oss = DesignOss()
        files = await design_oss.get_oss_object(url=media_url)
        file_name, file_type = self.get_info_by_url(media_url)
        # memory_file = BytesIO(files.body)
        # _data = {"media": (file_name, memory_file, files.content_type)}
        # multipart_encoder = MultipartEncoder(_data)
        # headers = {'Content-Type': multipart_encoder.content_type}
        form = FormData()
        form.add_field('media', files.body, filename=file_name, content_type=files.content_type)
        formdata_encoder = form()

        if appletid:
            if files:
                if file_type:
                    result = await engine.wx_api.upload_media(appletid, file_obj=formdata_encoder)
                else:
                    result = {"errmsg": "上传文件类型不支持"}
            else:
                result = {"errmsg": "文件不存在"}
        else:
            result = IDECode.APP_WX_NOT_BIND_EXIST
        return json(LDR(data=result))

class GrayRelease(HTTPMethodView):
    class GrayReleaseObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(GrayReleaseObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorzation", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.consumes("")
    @doc.tag(url_prefix)
    @doc.summary("测试commit代码是否成功")
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            gray_per = request.json.get("gray_percentage")
            experiencer = request.json.get("support_experiencer_first")
            debuger = request.json.get("support_debuger_first")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None
        if appletid:
            result = await engine.wx_api.gray_release(
                appletid, gray_percentage=gray_per,
                support_experiencer_first=experiencer, support_debuger_first=debuger)
        else:
            result = dict()
        return json(LDR(data=result))


class GetCodePrivacyInfo(HTTPMethodView):
    class GetCodePrivacyInfoObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(GetCodePrivacyInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorzation", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.consumes("")
    @doc.tag(url_prefix)
    @doc.summary("测试生成体验版是否成功")
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None
        if appletid:
            result = await engine.wx_api.get_code_privacy_info(appletid)
        else:
            result = IDECode.APP_WX_NOT_BIND_EXIST
        return json(LDR(data=result))


class SubmitAudit(HTTPMethodView):
    class SubmitAuditObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(SubmitAuditObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("小程序代码审核")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
            submit_info = request.json.get("submit_info")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None
        if appletid:
            # auditid = wx_app.auditid
            # if auditid:
            #     audit_check = await engine.wx_api.submit_audit_check(appletid, auditid)
            #     if audit_check:
            #         return json(LDR(data=audit_check))
            result = await engine.wx_api.submit_audit(appletid, submit_info)
            if result:
                auditid = result.get("auditid")
                if auditid:
                    await engine.access.update_authorizer_wx_auditid(app_uuid, auditid)
                    data = dict(
                        app_uuid=app_uuid,
                        appid=appletid,
                        auditid=auditid,
                        is_delete=False,
                        auditstatus=AuditStatusType.AUDITING,
                        submit_info=submit_info,
                        submit_time=int(datetime.now().timestamp())
                    )
                    await engine.access.create_or_update_submit_audit(**data)
            else:
                result = {"errmsg": "提交审核失败"}
        else:
            result = IDECode.APP_WX_NOT_BIND_EXIST
        return json(LDR(data=result))


class GetAllAuditInfo(HTTPMethodView):
    class GetAllAuditInfoObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(GetAllAuditInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("查看所有审核信息")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
        wx_audit_info = await engine.access.get_all_submitaudit_wx(**{"app_uuid": app_uuid})
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appid = wx_app.appid if wx_app else None
        if not appid:
            return json(LDR(IDECode.APP_WX_NOT_BIND_EXIST))
        # auditid = wx_app.auditid
        result = list()
        if wx_audit_info:
            wx_audit_info_list = list(wx_audit_info)
            for _wx_audit_info in wx_audit_info_list:
                submit_info = _wx_audit_info.submit_info
                auditid = _wx_audit_info.auditid
                auditstatus = _wx_audit_info.auditstatus
                submit_time = _wx_audit_info.submit_time
                if auditstatus in AuditStatusType.NEEDUPDATE:
                    audit_info = await engine.wx_api.get_auditstatus(appid, auditid)
                    auditstatus = audit_info.get("status")
                    if auditstatus:
                        await engine.access.create_or_update_submit_audit(**{
                            "auditid": auditid, "auditstatus": auditstatus})
                else:
                    audit_info = {"status": auditstatus}
                result.append({
                    "submit_info": submit_info, "auditid": auditid,
                    "auditstatus": audit_info, "submit_time": submit_time})
        else:
            result = [{"errmsg": "未找到审核信息"}]
        return json(LDR(data=result))


class GetAuditInfo(HTTPMethodView):
    class GetAuditInfoObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(GetAuditInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("查看审核信息")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
            auditid = request.json.get("auditid")
        # wx_audit_info = await engine.access.get_all_submitaudit_wx(**{"app_uuid": app_uuid})
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appid = wx_app.appid if wx_app else None
        if not appid:
            return json(LDR(IDECode.APP_WX_NOT_BIND_EXIST))
        audit_info = await engine.wx_api.get_auditstatus(appid, auditid)
        auditstatus = audit_info.get("status")
        if auditstatus in AuditStatusType.NEEDUPDATE:
            await engine.access.create_or_update_submit_audit(**{
                "auditid": auditid, "auditstatus": auditstatus})
        return json(LDR(data=audit_info))


class UndoCodeAudit(HTTPMethodView):
    class UndoCodeAuditObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(UndoCodeAuditObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("撤销小程序代码审核")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
            auditid = request.json.get("auditid")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None
        if not auditid:
            auditid = wx_app.auditid
        if appletid:
            if auditid:
                result = await engine.wx_api.undocodeaudit(appletid)
            else:
                result = {"errmsg": "auditid不存在"}
        else:
            result = IDECode.APP_WX_NOT_BIND_EXIST
        return json(LDR(data=result))


class GetPrivacySetting(HTTPMethodView):
    class GetPrivacySettingObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(GetPrivacySettingObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取小程序用户隐私保护指引")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
            privacy_ver = request.json.get("privacy_ver")
        # wx_audit_info = await engine.access.get_all_submitaudit_wx(**{"app_uuid": app_uuid})
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appid = wx_app.appid if wx_app else None
        if not appid:
            result = IDECode.APP_NOT_COMMIT
        else:
            result = await engine.wx_api.get_privacy_setting(appid, privacy_ver)
        return json(LDR(data=result))

class SetPrivacySetting(HTTPMethodView):
    class SetPrivacySettingObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(SetPrivacySettingObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("设置小程序用户隐私保护指引")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
            privacy_info = request.json.get("privacy_info")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appid = wx_app.appid if wx_app else None
        if not appid:
            result = IDECode.APP_NOT_COMMIT
        else:
            result = await engine.wx_api.set_privacy_setting(appid, privacy_info)
        return json(LDR(data=result))


class AuditRelease(HTTPMethodView):
    class AuditReleaseObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(AuditReleaseObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("发布已通过审核的小程序")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
        wx_app = await engine.access.get_authorizer_wx(**{"app_uuid": app_uuid})
        appletid = wx_app.appid if wx_app else None
        if appletid:
            result = await engine.wx_api.audit_release(appletid)
        else:
            result = dict()
        return json(LDR(data=result))


class UnbindWeixinApp(HTTPMethodView):
    class UnbindWeixinAppObj:
        app_uuid = doc.String("app_uuid")

    @doc.consumes(UnbindWeixinAppObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("解绑微信公众号或小程序")
    async def post(self, request):
        # todo: 校验是否app_uuid管理员
        with process_args():
            app_uuid = request.json.get("app_uuid")
        wx_info = await engine.access.get_obj(AuthorizedWX, **{"app_uuid": app_uuid})
        if not wx_info:
            return json(LDR(IDECode.APP_WX_NOT_BIND_EXIST))
        # res = await engine.wx_api.app_post(appid=wx_info.appid, url="https://api.weixin.qq.com/cgi-bin/open/unbind",
        #                                    body={"open_appid": WEIXIN.COMPONENT_APPID})
        # if res["errcode"] != 0:
        #     return json(LDR(**{"code": res["errcode"], "message": res["errmsg"]}))
        res = await engine.access.delete_authorizer_wx_app(app_uuid)
        return json(LDR(Code.OK))


class BindMiniProgTester(HTTPMethodView):
    class BindMiniProgUser:
        wechatid = doc.String()

    @doc.consumes(BindMiniProgUser, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("授权微信或小程序")
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            wechatid = request.json.get("wechatid")
        result = await engine.wx_api.app_post(appid=WeChartConfig.MINIPROG_LEMON_APPID,
                                              url="https://api.weixin.qq.com/wxa/bind_tester",
                                              body={"wechatid": wechatid})
        # result = await bind_tester(WEIXIN.MINIPROG_LEMON_APPID, wechatid, engine.redis)
        return json(LDR(data=result))


class DeleteApp(HTTPMethodView):
    class DeleteAppObj:
        app_uuid = doc.String("应用id")

    @doc.consumes(DeleteAppObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除App")
    @check_user_policy(policy_type=1)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            ext_tenant = request.json.get("ext_tenant")
            build_type = int(request.json.get("build_type", 0))
        app = await engine.access.get_app_by_app_uuid(app_uuid)
        ext_tenants = await engine.access.list_app_ext_tenants(app_uuid)
        app_ext_tenants = []
        for t in ext_tenants:
            app_ext_tenants.append(t["tenant_uuid"])
        if app.user_uuid == user_uuid:
            publish_service = PublishService(user_uuid, app_uuid, ext_tenant=ext_tenant, build_type=build_type,
                                             app_ext_tenants=app_ext_tenants)
            await publish_service.manage_app_status(offline=True)
            await engine.access.update_app(app_uuid, **{"is_delete": True,
                                                        "delete_time": time.time()})
        return json(LDR(Code.OK))


class DeleteTeam(HTTPMethodView):
    class DeleteAppObj:
        team_uuid = doc.String("团队uuid")

    @doc.consumes(DeleteAppObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除团队,对团队创建应用进行的操作")
    @check_user_policy(policy_type=1)
    @Permission.check_team_policy(policy_type=[2])
    async def post(self, request):
        with process_args():
            user_uuid, team_uuid = get_login_user_uuid(request)
        apps = await engine.access.list_app_by_user_uuid(team_uuid)
        for app_info in apps:
            app_uuid = app_info.get("app_uuid")
            app = await engine.access.get_app_by_app_uuid(app_uuid)
            ext_tenants = await engine.access.list_app_ext_tenants(app_uuid)
            app_ext_tenants = []
            for t in ext_tenants:
                app_ext_tenants.append(t["tenant_uuid"])
            if app.user_uuid == user_uuid:
                publish_service = PublishService(
                    user_uuid, app_uuid, ext_tenant=None, build_type=None,
                    app_ext_tenants=app_ext_tenants)
                await publish_service.manage_app_status(offline=True)
                await engine.access.update_app(app_uuid, **{"is_delete": True,
                                                            "delete_time": time.time()})
        return json(LDR(Code.OK))


class OfflineManage(HTTPMethodView):
    class OfflineManageObj:
        app_uuid = doc.String("应用id")
        action = doc.String("上线或者下线: offline/online")

    @doc.consumes(OfflineManageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("App上线下线管理")
    @check_user_policy(policy_type=1)
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            action = request.json.get("action")
            ext_tenant = request.json.get("ext_tenant")
            build_type = int(request.json.get("build_type", 0))
        assert action in ["offline", "online"]
        assert ext_tenant is None
        ext_tenants = await engine.access.list_app_ext_tenants(app_uuid)
        app_ext_tenants = []
        for t in ext_tenants:
            app_ext_tenants.append(t["tenant_uuid"])
        publish_service = PublishService(user_uuid, app_uuid, ext_tenant=ext_tenant, build_type=build_type,
                                         app_ext_tenants=app_ext_tenants)
        return_code = await publish_service.manage_app_status(offline=action == "offline")
        return json(LDR(return_code))


class ListTeamplayerAPP(HTTPMethodView):
    class ListTeamplayerAPPObj:
        user_uuid_list = doc.List("用户uuid列表")

    @doc.consumes(ListTeamplayerAPPObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出团队成员参与项目")
    @Permission.transcation()
    @Permission.check_team_policy(policy_type=[1, 2])
    async def post(self, request):
        with process_args():
            team_uuid = request.ctx.session.get("login_as")
            user_uuid_list = request.json.get("user_uuid_list")

        apps = await engine.access.list_teamplayer_app(team_uuid, user_uuid_list)
        result = {u: list() for u in user_uuid_list}
        for user_uuid in user_uuid_list:
            user_app = result.setdefault(user_uuid, list())
            for a in apps:
                if a.get("user_uuid") == user_uuid:
                    user_app.append(a)
        return json(LDR(data=result))


class ListAppCreatorInfo(HTTPMethodView):
    class ListTeamplayerAPPObj:
        app_uuid_list = doc.List("应用uuid列表")

    @doc.consumes(ListTeamplayerAPPObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("根据app_uuid列出应用创建者信息")
    @Permission.transcation()
    @Permission.check_team_policy(policy_type=[1, 2])
    async def post(self, request):
        with process_args():
            team_uuid = request.ctx.session.get("login_as")
            app_uuid_list = request.json.get("app_uuid_list")

        apps = await engine.access.list_app_creator_info(app_uuid_list)
        return json(LDR(data=apps))


class GetPublisherInfo(HTTPMethodView):
    class GetPublisherInfoObj:
        publisher = doc.Integer("发行商pk")

    @doc.consumes(GetPublisherInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取发行商详情")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            publisher = request.json.get("publisher")

        result = dict()
        if not publisher:
            return json(LDR(data=result))
        publisher_obj = await engine.access.get_obj(Publisher, id=publisher)
        if publisher_obj:
            result = publisher_obj.to_dict()
            result.pop("is_delete", None)
        return json(LDR(data=result))


class ListTeamAppCombiner(HTTPMethodView):
    class ListTeamAppCombinerObj:
        app_uuid = doc.String("应用uuid")

    @doc.consumes(ListTeamAppCombinerObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出团队APP的参与者")
    @Permission.transcation()
    @Permission.check_team_policy(policy_type=[1, 2])
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")

        combiners = await engine.access.list_combine_by_app_uuid(
            app_uuid, is_owner=0)
        for c in combiners:
            sandbox_uuids = c.get("sandbox_uuid", "") or ""
            sandbox_names = c.get("sandbox_name", "") or ""

            sandbox_info = list()
            if sandbox_uuids:
                sandbox_uuids = sandbox_uuids.split(",")
                sandbox_names = sandbox_names.split(",")
                sandbox_info = [{"sandbox_uuid": x[0], "sandbox_name": x[1]} for x in zip(sandbox_uuids, sandbox_names)]

            c["sandbox_info"] = sandbox_info

        return json(LDR(data=list(combiners)))


class DelTeamplayerCombine(HTTPMethodView):
    class DelTeamplayerCombineObj(object):
        app_uuid = doc.String("应用UUID")
        user_uuid = doc.List("用户UUID")

    @doc.consumes(DelTeamplayerCombineObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除应用协作者")
    @Permission.transcation()
    # @Permission.check_team_policy(policy_type=[1, 2])
    async def post(self, request):
        with process_args():
            real_user_uuid, team_uuid = get_login_user_uuid(request)
            team_permission = request.ctx.session.get("team_permission")
            user_uuid = request.json.get("user_uuid")
            app_uuid = request.json.get("app_uuid")
            del_all_app = request.json.get("del_all_app", False)

        if not del_all_app and not app_uuid:
            return json(LDR(Code.ARGS_ERROR))

        if real_user_uuid == team_uuid:
            return json(LDR(Code.ARGS_ERROR))

        if real_user_uuid != user_uuid:
            if team_permission not in [1, 2]:
                return json(LDR(Code.PERMISSION_POLICY))

        if user_uuid == "all":
            await engine.access.delete_all_teamplayer_combine(team_uuid)
        else:
            if del_all_app:
                await engine.access.delete_teamplayer_combine(team_uuid, user_uuid)
            else:
                await engine.access.delete_teamplayer_combine(team_uuid, user_uuid, app_uuid=app_uuid)
        return json(LDR())


class TransferAPP(HTTPMethodView):
    class TransferAPPObj(object):
        app_uuid = doc.String("应用UUID")

    @doc.consumes(TransferAPPObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("转让应用")
    @Permission.transcation()
    # @Permission.check_team_policy(policy_type=[1, 2])
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            to_user = request.json.get("to_user")
            to_team = request.json.get("to_team")
            team_permission = request.ctx.session.get("team_permission")

        if (to_user and to_team) or not (to_user or to_team):
            return json(LDR(Code.ERROR))

        app_obj: APP = await engine.access.get_app(**{"user_uuid": user_uuid, "app_uuid": app_uuid})
        if real_user_uuid != user_uuid:
            if team_permission != 2:  # 超级管理员
                return json(LDR(Code.PERMISSION_REQUIRED))
        else:
            if app_obj.user_uuid != user_uuid:
                return json(LDR(Code.PERMISSION_REQUIRED))

        new_owner = to_user or to_team

        query = app_obj.update(user_uuid=new_owner).where(app_obj._pk_expr())
        await engine.access.update_obj_by_query(APP, query)
        await engine.access.delete_all_teamplayer_combine(app_uuid=app_uuid)
        combine_data = {
            Combine.is_owner.name: True,
            Combine.app_uuid.name: app_uuid,
            Combine.user_uuid.name: new_owner,
            Combine.team_uuid.name: to_team
        }
        await engine.access.create_obj(Combine, **combine_data)
        await engine.access.update_policy(app_uuid, group_id=new_owner)
        # 清除sandbox sandbox_user
        await engine.access.delete_app_sandbox(app_uuid)
        if to_team:
            # 新建默认sandbox
            sandbox_uuid = lemon_uuid()
            data = {
                "sandbox_uuid": sandbox_uuid,
                "app_uuid": app_uuid,
                "sandbox_name": "默认沙箱"
            }
            await engine.access.create_obj(Sandbox, **data)

            super_admin = await engine.access.list_teamplayer(
                to_team, status=0, permission=2)
            app_log.info(super_admin)
            for sa in super_admin:  # 目前设计只有一个超级管理员
                combine_data = {
                    Combine.is_owner.name: False,
                    Combine.app_uuid.name: app_uuid,
                    Combine.user_uuid.name: sa.get("user_uuid"),
                    Combine.team_uuid.name: to_team,
                    Combine.permission.name: 0,  # 
                    Combine.publish_permission.name: PublishPermission.TEST + PublishPermission.PRO
                }
                await engine.access.create_obj(Combine, **combine_data)

                sandbox_user_data = {
                    SandboxUser.sandbox_uuid.name: sandbox_uuid,
                    SandboxUser.user_uuid.name: sa.get("user_uuid")
                }
                await engine.access.create_obj(SandboxUser, **sandbox_user_data)

        return json(LDR())


class AddTeamAppCombiner(HTTPMethodView):
    class AddAppCombinerObj(object):
        app_uuid = doc.String("应用UUID")
        combiner_info = doc.Dictionary({
            "user_uuid": {"app_uuid": "permission: 0: 查看并编辑  1: 仅查看"}
        })

    @doc.consumes(AddAppCombinerObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("新增团队应用协作者")
    @Permission.check_team_policy(policy_type=[1, 2])
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            combiner_info = request.json.get("combiner_info", dict())

        if real_user_uuid == user_uuid or not combiner_info:
            return json(LDR(Code.ARGS_ERROR))

        team_uuid = user_uuid
        teamplayers = await engine.access.list_teamplayer(team_uuid)
        teamplayer_info = {t["user_uuid"]: t for t in teamplayers}

        insert_data = list()
        new_app_uuid, new_user_uuid = set(), set()
        for combiner_user_uuid, info in combiner_info.items():
            publish_permission = 0
            t_info = teamplayer_info.get(combiner_user_uuid)
            if not t_info:
                return json(LDR(IDECode.USER_NOT_TEAMPLAYER))
            team_permission = t_info.get("permission")
            if team_permission in [1, 2]:  # admin
                publish_permission = PublishPermission.TEST + PublishPermission.PRO
            else:
                publish_permission = PublishPermission.TEST
            for combiner_app_uuid, permission in info.items():
                user_data = dict()
                user_data.update({
                    Combine.app_uuid.name: combiner_app_uuid,
                    Combine.user_uuid.name: combiner_user_uuid,
                    Combine.is_owner.name: False,
                    Combine.visible.name: True,
                    Combine.permission.name: permission,
                    Combine.team_uuid.name: team_uuid,
                    Combine.publish_permission.name: publish_permission})
                insert_data.append(user_data)
                new_app_uuid.add(combiner_app_uuid)
            new_user_uuid.add(combiner_user_uuid)
        team_user_apps = await engine.access.list_app_by_combine_user(new_user_uuid, team_uuid=team_uuid)
        for app_info in team_user_apps:
            if app_info.get("app_uuid") in new_app_uuid:
                return json(LDR(IDECode.TEAM_COMBINER_PERMISSION_REPEAT))

        if insert_data:
            await engine.access.insert_many_obj(Combine, insert_data)
        return json(LDR())


class ExitTeamplayerCombine(HTTPMethodView):
    class ExitTeamplayerCombineObj(object):
        app_uuid = doc.String("应用UUID")

    @doc.consumes(ExitTeamplayerCombineObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("退出应用参与")
    @Permission.transcation()
    @Permission.check_team_policy(policy_type=[0, 1])
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            real_user_uuid, user_uuid = get_login_user_uuid(request)

        if not app_uuid or real_user_uuid == user_uuid:
            return json(LDR(Code.ARGS_ERROR))

        await engine.access.delete_teamplayer_combine(user_uuid, real_user_uuid, app_uuid=app_uuid)
        return json(LDR())


class ManageTeamAppUser(HTTPMethodView):
    class ManageTeamAppUserObj(object):
        app_uuid = doc.String("操作的应用UUID")
        user_uuid = doc.String("操作的用户UUID")
        app_permission = doc.Integer("0: 查看并编辑  1: 仅查看")
        sandbox_uuid_list = doc.List("为他分配的沙箱列表")
        publish_permission = doc.Integer("发布权限  0: 无权限  2: 测试  4: 正式  6: 测试+正式")

    @doc.consumes(ManageTeamAppUserObj, content_type=DOC.JSON_TYPE, location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("团队应用管理参与者权限")
    @Permission.transcation()
    @Permission.check_team_policy(policy_type=[1, 2])
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
            cur_user_uuid, team_uuid = get_login_user_uuid(request)
            target_user_uuid = request.json.get("user_uuid")
            app_permission = request.json.get("app_permission")
            sandbox_uuid_list = request.json.get("sandbox_uuid_list")
            publish_permission = request.json.get("publish_permission")

        data = {
            "permission": app_permission,
            "publish_permission": publish_permission
        }
        await engine.access.update_combine(app_uuid, target_user_uuid, **data)

        await engine.access.delete_sandbox_user(target_user_uuid, app_uuid)
        if sandbox_uuid_list:
            data_list = [{"sandbox_uuid": s, "user_uuid": target_user_uuid} for s in sandbox_uuid_list]
            await engine.access.insert_many_obj(SandboxUser, data_list, on_conflict_replace=True)

        return json(LDR())


class ListSandbox(HTTPMethodView):
    class ListSandboxObj():
        app_uuid = doc.String("app_uuid")
        all_sandbox = doc.Boolean("true: 查看应用下全部沙箱,  false: 查询当前登录用户可用沙箱")

    @doc.consumes(ListSandboxObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出应用用户可用沙箱")
    @Permission.transcation()
    @Permission.check_team_policy(policy_type=[0, 1, 2])
    async def post(self, request):
        with process_args():
            real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_uuid = request.json.get("app_uuid")
            all_sandbox = request.json.get("all_sandbox", False)

        if real_user_uuid == user_uuid:
            return json(LDR(data=list()))
        if all_sandbox:
            real_user_uuid = None
        sandbox_list = await engine.access.list_sandbox(app_uuid, real_user_uuid)
        return json(LDR(data=list(sandbox_list)))


class ListTeamplayer(HTTPMethodView):
    class ListTeamplayerObj(object):
        status = doc.String("邀请状态: 0: 正式成员  2: 已同意,待确认")
        pagination = doc.String("页码")
        account = doc.String("姓名或手机号")

    @doc.consumes(ListTeamplayerObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.tag(url_prefix)
    @doc.description("")
    @doc.summary("列出团队成员")
    @Permission.transcation()
    @Permission.check_team_policy(policy_type=[1, 2])
    async def post(self, request):
        with process_args():
            team_uuid = request.ctx.session.get("login_as")
            status = request.json.get("status")
            pagination = request.json.get("pagination", 1)
            account = request.json.get("account")
            skip_app_uuid = request.json.get("skip_app_uuid")
        need_pagination = False
        if status == 0:
            need_pagination = True
        count, teamplayers = await engine.access.list_teamplayer_by_status(
            team_uuid, status=status, need_pagination=need_pagination,
            pagination=pagination, account=account, skip_app_uuid=skip_app_uuid)
        return json(LDR(data={"count": count, "data": list(teamplayers)}))


class RedirctCorpwx(HTTPMethodView):

    async def get(self, request):
        with process_args():
            code = request.args.get("code")
            corpid = request.args.get("i")
            corpsecret = request.args.get("s")
            real_redirect_uri = request.args.get("real_redirect", "")

        userid = ""
        token_url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
        async with ClientSession(trust_env=True) as session:
            request = await session.get(token_url)
            result_text = await request.text()
            result_json = ujson.loads(result_text)
            access_token = result_json.get("access_token")

            url = f"https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token={access_token}&code={code}"
            request = await session.get(url)
            result_text = await request.text()
            result = ujson.loads(result_text)
            app_log.info(result)
            if not result.get("errcode"):
                userid = result.get("userid")
        param_flag = "?" if "?" not in real_redirect_uri else "&"
        if userid and real_redirect_uri:
            real_redirect_uri = real_redirect_uri + param_flag + "userid=" + userid
            return redirect(real_redirect_uri)
        return json(LDR(data=result))


bp.add_route(ListAPPUserPolicy.as_view(), "/list_app_user_policy.json")
bp.add_route(UploadAppIcon.as_view(), "/upload_app_icon.json")
bp.add_route(GetAPP.as_view(), "/get_app.json")
bp.add_route(ListAPP.as_view(), "/list_app.json")
bp.add_route(HideAppInDesign.as_view(), "/hide_app_in_design.json")
bp.add_route(ListAppManage.as_view(), "/list_app_manage.json")
bp.add_route(ListRecentDeleteApp.as_view(), "/list_recent_delete_app.json")
bp.add_route(RecoverDeletedApp.as_view(), "/recover_deleted_app.json")
bp.add_route(DeleteApp.as_view(), "/delete_app.json")
bp.add_route(OfflineManage.as_view(), "/offline_manage.json")
bp.add_route(CreateAPP.as_view(), "/create_app.json")
bp.add_route(UpdateApp.as_view(), "/update_app.json")
bp.add_route(GetIconDominantColor.as_view(), "/get_icon_dominant.json")
# bp.add_route(GetResourceExtPolicy.as_view(), "/get_resource_ext_policy.json")
# bp.add_route(ListModuleExtPolicy.as_view(), "/list_module_ext_policy.json")
# bp.add_route(ListDocumentExtPolicy.as_view(), "/list_doc_ext_policy.json")
# bp.add_route(UpdateResourceExtPolicy.as_view(), "/update_resource_ext_policy.json")
bp.add_route(GetWXPreAuthUrl.as_view(), "/get_wx_pre_auth_url.json")
bp.add_route(AuthorizeWXAccess.as_view(), "/auth_wx_access.json")
bp.add_route(SubmitAudit.as_view(), "/submit_audit.json")
bp.add_route(UpLoadMedia.as_view(), "/upload_media.json")
bp.add_route(GetMaterial.as_view(), "/get_material.json")
bp.add_route(GrayRelease.as_view(), "/gray_release.json")
bp.add_route(GetCodePrivacyInfo.as_view(), "/get_code_privacy_info.json")
bp.add_route(GetAllAuditInfo.as_view(), "/get_all_audit_info.json")
bp.add_route(GetAuditInfo.as_view(), "/get_audit_info.json")
bp.add_route(UndoCodeAudit.as_view(), "/undo_code_audit.json")
bp.add_route(AuditRelease.as_view(), "/audit_release.json")
bp.add_route(GetPrivacySetting.as_view(), "/get_privacy_setting.json")
bp.add_route(SetPrivacySetting.as_view(), "/set_privacy_setting.json")
bp.add_route(GetWxAllCategoryName.as_view(), "/get_wx_category_name.json")
bp.add_route(UnbindWeixinApp.as_view(), "/unbind_wx_app.json")
bp.add_route(BindMiniProgTester.as_view(), "/bind_miniprog_tester.json")
bp.add_route(ListAppCombiner.as_view(), "/list_app_combiner.json")
bp.add_route(AddAppCombiner.as_view(), "/add_app_combiner.json")
bp.add_route(DelAppCombiner.as_view(), "/del_app_combiner.json")
bp.add_route(ExitAppCombiner.as_view(), "/exit_app_combiner.json")
bp.add_route(ListTeamplayerAPP.as_view(), "/list_teamplayer_app.json")
bp.add_route(DelTeamplayerCombine.as_view(), "/del_teamplayer_combine.json")
bp.add_route(DeleteTeam.as_view(), "/delete_team.json")
bp.add_route(ListTeamAppCombiner.as_view(), "/list_team_app_combiner.json")
bp.add_route(TransferAPP.as_view(), "/transfer_app.json")
bp.add_route(AddTeamAppCombiner.as_view(), "/add_team_app_combiner.json")
bp.add_route(ExitTeamplayerCombine.as_view(), "/exit_team_app_combiner.json")
bp.add_route(GetPublisherInfo.as_view(), "/get_publisher_info.json")
bp.add_route(ManageTeamAppUser.as_view(), "/manage_team_app_user.json")
bp.add_route(ListSandbox.as_view(), "/list_sandbox.json")
bp.add_route(ListTeamplayer.as_view(), "/list_teamplayer.json")
bp.add_route(WXVerify.as_view(), "/wx_verify.json")
bp.add_route(RedirctCorpwx.as_view(), "/redirct_corpwx.json")
