from apps.json_schema.validators.base import BaseValidator
from apps.json_schema.refresolver import RefResolver
from apps.json_schema.context import AppCtx, AppModel
from apps.utils import PageFinder
from apps.ide_const import Document
from apps.json_schema.utils import id_of
from loguru import logger


schema = {
    "is_element": True,
    "attr_name": "导入导出模板",
    "type": "object",
    "properties": {
        "data_source": {
            "type": "object",
            "properties": {
                "model": {
                    "$ref": "mem://common/model_ref"
                }
            }
        }
    }
}


class ExportTemplateValidator(BaseValidator):
    def __init__(self, app_ctx, version: str = '1.0') -> None:
        super().__init__(app_ctx, version)
        self.document_type = Document.TYPE.MODEL
        self.schema = schema
        self.resolver = RefResolver(base_uri=id_of(schema), referrer=schema)
        self.model_in_page = {}
