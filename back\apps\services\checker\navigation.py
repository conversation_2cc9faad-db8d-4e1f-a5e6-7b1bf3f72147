
import asyncio
from functools import partial

from baseutils.const import Action, ResourceType
from apps.entity import Navigation, NavigationItem, Policy
from apps.services import DocumentCheckerService, CheckerService
from apps.services.checker import checker
from baseutils.log import app_log
from apps.ide_const import LemonDesigner<PERSON>rrorCode as LDEC, Navigation as NavigationAttr


class NavigationCheckerService(CheckerService):
    def __init__(self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, document_name: str, element: dict, *args, **kwargs):
        super().__init__(app_uuid, module_uuid, module_name, document_uuid, document_name, element, *args, **kwargs)
        self.navigation_insert_data_list = list()
        self.navigation_update_query_list = list()
        self.navigation_delete_query_list = list()
        self.policy_insert_data_list = list()
        
        self.app_navigation_dict = kwargs.get("app_navigation_dict", dict())
        self.app_navigation_item_dict = kwargs.get("app_navigation_item_dict", dict())  # {"navigation_uuid": []}
        self.app_tag_dict = kwargs.get("app_tag_dict", dict())

    def check_navigation(self):
        app_log.info("noneedcheck")
        return
        
    def build_navigation_update_query(self, navigation):
        navigation_uuid = str(navigation.get("navigation_uuid", ""))
        navigation_style = int(navigation.get('navigation_style', 0))
        position = int(navigation.get("position", 0))
        platform = int(navigation.get("platform", 0))
        mainpage = str(navigation.get("mainpage", ""))
        mainpage_title = str(navigation.get("mainpage_title", ""))
        userpage = navigation.get("userpage", [])
        active_key = navigation.get("active_key", [])
        data = dict(
                app_uuid=self.app_uuid,
                navigation_style=navigation_style,
                position=position,
                platform=platform,
                mainpage=mainpage,
                mainpage_title=mainpage_title,
                userpage=userpage,
                document_uuid=self.document_uuid,
                is_delete=False,
                active_key=active_key,
            )
        model = Navigation
        query = model.update(**data).where(
            model.app_uuid == self.app_uuid,
            model.platform == platform,
            model.navigation_uuid == navigation_uuid
        )
        return query

    def build_navigation_insert_query_data(self, navigation):
        navigation_uuid = str(navigation.get("navigation_uuid", ""))
        navigation_style = int(navigation.get('navigation_style', 0))
        position = int(navigation.get("position", 0))
        platform = int(navigation.get("platform", 0))
        mainpage = str(navigation.get("mainpage", ""))
        mainpage_title = str(navigation.get("mainpage_title", ""))
        userpage = navigation.get("userpage", [])
        active_key = navigation.get("active_key", [])
        data = dict(
                app_uuid=self.app_uuid,
                navigation_uuid=navigation_uuid,
                navigation_style=navigation_style,
                position=position,
                platform=platform,
                mainpage=mainpage,
                mainpage_title=mainpage_title,
                userpage=userpage, 
                document_uuid=self.document_uuid,
                active_key=active_key
            )
        return data
    
    def build_navigation_policy_insert_query_data(self, navigation):
        navigation_uuid = str(navigation.get("navigation_uuid", ""))
        action = Action.DELETE
        data = dict(
            group_id=self.user_uuid, 
            resource_id=navigation_uuid,
            action=action, 
            app_uuid=self.app_uuid, 
            type=ResourceType.NAVIGATION
        )
        return data
    
    def gen_update_navigationitem_query(self, update_data):
        model = NavigationItem
        querys = []
        for data in update_data:
            query = model.update(**data).where(
                model.app_uuid==self.app_uuid, model.item_uuid==data["item_uuid"])
            querys.append(query)
        return querys

    def check_modify(self):
        self.item_insert = list()
        self.item_update_query = list()
        self.item_delete_query = list()
        
        
        platform_set = set()
        for navigation in self.element.get("navigations", list()):
            navigation_uuid = str(navigation.get("navigation_uuid", ""))
            platform = int(navigation.get("platform", 0))
            platform_set.add(platform)
            items = navigation.get("attr", [])
            if navigation_uuid in self.app_navigation_dict:
                query = self.build_navigation_update_query(navigation)
                self.navigation_update_query_list.append(query)
            else:
                data = self.build_navigation_insert_query_data(navigation)
                self.navigation_insert_data_list.append(data)
                policy_data = self.build_navigation_policy_insert_query_data(navigation)
                self.policy_insert_data_list.append(policy_data)
                
                
            # 处理item
            navigation_uuid = str(navigation.get("navigation_uuid", ""))
            navigationitem_uuid_list = [r["item_uuid"] for r in self.app_navigation_item_dict.get(navigation_uuid, dict())]
            create_item_func = partial(
                self.create_item_data, self.app_uuid, navigation_uuid)
            insert_item_data = []
            update_item_data = []
            self.get_navigation_final_items(
                items, insert_item_data, update_item_data, exist_uuid=navigationitem_uuid_list, 
                create_item_func=create_item_func)
            final_exist_navigationitem_uuid = {item["item_uuid"] for item in update_item_data}
            to_delete_item_uuid = set(navigationitem_uuid_list) - final_exist_navigationitem_uuid
            item_delete = [{"is_delete": True, "item_uuid": uuid} for uuid in to_delete_item_uuid]
            item_delete_query = self.gen_update_navigationitem_query(item_delete)
            self.item_delete_query.extend(item_delete_query)
            
            item_update_data = self.gen_update_navigationitem_query(update_item_data)
            self.item_update_query.extend(item_update_data)
            
            self.item_insert.extend(insert_item_data)
            for item in insert_item_data:
                item_policy = dict(
                    group_id=self.user_uuid, 
                    resource_id=item.get("item_uuid"),
                    action=Action.DELETE, 
                    app_uuid=self.app_uuid,
                    type=ResourceType.NAVIGATION_ITEM
                )
                self.policy_insert_data_list.append(item_policy)
                
        exist_platform = set(x["platform"] for x in self.app_navigation_dict.values())
        to_del_platform = (exist_platform|platform_set) - platform_set
        for to_del in to_del_platform:
            model = Navigation
            data = dict(
                is_delete = True
            )
            query = model.update(**data).where(
                model.app_uuid==self.app_uuid, model.platform==to_del)
            self.navigation_delete_query_list.append(query)
            
    @staticmethod
    def get_navigation_final_items(items, to_insert, to_update, exist_uuid, create_item_func, master=""):
        for item in items:
            item: dict
            data = create_item_func(item=item)
            item_uuid = item["item_uuid"]
            item["master"] = master
            if data["item_uuid"] in exist_uuid:
                to_update.append(data)
            else:
                to_insert.append(data)
            children = item.get("children", [])
            NavigationCheckerService.get_navigation_final_items(
                children, to_insert, to_update, exist_uuid, create_item_func, master=item_uuid)

    def create_item_data(self, app_uuid, navigation_uuid, item):
        res = dict(
                app_uuid=app_uuid,
                navigation_uuid=navigation_uuid,
                item_name=item.get("item_name", ""),
                item_uuid=item.get("item_uuid", ""),
                order=item.get("order", 0),
                level=item.get("level", 0),
                master=item.get("master", ""),
                icon_type=item.get("icon_type", 0),
                icon=item.get("icon", ""),
                item_type=item.get('item_type', 0),
                page_uuid=item.get("page_uuid", ""),
                page_url=item.get("page_url", ""),
                param=item.get("param", {}),
                permission=item.get("permission", []),
                icon_color=item.get("icon_color", ""),
                document_uuid=self.document_uuid,
                is_delete=False,
                item_title=item.get("item_title", ""),
                icon_text=item.get("icon_text"),
                item_position=item.get("item_position"),
                badge=item.get("badge"),
                permission_config=item.get("permission_config")
            )
        return res

    def check_icon_text_type(self):
        def need_icon(items):
            # 仅显示图标或显示图标加文字时进行文档检查
            # 老数据的icon_text值为None，显示默认值为“图片加文字”需文档检查
            return [item for item in items if item.get("icon_text") in [2, 3, None]]

        def check_child_icon(item):
            children = item.get("children", [])
            for child in children:
                if not child.get("icon"):
                    attr = NavigationAttr.ATTR.ICON
                    return_code = LDEC.NAVIGATIONITEM_ICON_NOT_SET
                    return_code.message = return_code.message.format(child.get("item_name"))
                    kwargs = {
                        "element_data": {
                            "name": self.document_name,
                            "uuid": self.document_uuid,
                        }
                    }
                    self._add_error_list(attr, return_code, **kwargs)
                check_child_icon(child)

        for navigation in self.element.get("navigations", []):
            # 目前只检查响应式导航的图标设置
            if navigation.get("platform") == 0:
                items = navigation.get("attr", [])
                for item in need_icon(items):
                    if not item.get("icon"):
                        attr = NavigationAttr.ATTR.ICON
                        return_code = LDEC.NAVIGATION_ICON_NOT_SET
                        return_code.message = return_code.message.format(item.get("item_name"))
                        kwargs = {
                            "element_data": {
                                "name": self.document_name,
                                "uuid": self.document_uuid,
                            }
                        }
                        self._add_error_list(attr, return_code, **kwargs)
                    check_child_icon(item)

    def check_item_name(self):
        def check_badge(item):
            attr = NavigationAttr.ATTR.BADGE
            badge = item.get("badge", {})
            self._check_badge(attr, badge)
        
        def check_childen_name(items):
            for item in items:
                item_name = item.get("item_name")
                if len(item_name) > 40:
                    attr = NavigationAttr.ATTR.ITEM_NAME
                    return_code = LDEC.NAVIGATION_ITEM_NAME_ERROR
                    return_code.message = return_code.message.format(item_name)
                    self._add_error_list(attr, return_code)
                check_badge(item)
                items = item.get("children", [])
                check_childen_name(items)

        for navigation in self.element.get("navigations", []):
            items = navigation.get("attr", [])
            check_childen_name(items)

    def check_permission_config(self):
        def check_item_permission_config(items):
            for item in items:
                permission_config = item.get("permission_config", {})
                element_data = {
                    "name": item.get("item_name"),
                    "uuid": item.get("item_uuid")
                }
                self._check_permission_config(permission_config, self.app_tag_dict, element_data)
                items = item.get("children", [])
                check_item_permission_config(items)

        for navigation in self.element.get("navigations", []):
            items = navigation.get("attr", [])
            check_item_permission_config(items)


class NavigationDocumentCheckerService(DocumentCheckerService):
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, 
        document_name: str, element: dict, model: Navigation, *args, **kwargs):
        super().__init__(app_uuid, module_uuid, module_name, document_uuid, document_name, 
                         element, model, *args, **kwargs)
        self.app_navigation_dict = kwargs.get("app_navigation_dict", dict())
        self.app_navigation_item_dict = kwargs.get("app_navigation_item_dict", dict())
        self.app_tag_dict = kwargs.get("app_tag_dict", dict())
        self.element_uuid = self.element.get("navigation_uuid")
        self.user_uuid = kwargs.get("user_uuid")
        
        self.navigation_insert_data_list = list()
        self.navigation_update_query_list = list()
        self.navigation_delete_query_list = list()
        self.item_insert = list()
        self.item_update_query = list()
        self.item_delete_query = list()
        self.policy_insert_data_list = list()
        
    @checker.run
    def check_navigation(self):
        navigation_checker_service = NavigationCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, self.element,
                is_copy=self.is_copy, app_navigation_dict=self.app_navigation_dict, 
                user_uuid=self.user_uuid, app_navigation_item_dict=self.app_navigation_item_dict,
                app_tag_dict=self.app_tag_dict
                )
        navigation_checker_service.document_other_info = self.document_other_info

        # 找到新增 或 更新
        navigation_checker_service.check_modify()
        # navigation_checker_service.check_icon_text_type()
        navigation_checker_service.check_item_name()
        navigation_checker_service.check_permission_config()
        self.update_any_list(navigation_checker_service)
        database_update_key = [
            "navigation_insert_data_list", 
            "navigation_update_query_list", 
            "navigation_delete_query_list", 
            "item_insert", 
            "item_update_query", 
            "item_delete_query", 
            "policy_insert_data_list"
        ]
        for key in database_update_key:
            l = getattr(navigation_checker_service, key, None)
            if l:
                setattr(self, key, l)
        
    async def commit_modify(self, engine):
        
        navigation_update_funcs = list()
        navigation_delete_funcs = list()
        
        async with engine.db.objs.atomic():
            for u in self.navigation_update_query_list:
                func = engine.access.update_obj_by_query(Navigation, u, need_delete=True)
                navigation_update_funcs.append(func)
                await func
            for d in self.navigation_delete_query_list:
                func = engine.access.update_obj_by_query(Navigation, d)
                navigation_delete_funcs.append(func)
                await func
            
            item_update_funcs = list()
            item_delete_funcs = list()
            for u in self.item_update_query:
                func = engine.access.update_obj_by_query(NavigationItem, u, need_delete=True)
                item_update_funcs.append(func)
                await func
            for d in self.item_delete_query:
                func = engine.access.update_obj_by_query(NavigationItem, d, need_delete=True)
                item_delete_funcs.append(func)
                await func

        
        
            if self.navigation_insert_data_list:
                await engine.access.insert_many_obj(Navigation, self.navigation_insert_data_list)
            if self.item_insert:
                await engine.access.insert_many_obj(NavigationItem, self.item_insert)
            if self.policy_insert_data_list:
                await engine.access.insert_many_obj(Policy, self.policy_insert_data_list)
        
            # await asyncio.gather(*navigation_update_funcs)
            # await asyncio.gather(*navigation_delete_funcs)
            # await asyncio.gather(*item_update_funcs)
            # await asyncio.gather(*item_delete_funcs)


