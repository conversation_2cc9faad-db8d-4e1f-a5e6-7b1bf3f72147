# -*- coding:utf-8 -*-

import asyncio
from functools import partial
import weakref
import abc
from typing import Union

from baseutils.log import app_log
from baseutils.utils import LemonContextVar
from baseutils.tracing import TracingContext
from apps.base_utils import lemon_uuid
from apps.ide_const import DictWrapper
from apps.exceptions import RuntimeComponentCallbackNotFound
from apps.utils import LemonRuntimeDictResponse as LDR

from runtime.utils import LemonMessage
from runtime.engine import engine
from runtime.core.utils import build_connector_topic
from runtime.core.actor import LemonPubsubActor
from runtime.protocol.client_rpc import ClientRPCRequest, ClientRPCResponse


class ClientABC(abc.ABC):

    async def run_command(self, command, data):
        command_str = "handle_" + command
        command_func = getattr(self, command_str, None)
        if callable(command_func):
            await command_func(command, data)

    def calc_need_summary(self, msg: dict):
        need_summary = False
        if not msg.get("topic", "").endswith(":result"):
            return need_summary
        if msg.get("result") == "create_page":
            need_summary = True
        elif msg.get("result") == "component_event":
            need_summary = True
        return need_summary

    def do_summary(self, msg, message_summary):
        # 认为所有的:result, 都会执行send_msg, 所以topic实际无影响
        message_summary.update({"topic": msg.get("topic")})
        values = message_summary.get("data", {})
        if msg.get("result") == "create_page":
            values.update({"create_page": msg})
        elif msg.get("result") == "component_event":
            data = msg.get("data", {})
            component = data.get("component", "")
            component_return = values.setdefault(component, {})
            if not component_return:
                component_return.update(msg)
            else:
                component_values = component_return.get("data", {}).get("values", [])
                component_variables = component_return.get("data", {}).get("variables", [])
                component_values.extend(data.get("values", []))
                component_variables.extend(data.get("variables", []))

    def build_error_result(self, component, stdout, stderr, return_code):
        data = {"component": component, "stdout": stdout, "stderr": stderr}
        msg = LDR(return_code, data=data, is_summary=True)
        return msg

    async def send_msg(self, msg: dict):
        ...

    @abc.abstractmethod
    async def send_raw(self, msg: Union[bytes, str]):
        ...

    @abc.abstractmethod
    async def send_client_rpc_request(
            self, request: ClientRPCRequest,
            need_result: bool = True) -> ClientRPCResponse:
        ...

    async def clean_popup_submit_queue(self):
        ...

    def add_wait_close(self):
        ...

    def cancel_wait_close(self):
        ...

    def connection_close(self):
        ...

    async def send_error_result(self, *args, **kwargs):
        ...

    async def on_result(self):
        ...

    async def close(self) -> None:
        ...

    def clean(self):
        ...

    async def _handle_message(self, message: LemonMessage):
        ...

    async def summary_call(self, message_summary):
        ...

    async def handle_client_rpc_response(self, message: LemonMessage):
        ...

    async def handle_message(self, message: LemonMessage):
        ...

    async def wx_handle_message(self, message: LemonMessage):
        ...


class BaseClientConnector():

    def __init__(self, sid=None, is_pattern=True):
        self.sid = sid if sid else lemon_uuid()
        self.topic = build_connector_topic(self.sid)
        self.is_pattern = is_pattern
        self._client = None
        self.client: ClientABC = None
        self.current_user = LemonContextVar.current_user.get()
        self._root_page = None  # 根页面；导航、按钮跳转打开的页面
        self._current_page = None  # 当前页面；可能是嵌套的弹窗、抽屉，也可能是导航、按钮的跳转页面
        self.root_pages = weakref.WeakValueDictionary()
        self.ready = asyncio.Lock()
        self.callbacks = dict()
        self.close_callbacks = list()
        self.context_send_messages = {}
        self.context_run_callbacks = {}
        self.context_exit_callbacks = {}

    @property
    def client(self):
        return self._client

    @client.setter
    def client(self, value):
        self._client = None if value is None else weakref.proxy(value)

    @property
    def send_messages(self):
        tid = TracingContext.get_trace_id()
        queue = self.context_send_messages.setdefault(tid, asyncio.Queue())
        return queue

    @property
    def run_callbacks(self):
        tid = TracingContext.get_trace_id()
        queue = self.context_run_callbacks.setdefault(tid, asyncio.Queue())
        return queue

    @property
    def exit_callbacks(self):
        tid = TracingContext.get_trace_id()
        queue = self.context_exit_callbacks.setdefault(tid, asyncio.Queue())
        return queue

    @property
    def root_page(self):
        return None if self._root_page is None else self._root_page()

    @root_page.setter
    def root_page(self, value):
        self._root_page = None if value is None else weakref.ref(value)
        if value:
            self.root_pages.update({value.uuid: value})

    @property
    def current_page(self):
        return None if self._current_page is None else self._current_page()

    @current_page.setter
    def current_page(self, value):
        if self._current_page is not None:
            page = self._current_page()
            if page and page is not value:
                page.shutdown_tasks()
        self._current_page = None if value is None else weakref.ref(value)

    def clean_callbacks(self):
        tid = TracingContext.get_trace_id()
        if tid in self.context_send_messages:
            del self.context_send_messages[tid]
        if tid in self.context_run_callbacks:
            del self.context_run_callbacks[tid]
        if tid in self.context_exit_callbacks:
            del self.context_exit_callbacks[tid]

    async def clean(self):
        self.clean_callbacks()
        if self.current_page:
            self.current_page.shutdown_tasks()

    @property
    def on_result(self):
        # 转换成功后的回调
        return self.client.on_result if self.client else None

    def get_callback(self, topic):
        return self.callbacks.get(topic)

    async def on_message(self, msg: LemonMessage):
        topic = msg.topic
        callback = self.get_callback(topic)
        app_log.info(f"callback: {callback}")
        if asyncio.iscoroutinefunction(callback):
            await callback(msg)
        elif callable(callback):
            callback(msg)
        else:
            raise RuntimeComponentCallbackNotFound()

    async def call(self, msg: LemonMessage, topic=None):
        message = LemonMessage(topic=topic) if topic else LemonMessage()
        message.update(msg)
        await self.on_message(message)

    async def publish(self, topic, msg, with_current_user=True):
        # 这种方式是在 等待下次事件循环 的时候触发某个状态机的事件
        if with_current_user:
            if self.current_user is not None:
                current_user_dict = self.current_user.as_dict()
                # app_log.info(f"current_user_dict: {current_user_dict}")
                msg.update({"current_user_dict": current_user_dict})
        await engine.pubsub.publish_json(topic, msg)

    async def close(self):
        app_log.info("connector: close")
        # self.on_result = None
        # self.current_user = None
        # self.callbacks = dict()
        # for func in self.close_callbacks:
        #     app_log.info(f"close_func: {func}")
        #     func()
        # self.close_callbacks = list()
        # gc.collect()
        # refers = gc.get_referrers(self)
        # for ref in refers:
        #     app_log.info(f"ref: {ref}, {type(ref)}, {id(ref)}")
        # app_log.info(f"sys.getrefcount(self): {sys.getrefcount(self)}")


class ClientConnector(BaseClientConnector):

    def __init__(self, sid, is_pattern=True):
        super().__init__(sid, is_pattern=is_pattern)
        self.form_transactions = dict()
        self.page_containers = dict()
        # 存放所有 init 过的 模型对象实例
        self.instances = DictWrapper()
        self.instances.update({"db": dict(), "memory": dict()})
        self._device_type = None
        self._other_transactions = None

    @property
    def device_type(self):
        return self._device_type

    @device_type.setter
    def device_type(self, value):
        self._device_type = value

    @property
    def other_transactions(self):
        return self._other_transactions

    @other_transactions.setter
    def other_transactions(self, value):
        self._other_transactions = value or dict()

    async def close(self):
        # self.page_containers = dict()
        # self.root_page = None
        # self.current_page = None
        for form_uuid in list(self.form_transactions.keys()):
            transaction = self.form_transactions[form_uuid]
            if transaction.is_stopped is False:
                await transaction.rollback()
            del self.form_transactions[form_uuid]
        self.form_transactions.clear()
        app_log.info("connector: close done")
        await super().close()

    def get_root_page_uuid(self):
        return self.root_page.uuid if self.root_page else None

    def get_page_callbacks(self):
        page_uuid = self.get_root_page_uuid()
        callbacks = self.callbacks.setdefault(
            page_uuid, dict())
        return callbacks

    def get_callback(self, topic):
        callbacks = self.get_page_callbacks()
        callback = callbacks.get(topic)
        return callback

    def add_callbacks(self, topic_func_list):
        callbacks = self.get_page_callbacks()
        [callbacks.update({topic: func}) for topic, func in topic_func_list]

    def add_callback(self, topic, func):
        callbacks = self.get_page_callbacks()
        callbacks.update({topic: func})

    def add_callback_result(self, topic):
        callbacks = self.get_page_callbacks()
        callbacks.update({topic: self.on_result})

    def remove_callback(self, topic):
        callbacks = self.get_page_callbacks()
        callbacks.update({topic: None})

    def get_root_page_container(self, page_uuid):
        page_obj = self.root_pages.get(page_uuid)
        if page_obj:
            page_obj.init_connector(self)
        return page_obj

    def delete_root_page_container(self, page_uuid):
        if page_uuid in self.root_pages:
            del self.root_pages[page_uuid]

    def get_page_containers(self, page_uuid=None):
        if page_uuid is None:
            if self.root_page is not None:
                page_uuid = self.root_page.uuid
        app_log.info(f"page_uuid: {page_uuid}")
        containers = self.page_containers.get(
            page_uuid, weakref.WeakValueDictionary())
        return containers

    def add_page_container(self, container=None, is_page_obj=False):
        if container is not None and self.root_page:
            page_uuid = self.root_page.uuid
            containers = self.page_containers.setdefault(
                page_uuid, weakref.WeakValueDictionary())
            machine_id = container.machine_id
            containers.update({machine_id: container})

    def find_page_container(self, machine_id, page_uuid=None):
        containers = self.get_page_containers(page_uuid)
        if containers:
            return containers.get(machine_id)
        return None

    def delete_page_container(self, machine_id=None, page_uuid=None):
        containers = self.get_page_containers(page_uuid)
        if machine_id is None:
            if containers:
                del self.page_containers[page_uuid]
            if self.root_page and page_uuid == self.root_page.uuid:
                self.root_page = None
                self.current_page = None
            if self.current_page and page_uuid == self.current_page.uuid:
                self.current_page = None
        else:
            container = containers.get(machine_id)
            if container:
                del containers[machine_id]
