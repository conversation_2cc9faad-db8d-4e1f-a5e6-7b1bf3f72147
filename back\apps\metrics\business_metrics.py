import asyncio
import os

from runtime import engine as runtime_engine
from baseutils.metrics_report import metrics_report_client

JENKINS_AGENT_NAME = os.environ.get("JENKINS_AGENT_NAME", "")


def is_publish_slave() -> bool:
    return bool(JENKINS_AGENT_NAME)


class DesignAppMetrics:

    @classmethod
    def collect_app_publish_steps(cls, step: str, time_cost: float):
        if not is_publish_slave():
            return
        asyncio.create_task(metrics_report_client.report(
            "app_publish_steps",
            [step],
            time_cost
        ))

    @classmethod
    def collect_app_publish(cls, success: bool, storage: int, publish_pc: bool, publish_mobile: bool, env: str,
                            sync_data: bool, time_cost: float):
        if not is_publish_slave():
            return
        asyncio.create_task(metrics_report_client.report(
            "app_publish",
            [str(success), str(storage), str(publish_pc), str(publish_mobile), str(env), str(sync_data)],
            time_cost
        ))
