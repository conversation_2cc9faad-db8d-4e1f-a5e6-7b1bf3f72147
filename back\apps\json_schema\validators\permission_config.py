from apps.json_schema.validators.base import BaseValidator
from apps.json_schema.refresolver import RefResolver
from apps.json_schema.context import AppCtx, AppModel
from apps.utils import PageFinder
from apps.ide_const import Document
from apps.json_schema.utils import id_of
from loguru import logger
from apps.json_schema.schemas.permission_config import permission_config


class PermissionConfigValidator(BaseValidator):
    def __init__(self, app_ctx, version: str = '1.0') -> None:
        super().__init__(app_ctx, version)
        self.document_type = Document.TYPE.WORKFLOW
        self.schema = permission_config
        self.resolver = RefResolver(base_uri=id_of(permission_config), referrer=permission_config)
        self.model_in_page = {}
