# -*- coding:utf-8 -*-

import asyncio
import traceback

from RestrictedPython import compile_restricted_eval

from baseutils.log import app_log
from apps.ide_const import VariableLevel, ValueEditorType, ValueEditorLanguage
from apps.base_utils import *


class DefaultGlobals:
    import re
    import ujson
    import time
    import datetime
    import random

    @classmethod
    def dicts(cls):
        _global = dict()
        _builtins = {
            "re": cls.re,
            "json": cls.ujson,
            "time": cls.time,
            "datetime": cls.datetime,
            "random": cls.random,
            "str": str,
            "int": int,
            "sorted": sorted, "list": list, "dict": dict, 
            "max": max, "min": min, "all": all, "sum": sum
        }
        _global.update({"__builtins__": _builtins})
        _global.update({"_getattr_": getattr})
        return _global


class ValueEditorMixin(object):

    def __init__(self, v_dict):
        self.value_editor = LemonBaseValueEditor(**v_dict)
    
    def __repr__(self):
        return "<%s('%s')@%s>" % (type(self).__name__, self.name, id(self))
    
    def set_globals(self, lsm):
        self.value_editor.set_globals(lsm)
    
    @property
    def value(self):
        return self.value_editor.value


class BaseValueEditor(object):

    def __init__(self, *args, **kwargs):
        self.initialize(*args, **kwargs)
    
    def initialize(self, *args, init=True, **kwargs):
        self._value = None
        self._value_set = False
        self._once_send = True
        self.column = None
        if init:
            self.globals = dict()
            self.columns = dict()
            self.path_columns = dict()
        else:
            self.globals.clear()
            self.columns.clear()
            self.path_columns.clear()
        self.uuid = kwargs.get("uuid")
        self.type = kwargs.get("type", ValueEditorType.CONST)
        self.type_str = ValueEditorType.TO_DICT.get(self.type, None)
        self.is_monitor = kwargs.get("is_monitor", False)
        self.once = kwargs.get("once", False)
        self.language = kwargs.get("language", ValueEditorLanguage.PYTHON)
    
    def __repr__(self):
        return "<%s('%s')@%s>" % (type(self).__name__, self.type_str, id(self))
    
    def clear(self):
        self.globals.clear()
        self._value_set = False
    
    def get_value_with_exception(self, *args, **kwargs):
        return self.value
    
    async def get_value_async_with_exception(self, *args, **kwargs):
        return await self.value_async
    
    @property
    def value(self):
        raise NotImplementedError()
    
    @value.setter
    def value(self, v):
        self._value = v
        self._value_set = True
    
    @property
    async def value_async(self):
        return self.value
    
    def set_globals(self, lsm):
        self.globals.update({"lemon": lsm.lemon, "ctx": lsm.lemon.ctx})
        # 工作流状态机，会添加额外的表单数据，作为条件表达式的占位符来引用
        form_data = getattr(lsm, "form_data", dict())
        if form_data:
            self.globals.update(**form_data)
            
    def set_globals_without_sm(self, _lemon, form_data):
        self.globals.update({"lemon": _lemon})
        if form_data:
            self.globals.update(**form_data)

    def update_globals(self, **kwargs):
        self.globals.update(kwargs)
    
    @property
    def get_value(self):
        return self._value


class BaseStringValueEditor(BaseValueEditor):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._value = kwargs.get("value", None) if kwargs else False
    
    @property
    def value(self):
        return self._value
    
    @value.setter
    def value(self, v):
        self._value = v
        self._value_set = True
    

class BaseConstValueEditor(BaseValueEditor):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.module_uuid = kwargs.get("module")
        self.const_uuid = kwargs.get("const")
        self._value = None
    
    @property
    def value(self):
        all_const_dict = self.globals.get("const_dict", dict())
        cur_const_info = all_const_dict.get(self.const_uuid, dict())
        if cur_const_info:
            self.value = cur_const_info.get("value")
        return self._value
    
    @value.setter
    def value(self, v):
        self._value = v
        self._value_set = True
    

class BaseVariableValueEditor(BaseValueEditor):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.variable_level = kwargs.get("variable_level")
        self.variable_uuid = kwargs.get("variable_uuid")
        self.field_name = kwargs.get("field_name")
    
    @property
    def value(self):
        """
        # 如果 self.variable_level 是 系统变量，那么应该用 engine.system 来获取
        # 此时 self.variable_uuid 应该是一个 key 来反射 engine.system 的值
        # 此时 有可能 值是一个对象，通过 self.field_name 再反射 这个对象，获取最终的值
        """
        """
        # 如果 self.variable_level 是 状态机系统变量，那么应该用 sm 来获取
        # 其他步骤同 系统变量
        """
        """
        # 如果 self.variable_level 是 状态机全局变量、状态变量，那么应该用 context 来获取
        # 其他步骤同 系统变量
        """
        if self._value_set:
            return self._value
        lemon = self.globals.get("lemon")
        if lemon is not None:
            if self.variable_level == VariableLevel.SYSTEM:
                obj = lemon.system
            elif self.variable_level == VariableLevel.SM:
                obj = lemon.sm
            elif self.variable_level == VariableLevel.GLOBAL:
                obj = lemon.sm.globals
            elif self.variable_level == VariableLevel.LOCAL:
                obj = lemon.sm.locals
            self._value = getattr(obj, self.variable_uuid, None)
        return self._value
    
    @value.setter
    def value(self, v):
        self._value = v
        self._value_set = True


class BaseExprValueEditor(BaseValueEditor):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.expr = kwargs.get("expr", "").replace('"', "'")
        self.expr_attr = kwargs.get("exprArr", list())
        self.globals = DefaultGlobals.dicts()
        self._once_send = False
        self.formatter = Formatter()
        self.placeholders = set()
        self.placeholders_value = list()
        self.attr_dict = dict()
        self.placeholder_parse()
    
    def placeholder_parse(self):
        # max_key = placeholder_parse(self.expr, self.placeholders)

        for attr in self.expr_attr:
            key = attr.get("key")
            value_editor_dict = attr.get("value")
            value_editor_dict.update({"language": self.language})
            value_editor = LemonBaseValueEditor(**value_editor_dict)
            if value_editor.type in ValueEditorType.EXPR_NESTED:
                self.attr_dict.update({key: value_editor})
                if value_editor.type == ValueEditorType.FIELD:
                    self.columns.update(value_editor.columns)
                    for field_uuid, field_info in value_editor.path_columns.items():
                        if field_uuid not in self.path_columns:
                            self.path_columns.update(value_editor.path_columns)
                        else:
                            field_path = field_info.get("path")
                            # 在表达式值编辑器中, 存在同一字段, 但 path 不同的情况
                            field_path_list = self.path_columns[field_uuid].get("path_list", [])
                            if field_path not in field_path_list:
                                field_path_list.append(field_path)
        max_key = 0
        if self.attr_dict:
            max_key = max(self.attr_dict.keys())

        # 初始化占位符的每个值
        for i in range(max_key + 1):
            self.placeholders_value.append("")

    @property
    def value(self):
        if self._value_set:
            return self._value
        try:
            for key, value_editor in self.attr_dict.items():
                value_editor.globals.update(self.globals)
                value = value_editor.value
                if isinstance(value, str) and self.language == ValueEditorLanguage.PYTHON:
                    value = "'" + value + "'"
                self.placeholders_value[key] = value
            expr = self.formatter.vformat(self.expr, args=self.placeholders_value, kwargs=dict())
            if self.language == ValueEditorLanguage.PYTHON:
                compiled = compile_restricted_eval(expr)
                app_log.info(f"compiled code: {compiled.code}")
                # app_log.info(f"expr: {self.expr}")
                app_log.info(f"globals: {self.globals}")
                value = eval(compiled.code, self.globals, None)
            else:
                value = expr
            # print(value)
        except BaseException:
            # 表达式报异常，则返回 False
            value = False
            app_log.error(traceback.format_exc())
        self._value = value
        return self._value
    
    @value.setter
    def value(self, v):
        self._value = v
        self._value_set = True


class BaseFieldValueEditor(BaseValueEditor):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.field = kwargs.get("field")
        self.path = kwargs.get("path", [])
        column_model = None
        if column_model:
            self.column = column_model._meta.columns.get(self.field)
        self.columns.update({self.field: self.column})
        self.path_columns.update({
            self.field: {"column": self.column, "path": self.path, "path_list": [self.path]}
        })
        self.globals.update({"model": column_model, "field": self.column})
    
    @property
    def value(self):
        if self._value_set:
            return self._value
        if self.language == ValueEditorLanguage.MYSQL:
            # XXX 先暂时这么做, 因为目前的场景, 当mysql时, 只会需要字段名
            if self.field and len(self.field) > 32 and self.field[32] == "*":
                return self.field.split("*")[-1]
            return self.field
        if self.column is None:
            self._value = None
        else:
            # 这里要看字段是数据容器的普通字段，还是关联字段，返回的数据格式可能不同
            self._value = self.globals.get(self.field, None)
        return self._value
    
    @value.setter
    def value(self, v):
        self._value = v
        self._value_set = True
        
        
class BaseEnumValueEditor(BaseValueEditor):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.enum_uuid = kwargs.get("enum_uuid")
        self.enum_item_name = kwargs.get("enum_item_name")
        self.enum_obj = None
        self._value = None

    @property
    def value(self):
        if enum_dict := self.globals.get("enum_dict"):
            self.value = enum_dict.get(self.enum_uuid, {}).get(self.enum_item_name)
        else:
            return self.enum_item_name
        return self._value

    @value.setter
    def value(self, v):
        self._value = v
        self._value_set = True
    

class LemonBaseValueEditor(Configurable, BaseValueEditor):
    
    @classmethod
    def configurable_base(cls, *args, **kwargs):
        value_editor_type = kwargs.get("type", ValueEditorType.STRING)
        base_class = BaseStringValueEditor
        if value_editor_type == ValueEditorType.STRING:
            base_class = BaseStringValueEditor
        elif value_editor_type == ValueEditorType.CONST:
            base_class = BaseConstValueEditor
        elif value_editor_type == ValueEditorType.VARIABLE:
            base_class = BaseVariableValueEditor
        elif value_editor_type == ValueEditorType.EXPR:
            base_class = BaseExprValueEditor
        elif value_editor_type == ValueEditorType.FIELD:
            base_class = BaseFieldValueEditor
        elif value_editor_type == ValueEditorType.ENUM:
            base_class = BaseEnumValueEditor
        # app_log.debug(f"base_class: {base_class}")
        return base_class


if __name__ == "__main__":
    expr = LemonBaseValueEditor(**{"type": 3, "expr": "int(time.time())"})
    print(expr)
    asyncio.run(expr.value)
    