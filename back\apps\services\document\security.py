
import ujson
from dataclasses import dataclass, asdict, field

@dataclass
class Json:

    @property
    def dict(self):
        return asdict(self)

    @property
    def json(self):
        return ujson.dumps(self.dict)

@dataclass
class Field(Json):
    uuid: str
    name: str
    type: int
    permission: str


@dataclass
class Model(Json):
    uuid: str
    name: str
    action: str
    fields: dict = field(default_factory=dict)
    filter: dict = field(default_factory=dict)

@dataclass
class Page(Json):
    uuid: str
    name: str
    permission: str

@dataclass
class Func(Json):
    uuid: str
    name: str
    permission: str

@dataclass
class Security(Json):
    uuid: str
    name: str
    models: dict = field(default_factory=dict)
    pages: dict = field(default_factory=dict)
    funcs: dict = field(default_factory=dict)




if __name__ == "__main__":
    class DocumentFields:
        user_name = Field(uuid="bdc86ab0f2a711ea8e9a3953924b83f4", name="user_name", permission="0001")
        ALL = {user_name.uuid: user_name.dict}

    class DocumentModels:
        a = Model(uuid="afdc44d0f2a711ea8e9a3953924b83f4" ,name="user", action="0000", fields=DocumentFields.ALL, filter={})
        b = Model(uuid="afdc44d0f2a711ea8e9a3953924b83f5" ,name="user", action="0000", fields={}, filter={})
        ALL = {a.uuid: a.dict, b.uuid: b.dict}

    class DocumentSecurity:
        s = Security(
            uuid="31ad47f2f26711eaba3500155de0426a",
            name = "admin",
            models=DocumentModels.ALL
        )
        ALL = [s]
    @dataclass
    class Document:
        module_role_list: list = field(default_factory=list)

    document = Document(module_role_list=DocumentSecurity.ALL)
    print(asdict(document))
    # f = Field("bdc86ab0f2a711ea8e9a3953924b83f4", FieldAttr("user_name", "0001"))
    # print(asdict(f))
    # a = Model(uuid="afdc44d0f2a711ea8e9a3953924b83f4" ,name="user", action="0000", fields={}, filter={})
    # print(a.json)
    # s = Security(uuid="afdc44d0f2a711ea8e9a3953924b83f4", name="admin", models=a)
    # print(s.json)