# -*- coding:utf-8 -*-

from apps.utils import <PERSON>son, RelationshipData

class Field(Json):

    def __init__(
        self, uuid, name, field_type, display_name="", description="", 
        length=0, decimals=0, default="", enum="", 
        calculate_field=False, calculate_function="", 
        calculate_type=0, calculate_func="",
        is_unique=False, is_required=True, is_visible=True, 
        check_function=False, check_error_message="", *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = field_type
        self.display_name = display_name
        self.description = description
        self.length = length
        self.decimals = decimals
        self.default = default
        self.enum = enum
        self.calculate_field = calculate_field
        self.calculate_function = calculate_function
        self.calculate_type = calculate_type
        self.calculate_func = calculate_func
        self.is_unique = is_unique
        self.is_required = is_required
        self.is_visible = is_visible
        self.check_function = check_function
        self.check_error_message = check_error_message
        super().__init__(*args, **kwargs)


class Index(Json):
    
    def __init__(self, uuid, name, is_unique, description="", fields=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.is_unique = is_unique
        self.description = description
        if fields is None:
            fields = list()
        self.fields = fields
        super().__init__(*args, **kwargs)


class Model(Json):

    def __init__(
        self, uuid, name, display_name="", description="", is_temp=False, 
        position=None, fields=None, indexes=None, is_system=False, **kwargs):
        self.uuid = uuid
        self.name = name
        self.display_name = display_name
        self.description = description
        self.is_temp = is_temp
        self.is_extension = False
        if position is None:
            position = {"top": 49, "left": 49}
        self.position = position
        if fields is None:
            fields = list()
        self.fields = fields
        if indexes is None:
            indexes = list()
        self.indexes = indexes
        self.is_system = is_system


class Relationship(RelationshipData):

    pass
        