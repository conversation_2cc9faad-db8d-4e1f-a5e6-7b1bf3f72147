# -*- coding:utf-8 -*-

from apps.base_engine import BaseEngine

from apps.services.checker import CheckerService, DocumentCheckerService
from apps.services.checker.value import ValueCheckerService
from apps.services.checker.model import ModelDocumentCheckerService
from apps.services.checker.state import StateMachineDocumentCheckerService
from apps.services.checker.func import FuncDocumentCheckerService
from apps.services.checker.image import ImageDocumentCheckerService
from apps.services.checker.const import ConstDocumentCheckerService
from apps.services.checker.json import JsonDocumentCheckerService
from apps.services.checker.enum import EnumDocumentCheckerService
from apps.services.checker.page import PageDocumentCheckerService
from apps.services.checker.print import PrintDocumentCheckerService
from apps.services.checker.label_print import LabelPrintDocumentCheckerService
from apps.services.checker.security import SecurityDocumentCheckerService
from apps.services.checker.rulechain import Rule<PERSON>hainDocumentCheckerService
from apps.services.checker.workflow import WorkflowDocumentCheckerService
from apps.services.checker.connector import ConnectorDocumentCheckerService
from apps.services.checker.excel_template import ExcelTemplateDocumentCheckerService
from apps.services.checker.approvalflow import ApprovalFlowDocumentCheckService
from apps.services.checker.module_deploy import ModuleDeployDocumentCheckService
from apps.services.checker.restful import RestfulDocumentCheckerService
from apps.services.checker.navigation import NavigationDocumentCheckerService
from apps.services.checker.app_security import APPSecurityDocumentCheckerService
from apps.services.checker.theme import ThemeDocumentCheckerService
from apps.services.checker.third_auth import ThirdAuthDocumentCheckerService
from apps.services.checker.watermark import WatermarkDocumentCheckerService
from apps.services.checker.export_template import ExportTemplateDocumentCheckerService
from apps.services.checker.module_theme import ModuleThemeDocumentCheckerService
from apps.services.checker.expansion_pack import ExpansionpackDocumentCheckerService
from apps.services.checker.app_layout import AppLayoutDocumentCheckerService

from apps.services.ide.check import DocumentCheckService
from apps.services.ide.query import QueryService
from apps.services.ide.component_create import ComponentCreateService
from apps.services.ide.component_create import PageCreateService

class Service(object):

    def __init__(self, engine: BaseEngine):
        self.document_check = DocumentCheckService(engine)
        self.query = QueryService(engine)
        self.component_create = ComponentCreateService(engine)
        self.page_create = PageCreateService(engine)

__all__ = [
    "Service",
    "CheckerService",
    "DocumentCheckerService",
    "ValueCheckerService",
    "ModelDocumentCheckerService",
    "StateMachineDocumentCheckerService",
    "FuncDocumentCheckerService",
    "ImageDocumentCheckerService",
    "ConstDocumentCheckerService",
    "JsonDocumentCheckerService",
    "EnumDocumentCheckerService",
    "PageDocumentCheckerService",
    "PrintDocumentCheckerService",
    "LabelPrintDocumentCheckerService",
    "SecurityDocumentCheckerService",
    "RuleChainDocumentCheckerService",
    "ConnectorDocumentCheckerService",
    "ExcelTemplateDocumentCheckerService",
    "ExportTemplateDocumentCheckerService",  
    "ApprovalFlowDocumentCheckService", 
    "ModuleDeployDocumentCheckService",
    "RestfulDocumentCheckerService", 
    "NavigationDocumentCheckerService", 
    "APPSecurityDocumentCheckerService", 
    "ThemeDocumentCheckerService",
    "ThirdAuthDocumentCheckerService",
    "WatermarkDocumentCheckerService",
    "ModuleThemeDocumentCheckerService",
    "ExpansionpackDocumentCheckerService",
    "AppLayoutDocumentCheckerService",
]
