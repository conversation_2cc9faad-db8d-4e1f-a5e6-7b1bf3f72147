from __future__ import annotations

import asyncio
from typing import List, Literal, Optional, Type, Dict, TypeVar, Union, Any
import weakref

import peewee
from peewee import Model
from pydantic import BaseModel, Field
from sanic.request import Request

from apps.entity import Page, LabelPrint, DocumentContent, Document
from apps.ide_const import PageOpenType, DeviceType, ComponentType
from baseutils.log import app_log
from baseutils.utils import LemonContextVar, lemon_uuid, sign_oss_url_sync
from runtime.api.client_rpc_utils import ClientRPCRes_T, call_client_rpc
from runtime.core.namespace import LemonWrapper
from runtime.core.utils import container_validate, get_container_control_fields
from runtime.engine import engine
from runtime.protocol.client_rpc import PageRouterHistory, ClientRPCCommand, RouterHistoriesResponse, \
    PopRouterHistoryRequest, RouterContext, PushRouterHistoryRequest, OpenExternalLinkRequest, GetPrinterListResponse, \
    PrinterInfo, LoginResult, LoginType, SetItemRequest, GetItemRequest, GetItemResponse, GetUIDResponse, \
    GetMACAddressListResponse, MacAddressInfo
from runtime.protocol.design_document.layout_components import Form, DataSource
from runtime.protocol.func import RuntimePageDocumentList, PageDocumentBrief, \
    DeployLocation, DeployEnv, BaseRuntimeComponent, FuncTrigger, PrintOrientation
from runtime.protocol.func_exceptions import APIExceptions
from runtime.utils import CurrentUser

"""
对现有的api进行重新封装，新增api
"""
RT = TypeVar("RT")
MT = TypeVar("MT")


class RuntimeFormComponent(BaseRuntimeComponent):

    class ValidationResult(BaseModel):
        """
        code == 200 请求成功
        code == 400 请求失败，错误信息见msg字段
        """
        code: int
        msg: str

    def validate_fields(self, fields: List[str]):
        fields_to_be_validated = set(f.name if isinstance(f, peewee.Field) else f for f in fields)
        control_dict = get_container_control_fields(container=self.raw_component)
        data_object = self.raw_component.get_obj()
        validate_params = []
        for _, control in control_dict.items():
            field = control.edit_column
            if field.name not in fields_to_be_validated:
                continue
            value = getattr(data_object, field.name)
            if control.component_type in ComponentType.RELATION_CONTROL_LIST:
                value = [] if value is None else [value]
            validate_params.append({"field": control.uuid, "value": value})
        return self.ValidationResult(**container_validate(validate_params, container=self.raw_component))

    def get_data_obj(self):
        return self.raw_component.get_obj()


class RuntimeDataListComponent(BaseRuntimeComponent):
    @property
    def selected_objs(self) -> List[Model]:
        return self.raw_component.selected_objs


def to_form(self: BaseRuntimeComponent) -> Optional[RuntimeFormComponent]:
    if not self.raw_component or self.raw_component.component_type != ComponentType.FORM:
        return None
    return RuntimeFormComponent.parse_obj(self.dict())


def to_data_list(self: BaseRuntimeComponent) -> Optional[RuntimeDataListComponent]:
    if not self.raw_component or self.raw_component.component_type not in {
        ComponentType.DATALIST, ComponentType.CARDLIST, ComponentType.DATAGRID
    }:
        return None
    return RuntimeDataListComponent.parse_obj(self.dict())


BaseRuntimeComponent.to_form = to_form
BaseRuntimeComponent.to_data_list = to_data_list


class RuntimeDocumentsAPI:
    """
    获取来自设计时的文档信息
    """

    def __init__(self, lemon: LemonWrapper):
        self.lemon = lemon

    def get_module_page(self, module_name: str, page_name: str) -> Optional[PageDocumentBrief]:
        pages = RuntimePageDocumentList.parse_obj(self.lemon.utils.page_list)
        for page in pages.__root__:
            if page.name == page_name and page.module_name == module_name:
                return page

    def get_page_document(self, page_uuid: str) -> Optional[Page]:
        return asyncio.run_coroutine_threadsafe(
            engine.access.select_page_content_by_page_uuid(
                page_uuid, ext_tenant=self.lemon.system.current_user),
            engine.loop
        ).result()

    def get_label_template(self, module_name: str, label_name: str) -> Optional[LabelPrint]:
        module = asyncio.run_coroutine_threadsafe(
            engine.access.get_module(module_name=module_name), engine.loop
        ).result()
        if not module:
            return None

        return asyncio.run_coroutine_threadsafe(
            engine.access.get_label_print_by_module_uuid_label_name(module.module_uuid, label_name), engine.loop
        ).result()

    def get_all_pages(self) -> List[PageDocumentBrief]:
        return RuntimePageDocumentList.parse_obj(self.lemon.utils.page_list).__root__


class ClientRPCClient:
    def __init__(
            self, sid: Optional[str] = None,
            machine_id: Optional[str] = None,
            loop: Optional[asyncio.AbstractEventLoop] = None
    ) -> None:
        self._sid: Optional[str] = sid
        self._machine_id: Optional[str] = machine_id
        self.loop = loop or engine.loop

    def _call_client_rpc_sync(
            self, command: ClientRPCCommand,
            payload: Optional[BaseModel], response_class: Optional[Type[ClientRPCRes_T]], need_result: bool = True
    ) -> ClientRPCRes_T:
        future = asyncio.run_coroutine_threadsafe(
            call_client_rpc(self._sid, command, payload, response_class, need_result), self.loop
        )
        return future.result()

    def get_histories(self):
        return self._call_client_rpc_sync(
            ClientRPCCommand.ROUTE_HISTORIES, None, RouterHistoriesResponse
        )

    def pop_history(self, n: int = 1):
        return self._call_client_rpc_sync(
            ClientRPCCommand.POP_HISTORY, PopRouterHistoryRequest(n=n), None
        )

    def push_history(
            self, page_uuid: str, parent_machine_id: Optional[str], page_title: Optional[str],
            query: Optional[Dict[str, str]], context: Optional[RouterContext],
            ignore_parent_machine_id: bool = False, with_tab: bool = False, intervalInfo: dict = dict()):
        query = query or {}
        return self._call_client_rpc_sync(
            ClientRPCCommand.PUSH_HISTORY,
            PushRouterHistoryRequest(
                page_uuid=page_uuid,
                parent_machine_id=parent_machine_id,
                page_title=page_title,
                query=query,
                context=context,
                ignore_parent_machine_id=ignore_parent_machine_id,
                with_tab=with_tab,
                intervalInfo=intervalInfo
            ),
            None
        )

    def open_external_link(self, url: str, target: Literal['_self', '_blank']):
        return self._call_client_rpc_sync(
            ClientRPCCommand.OPEN_EXTERNAL_LINK,
            OpenExternalLinkRequest(url=url, target=target),
            None
        )

    def get_printer_list(self):
        return self._call_client_rpc_sync(
            ClientRPCCommand.GET_PRINTER_LIST,
            None,
            GetPrinterListResponse
        )

    def login(self, login_result: LoginResult):
        return self._call_client_rpc_sync(ClientRPCCommand.LOGIN, login_result, None, need_result=False)

    def logout(self):
        return self._call_client_rpc_sync(ClientRPCCommand.LOGOUT, None, None, need_result=False)

    def set_localstorage(self, key: str, value: str):
        return self._call_client_rpc_sync(ClientRPCCommand.SET_LOCALSTORAGE, SetItemRequest(key=key, value=value), None)

    def get_localstorage(self, key: str) -> Optional[str]:
        return self._call_client_rpc_sync(
            ClientRPCCommand.GET_LOCALSTORAGE, GetItemRequest(key=key), GetItemResponse
        ).value

    def get_uid(self):
        return self._call_client_rpc_sync(ClientRPCCommand.GET_UID, None, GetUIDResponse)

    def get_mac_address_list(self):
        return self._call_client_rpc_sync(ClientRPCCommand.GET_MAC_ADDRESS_LIST, None, GetMACAddressListResponse)


async def print_label(
        page: Any, label: LabelPrint, pk_list: List[int], tenant_uuid: str, printer_name: str,
        print_type: Optional[str] = None, is_skip_preview: Optional[bool] = False
):
    app_log.info("print_label method called, pk_list: %s printer_name: %s, print_type: %s",
                 pk_list, printer_name, print_type)
    print_dict = await engine.access.select_label_content_by_label_uuid(
        label.label_uuid, ext_tenant=tenant_uuid)
    if not print_dict:
        raise APIExceptions.LabelTemplateNotFound(label.label_uuid, label.label_name)

    print_content = print_dict.get(DocumentContent.document_content.name)
    print_content.update({Document.document_type.name: print_dict.get(Document.document_type.name)})
    model_uuid = print_content.get("data_source", {}).get("model")
    print_type = print_type or print_content.get("code_type", None)
    form_component = engine.component_creator.create_component(
        component_type=ComponentType.FORM, control=False,
        component_dict=Form(data_source=DataSource(model=model_uuid, model_name="")).dict(by_alias=True),
        parent=page
    )
    need_merge = print_type == "PDF" and len(pk_list) > 1
    merge_data = {} if need_merge else None
    await form_component.start(view=False)

    app_log.info(f"run_form_label pk: %s, merge_data: %s", pk_list, merge_data)
    try:
        await form_component._run_form_label(
            label_uuid=label.label_uuid, pk_list=pk_list,
            print_content=print_content, print_type=print_type, printer_name=printer_name,
            merge_data=merge_data, is_skip_preview=is_skip_preview
        )
    except Exception as e:
        app_log.exception(f"run_form_label error: %s", e)
    app_log.info(f"after run_form_label merge_data: %s", merge_data)

    if need_merge and merge_data:
        json_body = merge_data.get("json_body")
        post_url = merge_data.get("post_url")
        app_log.info(f"get_pdf_url: json_body: %s, post_url: %s, print_type: %s", json_body, post_url, print_type)
        pdf_url, return_code = await form_component.get_pdf_url(json_body, post_url, print_type)
        app_log.info(f"got pdf url: %s, return_code: %s", pdf_url, return_code)
        await form_component.send_print_message(
            lemon_uuid(), pdf_url, print_type, None, return_code=return_code, printer_name=printer_name,
            is_skip_preview=is_skip_preview)


async def print_by_fileurl(
        page: Any, file_url: str, model_uuid: str, printer_name: str,
        print_type: Optional[str] = "pdf", is_skip_preview: Optional[bool] = False, 
        page_size: Optional[str] = "", wps_dir: Optional[str] = "",
        orientation=None):
    form_component = engine.component_creator.create_component(
        component_type=ComponentType.FORM, control=False,
        component_dict=Form(data_source=DataSource(model=model_uuid, model_name="")).dict(by_alias=True),
        parent=page
    )
    accessible_url = sign_oss_url_sync(engine, file_url, "GET", 360, slash_safe=True)
    await form_component.start(view=False)
    await form_component.send_print_message(
        lemon_uuid(), [accessible_url], print_type, return_code=None, need_prefix=False,
        printer_name=printer_name, is_skip_preview=is_skip_preview,  page_size=page_size,
        wps_dir=wps_dir, orientation=orientation)


class LemonDevicesAPI:
    PrintOrientation = PrintOrientation
    PrinterInfo = PrinterInfo

    def __init__(self, lemon, sid: Optional[str] = None, machine_id: Optional[str] = None) -> None:
        self._machine_id = machine_id
        self._sid = sid
        self.lemon = lemon
        self._client_rpc = ClientRPCClient(sid, machine_id, engine.loop)

    def get_printer_list(self) -> List[PrinterInfo]:
        return self._client_rpc.get_printer_list().printers

    def get_uid(self) -> str:
        return self._client_rpc.get_uid().uid

    def get_mac_address_list(self) -> List[MacAddressInfo]:
        return self._client_rpc.get_mac_address_list().mac_addresses

    def print_label(self, template: LabelPrint, pk_list: List[int], printer_name: str = "",
                    print_type: Optional[str] = None, is_skip_preview: Optional[bool] = False):
        task = asyncio.run_coroutine_threadsafe(
            print_label(
                self.lemon.utils.get_page(), template, pk_list,
                self.lemon.system.current_user.tenant_uuid, printer_name, print_type, is_skip_preview
            ),
            engine.loop
        )
        if exc := task.exception():
            raise exc
        return task.result()

    def print_by_fileurl(
            self, file_url: str, model_uuid: str, printer_name: str = "",
            print_type: Optional[str] = "pdf", is_skip_preview: Optional[bool] = False,
            page_size: Optional[str] = "", wps_dir: Optional[str] = "", orientation="portrait"):
        task = asyncio.run_coroutine_threadsafe(
            print_by_fileurl(
                self.lemon.utils.get_page(), file_url, model_uuid, printer_name, print_type, is_skip_preview,
                page_size=page_size, wps_dir=wps_dir, orientation=orientation
                ),
            engine.loop
        )
        if exc := task.exception():
            raise exc
        return task.result()


class LemonRuntimeRouter:
    def __init__(self, lemon, sid: Optional[str] = None, machine_id: Optional[str] = None) -> None:
        self._machine_id = machine_id
        self._sid = sid
        self.lemon = lemon
        self._client_rpc = ClientRPCClient(sid, machine_id, engine.loop)

    def histories(self) -> List[PageRouterHistory]:
        return self._client_rpc.get_histories().histories

    def pop_history(self, n: int = 1) -> None:
        if n <= 0:
            return
        open_type = self.lemon.utils.get_page().component_dict.get("open_type")

        if self.lemon.system.current_user.device_type != DeviceType.MOBILE and open_type != PageOpenType.JUMP:
            raise ValueError("当前API暂不支持在非移动端的弹窗/抽屉页面中调用")

        return self._client_rpc.pop_history(n)

    def push_history(self, page_uuid: str, page_title: Optional[str], query: Optional[Dict[str, str]],
                     context: Optional[RouterContext], parent_machine_id: str,
                     ignore_parent_machine_id: bool = False, with_tab: bool = False, intervalInfo: dict = dict()) -> None:
        return self._client_rpc.push_history(
            page_uuid, parent_machine_id, page_title, query, context, ignore_parent_machine_id, with_tab, intervalInfo)

    def close_page(self) -> None:
        return self.pop_history()

    def open_page(
            self, module_name: str, page_name: str, title: Optional[str] = None,
            context_pk: Optional[int] = None, independent: bool = True,
            ignore_parent_machine_id: bool = False, with_tab: bool = False, intervalId: str = ''
    ) -> None:
        """
        新页面，关联，编辑/新增
        新页面不关联，编辑/新增
        """
        api = RuntimeDocumentsAPI(self.lemon)
        page = api.get_module_page(module_name, page_name)
        if not page:
            raise APIExceptions.ModulePageNotFound(module_name, page_name)

        parent_machine_uuid = None
        context = None

        if independent:  # 弹窗页面不能独立打开
            page_info = api.get_page_document(page.page_uuid)
            if page_info.get("open_type") != PageOpenType.JUMP:
                independent = False
            if self.lemon.system.current_user.device_type != DeviceType.PC:
                independent = False

        if not independent:
            parent_machine_uuid = self._machine_id
        if context_pk:
            context = RouterContext(pk=context_pk)

        if parent_machine_uuid and context:
            context.current_page = self.lemon.utils.get_page().uuid
        if intervalId:
            intervalInfo = {"intervalId": intervalId}
        else:
            intervalInfo = {}
        return self.push_history(
            page_uuid=page.page_uuid, page_title=title, query=None,
            context=context, parent_machine_id=parent_machine_uuid,
            ignore_parent_machine_id=ignore_parent_machine_id, with_tab=with_tab, intervalInfo=intervalInfo
        )
        
    def interval_open_page(
            self,  page_list, independent: bool = True,
            ignore_parent_machine_id: bool = False, with_tab: bool = False, intervalTime: int = 0, intervalId: str = ''
    ) -> None:
        """
        新页面，关联，编辑/新增
        新页面不关联，编辑/新增
        """
        
        api = RuntimeDocumentsAPI(self.lemon)
        send_page_list = []
        for page_info in page_list:
            module_name = page_info.get("module_name")
            page_name = page_info.get("page_name")
            title = page_info.get('title')
            context_pk = page_info.get("context_pk")
            page = api.get_module_page(module_name, page_name)
            if not page:
                raise APIExceptions.ModulePageNotFound(module_name, page_name)

            parent_machine_uuid = None
            context = None

            if independent:  # 弹窗页面不能独立打开
                page_info = api.get_page_document(page.page_uuid)
                if page_info.get("open_type") != PageOpenType.JUMP:
                    independent = False
                if self.lemon.system.current_user.device_type != DeviceType.PC:
                    independent = False

            if not independent:
                parent_machine_uuid = self._machine_id
            if context_pk:
                context = RouterContext(pk=context_pk)

            if parent_machine_uuid and context:
                context.current_page = self.lemon.utils.get_page().uuid
            send_page_list.append(dict(page_uuid=page.page_uuid, page_title=title, context=context))
        intervalInfo = {'page_list': send_page_list, 'intervalTime': intervalTime, 'intervalId': intervalId}
        return self.push_history(
            page_uuid=page.page_uuid, page_title=title, query=None,
            context=context, parent_machine_id=parent_machine_uuid,
            ignore_parent_machine_id=ignore_parent_machine_id, with_tab=with_tab,
            intervalInfo=intervalInfo
        )

    def open_external_link(self, url: str, in_new_tab: bool = True) -> None:
        target: Literal['_blank', '_self'] = '_blank' if in_new_tab else '_self'
        return self._client_rpc.open_external_link(url, target)


class LemonViewAPI:
    class PopupButton(BaseModel):
        title: str
        value: str
        button_type: str
        shape: str
        size: str
        is_danger: bool = False
        is_ghost: bool = False
        button_height: Optional[int] = Field(None, alias="buttonheight")
        button_width: Optional[int] = Field(None, alias="buttonwidth")

        def __init__(
                self, title: str, value: str,
                button_type: Literal["default"] = "default",
                shape: Literal[""] = "default",
                size="middle", is_danger=False, is_ghost=False, button_height=None, button_width=None
        ):
            super().__init__(
                title=title, value=value, button_type=button_type, shape=shape, size=size, is_danger=is_danger,
                is_ghost=is_ghost, button_height=button_height, button_width=button_width
            )

    def __init__(self, lemon: LemonWrapper, sid: str, machine_id: str):
        self._lemon = lemon
        self._sid = sid
        self._machine_id = machine_id
        self._client_rpc = ClientRPCClient(sid, machine_id, engine.loop)

    def show_message(
            self, alert_type: Literal["info", "warning", "error"], message="", title="",
            button_content="", show_button=True, show_title=True
    ):
        if show_button and not button_content:
            button_content = "确定"
        self._lemon.utils.message_box.send_alert_message(
            alert_type, message, title, button_content, show_button, show_title
        )

    def info(
            self, message: str, title: str = "", show_title: bool = True,
            show_button: bool = False, button_content: str = ""
    ) -> None:
        return self.show_message("info", message, title, button_content, show_button, show_title)

    def error(
            self, message: str, title: str = "", show_title: bool = True,
            show_button: bool = False, button_content: str = ""
    ) -> None:
        return self.show_message("error", message, title, button_content, show_title, show_button)

    def warning(
            self, message: str, title: str = "", show_title: bool = True,
            show_button: bool = False, button_content: str = ""
    ) -> None:
        return self.show_message("warning", message, title, button_content, show_title, show_button)

    def show_confirm_popup(self, title: str, message: str, buttons: List[PopupButton]):
        return self._lemon.utils.popup_submit.send_submit(
            title, message,
            *(b.dict(by_alias=True, exclude_none=True) for b in buttons)
        )

    def get_component(self, name: str, page: Optional[Dict] = None) -> Optional[BaseRuntimeComponent]:
        if isinstance(page, RuntimePage):
            page = page.raw_component
        container = self._lemon.utils.get_container_by_name(name, page)
        if not container:
            return None
        return BaseRuntimeComponent(name=container.name, page_name=container.page.name,
                                    component_type=container.component_type,
                                    raw_component_accessor=weakref.ref(container))

    def get_form(self, name: str, page: Optional[Dict] = None) -> Optional['RuntimeFormComponent']:
        component = self.get_component(name, page)
        if not component:
            return None
        return component.to_form()

    def get_data_list(self, name, page: Optional[Dict] = None) -> Optional[RuntimeDataListComponent]:
        component = self.get_component(name, page)
        if not component:
            return None
        return component.to_data_list()


class RuntimePage(BaseRuntimeComponent):
    lemon: LemonWrapper

    @classmethod
    def from_raw_page(cls, page, lemon: LemonWrapper) -> Optional[RuntimePage]:
        if not page:
            return None
        return RuntimePage(
            name=page.name, page_name=page.name, component_type=page.component_type, lemon=lemon
        )

    def get_parent_page(self) -> Optional['RuntimePage']:
        parent = self.raw_component.parent
        if not parent or parent.component_type != ComponentType.PAGE:
            return None
        return RuntimePage.from_raw_page(parent, self.lemon)

    def get_container(self, name: str) -> Optional[BaseRuntimeComponent]:
        container_ = self.lemon.utils.get_container_by_name(name, self.raw_component)
        if not container_:
            return None
        return BaseRuntimeComponent(
            name=container_.name, page_name=container_.page.name,
            component_type=container_.component_type,
            raw_component_accessor=weakref.ref(container_)
        )

    def get_form(self, name: str) -> Optional[RuntimeFormComponent]:
        container = self.get_container(name)
        if not container:
            return None
        return container.to_form()

    def get_data_list(self, name: str) -> Optional[RuntimeFormComponent]:
        container = self.get_container(name)
        if not container:
            return None
        return container.to_data_list()

    @property
    def p_context(self) -> Dict:
        return self.raw_component.p_context

    @property
    def parent(self) -> Optional[RuntimePage]:
        return self.get_parent_page()

    @property
    def globals(self) -> Any:
        try:
            return self.raw_component.sm.globals
        except:
            return {}


async def login(
        sid: str, account: str, code: str, login_type: LoginType, tenant_id: str = "", auto_login: bool = True,
        restful_request: bool = False, extra_res: dict = None
) -> LoginResult:
    referer = f"{engine.config.APP_LOCALTION}/user/login"
    request = Request(
        url_bytes=b"/api/auth/login.json",
        headers={"Referer": referer},
        method="POST",
        transport=None,
        app=engine.app,
        version="1.1",
    )
    data = {"username": account, "password": code, "login_type": login_type.value}
    if tenant_id:
        data.update({"tenant_id": tenant_id})
    result_json = await engine.cas_client.write_and_read(
        request, verify_user=True, **data
    )
    code = result_json.get("code")
    message = result_json.get("message")
    if code != 200:
        raise APIExceptions.LoginError(message)
    data = result_json.get("data", {})
    if not extra_res:
        extra_res = {}
    res = LoginResult(
        access_token=data.get("access_token", ""),
        user_name=data.get("user_name", ""),
        user_uuid=data.get("user_uuid", ""),
        auto_login=auto_login,
        login_type=login_type,
        extra_res=extra_res
    )

    if not restful_request:
        client = engine.clients.get(sid)
        if not client:
            raise KeyError(f"client with sid={sid} not found")
        client.token = res.access_token
        await client.handle_on_open(send_msg=False)
    return res


class LemonAuthAPI:
    LoginError = APIExceptions.LoginError

    def __init__(self, lemon: LemonWrapper, sid: str, machine_id: str):
        self._lemon = lemon
        self._sid = sid
        self._machine_id = machine_id
        self._client_rpc = ClientRPCClient(sid, machine_id, engine.loop)

    def _get_login_result(
            self, account: str, password: str, login_type: LoginType, tenant_id: str = "", auto_login: bool = True,
            extra_res: dict = None
    ) -> LoginResult:
        task = asyncio.run_coroutine_threadsafe(
            login(self._sid, account, password, login_type, tenant_id, auto_login, extra_res=extra_res),
            engine.loop
        )
        if exc := task.exception():
            raise exc
        return task.result()

    def login_by_username(
        self, username: str, password: str, auto_login: bool = True, redirect_path: str = None, 
        query: dict = None, extra_res: dict = None
    ):
        login_result = self._get_login_result(
            username, password, login_type=LoginType.PASSWORD, auto_login=auto_login, extra_res=extra_res
        )
        login_result.redirect_path = redirect_path
        login_result.query = query
        self._client_rpc.login(login_result)

    def login_by_job_number(
        self, job_number: str, password: str, company_code: str, redirect_path: str = None, 
        query: dict = None, extra_res: dict = None
    ):
        login_result = self._get_login_result(job_number, password, LoginType.JOBNUMBER, company_code, True, extra_res)
        login_result.redirect_path = redirect_path
        login_result.query = query
        self._client_rpc.login(login_result)

    def logout(self):
        self._client_rpc.logout()


class LocalStorage:
    def __init__(self, sid: str, machine_id: str):
        self._sid = sid
        self._machine_id = machine_id
        self._client_rpc = ClientRPCClient(sid, machine_id, engine.loop)

    def get_item(self, key: str) -> Optional[str]:
        return self._client_rpc.get_localstorage(key)

    def set_item(self, key: str, value: str):
        return self._client_rpc.set_localstorage(key, value)


class LemonRuntimeContext:
    def __init__(self, lemon: LemonWrapper, raw_component: Optional[Any], sid: str, machine_id: str):
        self._lemon = lemon
        self._component = None
        self.local_storage: Optional[LocalStorage] = LocalStorage(sid, machine_id) if sid else None

        if raw_component is None:
            return

        component_type = getattr(raw_component, "component_type", None)
        if component_type is None:
            return

        page = getattr(raw_component, "page", None)
        component_class = RuntimeFormComponent if component_type == ComponentType.FORM \
            else BaseRuntimeComponent
        self._component: Optional[Union[BaseRuntimeComponent, RuntimeFormComponent]] = component_class(
            name=raw_component.name, page_name=page.name if page else "",
            component_type=component_type, raw_component_accessor=weakref.ref(raw_component)
        ) if raw_component else None

    @property
    def component(self) -> Optional[Union[BaseRuntimeComponent, RuntimeFormComponent]]:
        return self._component

    @property
    def current_page(self):
        return self._lemon.utils.get_page()

    @property
    def router(self) -> Optional[LemonRuntimeRouter]:
        return self._lemon.router

    @property
    def view(self) -> Optional[LemonViewAPI]:
        return self._lemon.view

    @property
    def func_trigger_stack(self) -> Optional[FuncTrigger]:
        return LemonContextVar.trigger_event.get()

    @property
    def current_obj(self) -> Optional[Model]:
        return self._lemon.system.obj

    @property
    def selected_objs(self) -> Optional[List[Model]]:
        return self._lemon.system.selected_objs

    @property
    def deploy_location(self) -> DeployLocation:
        return DeployLocation(self._lemon.system.app_location)

    @property
    def deploy_env(self) -> DeployEnv:
        return DeployEnv(self._lemon.system.current_env.name)

    @property
    def app_base_url(self) -> str:
        return self._lemon.system.app_location

    @property
    def user(self) -> CurrentUser:
        return self._lemon.system.current_user

    @property
    def page(self) -> Optional[RuntimePage]:
        if not self._component:
            return None
        component_type = getattr(self._component, "component_type", None)
        if component_type == ComponentType.PAGE:
            return RuntimePage.from_raw_page(self.component.raw_component, self._lemon)
        return RuntimePage.from_raw_page(getattr(self._component.raw_component, "page", None), self._lemon)

    def get_scan_result(self) -> str:
        return self._lemon.system.sqcode
