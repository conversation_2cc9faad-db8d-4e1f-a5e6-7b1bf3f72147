# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: exhook.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'exhook.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x65xhook.proto\x12\x0e\x65mqx.exhook.v2\"n\n\x15ProviderLoadedRequest\x12*\n\x06\x62roker\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.BrokerInfo\x12)\n\x04meta\x18\x02 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"D\n\x17ProviderUnloadedRequest\x12)\n\x04meta\x18\x01 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\x96\x01\n\x14\x43lientConnectRequest\x12*\n\x08\x63onninfo\x18\x01 \x01(\x0b\x32\x18.emqx.exhook.v2.ConnInfo\x12\'\n\x05props\x18\x02 \x03(\x0b\x32\x18.emqx.exhook.v2.Property\x12)\n\x04meta\x18\x03 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\xab\x01\n\x14\x43lientConnackRequest\x12*\n\x08\x63onninfo\x18\x01 \x01(\x0b\x32\x18.emqx.exhook.v2.ConnInfo\x12\x13\n\x0bresult_code\x18\x02 \x01(\t\x12\'\n\x05props\x18\x03 \x03(\x0b\x32\x18.emqx.exhook.v2.Property\x12)\n\x04meta\x18\x04 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"s\n\x16\x43lientConnectedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12)\n\x04meta\x18\x02 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\x86\x01\n\x19\x43lientDisconnectedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12\x0e\n\x06reason\x18\x02 \x01(\t\x12)\n\x04meta\x18\x03 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\x86\x01\n\x19\x43lientAuthenticateRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12\x0e\n\x06result\x18\x02 \x01(\x08\x12)\n\x04meta\x18\x03 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\x89\x02\n\x16\x43lientAuthorizeRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12\x45\n\x04type\x18\x02 \x01(\x0e\x32\x37.emqx.exhook.v2.ClientAuthorizeRequest.AuthorizeReqType\x12\r\n\x05topic\x18\x03 \x01(\t\x12\x0e\n\x06result\x18\x04 \x01(\x08\x12)\n\x04meta\x18\x05 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\".\n\x10\x41uthorizeReqType\x12\x0b\n\x07PUBLISH\x10\x00\x12\r\n\tSUBSCRIBE\x10\x01\"\xd0\x01\n\x16\x43lientSubscribeRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12\'\n\x05props\x18\x02 \x03(\x0b\x32\x18.emqx.exhook.v2.Property\x12\x32\n\rtopic_filters\x18\x03 \x03(\x0b\x32\x1b.emqx.exhook.v2.TopicFilter\x12)\n\x04meta\x18\x04 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\xd2\x01\n\x18\x43lientUnsubscribeRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12\'\n\x05props\x18\x02 \x03(\x0b\x32\x18.emqx.exhook.v2.Property\x12\x32\n\rtopic_filters\x18\x03 \x03(\x0b\x32\x1b.emqx.exhook.v2.TopicFilter\x12)\n\x04meta\x18\x04 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"r\n\x15SessionCreatedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12)\n\x04meta\x18\x02 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\xae\x01\n\x18SessionSubscribedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12\r\n\x05topic\x18\x02 \x01(\t\x12(\n\x07subopts\x18\x03 \x01(\x0b\x32\x17.emqx.exhook.v2.SubOpts\x12)\n\x04meta\x18\x04 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\x86\x01\n\x1aSessionUnsubscribedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12\r\n\x05topic\x18\x02 \x01(\t\x12)\n\x04meta\x18\x03 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"r\n\x15SessionResumedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12)\n\x04meta\x18\x02 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"t\n\x17SessionDiscardedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12)\n\x04meta\x18\x02 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"t\n\x17SessionTakenoverRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12)\n\x04meta\x18\x02 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\x85\x01\n\x18SessionTerminatedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12\x0e\n\x06reason\x18\x02 \x01(\t\x12)\n\x04meta\x18\x03 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"l\n\x15MessagePublishRequest\x12(\n\x07message\x18\x01 \x01(\x0b\x32\x17.emqx.exhook.v2.Message\x12)\n\x04meta\x18\x02 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\x9e\x01\n\x17MessageDeliveredRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12(\n\x07message\x18\x02 \x01(\x0b\x32\x17.emqx.exhook.v2.Message\x12)\n\x04meta\x18\x03 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"|\n\x15MessageDroppedRequest\x12(\n\x07message\x18\x01 \x01(\x0b\x32\x17.emqx.exhook.v2.Message\x12\x0e\n\x06reason\x18\x02 \x01(\t\x12)\n\x04meta\x18\x03 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"\x9a\x01\n\x13MessageAckedRequest\x12.\n\nclientinfo\x18\x01 \x01(\x0b\x32\x1a.emqx.exhook.v2.ClientInfo\x12(\n\x07message\x18\x02 \x01(\x0b\x32\x17.emqx.exhook.v2.Message\x12)\n\x04meta\x18\x03 \x01(\x0b\x32\x1b.emqx.exhook.v2.RequestMeta\"9\n\x0eLoadedResponse\x12\'\n\x05hooks\x18\x01 \x03(\x0b\x32\x18.emqx.exhook.v2.HookSpec\"\xd8\x01\n\x0eValuedResponse\x12:\n\x04type\x18\x01 \x01(\x0e\x32,.emqx.exhook.v2.ValuedResponse.ResponsedType\x12\x15\n\x0b\x62ool_result\x18\x03 \x01(\x08H\x00\x12*\n\x07message\x18\x04 \x01(\x0b\x32\x17.emqx.exhook.v2.MessageH\x00\">\n\rResponsedType\x12\x0c\n\x08\x43ONTINUE\x10\x00\x12\n\n\x06IGNORE\x10\x01\x12\x13\n\x0fSTOP_AND_RETURN\x10\x02\x42\x07\n\x05value\"\x0e\n\x0c\x45mptySuccess\"Q\n\nBrokerInfo\x12\x0f\n\x07version\x18\x01 \x01(\t\x12\x10\n\x08sysdescr\x18\x02 \x01(\t\x12\x0e\n\x06uptime\x18\x03 \x01(\x03\x12\x10\n\x08\x64\x61tetime\x18\x04 \x01(\t\"(\n\x08HookSpec\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06topics\x18\x02 \x03(\t\"\x9a\x01\n\x08\x43onnInfo\x12\x0c\n\x04node\x18\x01 \x01(\t\x12\x10\n\x08\x63lientid\x18\x02 \x01(\t\x12\x10\n\x08username\x18\x03 \x01(\t\x12\x10\n\x08peerhost\x18\x04 \x01(\t\x12\x10\n\x08sockport\x18\x05 \x01(\r\x12\x12\n\nproto_name\x18\x06 \x01(\t\x12\x11\n\tproto_ver\x18\x07 \x01(\t\x12\x11\n\tkeepalive\x18\x08 \x01(\r\"\xdb\x01\n\nClientInfo\x12\x0c\n\x04node\x18\x01 \x01(\t\x12\x10\n\x08\x63lientid\x18\x02 \x01(\t\x12\x10\n\x08username\x18\x03 \x01(\t\x12\x10\n\x08password\x18\x04 \x01(\t\x12\x10\n\x08peerhost\x18\x05 \x01(\t\x12\x10\n\x08sockport\x18\x06 \x01(\r\x12\x10\n\x08protocol\x18\x07 \x01(\t\x12\x12\n\nmountpoint\x18\x08 \x01(\t\x12\x14\n\x0cis_superuser\x18\t \x01(\x08\x12\x11\n\tanonymous\x18\n \x01(\x08\x12\n\n\x02\x63n\x18\x0b \x01(\t\x12\n\n\x02\x64n\x18\x0c \x01(\t\"\xd8\x01\n\x07Message\x12\x0c\n\x04node\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\t\x12\x0b\n\x03qos\x18\x03 \x01(\r\x12\x0c\n\x04\x66rom\x18\x04 \x01(\t\x12\r\n\x05topic\x18\x05 \x01(\t\x12\x0f\n\x07payload\x18\x06 \x01(\x0c\x12\x11\n\ttimestamp\x18\x07 \x01(\x04\x12\x35\n\x07headers\x18\x08 \x03(\x0b\x32$.emqx.exhook.v2.Message.HeadersEntry\x1a.\n\x0cHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\'\n\x08Property\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"(\n\x0bTopicFilter\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03qos\x18\x02 \x01(\r\"J\n\x07SubOpts\x12\x0b\n\x03qos\x18\x01 \x01(\r\x12\r\n\x05share\x18\x02 \x01(\t\x12\n\n\x02rh\x18\x03 \x01(\r\x12\x0b\n\x03rap\x18\x04 \x01(\r\x12\n\n\x02nl\x18\x05 \x01(\r\"T\n\x0bRequestMeta\x12\x0c\n\x04node\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x10\n\x08sysdescr\x18\x03 \x01(\t\x12\x14\n\x0c\x63luster_name\x18\x04 \x01(\t2\xc7\x0f\n\x0cHookProvider\x12[\n\x10OnProviderLoaded\x12%.emqx.exhook.v2.ProviderLoadedRequest\x1a\x1e.emqx.exhook.v2.LoadedResponse\"\x00\x12]\n\x12OnProviderUnloaded\x12\'.emqx.exhook.v2.ProviderUnloadedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12W\n\x0fOnClientConnect\x12$.emqx.exhook.v2.ClientConnectRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12W\n\x0fOnClientConnack\x12$.emqx.exhook.v2.ClientConnackRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12[\n\x11OnClientConnected\x12&.emqx.exhook.v2.ClientConnectedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12\x61\n\x14OnClientDisconnected\x12).emqx.exhook.v2.ClientDisconnectedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12\x63\n\x14OnClientAuthenticate\x12).emqx.exhook.v2.ClientAuthenticateRequest\x1a\x1e.emqx.exhook.v2.ValuedResponse\"\x00\x12]\n\x11OnClientAuthorize\x12&.emqx.exhook.v2.ClientAuthorizeRequest\x1a\x1e.emqx.exhook.v2.ValuedResponse\"\x00\x12[\n\x11OnClientSubscribe\x12&.emqx.exhook.v2.ClientSubscribeRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12_\n\x13OnClientUnsubscribe\x12(.emqx.exhook.v2.ClientUnsubscribeRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12Y\n\x10OnSessionCreated\x12%.emqx.exhook.v2.SessionCreatedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12_\n\x13OnSessionSubscribed\x12(.emqx.exhook.v2.SessionSubscribedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12\x63\n\x15OnSessionUnsubscribed\x12*.emqx.exhook.v2.SessionUnsubscribedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12Y\n\x10OnSessionResumed\x12%.emqx.exhook.v2.SessionResumedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12]\n\x12OnSessionDiscarded\x12\'.emqx.exhook.v2.SessionDiscardedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12]\n\x12OnSessionTakenover\x12\'.emqx.exhook.v2.SessionTakenoverRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12_\n\x13OnSessionTerminated\x12(.emqx.exhook.v2.SessionTerminatedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12[\n\x10OnMessagePublish\x12%.emqx.exhook.v2.MessagePublishRequest\x1a\x1e.emqx.exhook.v2.ValuedResponse\"\x00\x12]\n\x12OnMessageDelivered\x12\'.emqx.exhook.v2.MessageDeliveredRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12Y\n\x10OnMessageDropped\x12%.emqx.exhook.v2.MessageDroppedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x12U\n\x0eOnMessageAcked\x12#.emqx.exhook.v2.MessageAckedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\"\x00\x42I\n\x0eio.emqx.exhookB\x0f\x45mqxExHookProtoP\x01Z\x13\x65mqx.io/grpc/exhook\xaa\x02\x0e\x45mqx.Exhook.V2b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'exhook_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\016io.emqx.exhookB\017EmqxExHookProtoP\001Z\023emqx.io/grpc/exhook\252\002\016Emqx.Exhook.V2'
  _globals['_MESSAGE_HEADERSENTRY']._loaded_options = None
  _globals['_MESSAGE_HEADERSENTRY']._serialized_options = b'8\001'
  _globals['_PROVIDERLOADEDREQUEST']._serialized_start=32
  _globals['_PROVIDERLOADEDREQUEST']._serialized_end=142
  _globals['_PROVIDERUNLOADEDREQUEST']._serialized_start=144
  _globals['_PROVIDERUNLOADEDREQUEST']._serialized_end=212
  _globals['_CLIENTCONNECTREQUEST']._serialized_start=215
  _globals['_CLIENTCONNECTREQUEST']._serialized_end=365
  _globals['_CLIENTCONNACKREQUEST']._serialized_start=368
  _globals['_CLIENTCONNACKREQUEST']._serialized_end=539
  _globals['_CLIENTCONNECTEDREQUEST']._serialized_start=541
  _globals['_CLIENTCONNECTEDREQUEST']._serialized_end=656
  _globals['_CLIENTDISCONNECTEDREQUEST']._serialized_start=659
  _globals['_CLIENTDISCONNECTEDREQUEST']._serialized_end=793
  _globals['_CLIENTAUTHENTICATEREQUEST']._serialized_start=796
  _globals['_CLIENTAUTHENTICATEREQUEST']._serialized_end=930
  _globals['_CLIENTAUTHORIZEREQUEST']._serialized_start=933
  _globals['_CLIENTAUTHORIZEREQUEST']._serialized_end=1198
  _globals['_CLIENTAUTHORIZEREQUEST_AUTHORIZEREQTYPE']._serialized_start=1152
  _globals['_CLIENTAUTHORIZEREQUEST_AUTHORIZEREQTYPE']._serialized_end=1198
  _globals['_CLIENTSUBSCRIBEREQUEST']._serialized_start=1201
  _globals['_CLIENTSUBSCRIBEREQUEST']._serialized_end=1409
  _globals['_CLIENTUNSUBSCRIBEREQUEST']._serialized_start=1412
  _globals['_CLIENTUNSUBSCRIBEREQUEST']._serialized_end=1622
  _globals['_SESSIONCREATEDREQUEST']._serialized_start=1624
  _globals['_SESSIONCREATEDREQUEST']._serialized_end=1738
  _globals['_SESSIONSUBSCRIBEDREQUEST']._serialized_start=1741
  _globals['_SESSIONSUBSCRIBEDREQUEST']._serialized_end=1915
  _globals['_SESSIONUNSUBSCRIBEDREQUEST']._serialized_start=1918
  _globals['_SESSIONUNSUBSCRIBEDREQUEST']._serialized_end=2052
  _globals['_SESSIONRESUMEDREQUEST']._serialized_start=2054
  _globals['_SESSIONRESUMEDREQUEST']._serialized_end=2168
  _globals['_SESSIONDISCARDEDREQUEST']._serialized_start=2170
  _globals['_SESSIONDISCARDEDREQUEST']._serialized_end=2286
  _globals['_SESSIONTAKENOVERREQUEST']._serialized_start=2288
  _globals['_SESSIONTAKENOVERREQUEST']._serialized_end=2404
  _globals['_SESSIONTERMINATEDREQUEST']._serialized_start=2407
  _globals['_SESSIONTERMINATEDREQUEST']._serialized_end=2540
  _globals['_MESSAGEPUBLISHREQUEST']._serialized_start=2542
  _globals['_MESSAGEPUBLISHREQUEST']._serialized_end=2650
  _globals['_MESSAGEDELIVEREDREQUEST']._serialized_start=2653
  _globals['_MESSAGEDELIVEREDREQUEST']._serialized_end=2811
  _globals['_MESSAGEDROPPEDREQUEST']._serialized_start=2813
  _globals['_MESSAGEDROPPEDREQUEST']._serialized_end=2937
  _globals['_MESSAGEACKEDREQUEST']._serialized_start=2940
  _globals['_MESSAGEACKEDREQUEST']._serialized_end=3094
  _globals['_LOADEDRESPONSE']._serialized_start=3096
  _globals['_LOADEDRESPONSE']._serialized_end=3153
  _globals['_VALUEDRESPONSE']._serialized_start=3156
  _globals['_VALUEDRESPONSE']._serialized_end=3372
  _globals['_VALUEDRESPONSE_RESPONSEDTYPE']._serialized_start=3301
  _globals['_VALUEDRESPONSE_RESPONSEDTYPE']._serialized_end=3363
  _globals['_EMPTYSUCCESS']._serialized_start=3374
  _globals['_EMPTYSUCCESS']._serialized_end=3388
  _globals['_BROKERINFO']._serialized_start=3390
  _globals['_BROKERINFO']._serialized_end=3471
  _globals['_HOOKSPEC']._serialized_start=3473
  _globals['_HOOKSPEC']._serialized_end=3513
  _globals['_CONNINFO']._serialized_start=3516
  _globals['_CONNINFO']._serialized_end=3670
  _globals['_CLIENTINFO']._serialized_start=3673
  _globals['_CLIENTINFO']._serialized_end=3892
  _globals['_MESSAGE']._serialized_start=3895
  _globals['_MESSAGE']._serialized_end=4111
  _globals['_MESSAGE_HEADERSENTRY']._serialized_start=4065
  _globals['_MESSAGE_HEADERSENTRY']._serialized_end=4111
  _globals['_PROPERTY']._serialized_start=4113
  _globals['_PROPERTY']._serialized_end=4152
  _globals['_TOPICFILTER']._serialized_start=4154
  _globals['_TOPICFILTER']._serialized_end=4194
  _globals['_SUBOPTS']._serialized_start=4196
  _globals['_SUBOPTS']._serialized_end=4270
  _globals['_REQUESTMETA']._serialized_start=4272
  _globals['_REQUESTMETA']._serialized_end=4356
  _globals['_HOOKPROVIDER']._serialized_start=4359
  _globals['_HOOKPROVIDER']._serialized_end=6350
# @@protoc_insertion_point(module_scope)
