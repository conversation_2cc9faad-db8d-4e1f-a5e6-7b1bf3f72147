# -*- coding:utf-8 -*-

from functools import partial, reduce
import time
import asyncio

from apps.base_utils import lemon_uuid
from baseutils.log import app_log
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.entity import Workflow as WorkflowModel, Page as PageModel, RelationshipBasic
from apps.ide_const import (
    AutoProcessRule, ComponentType, FieldType, HandleType, HandlerType, LemonDesignerErrorCode as LDEC,
    NodeSystemActionType, NodeType, ParallelEndRule, ReturnHandleType, ValueEditorType,
    Button, ButtonShowType)
from apps.ide_const import (
    Workflow, Node, NodeSystemActionRule, NodeSystemAction, NodeCustomAction, NodeTimeout, NodeLine,
    Variable, TimerType, Timer, Relationship
)
from apps.base_entity import UUID
from apps.services import CheckerService as BaseCheckerService, DocumentCheckerService
from apps.services.checker import checker


def find_form_model(container_model):
    form_models = list()
    for model_data in container_model.values():
        component_type = model_data.get("component_type")
        if component_type == ComponentType.FORM:
            model_uuid = model_data.get("model")
            if model_data.get("is_outer_container"):
                form_models.append(model_uuid)
    return form_models


def check_settings_page(self, _key, _type):
    element_uuid = self.element_uuid
    title = self.element.get("name", "")

    key = _key + _type
    attr_key = key.upper() + "_PAGE"

    if isinstance(self, WorkflowCheckerService):
        attr_class = Workflow.ATTR
        attr_key = key.upper() + "_PAGE"
        return_code_key = "WF" + _type.upper() + "_PAGE_NEED_ONE_FORM"
        page_error_code_key = "WF" + _type.upper() + "_PAGE_IS_DELETE"
    elif isinstance(self, (CopyNodeCheckerService, ManualNodeCheckerService)):
        attr_class = Node.ATTR
        attr_key = key.upper() + "_PAGE"
        return_code_key = "WF" + _type.upper() + "_PAGE_NEED_ONE_FORM"
        page_error_code_key = "NODE" + _type.upper() + "_PAGE_IS_DELETE"
    else:
        attr_class = Node.ATTR
        attr_key = key.upper() + "_COPY_PAGE"
        return_code_key = "WF" + _type.upper() + "_COPY_PAGE_NEED_ONE_FORM"
        page_error_code_key = "WF" + _type.upper() + "_PAGE_IS_DELETE"
    attr = getattr(attr_class, attr_key)

    # Workflow_name_dict = {
    #     "TOTAL_FROM_PAGE": "发起页面", "PC_FROM_PAGE": "PC 发起页面", "MOBILE_FROM_PAGE": "手机 发起页面",
    #     "PAD_FROM_PAGE": "PAD 发起页面", "TOTAL_TO_PAGE": "处理页面", "PC_TO_PAGE": "PC 处理页面",
    #     "MOBILE_TO_PAGE": "手机 处理页面", "PAD_TO_PAGE": "PAD 处理页面"
    # }

    page = self.settings.get(key, {}).get("page")
    # 每条发起/处理页面下加了 uuid 字段以区分文档引用（只有新的文档有）
    uuid = self.settings.get(key, {}).get("uuid")
    # 兼容老数据
    if uuid is None:
        uuid = element_uuid
    page_exist = page in self.app_page_uuid_set
    if page_exist:
        title_name = title  # + Workflow_name_dict.get(attr_key, "")
        self.update_page_reference(page, title_name, uuid, attr)

    if self.app_page_dict.get(page, dict()).get(PageModel.is_delete.name):
        return_code = getattr(LDEC, page_error_code_key)
        return_code.message = return_code.message.format(name=self.element_name)
        self._add_error_list(attr=attr, return_code=return_code)

    # Page表数据
    page_dict = self.app_page_dict.get(page, dict())
    # 最外层表单数量
    form_count = page_dict.get(PageModel.form_count.name, 0)
    if form_count != 1 and page_exist:
        # 检查最外层表单数量
        return_code = getattr(LDEC, return_code_key)
        self._add_error_list(attr=attr, return_code=return_code)

    container_model = page_dict.get(PageModel.container_model.name, dict())
    form_models = find_form_model(container_model)

    # 所有页面(4个) 需要保证发起/处理模型一致
    # 如果page不存在, 就不用检查是否一致
    self.settings_page_info.update({key: {"page_exist": page_exist, "form_models": form_models}})


class WorkflowCheckerMixin:

    @property
    def wf_checker(self):
        if self.parent is None:
            return self
        return self.parent.wf_checker

    @property
    def cur_wf_checker(self):
        if isinstance(self, WorkflowCheckerService):
            return self
        return self.parent.cur_wf_checker


class CheckerService(BaseCheckerService, WorkflowCheckerMixin):

    def _build_error_dict(self, attr: str, return_code, **kwargs):
        error_dict = super()._build_error_dict(attr, return_code, **kwargs)
        error_dict.update({
            "checked_version": self.wf_checker.checked_version,
            "checked_uuid": self.wf_checker.checked_uuid  # 使用最外层的
        })
        return error_dict

    def _check_model(self, model_uuid, return_code_name):
        check_result = False
        attr = Node.ATTR.MESSAGE
        if not model_uuid:
            return_code = LDEC.NODE_MESSAGE_MODEL_NOT_CHOOSE
            return_code.message = return_code.message.format(name=(return_code_name))
            self._add_error_list(attr, return_code)
        elif model_uuid not in self.app_model_dict:
            return_code = LDEC.NODE_MESSAGE_MODEL_NOT_CHOOSE
            return_code.message = return_code.message.format(name=(return_code_name))
            self._add_error_list(attr, return_code)
        else:
            check_result = True
            self.update_model_reference(model_uuid, "消息发送", self.element_uuid, attr)
        return check_result

    def _check_field_choose(
            self, field_dict, attr, return_code, need_choose=True):
        # 是否选择字段
        if not field_dict or not field_dict.get("field"):
            if need_choose:
                self._add_error_list(attr, return_code)
        else:
            return True

    def _check_field_exist(self, field_dict, attr, return_code):
        # 字段是否存在
        field_uuid = field_dict.get("field")
        field_info = self.app_field_dict.get(field_uuid)
        if not field_info:
            self._add_error_list(attr, return_code)
        else:
            self.update_field_reference(
                field_uuid, attr, self.element_uuid, attr)
            return True

    def _check_field_type(self, field_dict, allow_type, attr, return_code):
        field_uuid = field_dict.get("field")
        field_info = self.app_field_dict.get(field_uuid)
        if field_info.get("field_type") not in allow_type:
            self._add_error_list(attr, return_code)
        else:
            return True

    def _check_field_relation(self, field_dict, attr, return_code):
        relation = field_dict.get("path")
        if relation:
            if relation[0] not in self.app_relationship_uuid_set:
                self._add_error_list(attr, return_code)
            else:
                for r in self.relationship_list:
                    if relation[0] == r.get(RelationshipBasic.relationship_uuid.name):
                        if r.get(RelationshipBasic.relationship_type.name) != Relationship.TYPE.ONE_TO_ONE:
                            self._add_error_list(attr, return_code)

    def _check_field(self, field_dict, allow_type: list, return_code_name):
        # 检查字段是否选择, 检查字段类型, 检查字段是否仍存在
        attr = Node.ATTR.MESSAGE
        not_choose_return_code = LDEC.NODE_MESSAGE_FIELD_NOT_CHOOSE
        not_choose_return_code.message = not_choose_return_code.message.format(
            name=(return_code_name))
        not_exist_return_code = LDEC.NODE_MESSAGE_FIELD_NOT_EXIST
        not_exist_return_code.message = not_exist_return_code.message.format(
            name=(return_code_name))
        type_err_return_code = LDEC.NODE_MESSAGE_FIELD_TYPE_ERROR
        type_err_return_code.message = type_err_return_code.message.format(
            name=(return_code_name))
        not_relation_return_code = LDEC.NODE_MESSAGE_FIELD_NOT_RELATION
        not_relation_return_code.message = not_relation_return_code.message.format(
            name=(return_code_name))
        if self._check_field_choose(
                field_dict, attr, not_choose_return_code):
            if self._check_field_exist(
                    field_dict, attr, not_exist_return_code):
                if self._check_field_type(
                        field_dict, allow_type, attr, type_err_return_code):
                    self._check_field_relation(
                        field_dict, attr, not_relation_return_code)

    def _check_btn_list_icon(self, btn_list_data):
        for btn in btn_list_data:
            show_title_type = btn.get("show_title_type")
            icon = btn.get("selectedIcon", {}).get("icon")
            name = btn.get("name")
            if show_title_type in [ButtonShowType.text_icon, ButtonShowType.only_icon] and not icon:
                attr = Button.ICON.BUTTON_ICON
                return_code = LDEC.BUTTON_ICON_NOT_SET
                return_code.message = return_code.message.format(button_name=name)
                self._add_error_list(attr=attr, return_code=return_code)


class LineCheckerService(CheckerService):

    attr_class = NodeLine.ATTR
    uuid_error = LDEC.WF_LINE_UUID_ERROR
    uuid_unique_error = LDEC.WF_LINE_UUID_UNIQUE_ERROR
    name_error = LDEC.NODE_LINE_NAME_FAILED
    name_unique_error = LDEC.NODE_LINE_NAME_NOT_UNIQUE
    allow_chinese_name = True

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", {})
        self.action = self.settings.get("action")
        self.default = self.settings.get("default", False)
        self.to_node = self.settings.get("to_node")
        self.hide = self.element.get("hide", False)

    def check_action(self, action_uuid_set, reject_auto_to_end=None):
        if self.action:
            if self.action not in action_uuid_set:
                attr = self.attr_class.ACTION
                return_code = LDEC.NODE_LINE_ACTION_NOT_FOUND
                self._add_error_list(attr=attr, return_code=return_code)

            # if reject_auto_to_end:
            #     if not self.hide and self.action == NodeSystemActionType.REJECT:
            #         attr = self.attr_class.ACTION
            #         return_code = LDEC.NODE_LINE_NOT_ALLOW_REJECT
            #         self._add_error_list(attr=attr, return_code=return_code)

    def check_to_node(self, node_uuid_set):
        if self.to_node not in node_uuid_set:
            attr = self.attr_class.TO_NODE
            return_code = LDEC.NODE_LINE_TO_NODE_NOT_FOUND
            self._add_error_list(attr=attr, return_code=return_code)

    def check_default_line(self, default_lines: dict):
        if self.source_element.get("settings", {}).get("default"):
            default_lines["default_count"] += 1
        # skip_check用来控制遍历间仅一次进入该报错
        if default_lines["default_count"] > 1 and not default_lines.get("skip_check"):
            default_lines["skip_check"] = True
            attr = self.attr_class.DEFAULT
            return_code = LDEC.NODE_LINE_DEFAULT_ERROR
            self._add_error_list(attr=attr, return_code=return_code)

    def check_condition_name_unique(self, name_set):
        if self.settings.get("condition_name") in name_set:
            self._add_error_list(attr=Node.ATTR.LINE, return_code=LDEC.CONDITION_NAME_NOT_UNIQUE)
        else:
            name_set.add(self.settings.get("condition_name"))

    def check_value(self):
        attr = Node.ATTR.LINE
        value = self.settings.get("value", {})
        if value:
            self._check_value_editor(
                value, attr, self.parent.app_field_dict, self.parent.app_relationship_uuid_set)


class TimeoutCheckerService(CheckerService):

    attr_class = NodeTimeout.ATTR
    uuid_error = LDEC.WF_TIMEOUT_UUID_ERROR
    uuid_unique_error = LDEC.WF_TIMEOUT_UUID_UNIQUE_ERROR
    name_error = LDEC.NODE_TIMEOUT_NAME_FAILED
    name_unique_error = LDEC.NODE_TIMEOUT_NAME_NOT_UNIQUE
    allow_chinese_name = True

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.timeout_days = self.element.get("timeout_days")
        self.timeout_hours = self.element.get("timeout_hours")
        self.urging_days = self.element.get("urging_days")
        self.urging_hours = self.element.get("urging_hours")
        self.urging_limit = self.element.get("urging_limit")
        self.timeout_to_node = self.element.get("timeout_to_node")

    def check_type(self):
        if self.type not in NodeTimeout.TYPE.ALL:
            attr = self.attr_class.TYPE
            return_code = LDEC.NODE_TIMEOUT_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)

    def check_timeout(self):
        if self.timeout_days < 0:
            day_seconds = 0
            attr = self.attr_class.TIMEOUT_DAY
            return_code = LDEC.NODE_TIMEOUT_DAYS_LESS
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            day_seconds = self.timeout_days * 86400
        if self.timeout_hours < 0:
            hour_seconds = 0
            attr = self.attr_class.TIMEOUT_HOUR
            return_code = LDEC.NODE_TIMEOUT_HOURS_LESS
            self._add_error_list(attr=attr, return_code=return_code)
        elif self.timeout_hours >= 24:
            hour_seconds = 0
            attr = self.attr_class.TIMEOUT_HOUR
            return_code = LDEC.NODE_TIMEOUT_HOURS_LARGE
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            hour_seconds = self.timeout_hours * 3600
        total_seconds = day_seconds + hour_seconds
        if total_seconds < 3600:
            attr = self.attr_class.TIMEOUT
            return_code = LDEC.NODE_TIMEOUT_TIME_ERROR
            self._add_error_list(attr=attr, return_code=return_code)

    def check_urging(self):
        if self.type == NodeTimeout.TYPE.MESSAGE:
            if self.urging_days < 0:
                day_seconds = 0
                attr = self.attr_class.URGING_DAY
                return_code = LDEC.NODE_URGING_DAYS_LESS
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                day_seconds = self.urging_days * 86400
            if self.urging_hours < 0:
                hour_seconds = 0
                attr = self.attr_class.URGING_HOUR
                return_code = LDEC.NODE_URGING_HOURS_LESS
                self._add_error_list(attr=attr, return_code=return_code)
            elif self.urging_hours >= 24:
                hour_seconds = 0
                attr = self.attr_class.URGING_HOUR
                return_code = LDEC.NODE_URGING_HOURS_LARGE
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                hour_seconds = self.urging_hours * 3600
            total_seconds = day_seconds + hour_seconds
            if total_seconds < 3600:
                attr = self.attr_class.URGING
                return_code = LDEC.NODE_URGING_TIME_ERROR
                self._add_error_list(attr=attr, return_code=return_code)
            if self.urging_limit < 1:
                attr = self.attr_class.URGING_LIMIT
                return_code = LDEC.NODE_URGING_LIMIT_ERROR
                self._add_error_list(attr=attr, return_code=return_code)

    def check_to_node(self, node_uuid_set):
        if self.type == NodeTimeout.TYPE.TO_NODE:
            if self.timeout_to_node not in node_uuid_set:
                attr = self.attr_class.TIMEOUT_TO_NODE
                return_code = LDEC.NODE_TIMEOUT_TO_NODE_NOT_FOUND
                self._add_error_list(attr=attr, return_code=return_code)


class CustomActionCheckerService(CheckerService):

    attr_class = NodeCustomAction.ATTR
    uuid_error = LDEC.WF_TIMEOUT_UUID_ERROR
    uuid_unique_error = LDEC.WF_TIMEOUT_UUID_UNIQUE_ERROR
    name_error = LDEC.NODE_TIMEOUT_NAME_FAILED
    name_unique_error = LDEC.NODE_TIMEOUT_NAME_NOT_UNIQUE
    allow_chinese_name = True

    def initialize(self):
        super().initialize()
        self.transition_rule_type = self.element.get("transition_rule_type", 0)
        self.transition_rule_value = self.element.get("transition_rule_value", 1)

    def check_transition_rule(self):
        if self.transition_rule_type not in NodeCustomAction.RULE_TYPE.ALL:
            attr = self.attr_class.RULE_TYPE
            return_code = LDEC.NODE_ACTION_RULE_TYPE_NOT_SUPPORT
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr=attr, return_code=return_code)
        if self.transition_rule_type == NodeCustomAction.RULE_TYPE.COUNT:
            if self.transition_rule_value < 1:
                attr = self.attr_class.RULE_VALUE
                return_code = LDEC.NODE_ACTION_RULE_VALUE_ERROR
                return_code.message = return_code.message.format(name=self.element_name)
                self._add_error_list(attr=attr, return_code=return_code)
        elif self.transition_rule_type == NodeCustomAction.RULE_TYPE.RATE:
            if self.transition_rule_value < 0 or self.transition_rule_value > 100:
                attr = self.attr_class.RULE_VALUE
                return_code = LDEC.NODE_ACTION_RULE_VALUE_ERROR
                return_code.message = return_code.message.format(name=self.element_name)

    def check_action(self):
        # 子类实现
        ...


class ReturnActionCheckerService(CustomActionCheckerService):

    attr_class = NodeSystemAction.ATTR
    uuid_error = LDEC.WF_TIMEOUT_UUID_ERROR
    uuid_unique_error = LDEC.WF_TIMEOUT_UUID_UNIQUE_ERROR
    name_error = LDEC.NODE_TIMEOUT_NAME_FAILED
    name_unique_error = LDEC.NODE_TIMEOUT_NAME_NOT_UNIQUE
    allow_chinese_name = True

    def check_action(self):
        if self.element.get("return_handle_type") == ReturnHandleType.DESIGN:
            if not self.element.get("to_node"):
                attr = Node.ATTR.ACTION
                return_code = LDEC.RETURN_TO_NODE_NOT_SET
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                nodes_dict = {n["uuid"]: n for n in self.cur_wf_checker.nodes}
                if self.element.get("to_node") not in nodes_dict:
                    data = {
                        "element_data": {
                            "uuid": self.parent.element_uuid,
                            "name": self.parent.element_name
                        }
                    }
                    attr = Node.ATTR.ACTION
                    return_code = LDEC.RETURN_TO_NODE_NOT_EXIST
                    self._add_error_list(attr=attr, return_code=return_code, **data)


class VariableCheckerService(CheckerService):

    attr_class = Variable.ATTR
    uuid_error = LDEC.VARIABLE_UUID_ERROR
    uuid_unique_error = LDEC.VARIABLE_UUID_UNIQUE_ERROR
    name_error = LDEC.VARIABLE_NAME_FAILED
    name_unique_error = LDEC.WF_VARIABLE_NAME_NOT_UNIQUE
    allow_chinese_name = True

    def initialize(self):
        super().initialize()


class BaseNodeCheckerService(CheckerService):

    attr_class = Node.ATTR
    uuid_error = LDEC.NODE_UUID_ERROR
    uuid_unique_error = LDEC.NODE_UUID_UNIQUE_ERROR
    name_error = LDEC.NODE_NAME_FAILED
    name_unique_error = LDEC.NODE_NAME_NOT_UNIQUE
    allow_chinese_name = True

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str,
        element: dict, wf_uuid: str, app_wf_uuid_set: set, app_wf_dict: dict,
        app_func_uuid_dict: set, app_page_uuid_set: set,
        app_user_role_uuid_set: set, app_page_dict: dict, modules_role,
        app_model_dict, app_field_dict, app_relationship_uuid_set, cur_node_direction,
            *args, **kwargs):
        self.wf_uuid = wf_uuid
        self.app_wf_uuid_set = app_wf_uuid_set
        self.app_wf_dict = app_wf_dict
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_uuid_set = app_page_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.app_page_dict = app_page_dict
        self.settings_page_info = dict()
        self.check_settings_page = partial(check_settings_page, self)
        self.modules_role = modules_role
        self.app_model_dict = app_model_dict
        self.app_field_dict = app_field_dict
        self.app_relationship_uuid_set = app_relationship_uuid_set
        self.cur_node_direction = cur_node_direction
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, element, *args, **kwargs)

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.lines = self.element.get("lines", list())
        self.action_uuid_set = set()
        self.message_uuid_set = set()
        self.message_name_set = set()
        self.line_uuid_set = set()
        self.line_name_set = set()

    # 检查 节点类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_node_type(self):
        if self.type not in Node.TYPE.ALL:
            attr = Node.ATTR.TYPE
            return_code = LDEC.STATE_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)

    def check_messages(self):
        messages = self.element.get("messages", dict())
        if not isinstance(messages, dict) or not messages.get("open_messages"):
            return
        self.check_message_content(messages.get("messages_info", dict()))
        self.check_wecom(messages.get("wecom_info", dict()))
        self.check_timeout(messages.get("timeout_info", dict()))

    def check_lines(self, node_uuid_set, manual_node=False, reject_auto_to_end=None):
        lines = self.element.get("lines", list())
        default_lines = {"default_count": 0}
        condition_name_set = set()

        for line in lines:
            line_checker_service = LineCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, line, parent=self)
            try:
                line_checker_service.check_uuid()
                line_checker_service.check_uuid_unique(self.line_uuid_set)
                line_checker_service.check_name()
                line_checker_service.check_name_unique(self.line_name_set)
                line_checker_service.check_condition_name_unique(condition_name_set)
                line_checker_service.check_value()

                if manual_node:
                    line_checker_service.check_action(
                        self.action_uuid_set, reject_auto_to_end=reject_auto_to_end)
                line_checker_service.check_to_node(node_uuid_set)
                line_checker_service.check_default_line(default_lines)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(line_checker_service)
                raise e
            else:
                self.update_any_list(line_checker_service)

    def check_node(self, node_uuid_set, variable_uuid_set):
        pass

    def _check_handlers(self, handlers):
        module_role = self.modules_role.get(self.module_uuid, list())
        module_role_uuids = {r["role_uuid"] for r in module_role}
        creator_only = True
        if handlers:
            for handler in handlers:
                attr = Node.ATTR.HANDLER
                handler_type = handler.get("type")
                value = handler.get("value", dict())
                if handler_type not in HandlerType.ALL:
                    return_code = LDEC.NODE_HANDLER_TYPE_NOT_SUPPORT
                    self._add_error_list(attr=attr, return_code=return_code)
                if handler_type not in [HandlerType.CREATOR, HandlerType.MANAGER]:
                    creator_only = False
                if handler_type == HandlerType.ROLE:
                    role_uuid = handler.get("role_uuid")
                    if role_uuid not in module_role_uuids:
                        handler["role_name"] = "**角色不存在**"
                        return_code = LDEC.NODE_HANDLER_ROLE_NOT_FOUND
                        self._add_error_list(attr=attr, return_code=return_code)
                    elif role_uuid not in self.module_roles_user_related:
                        return_code = LDEC.NODE_HANDLER_ROLE_NOT_SELECTED
                        return_code.message = return_code.message.format(name=handler.get("role_name"))
                        self._add_error_list(attr=attr, return_code=return_code)
                elif handler_type == HandlerType.MANAGER:
                    manager_level = int(handler.get("value", 0))
                    if manager_level < 0:
                        return_code = LDEC.NODE_HANDLER_MANAGER_ERROR
                        self._add_error_list(attr=attr, return_code=return_code)
                elif handler_type == HandlerType.VALUE:
                    if value:
                        if value.get("type") in [ValueEditorType.STRING, ValueEditorType.VARIABLE]:
                            if not value.get("value") and not value.get("variable_uuid"):
                                self._add_error_list(
                                    attr, return_code=LDEC.NODE_HANDLER_EXPR_NOT_EXIST,
                                    element_data=self.element)
                        else:
                            self._check_value_editor(
                                    value, attr, self.app_field_dict, self.app_relationship_uuid_set)
                    else:
                        self._add_error_list(
                            attr, return_code=LDEC.NODE_HANDLER_EXPR_NOT_EXIST,
                            element_data=self.element)
                elif handler_type == HandlerType.EXTRA_ASSIGN:
                    self.__check_direction_node()
                    limit_roles = handler.get("limit_roles", list())
                    if handler.get("role_limit"):  # 解决角色被删除的情况
                        if not limit_roles:
                            attr = Node.ATTR.HANDLER
                            return_code = LDEC.NODE_HANDLER_NO_LIMIT_ROLES
                            self._add_error_list(attr=attr, return_code=return_code)
            handle_type = self.settings.get("handle_type")
            if handle_type not in HandleType.ALL:
                attr = Node.ATTR.HANDLER
                return_code = LDEC.NODE_HANDLE_TYPE_NOT_SUPPORT
                self._add_error_list(attr=attr, return_code=return_code)
            if creator_only and self.wf_checker.need_schedule:
                attr = Node.ATTR.HANDLER
                return_code = LDEC.TIMER_WORKFLOW_HANDLER_CREATOR_ONLY
                self._add_error_list(attr=attr, return_code=return_code)
        else:
            attr = Node.ATTR.HANDLER
            return_code = LDEC.NODE_HANDLER_NOT_EXISTS
            self._add_error_list(attr=attr, return_code=return_code)

    def __check_direction_node(self):
        # 上一节点是开始节点 / 没有自动处理的人工节点 不报错
        for node in self.cur_node_direction:
            node_type = node.get("type")
            settings = node.get("settings", dict())
            auto_process = settings.get("auto_process")
            if node_type in [NodeType.START, NodeType.COUNTERSIGN] or (
                    node_type == NodeType.MANUAL and auto_process not in [
                        AutoProcessRule.PROCESSED, AutoProcessRule.PROCESSED_LAST]):
                ...
            else:
                attr = Node.ATTR.HANDLER
                return_code = LDEC.NODE_RUNTIME_HANDLER_CONFLICT_LAST_NODE
                return_code.message = return_code.message.format(name=node.get("name"))
                self._add_error_list(attr=attr, return_code=return_code)

    def _check_to_page(self):
        key = ["total", "pc", "mobile", "pad"]
        _type = ["_to"]

        for t in _type:
            for k in key:
                self.check_settings_page(k, t)
        app_log.info(f"node_settings_page_info: {self.settings_page_info}")

        if self.type == NodeType.COPY:
            attr = Node.ATTR.TO_COPY_PAGE
            return_code = LDEC.WF_TO_COPY_PAGE_FORM_MODEL_DIFF
        else:
            attr = Node.ATTR.TO_PAGE
            return_code = LDEC.WF_TO_PAGE_FORM_MODEL_DIFF

        for t in _type:
            page_exists = list()
            page_models = list()
            for k in key:
                page_exist = self.settings_page_info.get(k+t).get("page_exist")
                page_exists.append(page_exist)

                page_model = self.settings_page_info.get(k+t).get("form_models")
                page_models.append(page_model)

            device_models = [models for models in page_models if models]
            if device_models:
                device_models_the_same = reduce(lambda x, y: x if x == y else False, device_models)
                if not device_models_the_same:
                    self._add_error_list(attr=attr, return_code=return_code)
                elif self.wf_to_page_uuid and device_models_the_same != self.wf_to_page_uuid:
                    self._add_error_list(attr=attr, return_code=LDEC.NODE_TO_PAGE_FORM_MODEL_DIFF)
                elif device_models_the_same[0] in UUID.TABLE_NAME_ALL:
                    return_code = LDEC.NODE_TO_PAGE_SYS_MODEL
                    self._add_error_list(attr=attr, return_code=return_code)
            page_exists.clear()
            page_models.clear()
        self.settings_page_info.clear()

    def check_actions_events(self):
        attr = Node.ATTR.EVENT
        custom_actions = self.element.get("actions", dict()).get("custom", list())
        for action in custom_actions:
            action_events = action.get("events", list())
            action_uuid = action.get("uuid")
            action_name = action.get("name")
            if action_uuid not in NodeSystemActionType.ALL_EVENT:
                action_name = "自定义动作"
            for func in action_events:
                func_uuid = func.get("func_uuid")
                self.check_event_func(attr, func_uuid, action_name=action_name)

    def check_events(self):
        attr = Node.ATTR.EVENT
        events = self.element.get("events", dict())
        in_events = events.get("in", list())
        out_events = events.get("out", list())
        for f in in_events + out_events:
            func_uuid = f.get("func_uuid")
            self.check_event_func(attr, func_uuid)

    def check_event_func(self, attr, func_uuid, must_exist=True, action_name=None):
        position = action_name if action_name else "事件"
        if not func_uuid:
            if must_exist:
                self._add_error_list(attr=attr, return_code=LDEC.NODE_EVENT_NO_FUNC)
        elif func_uuid not in self.app_func_uuid_dict:
            return_code = LDEC.FUNC_IS_DELETED
            return_code.position = return_code.position.format(position=position)
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            self.check_event_args(attr, self.app_func_uuid_dict.get(func_uuid, dict()), position)
            self.update_cloud_func_reference(
                func_uuid, title=self.element_name, element_uuid=self.element_uuid, attr=attr)

    def check_event_args(self, attr, func_dict, position=None):
        arg_list = func_dict.get("arg_list", list())
        if len(arg_list) != 2:
            return_code = LDEC.NODE_EVENT_ARG_NUMBER_ERROR
            if position:
                return_code.position = return_code.position.format(position=position)
            self._add_error_list(attr=attr, return_code=return_code)

    def check_message_content(self, messages_info):
        attr = Node.ATTR.MESSAGE
        if not messages_info:
            return_code = LDEC.NODE_MESSAGE_CONTENT_NOT_EXIST
            self._add_error_list(attr=attr, return_code=return_code)
            return
        _type = messages_info.get("type")
        not_exist = False
        if _type == ValueEditorType.EXPR:
            if not messages_info.get("expr"):
                not_exist = True
        elif _type == ValueEditorType.STRING:
            if not messages_info.get("value"):
                not_exist = True
        elif _type == ValueEditorType.VARIABLE:
            if not messages_info.get("variable_uuid"):
                not_exist = True

        if not_exist:
            return_code = LDEC.NODE_MESSAGE_CONTENT_NOT_EXIST
            self._add_error_list(attr=attr, return_code=return_code)

    def check_wecom(self, wecom_info):
        if wecom_info.get("wecom_send"):
            self._check_field(
                wecom_info.get("account"), [FieldType.STRING], "接收人企微id")

    def check_timeout(self, timeout_info):
        if timeout_info.get("time_out_send"):
            timeout_model = timeout_info.get("time_out_model")
            if self._check_model(timeout_model, "超时未处理提醒"):
                need_check_field = {
                    "重复间隔": {"field_dict": timeout_info.get("period_interval"), "allow_type": [FieldType.INTEGER]},
                }
                for name, check_info in need_check_field.items():
                    field_dict = check_info.get("field_dict", dict())
                    allow_type = check_info.get("allow_type")
                    self._check_field(field_dict, allow_type, name)

    def check_is_only_auto_workflow(self):
        element = self.wf_checker.workflow
        element["is_only_auto"] = False


class AutoNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", dict())

    def check_settings(self, variable_uuid_set):
        module_roles = self.settings.get("module_role", list())

        # if not module_roles:
        #     attr = Node.ATTR.ROLE
        #     return_code = LDEC.AUTO_NODE_ROLE_NOT_SELECT
        #     self._add_error_list(attr=attr, return_code=return_code)
        if module_roles:
            attr = Node.ATTR.ROLE
            roles = []
            for role in self.modules_role.get(self.module_uuid, []):
                roles.append(role.get('role_uuid'))
            for module_role in module_roles:
                self.check_module_role(module_role)
            for module_role in module_roles:
                if module_role not in roles:
                    return_code = LDEC.AUTO_NODE_ROLE_NOT_FOUND
                    self._add_error_list(attr=attr, return_code=return_code)
                    break

    def check_events(self):
        auto_event = self.element.get("settings", dict()).get("func", dict())
        func_uuid = auto_event.get("uuid")
        self.check_event_func(Node.ATTR.EVENT, func_uuid)
        withdraw_event = self.element.get("settings", dict()).get("withdraw_func", dict())
        withdraw_func_uuid = withdraw_event.get("uuid")
        self.check_event_func(Node.ATTR.EVENT, withdraw_func_uuid, must_exist=False)

    def check_module_role(self, module_role_uuid):
        attr = Node.ATTR.ROLE
        return_code = LDEC.AUTO_NODE_ROLE_NOT_IN_APP_ROLE
        if module_role_uuid not in self.module_roles_user_related:
            self._add_error_list(attr=attr, return_code=return_code)

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.check_settings(variable_uuid_set)
        self.check_messages()
        self.check_lines(node_uuid_set)
        self.check_events()


class ManualNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", dict())
        self.actions = self.element.get("actions", dict())
        self.timeouts = self.element.get("timeouts", list())
        self.timeout_uuid_set = set()
        self.timeout_name_set = set()
        self.custom_action_uuid_set = set()
        self.custom_action_name_set = set()
        self.reject_auto_to_end = False

    def check_settings(self):
        handlers = self.settings.get("handlers", [])
        self._check_handlers(handlers)

        self._check_to_page()

        self._check_auto_process(self.settings)

    def _check_auto_process(self, settings: dict):
        auto_process = settings.get("auto_process")
        if auto_process not in [AutoProcessRule.PROCESSED, AutoProcessRule.PROCESSED_LAST, AutoProcessRule.CLOSED]:
            attr = Node.ATTR.ACTION
            return_code = LDEC.NODE_AUTO_PROCESS_TYPE_INVALID
            self._add_error_list(attr=attr, return_code=return_code)
        # if auto_process in [AutoProcessRule.PROCESSED, AutoProcessRule.PROCESSED_LAST]:
        #     approvalflow_opinion = settings.get("approvalflow_opinion")
        #     if approvalflow_opinion:
        #         attr = Node.ATTR.ACTION
        #         return_code = LDEC.NODE_AUTO_PROCESS_CONFLICT_COMMENT
        #         self._add_error_list(attr=attr, return_code=return_code)

    def check_actions(self):
        system_actions = self.actions.get("system", {})
        custom_actions = self.actions.get("custom", {})
        if not any([system_actions, custom_actions]):
            attr = Node.ATTR.ACTION
            return_code = LDEC.NODE_ACTIONS_IS_NULL
            self._add_error_list(attr=attr, return_code=return_code)
        default_rule = self.actions.get("default_rule")
        if system_actions:
            if default_rule not in NodeSystemActionRule.ALL:
                attr = Node.ATTR.ACTION
                return_code = LDEC.NODE_ACTION_DEFAULT_RULE_NOT_SUPPORT
                self._add_error_list(attr=attr, return_code=return_code)
        system_accept_uuid = system_actions.get("accept_uuid")
        system_reject_uuid = system_actions.get("reject_uuid")
        if system_accept_uuid is not None:
            self.action_uuid_set.add(system_accept_uuid)
            if system_accept_uuid != NodeSystemAction.TYPE.ACCEPT:
                attr = Node.ATTR.ACTION
                return_code = LDEC.NODE_ACTION_ACCEPT_UUID_ERROR
                self._add_error_list(attr=attr, return_code=return_code)
        if system_reject_uuid is not None:
            self.action_uuid_set.add(system_reject_uuid)
            if system_reject_uuid != NodeSystemAction.TYPE.REJECT:
                attr = Node.ATTR.ACTION
                return_code = LDEC.NODE_ACTION_REJECT_UUID_ERROR
                self._add_error_list(attr=attr, return_code=return_code)
        for action in custom_actions:
            service_class = CustomActionCheckerService
            if action["uuid"] == NodeSystemActionType.RETURN:
                if self.element.get("settings", dict()).get("accept_return", False):
                    service_class = ReturnActionCheckerService
            custom_action_checker_service = service_class(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, action, parent=self)
            action_uuid = custom_action_checker_service.element_uuid
            self.action_uuid_set.add(action_uuid)
            if action_uuid == NodeSystemActionType.REJECT:
                if action.get("to_end"):
                    self.reject_auto_to_end = True
            try:
                custom_action_checker_service.check_uuid()
                custom_action_checker_service.check_uuid_unique(self.custom_action_uuid_set)
                custom_action_checker_service.check_name()
                custom_action_checker_service.check_name_unique(self.custom_action_name_set)
                custom_action_checker_service.check_transition_rule()
                custom_action_checker_service.check_action()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(custom_action_checker_service)
                raise e
            else:
                self.update_any_list(custom_action_checker_service)

    def check_timeouts(self, node_uuid_set):
        for timeout in self.timeouts:
            timeout_checker_service = TimeoutCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, timeout)
            try:
                timeout_checker_service.check_uuid()
                timeout_checker_service.check_uuid_unique(self.timeout_uuid_set)
                timeout_checker_service.check_name()
                timeout_checker_service.check_name_unique(self.timeout_name_set)
                timeout_checker_service.check_type()
                timeout_checker_service.check_timeout()
                timeout_checker_service.check_urging()
                timeout_checker_service.check_to_node(node_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(timeout_checker_service)
                raise e
            else:
                self.update_any_list(timeout_checker_service)

    def check_return(self, node):
        attr = Node.ATTR.TIMER
        node_name = node["name"]
        node_uuid = node["uuid"]
        for node in self.cur_wf_checker.nodes:
            if node["type"] == Node.TYPE.START:
                startnode_uuid = node.get("uuid")
                need_schedule = node.get("need_schedule")
        if need_schedule and not self.cur_wf_checker.branch_wf:
            nodes_dict = {n["uuid"]: n for n in self.cur_wf_checker.nodes}
            return_node = nodes_dict.get(node_uuid)
            actions = return_node.get("actions").get("custom")
            for action in actions:
                if action.get("uuid") == NodeSystemActionType.RETURN:
                    if action.get("return_handle_type") == ReturnHandleType.DESIGN:
                        return_uuid = action.get("to_node")
                        if return_uuid == startnode_uuid:
                            return_code = LDEC.TIMER_RETURN_NODE_START
                            return_code.message = return_code.message.format(node_name=node_name)
                            self._add_error_list(attr=attr, return_code=return_code)

    def check_btn_list_icon(self):
        if not self.element.get("is_extend", True):
            self._check_btn_list_icon(self.element.get("btn_list_data", []))

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.check_settings()
        self.check_messages()
        self.check_actions()
        # self.check_timeouts(node_uuid_set)
        self.check_lines(node_uuid_set, manual_node=True, reject_auto_to_end=self.reject_auto_to_end)
        self.check_events()
        self.check_actions_events()
        self.check_return(self.element)
        self.check_is_only_auto_workflow()
        self.check_btn_list_icon()


class ParallelNodeCheckerService(BaseNodeCheckerService):

    def check_parallel(self, *args, **kwargs):
        self.app_const_uuid_set = set()
        self.app_enum_uuid_set = set()
        for branch in self.element.get("branches", list()):
            nodes_direction = self.gen_branch_nodes_direction(branch)
            branch_check_service = WorkflowCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, branch, self.app_wf_uuid_set, self.app_wf_dict, self.app_model_dict,
                self.app_field_dict, self.app_func_uuid_dict, self.app_page_uuid_set,
                self.app_const_uuid_set, self.app_enum_uuid_set, self.app_user_role_uuid_set,
                self.app_page_dict, self.modules_role, self.app_relationship_uuid_set,
                branch_wf=True, nodes_direction=nodes_direction, parent=self,
                relationship_list=self.relationship_list, wf_to_page_uuid=self.wf_to_page_uuid,
                module_roles_user_related=self.module_roles_user_related)

            branch_check_service.name_error = LDEC.PARALLEL_NAME_FAILED

            branch_check_service.document_other_info = self.document_other_info
            branch_check_service.check_uuid()
            branch_check_service.check_uuid_unique(self.app_wf_uuid_set)
            branch_check_service.check_name()
            branch_check_service.check_nodes()
            self.update_any_list(branch_check_service)

    def gen_branch_nodes_direction(self, branch: dict):
        # 把并行节点的前置节点 作为分支中开始节点后的的那个节点的前置节点
        nodes_direction = dict()
        nodes = branch.get("nodes", list())
        start_node = filter(lambda n: n["type"] == NodeType.START, nodes).__next__()
        lines = start_node.get("lines", dict())
        for line in lines:
            settings = line.get("settings", dict())
            to_node_uuid = settings.get("to_node")
            nodes_direction.update({to_node_uuid: self.cur_node_direction})
        return nodes_direction

    def check_settings(self):
        branches_number = len(self.element.get("branches", list()))
        attr = Node.ATTR.BRANCH
        if branches_number == 0:
            return_code = LDEC.PARALLEL_NODE_NO_BRANCH
            self._add_error_list(attr=attr, return_code=return_code)
        end_rule: dict = self.element.get("settings", dict()).get("end_rule", dict())
        if end_rule.get("type") == ParallelEndRule.FINISH_ALL:
            pass
        elif end_rule.get("type") == ParallelEndRule.FINISH_PART:
            amount = end_rule.get("amount", 0)
            if amount == 0:
                return_code = LDEC.PARALLEL_BRANCH_FINISH_AMOUNT_ZERO
                self._add_error_list(attr=attr, return_code=return_code)
            elif amount > branches_number:
                return_code = LDEC.PARALLEL_BRANCH_FINISH_AMOUNT_TOO_MUCH
                self._add_error_list(attr=attr, return_code=return_code)
        else:
            return_code = LDEC.PARALLEL_BRANCH_END_RULE_INVALID
            self._add_error_list(attr=attr, return_code=return_code)

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.check_parallel()
        self.check_settings()


class CopyNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", dict())

    def check_handlers(self):
        handlers = self.settings.get("handlers", [])
        self._check_handlers(handlers)

    def check_to_page(self):
        self._check_to_page()

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.check_handlers()
        self.check_to_page()
        self.check_lines(node_uuid_set)
        self.check_events()
        self.check_messages()
        self.check_is_only_auto_workflow()


class CountersignNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", dict())
        self.actions = self.element.get("actions", dict())
        self.custom_action_uuid_set = set()
        self.custom_action_name_set = set()

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.check_lines(node_uuid_set)
        self.check_is_only_auto_workflow()


class StartNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()

    def check_node(self, node_uuid_set, variable_uuid_set):
        # 判断开始节点，有且只有一条线
        if len(self.lines) != 1:
            attr = Node.ATTR.LINE
            return_code = LDEC.START_NODE_LINE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)

        if self.wf_checker.need_schedule and not self.parent.branch_wf:
            self.check_schedule()
            nodes_dict = {n["uuid"]: n for n in self.wf_checker.nodes}
            route_nodes = set()
            self.find_timer_next_node(self.element, nodes_dict, route_nodes)
        self.check_actions_events()

    def check_schedule(self):
        attr = Node.ATTR.TIMER
        timer_list = self.element.get("timer_list", list())
        self.check_tenant_start_field()
        for t in timer_list:
            if self.wf_checker.is_copy:
                t["uuid"] = lemon_uuid()
            self.check_start_timestamp(attr, t)
            if t["type"] == TimerType.PERIOD:
                self.check_period_timer(attr, t)

    def check_tenant_start_field(self):
        tenant_field_info = self.element.get("tenant_start_field", dict())
        attr = Node.ATTR.TIMER
        if self._check_field_choose(
                tenant_field_info, attr, LDEC.TENANT_START_FIELD_NOT_CHOOSE,
                need_choose=False):
            if self._check_field_exist(
                    tenant_field_info, attr, LDEC.TENANT_START_FIELD_NOT_EXIST):
                self._check_field_type(
                    tenant_field_info, [FieldType.BOOLEAN], attr,
                    LDEC.TENANT_START_FIELD_TYPE_ERROR)

    def check_start_timestamp(self, attr, timer):
        start_timestamp = timer.get("start_timestamp")
        if not start_timestamp:
            return_code = LDEC.TIMER_START_TIMESTAMP_NOT_EXIST
            self._add_error_list(attr=attr, return_code=return_code)

    def check_period_timer(self, attr, timer):
        period_type = timer.get("period_type")
        period_value = timer.get("period_value")
        if period_type not in Timer.PERIOD_TYPE.ALL:
            return_code = LDEC.TIMER_PERIOD_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
        elif period_type == Timer.PERIOD_TYPE.DAY:
            ...
        elif period_type == Timer.PERIOD_TYPE.WEEK:
            if not isinstance(period_value, list) or not period_value or \
                not all(map(
                    lambda x:  isinstance(x, int) and 0 <= x <= 6, period_value)):
                return_code = LDEC.TIMER_PERIOD_VALUE_WEEK_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
        elif period_type == Timer.PERIOD_TYPE.MONTH:
            if not isinstance(period_value, list) or not period_value or \
                not all(map(
                    lambda x:  isinstance(x, int) and 1 <= x <= 31, period_value)):
                return_code = LDEC.TIMER_PERIOD_VALUE_MONTH_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)

    def find_timer_next_node(self, node, nodes_dict, route_nodes):
        attr = Node.ATTR.TIMER
        node_type = node["type"]
        node_name = node["name"]
        node_uuid = node["uuid"]
        if node_uuid in route_nodes:
            return
        else:
            route_nodes.add(node_uuid)
        settings = node.get("settings", dict())
        handlers = settings.get("handlers", list())
        runtime_assign = False  # 运行时指定
        for h in handlers:
            if h["type"] == HandlerType.EXTRA_ASSIGN:
                runtime_assign = True
                break
        if node_type == NodeType.END:
            return
        elif node_type == NodeType.AUTO or \
                (node_type == NodeType.COPY and not runtime_assign) or \
                node_type == NodeType.START:
            lines = node.get("lines", list())
            to_nodes = set()
            for line in lines:
                settings = line.get("settings", dict())
                to_node = settings.get("to_node")
                to_nodes.add(to_node)
            for node_uuid in to_nodes:
                self.find_timer_next_node(nodes_dict.get(node_uuid), nodes_dict, route_nodes)
        elif node_type == NodeType.MANUAL and not runtime_assign:
            return
        elif node_type in [NodeType.COPY, NodeType.MANUAL] and runtime_assign:
            return_code = LDEC.TIMER_NEXT_NODE_INVALID
            node_name1 = "<" + node_name + ">" if node_type == NodeType.COPY else ""
            node_name2 = "<" + node_name + ">" if node_type == NodeType.MANUAL else ""
            return_code.message = return_code.message.format(node_name1=node_name1, node_name2=node_name2)
            self._add_error_list(attr=attr, return_code=return_code)
        elif node_type == NodeType.PARALLEL:
            return_code = LDEC.TIMER_NEXT_NODE_PARALLEL
            return_code.message = return_code.message.format(node_name=node_name)
            self._add_error_list(attr=attr, return_code=return_code)


class EndNodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()

    def check_node(self, node_uuid_set, variable_uuid_set):
        # 判断结束节点，应没有线
        if len(self.lines) != 0:
            attr = Node.ATTR.LINE
            return_code = LDEC.END_NODE_LINE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)


class SubflowNodeCheckerService(BaseNodeCheckerService):

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.check_is_only_auto_workflow()
        self.check_subflow()

    def check_subflow(self):
        self.check_subflow_chosen()
        self.check_subflow_pk()

    def check_subflow_chosen(self):
        attr = Node.ATTR.SUBFLOW
        wf_uuid = self.element.get("subflow", dict()).get("wf_uuid")
        if not wf_uuid:
            return_code = LDEC.NODE_SUBFLOW_NOT_CHOOSE
            self._add_error_list(attr, return_code)
        elif wf_uuid not in self.app_wf_dict:
            return_code = LDEC.NODE_SUBFLOW_NOT_EXIST
            self._add_error_list(attr, return_code)
        else:
            self.update_work_flow_reference(wf_uuid, "子流程", self.element_uuid, attr)

    def check_subflow_pk(self):
        attr = Node.ATTR.SUBFLOW
        submit_pk = self.element.get("subflow", dict()).get("submit_pk")
        self._check_value_editor(
                submit_pk, attr, self.app_field_dict, self.app_relationship_uuid_set)


class NodeCheckerService(BaseNodeCheckerService):

    def initialize(self):
        super().initialize()
        self.node_checker_class_dict = {
            Node.TYPE.AUTO: AutoNodeCheckerService,
            Node.TYPE.MANUAL: ManualNodeCheckerService,
            Node.TYPE.START: StartNodeCheckerService,
            Node.TYPE.END: EndNodeCheckerService,
            Node.TYPE.PARALLEL: ParallelNodeCheckerService,
            Node.TYPE.COPY: CopyNodeCheckerService,
            Node.TYPE.SUBFLOW: SubflowNodeCheckerService,
            Node.TYPE.COUNTERSIGN: CountersignNodeCheckerService,
        }
        node_checker_class = self.node_checker_class_dict.get(self.type, BaseNodeCheckerService)
        self.node_checker_service = node_checker_class(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, self.element, self.wf_uuid, self.app_wf_uuid_set, self.app_wf_dict,
                self.app_func_uuid_dict, self.app_page_uuid_set, self.app_user_role_uuid_set,
                self.app_page_dict, self.modules_role, self.app_model_dict,
                self.app_field_dict, self.app_relationship_uuid_set, self.cur_node_direction,
                parent=self.parent, relationship_list=self.relationship_list, wf_to_page_uuid=self.wf_to_page_uuid,
                module_roles_user_related=self.module_roles_user_related)

    def check_node(self, node_uuid_set, variable_uuid_set):
        self.node_checker_service.document_other_info = self.document_other_info
        self.node_checker_service.check_node(node_uuid_set, variable_uuid_set)
        self.update_any_list(self.node_checker_service)


class WorkflowCheckerService(CheckerService):

    attr_class = Workflow.ATTR
    uuid_error = LDEC.WF_UUID_ERROR
    uuid_unique_error = LDEC.WF_UUID_UNIQUE_ERROR
    name_error = LDEC.WF_NAME_FAILED
    name_unique_error = LDEC.WF_NAME_NOT_UNIQUE

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str, element: dict,
        app_wf_uuid_set: set, app_wf_dict: dict, app_model_dict: set,
        app_field_dict: set, app_func_uuid_dict: dict,
        app_page_uuid_set: set, app_const_uuid_set: set,
        app_enum_uuid_set: set, app_user_role_uuid_set: set,
            app_page_dict: dict, modules_role: dict, app_relationship_uuid_set: set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, element, *args, **kwargs)
        self.app_wf_uuid_set = app_wf_uuid_set
        self.app_wf_dict = app_wf_dict
        self.app_model_dict = app_model_dict
        self.app_field_dict = app_field_dict
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_uuid_set = app_page_uuid_set
        self.app_const_uuid_set = app_const_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.modules_role = modules_role
        self.app_relationship_uuid_set = app_relationship_uuid_set
        self.app_page_dict = app_page_dict
        self.description = self.element.get("description", "")
        self.online_version = self.element.get("online_version", "")
        self.show_version = self.element.get("show_version", "")
        self.versions = self.element.get("versions", list())
        self.type = self.element.get("type", 0)
        self.online_version_uuid_set = set()
        self.version_uuid_set = set()
        self.version_error = False
        self.version_unique = True
        self.workflow = dict()
        self.all_version_variables = dict()
        self.total_from = ""
        self.pc_from = ""
        self.mobile_from = ""
        self.pad_from = ""
        self.wf_to_page_uuid = ""
        self.allow_chinese_name = True
        self.settings_page_info = dict()
        self.check_settings_page = partial(check_settings_page, self)
        self.nodes_direction = kwargs.get("nodes_direction", dict())
        if self.is_copy:
            new_versions = list()
            for wf in self.versions:
                version_uuid = wf.get("uuid")
                last_version_uuid = self.versions[-1].get("uuid")  # 未知原因导致show_version没匹配上, 取最后一个版本复制
                if version_uuid == self.show_version or version_uuid == last_version_uuid:
                    temp_uuid = lemon_uuid()
                    self.element.update({"online_version": temp_uuid})
                    self.element.update({"show_version": temp_uuid})
                    self.online_version = temp_uuid
                    self.show_version = temp_uuid
                    wf.update({
                        "uuid": temp_uuid,
                        "status": 0,  # 草稿
                        "version": 1,
                        "timestamp": time.time()
                    })
                    new_versions.append(wf)
                    self.versions = new_versions
                    self.element["versions"] = self.versions
                    break

        for wf in self.versions:
            online = wf.get("online", False)
            version_uuid = wf.get("uuid")
            settings = wf.get("settings", dict())
            variables = settings.get("variables", list())
            self.all_version_variables.update({version_uuid: variables})
            if online is True:
                self.online_version_uuid_set.add(version_uuid)
            if version_uuid == self.online_version:
                self.workflow = wf
                for device in ["total", "pc", "mobile", "pad"]:
                    from_dic = settings.get(device+"_from", {})
                    from_page = from_dic.get("page", "")
                    setattr(self, device+"_from", from_page)
            if version_uuid in self.version_uuid_set:
                self.version_unique = False
            else:
                self.version_uuid_set.add(version_uuid)
        if self.online_version not in self.version_uuid_set:
            self.version_error = True
        self.branch_wf = self.kwargs.get("branch_wf", False)
        if self.branch_wf:
            self.workflow = self.element
        self.settings = self.workflow.get("settings", dict())
        self.variables = self.settings.get("variables", list())
        self.nodes = self.workflow.get("nodes", list())
        self.checked_uuid = None
        self.checked_version = None
        if not self.branch_wf:
            self.checked_uuid = self.workflow.get("uuid")
            self.checked_version = self.workflow.get('version')
        self.node_uuid_set = set()
        self.node_name_set = set()
        self.variable_uuid_set = set()
        self.variable_name_set = set()

        # 拿到当前工作流所有 节点 的UUID
        self.wf_node_uuid_set = set()
        self.wf_node_name_set = set()
        self.node_list = list()
        self.node_dict_list = list()
        self.need_schedule = False
        for node in self.nodes:
            if isinstance(node, dict):
                node_uuid = node.get("uuid")
                node_name = node.get("name")
                node_type = node.get("type")
                self.wf_node_uuid_set.add(node_uuid)
                self.wf_node_name_set.add(node_name)
                self.node_list.append(node)
                self.node_dict_list.append(
                    {"uuid": node_uuid, "name": node_name, "type": node_type})
        outmost_nodes = self.wf_checker.nodes
        for node in outmost_nodes:
            if node.get("type") == NodeType.START:
                self.need_schedule = node.get("need_schedule", False)
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []

    def build_insert_query_data(self):
        return {
            WorkflowModel.app_uuid.name: self.app_uuid,
            WorkflowModel.module_uuid.name: self.module_uuid,
            WorkflowModel.document_uuid.name: self.document_uuid,
            WorkflowModel.wf_uuid.name: self.element_uuid,
            WorkflowModel.wf_name.name: self.element_name,
            WorkflowModel.description.name: self.description,
            WorkflowModel.online_version.name: self.online_version,
            WorkflowModel.variables.name: self.all_version_variables,
            WorkflowModel.nodes.name: self.node_dict_list,
            WorkflowModel.modify_time.name: int(time.time()),
            WorkflowModel.pc_from.name: self.pc_from or self.total_from,  # setdefault total_from
            WorkflowModel.mobile_from.name: self.mobile_from or self.total_from,
            WorkflowModel.pad_from.name: self.pad_from or self.total_from,
            WorkflowModel.type.name: self.type,
            WorkflowModel.need_schedule.name: self.need_schedule
        }

    def build_update_query_data_version(self):
        return {
            WorkflowModel.online_version.name: self.online_version,
            WorkflowModel.variables.name: self.all_version_variables,
            WorkflowModel.nodes.name: self.node_dict_list,
            WorkflowModel.modify_time.name: int(time.time()),
            WorkflowModel.is_delete.name: False,
            WorkflowModel.pc_from.name: self.pc_from or self.total_from,
            WorkflowModel.mobile_from.name: self.mobile_from or self.total_from,
            WorkflowModel.pad_from.name: self.pad_from or self.total_from,
            WorkflowModel.need_schedule.name: self.need_schedule
        }

    def build_update_query_data_base(self):
        return {
            WorkflowModel.wf_name.name: self.element_name,
            WorkflowModel.description.name: self.description
        }

    def build_update_query(self, check_all_wf=True):
        base_query_data = self.build_update_query_data_base()
        version_query_data = {}
        if check_all_wf:
            version_query_data = self.build_update_query_data_version()
        query_data = {**base_query_data, **version_query_data}
        return WorkflowModel.update(**query_data).where(WorkflowModel.wf_uuid == self.element_uuid)

    @staticmethod
    def build_delete_query(sm_uuid):
        return WorkflowModel.update(**{
                WorkflowModel.is_delete.name: True
            }).where(WorkflowModel.wf_uuid == sm_uuid)

    def check_modify(self, document_wf_uuid_set, check_all_wf=True):
        if self.element_uuid in document_wf_uuid_set:
            query = self.build_update_query(check_all_wf)
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

    def check_settings(self):

        part_key = ["pc", "mobile", "pad"]
        key = ["total"] + part_key
        _type = ["_from", "_to"]

        for t in _type:
            for k in key:
                self.check_settings_page(k, t)
        app_log.info(f"settings_page_info: {self.settings_page_info}")

        for t in _type:
            page_exists = list()
            page_models = list()
            for k in key:
                page_exist = self.settings_page_info.get(k+t).get("page_exist")
                page_exists.append(page_exist)

                page_model = self.settings_page_info.get(k+t).get("form_models")
                page_models.append(page_model)
            if not page_exists[0]:
                if not all(page_exists[1:]):
                    attr_key = "TOTAL" + t.upper() + "_PAGE"
                    attr = getattr(Workflow.ATTR, attr_key)
                    return_code_key = "WF" + t.upper() + "_PAGE_NOT_FOUND"
                    return_code = getattr(LDEC, return_code_key)
                    self._add_error_list(attr=attr, return_code=return_code)

            device_models = [models for models in page_models if models]
            if device_models:
                device_models_the_same = reduce(lambda x, y: x if x == y else False, device_models)
                attr_key = "TOTAL" + t.upper() + "_PAGE"
                attr = getattr(Workflow.ATTR, attr_key)
                if not device_models_the_same:
                    return_code_key = "WF" + t.upper() + "_PAGE_FORM_MODEL_DIFF"
                    return_code = getattr(LDEC, return_code_key)
                    self._add_error_list(attr=attr, return_code=return_code)
                else:
                    if device_models_the_same[0] in UUID.TABLE_NAME_ALL:
                        return_code_key = "WF" + t.upper() + "_PAGE_SYS_MODEL"
                        return_code = getattr(LDEC, return_code_key)
                        self._add_error_list(attr=attr, return_code=return_code)
                if t == "_to" and device_models_the_same:
                    self.wf_to_page_uuid = device_models_the_same
            page_exists.clear()
            page_models.clear()
        self.settings_page_info.clear()

    def check_workflow_version(self):
        if self.version_error:
            attr = Workflow.ATTR.VERSION
            return_code = LDEC.WF_ONLINE_VERSION_ERROR
            self._add_error_list(attr=attr, return_code=return_code)
        if not self.version_unique:
            attr = Workflow.ATTR.VERSION
            return_code = LDEC.WF_VERSION_NOT_UNIQUE
            self._add_error_list(attr=attr, return_code=return_code)
        if len(self.online_version_uuid_set) > 1:
            attr = Workflow.ATTR.VERSION
            return_code = LDEC.WF_ONLINE_VERSION_THAN_ONE
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查 变量列表
    def check_variables(self):
        for variable in self.variables:
            variable_checker_service = VariableCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, variable, parent=self)
            try:
                variable_checker_service.check_uuid()
                variable_checker_service.check_uuid_unique(self.variable_uuid_set)
                variable_checker_service.check_name()
                variable_checker_service.check_name_unique(self.variable_name_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(variable_checker_service)
                raise e
            else:
                self.update_any_list(variable_checker_service)

    def check_nodes_order(self, nodes):
        def _create_graph(nodes):
            graph = {}
            for node in nodes:
                uuid = node["uuid"]
                neighbors = set()

                for line in node.get("lines", list()):
                    settings = line.get("settings", dict())
                    to_node_uuid = settings.get("to_node")
                    if to_node_uuid and not settings.get("action") and not settings.get("value"):
                        neighbors.add(to_node_uuid)
                graph[uuid] = neighbors
            return graph

        def _topological_sort(graph):
            in_degrees = {node: 0 for node in graph}
            for node in graph:
                for neighbor in graph[node]:
                    in_degrees[neighbor] += 1

            zero_indegree_nodes = [node for node in in_degrees if in_degrees[node] == 0]

            sorted_nodes = []
            while zero_indegree_nodes:
                node = zero_indegree_nodes.pop(0)
                sorted_nodes.append(node)
                for neighbor in graph[node]:
                    in_degrees[neighbor] -= 1
                    if in_degrees[neighbor] == 0:
                        zero_indegree_nodes.append(neighbor)

            if len(sorted_nodes) != len(graph):
                return False

            return sorted_nodes

        graph = _create_graph(nodes)
        sorted_nodes = _topological_sort(graph)
        if not sorted_nodes:
            # 添加报错, 流程成环且环上节点全部自动流转
            attr = Workflow.ATTR.NODE
            return_code = LDEC.NODE_LOOP
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查 各个节点
    def check_nodes(self):
        start_node_count = 0
        end_node_count = 0
        # TODO 没看懂为什么希望node
        # flag = True if self.is_copy else False
        # self.is_copy = not self.is_copy if flag else self.is_copy
        if not self.branch_wf:
            self.document_other_info.update({"settings": self.settings})
        self.process_nodes_direction()
        self.check_nodes_order(self.nodes)
        for node in self.nodes:
            cur_node_direction = self.nodes_direction.get(node["uuid"], list())
            node_checker_service = NodeCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, node, self.element_uuid, self.app_wf_uuid_set, self.app_wf_dict,
                self.app_func_uuid_dict, self.app_page_uuid_set, self.app_user_role_uuid_set,
                self.app_page_dict, self.modules_role, self.app_model_dict, self.app_field_dict,
                self.app_relationship_uuid_set, cur_node_direction, parent=self,
                relationship_list=self.relationship_list, wf_to_page_uuid=self.wf_to_page_uuid,
                module_roles_user_related=self.module_roles_user_related)
            node_checker_service.document_other_info = self.document_other_info
            node_type = node_checker_service.type
            if node_type == Node.TYPE.START:
                start_node_count += 1
            elif node_type == Node.TYPE.END:
                end_node_count += 1
            try:
                node_checker_service.check_uuid()
                node_checker_service.check_uuid_unique(self.node_uuid_set)
                node_checker_service.check_name()
                if node_checker_service.type != NodeType.COUNTERSIGN:
                    node_checker_service.check_name_unique(self.node_name_set)
                node_checker_service.check_node_type()
                node_checker_service.check_node(self.wf_node_uuid_set, self.variable_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(node_checker_service)
                # self.is_copy = not self.is_copy if flag else self.is_copy
                raise e
            else:
                self.update_any_list(node_checker_service)
            # self.is_copy = not self.is_copy if flag else self.is_copy
            if start_node_count != 1:
                attr = Workflow.ATTR.START_NODE
                return_code = LDEC.WF_START_NODE_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
        # if end_node_count != 1:
        #     attr = Workflow.ATTR.END_NODE
        #     return_code = LDEC.WF_END_NODE_INCURRECT
        #     self._add_error_list(attr=attr, return_code=return_code)

    def process_nodes_direction(self):
        for node in self.nodes:
            lines = node.get("lines", list())
            for line in lines:
                settings = line.get("settings", dict())
                to_node = settings.get("to_node")
                to_node_info = self.nodes_direction.setdefault(to_node, list())
                if node not in to_node_info:
                    to_node_info.append(node)


class WorkflowDocumentCheckerService(DocumentCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str,
        element: dict, document_version: int,
        app_workflow_list: list, app_model_dict: set,
        app_field_dict: set, app_func_uuid_dict: dict,
        app_page_list: set, app_const_uuid_set: set,
        app_enum_uuid_set: set, app_user_role_uuid_set: set, modules_role,
            app_relationship_uuid_set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, WorkflowModel, *args, **kwargs)
        self.document_version = document_version
        self.app_model_dict = app_model_dict
        self.app_field_dict = app_field_dict
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_list = app_page_list
        self.app_const_uuid_set = app_const_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.modules_role = modules_role
        self.app_relationship_uuid_set = app_relationship_uuid_set
        self.app_page_uuid_set = set()
        self.app_page_dict = dict()
        self.other_conflict_document_names_set = kwargs.get("other_conflict_document_names_set", set())
        for page in self.app_page_list:
            page_uuid = page.get(PageModel.page_uuid.name)
            self.app_page_dict.update({page_uuid: page})
            if not page.get(PageModel.is_delete.name):
                self.app_page_uuid_set.add(page_uuid)
        self.app_wf_uuid_set = set()
        self.module_wf_name_set = set()
        self.document_wf_uuid_set = set()
        self.app_wf_dict = dict()
        for wf in app_workflow_list:
            wf_uuid = wf.get("wf_uuid", "")
            wf_name = wf.get("wf_name", "")
            wf_module_uuid = wf.get("module_uuid", "")
            wf_document_uuid = wf.get("document_uuid", "")

            # 找到原文档中所有的工作流，为了新增、更新、删除文档的工作流
            if wf_document_uuid == self.document_uuid:
                self.document_wf_uuid_set.add(wf_uuid)
            else:
                # 排除当前文档所有的 wf_uuid，获取应用的所有 wf_uuid
                self.app_wf_uuid_set.add(wf_uuid)
                # 排除当前文档所有的 wf_name，获取模块的所有 wf_name
                if wf_module_uuid == self.module_uuid:
                    self.module_wf_name_set.add(wf_name)

            if wf.get("is_delete") is False:
                self.app_wf_dict[wf_uuid] = wf

    @checker.run
    def check_workflow(self):
        workflow_checker_service = WorkflowCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, self.element,
                self.app_wf_uuid_set, self.app_wf_dict, self.app_model_dict,
                self.app_field_dict, self.app_func_uuid_dict,
                self.app_page_uuid_set, self.app_const_uuid_set,
                self.app_enum_uuid_set, self.app_user_role_uuid_set,
                self.app_page_dict, self.modules_role, self.app_relationship_uuid_set,
                relationship_list=self.relationship_list,
                is_copy=self.is_copy, parent=None, module_roles_user_related=self.module_roles_user_related)
        wf_uuid = workflow_checker_service.element_uuid
        workflow_checker_service.document_other_info = self.document_other_info
        try:
            workflow_checker_service.check_uuid()
            workflow_checker_service.check_uuid_unique(self.app_wf_uuid_set)
            workflow_checker_service.check_name()
            workflow_checker_service.check_name_unique(self.other_conflict_document_names_set)
            check_all_wf = workflow_checker_service.workflow.get("status") == 0  # 草稿状态需要检查所有
            if check_all_wf:
                # Do version related check
                workflow_checker_service.workflow["is_only_auto"] = True
                btn_list_data = workflow_checker_service.workflow.get("btn_list_data", [])
                workflow_checker_service._check_btn_list_icon(btn_list_data)
                workflow_checker_service.check_settings()
                workflow_checker_service.check_variables()
                workflow_checker_service.check_nodes()
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(workflow_checker_service)
            raise e
        else:
            self.update_any_list(workflow_checker_service)

        # 找到新增 或 更新的工作流
        workflow_checker_service.check_modify(self.document_wf_uuid_set, check_all_wf=check_all_wf)
        if workflow_checker_service.insert_query_list:
            self.document_insert_list.extend(workflow_checker_service.insert_query_list)
        if workflow_checker_service.update_query_list:
            self.document_update_list.extend(workflow_checker_service.update_query_list)

        # 找出删除的工作流，将其 is_delete 置为 True
        delete_wf_uuid_set = self.document_wf_uuid_set - set([wf_uuid])
        for this_wf_uuid in delete_wf_uuid_set:
            query = workflow_checker_service.build_delete_query(this_wf_uuid)
            self.document_delete_list.append(query)

    async def commit_modify(self, engine):
        # document_update_func_list = []
        # document_delete_func_list = []

        for query in self.document_update_list:
            await engine.access.update_obj_by_query(self.model, query, need_delete=True)
            # document_update_func_list.append(func)
        for query in self.document_delete_list:
            await engine.access.update_obj_by_query(self.model, query, need_delete=True)
            # document_delete_func_list.append(func)

        # 这里如果数据量大的话，会有性能问题
        async with engine.db.objs.atomic():
            if self.document_insert_list:
                app_log.info(f"Insert {self.model.__name__}, len: {len(self.document_insert_list)}")
                await engine.access.insert_many_obj(self.model, self.document_insert_list)

            # app_log.info(f"Update {self.model.__name__}, len: {len(document_update_func_list)}")
            # await asyncio.gather(*document_update_func_list)

            # app_log.info(f"Update {self.model.__name__}.is_delete, len: {len(document_delete_func_list)}")
            # await asyncio.gather(*document_delete_func_list)
