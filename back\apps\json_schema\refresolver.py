from jsonschema import RefResolver as BaseRefResolver
import apps.json_schema.schemas as schemas


class RefResolver(BaseRefResolver):

    def resolve_remote(self, uri):
        if uri.startswith("mem://"):
            module_name, attr_name = uri.lstrip("mem://").split("/")
            module = getattr(schemas, module_name, None)
            if module is not None:
                return getattr(module, attr_name, None)
        else:
            result = super().resolve_remote(uri)
            return result
