# -*- coding:utf-8 -*-

from apps.services.checker import CheckerService

from apps.ide_const import LemonDesignerErrorC<PERSON> as LDEC
from apps.ide_const import Variable, ValueEditor


class BaseValueCheckerService(CheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, element_uuid, element_name, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.type = self.element.get("type")
        self.value_type = self.element.get("value_type")
        self.element_uuid = element_uuid
        self.element_name = element_name
    
    def initialize(self):
        super().initialize()


class StringValueCheckerService(BaseValueCheckerService):
    
    def initialize(self):
        super().initialize()
        self.value = self.element.get("value")
    
    def check_value(self, attr):
        return_code = LDEC.VALUE_AND_VALUE_TYPE_NOT_EQUAL
        if type(self.value) is not Variable.TYPE.TO_PYTHON.get(self.value_type):
            self._add_error_list(attr=attr, return_code=return_code)


class ConstValueCheckerService(BaseValueCheckerService):
    
    def initialize(self):
        super().initialize()
        self.module_uuid = self.element.get("module_uuid")
        self.const_uuid = self.element.get("const_uuid")


class VariableValueCheckerService(BaseValueCheckerService):
    
    def initialize(self):
        super().initialize()
        self.variable_level = self.element.get("variable_level")
        self.variable_uuid = self.element.get("variable_uuid")
        self.field_name = self.element.get("field_name")


class ExprValueCheckerService(BaseValueCheckerService):
    
    def initialize(self):
        super().initialize()
        self.expr = self.element.get("expr")


class ValueCheckerService(BaseValueCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, element_uuid, element_name, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, element_uuid, element_name, *args, **kwargs)
        self.value_checker_class_dict = {
            ValueEditor.TYPE.STRING: StringValueCheckerService,
            ValueEditor.TYPE.CONST: ConstValueCheckerService,
            ValueEditor.TYPE.VARIABLE: VariableValueCheckerService,
            ValueEditor.TYPE.EXPR: ExprValueCheckerService
        }
    
    def check_value(self, attr):
        # if self.value_type not in Variable.TYPE.ALL:
        #     return_code = LDEC.VALUE_TYPE_NOT_SUPPORT
        #     self._add_error_list(attr=attr, return_code=return_code)
        if self.type not in ValueEditor.TYPE.ALL:
            return_code = LDEC.VALUE_EDITER_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            value_checker_class = self.value_checker_class_dict.get(self.type)
            if value_checker_class:
                value_checker_service = value_checker_class(
                    self.app_uuid, self.module_uuid, self.module_name,
                    self.document_uuid, self.document_name, self.element,
                    self.element_uuid, self.element_name)
                # value_checker_service.check_value()
