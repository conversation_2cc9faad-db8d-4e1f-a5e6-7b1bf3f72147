import datetime
import time
import traceback
import re
import os
import asyncio
import u<PERSON><PERSON>
from io import BytesIO
from operator import itemgetter
from functools import partial
from collections import defaultdict
from decimal import Decimal
from copy import deepcopy

import aiohttp
import peewee
import openpyxl
from runtime.component.utils import (
    build_recursive_query, process_multifloor_data)
import xlsxwriter
from PIL import Image as P_Image
from openpyxl.utils import column_index_from_string

from apps.base_utils import (lemon_uuid, calc_decimal_field_list, python_agg_field,
                             calc_bool_field_list, is_system_field, get_sys_field_uuid)
from apps.utils import build_model_field_path
from baseutils.const import SystemTable, UserTableStatus, SystemField
from baseutils.log import app_log
from apps.ide_const import FieldType, TypeSystem, AggreFunc
from runtime.core.func_run import FuncRun
from runtime.utils import build_join_info
from runtime.core.utils import (
    calc_self_referential_path_join, lemon_association,
    lemon_field, lemon_model, lemon_model_node)
from runtime.engine import engine
from runtime.log import runtime_log
from runtime.api.helper import SimpleAtomic, RuntimeOssHelper
from runtime.var import app_relationship



class ExcelAtomic(SimpleAtomic):
    pass


class BaseTableObj(object):
    oss_helper = RuntimeOssHelper()
    zero_datetime = datetime.datetime.strptime("1970", "%Y")
    insert_error = "写入数据库失败"

    def __init__(self, excel_obj, table_dict) -> None:
        self.data_model = None
        self.excel_obj = excel_obj
        if excel_obj:
            self.reset_excel_obj()
        self.table_dict = table_dict
        self.unique_columns = table_dict.get("unique_columns", [])
        self.unique_data = table_dict.get("unique_data", {})
        self.repeating_data = 0
        self.max_id = 10000
        self.repeating_message = "由于设定唯一，{table_name}更新了{data_count}条数据"
        self.table_name = ""
        self.init_data_model()

    def init_data_model(self):
        if isinstance(self.table_dict, dict):
            self.data_model = self.table_dict.get("model")
            app_log.info(self.data_model)
            if self.data_model:
                self.table_name = self.data_model._meta.class_table_name

    def reset_excel_obj(self):
        self.datalist_obj = self.excel_obj.datalist_obj
        self.lsm = self.datalist_obj.lsm
        self.page_machine_id = self.datalist_obj.page_machine_id
        self.machine_id = self.datalist_obj.machine_id
        self.error_dict = self.excel_obj.error_dict

    async def handle_cell_value(self, value, column_info, column_index, row):
        result_value = None
        status = False
        field = column_info.get("field")
        field_import_method = column_info.get("field_import_method")
        is_required = column_info.get("is_required")
        pass_support = column_info.get("pass_support")
        error = False
        # 值编辑器
        if field_import_method == 1:
            default_data = column_info.get("excel_function")
            self.datalist_obj.lsm.lemon.system.cell_data = value
            result = await self.datalist_obj.lemon_editor(
                default_data, to_str=False)
            if result is False:
                error = True
                result_value = None
            else:
                result_value = result
                status = True
        # 云函数
        elif field_import_method == 2:
            func_uuid = column_info.get("excel_func")
            stdout, result = await FuncRun.run_func(
                func_uuid, value, need_stdout=True)
            if result is False:
                error = True
                result_value = None
            else:
                result_value = result
                status = True
        else:
            if pass_support and value is None:
                status = False
                result_value = None
            else:   
                try:
                    result_value, status = await self.deal_field_data(
                        field, value, column_index, row, column_info)
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    error = True
                    error_info = self.build_excel_data_error(column_index)
                    self.add_error_info(row, error_info)
                else:
                    if not status:
                        error = True
        if error:
            error_info = self.build_excel_data_error(column_index)
            self.add_error_info(row, error_info)
        else:
            if pass_support and result_value is None:
                status = False
            else:
                if is_required:
                    if result_value is None:
                        status = False
                        error_info = self.build_excel_data_error(
                            column_index, "为必填项")
                        self.add_error_info(row, error_info)
        return result_value, status

    async def get_relation_value_by_column_info(self, column_info, row):
        column_index = column_info.get("index")
        separator = column_info.get("separator")
        result_list = []
        value = self.excel_obj.sheet.cell(row, column_index).value
        if separator and isinstance(value, str):
            value_list = value.split(separator)
            for split_value in value_list:
                if column_info.get("field").lemon_type == TypeSystem.BOOLEAN:
                    if split_value == '1' or split_value == '0':
                        split_value = int(split_value)
                result_value, status = await self.handle_cell_value(split_value, column_info, column_index, row)
                result_list.append((result_value, status))
        else:
            result_value, status = await self.handle_cell_value(value, column_info, column_index, row)
            result_list.append((result_value, status))
        return result_list

    async def get_value_by_column_info(self, column_info, row):
        column_index = column_info.get("index")
        value = self.excel_obj.sheet.cell(row, column_index).value
        result_value, status = await self.handle_cell_value(value, column_info, column_index, row)
        return result_value, status

    async def get_cache_info(self, data_dict, unique_column, value):
        index_key = str(value)
        pk = data_dict.get(unique_column.name).get(index_key, "not_exist")
        colum_list = data_dict.keys()
        if pk == "not_exist":
            filter_info = {unique_column.name: value}
            obj = await engine.access.get_obj(unique_column.model, **filter_info)
            if not obj:
                pk = None
                data_dict[unique_column.name][value] = pk
            else:
                pk = obj.id
                # 有可能一行数据会通过本表的不同字段筛选
                for column_name in colum_list:
                    column_value = getattr(obj, column_name)
                    data_dict[column_name][column_value] = pk
        return pk

    async def get_unique_key(self, row):
        value_list = []
        filter_info = dict()
        for column_info in self.unique_columns:
            column = column_info.get("field")
            column_name = column.name
            value, status = await self.get_value_by_column_info(column_info, row)
            if status:
                value_list.append(str(value))
                filter_info[column_name] = value
            else:
                value_list.append("")
        unique_key = "-".join(value_list)
        unique_info = {"unique_key": unique_key, "filter_info": filter_info}
        return unique_info

    async def get_unique_info_by_data(self, data_dict):
        value_list = []
        filter_info = {}
        for column_info in self.unique_columns:
            column = column_info.get("field")
            column_name = column.name
            value = data_dict.get(column_name)
            if value is not None:
                value_list.append(str(value))
                filter_info[column_name] = value
            else:
                value_list.append("")
        unique_key = "-".join(value_list)
        unique_info = {"unique_key": unique_key, "filter_info": filter_info}
        return unique_info

    async def get_cache_by_columns(self, row, unique_info=None):
        unique_info = unique_info or await self.get_unique_key(row)
        index_key = unique_info.get("unique_key")
        filter_info = unique_info.get("filter_info")
        pk = self.unique_data.get(index_key, "not_exist")
        data_model = self.unique_columns[0].get("field").model
        if pk == "not_exist":
            obj = await engine.access.get_obj(data_model, **filter_info)
            if not obj:
                pk = None
                self.unique_data[index_key] = pk
            else:
                pk = obj.id
                self.unique_data[index_key] = pk
        return pk

    def update_cache_info(self, data_dict, row_info, pk):
        colum_list = data_dict.keys()
        for column_name in colum_list:
            column_value = row_info.get(column_name)
            data_dict[column_name][column_value] = pk
        return data_dict

    def build_excel_data_error(self, index, message="数据有误"):
        return f"第{index}列{message}"

    def add_error_info(self, row, message):
        row_error_dict = self.error_dict.setdefault(
            row, {"row": row, "error_list": []})
        error_list = row_error_dict.get("error_list")
        error_list.append(message)

    async def deal_field_data(
            self, field, cell_value, column, row, column_info):
        value = None
        status = False
        field = column_info.get("field")
        field_type = field.lemon_type
        if field_type == TypeSystem.DATETIME:
            if not cell_value:
                value = None
                status = True
            else:
                time_data = self.deal_time(cell_value, column_info)
                if time_data:
                    value = time_data
                    status = True
        elif field_type == TypeSystem.IMAGE:
            cell_place = ":".join([str(row), str(column)])
            image_body = self.excel_obj.image_dict.get(cell_place)
            if image_body:
                file_name = lemon_uuid() + ".jpeg"
                url = self.oss_helper.gen_oss_tmp_path(
                    middle_user_id=engine.config.MIDDLE_USER_UUID,
                    app_uuid=self.lsm.lemon.system.current_user.app_uuid,
                    tenant_uuid=self.lsm.lemon.system.current_user.tenant_id,
                    page_uuid=self.page_machine_id,
                    control_uuid=self.machine_id)
                url = url + "/" + file_name
                await self.oss_helper.put_oss_object(path=url, file=image_body)
                time_stamp = int(time.time() * 1000)
                image_data = {
                    "uid": "rc-upload-" + str(time_stamp),
                    "url": url,
                    "name": file_name,
                    "status": "done"
                }
                value = [image_data]
            else:
                value = []
            status = True

        elif field_type == TypeSystem.FILE:
            # 不支持，但也许之后会支持
            pass
        elif field_type == TypeSystem.ENUM:
            enums = field.choices
            for enum_index, enum in enumerate(enums):
                if cell_value == enum.value:
                    value = enum.value
                    status = True
                    break
            # 支持枚举字段导入为空
            if not status:
                if cell_value is None:
                    value = None
                status = True
        elif field_type == TypeSystem.BOOLEAN:
            if cell_value == "是" or cell_value == 1:
                value = 1
                status = True
            elif cell_value == "否" or cell_value == 0:
                value = 0
                status = True
            elif cell_value is None:
                value = None
                status = True
        elif field_type == TypeSystem.INTEGER:
            if cell_value is not None:
                try:
                    int(cell_value)
                except Exception as e:
                    raise e 
                value = cell_value
                status = True
            else:
                value = None
                status = True
        elif field_type == TypeSystem.DECIMAL:
            if cell_value is not None:
                try:
                    Decimal(cell_value)
                except Exception as e:
                    raise e
                value = cell_value
                status = True
            else:
                value = None
                status = True
        elif field_type == TypeSystem.STRING:
            if cell_value is None:
                value = None
            else:
                try:
                    value = str(cell_value)
                except Exception as e:
                    raise e
            status = True
        else:
            value = cell_value
            status = True
        return value, status

    def deal_time(self, time_str, column_info):
        if isinstance(time_str, (datetime.datetime, datetime.date)):
            timestamp = self.get_timestamp_by_datetime(time_str)
            return timestamp
        if not time_str:
            return
        time_str = re.split(r"[\s\-\_\.\:\/]+", time_str)
        len_time_str = len(time_str)
        if len_time_str < 6:
            for n in range(6 - len_time_str):
                time_str.append("00")
        time_str = "/".join(time_str)
        time_str = time_str.replace("年", "").replace("月", "").replace(
            "日", "").replace("时", "").replace("分", "").replace("秒", "")
        try:
            time_str = "".join(time_str)
            local_datetime = datetime.datetime.strptime(
                time_str, "%Y/%m/%d/%H/%M/%S")
            timestamp = self.get_timestamp_by_datetime(local_datetime)
        except:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            return False
        return timestamp

    def get_timestamp_by_datetime(self, local_datetime: datetime.datetime):
        time_zone = self.lsm.lemon.system.current_user.timezone
        # local_timezone = engine.app.config.LOCAL_TIMEZONE
        timestamp = (local_datetime -
                     self.zero_datetime).total_seconds() - time_zone * 3600
        return timestamp

    async def get_max_id(self, data_model):
        query = data_model.select(peewee.fn.MAX(data_model._meta.primary_key))
        async with engine.userdb.objs.atomic():
            self.max_id = await engine.userdb.objs.scalar(query)

    async def _save(self):
        pass

    async def save(self, *args, **kargs):
        result = await self._save(*args, **kargs)
        if self.repeating_data:
            message = self.repeating_message.format(
                table_name=self.table_name, data_count=self.repeating_data)
            self.add_error_info(-1, message)
        return result


class ExcelTable(BaseTableObj):

    def __init__(self, excel_obj, table_dict, *args, **kwargs) -> None:
        super().__init__(excel_obj, table_dict)
        self.table_unique_dict = self.table_dict.get("unique_dict")
        self.data_dict = table_dict.get("data_dict")
        self.submit_row_list = []

    async def collect_row_data(self, row):
        table_columns = self.table_dict.get("columns", {})
        row_info = {}
        for column in table_columns:
            value, status = await self.get_value_by_column_info(column, row)
            if status:
                field = column.get("field")
                row_info[field.name] = value
        if not row_info:
            return False
        # excel里可能存在多行数据对应数据库一行数据,通过设置的本表唯一字段来去重
        if self.unique_columns:
            unique_info = await self.get_unique_info_by_data(row_info)
            unique_value = unique_info.get("unique_key")
        # 未设置唯一字段，代表每一行都对应数据库一行数据
        else:
            unique_value = row
        if row_info:
            row_dict = {"row_info": row_info,
                        "row": row, "unique_value": unique_value}
            self.table_unique_dict[unique_value] = row_dict
            return row_dict
        else:
            return False

    async def _save(self, tree_obj):
        data_list = sorted(self.table_dict.get(
            "unique_dict").values(), key=itemgetter("row"))
        # 插入本表的数据，并将外键端在本表的模型和自关联的父节点关联上
        tree_table_list = tree_obj.table_list
        table_preserve = self.table_dict.get("preserve")
        submit_row_list = []
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            one_insert = True
            insert_data_list = []
            update_data_list = []
            row_info_list = []
            start_row = data_list[row].get("row")
            end_row = data_list[row_right - 1].get("row")
            for row_dict in data_list[row_left: row_right]:
                row_info = row_dict.get("row_info")
                row = row_dict.get("row")
                # 有问题的一行不插入
                if self.error_dict.get(row):
                    continue
                unique_value = row_dict.get("unique_value")
                for tree_table in tree_table_list:
                    tree_ref_info = tree_table.table_dict
                    data_dict = tree_ref_info.get("data_dict")
                    parent_dict = tree_ref_info.get("parent_dict")
                    parent_value = data_dict.get(unique_value)
                    parent_row = parent_dict.get(parent_value)
                    front_ref = tree_ref_info.get("front_ref")
                    # 依赖的父节点在excel内
                    if isinstance(parent_row, int):
                        # 依赖的父节点在同批次，所以不能一次插入多条数据
                        if start_row <= parent_row <= end_row:
                            one_insert = False
                        # 不支持父节点所在位置在子节点之后
                        elif row > end_row:
                            pass
                        # 依赖的父节点在之前的插入
                        else:
                            parent_pk = await tree_table.get_tree_cache_info(parent_value)
                            row_info[front_ref.name] = parent_pk
                    elif parent_value is None:
                        row_info[front_ref.name] = None
                    # 依赖的父节点不在excel内
                    else:
                        parent_pk = await tree_table.get_tree_cache_info(parent_value)
                        row_info[front_ref.name] = parent_pk
                update_data_list.append(deepcopy(row_info))
                insert_data_list.append(deepcopy(row_info))
                row_info_list.append(row_dict)
            # 自关联父节点pk都已查到
            if one_insert:
                query = self.data_model.insert_many(insert_data_list)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                        length = len(insert_data_list)
                        start = ans
                        for i in range(length):
                            pk = start + i
                            self.update_cache_info(
                                self.data_dict, insert_data_list[i], pk)
                            # 更新excel单行数据在数据库对应的pk
                            row_dict = row_info_list[i]
                            row_dict["pk"] = pk
                            submit_row_list.append(pk)
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    for i, data in enumerate(update_data_list):
                        query = self.data_model.insert(
                            data).on_conflict(preserve=table_preserve)
                        try:
                            async with engine.userdb.objs.atomic():
                                ans = await engine.userdb.objs.execute(
                                    query, notify=False)
                                if ans:
                                    pk = ans
                                    if ans < self.max_id:
                                        self.repeating_data += 1
                                    submit_row_list.append(ans)
                                    self.update_cache_info(
                                        self.data_dict, insert_data_list[i], pk)
                                    row_dict = row_info_list[i]
                                    row_dict["pk"] = pk
                        except:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                            await runtime_log.error(format_error)
                            row_dict = row_info_list[i]
                            row = row_dict.get("row")
                            self.add_error_info(row, self.insert_error)
            else:
                for row_dict in data_list[row_left: row_right]:
                    row_info = row_dict.get("row_info")
                    row = row_dict.get("row")
                    # 有问题的一行不插入
                    if self.error_dict.get(row):
                        continue
                    unique_value = row_dict.get("unique_value")
                    for tree_table in tree_table_list:
                        tree_ref_info = tree_table.table_dict
                        data_dict = tree_ref_info.get("data_dict")
                        parent_dict = tree_ref_info.get("parent_dict")
                        parent_value = data_dict.get(unique_value)
                        parent_row = parent_dict.get(parent_value)
                        front_ref = tree_ref_info.get("front_ref")
                        parent_pk = await tree_table.get_tree_cache_info(parent_value)
                        row_info[front_ref.name] = parent_pk
                    query = self.data_model.insert(
                        row_info).on_conflict(preserve=table_preserve)
                    try:
                        async with engine.userdb.objs.atomic():
                            ans = await engine.userdb.objs.execute(
                                query, notify=False)
                            if ans:
                                pk = ans
                                if ans < self.max_id:
                                    self.repeating_data += 1
                                submit_row_list.append(ans)
                                self.update_cache_info(
                                    self.data_dict, row_info, pk)
                                row_dict["pk"] = pk
                    except:
                        format_error = traceback.format_exc()
                        app_log.error(format_error)
                        await runtime_log.error(format_error)
                        row = row_dict.get("row")
                        self.add_error_info(row, self.insert_error)
        return submit_row_list


class RelationTable(BaseTableObj):

    def __init__(self, excel_obj, table_dict, excel_table_obj) -> None:
        super().__init__(excel_obj, table_dict)
        self.excel_table_obj = excel_table_obj  # 本表数据操作对象
        self.table_unique_dict = {}
        self.r_table_obj = None

    def stack_reset(self, excel_obj, excel_table_obj):
        self.excel_obj = excel_obj
        self.excel_table_obj = excel_table_obj
        self.reset_excel_obj()
        if self.r_table_obj:
            self.r_table_obj.stack_reset(self.excel_obj, self)

    def set_r_table_obj(self, r_table_obj):
        # 多级关联导入，下一级关联操作数据的对象
        self.r_table_obj = r_table_obj

    async def stack_save(self):
        if isinstance(self.r_table_obj, (BackRefScanStack, BackRefInsertStack,
                                         MiddleRefScanStack, MiddleRefInsertStack)):
            await self.save()
            await self.r_table_obj.stack_save()
            # 避免循环引用导致内存无法回收
            self.r_table_obj = None
        elif isinstance(self.r_table_obj, (FrontRefInsertStack, FrontScanStack)):
            await self.r_table_obj.stack_save()
            # 避免循环引用导致内存无法回收
            self.r_table_obj = None
            # 外键在本表的时候，关联表插入后会更新本表的外键所以需要进行更新操作
            await self.save()
        else:
            await self.save()


class RelationTableClass(object):

    table_obj = RelationTable

    def __init__(self, excel_obj, table_dict, excel_table_obj=None) -> None:
        self.data_model = None
        self.excel_obj = excel_obj
        self.table_dict = table_dict
        self.excel_table_obj = excel_table_obj
        self.table_list = []
        self.init_all_table()

    def init_all_table(self):
        for table_info in self.table_dict.values():
            self.table_list.append(self.table_obj(
                self.excel_obj, table_info, self.excel_table_obj))

    async def collect_row_data(self, row, row_dict):
        for table_instance in self.table_list:
            await table_instance.collect_row_data(row, row_dict)


class FrontScan(RelationTable):

    async def collect_row_data(self, row, row_dict):
        row_info = row_dict.get("row_info")
        ref_obj = self.table_dict.get("front_ref")
        pk = await self.get_cache_by_columns(row)
        if pk:
            row_info[ref_obj.name] = pk
        else:
            row_info[ref_obj.name] = None
        return pk


class FrontScanStack(FrontScan):

    def __init__(self, excel_obj, table_dict, excel_table_obj) -> None:
        super().__init__(excel_obj, table_dict, excel_table_obj)
        self.table_unique_dict = {}
        self.r_table_obj = None

    async def collect_row_data(self, row, row_dict):
        row_info = row_dict.get("row_info")
        # column = self.table_dict.get("column")
        ref_obj = self.table_dict.get("front_ref")
        # data_dict = self.table_dict.get("data_dict", {})
        # field = column.get("field")
        unique_info = await self.get_unique_key(row)
        index_key = unique_info.get("unique_key")
        pk = await self.get_cache_by_columns(row, unique_info)
        if pk:
            row_info[ref_obj.name] = pk
        else:
            row_info[ref_obj.name] = None
        table_row_dict = {"row_info": dict(), "row": row,
                          "unique_value": index_key, "pk": pk}
        self.table_unique_dict[index_key] = table_row_dict
        if self.r_table_obj:
            await self.r_table_obj.collect_row_data(row, table_row_dict)

    async def save(self):
        data_list = self.table_unique_dict.values()
        model = self.table_dict.get("model")
        for row_dict in data_list:
            row_info = row_dict.get("row_info")
            row = row_info.get("row")
            if row_info:
                pk = row_dict.get("pk")
                if pk:
                    query = model.update(**row_info).where(model.id == pk)
                    try:
                        async with engine.userdb.objs.atomic():
                            ans = await engine.userdb.objs.execute(
                                query, notify=False)
                    except:
                        format_error = traceback.format_exc()
                        app_log.error(format_error)
                        await runtime_log.error(format_error)
                        self.add_error_info(row, self.insert_error)

    async def stack_save(self):
        if isinstance(self.r_table_obj, (BackRefScanStack, BackRefInsertStack,
                                         MiddleRefScanStack, MiddleRefInsertStack)):
            await self.r_table_obj.stack_save()
            self.r_table_obj = None
        elif isinstance(self.r_table_obj, (FrontRefInsertStack, FrontScanStack)):
            await self.r_table_obj.stack_save()
            self.r_table_obj = None
            # 外键在本表的时候，关联表插入后会更新本表的外键所以需要进行更新操作
            await self.save()


class FrontScanClass(RelationTableClass):

    table_obj = FrontScan


class BackRefScan(FrontScan):

    async def collect_row_data(self, row, row_dict):
        row_unique = row_dict.get("unique_value")
        scan_table_info = self.table_dict
        column_dict = self.table_dict.get("column_dict")
        for column_list in column_dict.values():
            data_dict = defaultdict(dict)
            for column_info in column_list:
                field = column_info.get("field")
                field_name = field.name
                value_list = await self.get_relation_value_by_column_info(column_info, row)
                for index, value_info in enumerate(value_list):
                    value, status = value_info
                    if status:
                        data_dict[index][field_name] = value
            for data_info in data_dict.values():
                unique_info = await self.get_unique_info_by_data(data_info)
                unique_key = unique_info.get("unique_key")
                update_dict = scan_table_info.get("update_dict")
                update_dict[unique_key] = {
                    "row_unique": row_unique, "unique_info": unique_info}

    async def update_relation(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        scan_table_info = self.table_dict
        # column = column_info.get("field")
        update_dict = scan_table_info.get("update_dict")
        data_list = list(update_dict.values())
        # data_dict = scan_table_info.get("data_dict")
        front_ref = scan_table_info.get("front_ref")
        model = front_ref.model
        front_ref_column = front_ref.name
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            for row_dict in data_list[row_left: row_right]:
                row_unique = row_dict.get("row_unique")
                # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                table_row_dict = table_unique_dict.get(row_unique, {})
                row_pk = table_row_dict.get("pk")
                if row_pk:
                    unique_info = row_dict.get("unique_info")
                    pk = await self.get_cache_by_columns(None, unique_info)
                    if pk:
                        # row_info = {id_column: pk, front_ref_column: row_pk}
                        row_info = {front_ref_column: row_pk}
                        query = model.update(**row_info).where(model.id == pk)
                        try:
                            async with engine.userdb.objs.atomic():
                                ans = await engine.userdb.objs.execute(
                                    query, notify=False)
                        except:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                            await runtime_log.error(format_error)
                            self.add_error_info(row, self.insert_error)


class BackRefScanStack(BackRefScan):

    def __init__(self, excel_obj, table_dict, excel_table_obj) -> None:
        super().__init__(excel_obj, table_dict, excel_table_obj)
        self.table_unique_dict = {}
        self.r_table_obj = None

    async def collect_row_data(self, row, row_dict):
        row_unique = row_dict.get("unique_value")
        scan_table_info = self.table_dict
        column_dict = self.table_dict.get("column_dict")
        for column_list in column_dict.values():
            data_dict = defaultdict(dict)
            for column_info in column_list:
                field = column_info.get("field")
                field_name = field.name
                value_list = await self.get_relation_value_by_column_info(column_info, row)
                for index, value_info in enumerate(value_list):
                    value, status = value_info
                    if status:
                        data_dict[index][field_name] = value
            for data_info in data_dict.values():
                row_info = dict()
                unique_info = await self.get_unique_info_by_data(data_info)
                unique_key = unique_info.get("unique_key")
                update_dict = scan_table_info.get("update_dict")
                update_dict[unique_key] = {"row_unique": row_unique, "unique_info": unique_info,
                                           "row_info": row_info}
                table_row_dict = {"row_info": row_info,
                                  "row": row, "unique_value": unique_key}
                self.table_unique_dict[unique_key] = table_row_dict
                if self.r_table_obj:
                    await self.r_table_obj.collect_row_data(row, table_row_dict)

    async def save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        scan_table_info = self.table_dict
        update_dict = scan_table_info.get("update_dict")
        data_list = list(update_dict.values())
        front_ref = scan_table_info.get("front_ref")
        model = front_ref.model
        front_ref_column = front_ref.name
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            for row_dict in data_list[row_left: row_right]:
                row_unique = row_dict.get("row_unique")
                # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                table_row_dict = table_unique_dict.get(row_unique, {})
                row_pk = table_row_dict.get("pk")
                if row_pk:
                    unique_info = row_dict.get("unique_info")
                    unique_key = unique_info.get("unique_key")
                    pk = await self.get_cache_by_columns(None, unique_info)
                    if pk:
                        self.table_unique_dict[unique_key].update({"pk": pk})
                        row_info = row_dict.get("row_info")
                        row_info.update({front_ref_column: row_pk})
                        query = model.update(**row_info).where(model.id == pk)
                        try:
                            async with engine.userdb.objs.atomic():
                                ans = await engine.userdb.objs.execute(
                                    query, notify=False)
                        except:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                            await runtime_log.error(format_error)
                            self.add_error_info(row, self.insert_error)


class BackRefScanClass(FrontScanClass):

    table_obj = BackRefScan

    async def update_relation(self):
        for table_instance in self.table_list:
            await table_instance.update_relation()


class MiddleRefScan(FrontScan):

    async def collect_row_data(self, row, row_dict):
        row_unique = row_dict.get("unique_value")
        scan_table_info = self.table_dict
        column_dict = self.table_dict.get("column_dict")
        for column_list in column_dict.values():
            data_dict = defaultdict(dict)
            for column_info in column_list:
                field = column_info.get("field")
                field_name = field.name
                value_list = await self.get_relation_value_by_column_info(column_info, row)
                for index, value_info in enumerate(value_list):
                    value, status = value_info
                    if status:
                        data_dict[index][field_name] = value
            for data_info in data_dict.values():
                unique_info = await self.get_unique_info_by_data(data_info)
                unique_key = unique_info.get("unique_key")
                middle_dict = scan_table_info.get("middle_dict", {})
                middle_row_info = middle_dict.setdefault(
                    unique_key, {"row_unique_set": set(), "unique_info": unique_info})
                row_unique_set = middle_row_info.get("row_unique_set")
                row_unique_set.add(row_unique)

    async def save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        scan_table_info = self.table_dict
        # column = scan_table_info.get("column")
        # column = column.get("field")
        middle_dict = scan_table_info.get("middle_dict")
        data_list = list(middle_dict.values())
        # data_dict = scan_table_info.get("data_dict")
        front_ref = scan_table_info.get("front_ref")
        back_ref = scan_table_info.get("back_ref")
        model = front_ref.model
        front_ref_column = front_ref.name
        back_ref_column = back_ref.name
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            insert_data_list = []
            for row_dict in data_list[row_left: row_right]:
                unique_info = row_dict.get("unique_info")
                pk = await self.get_cache_by_columns(None, unique_info)
                if pk:
                    row_unique_set = row_dict.get("row_unique_set")
                    for row_unique in row_unique_set:
                        # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                        table_row_dict = table_unique_dict.get(row_unique, {})
                        row_pk = table_row_dict.get("pk")
                        if row_pk:
                            row_info = {back_ref_column: pk,
                                        front_ref_column: row_pk}
                            insert_data_list.append(row_info)
            if insert_data_list:
                query = model.insert_many(insert_data_list)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    self.add_error_info(row, self.insert_error)


class MiddleRefScanStack(MiddleRefScan):
    def __init__(self, excel_obj, table_dict, excel_table_obj) -> None:
        super().__init__(excel_obj, table_dict, excel_table_obj)
        self.table_unique_dict = {}
        self.r_table_obj = None

    async def collect_row_data(self, row, row_dict):
        row_unique = row_dict.get("unique_value")
        scan_table_info = self.table_dict
        column_dict = self.table_dict.get("column_dict")
        for column_list in column_dict.values():
            data_dict = defaultdict(dict)
            for column_info in column_list:
                field = column_info.get("field")
                field_name = field.name
                value_list = await self.get_relation_value_by_column_info(column_info, row)
                for index, value_info in enumerate(value_list):
                    value, status = value_info
                    if status:
                        data_dict[index][field_name] = value
                # if status:
                #     row_info = dict()
                #     middle_dict = scan_table_info.get("middle_dict", {})
                #     middle_row_info = middle_dict.setdefault(
                #         value, {"row_unique_set": set(), "value": value})
                #     row_unique_set = middle_row_info.get("row_unique_set")
                #     row_unique_set.add(row_unique)
                #     table_row_dict = {"row_info": row_info,
                #                       "row": row, "unique_value": value}
                #     self.table_unique_dict[value] = table_row_dict
                #     if self.r_table_obj:
                #         await self.r_table_obj.collect_row_data(row, table_row_dict)
            for data_info in data_dict.values():
                row_info = dict()
                unique_info = await self.get_unique_info_by_data(data_info)
                unique_key = unique_info.get("unique_key")
                middle_dict = scan_table_info.get("middle_dict", {})
                middle_row_info = middle_dict.setdefault(
                    unique_key, {"row_unique_set": set(), "unique_info": unique_info})
                row_unique_set = middle_row_info.get("row_unique_set")
                row_unique_set.add(row_unique)
                table_row_dict = {"row_info": row_info,
                                  "row": row, "unique_value": unique_key}
                self.table_unique_dict[unique_key] = table_row_dict
                if self.r_table_obj:
                    await self.r_table_obj.collect_row_data(row, table_row_dict)

    async def save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        scan_table_info = self.table_dict
        middle_dict = scan_table_info.get("middle_dict")
        data_list = list(middle_dict.values())
        front_ref = scan_table_info.get("front_ref")
        back_ref = scan_table_info.get("back_ref")
        model = front_ref.model
        front_ref_column = front_ref.name
        back_ref_column = back_ref.name
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            insert_data_list = []
            for row_dict in data_list[row_left: row_right]:
                unique_info = row_dict.get("unique_info")
                pk = await self.get_cache_by_columns(None, unique_info)
                if pk:
                    unique_key = unique_info.get("unique_key")
                    row_unique_set = row_dict.get("row_unique_set")
                    self.table_unique_dict[unique_key].update({"pk": pk})
                    for row_unique in row_unique_set:
                        # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                        table_row_dict = table_unique_dict.get(row_unique, {})
                        row_pk = table_row_dict.get("pk")
                        if row_pk:
                            row_info = {back_ref_column: pk,
                                        front_ref_column: row_pk}
                            insert_data_list.append(row_info)
            if insert_data_list:
                query = model.insert_many(insert_data_list)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    self.add_error_info(row, self.insert_error)


class MiddleRefScanClass(BackRefScanClass):

    table_obj = MiddleRefScan

    async def save(self):
        for table_instance in self.table_list:
            await table_instance.save()


class FrontRefInsert(RelationTable):

    async def collect_row_data(self, row, row_dict):
        row_unique = row_dict.get("unique_value")
        table_info = self.table_dict
        column_dict = table_info.get("column_dict")
        data_dict = table_info.get("data_dict")
        # 同一字段一行可能有多列，代表多条数据
        for i, columns in column_dict.items():
            insert_row_dict = defaultdict(dict)
            for column in columns:
                field = column.get("field")
                value_list = await self.get_relation_value_by_column_info(column, row)
                for index, value_info in enumerate(value_list):
                    value, status = value_info
                    if status:
                        insert_row_info = insert_row_dict[index]
                        insert_row_info[field.name] = value
            for index, insert_row_info in insert_row_dict.items():
                if not table_info:
                    continue
                # 保证同一批次数据字段数一致
                for column in columns:
                    field = column.get("field")
                    field_name = field.name
                    if field_name not in insert_row_info.keys():
                        insert_row_info[field_name] = None
                if self.unique_columns:
                    unique_info = await self.get_unique_info_by_data(insert_row_info)
                    insert_unique_value = unique_info.get("unique_key")
                    # ref_unique_column = table_info["unique"].get("field").name
                    # insert_unique_value = insert_row_info.get(
                    #     ref_unique_column)
                else:
                    insert_unique_value = ":".join(
                        [str(row), str(i), str(index)])
                if insert_row_info:
                    await self.collect_func(data_dict, insert_row_info,
                                            insert_unique_value,  row, i, row_unique)

    async def collect_func(self, data_dict, insert_row_info,
                           insert_unique_value,  row, index, row_unique):
        row_dict = data_dict.setdefault(
            insert_unique_value, {"row_unique_set": set()})
        new_row_info = {"row_info": insert_row_info,
                        "row": row, "row_index": index}
        row_dict.update(new_row_info)
        row_unique_set = row_dict.get("row_unique_set")
        row_unique_set.add(row_unique)

    async def _save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        insert_table_info = self.table_dict
        data_dict = insert_table_info.get("data_dict")
        front_ref = insert_table_info.get("front_ref")
        model = insert_table_info.get("model")
        front_ref_column = front_ref.name
        preserve = insert_table_info.get("preserve")
        data_list = list(data_dict.values())
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            row_dict_list = []
            insert_data_list = []
            for row_dict in data_list[row_left: row_right]:
                row_info = row_dict.get("row_info")
                insert_data_list.append(row_info)
                row_dict_list.append(row_dict)
            if insert_data_list:
                query = model.insert_many(insert_data_list)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                    length = len(insert_data_list)
                    start = ans
                    for i in range(length):
                        pk = start + i
                        row_dict = row_dict_list[i]
                        row_unique_set = row_dict.get("row_unique_set")
                        for row_unique in row_unique_set:
                            # 更新本表数据本关联外键的值
                            table_row_dict = table_unique_dict.get(
                                row_unique, {})
                            table_row_info = table_row_dict.get("row_info")
                            table_row_info[front_ref_column] = pk
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    for i, data in enumerate(insert_data_list):
                        query = model.insert(data).on_conflict(
                            preserve=preserve)
                        try:
                            async with engine.userdb.objs.atomic():
                                ans = await engine.userdb.objs.execute(
                                    query, notify=False)
                                if ans:
                                    row_dict = row_dict_list[i]
                                    row_unique_set = row_dict.get(
                                        "row_unique_set")
                                    pk = ans
                                    if ans < self.max_id:
                                        self.repeating_data += 1
                                    for row_unique in row_unique_set:
                                        # 更新本表数据本关联外键的值
                                        table_row_dict = table_unique_dict.get(
                                            row_unique, {})
                                        table_row_info = table_row_dict.get(
                                            "row_info")
                                        table_row_info[front_ref_column] = pk
                        except:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                            await runtime_log.error(format_error)
                            row_dict = row_dict_list[i]
                            row = row_dict.get("row")
                            self.add_error_info(row, self.insert_error)


class FrontRefInsertStack(FrontRefInsert):

    async def collect_func(self, data_dict, insert_row_info, insert_unique_value,  row, index, row_unique):
        row_dict = data_dict.setdefault(
            insert_unique_value, {"row_unique_set": set()})
        new_row_info = {"row_info": insert_row_info,
                        "row": row, "row_index": index, "unique_value": insert_unique_value}
        row_dict.update(new_row_info)
        row_unique_set = row_dict.get("row_unique_set")
        row_unique_set.add(row_unique)
        table_row_dict = {"row_info": insert_row_info,
                          "row": row, "unique_value": insert_unique_value}
        self.table_unique_dict[insert_unique_value] = table_row_dict
        if self.r_table_obj:
            await self.r_table_obj.collect_row_data(row, table_row_dict)

    async def _save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        insert_table_info = self.table_dict
        data_dict = insert_table_info.get("data_dict")
        front_ref = insert_table_info.get("front_ref")
        model = insert_table_info.get("model")
        front_ref_column = front_ref.name
        preserve = insert_table_info.get("preserve")
        data_list = list(data_dict.values())
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            row_dict_list = []
            insert_data_list = []
            update_data_list = []
            for row_dict in data_list[row_left: row_right]:
                row_info = row_dict.get("row_info")
                update_data_list.append(deepcopy(row_info))
                insert_data_list.append(deepcopy(row_info))
                row_dict_list.append(row_dict)
            if insert_data_list:
                query = model.insert_many(insert_data_list)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                    length = len(insert_data_list)
                    start = ans
                    for i in range(length):
                        pk = start + i
                        row_dict = row_dict_list[i]
                        row_unique_set = row_dict.get("row_unique_set")
                        insert_unique_value = row_dict.get("unique_value")
                        self.table_unique_dict[insert_unique_value].update({
                                                                           "pk": pk})
                        for row_unique in row_unique_set:
                            # 更新本表数据本关联外键的值
                            table_row_dict = table_unique_dict.get(
                                row_unique, {})
                            table_row_info = table_row_dict.get("row_info")
                            table_row_info[front_ref_column] = pk
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    for i, data in enumerate(update_data_list):
                        query = model.insert(data).on_conflict(
                            preserve=preserve)
                        try:
                            async with engine.userdb.objs.atomic():
                                ans = await engine.userdb.objs.execute(
                                    query, notify=False)
                                if ans:
                                    row_dict = row_dict_list[i]
                                    row_unique_set = row_dict.get(
                                        "row_unique_set")
                                    pk = ans
                                    if ans < self.max_id:
                                        self.repeating_data += 1
                                    insert_unique_value = row_dict.get(
                                        "unique_value")
                                    self.table_unique_dict[insert_unique_value].update({
                                                                                       "pk": pk})
                                    for row_unique in row_unique_set:
                                        # 更新本表数据本关联外键的值
                                        table_row_dict = table_unique_dict.get(
                                            row_unique, {})
                                        table_row_info = table_row_dict.get(
                                            "row_info")
                                        table_row_info[front_ref_column] = pk
                        except:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                            await runtime_log.error(format_error)
                            row_dict = row_dict_list[i]
                            row = row_dict.get("row")
                            self.add_error_info(row, self.insert_error)


class FrontRefInsertClass(RelationTableClass):
    table_obj = FrontRefInsert

    async def save(self):
        for table_instance in self.table_list:
            await table_instance.save()


class BackRefInsert(FrontRefInsert):

    def __init__(self, excel_obj, table_dict, excel_table_obj) -> None:
        super().__init__(excel_obj, table_dict, excel_table_obj)
        self.repeating_message = "{table_name}有{data_count}条数据受到影响"

    async def collect_func(self, data_dict, insert_row_info, insert_unique_value, row, index, row_unique):
        data_dict[insert_unique_value] = {
            "row_info": insert_row_info, "row_unique": row_unique,
            "row": row, "row_index": index}

    async def _save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        table_info = self.table_dict
        # 插入外键在其它模型上的数据
        data_dict = table_info.get("data_dict")
        front_ref = table_info.get("front_ref")
        model = table_info.get("model")
        front_ref_column = front_ref.name
        preserve = table_info.get("preserve")
        data_list = sorted(data_dict.values(), key=itemgetter("row"))
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            row_dict_list = []
            insert_data_list = []
            for row_dict in data_list[row_left: row_right]:
                row_info = row_dict.get("row_info")
                row_unique = row_dict.get("row_unique")
                # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                table_row_dict = table_unique_dict.get(row_unique, {})
                pk = table_row_dict.get("pk")
                row_dict["row_pk"] = pk
                # 只有主表插入成功才能插入关联表
                if pk:
                    row_info[front_ref_column] = pk
                    insert_data_list.append(row_info)
                    row_dict_list.append(row_dict)
            if insert_data_list:
                query = model.insert_many(
                    insert_data_list).on_conflict(preserve=preserve)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                        self.repeating_data += len(insert_data_list)
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)


class BackRefInsertStack(BackRefInsert):

    async def collect_func(self, data_dict, insert_row_info, insert_unique_value, row, index, row_unique):
        data_dict[insert_unique_value] = {
            "row_info": insert_row_info, "row_unique": row_unique,
            "row": row, "row_index": index, "unique_value": insert_unique_value}
        table_row_dict = {"row_info": insert_row_info,
                          "row": row, "unique_value": insert_unique_value}
        self.table_unique_dict[insert_unique_value] = table_row_dict
        if self.r_table_obj:
            await self.r_table_obj.collect_row_data(row, table_row_dict)

    async def _save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        table_info = self.table_dict
        # 插入外键在其它模型上的数据
        data_dict = table_info.get("data_dict")
        front_ref = table_info.get("front_ref")
        model = table_info.get("model")
        front_ref_column = front_ref.name
        preserve = table_info.get("preserve")
        data_list = sorted(data_dict.values(), key=itemgetter("row"))
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            row_dict_list = []
            insert_data_list = []
            update_data_list = []
            for row_dict in data_list[row_left: row_right]:
                row_info = row_dict.get("row_info")
                row_unique = row_dict.get("row_unique")
                # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                table_row_dict = table_unique_dict.get(row_unique, {})
                pk = table_row_dict.get("pk")
                row_dict["row_pk"] = pk
                # 只有主表插入成功才能插入关联表
                if pk:
                    row_info[front_ref_column] = pk
                    insert_data_list.append(deepcopy(row_info))
                    update_data_list.append(deepcopy(row_info))
                    row_dict_list.append(row_dict)
            if insert_data_list:
                query = model.insert_many(insert_data_list)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                        length = len(insert_data_list)
                        start = ans
                        for i in range(length):
                            pk = start + i
                            row_dict = row_dict_list[i]
                            insert_unique_value = row_dict.get("unique_value")
                            self.table_unique_dict[insert_unique_value].update({
                                                                               "pk": pk})
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    for i, data in enumerate(update_data_list):
                        query = model.insert(data).on_conflict(
                            preserve=preserve)
                        try:
                            async with engine.userdb.objs.atomic():
                                ans = await engine.userdb.objs.execute(
                                    query, notify=False)
                                if ans:
                                    row_dict = row_dict_list[i]
                                    insert_unique_value = row_dict.get(
                                        "unique_value")
                                    if ans < self.max_id:
                                        self.repeating_data += 1
                                    self.table_unique_dict[insert_unique_value].update({
                                                                                       "pk": ans})
                        except:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                            await runtime_log.error(format_error)
                            row_dict = row_dict_list[i]
                            row = row_dict.get("row")
                            self.add_error_info(row, self.insert_error)


class BackRefInsertClass(FrontRefInsertClass):
    table_obj = BackRefInsert


class MiddleRefInsert(FrontRefInsert):

    async def _save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        insert_table_info = self.table_dict
        data_dict = insert_table_info.get("data_dict")
        front_ref = insert_table_info.get("front_ref")
        model = insert_table_info.get("model")
        front_ref_column = front_ref.name
        back_ref = insert_table_info.get("back_ref")
        back_ref_column = back_ref.name
        preserve = insert_table_info.get("preserve")
        data_list = list(data_dict.values())
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            row_dict_list = []
            insert_data_list = []
            update_data_list = []
            middle_data_list = []
            for row_dict in data_list[row_left: row_right]:
                row_info = row_dict.get("row_info")
                insert_data_list.append(deepcopy(row_info))
                update_data_list.append(deepcopy(row_info))
                row_dict_list.append(row_dict)
            if insert_data_list:
                query = model.insert_many(insert_data_list)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                    length = len(insert_data_list)
                    start = ans
                    for i in range(length):
                        pk = start + i
                        row_dict = row_dict_list[i]
                        row_unique_set = row_dict.get("row_unique_set")
                        for row_unique in row_unique_set:
                            # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                            table_row_dict = table_unique_dict.get(
                                row_unique, {})
                            row_pk = table_row_dict.get("pk")
                            if row_pk:
                                row_info = {back_ref_column: pk,
                                            front_ref_column: row_pk}
                                middle_data_list.append(row_info)
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    for i, data in enumerate(update_data_list):
                        query = model.insert(data).on_conflict(
                            preserve=preserve)
                        try:
                            async with engine.userdb.objs.atomic():
                                ans = await engine.userdb.objs.execute(
                                    query, notify=False)
                                if ans:
                                    row_dict = row_dict_list[i]
                                    row_unique_set = row_dict.get(
                                        "row_unique_set")
                                    if ans < self.max_id:
                                        self.repeating_data += 1
                                    for row_unique in row_unique_set:
                                        # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                                        table_row_dict = table_unique_dict.get(
                                            row_unique, {})
                                        row_pk = table_row_dict.get("pk")
                                        if row_pk:
                                            row_info = {
                                                back_ref_column: ans,
                                                front_ref_column: row_pk}
                                            middle_data_list.append(row_info)
                        except:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                            await runtime_log.error(format_error)
                            row_dict = row_dict_list[i]
                            row = row_dict.get("row")
                            self.add_error_info(row, self.insert_error)
                if middle_data_list:
                    query = front_ref.model.insert_many(middle_data_list)
                    try:
                        async with engine.userdb.objs.atomic():
                            ans = await engine.userdb.objs.execute(
                                query, notify=False)
                    except:
                        format_error = traceback.format_exc()
                        app_log.error(format_error)
                        await runtime_log.error(format_error)
                        self.add_error_info(row, self.insert_error)


class MiddleRefInsertStack(FrontRefInsertStack):

    async def _save(self):
        table_unique_dict = self.excel_table_obj.table_unique_dict
        insert_table_info = self.table_dict
        data_dict = insert_table_info.get("data_dict")
        front_ref = insert_table_info.get("front_ref")
        model = insert_table_info.get("model")
        front_ref_column = front_ref.name
        back_ref = insert_table_info.get("back_ref")
        back_ref_column = back_ref.name
        preserve = insert_table_info.get("preserve")
        data_list = list(data_dict.values())
        for row in range(0, len(data_list), 100):
            row_left = row
            row_right = row + 100 if row + \
                100 < len(data_list) else len(data_list)
            row_dict_list = []
            insert_data_list = []
            update_data_list = []
            middle_data_list = []
            for row_dict in data_list[row_left: row_right]:
                row_info = row_dict.get("row_info")
                insert_data_list.append(deepcopy(row_info))
                update_data_list.append(deepcopy(row_info))
                row_dict_list.append(row_dict)
            if insert_data_list:
                query = model.insert_many(insert_data_list)
                try:
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)
                    length = len(insert_data_list)
                    start = ans
                    for i in range(length):
                        pk = start + i
                        row_dict = row_dict_list[i]
                        row_unique_set = row_dict.get("row_unique_set")
                        row_unique_set = row_dict.get("row_unique_set")
                        insert_unique_value = row_dict.get("unique_value")
                        self.table_unique_dict[insert_unique_value].update({
                                                                           "pk": pk})
                        for row_unique in row_unique_set:
                            # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                            table_row_dict = table_unique_dict.get(
                                row_unique, {})
                            row_pk = table_row_dict.get("pk")
                            if row_pk:
                                row_info = {back_ref_column: pk,
                                            front_ref_column: row_pk}
                                middle_data_list.append(row_info)
                except:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                    await runtime_log.error(format_error)
                    for i, data in enumerate(update_data_list):
                        query = model.insert(data).on_conflict(
                            preserve=preserve)
                        try:
                            async with engine.userdb.objs.atomic():
                                ans = await engine.userdb.objs.execute(
                                    query, notify=False)
                                if ans:
                                    if ans < self.max_id:
                                        self.repeating_data += 1
                                    row_dict = row_dict_list[i]
                                    row_unique_set = row_dict.get(
                                        "row_unique_set")
                                    row_unique_set = row_dict.get(
                                        "row_unique_set")
                                    insert_unique_value = row_dict.get(
                                        "unique_value")
                                    self.table_unique_dict[insert_unique_value].update({
                                                                                       "pk": ans})
                                    for row_unique in row_unique_set:
                                        # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                                        table_row_dict = table_unique_dict.get(
                                            row_unique, {})
                                        row_pk = table_row_dict.get("pk")
                                        if row_pk:
                                            row_info = {
                                                back_ref_column: ans,
                                                front_ref_column: row_pk}
                                            middle_data_list.append(row_info)
                        except:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                            await runtime_log.error(format_error)
                            row_dict = row_dict_list[i]
                            row = row_dict.get("row")
                            self.add_error_info(row, self.insert_error)
                if middle_data_list:
                    query = front_ref.model.insert_many(middle_data_list)
                    try:
                        async with engine.userdb.objs.atomic():
                            ans = await engine.userdb.objs.execute(
                                query, notify=False)
                    except:
                        format_error = traceback.format_exc()
                        app_log.error(format_error)
                        await runtime_log.error(format_error)
                        self.add_error_info(row, self.insert_error)


class MiddleRefInsertClass(FrontRefInsertClass):
    table_obj = MiddleRefInsert


class TreeRefObj(ExcelTable):

    async def collect_row_data(self, row, row_dict):
        row_unique = row_dict.get("unique_value")
        row_info = row_dict.get("row_info")
        tree_ref_info = self.table_dict
        column_info = tree_ref_info.get("column")
        column = column_info.get("field")
        data_dict = tree_ref_info.get("data_dict")
        parent_dict = tree_ref_info.get("parent_dict")
        value, status = await self.get_value_by_column_info(column_info, row)
        if status:
            parent_column_value = row_info.get(column.name)
            if parent_column_value is not None:
                # 用于自关联找到同批插入的父级
                if value is not None:
                    parent_column_value = "/".join([value,
                                                   parent_column_value])
                parent_dict[parent_column_value] = row
            data_dict[row_unique] = value

    async def get_tree_cache_info(self, value):
        if not value:
            return None
        parent_pk_dict = self.table_dict.get("parent_pk_dict")
        pk = parent_pk_dict.get(value, "not_exist")
        if pk == "not_exist":
            column_info = self.table_dict.get("column")
            column = column_info.get("field")
            front_ref = self.table_dict.get("tree_front_ref")
            tree_list = value.split("/")
            name = tree_list[-1]
            parent_value = "/".join(tree_list[:-1])
            if parent_value:
                parent_pk = await self.get_tree_cache_info(parent_value)
            else:
                parent_pk = None
            filter_info = {column.name: name, front_ref.name: parent_pk}
            obj = await engine.access.get_obj(column.model, **filter_info)
            if not obj:
                pk = None
                parent_pk_dict[value] = pk
            else:
                pk = obj.id
                parent_pk_dict[value] = pk
        return pk


class TreeRefObjClass(RelationTableClass):
    table_obj = TreeRefObj


class FrontTreeRefObj(TreeRefObj):

    async def collect_row_data(self, row, row_dict):
        row_info = row_dict.get("row_info")
        column = self.table_dict.get("column")
        ref_obj = self.table_dict.get("front_ref")
        value, status = await self.get_value_by_column_info(column, row)
        if status:
            pk = await self.get_tree_cache_info(value)
            if pk:
                row_info[ref_obj.name] = pk
            else:
                row_info[ref_obj.name] = None


class FrontTreeRefObjClass(TreeRefObjClass):
    table_obj = FrontTreeRefObj


class MiddleTree(MiddleRefScan, TreeRefObj):

    async def get_cache_info(self, data_dict, unique_column, value):
        return await self.get_tree_cache_info(value)


class MiddleTreeClass(MiddleRefScanClass):
    table_obj = MiddleTree


class TableExport(object):
    oss_helper = RuntimeOssHelper()

    def __init__(self, datalist_obj, table_dict, sheet, write_excel) -> None:
        self.sheet = sheet
        self.cell_format = write_excel.add_format({
            "align": "left"
        })
        self.date_cell_format = write_excel.add_format({
            "align": "left",
            "num_format": "yyyy/mm/dd hh:mm:ss"
        })
        self.table_dict = table_dict
        self.model = table_dict.get("model")
        self.datalist_obj = datalist_obj

    def build_column_query(self, base_query):
        field_list = self.table_dict.get("field_list")
        base_query = base_query.select_extend(*field_list)
        if not base_query._group_by:
            base_query = base_query.group_by(self.model.id)
        return base_query

    async def export_table_data(self, row_dict, row):
        columns = self.table_dict.get("columns")
        for column_info in columns:
            field = column_info.get("field")
            field_value = row_dict.get(field.name)
            await self.insert_cell_data(column_info, row, field_value)

    def get_image_data(self, image_body):
        image_data = BytesIO(image_body)
        pil_img = P_Image.open(image_data)
        h = pil_img.height
        w = pil_img.width
        return h, w, image_data

    async def cell_write(self, row, index, value, cell_format=None):
        cell_format = cell_format or self.cell_format
        write_func = partial(self.sheet.write, row, index, value, cell_format)
        await engine.app.loop.run_in_executor(None, write_func)
        # self.sheet.write(row, index, value, cell_format)

    async def insert_field_data(self, field, column_value, column_index, row, separator=""):
        field_type = field.lemon_type
        value = column_value
        if field_type == TypeSystem.DATETIME:
            if isinstance(column_value, int):
                value = datetime.datetime.fromtimestamp(column_value)
                await self.cell_write(
                    row, column_index, value, self.date_cell_format)
            if isinstance(column_value, str):
                values = column_value.split("/")
                for i, v in enumerate(values):
                    v_agg_list = v.split(separator) if separator else v.split()
                    for v_agg_i in range(len(v_agg_list)):
                        v_agg = v_agg_list[v_agg_i]
                        try:
                            v_agg_list[v_agg_i] = str(datetime.datetime.fromtimestamp(int(v_agg)))
                        except Exception as e:
                            app_log.error(e)
                    values[i] = separator.join(v_agg_list)
                value = "/".join(values)
                await self.cell_write(row, column_index, value, self.date_cell_format)
        elif field_type == TypeSystem.IMAGE:
            if isinstance(column_value, list):
                for image_info in column_value:
                    image_url = image_info.get("url")
                    await self.excel_insert_image(
                        image_url, self.sheet, row, column_index)
        elif field_type == TypeSystem.FILE:
            # 不支持，但也许之后会支持
            value = None
        elif field_type == TypeSystem.BOOLEAN:
            if column_value is not None:
                if isinstance(column_value, int):
                    value = "是" if column_value else "否"
                elif isinstance(column_value, str):
                    values = column_value.split("/")
                    for i, v in enumerate(values):
                        value_list = v.split(separator) if separator else v.split()
                        for value_i, value_v in enumerate(value_list):
                            if value_v != "None":
                                value_list[value_i] = "是" if value_v == "1" else "否"
                        values[i] = separator.join(value_list)
                    value = "/".join(values)
                await self.cell_write(row, column_index, value)
        else:
            await self.cell_write(row, column_index, column_value)
        return value

    async def insert_cell_data(self, column_info, row, value, separator=""):
        column_index = column_info.get("index")

        # cell的row和column都是从1开始的
        row -= 1
        column_index -= 1
        field = column_info.get("field")
        field_import_method = column_info.get("field_import_method")
        self.datalist_obj.lsm.lemon.system.cell_row = row
        # 值编辑器
        if field_import_method == 1:
            default_data = column_info.get("excel_function")
            self.datalist_obj.lsm.lemon.system.cell_data = value
            result = await self.datalist_obj.lemon_editor(
                default_data, to_str=False)
            if result is not False:
                await self.cell_write(row, column_index, result)
        # 云函数
        elif field_import_method == 2:
            func_uuid = column_info.get("excel_func")
            stdout, result = await FuncRun.run_func(
                func_uuid, value, need_stdout=True)
            if result is not False:
                await self.cell_write(row, column_index, result)
        else:
            try:
                await self.insert_field_data(field, value, column_index, row, separator)
            except:
                format_error = traceback.format_exc()
                app_log.error(format_error)
                await runtime_log.error(format_error)

    async def excel_insert_image(self, img_url, sheet1, row, col):
        # r 和 d 分别表示在 runtime 和 design 目录下存放的图片
        image_data, width, height = b"", 27, 27
        if img_url[0] == "r" or img_url[0] == "d":
            object_stream = await self.oss_helper.get_oss_object(url=img_url)
            image_body = object_stream.body
            height, width, image_data = self.get_image_data(image_body)
        elif img_url[0] == "h":
            async with aiohttp.request("GET", img_url) as img:
                height, width, image_data = self.get_image_data(BytesIO(await img.read()))
        sheet1.insert_image(
            row, col, "temp.jpeg",
            {"image_data": image_data, "x_scale": 27 / width, "y_scale": 27 / height})


class RtableExport(TableExport):

    def __init__(self, datalist_obj, table_dict, sheet, write_excel) -> None:
        super().__init__(datalist_obj, table_dict, sheet, write_excel)
        self.rename_model = table_dict.get("rename_model")
        self.r_name = self.rename_model.alias
        self.model = table_dict.get("model")
        self.start_model = table_dict.get("start_model")

    def load_func(self, obj_func, data):
        data = ujson.loads(data)
        data.pop('0', None)
        data = list(map(obj_func, data.values()))
        return data

    def load_data_from_json_object(self, fields, data):
        field_list = calc_decimal_field_list(fields)
        for name, field in field_list:
            if name in data:
                value = data.get(name)
                if isinstance(value, (int, float)):
                    data[name] = field.load_json_value(value)
        bool_field_list = calc_bool_field_list(fields)
        for name, field in bool_field_list:
            if name in data:
                value = data.get(name)
                if isinstance(value, int):
                    data[name] = '是' if value else '否'
        return data

    def build_column_query(self, base_query):
        field_list = self.table_dict.get("field_list")
        field_list = list(set(field_list))
        model_object_args = []
        for field in field_list:
            model_object_args.append(field.column_name)
            model_object_args.append(getattr(self.rename_model, field.name))
        func = partial(self.load_data_from_json_object, model_object_args)
        # model_object_alias = peewee.fn.JSON_ARRAYAGG(
        #     peewee.fn.JSON_OBJECT(*model_object_args)
        # ).python_value(func).alias(self.r_name + "_obj")
        load_func = partial(self.load_func, func)
        start_node = lemon_model_node(self.start_model._meta.table_name)
        end_node = lemon_model_node(self.model._meta.table_name)
        path_list = self.table_dict.get("r_path")
        is_many, last_path, data_query = build_join_info(
            start_node, end_node, path=path_list,
            lemon_association=lemon_association,
            lemon_model_node=lemon_model_node,
            query=base_query, rename_model=True)
        # if last_path.is_middle:
        #     if last_path.r_uuid in SystemTable.UUIDS:
        #         middle_model = make_tenant_sys_table_copy(
        #             last_path.m_node.model)
        #         middle_model_uuid = middle_model._meta.table_name
        #     else:
        #         middle_model = last_path.m_node.model
        #         middle_model_uuid = last_path.r_uuid
        #     middle_model_path_uuid = build_model_field_path(
        #         middle_model_uuid, path_list)
        #     middle_model = middle_model.alias(middle_model_path_uuid)
        # else:
        #     middle_model = None
        # if middle_model:
        #     model_id = middle_model.id
        # else:
        model_id = self.rename_model.id
        base_query = data_query
        model_object_alias = peewee.fn.JSON_OBJECTAGG(
            peewee.fn.COALESCE(model_id, 0),
            peewee.fn.JSON_OBJECT(*model_object_args)).python_value(load_func).alias(self.r_name + "_obj")
        base_query = base_query.select_extend(*[model_object_alias])
        return base_query

    def get_number_list(self, data_list):
        data_list = list(filter(lambda x: isinstance(
            x, (float, int, Decimal)), data_list))
        return data_list

    def sum(self, data_list):
        data_list = self.get_number_list(data_list)
        if not data_list:
            return None
        return sum(data_list)

    def max(self, data_list):
        data_list = self.get_number_list(data_list)
        if not data_list:
            return None
        return max(data_list)

    def min(self, data_list):
        data_list = self.get_number_list(data_list)
        if not data_list:
            return None
        return min(data_list)

    def agg_avg_func(self, value_list):
        if value_list:
            data_list = self.get_number_list(value_list)
            total = sum(data_list)
            if not data_list:
                return None
            else:
                value = total/len(data_list)
        else:
            value = None
        return value

    def agg_first_func(self, value_list):
        value = value_list[0] if len(value_list) >= 1 else None
        return value

    def agg_concat(self, separator, value_list):
        return separator.join(map(str, value_list))

    def select_aggre_handle_func(self, calc_type, separator=","):
        handle_func = None
        if calc_type == AggreFunc.COUNT:
            handle_func = len
        elif calc_type == AggreFunc.SUM:
            handle_func = self.sum
        elif calc_type == AggreFunc.AVG:
            handle_func = self.agg_avg_func
        elif calc_type == AggreFunc.MAX:
            handle_func = self.max
        elif calc_type == AggreFunc.MIN:
            handle_func = self.min
        elif calc_type == AggreFunc.FIRST:
            handle_func = self.agg_first_func
        elif calc_type == AggreFunc.CONCAT:
            handle_func = partial(self.agg_concat, separator)
        return handle_func

    async def calc_aggre_field_by_func(self, func_uuid, value_list):
        func_wrapper = await FuncRun.get_func(func_uuid)
        arg_name_list = [func_arg.name for func_arg in func_wrapper.func.func_arg_list] \
            if func_wrapper.func.func_arg_list else []
        cloud_func_data = {}
        for p_index, arg_name in enumerate(arg_name_list):
            cloud_func_data.update(
                {arg_name: value_list}
            )
        if func_wrapper:
            stdout, cloud_func_return_data = await FuncRun.run(
                func_wrapper, lsm=self.datalist_obj.lsm, need_stdout=True, **cloud_func_data)
            return cloud_func_return_data
        else:
            return 0

    async def export_table_data(self, row_dict, row):
        obj_key = self.r_name + "_obj"
        obj_list = row_dict.get(obj_key, [])
        column_dict = self.table_dict.get("column_dict")
        column_length = len(column_dict.values())
        aggre_dict = defaultdict(list)
        aggre_column_list = self.table_dict.get("aggre_column_list")
        aggre_field_set = {column_info.get(
            "field").column_name for column_info in aggre_column_list}
        for index, obj_info in enumerate(obj_list):
            for field_name in aggre_field_set:
                field_value = obj_info.get(field_name)
                if field_value is not None:
                    value_list = aggre_dict[field_name]
                    value_list.append(field_value)
            if index >= column_length:
                continue
            column_list = column_dict[index]
            for column_info in column_list:
                field = column_info.get("field")
                field_value = obj_info.get(field.column_name)
                if is_system_field(field):
                    if field.column_name == SystemField.STATUS.get("field_name"):
                        field_value = UserTableStatus.status_code2_desc.get(field_value, "")
                await self.insert_cell_data(column_info, row, field_value)
        for column_info in aggre_column_list:
            field = column_info.get("field")
            value_list = aggre_dict[field.column_name]
            multi_field = column_info.get("multi_field")
            aggre_func = multi_field.get("aggre_func")
            separator = multi_field.get("separator")
            if aggre_func == AggreFunc.FUNC:
                func_uuid = multi_field.get("custom")
                out_value = await self.calc_aggre_field_by_func(func_uuid, value_list)
            else:
                handle_func = self.select_aggre_handle_func(
                    aggre_func, separator)
                if handle_func:
                    out_value = handle_func(value_list)
                else:
                    out_value = ""
            await self.insert_cell_data(column_info, row, out_value, separator)


class TreeTableExport(RtableExport):

    def __init__(self, datalist_obj, table_dict, sheet, write_excel) -> None:
        super().__init__(datalist_obj, table_dict, sheet, write_excel)
        self.field_name = None
        self.tree_dict = {}

    def build_column_query(self, base_query):
        path_list = self.table_dict.get("r_path")
        if path_list:
            id_field = self.rename_model.id
            # id_field = id_field.alias(self.r_name + "_pk")
            # r_path = self.table_dict.get("r_path")
            column = python_agg_field(id_field).alias(self.r_name + "_pk")
            # if r_path:
            #     r_path = r_path[:-1]
            base_query = base_query.select_extend(*[column])
            start_node = lemon_model_node(self.start_model._meta.table_name)
            end_node = lemon_model_node(self.model._meta.table_name)
            path_list = self.table_dict.get("r_path")
            is_many, last_path, data_query = build_join_info(
                start_node, end_node, path=path_list,
                lemon_association=lemon_association,
                lemon_model_node=lemon_model_node,
                query=base_query, rename_model=True)
            base_query = data_query
        return base_query

    async def get_tree_dict(self):
        data_source = self.table_dict.get("data_source")
        select_data = []
        self_referential_path, frontref = calc_self_referential_path_join(
            self.model, data_source=data_source)
        field_list = self.table_dict.get("field_list")
        field_list = list(set(field_list))
        extend_field_list = []
        name_keys = []
        for field in field_list:
            if is_system_field(field):
                model_uuid = field.model._meta.origin_table_name
                field_name = field.column_name
                field_alias = get_sys_field_uuid(model_uuid, field_name)
                extend_field_list.append(field.alias(field_alias))
                name_keys.append(field_alias)
            else:
                extend_field_list.append(field)
                name_keys.append(field.column_name)
        data_query = self.model.select(
            self.model.id,
            self.model.id.alias("value"),
            frontref.alias("pid"),
        )
        data_query = data_query.select_extend(*extend_field_list)
        tenant_uuid = self.datalist_obj.lsm.lemon.system.current_user.tenant_id
        query, cte = build_recursive_query(
            self.model, frontref, data_query, tenant_uuid,
            name_key=name_keys, need_value=True)
        data_list = await engine.userdb.objs.execute(query.dicts())
        # app_log.info(f"data_list: {list(data_list)}")
        data_list = process_multifloor_data(data_list, name_key=name_keys)
        # app_log.info(f"data_list: {data_list}")
        for floor_info in data_list:
            self.build_tree_dict(floor_info, [])
        app_log.info(self.tree_dict)
        return select_data

    def build_tree_dict(self, floor_info, parent_list):
        new_floor_info = {}
        children = floor_info.pop("children", [])
        new_floor_info.update(floor_info)
        new_floor_info["parent_list"] = parent_list
        new_parent_list = parent_list[::]
        new_parent_list.append(floor_info)
        pk = new_floor_info.get("value")
        self.tree_dict[pk] = new_floor_info
        for child in children:
            self.build_tree_dict(child, new_parent_list)

    async def export_table_data(self, row_dict, row):
        path_list = self.table_dict.get("r_path")
        if path_list:
            obj_key = self.r_name + "_pk"
            pk_list = row_dict.get(obj_key, [])
        else:
            pk_list = [row_dict.get("id")]
        column_dict = self.table_dict.get("column_dict")
        column_length = len(column_dict.values())
        aggre_dict = defaultdict(list)
        aggre_column_list = self.table_dict.get("aggre_column_list", [])
        aggre_field_set = {column_info.get(
            "field").column_name for column_info in aggre_column_list}
        for index, pk in enumerate(pk_list):
            for field_name in aggre_field_set:
                field_value = self.get_path_name(field_name, pk)
                if field_value is not None:
                    value_list = aggre_dict[field_name]
                    value_list.append(field_value)
            if index >= column_length:
                continue
            column_list = column_dict[index]
            for column_info in column_list:
                field = column_info.get("field")
                should_translate = False
                if is_system_field(field):
                    model_uuid = field.model._meta.origin_table_name
                    field_name = field.column_name
                    name_key = get_sys_field_uuid(model_uuid, field_name)
                    if field_name == SystemField.STATUS.get("field_name"):
                        should_translate = True
                else:
                    name_key = field.column_name
                field_value = self.get_path_name(name_key, pk, should_translate=should_translate)
                await self.insert_cell_data(column_info, row, field_value)
        for column_info in aggre_column_list:
            field = column_info.get("field")
            value_list = aggre_dict[field.column_name]
            multi_field = column_info.get("multi_field")
            aggre_func = multi_field.get("aggre_func")
            separator = multi_field.get("separator")
            if aggre_func == AggreFunc.FUNC:
                func_uuid = multi_field.get("custom")
                out_value = await self.calc_aggre_field_by_func(func_uuid, value_list)
            else:
                handle_func = self.select_aggre_handle_func(
                    aggre_func, separator)
                if handle_func:
                    out_value = handle_func(value_list)
                else:
                    out_value = ""
            await self.insert_cell_data(column_info, row, out_value, separator)

    def get_path_name(self, name_key, pk, should_translate=False):
        # should_translate 代表系统字段 审批流状态 需要数字转中文
        floor_info = self.tree_dict.get(pk)
        parent_list = floor_info.get("parent_list", [])
        name_list = []
        for parent in parent_list:
            if should_translate:
                name_list.append(UserTableStatus.status_code2_desc.get(str(parent.get(name_key, "")), ""))
            else:
                name_list.append(str(parent.get(name_key, "")))
        if should_translate:
            name_list.append(UserTableStatus.status_code2_desc.get(str(floor_info.get(name_key, "")), ""))
        else:
            name_list.append(str(floor_info.get(name_key, "")))
        return "/".join(name_list)


class RuntimeExcel(object):
    zero_datetime = datetime.datetime.strptime("1970", "%Y")
    insert_error = "写入数据库失败"
    oss_helper = RuntimeOssHelper()

    def __init__(self, datalist_obj) -> None:
        self.data_model = None
        self.datalist_obj = datalist_obj
        self.lsm = datalist_obj.lsm
        self.page_machine_id = datalist_obj.page_machine_id
        self.machine_id = datalist_obj.machine_id
        self.error_dict = {}
        self.submit_row_list = []
        self.template_detail = None
        self.sheet = None
        self.image_dict = None
        self.page_size = 2000

    def get_error_info(self):
        repeating_dict = self.error_dict.pop(-1, {})
        repeating_list = repeating_dict.pop("error_list", [])
        error_list = list(
            sorted(self.error_dict.values(), key=itemgetter("row")))
        success = len(self.submit_row_list)
        fail = len(error_list)
        error_info = {"error_list": error_list,
                      "success": success, "fail": fail, "repeating_list": repeating_list}
        return error_info

    async def handle_insert_table(self, sheet, insert_table_info,
                                  row, image_dict, unique_value, data_func):
        for table_info in insert_table_info.values():
            column_dict = table_info.get("column_dict")
            data_dict = table_info.get("data_dict")
            # 同一字段一行可能有多列，代表多条数据
            for i, columns in column_dict.items():
                insert_row_info = {}
                for column in columns:
                    field = column.get("field")
                    value, status = await self.get_value_by_column_info(
                        sheet, column, row, image_dict)
                    if status:
                        insert_row_info[field.name] = value
                if not table_info:
                    continue
                if table_info.get("unique"):
                    ref_unique_column = table_info["unique"].get("field").name
                    insert_unique_value = insert_row_info.get(
                        ref_unique_column)
                else:
                    insert_unique_value = ":".join([str(row), str(i)])
                if insert_row_info:
                    data_func(data_dict, insert_row_info,
                              insert_unique_value,  row, i, unique_value)

    async def insert_back_ref_data(self):
        r_dict = self.template_detail.get("r_dict")
        table_dict = r_dict.get("table")
        table_unique_dict = table_dict.get("unique_dict")
        back_insert_dict = r_dict.get("back_insert_dict")
        # 插入外键在其它模型上的数据
        for table_info in back_insert_dict.values():
            data_dict = table_info.get("data_dict")
            front_ref = table_info.get("front_ref")
            model = table_info.get("model")
            front_ref_column = front_ref.name
            preserve = table_info.get("preserve")
            data_list = sorted(data_dict.values(), key=itemgetter("row"))
            for row in range(0, len(data_list), 100):
                row_left = row
                row_right = row + 100 if row + \
                    100 < len(data_list) else len(data_list)
                row_dict_list = []
                insert_data_list = []
                for row_dict in data_list[row_left: row_right]:
                    row_info = row_dict.get("row_info")
                    row_unique = row_dict.get("row_unique")
                    # 获取关联数据所在行对应pk,pk在上一步本表数据插入后更新
                    table_row_dict = table_unique_dict.get(row_unique, {})
                    pk = table_row_dict.get("pk")
                    row_dict["row_pk"] = pk
                    # 只有主表插入成功才能插入关联表
                    if pk:
                        row_info[front_ref_column] = pk
                        insert_data_list.append(row_info)
                        row_dict_list.append(row_dict)
                if insert_data_list:
                    query = model.insert_many(
                        insert_data_list).on_conflict(preserve=preserve)
                    async with engine.userdb.objs.atomic():
                        ans = await engine.userdb.objs.execute(
                            query, notify=False)

    async def import_excel(self, open_excel: openpyxl, template_uuid):
        self.datalist_obj.lsm.lemon.system.excel_info = dict()
        template_detail = await self.handle_excel_template(template_uuid)
        result = {}
        check_result, message = self.check_import_excel(
            open_excel, template_detail)
        if not check_result:
            data_import = {
                "code": -1,
                "message": message
            }
            result.update({"message_info": data_import})
            return result
        sheet_name = template_detail.get("sheet_name")
        sheet1 = open_excel[sheet_name]
        title = ""
        for data in sheet1[1]:
            title = data.value
            if title is not None:
                break
        num_of_row = min(50010, sheet1.max_row)
        model_uuid = template_detail.get("model_uuid")
        self.data_model = lemon_model(model_uuid)
        r_dict = template_detail.get("r_dict")
        title_row = template_detail.get("title_row")
        table_dict = r_dict.get("table")
        front_scan_dict = r_dict.get("front_scan_dict")
        front_insert_dict = r_dict.get("front_insert_dict")
        back_scan_dict = r_dict.get("back_scan_dict")
        back_insert_dict = r_dict.get("back_insert_dict")
        tree_ref_dict = r_dict.get("tree_ref_dict")
        middle_scan_dict = r_dict.get("middle_scan_dict")
        middle_insert_dict = r_dict.get("middle_insert_dict")
        middle_tree_dict = r_dict.get("middle_tree_dict")
        front_tree_ref_dict = r_dict.get("front_tree_ref_dict")
        stack_list = r_dict.get("stack_list")
        stack_list_before = r_dict.get("stack_list_before")
        image_dict = self.get_image_dict(sheet1)
        self.sheet = sheet1
        self.image_dict = image_dict
        excel_table_obj = ExcelTable(self, table_dict)
        backref_scan_obj = BackRefScanClass(
            self, back_scan_dict, excel_table_obj)
        frontref_scan_obj = FrontScanClass(
            self, front_scan_dict, excel_table_obj)
        middleref_scan_obj = MiddleRefScanClass(
            self, middle_scan_dict, excel_table_obj)
        backref_insert_obj = BackRefInsertClass(
            self, back_insert_dict, excel_table_obj)
        frontref_insert_obj = FrontRefInsertClass(
            self, front_insert_dict, excel_table_obj)
        middleref_insert_obj = MiddleRefInsertClass(
            self, middle_insert_dict, excel_table_obj)
        middle_tree_obj = MiddleTreeClass(
            self, middle_tree_dict, excel_table_obj)
        tree_table_obj = TreeRefObjClass(self, tree_ref_dict)
        front_tree_table_obj = FrontTreeRefObjClass(self, front_tree_ref_dict)
        for stack_obj in stack_list:
            stack_obj.stack_reset(self, excel_table_obj)
        for stack_obj in stack_list_before:
            stack_obj.stack_reset(self, excel_table_obj)
        for row in range(title_row + 1, num_of_row + 1):
            row_dict = await excel_table_obj.collect_row_data(row)
            if row_dict:
                await frontref_scan_obj.collect_row_data(row, row_dict)
                await backref_scan_obj.collect_row_data(row, row_dict)
                await middleref_scan_obj.collect_row_data(row, row_dict)
                await middle_tree_obj.collect_row_data(row, row_dict)
                await frontref_insert_obj.collect_row_data(row, row_dict)
                await backref_insert_obj.collect_row_data(row, row_dict)
                await middleref_insert_obj.collect_row_data(row, row_dict)
                await tree_table_obj.collect_row_data(row, row_dict)
                await front_tree_table_obj.collect_row_data(row, row_dict)
                for stack_obj in stack_list:
                    await stack_obj.collect_row_data(row, row_dict)
                for stack_obj in stack_list_before:
                    await stack_obj.collect_row_data(row, row_dict)
        for stack_obj in stack_list_before:
            await stack_obj.stack_save()
        await frontref_insert_obj.save()
        self.submit_row_list = await excel_table_obj.save(tree_table_obj)
        for stack_obj in stack_list:
            await stack_obj.stack_save()
        await backref_scan_obj.update_relation()
        await middleref_scan_obj.save()
        await middle_tree_obj.update_relation()
        await backref_insert_obj.save()
        await middleref_insert_obj.save()
        result["submit_row_list"] = self.submit_row_list
        return result

    async def export_row_date(self, table_export_obj, r_table_list, row_info, row):
        await table_export_obj.export_table_data(row_info, row)
        for r_table_export_obj in r_table_list:
            await r_table_export_obj.export_table_data(row_info, row)

    async def insert_page_data(self, base_query, title_row, page_number,
                               table_export_obj, r_table_list):
        data_query = base_query.paginate(page_number, self.page_size).dicts()
        data_list = await engine.userdb.objs.execute(data_query)
        # func_list = list()
        for index, row_info in enumerate(data_list, start=title_row+1):
            row = (page_number - 1) * self.page_size + index
            await self.export_row_date(
                table_export_obj, r_table_list, row_info, row)
        #     func_list.append(self.export_row_date(
        #         table_export_obj, r_table_list, row_info, row))
        #     if index % 10 == 0 and index != 0:
        #         await asyncio.gather(*func_list)
        #         func_list = list()
        # if func_list:
        #     await asyncio.gather(*func_list)

    async def export_excel(self, template_uuid, base_query):
        export_excel_data = {
            "code": 0,
            "message": ""
        }
        try:
            template_detail = await self.handle_export_excel_template(template_uuid)
            model_uuid = template_detail.get("model_uuid")
            self.data_model = lemon_model(model_uuid)
            r_table_list = []
            r_dict = template_detail.get("r_dict")
            table_dict = r_dict.get("table")
            r_table_dict = r_dict.get("r_table")
            tree_dict = r_dict.get("tree_dict")
            write_excel, sheet, file_path, file_name = await self.export_template(template_uuid,
                                                                                  template_detail)
            if base_query is None:
                base_query = self.data_model.select(self.data_model.id)
            else:
                base_query = base_query.select(self.data_model.id)
            table_export_obj = TableExport(
                self.datalist_obj, table_dict, sheet, write_excel)
            base_query = table_export_obj.build_column_query(base_query)
            for r_table in r_table_dict.values():
                r_table_export_obj = RtableExport(
                    self.datalist_obj, r_table, sheet, write_excel)
                base_query = r_table_export_obj.build_column_query(base_query)
                r_table_list.append(r_table_export_obj)
            for r_table in tree_dict.values():
                r_table_export_obj = TreeTableExport(
                    self.datalist_obj, r_table, sheet, write_excel)
                base_query = r_table_export_obj.build_column_query(base_query)
                await r_table_export_obj.get_tree_dict()
                r_table_list.append(r_table_export_obj)
            title_row = template_detail.get("title_row")
            count_query = base_query.clone()
            count_query._returning = [peewee.fn.Count(peewee.SQL('*'))]
            total_number = await engine.userdb.objs.count(count_query)
            # func_list = list()
            for page_number in range(int(total_number/self.page_size) + 1):
                page_number += 1
                # func_list.append(self.insert_page_data(base_query, title_row, page_number,
                #                                        table_export_obj, r_table_list))
                await self.insert_page_data(
                    base_query, title_row, page_number,
                    table_export_obj, r_table_list)
            #     if page_number % 5 == 0:
            #         await asyncio.gather(*func_list)
            #         func_list = []
            # if func_list:
            #     await asyncio.gather(*func_list)

            write_excel.close()
            url = self.oss_helper.gen_oss_tmp_path(
                middle_user_id=engine.config.MIDDLE_USER_UUID,
                app_uuid=self.lsm.lemon.system.current_user.app_uuid,
                tenant_uuid=self.lsm.lemon.system.current_user.tenant_id,
                page_uuid=self.page_machine_id,
                control_uuid=self.machine_id) + "/" + file_name
            await self.oss_helper.put_oss_object(
                url, file_path, local_file=True)
            export_excel_data.update({
                "data": {
                    "url": url
                }
            })
        except:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            export_excel_data = {
                "code": -1,
                "message": "导出失败"
            }
        return export_excel_data

    def get_image_dict(self, sheet):
        image_dict = {}
        for _image in sheet._images:
            image_col = _image.anchor._from.col
            image_row = _image.anchor._from.row
            image_body = _image.ref.getvalue()
            # 导入时默认行列下标从1开始
            cell_place = ":".join([str(image_row + 1), str(image_col + 1)])
            image_dict[cell_place] = image_body
        return image_dict

    def update_column_dict(self, table_info, column_index, column_info):
        column_index_list = column_index.split(",")
        column_dict = table_info.get("column_dict")
        for i, index_value in enumerate(column_index_list):
            columns = column_dict.setdefault(i, [])
            column_detail = {"index": self.column_to_int(index_value)}
            column_detail.update(column_info)
            columns.append(column_detail)

    async def handle_multi_relation(self, relation_setting, model_uuid, table_dict):

        model_path_list = relation_setting.get("path")
        model_list = relation_setting.get("model_list")
        object_list = []
        column_list = []
        for index, model_info in enumerate(model_list):
            model_path = model_path_list[index]
            field_model_node = lemon_model_node(model_uuid)
            node_dict = {
                node.r_uuid: node for node in field_model_node.relation_nodes}
            columns = model_info.get("field_list")
            relation_node = node_dict.get(model_path)
            is_middle = relation_node.is_middle
            frontref = relation_node.frontref
            backref = relation_node.backref
            front_triggle_model = frontref.model
            is_front = front_triggle_model._meta.table_name == model_uuid
            data_scan = model_info.get("relation_data_method")
            if data_scan == 1:
                table_info = {"front_ref": frontref, "data_dict": {}, "unique_data": {},
                              "column_dict": {}, "preserve": [], "unique_columns": []}
                # TODO is_required要取关联上的
                if is_middle:
                    start_model = frontref.rel_field.model._meta.table_name
                    is_front = start_model == model_uuid
                    # 保证front_ref一定是本模型的
                    if not is_front:
                        swp_ref = frontref
                        frontref = backref
                        backref = swp_ref
                    talbe_name = frontref.model._meta.table_name
                    table_info.update({"front_ref": frontref, "back_ref": backref,
                                       "is_front": is_front, "middle_dict": {}, "model": lemon_model(talbe_name)})
                    # middle_scan_dict[r_path] = table_info
                    table_obj = MiddleRefScanStack(None, table_info, None)
                    object_list.append(table_obj)
                else:
                    if is_front:
                        talbe_name = frontref.model._meta.table_name
                        table_info["model"] = lemon_model(talbe_name)
                        # front_scan_dict[r_path] = table_info
                        self.update_preserve_columns(
                            table_dict, frontref)
                        table_obj = FrontScanStack(None, table_info, None)
                        object_list.append(table_obj)
                    else:
                        # 主表插入前查询，记录数据,主表插入后更新关联表关联关系
                        talbe_name = backref.model._meta.table_name
                        table_info.update({"front_ref": frontref,
                                           "back_ref": backref,
                                           "is_front": is_front,
                                           "update_dict": {},
                                           "model": lemon_model(talbe_name)})
                        table_obj = BackRefScanStack(None, table_info, None)
                        object_list.append(table_obj)
            else:
                table_info = {"column_dict": {},
                              "front_ref": frontref,
                              "back_ref": backref, "unique_data": {},
                              "data_dict": {}, "preserve": [], "unique_columns": []
                              }
                if is_middle:
                    # 主表插入后插入，同时插入中间表数据
                    start_model = frontref.rel_field.model._meta.table_name
                    is_front = start_model == model_uuid
                    if not is_front:
                        swp_ref = frontref
                        frontref = backref
                        backref = swp_ref
                    talbe_name = backref.rel_field.model._meta.table_name
                    table_info.update(
                        {"front_ref": frontref,
                            "back_ref": backref,
                            "is_front": is_front,
                            "model": lemon_model(talbe_name)})
                    table_obj = MiddleRefInsertStack(None, table_info, None)
                    object_list.append(table_obj)
                else:
                    # 主表插入前插入，同时更新主表关联
                    if is_front:
                        table_info["model"] = backref.model
                        self.update_preserve_columns(
                            table_dict, frontref)
                        table_obj = FrontRefInsertStack(None, table_info, None)
                        object_list.append(table_obj)
                    # 主表插入后插入
                    else:
                        table_info["model"] = frontref.model
                        self.update_preserve_columns(
                            table_info, frontref)
                        table_obj = BackRefInsertStack(None, table_info, None)
                        object_list.append(table_obj)
            for column in columns:
                field_info = column.get("field_info")
                path = field_info.get("path", [])
                field_uuid = field_info.get("uuid")
                field_obj = lemon_field(field_uuid)
                # 如果模板上没有设置唯一字段，但是模型上是唯一的也被认为设置了唯一字段
                if field_obj.is_unique:
                    column["unique_key"] = True
                column_index = column.get("column_index")
                column_title = column.get("column_title")
                field_import_method = column.get("field_import_method")
                is_required = field_obj.is_required
                separator = column.get("import_separator")
                column_info = {"field": field_obj,  "path": path, "column_title": column_title,
                               "field_import_method": field_import_method,
                               "is_required": is_required, "separator": separator}
                if field_import_method == 2:
                    excel_func = column.get("excel_func")
                    column_info["excel_func"] = excel_func
                elif field_import_method == 1:
                    excel_function = column.get("excel_function")
                    column_info["excel_function"] = excel_function
                r_path = column.get("path")
                r_path = field_info.get("path")
                if r_path:
                    r_path = r_path[0]
                front_triggle_model = frontref.model
                is_front = front_triggle_model._meta.table_name == model_uuid
                # 仅查询
                if data_scan == 1:
                    # TODO is_required要取关联上的
                    column_info.update({"is_required": False})
                    # if column.get("unique_key"):
                    data_dict = table_info["data_dict"]
                    data_dict[field_obj.name] = {}
                    self.update_column_dict(
                        table_info, column_index, column_info)
                    column_info.update({"index": self.column_to_int(
                                        column_index), "is_required": False})
                    # table_info.update({"column": column_info})
                    table_info["unique_columns"].append(column_info)

                else:
                    if column.get("unique_key"):
                        # table_info["unique"] = column_info
                        table_info["unique_columns"].append(column_info)
                    self.update_preserve_columns(table_info, field_obj)
                    self.update_column_dict(
                        table_info, column_index, column_info)
            column_dict = table_info.get("column_dict")
            for columns in column_dict.values():
                column_list.extend(columns)
            model_uuid = model_info.get("model_uuid")
            table_dict = table_info
        last_table = object_list[0]
        for table_obj in object_list[1:]:
            last_table.set_r_table_obj(table_obj)
            last_table = table_obj
        return object_list[0], column_list

    async def handle_excel_template(self, template_uuid):
        template_detail = {}
        template_info = await engine.access.select_excel_template(template_uuid)
        if template_info:
            table_columns = []
            table_data_dict = {}
            front_scan_dict = {}
            front_insert_dict = {}
            back_insert_dict = {}
            back_scan_dict = {}
            tree_ref_dict = {}
            front_tree_ref_dict = {}
            middle_tree_dict = {}
            middle_insert_dict = {}
            middle_scan_dict = {}
            insert_dict = {}
            scan_dict = {}
            stack_list = []
            stack_list_before = []
            template_content = template_info.get("template")
            sheet_name = template_content.get("sheet_name")
            model_uuid = template_content.get("model")
            table_dict = {"columns": table_columns, "unique_dict": {}, "data_dict": table_data_dict,
                          "model": lemon_model(model_uuid), "preserve": [], "unique_columns": []}
            columns = template_content.get("columns", [])
            """
            table:本表，front_scan_dict:外键在本表仅查询，back_insert_dict：外键在关联表仅插入 tree_ref_dict: 自关联，
            back_scan_dict 外键在关联表仅查询，front_insert_dict:外键在本表仅插入
            """
            stack_column_list = []
            r_dict = {"table": table_dict, "front_scan_dict": front_scan_dict,
                      "back_insert_dict": back_insert_dict, "tree_ref_dict": tree_ref_dict,
                      "front_tree_ref_dict": front_tree_ref_dict,
                      "middle_tree_dict": middle_tree_dict,
                      "middle_insert_dict": middle_insert_dict,
                      "middle_scan_dict": middle_scan_dict,
                      "front_insert_dict": front_insert_dict, "back_scan_dict": back_scan_dict,
                      "stack_list": stack_list,
                      "stack_list_before": stack_list_before,
                      "stack_column_list": stack_column_list
                      }
            field_model_node = lemon_model_node(model_uuid)
            node_dict = {
                node.r_uuid: node for node in field_model_node.relation_nodes}
            for column in columns:
                table_type = column.get("relation_type")
                if table_type == 3:
                    stack_obj, column_list = await self.handle_multi_relation(column, model_uuid, table_dict)
                    stack_column_list.extend(column_list)
                    if isinstance(stack_obj, (BackRefScanStack, BackRefInsertStack,
                                              MiddleRefScanStack, MiddleRefInsertStack)):
                        stack_list.append(stack_obj)
                    elif isinstance(stack_obj, (FrontRefInsertStack, FrontScanStack)):
                        stack_list_before.append(stack_obj)
                else:
                    field_info = column.get("field_info")
                    path = field_info.get("path", [])
                    field_uuid = field_info.get("uuid")
                    field_obj = lemon_field(field_uuid)
                    # 如果模板上没有设置唯一字段，但是模型上是唯一的也被认为设置了唯一字段
                    if field_obj.is_unique:
                        column["unique_key"] = True
                    column_index = column.get("column_index")
                    column_title = column.get("column_title")
                    field_import_method = column.get("field_import_method")
                    is_required = field_obj.is_required
                    separator = column.get("import_separator")
                    pass_support = column.get("pass")
                    column_info = {"field": field_obj,  "path": path, "column_title": column_title,
                                   "field_import_method": field_import_method,
                                   "is_required": is_required, "separator": separator, "pass_support": pass_support}
                    if field_import_method == 2:
                        excel_func = column.get("excel_func")
                        column_info["excel_func"] = excel_func
                    elif field_import_method == 1:
                        excel_function = column.get("excel_function")
                        column_info["excel_function"] = excel_function
                    if table_type == 1:
                        column_info["index"] = self.column_to_int(column_index)
                        table_columns.append(column_info)
                        self.update_preserve_columns(table_dict, field_obj)
                        # 记录本表唯一的字段, 如果没设置默认为行号
                        if column.get("unique_key"):
                            table_dict["unique_columns"].append(column_info)
                    elif table_type == 0:
                        r_path = column.get("path")
                        r_path = field_info.get("path")
                        if r_path:
                            r_path = r_path[0]
                        data_scan = column.get("relation_data_method")
                        relation_node = node_dict.get(r_path)

                        is_middle = relation_node.is_middle
                        frontref = relation_node.frontref
                        backref = relation_node.backref
                        front_triggle_model = frontref.model
                        is_front = front_triggle_model._meta.table_name == model_uuid
                        # 仅查询
                        if data_scan == 1:
                            relation_info = app_relationship.get(r_path)
                            if not relation_info:
                                if relation_node.self_referential:
                                    real_uuid = frontref.column_name
                                    relation_info = app_relationship.get(real_uuid)
                                    source_required = relation_info.get(
                                    "source_required")
                                    target_required = relation_info.get(
                                    "target_required")
                                    is_source = relation_node.is_source
                                    is_required = target_required if is_source else source_required
                                else:
                                    is_required = False
                            else:
                                source_required = relation_info.get(
                                    "source_required")
                                target_required = relation_info.get(
                                    "target_required")
                                source_model = relation_info.get("source_model")
                                is_required = target_required if source_model == model_uuid else source_required
                            table_info = scan_dict.setdefault(r_path,  {"front_ref": frontref,
                                                                        "data_dict": {}, "unique_columns": [],
                                                                        "unique_data": {},
                                                                        "column_dict": {}})
                            data_dict = table_info["data_dict"]
                            unique_columns = table_info["unique_columns"]
                            unique_columns.append(column_info)
                            column_info.update(
                                {"is_required": is_required})
                            data_dict[field_obj.name] = {}
                            self.update_column_dict(
                                table_info, column_index, column_info)
                            # 主表插入前查询，记录数据,主表插入后插入中间表数据
                            if is_middle:
                                start_model = frontref.rel_field.model._meta.table_name
                                is_front = start_model == model_uuid
                                # 保证front_ref一定是本模型的
                                if not is_front:
                                    swp_ref = frontref
                                    frontref = backref
                                    backref = swp_ref
                                table_info.update({"front_ref": frontref, "back_ref": backref,
                                                   "is_front": is_front, "middle_dict": {}})
                                middle_scan_dict[r_path] = table_info
                            else:
                                if is_front:
                                    column_info.update({"index": self.column_to_int(
                                        column_index)})
                                    # 主表插入前查询，将pk更新到主表数据中，随主表数据的插入而更新
                                    table_info["model"] = frontref.model
                                    front_scan_dict[r_path] = table_info
                                    self.update_preserve_columns(
                                        table_dict, frontref)
                                else:
                                    # 主表插入前查询，记录数据,主表插入后更新关联表关联关系
                                    table_info.update({"front_ref": frontref,
                                                       "back_ref": backref,
                                                       "is_front": is_front,
                                                       "update_dict": {},
                                                       "model": backref.model})
                                    back_scan_dict[r_path] = table_info
                        else:
                            table_info = insert_dict.setdefault(
                                r_path,
                                {"column_dict": {},
                                    "front_ref": frontref,
                                    "back_ref": backref,
                                    "data_dict": {}, "preserve": [],
                                    "unique_columns": [], "unique_data": {}
                                 })
                            # table_info["preserve"] = self.get_preserve_columns(field_obj)
                            if column.get("unique_key"):
                                unique_columns = table_info["unique_columns"]
                                unique_columns.append(column_info)
                            self.update_preserve_columns(table_info, field_obj)
                            self.update_column_dict(
                                table_info, column_index, column_info)

                            if is_middle:
                                # 主表插入后插入，同时插入中间表数据
                                start_model = frontref.rel_field.model._meta.table_name
                                is_front = start_model == model_uuid
                                if not is_front:
                                    swp_ref = frontref
                                    frontref = backref
                                    backref = swp_ref
                                table_info.update(
                                    {"front_ref": frontref,
                                     "back_ref": backref,
                                     "is_front": is_front,
                                     "model": backref.rel_field.model})
                                middle_insert_dict[r_path] = table_info
                            else:
                                # 主表插入前插入，同时更新主表关联
                                if is_front:
                                    talbe_name = backref.model._meta.table_name
                                    table_info["model"] = lemon_model(talbe_name)
                                    front_insert_dict[r_path] = table_info
                                    self.update_preserve_columns(
                                        table_dict, frontref)
                                # 主表插入后插入
                                else:
                                    talbe_name = frontref.model._meta.table_name
                                    table_info["model"] = lemon_model(talbe_name)
                                    self.update_preserve_columns(
                                        table_info, frontref)
                                    back_insert_dict[r_path] = table_info
                    elif table_type == 2:
                        r_path = column.get("path")
                        r_path = field_info.get("path")
                        data_source = column.get("data_source")
                        if r_path:
                            r_path = r_path[0]
                        referential_path = data_source.get(
                            "association", {}).get("path", [])
                        # 兼容之前只用选字段的设置
                        if not referential_path:
                            relation_node = node_dict.get(r_path)
                            frontref = relation_node.frontref
                            talbe_name = frontref.model._meta.table_name
                            table_data_dict[field_obj.name] = {}
                            column_info.update({
                                "index": self.column_to_int(column_index),
                                "is_required": False})
                            self.update_preserve_columns(table_dict, frontref)
                            tree_ref_dict[r_path] = {
                                "front_ref": frontref, "column": column_info,
                                "data_dict": {}, "parent_dict": {},
                                "model": lemon_model(talbe_name), "parent_pk_dict": {},
                                "tree_front_ref": frontref, "unique_data": {}}
                        else:
                            tree_path = referential_path[0]
                            # 关联表
                            if r_path:
                                relation_node = node_dict.get(r_path)
                                data_source = column.get("data_source")
                                frontref = relation_node.frontref
                                front_triggle_model = frontref.model
                                is_middle = relation_node.is_middle
                                is_front = front_triggle_model._meta.table_name == model_uuid
                                if is_middle:
                                    start_model = frontref.rel_field.model._meta.table_name
                                    is_front = start_model == model_uuid
                                    backref = relation_node.backref
                                    # 保证front_ref一定是本模型的
                                    if not is_front:
                                        swp_ref = frontref
                                        frontref = backref
                                        backref = swp_ref
                                    r_model = backref.rel_field.model
                                    r_model_uuid = r_model._meta.table_name
                                    r_model_node = lemon_model_node(
                                        r_model_uuid)
                                    r_node_dict = {node.r_uuid: node for node in
                                                   r_model_node.relation_nodes}
                                    tree_relation_node = r_node_dict.get(
                                        tree_path)
                                    tree_front_ref = tree_relation_node.frontref
                                    column_info.update({
                                        "is_required": False})
                                    talbe_name = frontref.model._meta.table_name
                                    tree_table_info = {
                                        "front_ref": frontref, "back_ref": backref,
                                        "is_front": is_front, "middle_dict": {},
                                        "tree_front_ref": tree_front_ref,
                                        "parent_pk_dict": {},
                                        "parent_dict": {},
                                        "column": column_info,
                                        "model": lemon_model(talbe_name),
                                        "column_dict": {}, "unique_data": {}
                                    }
                                    self.update_column_dict(
                                        tree_table_info, column_index, column_info)
                                    middle_tree_dict[r_path] = tree_table_info
                                elif is_front:
                                    backref = relation_node.backref
                                    r_model = backref.model
                                    r_model_uuid = r_model._meta.table_name
                                    r_model_node = lemon_model_node(
                                        r_model_uuid)
                                    r_node_dict = {node.r_uuid: node for node in
                                                   r_model_node.relation_nodes}
                                    tree_relation_node = r_node_dict.get(
                                        tree_path)
                                    tree_front_ref = tree_relation_node.frontref
                                    column_info.update(
                                        {"index": self.column_to_int(column_index),
                                         "is_required": False})
                                    self.update_preserve_columns(
                                        table_dict, frontref)
                                    talbe_name = frontref.model._meta.table_name
                                    front_tree_ref_dict[r_path] = {
                                        "front_ref": frontref,
                                        "column": column_info,
                                        "data_dict": {}, "parent_dict": {},
                                        "model": lemon_model(talbe_name),
                                        "parent_pk_dict": {},
                                        "tree_front_ref": tree_front_ref,
                                        "unique_data": {}}

                            else:
                                relation_node = node_dict.get(tree_path)
                                frontref = relation_node.frontref
                                table_data_dict[field_obj.name] = {}
                                column_info.update({"index": self.column_to_int(column_index),
                                                    "is_required": False})
                                self.update_preserve_columns(
                                    table_dict, frontref)
                                talbe_name = frontref.model._meta.table_name
                                tree_ref_dict[tree_path] = {"front_ref": frontref, "column": column_info,
                                                         "data_dict": {}, "parent_dict": {},
                                                         "model": lemon_model(talbe_name), "parent_pk_dict": {},
                                                         "tree_front_ref": frontref,
                                                         "unique_data": {}}

            # 这里不管是行号和列号用户设置的都是从1开始
            title_row = template_content.get("title_row", 1)
            name = template_info.get("template_name")
            template_detail.update({"model_uuid": model_uuid, "r_dict": r_dict, "sheet_name": sheet_name,
                                    "title_row": title_row,
                                    "name": name})
        return template_detail

    async def handle_export_excel_template(self, template_uuid):
        template_detail = {}
        template_info = await engine.access.select_excel_template(template_uuid)
        if template_info:
            table_columns = []
            table_data_dict = {}
            tree_ref_dict = {}
            r_table_dict = {}
            tree_dict = {}
            template_content = template_info.get("template")
            sheet_name = template_content.get("sheet_name")
            model_uuid = template_content.get("model")
            data_model = lemon_model(model_uuid)
            table_field_list = []
            table_dict = {"columns": table_columns, "unique_dict": {}, "data_dict": table_data_dict,
                          "model": data_model, "field_list": table_field_list}
            columns = template_content.get("columns", [])
            # table:本表，front_scan_dict:外键在本表仅查询，back_insert_dict：外键在关联表仅插入 tree_ref_dict: 自关联， back_scan_dict 外键在关联表仅查询， front_insert_dict:外键在本表仅插入
            r_dict = {"table": table_dict,
                      "r_table": r_table_dict, "tree_dict": tree_dict}
            field_model_node = lemon_model_node(model_uuid)
            for column in columns:
                table_type = column.get("relation_type")
                field_info = column.get("field_info")
                path = field_info.get("path", [])
                field_uuid = field_info.get("uuid")
                field_obj = lemon_field(field_uuid)
                column_index = column.get("column_index")
                column_title = column.get("column_title")
                field_import_method = column.get("field_import_method")
                is_required = field_obj.is_required
                column_info = {"field": field_obj,  "path": path, "column_title": column_title,
                               "field_import_method": field_import_method,
                               "is_required": is_required}
                if field_import_method == 2:
                    excel_func = column.get("excel_func")
                    column_info["excel_func"] = excel_func
                elif field_import_method == 1:
                    excel_function = column.get("excel_function")
                    column_info["excel_function"] = excel_function
                if table_type == 1:
                    column_info["index"] = self.column_to_int(column_index)
                    table_columns.append(column_info)
                    table_field_list.append(field_obj)
                elif table_type in [0, 2]:
                    r_path = field_info.get("path", [])
                    model = field_obj.model
                    model_name = model._meta.table_name
                    r_name = build_model_field_path(model_name, r_path)
                    column_index_list = column_index.split(",")
                    # table_info["preserve"] = self.get_preserve_columns(field_obj)
                    rename_model = model.alias(r_name)
                    if table_type == 0:
                        r_table_info = r_table_dict
                        table_info = r_table_info.setdefault(r_name,
                                                             {"column_dict": {}, "field_list": [],
                                                              "r_path": r_path, "model": model,
                                                              "rename_model": rename_model,
                                                              "start_model": data_model, "aggre_column_list": []})
                    elif table_type == 2:
                        r_table_info = tree_dict
                        data_source = column.get("data_source")
                        table_info = r_table_info.setdefault(r_name,
                                                             {"column_dict": {}, "field_list": [],
                                                              "r_path": r_path, "model": model,
                                                              "rename_model": rename_model,
                                                              "start_model": data_model,
                                                              "data_source": data_source, "aggre_column_list": []})
                    column_dict = table_info.get("column_dict")
                    field_list = table_info.get("field_list")
                    field_list.append(field_obj)
                    multi_field = column.get("multi_field", {})
                    aggre_func = multi_field.get("aggre_func")
                    aggre_column_list = table_info.get("aggre_column_list")
                    if aggre_func is None or aggre_func == -1:
                        for i, index_value in enumerate(column_index_list):
                            columns = column_dict.setdefault(i, [])
                            column_detail = {"index": self.column_to_int(
                                index_value), "name": field_obj.name}
                            column_detail.update(column_info)
                            columns.append(column_detail)
                    else:
                        index_value = column_index_list[0]
                        column_detail = {"index": self.column_to_int(index_value), "name": field_obj.name,
                                         "multi_field": multi_field}
                        column_detail.update(column_info)
                        aggre_column_list.append(column_detail)

            # 这里不管是行号和列号用户设置的都是从1开始
            title_row = template_content.get("title_row", 1)
            name = template_info.get("template_name")
            template_detail.update({"model_uuid": model_uuid, "r_dict": r_dict, "sheet_name": sheet_name, "title_row": title_row,
                                    "name": name})
        return template_detail

    def get_preserve_columns(self, field_obj):
        preserve_columns = list(field_obj.model._meta.columns.values())
        preserve_columns.remove(field_obj.model.id)
        return preserve_columns

    def update_preserve_columns(self, table_info, field_obj):
        preserve = table_info.get("preserve")
        preserve.append(field_obj)
        # 避免导入数据完全相同导致insert返回0
        midify_time = field_obj.model._modified_time
        preserve.append(midify_time)
        table_info["preserve"] = list(set(preserve))

    def column_to_int(self, column_index: str):
        index = column_index_from_string(column_index)
        return index

    def check_import_excel(self, open_excel: openpyxl, template_detail):
        sheet_name = template_detail.get("sheet_name")
        if sheet_name not in open_excel.sheetnames:
            message = "使用的模版与需要导入的EXCEL文档中的【工作表名称】需要一致"
            return False, message
        title_row = template_detail.get("title_row")
        sheet = open_excel[sheet_name]
        title_row_list, enum_column_dict, max_index = self.get_title_row_column(
            template_detail)
        for column_detail in title_row_list:
            column_index = column_detail.get("column_index") + 1
            column_title = column_detail.get("column_title")
            excel_title = sheet.cell(title_row, column_index).value
            if excel_title:
                excel_title = excel_title.replace("*", "").strip()
            if column_title:
                column_title = column_title.replace("*", "").strip()
            if column_title != excel_title:
                message = "导入excel与导入模板格式不一致"
                return False, message
        return True, ""

    def get_title_row_column(self, template_detail):
        title_row_list = []
        enum_column_dict = {}
        r_dict = template_detail.get("r_dict")
        table_dict = r_dict.get("table")
        insert_table_list = ["middle_insert_dict",
                             "front_insert_dict", "back_insert_dict"]
        scan_table_list = ["front_scan_dict"]
        many_scan_table_list = ["back_scan_dict", "middle_scan_dict"]
        tree_ref_dict = r_dict.get("tree_ref_dict")
        front_tree_ref_dict = r_dict.get("front_tree_ref_dict")
        middle_tree_dict = r_dict.get("middle_tree_dict")
        stack_column_list = r_dict.get("stack_column_list")
        table_columns = table_dict.get("columns", {})
        max_index = 0
        for column in table_columns:
            field = column.get("field")
            column_index = column.get("index")
            column_index -= 1
            max_index = max(max_index, column_index)
            column_title = column.get("column_title", "")
            is_required = column.get("is_required")
            if is_required:
                column_title = "*" + column_title
            title_row_list.append(
                {"column_index": column_index, "column_title": column_title})
            if field.lemon_type == FieldType.ENUM:
                enum_column_dict.update(
                    {column_index: field})
        for table_name in scan_table_list:
            scan_dict = r_dict.get(table_name)
            for front_ref_info in scan_dict.values():
                for column in front_ref_info.get("unique_columns"):
                    field = column.get("field")
                    column_index = column.get("index")
                    column_index -= 1
                    max_index = max(max_index, column_index)
                    column_title = column.get("column_title")
                    is_required = column.get("is_required")
                    if is_required:
                        column_title = "*" + column_title
                    title_row_list.append(
                        {"column_index": column_index, "column_title": column_title})
        for table_name in many_scan_table_list:
            scan_dict = r_dict.get(table_name)
            for ref_info in scan_dict.values():
                column_dict = ref_info.get("column_dict")
                # 同一字段一行可能有多列，代表多条数据
                for i, columns in column_dict.items():
                    for column in columns:
                        field = column.get("field")
                        column_index = column.get("index")
                        column_index -= 1
                        max_index = max(max_index, column_index)
                        column_title = column.get("column_title")
                        is_required = column.get("is_required")
                        if is_required:
                            column_title = "*" + column_title
                        title_row_list.append({"column_index": column_index,
                                               "column_title": column_title})
        for table_name in insert_table_list:
            insert_dict = r_dict.get(table_name)
            for back_ref_info in insert_dict.values():
                column_dict = back_ref_info.get("column_dict")
                # 同一字段一行可能有多列，代表多条数据
                for i, columns in column_dict.items():
                    for column in columns:
                        field = column.get("field")
                        column_index = column.get("index")
                        column_index -= 1
                        max_index = max(max_index, column_index)
                        column_title = column.get("column_title")
                        is_required = column.get("is_required")
                        if is_required:
                            column_title = "*" + column_title
                        title_row_list.append(
                            {"column_index": column_index, "column_title": column_title})
                        if field.lemon_type == FieldType.ENUM:
                            enum_column_dict.update(
                                {column_index: field})
        for column in stack_column_list:
            field = column.get("field")
            column_index = column.get("index")
            column_index -= 1
            max_index = max(max_index, column_index)
            column_title = column.get("column_title")
            is_required = column.get("is_required")
            if is_required:
                column_title = "*" + column_title
            title_row_list.append(
                {"column_index": column_index, "column_title": column_title})
            if field.lemon_type == FieldType.ENUM:
                enum_column_dict.update(
                    {column_index: field})
        tree_table_list = list(tree_ref_dict.values())
        tree_table_list.extend(list(front_tree_ref_dict.values()))
        # tree_table_list.extend(list(middle_tree_dict.values()))
        for tree_ref_info in tree_table_list:
            column = tree_ref_info.get("column")
            field = column.get("field")
            column_index = column.get("index")
            column_index -= 1
            max_index = max(max_index, column_index)
            column_title = column.get("column_title")
            title_row_list.append(
                {"column_index": column_index, "column_title": column_title})
        for tree_ref_info in middle_tree_dict.values():
            column_dict = tree_ref_info.get("column_dict")
            # 同一字段一行可能有多列，代表多条数据
            for i, columns in column_dict.items():
                for column in columns:
                    field = column.get("field")
                    column_index = column.get("index")
                    column_index -= 1
                    max_index = max(max_index, column_index)
                    column_title = column.get("column_title")
                    title_row_list.append(
                        {"column_index": column_index,
                         "column_title": column_title})
        return title_row_list, enum_column_dict, max_index

    async def import_template(self, template_uuid):
        column_title = []
        column_index = 0
        enum_column_dict = {}
        export_template_data = {
            "code": 0,
            "message": ""
        }
        template_detail = await self.handle_excel_template(template_uuid)
        sheet_name = template_detail.get("sheet_name")
        title = template_detail.get("name")
        file_name = title + "模板_" + datetime.datetime.now().strftime("%Y_%m_%d") + ".xlsx"
        temp_path = engine.config.STATIC_PATH_PREFIX + \
            "/temp/" + engine.config.MIDDLE_USER_UUID
        path = temp_path + "/" + file_name
        if not os.path.exists(temp_path):
            os.makedirs(temp_path)
        write_excel = xlsxwriter.Workbook(path, {
            "default_format_properties": {
                "font_name": "等线",
                "valign": "vcenter"
            }
        })
        sheet1 = write_excel.add_worksheet(sheet_name)
        title_format = write_excel.add_format({
            "font_size": 14,
            "bold": True,
            "font_color": "#5B9BD8",
            "align": "center",
        })
        field_name_format = write_excel.add_format({
            "font_size": 11,
            "bold": True,
            "align": "left",
            "bg_color": "#DDEBF7"
        })
        title_row = template_detail.get("title_row")
        title_row -= 1
        max_index = 0
        try:
            title_row_list, enum_column_dict, max_index = self.get_title_row_column(
                template_detail)
            for column_detail in title_row_list:
                column_index = column_detail.get("column_index")
                column_title = column_detail.get("column_title")
                sheet1.write(title_row, column_index,
                             column_title, field_name_format)
            sheet1.set_default_row(20)
            sheet1.set_column(0, max_index, 15)
            if title_row != 0:
                sheet1.merge_range(title_row - 1, 0, title_row - 1,
                                   max_index, data=title, cell_format=title_format)
            for index, column in enum_column_dict.items():
                source_data = [enum_data.value for enum_data in column.choices]
                sheet1.data_validation(
                    title_row, index, 1048575, index,
                    {'validate': 'list', 'source': source_data})
            write_excel.close()
            url = self.oss_helper.gen_oss_tmp_path(
                middle_user_id=engine.config.MIDDLE_USER_UUID,
                app_uuid=self.lsm.lemon.system.current_user.app_uuid,
                tenant_uuid=self.lsm.lemon.system.current_user.tenant_id,
                page_uuid=self.page_machine_id,
                control_uuid=self.machine_id) + "/" + file_name
            await self.oss_helper.put_oss_object(url, path, local_file=True)
        except:
            format_error = traceback.format_exc()
            app_log.error(format_error)
            await runtime_log.error(format_error)
            export_template_data.update({"code": -1})
            return export_template_data
        export_template_data.update({
            "data": {
                "url": url
            }
        })
        return export_template_data

    async def export_template(self, template_uuid, template_detail=None):
        column_index = 0
        enum_column_dict = {}
        if not template_detail:
            template_detail = await self.handle_export_excel_template(
                template_uuid)
        datetime_column = []
        sheet_name = template_detail.get("sheet_name")
        title = template_detail.get("name")
        file_name = title + "模板_" + datetime.datetime.now().strftime("%Y_%m_%d") + ".xlsx"
        temp_path = engine.config.STATIC_PATH_PREFIX + \
            "/temp/" + engine.config.MIDDLE_USER_UUID
        path = temp_path + "/" + file_name
        if not os.path.exists(temp_path):
            os.makedirs(temp_path)
        write_excel = xlsxwriter.Workbook(path, {
            "default_format_properties": {
                "font_name": "等线",
                "valign": "vcenter"
            }
        })
        sheet1 = write_excel.add_worksheet(sheet_name)
        title_format = write_excel.add_format({
            "font_size": 14,
            "bold": True,
            "font_color": "#5B9BD8",
            "align": "center",
        })
        field_name_format = write_excel.add_format({
            "font_size": 11,
            "bold": True,
            "align": "left",
            "bg_color": "#DDEBF7"
        })
        title_row = template_detail.get("title_row")
        title_row -= 1
        r_dict = template_detail.get("r_dict")
        max_index = 0
        r_dict = template_detail.get("r_dict")
        r_table = r_dict.get("r_table")
        table_dict = r_dict.get("table")
        tree_dict = r_dict.get("tree_dict")
        table_columns = table_dict.get("columns", {})
        for column in table_columns:
            field = column.get("field")
            column_index = column.get("index")
            column_index -= 1
            max_index = max(max_index, column_index)
            column_title = column.get("column_title", "")
            is_required = column.get("is_required")
            if is_required:
                column_title = "*" + column_title
            sheet1.write(title_row, column_index,
                         column_title, field_name_format)
            if field.lemon_type == FieldType.ENUM:
                enum_column_dict.update(
                    {column_index: field})
            elif field.lemon_type == FieldType.DATETIME:
                datetime_column.append(column_index)
        table_list = list(r_table.values())
        table_list.extend(list(tree_dict.values()))
        for table_info in table_list:
            # 同一字段配置了多列展示
            column_dict = table_info.get("column_dict")
            # 字段配置了聚合函数
            aggre_column_list = table_info.get("aggre_column_list")
            column_list = list(column_dict.values())
            if aggre_column_list:
                column_list.append(aggre_column_list)
            for columns in column_list:
                for column in columns:
                    field = column.get("field")
                    column_index = column.get("index")
                    column_index -= 1
                    max_index = max(max_index, column_index)
                    column_title = column.get("column_title")
                    sheet1.write(title_row, column_index,
                                 column_title, field_name_format)
                    if field.lemon_type == FieldType.ENUM:
                        enum_column_dict.update(
                            {column_index: field})
                    elif field.lemon_type == FieldType.DATETIME:
                        datetime_column.append(column_index)
        sheet1.set_default_row(20)
        sheet1.set_column(0, max_index, 15)
        sheet1.autofilter(title_row, 0, title_row, max_index)
        for column_index in datetime_column:
            sheet1.set_column(column_index, column_index, 20)
        if title_row != 0:
            # # 如果表头行不在第一行，第一行到表头行所有行作为标题行
            # if max_index > 0 or title_row > 1:
            #     sheet1.merge_range(0, 0, title_row - 1, max_index,
            #                        data=title, cell_format=title_format)
            # else:
            #     if title_row == 1:
            # sheet1.write(title_row - 1, 0, title, title_format)
            sheet1.merge_range(title_row - 1, 0, title_row - 1,
                               max_index, data=title, cell_format=title_format)
        for index, column in enum_column_dict.items():
            source_data = [enum_data.value for enum_data in column.choices]
            sheet1.data_validation(
                title_row, index, 1048575, index,
                {'validate': 'list', 'source': source_data})
        return write_excel, sheet1, path, file_name


__all___ = ["FrontScanStack", "BackrefScanStack", "MiddleRefScanStack", "FrontInsertStack", "BackrefInsertStack",
            "MiddleRefInsertStack"]
