# -*- coding:utf-8 -*-

from apps.utils import Json

class Func(Json):

    def __init__(
        self, uuid, name, description, func, icon_type=None, icon=None, is_async=True,
        is_system=True, arg_list=None, return_list=None, install_sm_toolbox=True, 
        sm_tool_name=None, sm_tool_category=None, from_py_module: bool = False, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.func = func
        self.icon_type = icon_type
        self.icon = icon
        self.is_async = is_async
        self.is_system = is_system
        if arg_list is None:
            arg_list = list()
        if return_list is None:
            return_list = list()
        self.arg_list = arg_list
        self.return_list = return_list
        self.install_sm_toolbox = install_sm_toolbox
        self.sm_tool_name = sm_tool_name
        self.sm_tool_category = sm_tool_category
        self.from_py_module: bool = from_py_module
        super().__init__(*args, **kwargs)
