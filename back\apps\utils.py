# -*- coding:utf-8 -*-

import random
import string
import json
import copy
import types
import time
import uuid
import os
from typing import AnyStr, Dict, List
from operator import itemgetter
from itertools import groupby
import deepdiff
import asyncio
import hashlib
import traceback
import re
import collections
from functools import partial, wraps
from contextlib import contextmanager
from httptools import parse_url
from PIL import Image
from collections import defaultdict, OrderedDict
from peewee import DatabaseError, Field
from enum import Enum
import xml.etree.cElementTree as ET

import ujson
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from sanic.response import StreamingHTTPResponse
from packages.weworkapi_python.callback.WXBizMsgCrypt3 import WXBizMsgCrypt

from apps import exceptions, Runtime
from apps.local_ext_config import ALIYUN
from baseutils.log import app_log
from baseutils.const import SystemField, SystemTable, TagPermissionAction, UserTableStatus, WorkflowType
from baseutils.const import Code, ReturnCode, LemonPublishError, make_new_key
from apps.ide_const import (
    FieldType, ImageSourceType, MysqlErrorDesc, PrintType, LabelPrintType, ComponentType,
    DataSourceType, RelationshipType, ConstantCode, ValueEditorType,
    LemonDesignerErrorCode as LDEC, WorkflowStatus, NodeSystemActionType,
    DocumentName
)
from apps.value_editor_utils import LemonBaseValueEditor
from apps.base_utils import *
from apps.exceptions import PermissionRequired

from baseutils.utils import lemon_uuid, make_tenant_user_table_name, MODEL_COPY_DICT, LemonContextVar


class LemonDictResponse(dict):

    def __init__(self, return_code: ReturnCode = Code.OK, data: dict = None, **kwargs):
        super().__init__()
        code, message = return_code.code, return_code.message
        # 运行时 code 为 0 时，也表示 success 为 True
        success = True if code == 200 or code == 0 else False
        self.update(code=code, message=message, success=success)
        if data is not None:
            self.update(data=data)
        if kwargs:
            self.update(kwargs)
        self.initialize()

    def initialize(self):
        pass

    def update_data(self, data: dict) -> None:
        self.update(data=data)


class LemonRuntimeDictResponse(LemonDictResponse):

    def __init__(
            self, return_code: ReturnCode = Code.OK, data: dict = None,
            result: str = "response", **kwargs):
        self.update(result=result)
        super().__init__(return_code, data, **kwargs)


class LemonDesignerErrorDict(dict):

    def __init__(
            self, error_code: int, error_message: str,
            module_name: str = "", document_name: str = "",
            element_uuid: str = "", element_name: str = "",
            element_attr: str = "", error_position: str = "",
            **kwargs):
        super().__init__()
        self.update(
            error_code=error_code,
            error_message=error_message,
            module_name=module_name,
            document_name=document_name,
            element_uuid=element_uuid,
            element_name=element_name,
            element_attr=element_attr,
            error_position=error_position
        )
        self.update(kwargs)

    def __getattr__(self, key):
        if key.startswith('__') and key.endswith('__'):
            # 防止pickle报错
            raise AttributeError
        return self.get(key, None)


class Json(object):

    def __init__(self, *args, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def __getattr__(self, key):
        return self.__dict__.get(key, None)

    def to_dict(self):
        json_dict = dict()
        for key, value in self.__dict__.items():
            if key == "re_class":
                key = "class"
            if isinstance(value, set):
                value = list(value)
            if isinstance(value, Json):
                json_dict.update({key: value.to_dict()})
            elif isinstance(value, list):
                to_list = list()
                for v in value:
                    if isinstance(v, Json):
                        to_list.append(v.to_dict())
                    else:
                        to_list.append(v)
                json_dict.update({key: to_list})
            else:
                json_dict.update({key: value})
        return json_dict

    def to_json(self, pretty=False):
        json_dict = self.to_dict()
        if pretty:
            return ujson.dumps(json_dict, indent=2)
        return ujson.dumps(json_dict)


class Flow:

    _protected_keys = ["流程名", "流程类型", "工作流状态", "审批流状态", "审核时间", "处理动作", "节点名"]

    def __init__(self, **kwargs) -> None:

        self.locked = True
        self.__流程名 = kwargs.get("wf_name")
        self.__流程类型 = kwargs.get("wf_type")
        self.__工作流状态 = None
        self.__审批流状态 = None
        self.__审核时间 = None
        self.__处理动作 = None
        self.__节点名 = None
        self.__审批意见 = None
        for k, v in kwargs.items():
            setattr(self, k, v)

    @property
    def locked(self):
        return self.__locked

    @locked.setter
    def locked(self, value):
        self.__locked = value

    def __setattr__(self, name, value):
        if name in self._protected_keys and self.locked:
            raise Exception("Cannot modify attribute when locked")
        super().__setattr__(name, value)

    @property
    def 流程名(self):
        return self.__流程名

    @流程名.setter
    def 流程名(self, value):
        self.__流程名 = value

    @property
    def 流程类型(self):
        return self.__流程类型

    @流程类型.setter
    def 流程类型(self, value):
        self.__流程类型 = value

    @property
    def 工作流状态(self):
        return self.__工作流状态

    @工作流状态.setter
    def 工作流状态(self, value):
        self.__工作流状态 = value

    @property
    def 审批流状态(self):
        return self.__审批流状态

    @审批流状态.setter
    def 审批流状态(self, value):
        self.__审批流状态 = value

    @property
    def 审核时间(self):
        return self.__审核时间

    @审核时间.setter
    def 审核时间(self, value):
        self.__审核时间 = value

    @property
    def 处理动作(self):
        return self.__处理动作

    @处理动作.setter
    def 处理动作(self, value):
        self.__处理动作 = value

    @property
    def 节点名(self):
        return self.__节点名

    @节点名.setter
    def 节点名(self, value):
        self.__节点名 = value

    @property
    def 审批意见(self):
        return self.__审批意见

    @审批意见.setter
    def 审批意见(self, value):
        self.__审批意见 = value

    def unlock(self):
        self.locked = False

    def lock(self):
        self.locked = True

    def set_form_model_status(self, node, action_uuid, action_name, action_data):
        with temp_unlock(self):
            form_status, approval_time = self.gen_form_model_status(
                node.workflow.wf_status, action_uuid)
            if self.流程类型 == WorkflowType.SIMPLE:
                # 审批流
                self.审批流状态 = UserTableStatus.status_code2_desc.get(form_status)
                self.审核时间 = approval_time
            self.处理动作 = action_name
            self.节点名 = node.node_name
            if isinstance(action_data, dict):
                self.审批意见 = action_data.get("comment")
            self._action_data = action_data
            self._handlers = node.handlers

    def set_form_model_wf_status(self, form_wf_status):
        with temp_unlock(self):
            if self.流程类型 == WorkflowType.STANDARD:
                self.工作流状态 = form_wf_status

    def gen_form_model_status(self, wf_status, action_uuid):
        if wf_status in [WorkflowStatus.DONE] and \
                action_uuid == NodeSystemActionType.REJECT:
            # 拒绝后进入结束节点, 已关闭
            form_status = UserTableStatus.CLOSED
            approval_time = None
        elif wf_status in [WorkflowStatus.DONE]:
            # 同意后进入结束节点 或 直接进入结束节点 -> 已审核
            form_status = UserTableStatus.CHECKED
            approval_time = int(time.time())
        elif wf_status in [WorkflowStatus.CANCEL]:
            # 已取消发起
            form_status = UserTableStatus.UNSUBMIT
            approval_time = None
        elif wf_status in [WorkflowStatus.STOPPED]:
            # 作废
            form_status = UserTableStatus.INVALID
            approval_time = None
        elif wf_status in [WorkflowStatus.EXEC]:
            form_status = UserTableStatus.SUBMIT
            approval_time = None
        else:
            form_status = None
            approval_time = None
        return form_status, approval_time


@contextmanager
def process_args():
    try:
        yield
    except Exception:
        raise exceptions.RequestJsonError("request json error.")


def create_token(container: string = None, length: int = 16):
    if not container:
        container = string.digits
    token = "".join(random.sample(container, length))
    return token


def send_sms(phone_number, code, template_code=None):
    if template_code is None:
        template_code = ALIYUN.SMS_TEMPLATE_LOGIN
    json_code = ujson.dumps({"code": code})
    client = AcsClient(
        ALIYUN.KEY, ALIYUN.KEY_SECRET, ALIYUN.SMS_REGION)
    request = CommonRequest()

    request.set_accept_format(ALIYUN.SMS_FORMAT)
    request.set_domain(ALIYUN.SMS_DOMAIN)
    request.set_method(ALIYUN.SMS_METHOD)
    request.set_protocol_type(ALIYUN.SMS_PROTOCOL_TYPE)  # https | http
    request.set_version(ALIYUN.SMS_VERSION)
    request.set_action_name(ALIYUN.SMS_ACTION)

    request.add_query_param('RegionId', ALIYUN.SMS_REGION)
    request.add_query_param('PhoneNumbers', phone_number)
    request.add_query_param('SignName', ALIYUN.SMS_SIGN_NAME)
    request.add_query_param('TemplateCode', template_code)
    request.add_query_param('TemplateParam', json_code)

    response = client.do_action(request)
    return response


def get_ext_tenant(request, key=None):
    referer = request.headers.get("Referer")
    content_type = request.headers.get("content-type", None)
    if referer:
        _parsed_url = parse_url(referer.encode())
        path_split = _parsed_url.path.decode("utf8").split("/")
        if len(path_split) > 3:
            ext_tenant = path_split[3]
            if check_lemon_uuid(ext_tenant):
                if "application/json" in content_type:
                    if request.json:
                        request.json.update({"ext_tenant": ext_tenant})
                return ext_tenant
    if content_type is not None:
        if "application/json" in content_type:
            if request.json:
                if key is None:
                    key = "ext_tenant"
                if key:
                    return request.json.get(key)


def restore_adapter(template):
    all_editors = {}
    if template:
        for template in template:
            uuid = lemon_uuid()
            type_ = ValueEditorType.STRING
            value = template.get("default")
            value_editor_dict = dict(uuid=uuid,
                                     type=type_,
                                     is_monitor=True,
                                     once=True,
                                     value=value
                                     )
            key = template.get("key")
            name = template.get("name")
            all_editors.update({
                key: {
                    "name": name,
                    "value": value_editor_dict
                }
            })
    return all_editors


class LemonJsonEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, set):
            return list(o)
        return super().default(o)


def gen_json_delta(t1, t2):
    diff = deepdiff.DeepDiff(t1, t2)
    delta = deepdiff.Delta(diff)
    return delta.to_dict()


def patch_json(s, d):
    delta = deepdiff.Delta(d)
    return s + delta


def patch_document_content(json_obj):
    json_obj = ujson.loads(json_obj)
    source = json_obj.get("source", {})
    if source is None:
        source = {}
    delta = json_obj.get("delta")
    if delta and source:
        return patch_json(source, delta)
    return source


def get_dominant_color(img, numcolors=3, resize=150):
    # Resize image to speed up processing
    # img = Image.open(image_file)
    img = img.copy()
    img.thumbnail((resize, resize))

    # Reduce to palette
    paletted = img.convert('P', palette=Image.ADAPTIVE, colors=numcolors)

    # Find dominant colors
    palette = paletted.getpalette()
    color_counts = sorted(paletted.getcolors(), reverse=True)
    colors = list()
    for i in range(min(numcolors, len(color_counts))):
        palette_index = color_counts[i][1]
        dominant_color = palette[palette_index*3:palette_index*3+3]
        colors.append(tuple(dominant_color))
    if len(colors) < numcolors:
        for _ in range(numcolors - len(colors)):
            colors.append((255, 255, 255))
    return colors


def get_login_user_uuid(request):
    """
    return:
        real_user_uuid
        login_as:  可能是team_uuid或user_uuid
    """
    session_dict = request.ctx.session
    login_as = session_dict.get("login_as")
    user_uuid = session_dict.get("user_uuid")
    return user_uuid, login_as or user_uuid


async def async_run(cmd, env=None):
    app_log.info("async run cmd: " + repr(cmd))
    start_time = time.time()
    proc = await asyncio.create_subprocess_shell(
        cmd,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
        env=env)

    stdout, stderr = await proc.communicate()
    step = time.time() - start_time
    app_log.debug(f"result: {proc.returncode}")
    app_log.info(
        f"async run cmd ({cmd}) result: {proc.returncode} run_time:{step}")
    if proc.returncode != 0:
        app_log.info(f"async_run error: {stdout}")
        app_log.info(f"async_run error: {stderr}")
    return proc.returncode, stdout, stderr

"""
检查用户权限
policy_type:  1仅owner  0仅combiner  -1全部
# TODO 扩展租户
"""


def check_user_policy(**kwarg):
    from apps.engine import engine
    from apps.entity import Combine

    def check_policy_type(f):
        # 检查请求中用户权限
        @wraps(f)
        async def inner(*args, **kwargs):
            policy_type = kwarg.get("policy_type", 1)
            with process_args():
                request = args[1]
                app_uuid = request.ctx.session.get("app_uuid")
                if not app_uuid and isinstance(request.json, dict):
                    app_uuid = request.json.get("app_uuid")
                real_user_uuid, user_uuid = get_login_user_uuid(request)
            app_log.info(f"user_policy_checking: {app_uuid}, {user_uuid}")
            if not all([app_uuid, user_uuid]):
                raise PermissionRequired(
                    f"{request.path} permission required!")
            model = Combine
            query = model.select().where(model.app_uuid == app_uuid,
                                         model.user_uuid == user_uuid)
            combine = await engine.access.select_obj_by_query(model, query)
            if policy_type in [1, 0]:
                if not combine.get("team_uuid") and policy_type != combine.get("is_owner"):
                    raise PermissionRequired(
                        f"{request.path} permission required!")
            elif policy_type == -1:
                ...
            else:
                raise PermissionRequired(
                    f"{request.path} permission required!")
            if not combine:
                raise PermissionRequired(
                    f"{request.path} permission required!")
            return await f(*args, **kwargs)
        return inner
    return check_policy_type


def gen_oss_domain_url(url, config):
    domain_url = re.sub(
        r"lemon-resources-.*\.aliyuncs\.com",
        config.OSS_DOMAIN_NAME,
        url,
        1
    )
    return domain_url


def gen_oss_image_resize_params(kwargs):
    with process_args():
        url = str(kwargs.get("url", ""))
        size_type = str(kwargs.get("size_type"))  # 0: 像素   1: 百分比
        width = int(float(kwargs.get("width", 0)))  # 长度 w
        height = int(float(kwargs.get("height", 0)))  # 宽度 h
        row_size = kwargs.get("row_size")  # 1: 锁定长宽比 0:不锁定
        per_value = kwargs.get("per_value")  # 百分比缩放数值
        limit = str(kwargs.get("limit", 0))  # 0: 允许大于原图 1: 不允许

    def _gen_pixel_type_params(row_size, height, width):
        pixel_type_params = []
        if int(row_size):
            if_fix_row_size = "m_lfit"
        else:
            if_fix_row_size = "m_fixed"
        pixel_type_params.append(if_fix_row_size)

        if height or width:
            if height:
                h_value = "h_" + str(height)
                pixel_type_params.append(h_value)
            if width:
                w_value = "w_" + str(width)
                pixel_type_params.append(w_value)
        else:
            pixel_type_params = []
        return pixel_type_params

    def _gen_percentage_type_params(per_value):
        per_value_params = []
        if per_value:
            per_value_style = "p_" + str(per_value)
            per_value_params.append(per_value_style)
        return per_value_params

    need_resize = size_type is not None
    style_parts, value_params = [], []
    if need_resize:
        style_flag = "image/resize"
        style_parts.append(style_flag)
        if_bigger_than_previous = "limit_" + limit
        style_parts.append(if_bigger_than_previous)

        if size_type == "0":
            value_params = _gen_pixel_type_params(row_size, height, width)
        elif size_type == "1":
            value_params = _gen_percentage_type_params(per_value)
    if value_params:
        style_parts.extend(value_params)
    else:
        style_parts = []
    style = ",".join(style_parts)

    app_log.info(f"{url} resize style: {style}")
    return style


async def stream_response(object_stream, content_type=None):
    chunk_size = 4096

    if content_type is None:
        content_type = object_stream.content_type
    file_stream = object_stream

    async def _streaming_fn(response):
        try:
            # file_stream.seek(0)
            while True:
                content = file_stream.read(chunk_size)
                if len(content) < 1:
                    break
                await response.write(content)
        finally:
            file_stream.close()
    response = StreamingHTTPResponse(
        streaming_fn=_streaming_fn,
        # headers=headers,
        content_type=content_type,
    )
    return response


def gen_lemon_uuid_by_pin_uuid(old_uuid, pin_uuid):
    return str(uuid.uuid5(uuid.UUID(old_uuid), pin_uuid)).replace("-", "")


def gen_uuid(old_uuid, uuid_dict, pin_uuid=None):
    new_uuid_been_created = uuid_dict.get(old_uuid)
    if not new_uuid_been_created:
        if pin_uuid:
            new_uuid = gen_lemon_uuid_by_pin_uuid(old_uuid, pin_uuid)
        else:
            new_uuid = lemon_uuid()
        uuid_dict.update({old_uuid: new_uuid})
    else:
        new_uuid = new_uuid_been_created
    return new_uuid


def gen_uuid_update_dict(old_uuid, uuid_dict, extra_reg, pin_uuid=None):
    new_uuid = old_uuid
    if isinstance(old_uuid, str) and re.match(r"[a-z0-9]{32}$", old_uuid):
        new_uuid = gen_uuid(old_uuid, uuid_dict, pin_uuid)
    elif isinstance(old_uuid, str) and re.match(r"[a-z0-9]{32}_[a-z0-9]{32}", old_uuid):
        app_log.info(f"special_uuid: {old_uuid}")
        part1, part2 = old_uuid.split("_")
        new_part1 = gen_uuid(part1, uuid_dict, pin_uuid)
        new_part2 = gen_uuid(part2, uuid_dict, pin_uuid)
        new_uuid = "_".join([new_part1, new_part2])
    # TODO 需要精确到? re.match(r"[a-z0-9]{32}\*(_)?[id|_status|_owner]", old_uuid)
    elif isinstance(old_uuid, str) and re.match(r"[a-z0-9]{32}\*(_)?[a-zA-Z]", old_uuid):
        app_log.info(f"special_uuid: {old_uuid}")
        part1, part2 = old_uuid.split("*")
        part1 = gen_uuid(part1, uuid_dict, pin_uuid)
        new_uuid = "*".join([part1, part2])
    elif isinstance(old_uuid, str) and re.match(r"[a-z0-9]{32}\.[a-zA-Z]", old_uuid):
        app_log.info(f"special_uuid: {old_uuid}")
        part1, part2 = old_uuid.split(".")
        part1 = gen_uuid(part1, uuid_dict, pin_uuid)
        new_uuid = ".".join([part1, part2])
    elif isinstance(old_uuid, str) and isinstance(extra_reg, dict):
        extra_search = extra_reg.get("search", dict())
        for _, ext in extra_search.items():
            reg = ext.get("reg")
            new = ext.get("new")
            new_uuid = re.sub(reg, new, old_uuid)
        # for reg in extra_match:
        #     if re.match(reg, old_uuid):
        #         new_uuid = gen_uuid(old_uuid, uuid_dict)
    return new_uuid


def update_image_url(old_url, uuid_template_to_app, extra_reg, pin_uuid):
    new_image_info = list()
    source_image_info = old_url.split("/")
    for old_uuid in source_image_info:
        new_uuid = gen_uuid_update_dict(
            old_uuid, uuid_template_to_app, extra_reg, pin_uuid)
        new_image_info.append(new_uuid)
    new_url = "/".join(new_image_info)
    return new_url


def replace_uuid_in_dict(d, uuid_template_to_app: dict, extra_reg=None, pin_uuid=None):
    """
    d中的全部uuid重新构造,
    params: uuid_template_to_app
        指定old_key-new_key, 不重新随机
    """
    if isinstance(d, list):
        for i, v in enumerate(d):
            if isinstance(v, (dict, list)):
                replace_uuid_in_dict(
                    v, uuid_template_to_app, extra_reg, pin_uuid)
            else:
                new_uuid = gen_uuid_update_dict(
                    v, uuid_template_to_app, extra_reg, pin_uuid)
                d[i] = new_uuid
    elif isinstance(d, dict):
        for k in list(d.keys()):
            v = d[k]
            new_k = gen_uuid_update_dict(
                k, uuid_template_to_app, extra_reg, pin_uuid)
            k_changed = new_k != k
            if isinstance(v, (dict, list)):
                replace_uuid_in_dict(
                    v, uuid_template_to_app, extra_reg, pin_uuid)
                new_v = v
            elif isinstance(v, str) and re.match(r"^design/", v):
                new_v = update_image_url(v, uuid_template_to_app, extra_reg, pin_uuid)
            else:
                new_uuid = gen_uuid_update_dict(
                    v, uuid_template_to_app, extra_reg, pin_uuid)
                new_v = new_uuid
            if k_changed:
                d.pop(k)
                d[new_k] = new_v
            else:
                d[k] = new_v


class MySQLErrorHandler:
    # TODO 抽象类
    def __init__(self, e: DatabaseError) -> None:
        self.exc = e
        self.sql = e.sql if hasattr(e, "sql") else ""
        self.error_code = e.args[0]
        self.error_infomation = e.args[-1]

    def handle(self):
        app_log.info(f"query: {type(self.sql)},  {self.sql}")
        element_uuid, element_type_num = self.gen_error_element_uuid()
        return element_uuid, element_type_num

    def gen_error_element_uuid(self):
        element_type = ["INDEX", "COLUMN", "TABLE"]
        for element_type_num, t in enumerate(element_type, 1):
            re_pattern = r"(CREATE|UPDATE|MODIFY|ALTER|DELETE|SELECT).*?" + \
                t + r".*?`[a-z0-9]{32}"
            r = re.match(re_pattern, self.sql)
            if r:
                if element_type_num == 1:  # 处理创建唯一索引失败
                    re_pattern = r"(CREATE|UPDATE|MODIFY|ALTER|DELETE|SELECT).*" + \
                        t + r".*`[a-z0-9]{32}"
                    r = re.match(re_pattern, self.sql)
                    element_type_num = 2
                app_log.info(r.group())
                element_uuid = r.group()[-32:]
                return element_uuid, element_type_num
        return "", 0

    def update_publish_message(self, message, element_data):
        error_list = list()
        error_desc = MysqlErrorDesc.code2desc.get(self.error_code)
        if not error_desc:
            error_desc = "\n\n".join(
                [str(self.error_code), self.error_infomation, self.sql])
        for d in element_data:
            model_name = d.get("model_name")
            error_info = str(self.error_code) + ": " + \
                error_desc.format(model_name)
            m = copy.copy(message)
            error_list.append(m)
            m.update({
                "element_name": d.get("element_name"),
                "element_uuid": d.get("element_uuid"),
                "module_uuid": d.get("module_uuid"),
                "model_uuid": d.get("model_uuid"),
                "document_uuid": d.get("document_uuid"),
                "document_type": d.get("document_type"),
                "document_name": d.get("document_name"),
                "module_name": d.get("module_name"),
                "error_message": error_info,
                "error_position": "通用",
                "error_code": self.error_code
            })
        return error_list


class LemonErrorHandler:

    def __init__(self, e: LemonPublishError) -> None:
        self.exc = e
        self.element_type_num = getattr(e, "element_type_num", 0)
        self.element_uuid = getattr(e, "element_uuid", "")
        self.true_element_uuid = getattr(e, "true_element_uuid", dict())
        self.true_element_name = getattr(e, "true_element_name", dict())

    def handle(self):
        return self.element_uuid, self.element_type_num

    def update_publish_message(self, message: dict, element_data):
        error_list = list()
        for d in element_data:
            m = copy.copy(message)
            error_list.append(m)
            m.update({
                "element_name": d.get("element_name", ""),
                "element_uuid": d.get("element_uuid", ""),
                "module_uuid": d.get("module_uuid", ""),
                "document_uuid": d.get("document_uuid", ""),
                "document_type": d.get("document_type", ""),
                "document_name": d.get("document_name", ""),
                "module_name": d.get("module_name", ""),
                "error_message": self.exc.return_code.message,
                "error_position": "通用",
                "error_code": self.exc.return_code.code
            })
        return error_list


class BaseErrorHandler:

    @classmethod
    def from_exception(cls, e: Exception):
        if not e.args:
            return None
        msg = e.args[0]
        if not isinstance(msg, str):
            return None
        return_code = Code.PUBLISH_ERROR
        column_pattern = r"I don't know how to change ColumnMetadata\(name='(.*?)', data_type="
        column_match = re.search(column_pattern, msg)
        if column_match:
            column_uuid = column_match.group(1)
            return_code.message = "字段出错"
            if msg.count("expression=''") == 1:
                # 在尝试把生成列与其他列互相转换
                return_code.message += ", 禁止生成列与其他列互相转换"
            return LemonPublishError(return_code, element_type_num=2, element_uuid=column_uuid)
        table_pattern = r"In table '(.*?)' I don't know how to change"
        table_match = re.search(table_pattern, msg)
        if table_match:
            table_uuid = table_match.group(1)
            return_code.message = "模型出错"
            return LemonPublishError(return_code, element_type_num=1, element_uuid=table_uuid)
        return None


class DocumentCheckErrorHandler:

    def __init__(self, e) -> None:
        self.exc = e
        self.error_message_list = getattr(e, "error_message", list())

    def handle(self):
        return self.error_message_list

    def update_publish_message(self, message: dict, element_data):
        error_list = list()
        for d in element_data:
            m = copy.copy(message)
            error_list.append(m)
            m.update({
                "element_name": d.get("element_name", ""),
                "element_uuid": d.get("element_uuid", ""),
                "module_uuid": d.get("module_uuid", ""),
                "document_uuid": d.get("document_uuid", ""),
                "document_type": d.get("document_type", ""),
                "document_name": d.get("document_name", ""),
                "module_name": d.get("module_name", ""),
                "error_message": d.get("error_message", ""),
                "error_position": d.get("error_position", ""),
                "error_code": d.get("error_code", "")
            })
        return error_list


class ModelField(object):

    def __init__(self, field_dict):
        self.uuid = field_dict.get("field_uuid")
        self.name = field_dict.get("field_name")
        self.display_name = field_dict.get("display_name")
        self.field_type = field_dict.get("field_type")
        self.calculate_field = field_dict.get("calculate_field")
        self.calculate_type = field_dict.get("calculate_type")
        self.is_system = field_dict.get("is_system")

    def __repr__(self):
        return "<%s('%s')|('%s')@%s>" % (type(self).__name__, self.uuid, self.name, id(self))


class ModelBasic(object):

    def __init__(self, model_dict):
        self.uuid = model_dict.get("model_uuid")
        self.name = model_dict.get("model_name")
        self.display_name = model_dict.get("display_name")
        self.name_field = model_dict.get("name_field")
        self.columns = dict()

    def add_column(self, model_field: ModelField):
        field_uuid = model_field.uuid
        self.columns.update({field_uuid: model_field})

    def __repr__(self):
        return "<%s('%s')|('%s')@%s>" % (type(self).__name__, self.uuid, self.name, id(self))

    @property
    def verbose_name(self):
        if self.display_name:
            return self.display_name
        else:
            return self.name


class RelationshipBasic(object):

    def __init__(self, relationship_dict):
        self.uuid = relationship_dict.get("relationship_uuid")
        self.name = relationship_dict.get("relationship_name")
        self.type = int(relationship_dict.get("relationship_type"))
        self.display_name = relationship_dict.get("display_name")
        self.source_model = relationship_dict.get("source_model")
        self.target_model = relationship_dict.get("target_model")
        self.extra = relationship_dict.get("extra")

    def __repr__(self):
        return "<%s('%s')|('%s')@%s>" % (type(self).__name__, self.uuid, self.name, id(self))

    @property
    def verbose_name(self):
        if self.display_name:
            return self.display_name
        else:
            return self.name


class ModelNode(object):

    def __init__(self, model: ModelBasic):
        self.model = model
        self.relation_nodes = set()

    def __repr__(self):
        return "<%s('%s')|('%s')@%s>" % (type(self).__name__, self.model.name, self.model.uuid, id(self))

    def add_nodes(self, node):
        self.relation_nodes.add(node)


class Node(object):

    def __repr__(self):
        return "<%s('%s')|('%s')@%s>" % (
            type(self).__name__, self.node.model.name, self.node.model.uuid, id(self))

    def __lt__(self, other):
        return id(self) < id(other)


class RelationNode(Node):

    def __init__(self, node: ModelNode, r_type, name="", display_name="", is_source=True, r_uuid=None):
        self.node = node
        self.r_type = r_type
        self.name = name
        self.display_name = display_name
        self.is_source = is_source
        self.is_middle = False
        self.r_uuid = r_uuid

    @property
    def verbose_name(self):
        if self.display_name:
            return self.display_name
        else:
            return self.name


class SourceNode(RelationNode):

    def __init__(self, node: ModelNode, r_type, name="", display_name="", is_source=True, r_uuid=None):
        super().__init__(node, r_type, name, display_name, is_source, r_uuid)


class TargetNode(RelationNode):

    def __init__(self, node: ModelNode, r_type, name="", display_name="", is_source=False, r_uuid=None):
        super().__init__(node, r_type, name, display_name, is_source, r_uuid=r_uuid)


def build_model_field_path(model_name, field_path):
    if field_path:
        if isinstance(field_path, list):
            path_str = "|".join(field_path)
        else:
            path_str = field_path
        return "|".join([model_name, path_str])
    else:
        return model_name


def base_lemon_field(field_uuid, field_model_uuid_map, path=None):
    if isinstance(field_uuid, Field):
        field_model = field_uuid.model
        field_uuid = field_uuid.column_name
    else:
        field_model = field_model_uuid_map.get(field_uuid)
    field = None
    if not field_model:
        return field
    # 系统字段field_uuid 特殊处理
    if "*" in field_uuid:
        model_uuid, field_uuid = decode_sys_field_uuid(field_uuid)
    if field_model:
        model_uuid = field_model._meta.table_name
        if model_uuid in SystemTable.UUIDS:
            field_model = make_tenant_sys_table_copy(field_model)
            model_uuid = field_model._meta.table_name
        if hasattr(field_model, "_meta"):
            field = field_model._meta.columns.get(field_uuid)
        else:
            field = field_model.columns.get(field_uuid)
        if path:
            field_model = field_model.alias(
                build_model_field_path(model_uuid, path))
            field = getattr(field_model, field.name)
    return field


def base_lemon_model(model_uuid, model_uuid_map):
    if model_uuid in MODEL_COPY_DICT:
        model_copy_info = MODEL_COPY_DICT.get(model_uuid, {})
        model_uuid = model_copy_info.get("table_name")
    model = model_uuid_map.get(model_uuid)
    return model


def base_lemon_model_node(model_uuid, node_uuid_map):
    if model_uuid in MODEL_COPY_DICT:
        model_copy_info = MODEL_COPY_DICT.get(model_uuid, {})
        model_uuid = model_copy_info.get("table_name")
    node = node_uuid_map.get(model_uuid)
    return node


def base_lemon_association(association, model_uuid, node_uuid_map, relationship_uuid_map, path=None):
    # 通过关联路径 和 给出的模型，找到对端模型
    # is_source : 对端模型是否为 外键模型
    # is_many : 对端模型是否可以 选择多条数据
    # 例如，给定 association 和 多端的模型 model_uuid ，对端为 1端 ，则
    # is_source 为 False ； is_many 也为 False
    # 因为此时 对端 不是外键模型 ； 并且为 1端
    _, model_uuid = get_sys_model_copy(model_uuid)
    find_model_node = partial(base_lemon_model_node,
                              node_uuid_map=node_uuid_map)
    field_model, is_source, is_many, r_type = None, True, True, 0
    relationship = relationship_uuid_map.get(association, None)
    if relationship is not None:
        s_model, t_model, r_type, self_referential, self_from_source = relationship
        if self_referential:
            if self_from_source:
                field_model, is_source, is_many = t_model, False, False
            else:
                field_model, is_source, is_many = s_model, True, True
            return field_model, is_source, is_many, r_type
        s_model_uuid = s_model._meta.table_name if hasattr(
            s_model, "_meta") else s_model.uuid
        t_model_uuid = t_model._meta.table_name if hasattr(
            t_model, "_meta") else t_model.uuid
        s_model_uuid = get_table_meta_class_name(s_model_uuid)
        t_model_uuid = get_table_meta_class_name(t_model_uuid)
        # app_log.info(f"s_model: {s_model}, t_model: {t_model}")
        if r_type == RelationshipType.MANY_TO_MANY:
            if model_uuid in [s_model_uuid, t_model_uuid]:
                if model_uuid == s_model_uuid:
                    field_model = t_model
                elif model_uuid == t_model_uuid:
                    field_model = s_model
            else:
                start_node = find_model_node(model_uuid)
                s_end_node = find_model_node(s_model_uuid)
                t_end_node = find_model_node(t_model_uuid)
                s_path = join_search_path(start_node, s_end_node, path=path)
                t_path = join_search_path(start_node, t_end_node, path=path)
                if s_path and t_path:
                    len_s_path, len_t_path = len(s_path), len(t_path)
                    if len_s_path >= len_t_path:
                        field_model = s_model
                    else:
                        field_model = t_model
                else:
                    field_model = None
        elif r_type == RelationshipType.ONE_TO_ONE:
            is_many = False
            if model_uuid in [s_model_uuid, t_model_uuid]:
                if model_uuid == s_model_uuid:
                    field_model = t_model
                    is_source = False
                elif model_uuid == t_model_uuid:
                    field_model = s_model
            else:
                start_node = find_model_node(model_uuid)
                s_end_node = find_model_node(s_model_uuid)
                t_end_node = find_model_node(t_model_uuid)
                s_path = join_search_path(start_node, s_end_node, path=path)
                t_path = join_search_path(start_node, t_end_node, path=path)
                len_s_path, len_t_path = len(s_path), len(t_path)
                if s_path and t_path:
                    if len_s_path >= len_t_path:
                        field_model = s_model
                    else:
                        field_model = t_model
                        is_source = False
                else:
                    field_model = None
        elif r_type == RelationshipType.ONE_TO_MANY:
            if model_uuid in [s_model_uuid, t_model_uuid]:
                if model_uuid == s_model_uuid:
                    field_model = t_model
                    is_source = False
                    is_many = False
                elif model_uuid == t_model_uuid:
                    field_model = s_model
            else:
                start_node = find_model_node(model_uuid)
                s_end_node = find_model_node(s_model_uuid)
                t_end_node = find_model_node(t_model_uuid)
                s_path = join_search_path(start_node, s_end_node, path=path)
                t_path = join_search_path(start_node, t_end_node, path=path)
                len_s_path, len_t_path = len(s_path), len(t_path)
                if s_path and t_path:
                    if len_s_path >= len_t_path:
                        field_model = s_model
                        is_many = False
                    else:
                        field_model = t_model
                        is_source = False
                else:
                    field_model = None
    return field_model, is_source, is_many, r_type


class FinderMixin(object):

    def __init__(self, *args, **kwargs):
        self.find_func = None

    def _find_children(self, component_dict, children, row=False, **kwargs):
        parent_component_uuid = component_dict.pop(
            "parent_component_uuid", None)
        children_container_count = component_dict.pop(
            "children_container_count", {})
        for child in children:
            children_container_count = self.handle_nest_relationship(parent_component_uuid,
                                                                     children_container_count,
                                                                     child["uuid"])
            if row is True and parent_component_uuid and children_container_count:
                child.update({"parent_component_uuid": child["uuid"],
                              "children_container_count": children_container_count})
                self.find_cols(child, **kwargs)
            elif callable(self.find_func):
                self.find_func(child, **kwargs)

    def find_cols(self, component_dict, **kwargs):
        cols = component_dict.get("cols", list())
        self._find_children(component_dict, cols, **kwargs)

    def find_rows(self, component_dict, **kwargs):
        rows = component_dict.get("rows", list())
        self._find_children(component_dict, rows, row=True, **kwargs)

    def find_tabs(self, component_dict, **kwargs):
        tabs = component_dict.get("tabs", list())
        self._find_children(component_dict, tabs, **kwargs)

    def find_panels(self, component_dict, **kwargs):
        panels = component_dict.get("panels", list())
        self._find_children(component_dict, panels, **kwargs)

    def find_container(self, component_dict, **kwargs):
        if callable(self.find_func):
            self.find_func(component_dict, **kwargs)

    def handle_nest_relationship(self, parent_component_uuid, children_container_count, child_uuid):
        """
        将栅格 页签组等作为父容器,把他们的uuid添加进层级关系map中
        """
        if parent_component_uuid and children_container_count:
            if not children_container_count.get(parent_component_uuid, True):
                children_container_count.update({child_uuid: False})
        return children_container_count


class PrintFinder(FinderMixin):

    def __init__(self):
        self.form_visual_tree = dict()
        self.visual_list = list()
        self.container_tree = dict()
        self.model_in_page = dict()
        self.form_count = 0
        self.find_func = self.find_print_container

    def find_form_visual(self, form_dict):
        form_uuid = form_dict.get("uuid")
        children = form_dict.get("children", list())
        for child in children:
            child_uuid = child.get("uuid")
            child_type = child.get("type")
            if child_type == PrintType.VISUAL:
                form_visual_list = self.form_visual_tree.setdefault(
                    form_uuid, list())
                if child_uuid not in form_visual_list:
                    form_visual_list.append(child_uuid)

    def find_print_container(self, print_dict, *args, **kwargs):
        children = print_dict.get("children", list())
        for child in children:
            child_uuid = child.get("uuid")
            child_type = child.get("type")
            if child_type in [
                    PrintType.DATALIST, PrintType.CARDLIST, PrintType.FORM]:
                self.container_tree[child_uuid] = child
                if child_type == PrintType.FORM:
                    self.find_form_visual(child)
                    self.form_count += 1
                    data_source = child.get("data_source", {})
                    data_source_model = data_source.get("model", None)
                    self.model_in_page[child_uuid] = {
                        "component_type": child_type,
                        "model": data_source_model
                    }
            elif child_type == PrintType.TABLE:
                self.find_func(child)
            elif child_type in [PrintType.TEXT, PrintType.IMAGE]:
                self.container_tree[child_uuid] = child
            elif child_type == PrintType.VISUAL:
                if child_uuid not in self.visual_list:
                    self.visual_list.append(child_uuid)


class PrintContainerFinder(FinderMixin):

    def __init__(self):
        self.column_tree = dict()
        self.control_tree = dict()
        self.find_func = self.find_print_control

    def find_print_control(self, print_dict, *args, **kwargs):
        children = print_dict.get("children", list())
        for child in children:
            child_uuid = child.get("uuid")
            child_type = child.get("type")
            if child_type in [PrintType.TEXT, PrintType.IMAGE]:
                self.control_tree[child_uuid] = child
            elif child_type == ComponentType.TABLE:
                self.find_func(child)


class LabelContainerFinder(FinderMixin):
    def __init__(self):
        self.column_tree = dict()
        self.control_tree = dict()
        self.find_func = self.find_print_control
        self.type_conversion = {
                    LabelPrintType.TEXT: PrintType.TEXT,
                    LabelPrintType.MULTILINE_TEXT: PrintType.TEXT,
                    LabelPrintType.BAR_CODE: PrintType.IMAGE,
                    LabelPrintType.QR_CODE: PrintType.IMAGE,
                    LabelPrintType.IMAGE: PrintType.IMAGE
                }

    def find_print_control(self, print_dict, *args, **kwargs):
        children = print_dict.get("label_context", list())
        for child in children:
            child_uuid = child.get("uuid")
            child_type = child.get("label_type")
            if child_type in [LabelPrintType.TEXT, LabelPrintType.MULTILINE_TEXT,
                              LabelPrintType.BAR_CODE, LabelPrintType.QR_CODE, LabelPrintType.IMAGE]:
                child.update({"type": self.type_conversion.get(child_type)})
                self.control_tree[child_uuid] = child


class PrintDatalistFinder(PrintContainerFinder):

    def find_print_column(self, print_dict, *args, **kwargs):
        columns = print_dict.get("columns", list())
        for child in columns:
            child_uuid = child.get("uuid")
            self.column_tree[child_uuid] = child

    def find_print_control(self, print_dict, *args, **kwargs):
        self.find_print_column(print_dict)
        super().find_print_control(print_dict)


class FieldFinder(FinderMixin):

    ALL_INPUT_CONTROL_LIST = ComponentType.INPUT_CONTROL_LIST + \
        ComponentType.R_SELECT_CONTROL_LIST

    def __init__(self, field_in_container):
        self.field_in_container = field_in_container
        self.find_func = self.find_container_field

    def _get_value_editor_field(self, value_editor_dict):
        field_uuid_dict = dict()
        if value_editor_dict:
            value_editor = LemonBaseValueEditor(**value_editor_dict)
            # TODO: BUG 表达式值编辑器里 会有相同 field_uuid 但 path 不同的情况
            for field_uuid, field_info in value_editor.path_columns.items():
                path = field_info.get("path", [])
                field_uuid_dict.update(
                    {field_uuid: {"uuid": field_uuid, "path": path}})
        return field_uuid_dict

    def get_control_field(self, control_dict):
        field_uuid_dict = dict()
        control_type = control_dict.get("type")
        # app_log.info(f"control_dict: {control_dict}")
        if control_type in self.ALL_INPUT_CONTROL_LIST:
            field_info = control_dict.get("field_info", {})
            field_uuid = field_info.get("uuid")
            path = field_info.get("path", [])
            field_uuid_dict[field_uuid] = {"uuid": field_uuid, "path": path}
            editable = control_dict.get("editable", {})
            this_field_uuid_dict = self._get_value_editor_field(editable)
            field_uuid_dict.update(this_field_uuid_dict)
        elif control_type in [ComponentType.TEXT, ComponentType.TAG]:
            data_source = control_dict.get("data_source", {})
            value_editor_dict = data_source.get("valueEdit")
            this_field_uuid_dict = self._get_value_editor_field(
                value_editor_dict)
            field_uuid_dict.update(this_field_uuid_dict)
        elif control_type == ComponentType.IMAGE:
            data_source = control_dict.get("data_source", {})
            data_source_type = data_source.get("type")
            if data_source_type == ImageSourceType.CODE:
                value_editor_dict = data_source.get("code_data")
                this_field_uuid_dict = self._get_value_editor_field(
                    value_editor_dict)
                field_uuid_dict.update(this_field_uuid_dict)
            else:
                field_info = data_source.get("field_info", {})
                field_uuid = field_info.get("uuid")
                path = field_info.get("path")
                field_uuid_dict[field_uuid] = {
                    "uuid": field_uuid, "path": path}
        elif control_type == ComponentType.FILE:
            field_uuid = control_dict.get("field_info", {}).get("uuid")
            path = control_dict.get("field_info", {}).get("path")
            field_uuid_dict[field_uuid] = {"uuid": field_uuid, "path": path}
        elif control_type in [ComponentType.RING_BAR, ComponentType.LINE_BAR]:
            data_source = control_dict.get("data_source", {})
            value_editor_dict = data_source.get("currentvalueEdit")
            this_field_uuid_dict = self._get_value_editor_field(
                value_editor_dict)
            field_uuid_dict.update(this_field_uuid_dict)
        return field_uuid_dict

    def find_container_field(self, component_dict, *args, **kwargs):
        container_type = component_dict.get("type")
        app_log.info(f"container_type: {container_type}")
        if container_type in [
            ComponentType.DATALIST, ComponentType.DATAGRID,
            ComponentType.TREELIST
        ]:
            columns = component_dict.get("columns", [])
            for column in columns:
                control_dict = column.get("component", {})
                control_uuid = control_dict.get("uuid")
                field_uuid_dict = self.get_control_field(control_dict)
                if field_uuid_dict:
                    self.field_in_container.update({
                        control_uuid: field_uuid_dict})
        elif container_type in [
                ComponentType.CARDLIST, ComponentType.FORM,
                ComponentType.CALENDAR, ComponentType.GRID_COL]:
            children = component_dict.get("children", [])
            if container_type == ComponentType.CARDLIST:
                # 卡片列表标题
                card_item = component_dict.get("card_item", {})
                title = card_item.get("title", {})
                this_field_uuid_dict = self._get_value_editor_field(title)
                if this_field_uuid_dict:
                    component_uuid = component_dict.get("uuid")
                    self.field_in_container.update({
                        component_uuid: this_field_uuid_dict})
            for child in children:
                child_type = child.get("type")
                if child_type == ComponentType.GRID_ROW:
                    self.find_cols(child)
                elif child_type == ComponentType.GRID:
                    self.find_rows(child)
                elif child_type == ComponentType.TABS:
                    self.find_tabs(child)
                elif child_type == ComponentType.COLLAPSE:
                    self.find_panels(child)
                elif child_type == ComponentType.SPLIT_PAGE:
                    self.find_panels(child)
                elif child_type == ComponentType.CONTAINER:
                    self.find_container(child)
                else:
                    control_uuid = child.get("uuid")
                    field_uuid_dict = self.get_control_field(child)
                    if field_uuid_dict:
                        self.field_in_container.update({
                            control_uuid: field_uuid_dict})


class PageFinder(FinderMixin):

    def __init__(
            self, model_in_page=None, except_form=True, count_container=False,
            cache_form=False, find_field=False, find_association=False,
            model_list=None, relationship_list=None, field_list=None):
        self.model_in_page = dict() if model_in_page is None else model_in_page
        self.except_form = except_form
        self.count_container = count_container
        self.find_field = find_field
        self.cache_form = cache_form
        self.form_containers = list()
        self.datalist_count = 0
        self.cardlist_count = 0
        self.datagrid_count = 0
        self.treelist_count = 0
        self.form_count = 0
        self.calendar = 0
        self.scan_global_focus = 0  # 全局焦点的scan个数
        self.scan_focus = 0  # 总scan个数
        self.children_container_count = {}  # 标记他的child form是否参与计数
        self.find_func = self._find_func
        self.init_data()
        self.relationship_finder = RelationshipFinder(
            model_list or [], relationship_list or [],
            field_list or []) if find_association else None

    def init_data(self):
        self.print_control = dict()
        self.linkage_relation_table_in_page = dict()
        self.association_dict = dict()
        self.linkage_dict = dict()

    def exit(self):
        self.init_data()
        self.model_in_page = dict()
        self.relationship_finder = None

    def _find_func(self, *args, **kwargs):
        self.find_container_model(*args, **kwargs)

    def count_page_container(self, child_type, settings: dict = None,
                             parent_component_uuid=None, child_uuid=None):
        if self.count_container:
            if child_type == ComponentType.DATALIST:
                self.datalist_count += 1
            elif child_type == ComponentType.CARDLIST:
                self.cardlist_count += 1
            elif child_type == ComponentType.DATAGRID:
                self.datagrid_count += 1
            elif child_type == ComponentType.TREELIST:
                self.treelist_count += 1
            elif child_type == ComponentType.FORM:
                self.children_container_count.update({child_uuid: False})
                if self.children_container_count.get(
                        parent_component_uuid, True):
                    self.form_count += 1
            elif child_type == ComponentType.CALENDAR:
                self.calendar += 1
            elif child_type == ComponentType.SCAN:
                self.scan_focus += 1
                if isinstance(settings, dict) and \
                        settings.get("monitor_type") == ConstantCode.MONITOR_TYPE_NOT_NEED_FOCUS:
                    self.scan_global_focus += 1

    def update_model_in_page(
            self, child, data_source_type, model_uuid, path=None,
            is_outer_container=None, memory_storage=False):
        if data_source_type in [
                DataSourceType.ASSOCIATION_MEMORY,
                DataSourceType.FORM_ASSOCIATION_MEMORY]:
            memory_storage = True
        child_type = child.get("type")
        child_uuid = child.get("uuid")
        path = path or []
        self.model_in_page[child_uuid] = {
            "component_type": child_type, "type": data_source_type,
            "model": model_uuid, "path": path, "field": dict()
        }
        if is_outer_container is True:
            self.model_in_page[child_uuid].update({
                "is_outer_container": True
            })
        self.model_in_page[child_uuid].update(
            {"memory_storage": memory_storage})
        if self.find_field:
            field_in_container = self.model_in_page[child_uuid]["field"]
            field_finder = FieldFinder(field_in_container)
            field_finder.find_func(child)

    def find_association_model(self, association, model_uuid):
        associaton_model_uuid = None
        if self.relationship_finder is not None:
            associaton_model, is_source, is_many, r_type = self.relationship_finder.lemon_association(
                association, model_uuid)
            if associaton_model is None:
                return None
            associaton_model_uuid = associaton_model.uuid
        return associaton_model_uuid

    def find_relation_table_model(
            self, component_dict, parent_model_uuid, data_source_type):
        data_source = component_dict.get("data_source", {})
        association_info = data_source.get("association", {})
        path = data_source.get("path", [])
        if isinstance(association_info, dict):
            association = association_info.get("uuid")
        else:
            association = association_info
        data_source_model = self.find_association_model(
            association, parent_model_uuid)
        self.update_model_in_page(
            component_dict, data_source_type, data_source_model, path=path)
        return data_source_model

    def find_container_model(self, component_dict, parent_model_uuid=None):
        children: List[Dict] = component_dict.get("children", list())
        parent_component_uuid = component_dict.get("uuid", "")
        self.children_container_count.setdefault(parent_component_uuid, True)
        form_children = list()  # 记录下当前的表单
        is_outer_container = parent_model_uuid is None  # 是最外层组件

        for child in children:
            if child is None:
                continue
            child_uuid = child.get("uuid")
            child_type = child.get("type")
            settings: dict = child.get("settings")
            # 统计页面上有多少数据容器， 如果需要统计的话
            if not self.children_container_count.get(parent_component_uuid, True):
                self.children_container_count.update({child_uuid: False})
            self.count_page_container(child_type, settings, child_uuid=child_uuid,
                                      parent_component_uuid=parent_component_uuid)
            if child_type in [
                    ComponentType.DATALIST, ComponentType.CARDLIST,
                    ComponentType.DATAGRID]:
                data_source = child.get("data_source", {})
                data_source_type = data_source.get(
                    "type", DataSourceType.MODEL_WITH_EDITOR)
                if parent_model_uuid and data_source_type in [
                        DataSourceType.ASSOCIATION, DataSourceType.ASSOCIATION_MEMORY]:
                    data_source_model = self.find_relation_table_model(
                        child, parent_model_uuid, data_source_type)
                else:
                    data_source_model = data_source.get("model")
                    self.update_model_in_page(
                        child, data_source_type, data_source_model)
                subtable = child.get("subtable", [])
                for table in subtable:
                    table_type = table.get("type")
                    if table_type in [ComponentType.DATALIST, ComponentType.CARDLIST]:
                        sub_data_source = table.get("data_source", {})
                        sub_data_source_type = sub_data_source.get(
                            "type", DataSourceType.MODEL_WITH_EDITOR)
                        association = sub_data_source.get("association")
                        path = sub_data_source.get("path", [])
                        if isinstance(association, dict):
                            association = association.get("uuid")
                        # 运行时，没有传入 relationship_list 不能计算出关联模型 sub_data_source_model 为 None
                        # 但也应该加入到 self.model_in_page 中
                        sub_data_source_model = self.find_association_model(
                            association, data_source_model)
                        self.update_model_in_page(
                            table, sub_data_source_type, sub_data_source_model, path=path)
                self.find_func(child, parent_model_uuid=data_source_model)
            elif child_type == ComponentType.FORM:
                form_children.append(child)
                if self.cache_form:
                    self.form_containers.append(child)
                # 注释了才能找到表单下的打印组件
                # if not self.except_form:
                data_source = child.get("data_source", {})
                data_source_type = data_source.get(
                    "type", DataSourceType.FORM_WITH_EDITOR)
                if data_source_type == DataSourceType.FORM_LINKAGE:
                    data_source_component = data_source.get(
                        "component")  # 记录的是联动的那个容器组件的UUID
                    app_log.info(
                        f"data_source_component: {data_source_component}")
                    self.update_model_in_page(
                        child, data_source_type, data_source_component, is_outer_container=is_outer_container)
                elif data_source_type in [
                        DataSourceType.FORM_ASSOCIATION,
                        DataSourceType.FORM_ASSOCIATION_MEMORY]:
                    data_source_model = data_source.get("model")
                    association = data_source.get("association")
                    path = data_source.get("path", [])
                    if parent_model_uuid:
                        # 运行时，没有传入 relationship_list 不能计算出关联模型 data_source_model 为 None
                        # 但也应该加入到 self.model_in_page 中
                        data_source_model = self.find_association_model(
                            association, parent_model_uuid)
                        self.update_model_in_page(
                            child, data_source_type, data_source_model, path=path,
                            is_outer_container=is_outer_container)
                    else:
                        self.update_model_in_page(
                            child, data_source_type, data_source_model, path=path,
                            is_outer_container=is_outer_container)
                else:
                    data_source_model = data_source.get("model")
                    self.update_model_in_page(
                        child, data_source_type, data_source_model, is_outer_container=is_outer_container)
            elif child_type == ComponentType.SCAN:
                data_source = child.get("data_source", {})
                data_source_type = data_source.get("type")
                data_source_model = data_source.get("model")
                if data_source_model is not None:
                    self.update_model_in_page(
                        child, data_source_type, data_source_model)
            elif child_type == ComponentType.GRID_COL:
                self.find_func(child)
            elif child_type == ComponentType.GRID_ROW:
                child.update({"parent_component_uuid": parent_component_uuid,
                              "children_container_count": self.children_container_count})
                self.find_cols(child)
            elif child_type == ComponentType.GRID:
                child.update({"parent_component_uuid": parent_component_uuid,
                              "children_container_count": self.children_container_count})
                kwargs = {"parent_model_uuid": parent_model_uuid}
                self.find_rows(child, **kwargs)
            elif child_type == ComponentType.TABS:
                child.update({"parent_component_uuid": parent_component_uuid,
                              "children_container_count": self.children_container_count})
                kwargs = {"parent_model_uuid": parent_model_uuid}
                self.find_tabs(child, **kwargs)
            elif child_type == ComponentType.COLLAPSE:
                child.update({"parent_component_uuid": parent_component_uuid,
                              "children_container_count": self.children_container_count})
                kwargs = {"parent_model_uuid": parent_model_uuid}
                self.find_panels(child, **kwargs)
            elif child_type == ComponentType.SPLIT_PAGE:
                child.update({"parent_component_uuid": parent_component_uuid,
                              "children_container_count": self.children_container_count})
                kwargs = {"parent_model_uuid": parent_model_uuid}
                self.find_panels(child, **kwargs)
            elif child_type == ComponentType.CONTAINER:
                kwargs = {"parent_model_uuid": parent_model_uuid}
                self.find_container(child, **kwargs)
            elif child_type == ComponentType.PRINT:
                if isinstance(child, dict):
                    self.print_control.update({child_uuid: child})
            elif child_type == ComponentType.CALENDAR:
                data_source = child.get("data_source", {})
                data_source_type = data_source.get(
                    "type", DataSourceType.MODEL_WITH_EDITOR)
                if data_source_type in [
                        DataSourceType.ASSOCIATION,
                        DataSourceType.ASSOCIATION_MEMORY]:
                    self.find_relation_table_model(
                        child, parent_model_uuid, data_source_type)
                else:
                    data_source_model = data_source.get("model")
                    if data_source_model is not None:
                        self.update_model_in_page(
                            child, data_source_type, data_source_model)
            elif child_type == ComponentType.TREE:
                data_source = child.get("data_source", {})
                data_source_type = data_source.get(
                    "type", DataSourceType.MODEL_WITH_EDITOR)
                data_source_model = data_source.get("model")
                self.update_model_in_page(
                    child, data_source_type, data_source_model)
            elif child_type == ComponentType.TREELIST:
                data_source = child.get("data_source", {})
                data_source_type = data_source.get(
                    "type", DataSourceType.MODEL_WITH_EDITOR)
                if parent_model_uuid and data_source_type in [
                        DataSourceType.ASSOCIATION, DataSourceType.ASSOCIATION_MEMORY]:
                    data_source_model = self.find_relation_table_model(
                        child, parent_model_uuid, data_source_type)
                else:
                    data_source_model = data_source.get("model")
                    self.update_model_in_page(
                        child, data_source_type, data_source_model)

        # 遍历表单，寻找表单下的子容器
        for child in form_children:
            child_uuid = child.get("uuid")
            data_source = child.get("data_source", {})
            data_source_type = data_source.get(
                "type", DataSourceType.FORM_WITH_EDITOR)
            if data_source_type == DataSourceType.FORM_LINKAGE:
                linkage_component = data_source.get("component")
                linkage_model_data = self.model_in_page.get(
                    linkage_component, dict())
                linkage_model_uuid = linkage_model_data.get("model")
                linkage_path = linkage_model_data.get("path", [])
                memory_storage = linkage_model_data.get(
                    "memory_storage", False)
                if linkage_model_uuid:
                    self.model_in_page[child_uuid].update({
                        "model": linkage_model_uuid, "path": linkage_path,
                        "memory_storage": memory_storage})
                else:
                    self.model_in_page[child_uuid].update({
                        "model": None, "path": [],
                        "memory_storage": memory_storage})
            data_source_model = self.model_in_page[child_uuid]["model"]
            self.find_func(child, parent_model_uuid=data_source_model)


class RelationshipFinder(object):

    def __init__(self, model_list, relationship_list, field_list):
        self.model_uuid_map: Dict[AnyStr, ModelBasic] = dict()
        self.relationship_basic_map: Dict[AnyStr, RelationshipBasic] = dict()
        self.node_uuid_map: Dict[AnyStr, ModelNode] = dict()
        self.field_uuid_map = dict()
        self.field_model_uuid_map: Dict[AnyStr, ModelBasic] = dict()
        self.relationship_uuid_map = dict()
        self.init_model_list(model_list)
        self.init_relationship_list(relationship_list)
        self.init_field_list(field_list)

    def lemon_field(self, field_uuid):
        func = partial(base_lemon_field,
                       field_model_uuid_map=self.field_model_uuid_map)
        return func(field_uuid)

    def lemon_model(self, model_uuid) -> ModelBasic:
        func = partial(base_lemon_model, model_uuid_map=self.model_uuid_map)
        return func(model_uuid)

    def lemon_model_node(self, model_uuid):
        func = partial(base_lemon_model_node, node_uuid_map=self.node_uuid_map)
        return func(model_uuid)

    def lemon_association(self, association, model_uuid, path=None):
        func = partial(
            base_lemon_association, node_uuid_map=self.node_uuid_map,
            relationship_uuid_map=self.relationship_uuid_map)
        return func(association, model_uuid, path=path)

    def init_model_list(self, model_list):
        for model_dict in model_list:
            model = ModelBasic(model_dict)
            self.model_uuid_map.update({model.uuid: model})
            model_node = ModelNode(model)
            self.node_uuid_map.update({model.uuid: model_node})

    def init_relationship_list(self, relationship_list):
        for relationship_dict in relationship_list:
            relationship_basic = RelationshipBasic(relationship_dict)
            relationship_uuid = relationship_basic.uuid
            relationship_type = relationship_basic.type
            relationship_name = relationship_basic.name
            relationship_display_name = relationship_basic.display_name
            target_uuid = relationship_basic.target_model
            source_uuid = relationship_basic.source_model

            t_model_node = self.node_uuid_map.get(target_uuid)
            s_model_node = self.node_uuid_map.get(source_uuid)
            if not t_model_node or not s_model_node:
                continue
            target_node = TargetNode(
                t_model_node, relationship_type, name=relationship_name, display_name=relationship_display_name,
                r_uuid=relationship_uuid)
            source_node = SourceNode(
                s_model_node, relationship_type, name=relationship_name, display_name=relationship_display_name,
                r_uuid=relationship_uuid)
            t_model_node.add_nodes(source_node)
            s_model_node.add_nodes(target_node)
            t_model = t_model_node.model
            s_model = s_model_node.model
            self.relationship_uuid_map.update({
                relationship_uuid: (s_model, t_model, relationship_type, 0, 0)
            })
            self.relationship_basic_map.update(
                {relationship_uuid: relationship_basic})

            # 自关联 增加了两个隐藏 relationship_uuid 以确保运行时能正确 join
            if source_uuid == target_uuid:
                relationship_uuid_source = new_relationship_uuid(
                    relationship_uuid)
                relationship_uuid_target = new_relationship_uuid(
                    relationship_uuid, from_source=False)
                # 第一个 1 是否为 自关联
                # 第二个 1 是否为 从外键端开始
                self.relationship_uuid_map.update({
                    relationship_uuid_source: (s_model, t_model, relationship_type, 1, 1),
                    relationship_uuid_target: (
                        s_model, t_model, relationship_type, 1, 0)
                })
                self.relationship_basic_map.update({
                    relationship_uuid_source: relationship_basic,
                    relationship_uuid_target: relationship_basic
                })

    def init_field_list(self, field_list):
        for field_dict in field_list:
            model_field = ModelField(field_dict)
            field_model_uuid = field_dict.get("model_uuid")
            field_uuid = field_dict.get("field_uuid")
            field_model = self.model_uuid_map.get(field_model_uuid)
            field_model.add_column(model_field)
            self.field_model_uuid_map.update({field_uuid: field_model})
            self.field_uuid_map.update({field_uuid: model_field})

    def build_path_name(self, model_basic: ModelBasic, model_names, relationship_names, concat_str="/"):
        path_names = []
        for index, name in enumerate(model_names):
            r_name = relationship_names[index]
            path_names.append(name + "(" + r_name + ")")
        path_name = concat_str.join(path_names)
        if path_name:
            path_name = model_basic.verbose_name + concat_str + path_name
        else:
            path_name = model_basic.verbose_name
        return path_name

    def find_model_join_path(self, model_uuid, other_model_uuid):
        start_node = self.node_uuid_map.get(model_uuid)
        end_node = self.node_uuid_map.get(other_model_uuid)
        join_path = join_search_path(start_node, end_node)
        return join_path

    def find_field_path(self, model_uuid, field_uuid_dict):
        if isinstance(field_uuid_dict, list):
            real_field_uuid_dict = dict()
            for field_uuid in field_uuid_dict:
                real_field_uuid_dict.update(
                    {field_uuid: {"uuid": field_uuid, "path": []}})
        else:
            real_field_uuid_dict = field_uuid_dict
        path_dict = dict()
        start_node = self.node_uuid_map.get(model_uuid)
        if not start_node:
            return path_dict
        for control_field_uuid, field_info in real_field_uuid_dict.items():
            field_uuid = field_info.get("uuid")
            if not control_field_uuid:
                continue
            model_names, relationship_names = [], []
            field_model = self.field_model_uuid_map.get(field_uuid)
            if not field_model:
                # path_dict.update({control_field_uuid: "*字段不存在*"})
                continue
            path = field_info.get("path", [])
            if path and isinstance(path, list):
                last_model_uuid = model_uuid
                for t_path in path:
                    relationship_basic = self.relationship_basic_map.get(
                        t_path)
                    if not relationship_basic:
                        break
                    s_model_uuid = relationship_basic.source_model
                    t_model_uuid = relationship_basic.target_model
                    s_model = self.lemon_model(s_model_uuid)
                    t_model = self.lemon_model(t_model_uuid)
                    if last_model_uuid in [s_model_uuid, t_model_uuid]:
                        if last_model_uuid == s_model_uuid:
                            model_names.append(t_model.verbose_name)
                            last_model_uuid = t_model_uuid
                        else:
                            model_names.append(s_model.verbose_name)
                            last_model_uuid = s_model_uuid
                        relationship_names.append(
                            relationship_basic.verbose_name)
                    else:
                        break
                # 关联被删除时需提示用户
                if not relationship_names:
                    relationship_names.append("*关联关系不存在*")
                    model_names.append(field_model.display_name)
                path_name = self.build_path_name(
                    start_node.model, model_names, relationship_names)
                path_dict.update({control_field_uuid: path_name})
            else:
                field_model_uuid = field_model.uuid
                end_node = self.node_uuid_map.get(field_model_uuid)
                if field_model_uuid == model_uuid:
                    path_name = self.build_path_name(
                        start_node.model, model_names, relationship_names)
                    path_dict.update({control_field_uuid: path_name})
                else:
                    app_log.info(
                        f"start_node: {start_node}, end_node: {end_node}")
                    join_path = join_search_path(start_node, end_node)
                    app_log.info(f"join_path: {join_path}")
                    if join_path:
                        for path in join_path:
                            if isinstance(path, RelationNode):
                                model_names.append(
                                    path.node.model.verbose_name)
                                relationship_names.append(path.verbose_name)
                        path_name = self.build_path_name(
                            start_node.model, model_names, relationship_names)
                        path_dict.update({control_field_uuid: path_name})
                    else:
                        path_dict.update({control_field_uuid: "*关联不存在*"})
        return path_dict

    '''
    @description: 获取开始节点到目标节点的所有路径
    @param {*} self
    @param {*} start_node
    @param {*} end_node
    @param {*} exist_relations
    @param {*} node_list
    @param {*} all_path
    @return {*}
    @author: lv.jimin
    '''

    def find_relation_path(self, start_node, end_node, exist_relations, node_list, all_path, depth, abandon):
        depth += 1
        if depth > 10:
            return False
        if hasattr(start_node, "node"):
            model_node = start_node.node
        else:
            model_node = start_node
        model_uuid = model_node.model.uuid
        if exist_relations.get(model_uuid):
            #    all_path.append(node_list)
            return False
        else:
            exist_relations[model_uuid] = 1
        if model_node == end_node:
            # for index, node in enumerate(node_list):
            #     abandon[node.r_uuid] += -2 * index
            all_path.append(node_list)
        else:
            for relation_node in model_node.relation_nodes:
                # r_uuid = relation_node.r_uuid
                # abandon_num = abandon.get(r_uuid)
                # if abandon_num:
                #     if abandon_num > 30:
                #         app_log.info(start_node)
                #         continue
                #     else:
                #         abandon[r_uuid] += 1
                # else:
                #     abandon[r_uuid] = 1
                copy_node_list = node_list[::]
                copy_relations = {}
                copy_relations.update(exist_relations)
                copy_node_list.append(relation_node)
                self.find_relation_path(
                    relation_node, end_node, copy_relations, copy_node_list, all_path, depth, abandon)

    def get_model_path_list(self, source_model, target_model):
        start_node = self.lemon_model_node(source_model)
        end_node = self.lemon_model_node(target_model)
        path_list = []
        abandon = defaultdict(int)
        # for relation_node in end_node.relation_nodes:
        #     r_uuid = relation_node.r_uuid
        #     if hasattr(relation_node, "node"):
        #         relation_node = relation_node.node
        #     abandon[r_uuid] += -100
        #     for c_relation_node in relation_node.relation_nodes:
        #         r_uuid = c_relation_node.r_uuid
        #         abandon[r_uuid] += -50
        self.find_relation_path(start_node, end_node, dict(
        ), list(), path_list, depth=0, abandon=abandon)
        relation_path_list = []
        for node_path in path_list:
            relation_id_list = []
            path_name = start_node.model.display_name
            for node in node_path:
                relation_id_list.append(node.r_uuid)
                r_display_name = node.display_name
                path_name = path_name + \
                    f"({r_display_name})/{node.node.model.display_name}"
            # 只列出不是直接关联的路径
            if len(relation_id_list) < 2:
                continue
            path_info = {"path": relation_id_list,
                         "name": path_name, "length": len(relation_id_list)}
            relation_path_list.append(path_info)
        relation_path_list = sorted(
            relation_path_list, key=itemgetter("length", "name"))
        return relation_path_list

    # 深度优先, 找到目标节点后, 退回到上一分叉, 进行下一选择

    def traverse(self, start, end, map_, map_count, relationship_map):

        # 当前路径情况下, 约束使用情况
        cur_count = {}

        main_stack = [start]
        # 相邻节点
        side_stack = [map_[start]]

        def list_sort(v1, v2):
            list1 = [v1, v2]
            list1.sort()
            return "_".join(list1)

        def pop_stack(main_stack, side_stack):
            main_stack.pop()
            if len(side_stack) > 0:
                side_stack.pop()

        result = []
        path_dict = {"child_ship": {}}
        while len(main_stack) > 0:
            side_top = side_stack.pop()
            if len(side_top) > 0:
                last_top = main_stack[-1]
                main_top = side_top[0]
                main_stack.append(main_top)

                route = list_sort(last_top, main_top)
                cur_count.setdefault(route, 0)
                # 节点间连线多于一条, 在移动到下一节点之后, 认为可以该节点仍可到达
                if len(map_count[route]) > 1 and cur_count.get(route, 1) < len(map_count[route]):
                    cur_count[route] += 1
                    side_stack.append(side_top[:])
                else:
                    side_stack.append(side_top[1:])
                if len(map_[main_top]) > 0:
                    side_top = []
                    for v in map_[main_top]:
                        if v not in main_stack:
                            side_top.append(v)
                    side_stack.append(side_top)
            else:
                main_stack.pop()

            if len(main_stack) > 0 and main_stack[-1] == end:

                # 返回的关联uuid列表
                rel_list = []
                for i in range(len(main_stack) - 1):
                    behind = main_stack[i]
                    front = main_stack[i+1]
                    route = list_sort(behind, front)

                    # 找到关联路径的uuid
                    if len(relationship_map[route][1]) > 0:
                        r = relationship_map[route][1].pop()
                        relationship_map[route][0].append(r)
                    else:
                        relationship_map[route][1].extend(
                            relationship_map[route][0])
                        relationship_map[route][0] = []
                        r = relationship_map[route][1].pop()
                        relationship_map[route][0].append(r)
                    rel_list.append(r)
                app_log.info(rel_list)
                # 返回的路径名
                res = ""
                for index in range(len(main_stack)):
                    model_uuid = main_stack[index]
                    model_name = self.model_uuid_map[model_uuid].display_name
                    relationship_name = ""
                    if index < len(rel_list):
                        relationship_uuid = rel_list[index]
                        relationship_name = self.relationship_basic_map[relationship_uuid].display_name
                        relationship_name = "(" + relationship_name + ")/"
                    res += model_name
                    res += relationship_name
                app_log.info(res)
                p_length = len(rel_list)
                if p_length > 1:
                    last_model_uuid = main_stack[0]
                    result.append(
                        {"name": res, "path": rel_list, "length": p_length})
                    child_ship = path_dict.get("child_ship")
                    relationship_info = {}
                    for relationship_uuid in rel_list:
                        relationship_info = child_ship.get(relationship_uuid)
                        if not relationship_info:
                            ship_info = self.relationship_basic_map[relationship_uuid]
                            source_model = ship_info.source_model
                            target_model = ship_info.target_model
                            source_model_name = self.model_uuid_map[source_model].display_name
                            target_model_name = self.model_uuid_map[target_model].display_name
                            relationship_name = ship_info.display_name
                            if source_model == last_model_uuid:
                                start_model = source_model_name
                                end_model = target_model_name
                                last_model_uuid = target_model
                            else:
                                start_model = target_model_name
                                end_model = source_model_name
                                last_model_uuid = source_model
                            relationship_info = {"child_ship": {}, "start_model": start_model,
                                                 "end_model": end_model, "relationship_name": relationship_name}
                            child_ship[relationship_uuid] = relationship_info
                        child_ship = relationship_info.get("child_ship")
                    relationship_info["path"] = rel_list
                pop_stack(main_stack, side_stack)
        self.handle_model_path(path_dict)
        result = sorted(result, key=itemgetter("length", "name"))
        length = len(result)
        app_log.info(length)
        return result

    def sort_model_path(self, relationship_info):
        self.handle_model_path(relationship_info)
        return relationship_info.get("relationship_name")

    def handle_model_path(self, relationship_info):
        child_ship = relationship_info.get("child_ship")
        child_ship_list = sorted(child_ship.values(), key=self.sort_model_path)
        relationship_info["child_ship_list"] = child_ship_list


class RelationshipData(Json):

    def __init__(
            self, uuid, name, relationship_type, frontref, backref,
            source_model, target_model, display_name="", is_system=False,
            system_relationship=False, **kwargs):
        self.description = ""
        self.display_name = display_name
        self.frontref = frontref
        self.backref = backref
        self.source_model = source_model
        self.target_model = target_model
        self.source_model_on_delete = "0"
        self.target_model_on_delete = "0"
        self.extra = {"source_required": False, "target_required": False}
        self.is_extension = False
        self.is_system = is_system
        self.system_relationship = system_relationship
        self.init(uuid, name, relationship_type, **kwargs)

    def init(self, uuid, name, relationship_type, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = str(relationship_type)
        self.to_field_name = kwargs.get("to_field_name", "id")


class RelationshipBasicData(RelationshipData):

    def init(self, uuid, name, relationship_type, **kwargs):
        self.relationship_uuid = uuid
        self.relationship_name = name
        self.relationship_type = int(relationship_type)


def create_system_relationship_data(
        model_uuid, model_name, field_info: dict, basic_data=False):
    field_name = field_info.get("field_name")
    field_display_name = field_info.get("display_name")
    rel_table: Enum = field_info.get("rel_table")
    relationship_type = field_info.get("relationship_type", RelationshipType.ONE_TO_MANY)
    r_uuid = new_relationship_uuid(model_uuid, field_name)
    r_name = "_".join([model_name, field_display_name])
    # frontref = field_display_name
    frontref = field_name
    backref = "_".join([field_display_name, model_name])
    data_class = RelationshipBasicData if basic_data else RelationshipData
    r_data = data_class(
        r_uuid, r_name, relationship_type,
        frontref, backref, model_uuid, rel_table.table_name.value,
        display_name=r_name, is_system=True, system_relationship=True)
    return r_data


def create_system_relationships(models):
    system_relationships = []
    for model in models:
        model_uuid = model.get("uuid")
        model_name = model.get("name")
        for field_info in SystemField.RELATIONSHIP_FIELD:
            r_data = create_system_relationship_data(
                model_uuid, model_name, field_info)
            system_relationships.append(r_data.to_dict())
    return system_relationships


def list_system_relationship(
        models, as_dict=True, basic_data=True, reversed=False) -> list:
    """
    reversed: 当前model是否为用户的表
    """
    system_relationship_list = []
    for model_dict in models:
        if not isinstance(model_dict, dict):
            model_dict = model_dict.to_dict()
        model_uuid = model_dict.get("model_uuid", "")
        if model_uuid in SystemTable.UUIDS:
            continue
        model_name = model_dict.get("model_name", "")
        for field_info in SystemField.RELATIONSHIP_FIELD:
            rel_table: Enum = field_info.get("rel_table")
            r_data = create_system_relationship_data(
                model_uuid, model_name, field_info, basic_data=basic_data)
            if not as_dict:
                system_relationship_list.append(r_data)
                continue
            copy_relationship_data = dict()
            copy_relationship_data.update(r_data.to_dict())
            if reversed:
                display_name = model_dict.get("display_name", "")
                name_field = model_dict.get("name_field", "")
                data_name = model_dict.get("data_name", "")
                copy_relationship_data.update({
                    "relationship_display_name": r_data.display_name,
                    "model_uuid": model_uuid,
                    "model_name": model_name,
                    "display_name": display_name,
                    "name_field": name_field,
                    "data_name": data_name,
                    "is_source": 1,
                    "is_many": 1,
                    "model_is_many": 1,
                    "relationship_type": r_data.relationship_type
                })
            else:
                copy_relationship_data.update({
                    "relationship_display_name": r_data.display_name,
                    "model_uuid": rel_table.table_name.value,
                    "model_name": rel_table.display_name.value,
                    "display_name": rel_table.display_name.value,
                    "name_field": "",
                    "data_name": "",
                    "is_source": 0,
                    "is_many": 0,
                    "model_is_many": 0,
                    "relationship_type": r_data.relationship_type
                })
            system_relationship_list.append(copy_relationship_data)
    return system_relationship_list


class LemonIterQueue(asyncio.Queue, collections.abc.AsyncIterator):

    async def __anext__(self):
        try:
            message = await asyncio.wait_for(self.get(), timeout=None)
        except BaseException:
            # 有可能出现 asyncio 的 CancelError 导致 event loop close ？
            # 暂时先改成 BaseException
            app_log.error(traceback.format_exc())
        else:
            return message


def calc_action(cu, tags, target_action):
    if not tags:
        return True
    container_tag_uuid = set()
    for t in tags:
        tag_uuid = t.get("tag_uuid")
        action = cu.all_tag_permission.get(tag_uuid)
        if action is None:
            continue
        action = int(action, 2)
        if action & target_action == target_action:
            container_tag_uuid.add(tag_uuid)
    if not container_tag_uuid:
        return True

    result = False
    for _, role_permission in cu.roles_permission.items():
        for tag_uuid, action in role_permission.items():
            # 检查角色是否选择了组件tag
            if tag_uuid not in container_tag_uuid:
                continue
            result = True
        if result:
            # 任一角色有权限, 认为有权限
            return True
    return result


def check_item_action(item, current_user):
    permission_config = item.get("permission_config", {})
    enable = permission_config.get("enable")
    tags = permission_config.get("tag", [])
    if not all([enable, tags]):
        return True
    target_action = TagPermissionAction.VISIBLE
    return calc_action(current_user, tags, target_action)


async def process_navigation_item(
        navigation_items: list, engine, need_permission_check=False,
        modulerole=None, url_dict=None, value_editor_func=None):
    url_dict = url_dict or dict()
    items_split_by_level = dict()
    modulerole = modulerole or dict()
    for item in navigation_items:
        level = item.get("level")
        item_uuid = item.get("item_uuid")
        this_level_items = items_split_by_level.setdefault(level, dict())
        this_level_items.update({item_uuid: item})
    items_sort_obj = sorted(items_split_by_level.items(),
                            key=lambda x: x, reverse=True)
    items_split_by_level = OrderedDict(items_sort_obj)

    start_step = 0
    items_split_len = len(items_split_by_level)
    for level, items in items_split_by_level.items():
        start_step += 1
        for item_uuid in list(items.keys()):
            item = items[item_uuid]
            master = item.get("master")
            page_uuid = item.get("page_uuid")
            item["custom_url"] = url_dict.get(page_uuid, "")
            role_names = modulerole.get(page_uuid, list())
            item["role_names"] = role_names

            p_item_children = None
            if master:
                p_level = level - 1  # 要求level连续
                p_item = items_split_by_level.get(p_level, {}).get(master, {})
                if p_item:
                    p_item_children = p_item.setdefault("children", list())

            if need_permission_check:
                # 检查标签权限
                own_tag_permission = check_item_action(item, engine.system.current_user)
                if not own_tag_permission:
                    item["no_permission"] = True
                # 末端导航检查权限
                if own_tag_permission and page_uuid and not isinstance(item.get("children"), list):
                    # 如果有page_uuid且没有children -> 末端导航, 进行检查权限
                    try:
                        engine.pm.predicate(page_uuid, 16)
                    except Exception:
                        item["no_permission"] = True
                elif isinstance(item.get("children"), list) and not item.get("children"):
                    # 非末端, 但全部子节点无权限
                    item["no_permission"] = True

            if not item.get("no_permission") and master:
                if isinstance(p_item_children, list):
                    p_item_children.append(item)
            if start_step == items_split_len:  # 最顶级导航
                # 自身也是末端导航, 但无权限
                if item.get("no_permission"):
                    items.pop(item_uuid)
                # 自身不是末端, 但全部children无权限
                elif isinstance(item.get("children"), list) and not item.get("children"):
                    items.pop(item_uuid)
            param = item.get("param")
            if isinstance(param, dict):
                try:
                    if value_editor_func:
                        param_value = await value_editor_func(param)
                    else:
                        param_value = LemonBaseValueEditor(**param).value
                except Exception:
                    format_error = traceback.format_exc()
                    app_log.error(format_error)
                item["param_value"] = param_value
            if item.get("children"):
                children: list = item["children"]
                children.sort(key=itemgetter("order"))
                for child in children:
                    param = child.get("param")
                    if isinstance(param, dict):
                        try:
                            if value_editor_func:
                                param_value = await value_editor_func(param)
                            else:
                                param_value = LemonBaseValueEditor(**param).value
                        except Exception:
                            format_error = traceback.format_exc()
                            app_log.error(format_error)
                        child["param_value"] = param_value

    attrs = list()
    if items_split_by_level:
        top_level, navigation_items = items_split_by_level.popitem()
        attrs = list(navigation_items.values())
        attrs.sort(key=itemgetter("order"))
    return attrs


def new_relationship_uuid(relationship_uuid, *args, from_source=True):
    # 自关联时, 需要生成新的 relationship_uuid 以便用户选择不同方向的关联
    # 自关联默认生成的 relationship_uuid 不能区分关联方向
    source_key = "*" if from_source else "1"
    add_key = "_".join(args) if args else ""
    key_string = relationship_uuid + source_key + add_key
    relationship_uuid_encode = key_string.encode()
    relationship_uuid = hashlib.md5(relationship_uuid_encode).hexdigest()
    return relationship_uuid


def replace_relationship_uuid(
        relationship_dict: dict, model_class, from_source=True, keep=False, replace=True):
    source_model = relationship_dict.get(model_class.source_model.name)
    target_model = relationship_dict.get(model_class.target_model.name)
    relationship_uuid_new = None
    if source_model == target_model:
        relationship_uuid_key = model_class.relationship_uuid.name
        relationship_uuid = relationship_dict.get(relationship_uuid_key)
        if not relationship_uuid:
            relationship_uuid_key = "uuid"
            relationship_uuid = relationship_dict.get(relationship_uuid_key)
        relationship_uuid_new = new_relationship_uuid(
            relationship_uuid, from_source=from_source)
        if replace:
            relationship_dict[relationship_uuid_key] = relationship_uuid_new
        if keep:
            relationship_dict["source_relationship_uuid"] = relationship_uuid
    return relationship_uuid_new


def get_sys_table_rel_table_name(model_uuid, table_dict):
    if real_table_name := table_dict.get(model_uuid):
        return real_table_name
    return model_uuid


def make_runtime_table_name(app_uuid, app_revision, table_name):
    app_uuid = app_uuid or get_runtime_app_uuid()
    if not app_uuid:
        raise "app_uuid is required, sth wrong with app_uuid"
    if table_name in [t.meta_table_name.value for t in SystemTable.APP_TABLE_NAME]:
        return "_".join([app_uuid[:16], table_name])
    # TODO 可以把租户表的make_name的逻辑在这儿统一
    # elif table_name in [t.meta_table_name.value for t in SystemTable.APP_TENANT_TABLE_NAME]:
    #     return "_".join([app_uuid[:16], str(app_revision), table_name])
    else:
        return "_".join([app_uuid[:16], str(app_revision), table_name])


def get_runtime_table_copy(model, app_uuid=None, tenant_uuid=None):
    if not app_uuid or not tenant_uuid:
        current_user = LemonContextVar.current_user.get()
        if current_user:
            if not app_uuid:
                app_uuid = current_user.app_uuid
            if not tenant_uuid:
                tenant_uuid = current_user.tenant_id
        else:
            return None
    table_name = model._meta.class_table_name
    if table_name in [t.meta_table_name.value for t in SystemTable.APP_TENANT_TABLE_NAME]:
        real_table_name = make_tenant_user_table_name(table_name, tenant_uuid)
    else:
        # XXX 认为这儿需要构造的表名, 都不会有需要app_revision的情况, 所以直接给了None
        real_table_name = make_runtime_table_name(app_uuid, None, table_name)
    return MODEL_COPY_DICT.get(real_table_name, {}).get("model", model)


async def gen_model_graph_depend_model(app_uuid, engine, async_select=True, sys_table_dict=None):
    from apps.entity import EnumTable as model
    sys_table_dict = sys_table_dict or dict()
    func_list = list()
    module_list = engine.access.list_module_by_app_uuid(
        app_uuid, with_sys=True)
    func_list.append(module_list)
    model_list = engine.access.list_model_by_app_uuid(
        app_uuid, as_dict=False, with_sys=True, fields="ALL")
    func_list.append(model_list)
    index_list = engine.access.list_index_by_app_uuid(app_uuid)
    func_list.append(index_list)
    field_list = engine.access.list_app_field_for_publish(
        app_uuid, need_sys=True)
    func_list.append(field_list)
    relationship_list = engine.access.list_relationship_by_app_uuid(
        app_uuid, with_sys=True, as_dict=False)
    func_list.append(relationship_list)
    fields = (
        model.app_uuid,
        model.module_uuid,
        model.document_uuid,
        model.enum_uuid,
        model.enum_name,
        model.value,
        model.description
    )
    enum_list = engine.access.list_enum_by_app_uuid(app_uuid, fields=fields)
    func_list.append(enum_list)

    const_list = engine.access.list_const_by_app_uuid(app_uuid)
    func_list.append(const_list)
    if async_select:
        result_list = await asyncio.gather(*func_list)
    else:
        result_list = list()
        for f in func_list:
            result_list.append(await f)
    module_list, model_list, index_list, field_list, relationship_list, enum_list, const_list = result_list

    relationship_list = list(relationship_list)
    r_list = list_system_relationship(model_list, as_dict=False)
    relationship_list.extend(r_list)

    if sys_table_dict:
        for m in model_list:
            m.model_uuid = get_sys_table_rel_table_name(m.model_uuid, sys_table_dict)
        for f in field_list:
            f["model_uuid"] = get_sys_table_rel_table_name(f.get("model_uuid"), sys_table_dict)
        for r in relationship_list:
            r.target_model = get_sys_table_rel_table_name(r.target_model, sys_table_dict)
            r.source_model = get_sys_table_rel_table_name(r.source_model, sys_table_dict)

    def _process_data(modules, models, indexes, fields, relationships, enums, constant):
        module_dict = {m.get("module_uuid"): m for m in modules}

        model_dict = dict()
        index_dict = dict()
        for m in models:
            model_dict.setdefault(m.module_uuid, list())
            model_dict[m.module_uuid].append(m)
            if m.model_uuid.endswith("lemon_modelbasic"):
                index_dict.setdefault(m.model_uuid, list())
                index_dict[m.model_uuid].append({"fields": ["243201e54bd5586ca8795b669988b905"], "is_unique": 1})
                app_log.info(index_dict)

        for i in indexes:
            index_dict.setdefault(i["model_uuid"], list())
            index_dict[i["model_uuid"]].append(i)

        field_dict = OrderedDict()
        for f in fields:
            field_dict.setdefault(f.get("model_uuid"), list())
            field_dict[f.pop("model_uuid")].append(f)

        enum_dict = {e.get("enum_uuid"): e for e in enums}

        const_dict = {c.get("const_uuid"): c for c in constant}

        return module_dict, model_dict, index_dict, field_dict, relationships, enum_dict, const_dict

    return _process_data(
        module_list, model_list, index_list, field_list, relationship_list, enum_list, const_list)


def gen_model_graph(
        modules, models, indexes, fields, relationships, enums, const_dict, raise_error=False, enum_alias=None):
    """
    构造模型拓扑排序所需的图
    raise_error: 控制抛出异常,或是记录异常
    enum_alias: 常量, 避免导入报错, 选择参数传入
    """
    start_t = time.time()
    model_graph = dict()
    model_uuid_map = dict()
    field_uuid_map = dict()
    for module in modules.values():
        module_models = models.get(module["module_uuid"], list())
        for model in module_models:
            model: object
            model_uuid_map[model.model_uuid] = model
            model.module_name = module["module_name"]
            model.dependencies = list()
            model_graph[model] = set()

            model_indexes = indexes.get(model.model_uuid, list())
            model_fields = fields.get(model.model_uuid, list())
            for field in model_fields:
                field_uuid_map.update({field["field_uuid"]: model})
                if field["field_type"] == FieldType.ENUM and raise_error:
                    enum = enums.get(field["enum_uuid"])
                    if enum:
                        enum_module = modules.get(enum["module_uuid"])
                        if not enum_module:
                            raise Exception("enum invalid module uuid")
                        field["choices"] = \
                            f'{enum_module.get("module_name")}{enum_alias}.' + \
                            enum["enum_name"]
                    else:
                        raise LemonPublishError(
                            LDEC.FIELD_ENUM_NOE_EXISTS, element_uuid=field.get(
                                "field_uuid"),
                            element_type_num=2)
            model.model_indexes = list(model_indexes)
            model.model_fields = model_fields

    # relationship_type=0 一对多 1 多对多 2一对一
    for r in relationships:
        if r.relationship_type == 1:
            model_id = copy.copy(r.relationship_uuid)
            model = r

            model.model_uuid = model_id
            model.origin_model_uuid = model_id
            model_uuid_map[model_id] = model
            target_model = model_uuid_map[r.target_model]
            source_model = model_uuid_map[r.source_model]
            module_name = target_model.module_name
            if target_model.model_uuid in SystemTable.UUIDS:
                # 与系统模块的多对多，不能使用 系统模块.模型名 访问
                module_name = source_model.module_name
            model.module_name = module_name
            model.model_name = r.relationship_name
            model.dependencies = list()
            # TODO: 自关联, 暂不支持多对多
            r.relationship_uuid = make_new_key(model_id, r.source_model)
            model.dependencies.append(r)

            relationship_cls_dict = {
                "relationship_type": r.relationship_type,
                "relationship_uuid": make_new_key(model_id, r.target_model),
                "source_model": r.target_model,
                "target_model": r.source_model,
                "backref": r.frontref,
                "frontref": r.backref,
                "target_model_on_delete": r.source_model_on_delete,
                "source_model_on_delete": r.target_model_on_delete,
                "is_system": r.is_system,
                "system_relationship": r.system_relationship,
                "model_uuid": model.model_uuid
            }
            r2 = types.new_class("relationship", (), {},
                                 lambda ns: ns.update(relationship_cls_dict))
            model.dependencies.append(r2)

            model_graph[model] = set()
            model_graph[target_model].add(model)
            model_graph[source_model].add(model)
        else:
            model = model_uuid_map[r.source_model]
            if r not in model.dependencies:
                model.dependencies.append(r)
            model_graph[model_uuid_map[r.target_model]].add(model)
    app_log.info(f"gen model graph cost: {time.time()-start_t}")

    # 去除自关联的影响
    model_self_referential = get_self_referential(model_graph)
    for model in model_self_referential:
        app_log.debug(model.dependencies)
        for dependence in model.dependencies:
            if model.model_uuid == dependence.target_model:
                dependence.target_model = "self"

        model_graph[model].remove(model)
    return model_graph, model_uuid_map, field_uuid_map, model_self_referential


def get_self_referential(graph):
    seq = []
    for k in graph:
        if k in graph[k]:
            seq.append(k)
    return seq


def pre_process_self_associate(data):
    """
    由于对自关联的特殊处理, 导致一条自关联被认为有两条实际的关联, 并拥有了新的relationship_uuid
    但这些没有保存在ModelBasic中
    导致从ModelBasic中取出的model, 无法找到特殊处理添加的两个自关联
    这里是为了解决这个问题, 让两处建立联系, 能找到新添加的自关联
    """
    self_associations = dict()
    for mc in data:
        dc = mc.get("document_content", {}) or dict()
        relationships: list = dc.get("relationships", [])
        for r in relationships:
            if r.get("source_model") == r.get("target_model") is not None:
                self_associations.update({r.get("uuid"): {
                    "relationship_uuid_source": r.get("relationship_uuid_source", ""),
                    "relationship_uuid_target": r.get("relationship_uuid_target", "")
                }})
    return self_associations


def gen_password_salt_hash(password):
    password_salt = create_token(
        container=string.ascii_letters + string.digits,
        length=8)
    password_value = password_salt + password
    password_value_str = password_value.encode("utf-8")
    password_hash = hashlib.sha3_256(password_value_str).hexdigest()
    return password_salt, password_hash


def order_document(data_list, order_list):
    # --------- 对结果重新排序 ---------
    # 建立data["children"]索引 eg:{'31a66b187a3f5a049308a92e76e9f9ec':1, ...}
    uuid_index_data_children = {r["document_uuid"]: i for i, r in enumerate(data_list)}
    result = []
    order_result = {}
    i = 0
    flag = False
    for order_uuid in order_list:
        if order_uuid in uuid_index_data_children:
            result.append(data_list[uuid_index_data_children[order_uuid]])
            uuid_index_data_children.pop(order_uuid)
            order_result.update({order_uuid: i})
            i += 1
    for data_uuid in uuid_index_data_children:
        app_log.debug("order_list 长度和 data_list 不一致")
        flag = True
        result.append(data_list[uuid_index_data_children[data_uuid]])
        order_result.update({data_uuid: i})
        i += 1
    return result, order_result, flag


def check_placeholder_exist(value: dict, field_set, relationship_set):
    value = value.get("value", dict())
    _type = value.get("type")
    if _type == ValueEditorType.FIELD:
        _path = value.get("path", list())
        # if _field not in field_set:  # TODO field_set中没有系统表的字段, 会误报
        #     return False
        for p in _path:
            if p not in relationship_set:
                return False

    return True


def document_name_to_document_type(name):
    document_type_list = []
    for d_name, d_type in DocumentName.NAME_TO_TYPE.items():
        name_lower = name.lower()
        d_name_lower = d_name.lower()
        # 既要用户搜索的内容包含，又要搜索的内容被包含
        if name_lower in d_name_lower or d_name_lower in name_lower:
            document_type_list.append(d_type)
    return document_type_list


def gen_model_resource_info(
        model_list, field_list, relationship_list,
        model_basic_class, model_field_class, relationship_basic_class):
    field_dict = {field_info.get(model_field_class.field_uuid.name): field_info for field_info in field_list}
    model_dict = {model_info.get(model_basic_class.model_uuid.name): model_info for model_info in model_list}
    relationship_list = list(relationship_list)
    r_list = list_system_relationship(model_list)
    relationship_list.extend(r_list)
    relationship_dict = {}
    relationship_uuid_key = relationship_basic_class.relationship_uuid.name
    source_model_key = relationship_basic_class.source_model.name
    target_model_key = relationship_basic_class.target_model.name
    for relationship_info in relationship_list:
        # 为 自关联 生成新的 relationship_uuid 以便用户选择不同方向关联
        # 自关联 默认的 relationship_uuid 不能区分关联方向
        relationship_uuid = relationship_info.get(relationship_uuid_key)
        source_model = relationship_info.get(source_model_key)
        target_model = relationship_info.get(target_model_key)
        relationship_dict.update({relationship_uuid: relationship_info})
        if source_model == target_model:
            source_relationship_info, target_relationship_info = dict(), dict()
            source_uuid = new_relationship_uuid(relationship_uuid)
            target_uuid = new_relationship_uuid(relationship_uuid, from_source=False)
            source_relationship_info.update(relationship_info)
            source_relationship_info.update({relationship_uuid_key: source_uuid})
            target_relationship_info.update(relationship_info)
            target_relationship_info.update({relationship_uuid_key: target_uuid})
            relationship_dict.update({source_uuid: source_relationship_info})
            relationship_dict.update({target_uuid: target_relationship_info})
    from collections import defaultdict
    from apps.entity import ModelBasic
    structural_model_dict = defaultdict(dict)
    structural_field_dict = defaultdict(dict)
    for field_key, field_info in field_dict.items():
        app_uuid = field_info.get(ModelBasic.app_uuid.name)
        module_uuid = field_info.get(ModelBasic.module_uuid.name)
        model_uuid = field_info.get(ModelBasic.model_uuid.name)
        structural_field_dict[app_uuid].setdefault(module_uuid, {})
        structural_field_dict[app_uuid][module_uuid].setdefault(model_uuid, {})
        structural_field_dict[app_uuid][module_uuid][model_uuid].update({field_key: field_info})

    for model_uuid, model_info in model_dict.items():
        app_uuid = model_info.get(ModelBasic.app_uuid.name)
        module_uuid = model_info.get(ModelBasic.module_uuid.name)
        field_dict_info = structural_field_dict[app_uuid][module_uuid][model_uuid]
        model_info.update({"field_dict": field_dict_info})
        structural_model_dict[app_uuid].setdefault(module_uuid, {})
        structural_model_dict[app_uuid][module_uuid][model_uuid] = model_info
    # "app_dict": structural_model_dict
    return {"field_dict": field_dict, "model_dict": model_dict, "relationship_dict": relationship_dict}


async def gen_sql_info_file(path, user, password, host):
    if not os.path.exists(path):
        await async_run(f"mkdir -p {path}")
    config_context = f"""[client]\nuser={user}\npassword={password}\nhost={host}"""
    await async_run(f"echo \"{config_context}\" > {path}/p.cnf")


async def copy_tenant_data(tables: dict, path, to_database_name: list):
    if not os.path.exists(path):
        await async_run(f"mkdir -p {path}")
    app_log.info(tables)
    if tables:
        table_names = " ".join(tables.keys())
        command = f"mysqldump --defaults-extra-file={path}/p.cnf lemon_tenant_center {table_names} --disable-keys > "\
            f"{path}/tenanttable.sql"
        app_log.info(command)
        await async_run(command)

        with open(f"{path}/tenanttable.sql", "r") as s:
            content = s.read()
        for old, new in tables.items():
            content = content.replace(old, new)
        with open(f"{path}/tenanttable_backup.sql", "w") as s:
            s.write(content)

        for database_name in to_database_name:
            try:
                import_command = f"mysql --defaults-extra-file={path}/p.cnf {database_name} < "\
                    f"{path}/tenanttable_backup.sql"
                app_log.info(import_command)
                await async_run(import_command)
            except Exception:
                traceback.print_exc()


def process_app_env(app_env, need_char_key=False):
    return app_env


async def publish_document_update(engine, app_uuid, module_uuid, document_uuid, cache_data, force_update=False):
    cache_key = f'{engine.app.config.DOCUMENT_CONTENT_CACHE_PREFIX}:{document_uuid}'
    await engine.redis.set_cache(
        cache_key, cache_data,
        expiry=engine.app.config.DOCUMENT_CONTENT_CACHE_EXPIRY,
        with_conf_prefix=False
    )
    push_key = f'{engine.app.config.REDIS_CHANNEL_DOCUMENT_CHANGED_KEY}:{app_uuid}:{document_uuid}'
    document_json_data = {
        # DocumentContent.document_content.name: document_content,
        "document_uuid": document_uuid,
        "app_uuid": app_uuid,
        # Document.document_type.name: doc.document_type,
        # Document.document_version.name: doc.document_version,
        "module_uuid": module_uuid,
        "cache_key": cache_key,
        "force_update": force_update,
    }
    await engine.pubsub.publish_json(push_key, document_json_data)


def get_table_meta_class_name(model_uuid: str):
    app_uuid = get_runtime_app_uuid()
    version = get_runtime_app_revision()
    if model_uuid and model_uuid.startswith("_".join([app_uuid[:16], str(version)])):
        model_uuid = model_uuid.split("_", 2)[-1]
    elif model_uuid and model_uuid.startswith(app_uuid[:16]+"_"):
        model_uuid = model_uuid.split("_", 1)[-1]
    return model_uuid


def get_sys_model_copy(model_uuid):
    model, table_name = None, None
    if model_uuid not in MODEL_COPY_DICT:
        model_uuid = get_table_meta_class_name(model_uuid)
    model_copy_info = MODEL_COPY_DICT.get(model_uuid, {})
    model = model_copy_info.get("model")
    table_name = model_copy_info.get("table_name")
    return model, table_name or model_uuid


def split_binary_string(binary_string):
    # 将二进制字符串转换为整数
    num = int(binary_string, 2)

    # 用于存储拆分后的二进制和
    binary_sums = []

    # 从最低位开始拆分
    while num > 0:
        # 找到最低位的 1
        lowest_bit = num & -num

        # 将最低位的 1 加入二进制和
        binary_sums.append(bin(lowest_bit))

        # 从原数中去除最低位的 1
        num = num - lowest_bit

    # 将二进制和转换为字符串
    binary_sums = [str(bin_sum)[2:] for bin_sum in binary_sums]

    return binary_sums


def process_tag_grouby_resource_uuid(obj_result):
    obj_result = list(obj_result)
    obj_result.sort(key=itemgetter("resource_uuid"))
    obj_list = []
    resource_uuid_dict = {}
    for _, group in groupby(
            obj_result, itemgetter("resource_uuid")):
        tag_infos = []
        for t in group:
            tag_infos.append({
                "id": t["id"],
                "tag_uuid": t["tag_uuid"],
                "tag_name": t["tag_name"],
                "action": t["action"],
            })
        else:
            one_tag_info = t
            resource_dict = {
                "module_uuid": one_tag_info.get("module_uuid"),
                "resource_id": one_tag_info.get("resource_id"),
                "resource_uuid": one_tag_info.get("resource_uuid"),
                "resource_name": one_tag_info.get("resource_name"),
                "resource_type": one_tag_info.get("resource_type"),
                "children": tag_infos
            }
            resource_uuid_dict.update({one_tag_info.get("resource_id"): resource_dict})
            obj_list.append(resource_dict)
    return obj_list, resource_uuid_dict


def process_resource_tree(resources, resource_uuid_dict, filter_with_tags=False):
    resource_uuid_map = {resource["id"]: {
        "module_uuid": resource["module_uuid"],
        "resource_id": resource["id"],
        "resource_name": resource["resource_name"],
        "resource_uuid": resource["resource_uuid"],
        "superior_resource": resource["superior_resource"],
        "children": []  # 初始化children列表
    } for resource in resources}

    top_resource = []
    for resource in resources:
        superior_resource = resource["superior_resource"]
        if superior_resource is None:
            top_resource.append(resource_uuid_map[resource["id"]])
        else:
            superior_resource_info = resource_uuid_map.get(superior_resource, {})
            superior_resource_info["children"].append(resource_uuid_map[resource["id"]])

    for resource_id, resource_info in resource_uuid_dict.items():
        resource_uuid_map[resource_id].update(resource_info)

    if filter_with_tags:
        top_resource = process_tree_with_tags(top_resource)

    return top_resource


def process_tree_with_tags(top_resource):
    result = []
    for resource in top_resource:
        children = resource.get("children", [])
        children_result = process_tree_with_tags(children)
        resource["children"] = children_result

        if "tag_uuid" in resource or children_result:
            result.append(resource)

    return result


class Singleton(type):
    def __init__(self, *args, **kwargs):
        self.__instance = None
        super().__init__(*args, **kwargs)

    def __call__(self, *args, **kwargs):
        if self.__instance is None:
            self.__instance = super().__call__(*args, **kwargs)
        return self.__instance


@contextmanager
def temp_unlock(obj):
    try:
        out_set = False
        if not obj.locked:
            out_set = True
        obj.unlock()
        yield
    finally:
        if not out_set:
            obj.lock()


class WXBehalfHelper:

    sToken = "UBg8SLXw5unUCmRMcr4tD8j8S2M"
    sEncodingAESKey = "F52lficMPYDVkVx19CJslxMuB2LcU9Kcbgs5PmIMNyk"
    lemon_wx_id = "ww40ee818c5e1c5754"
    suite_id = "dk8f50481681086b22"
    suite_secret = "KKVhBcF8VL9juHqMqnHQZ97WkScP40lCJU48W_36OJE"
    auth_corp_id = "wpjXJlDgAAdPR3NveBrOFKcPfmhWzZ4Q"
    # auth_info = {
    #     "delai": 
    # }

    def __init__(self):
        pass

    def verify_lemon_url(self, msg_signature, timestamp, nonce, echostr):
        '''
        ------------使用示例一：验证回调URL---------------
        *企业开启回调模式时，企业号会向验证url发送一个get请求
        假设点击验证时，企业收到类似请求：
        * GET /cgi-bin/wxpush?msg_signature=5c45ff5e21c57e6ad56bac8758b79b1d9ac89fd3&timestamp=1409659589&nonce=263014780&echostr=P9nAzCzyDtyTWESHep1vC5X9xho%2FqYX3Zpb4yKa9SKld1DsH3Iyt3tP3zNdtp%2B4RPcs8TgAE7OaBO%2BFZXvnaqQ%3D%3D
        * HTTP/1.1 Host: qy.weixin.qq.com

        接收到该请求时，企业应  
        1.解析出Get请求的参数，包括消息体签名(msg_signature)，时间戳(timestamp)，随机数字串(nonce)以及企业微信推送过来的随机加密字符串(echostr),
        这一步注意作URL解码。
        2.验证消息体签名的正确性
        3. 解密出echostr原文，将原文当作Get请求的response，返回给企业微信
        第2，3步可以用企业微信提供的库函数VerifyURL来实现。
        '''

        wxcpt = WXBizMsgCrypt(self.sToken, self.sEncodingAESKey, self.lemon_wx_id)
        ret, echostr_res = wxcpt.VerifyURL(msg_signature, timestamp, nonce, echostr)
        app_log.info(echostr_res)
        return echostr_res.decode()

    def verify_auth_url(self, msg_signature, timestamp, nonce, echostr):
        wxcpt = WXBizMsgCrypt(self.sToken, self.sEncodingAESKey, self.auth_corp_id)
        ret, echostr_res = wxcpt.VerifyURL(msg_signature, timestamp, nonce, echostr)
        app_log.info(echostr_res)
        return echostr_res.decode()

    def decrypt_suite_info(self, msg_signature, timestamp, nonce, encrypt_xml):
        app_log.info(encrypt_xml)
        xml_tree = ET.fromstring(encrypt_xml)
        to_id = xml_tree.find("ToUserName").text
        wxcpt = WXBizMsgCrypt(self.sToken, self.sEncodingAESKey, to_id)

        app_log.info(encrypt_xml)
        code, res = wxcpt.DecryptMsg(encrypt_xml.decode(), msg_signature, timestamp, nonce)
        if code != 0:
            app_log.info(f"ERR: DecryptMsg ret: {code}, {res}")
        app_log.info([code, res])
        # return text("success")

        info_type_dict = {
            "suite_ticket": "SuiteTicket",
            "reset_permanent_code": "AuthCode"
        }

        xml_tree = ET.fromstring(res)
        info_type = xml_tree.find("InfoType").text
        real_info_type = info_type_dict.get(info_type, info_type)
        content = xml_tree.find(real_info_type).text
        app_log.info(f"decrypt_result: {real_info_type}, {content}")
        return "success"
