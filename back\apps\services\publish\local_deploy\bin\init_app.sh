#!/bin/bash
# 第一次部署需要执行,如果没有执行过dependencies.sh,需要先执行dependencies.sh

#  如果下列包安装失败尝试手动执行 apt-get update
systemctl start docker || true
source ./config.sh
docker network create --subnet=$subnet --gateway=$gateway --opt "com.docker.network.bridge.name"="lemon-bridge" lemon-bridge || true
DIR="$( cd "$( dirname "$0"  )" && pwd  )"
cd "$(dirname "$DIR" )"
docker-compose pull
docker-compose -f init-compose.yml pull
docker-compose -f init-compose.yml -p $app_env up -d
# 第一次初始化数据库可能需要花较长时间,所以会等待10秒再执行候选步骤
sleep 20
docker exec -it mysql-$app_env /local_deploy/bin/load_runtime_data.sh
docker exec -it mysql-$app_env /local_deploy/bin/load_sys_table.sh
docker exec -it web-$app_env /root/local_deploy/new_version.sh
docker exec -it mysql-$app_env /local_deploy/bin/load_sys_table.sh
docker exec -it mysql-$app_env /local_deploy/bin/load_view.sh
docker-compose -f init-compose.yml  -p $app_env down
