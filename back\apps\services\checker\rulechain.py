# -*- coding:utf-8 -*-

import asyncio

from baseutils.log import app_log
from apps.utils import check_lemon_name
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.entity import <PERSON>Chain as RuleChainModel


from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.ide_const import (
    Event, Variable, Timer, Condition, Trigger, Transition, Action, 
    ValueEditor, VariableStateLocal, RuleChain
)
from apps.services import CheckerService, DocumentCheckerService
from apps.services import ValueCheckerService
from apps.services.checker import checker


class ConditionCheckerService(CheckerService):

    attr_class = Condition.ATTR
    uuid_error = LDEC.CONDITION_UUID_ERROR
    uuid_unique_error = LDEC.CONDITION_UUID_UNIQUE_ERROR

    def initialize(self):
        super().initialize()
        self.condition_type = self.element.get("condition")
        self.action_list = self.element.get("action_list")
        self.action_uuid_set = set()
        self.expr = self.element.get("expr")

    def check_condition_type(self):
        attr = Condition.ATTR.CONDITION_TYPE
        return_code = LDEC.CONDITION_TYPE_ERROR
        if self.condition_type not in [0, 1, 2]:
            self._add_error_list(attr, return_code)

    # 检查 条件表达式 是否准确，如果不正确，会向错误列表添加报错信息
    def check_expr(self):
        if self.expr is not None:
            value_checker_service = ValueCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, self.expr, self.element_uuid, self.element_name)
            value_checker_service.check_value(attr=Condition.ATTR.EXPR)
            self.update_any_list(value_checker_service)
    
    # 检查 条件转换列表
    def check_action_list(self, app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set):
        self._check_action_list(
            action_checker_class=ActionCheckerService, action_list=self.action_list, 
            app_func_uuid_dict=app_func_uuid_dict, action_uuid_set=self.action_uuid_set, 
            sm_variable_uuid_set=sm_variable_uuid_set, state_variable_uuid_set=state_variable_uuid_set)


class ActionCheckerService(CheckerService):

    attr_class = Action.ATTR
    uuid_error = LDEC.ACTION_UUID_ERROR
    uuid_unique_error = LDEC.ACTION_UUID_UNIQUE_ERROR
    name_error = LDEC.ACTION_NAME_FAILED

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.action_list = self.element.get("action_list")
    
    # 检查 动作类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_action_type(self):
        if self.type not in Action.TYPE.ALL:
            attr = Action.ATTR.TYPE
            return_code = LDEC.ACTION_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 自动动作 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_auto(self):
        if self.type == Action.TYPE.FUNC:
            auto = self.element.get("auto", False)
            if not isinstance(auto, bool):
                attr = Action.ATTR.AUTO
                return_code = LDEC.ACTION_AUTO_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 云函数 是否在 应用 中存在，如果不存在，会向错误列表添加一条报错信息
    def check_func(self, app_func_uuid_dict):
        if self.type == Action.TYPE.FUNC:
            func_uuid = self.element.get("func")
            if func_uuid not in app_func_uuid_dict:
                attr = Action.ATTR.FUNC
                return_code = LDEC.ACTION_FUNC_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 异步执行 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_run_async(self):
        if self.type == Action.TYPE.FUNC:
            run_async = self.element.get("run_async", False)
            if not isinstance(run_async, bool):
                attr = Action.ATTR.RUN_ASYNC
                return_code = LDEC.ACTION_RUN_ASYNC_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 同步点类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_sync_type(self):
        if self.type == Action.TYPE.FUNC:
            run_async = self.element.get("run_async", False)
            if run_async is True:
                sync_type = self.element.get("sync_type", Action.SYNC_TYPE.NONE)
                if sync_type not in Action.SYNC_TYPE.ALL:
                    attr = Action.ATTR.SYNC_TYPE
                    return_code = LDEC.ACTION_SYNC_TYPE_NOT_SUPPORT
                    self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 延迟执行 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_run_delay(self):
        if self.type == Action.TYPE.FUNC:
            run_delay = self.element.get("run_delay", False)
            if not isinstance(run_delay, bool):
                attr = Action.ATTR.RUN_DELAY
                return_code = LDEC.ACTION_RUN_DELAY_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 延迟执行事件 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_delay_time(self):
        if self.type == Action.TYPE.FUNC:
            run_delay = self.element.get("run_delay", False)
            if run_delay is True:
                delay_time = self.element.get("delay_time")
                try:
                    int(delay_time)
                except Exception:
                    attr = Action.ATTR.DELAY_TIME
                    return_code = LDEC.ACTION_DELAY_TIME_INCURRECT
                    self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 数据绑定参数 与 云函数参数
    def check_arg_list(self, app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set):
        all_variable_uuid_set = sm_variable_uuid_set | state_variable_uuid_set
        if self.type == Action.TYPE.FUNC:
            func_uuid = self.element.get("func")
            if func_uuid in app_func_uuid_dict:
                func = app_func_uuid_dict.get(func_uuid, dict())
                func_arg_name_set = set()
                func_return_name_set = set()
                func_arg_list = func.get("arg_list", list())
                func_return_list = func.get("return_list", list())
                for arg in func_arg_list:
                    arg_name = arg.get("name")
                    func_arg_name_set.add(arg_name)
                for r in func_return_list:
                    r_name = r.get("name")
                    func_return_name_set.add(r_name)
                arg_list = self.element.get("arg_list", list())
                return_list = self.element.get("return_list", list())
                for arg in arg_list:
                    arg_name = arg.get("name")
                    arg_type = arg.get("type")
                    if arg_name not in func_arg_name_set:
                        attr = Action.ATTR.DATA_BIND_ARG
                        return_code = LDEC.ACTION_DATA_BIND_ARG_INCURRECT
                        return_code.message = return_code.message.format(name=arg_name)
                        self._add_error_list(attr=attr, return_code=return_code)
                    if arg_type not in Variable.TYPE.ALL:
                        attr = Action.ATTR.DATA_BIND_ARG_TYPE
                        return_code = LDEC.ACTION_DATA_BIND_ARG_TYPE_NOT_SUPPORT
                        return_code.message = return_code.message.format(name=arg_name)
                        self._add_error_list(attr=attr, return_code=return_code)
                for rn in return_list:
                    rn_name = rn.get("name")
                    rn_type = rn.get("type")
                    bind_to = rn.get("bind_to")
                    if rn_name not in func_return_name_set:
                        attr = Action.ATTR.DATA_BIND_RETURN
                        return_code = LDEC.ACTION_DATA_BIND_RETURN_INCURRECT
                        return_code.message = return_code.message.format(name=rn_name)
                        self._add_error_list(attr=attr, return_code=return_code)
                    if rn_type not in Variable.TYPE.ALL:
                        attr = Action.ATTR.DATA_BIND_RETURN_TYPE
                        return_code = LDEC.ACTION_DATA_BIND_RETURN_TYPE_NOT_SUPPORT
                        return_code.message = return_code.message.format(name=rn_name)
                        self._add_error_list(attr=attr, return_code=return_code)
                    if bind_to:
                        if bind_to not in all_variable_uuid_set:
                            attr = Action.ATTR.DATA_BIND_VARIABLE
                            return_code = LDEC.ACTION_DATA_BIND_VARIABLE_NOT_EXISTS
                            return_code.message = return_code.message.format(name=rn_name)
                            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 for动作循环变量 是否存在，如果不存在，会向错误列表添加一条错误信息
    def check_loop_variable(self, state_variable_uuid_set):
        if self.type == Action.TYPE.FOR:
            for_expr = self.element.get("for_expr", dict())
            loop_variable = for_expr.get("loop_variable")
            if loop_variable not in state_variable_uuid_set:
                attr = Action.ATTR.LOOP_VARIABLE
                return_code = LDEC.ACTION_FOR_LOOP_VARIABLE_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 for动作循环列表
    def check_loop_list(self):
        if self.type == Action.TYPE.FOR:
            for_expr = self.element.get("for_expr", dict())
            loop_list = for_expr.get("loop_list")
            value_checker_service = ValueCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, 
                self.document_name, loop_list, self.element_uuid, self.element_name)
            value_checker_service.check_value(attr=Action.ATTR.LOOP_LIST)
            self.update_any_list(value_checker_service)
    
    # 检查 if动作控制流 是否设置正确
    def check_control_flow(self):
        if self.type == Action.TYPE.IF:
            attr = Action.ATTR.CONTROL_FLOW
            return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
            if_expr = self.element.get("if_expr", dict())
            else_expr = self.element.get("else_expr", dict())
            elif_expr_list = self.element.get("elif_expr_list", list())
            if not isinstance(if_expr, dict):
                return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
                return_code.message = return_code.message.format(control="if")
                self._add_error_list(attr=attr, return_code=return_code)
            if not isinstance(else_expr, dict):
                return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
                return_code.message = return_code.message.format(control="else")
                self._add_error_list(attr=attr, return_code=return_code)
            if not if_expr:
                return_code = LDEC.ACTION_IF_CONTROL_FLOW_IF_LOST
                self._add_error_list(attr=attr, return_code=return_code)
            if not isinstance(elif_expr_list, list):
                return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
                return_code.message = return_code.message.format(control="elif")
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                for elif_expr in elif_expr_list:
                    if not isinstance(elif_expr, dict):
                        return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
                        return_code.message = return_code.message.format(control="elif")
                        self._add_error_list(attr=attr, return_code=return_code)


class BaseTimerCheckerService(CheckerService):

    attr_class = Timer.ATTR
    uuid_error = LDEC.TIMER_UUID_ERROR
    uuid_unique_error = LDEC.TIMER_UUID_UNIQUE_ERROR
    name_error = LDEC.TIMER_NAME_FAILED
    name_unique_error = LDEC.TIMER_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.event_list = self.element.get("event_list", [])
        self.start_timestamp = self.element.get("start_timestamp")
    
    # 检查 定时器类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_timer_type(self):
        if self.type not in Timer.TYPE.ALL:
            attr = Timer.ATTR.TYPE
            return_code = LDEC.TIMER_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 开始时间 值编辑器数据是否合理，如果不合理，会向错误列表添加报错信息
    def check_start_timestamp(self):
        attr = Timer.ATTR.START_TIMESTAMP
        if isinstance(self.start_timestamp, int):
            if self.start_timestamp <= 0 or self.start_timestamp >= 9999999999:
                return_code = LDEC.TIMER_START_TIMESTAMP_VALUE_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
        elif isinstance(self.start_timestamp, dict):
            value_check_service = ValueCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, self.start_timestamp, 
                element_uuid=self.element_uuid, element_name=self.element_name)
            value_check_service.check_value(attr=attr)
            self.update_any_list(value_check_service)
        else:
            return_code = LDEC.TIMER_START_TIMESTAMP_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    def check_event_list(self, document_event_uuid_set):
        attr = Timer.ATTR.EVENT_LIST
        return_code = LDEC.TIMER_EVENT_UUID_NOT_EXISTS
        for event in self.event_list:
            if event not in document_event_uuid_set:
                self._add_error_list(attr, return_code)

    def check_timer(self):
        pass


class OnceTimerCheckerService(BaseTimerCheckerService):
    
    def check_timer(self):
        pass


class IntervalTimerCheckerService(BaseTimerCheckerService):

    def initialize(self):
        super().initialize()
        self.interval_type = self.element.get("interval_type")
        self.interval_value = self.element.get("interval_value")
    
    # 检查 间隔类型、间隔值 设定是否合理，如果不合理，会向错误列表添加报错信息
    def check_timer(self):
        if self.interval_type not in Timer.INTERVAL_TYPE.ALL:
            attr = Timer.ATTR.INTERVAL_TYPE
            return_code = LDEC.TIMER_INTERVAL_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
        try:
            int(self.interval_value)
        except Exception:
            attr = Timer.ATTR.INTERVAL_VALUE
            return_code = LDEC.TIMER_INTERVAL_VALUE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)


class PeriodTimerCheckerService(BaseTimerCheckerService):

    def initialize(self):
        super().initialize()
        self.period_type = self.element.get("period_type")
        self.period_value = self.element.get("period_value")
    
    # 检查 周期类型、周期值 设定是否合理，如果不合理，会向错误列表添加报错信息
    def check_timer(self):
        if self.period_type not in Timer.PERIOD_TYPE.ALL:
            attr = Timer.ATTR.PERIOD_TYPE
            return_code = LDEC.TIMER_PERIOD_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
        
        attr = Timer.ATTR.PERIOD_VALUE
        if self.period_type == Timer.PERIOD_TYPE.WEEK:
            if not self.period_value:
                return_code = LDEC.TIMER_PERIOD_VALUE_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                for v in self.period_value:
                    if v not in list(range(7)):
                        return_code = LDEC.TIMER_PERIOD_VALUE_WEEK_INCURRECT
                        self._add_error_list(attr=attr, return_code=return_code)
                        break
        elif self.period_type == Timer.PERIOD_TYPE.MONTH:
            if not self.period_value:
                return_code = LDEC.TIMER_PERIOD_VALUE_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                for v in self.period_value:
                    if v not in list(range(1, 31)):
                        return_code = LDEC.TIMER_PERIOD_VALUE_MONTH_INCURRECT
                        self._add_error_list(attr=attr, return_code=return_code)
                        break


class TimerCheckerService(BaseTimerCheckerService):

    def initialize(self):
        super().initialize()
        self.timer_checker_class_dict = {
            Timer.TYPE.ONCE: OnceTimerCheckerService,
            Timer.TYPE.INTERVAL: IntervalTimerCheckerService,
            Timer.TYPE.PERIOD: PeriodTimerCheckerService
        }
    
    def check_timer(self):
        timer_checker_class = self.timer_checker_class_dict.get(self.type, BaseTimerCheckerService)
        if timer_checker_class:
            timer_checker_service = timer_checker_class(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, self.element,
                self.element_uuid, self.element_name)
            timer_checker_service.check_timer()
            self.update_any_list(timer_checker_service)


class VariableCheckerService(CheckerService):

    attr_class = Variable.ATTR
    uuid_error = LDEC.VARIABLE_UUID_ERROR
    uuid_unique_error = LDEC.VARIABLE_UUID_UNIQUE_ERROR
    name_error = LDEC.VARIABLE_NAME_FAILED
    name_unique_error = LDEC.VARIABLE_NAME_NOT_UNIQUE
    
    allow_chinese_name = True

    def initialize(self):
        super().initialize()
    

class EventCheckerService(CheckerService):

    attr_class = Event.ATTR
    uuid_error = LDEC.EVENT_UUID_ERROR
    uuid_unique_error = LDEC.EVENT_UUID_UNIQUE_ERROR
    name_error = LDEC.EVENT_NAME_FAILED
    name_unique_error = LDEC.EVENT_NAME_NOT_UNIQUE

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, rule_uuid: str, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.rule_uuid = rule_uuid
    
    def initialize(self):
        super().initialize()
        self.event_type = self.element.get("type")
        self.description = self.element.get("description", "")
        arg_list = self.element.get("arg_list", list())
        condition_list = self.element.get("condition_list", list())
        self.arg_list = list() if arg_list is None else arg_list
        self.condition_list = list() if condition_list is None else condition_list
        self.condition_uuid_set = set()
        self.condition_name_set = set()

    def check_type(self):
        attr = Event.ATTR.TYPE
        return_code = LDEC.EVENT_TYPE_NOT_SUPPORT
        if self.event_type not in list(range(3)):
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 参数列表，是否在可选范围内，如果不在，会向错误列表添加一条报错信息
    def check_arg_list(self, app_model_uuid_set, app_enum_uuid_set):

        def _check_arg_name(name):
            attr = Event.ATTR.ARG_NAME
            return_code = LDEC.EVENT_ARG_NAME_FAILED
            if not check_lemon_name(name):
                self._add_error_list(attr=attr, return_code=return_code)

        def _check_arg_name_unique(name, name_set):
            attr = Event.ATTR.ARG_NAME
            return_code = LDEC.EVENT_ARG_NAME_NOT_UNIQUE
            if name in name_set:
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                name_set.add(name)
        
        arg_name_set = set()
        for arg in self.arg_list:
            arg_name = arg.get("name")
            arg_default = arg.get("default")
            _check_arg_name(arg_name)
            _check_arg_name_unique(arg_name, arg_name_set)
            if isinstance(arg_default, dict):
                value_check_service = ValueCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                    self.document_name, arg_default, 
                    element_uuid=self.element_uuid, element_name=self.element_name)
                value_check_service.check_value(attr=Event.ATTR.ARG_DEFAULT)
                self.update_any_list(value_check_service)
    
    # 检查 条件列表
    def check_condition_list(
        self, app_func_uuid_dict, 
        sm_variable_uuid_set, state_variable_uuid_set):
        condition_default_count = 0
        for condition in self.condition_list:
            condition_checker_service = ConditionCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, 
                self.document_name, condition)
            try:
                condition_checker_service.check_uuid()
                condition_checker_service.check_uuid_unique(self.condition_uuid_set)
                condition_checker_service.check_expr()
                condition_checker_service.check_condition_type()
                condition_checker_service.check_action_list(
                    app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(condition_checker_service)
                raise e
            else:
                self.update_any_list(condition_checker_service)
            if condition_checker_service.condition_type == 2:
                condition_default_count += 1
        if condition_default_count > 1:
            attr = Transition.ATTR.CONDITION_DEFAULT
            return_code = LDEC.TRANSITION_DEFAULT_CONDITION_LARGER_THEN_ONE
            self._add_error_list(attr=attr, return_code=return_code)

class RuleChainCheckerService(CheckerService):

    attr_class = RuleChain.ATTR
    uuid_error = LDEC.RC_UUID_ERROR
    uuid_unique_error = LDEC.RC_UUID_UNIQUE_ERROR
    name_error = LDEC.RC_NAME_FAILED
    name_unique_error = LDEC.RC_NAME_NOT_UNIQUE

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict,  app_rule_uuid_set: set, app_model_uuid_set: set, 
        app_func_uuid_dict: dict, app_page_uuid_set: set, 
        app_const_uuid_set: set, app_enum_uuid_set: set, 
        app_user_role_uuid_set: set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.app_rule_uuid_set = app_rule_uuid_set
        self.app_model_uuid_set = app_model_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_uuid_set = app_page_uuid_set
        self.app_const_uuid_set = app_const_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.allow_chinese_name = True
    
    def initialize(self):
        super().initialize()
        self.display_name = self.element.get("display_name", "")
        self.description = self.element.get("description", "")
        self.system_service = self.element.get("system_service", False)
        self.event_list = self.element.get("event_list", list())
        self.variable_list = self.element.get("variable_list", list())
        self.timer_list = self.element.get("timer_list", list())
        self.state_uuid_set = set()
        self.state_name_set = set()
        self.variable_uuid_set = set()
        self.variable_name_set = set()
        self.timer_uuid_set = set()
        self.timer_name_set = set()
        
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []

    
    def build_insert_query_data(self):
        return {
            RuleChainModel.app_uuid.name: self.app_uuid,
            RuleChainModel.module_uuid.name: self.module_uuid,
            RuleChainModel.document_uuid.name: self.document_uuid,
            RuleChainModel.rule_uuid.name: self.element_uuid,
            RuleChainModel.rule_name.name: self.element_name,
            RuleChainModel.system_service.name: self.system_service,
            RuleChainModel.description.name: self.description,
            RuleChainModel.variable_list.name: self.variable_list
        }
    
    def build_update_query_data(self):
        return {
            RuleChainModel.app_uuid.name: self.app_uuid,
            RuleChainModel.module_uuid.name: self.module_uuid,
            RuleChainModel.document_uuid.name: self.document_uuid,
            RuleChainModel.rule_uuid.name: self.element_uuid,
            RuleChainModel.rule_name.name: self.element_name,
            RuleChainModel.system_service.name: self.system_service,
            RuleChainModel.description.name: self.description,
            RuleChainModel.variable_list.name: self.variable_list,
            RuleChainModel.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        app_log.info(query_data)
        return RuleChainModel.update(**query_data).where(
            RuleChainModel.rule_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(rule_uuid):
        return RuleChainModel.update(**{
                RuleChainModel.is_delete.name: True
            }).where(RuleChainModel.rule_uuid==rule_uuid)
    
    def check_modify(self, document_rule_uuid_set):
        if self.element_uuid in document_rule_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
    # 检查 事件列表
    def check_event_list(
        self, event_uuid_set, event_name_set, event_uuid_dict, 
        app_model_uuid_set, app_enum_uuid_set):
        this_event_uuid_set = set()
        for event in self.event_list:
            event_checker_service = EventCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, event, self.element_uuid)
            event_uuid = event_checker_service.element_uuid
            try:
                event_checker_service.check_uuid()
                event_checker_service.check_uuid_unique(event_uuid_set)
                if event_checker_service.event_type == 2:
                    event_checker_service.check_name()
                    event_checker_service.check_name_unique(event_name_set)
                event_checker_service.check_arg_list(app_model_uuid_set, app_enum_uuid_set)
                event_checker_service.check_condition_list(self.app_func_uuid_dict, self.variable_uuid_set, set())
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(event_checker_service)
                raise e
            else:
                self.update_any_list(event_checker_service)
            event_uuid_dict.update({event_uuid: event})
            this_event_uuid_set.add(event_uuid)
    
    # 检查 变量列表
    def check_variable_list(self):
        for variable in self.variable_list:
            variable_checker_service = VariableCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, variable)
            try:
                variable_checker_service.check_uuid()
                variable_checker_service.check_uuid_unique(self.variable_uuid_set)
                variable_checker_service.check_name()
                variable_checker_service.check_name_unique(self.variable_name_set)
                # variable_checker_service.check_default()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(variable_checker_service)
                raise e
            else:
                self.update_any_list(variable_checker_service)
    
    # 检查 定时器列表
    def check_timer_list(self, document_event_uuid_set):
        for timer in self.timer_list:
            timer_checker_service = TimerCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, timer)
            try:
                timer_checker_service.check_uuid()
                timer_checker_service.check_uuid_unique(self.timer_uuid_set)
                timer_checker_service.check_name()
                timer_checker_service.check_name_unique(self.timer_name_set)
                timer_checker_service.check_timer_type()
                timer_checker_service.check_start_timestamp()
                timer_checker_service.check_timer()
                timer_checker_service.check_event_list(document_event_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(timer_checker_service)
                raise e
            else:
                self.update_any_list(timer_checker_service)


class RuleChainDocumentCheckerService(DocumentCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict, document_version: int, 
        app_rule_list: list,
        app_func_uuid_dict: dict, 
        app_page_uuid_set: set, app_const_uuid_set: set, 
        app_enum_uuid_set: set, app_user_role_uuid_set: set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, RuleChainModel, *args, **kwargs)
        self.document_version = document_version
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_uuid_set = app_page_uuid_set
        self.app_const_uuid_set = app_const_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.app_model_uuid_set = set()
        self.app_rule_uuid_set = set()
        self.app_event_uuid_set = set()
        self.app_event_uuid_dict = dict()
        self.module_rule_name_set = set()
        self.module_event_name_set = set()
        self.document_rule_uuid_set = set()
        self.document_event_uuid_set = set()
        self.allow_chinese_name = True
        module_document_uuid_list = [d["module_uuid"] for d in kwargs.get('document_list') if 
                                     d["module_uuid"] == self.module_uuid]
        for rule in app_rule_list:
            rule_uuid = rule.get("rule_uuid", "")
            rule_name = rule.get("rule_name", "")
            rule_module_uuid = rule.get("module_uuid", "")
            rule_document_uuid = rule.get("document_uuid", "")

            # 找到原文档中所有的状态机，为了新增、更新、删除文档的状态机
            if rule_document_uuid == self.document_uuid:
                self.document_rule_uuid_set.add(rule_uuid)
            else:
                # 排除当前文档所有的 sm_uuid，获取应用的所有 sm_uuid
                self.app_rule_uuid_set.add(rule_uuid)
                # 排除当前文档所有的 sm_name，获取模块的所有 sm_name
                if rule_module_uuid == self.module_uuid and rule_document_uuid in module_document_uuid_list:
                    self.module_rule_name_set.add(rule_name)

        # for event in app_event_list:
        #     event_uuid = event.get("event_uuid", "")
        #     event_name = event.get("event_name", "")
        #     event_module_uuid = event.get("module_uuid", "")
        #     event_document_uuid = event.get("document_uuid", "")

        #     # 找到原文档中所有的事件，为了新增、更新、删除文档的事件
        #     if event_document_uuid == self.document_uuid:
        #         self.document_event_uuid_set.add(event_uuid)
        #     else:
        #         # 排除当前文档所有的 event_uuid，获取应用的所有 event_uuid
        #         self.app_event_uuid_set.add(event_uuid)
        #         self.app_event_uuid_dict.update({event_uuid: event})
        #         # 排除当前文档所有的 event_name，获取模块的所有 event_name
        #         if event_module_uuid == self.module_uuid:
        #             self.module_event_name_set.add(event_name)

    @checker.run
    def check_rule_chain(self):
        rulechain_checker_service = RuleChainCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, self.element,
                self.app_rule_uuid_set, self.app_model_uuid_set,
                self.app_func_uuid_dict, self.app_page_uuid_set,
                self.app_const_uuid_set, self.app_enum_uuid_set, 
                self.app_user_role_uuid_set, is_copy=self.is_copy)
        rule_uuid = rulechain_checker_service.element_uuid
        try:
            rulechain_checker_service.check_uuid()
            rulechain_checker_service.check_uuid_unique(self.app_rule_uuid_set)
            rulechain_checker_service.check_name()
            rulechain_checker_service.check_name_unique(self.module_rule_name_set)
            rulechain_checker_service.check_variable_list()
            rulechain_checker_service.check_event_list(
                self.document_event_uuid_set, self.module_event_name_set, self.app_event_uuid_dict, 
                self.app_model_uuid_set, self.app_enum_uuid_set)
            
            rulechain_checker_service.check_timer_list(self.document_event_uuid_set)
            
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(rulechain_checker_service)
            raise e
        else:
            self.update_any_list(rulechain_checker_service)

        # 找到新增 或 更新的状态机
        rulechain_checker_service.check_modify(self.document_rule_uuid_set)
        if rulechain_checker_service.insert_query_list:
            self.document_insert_list.extend(rulechain_checker_service.insert_query_list)
        if rulechain_checker_service.update_query_list:
            self.document_update_list.extend(rulechain_checker_service.update_query_list)

        # 找出删除的状态机，将其 is_delete 置为 True
        delete_rule_uuid_set = self.document_rule_uuid_set - set([rule_uuid])
        for this_rule_uuid in delete_rule_uuid_set:
            query = RuleChainCheckerService.build_delete_query(this_rule_uuid)
            self.document_delete_list.append(query)

    async def commit_modify(self, engine):
        # document_update_func_list = []
        # document_delete_func_list = []

        for query in self.document_update_list:
            func = engine.access.update_obj_by_query(self.model, query, need_delete=True)
            await func
            # document_update_func_list.append(func)
        for query in self.document_delete_list:
            func = engine.access.update_obj_by_query(self.model, query, need_delete=True)
            await func
            # document_delete_func_list.append(func)

        # 这里如果数据量大的话，会有性能问题
        async with engine.db.objs.atomic():
            if self.document_insert_list:
                app_log.info(f"Insert {self.model.__name__}, len: {len(self.document_insert_list)}")
                await engine.access.insert_many_obj(self.model, self.document_insert_list)

            # app_log.info(f"Update {self.model.__name__}, len: {len(document_update_func_list)}")
            # await asyncio.gather(*document_update_func_list)

            # app_log.info(f"Update {self.model.__name__}.is_delete, len: {len(document_delete_func_list)}")
            # await asyncio.gather(*document_delete_func_list)
