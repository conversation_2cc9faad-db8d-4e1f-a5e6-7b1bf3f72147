# -*- coding:utf-8 -*-


from sanic import Blueprint
from sanic_openapi import doc
from sanic.views import HTTPMethodView as OriginHTTPMethodView
from sanic import response
from urllib import parse as urllib_parse
from sanic.request import Request
from baseutils.const import DOC
from runtime.const import VFS
from runtime.api.helper import RuntimeOssHelper
from baseutils.log import app_log
from reactpy.backend._common import safe_client_build_dir_path, ASSETS_PATH, MODULES_PATH, safe_web_modules_dir_path, REACTPY_WEB_MODULES_DIR
from runtime.engine import engine
from apps.utils import LemonDictResponse as LDR
REACTPY_WEB_MODULES_DIR.set_default("/tmp/ReactPydir")
API_NAME_VERSION = "_".join([VFS.NAME, VFS.V1])
url_prefix = "/api/" + "/".join([VFS.NAME, VFS.V1])
bp = Blueprint(API_NAME_VERSION, url_prefix=url_prefix)


class UploadFile(OriginHTTPMethodView):

    class UploadFileObj():
        page_uuid = doc.String("页面 UUID", name="page_uuid")
        control_uuid = doc.String("控件 UUID", name="control_uuid")
        file = doc.File("文件", name="file")

    @doc.consumes(UploadFileObj.file, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(UploadFileObj.page_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(UploadFileObj.control_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("上传文件或图片")
    async def post(self, request):
        helper = RuntimeOssHelper(request)
        result = await helper.upload()
        helper.clear_request()
        return result


class MultipartUploadFile(OriginHTTPMethodView):

    class UploadFileObj():
        file_uuid = doc.String("文件uuid", name="file_uuid")
        file = doc.File("文件", name="file")
        content_index = doc.Integer("当前内容索引", name="content_index")
        max_index = doc.Integer("最大内容索引", name="max_index")

    @doc.consumes(UploadFileObj.file, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(UploadFileObj.file_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(UploadFileObj.content_index, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("分片上传文件")
    async def post(self, request):
        helper = RuntimeOssHelper(request)
        result = await helper.multipart_upload()
        helper.clear_request()
        return result


class GetFileUrl(OriginHTTPMethodView):
    # 流式响应 会出现数据库连接不释放的情况？

    class GetFileUrlObj():
        page_uuid = doc.String("页面 UUID", name="page_uuid")
        control_uuid = doc.String("控件 UUID", name="control_uuid")
        file = doc.File("文件", name="file")
        add_watermark = doc.String("是否加水印", name="add_watermark")
        sid = doc.String("ws sid", name="sid")

    @doc.consumes(GetFileUrlObj.sid, content_type=DOC.FORM_DATA, location="formData", required=False)
    @doc.consumes(GetFileUrlObj.add_watermark, content_type=DOC.FORM_DATA, location="formData", required=False)
    @doc.consumes(GetFileUrlObj.file, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(GetFileUrlObj.page_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(GetFileUrlObj.control_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取图片/文件地址")
    async def post(self, request):
        helper = RuntimeOssHelper(request)
        return helper.get_file_url()

    async def get(self, request):
        helper = RuntimeOssHelper(request)
        return await helper.get_file_data()


class GetPackageFile(OriginHTTPMethodView):
    # 流式响应 会出现数据库连接不释放的情况？

    class GetPackageFile():
        page_uuid = doc.String("页面 UUID", name="page_uuid")
        control_uuid = doc.String("控件 UUID", name="control_uuid")
        file_list = doc.File("文件", name="file")
        add_watermark = doc.String("是否加水印", name="add_watermark")
        sid = doc.String("水印文本", name="sid")

    @doc.consumes(GetPackageFile.sid, content_type=DOC.FORM_DATA, location="formData", required=False)
    @doc.consumes(GetPackageFile.add_watermark, content_type=DOC.FORM_DATA, location="formData", required=False)
    @doc.consumes(GetPackageFile.file_list, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取打包文件")
    async def post(self, request):
        helper = RuntimeOssHelper(request)
        result = await helper.get_package_file()
        return result


class FleGetFileUrl(OriginHTTPMethodView):
    # 流式响应 会出现数据库连接不释放的情况？

    class FleGetFileUrlObj():
        page_uuid = doc.String("页面 UUID", name="page_uuid")
        control_uuid = doc.String("控件 UUID", name="control_uuid")
        file = doc.File("文件", name="file")
        add_watermark = doc.String("是否加水印", name="add_watermark")
        sid = doc.String("ws sid", name="sid")

    @doc.consumes(FleGetFileUrlObj.sid, content_type=DOC.FORM_DATA, location="formData", required=False)
    @doc.consumes(FleGetFileUrlObj.add_watermark, content_type=DOC.FORM_DATA, location="formData", required=False)
    @doc.consumes(FleGetFileUrlObj.file, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(FleGetFileUrlObj.page_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(FleGetFileUrlObj.control_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取图片/文件地址")
    async def post(self, request):
        helper = RuntimeOssHelper(request)
        return helper.get_file_url()

    async def get(self, request, file_name):
        app_log.info(f"FleGetFileUrl file_name: {file_name}")
        helper = RuntimeOssHelper(request)
        return await helper.get_file_data()


class ReactAssertFiles(OriginHTTPMethodView):

    async def get(self, request: Request, path: str):
        path = urllib_parse.unquote(path)
        headers = {"Cache-Control": "public, max-age=31536000"}
        return await response.file(safe_client_build_dir_path(f"assets/{path}"), headers=headers)


class ReactModuleFiles(OriginHTTPMethodView):

    async def get(self, request: Request, path: str):
        path = urllib_parse.unquote(path)
        headers = {"Cache-Control": "public, max-age=31536000"}
        return await response.file(safe_web_modules_dir_path(path), mime_type="text/javascript", headers=headers)


class DeleteFile(OriginHTTPMethodView):

    class DeleteFileObj():
        component_uuid = doc.String("组件 UUID", name="component_uuid")

    @doc.consumes(DeleteFileObj.component_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("删除模板")
    async def post(self, request):
        template_uuid = request.json.get("template_uuid")
        result = await engine.user_access.update_univer_template(template_uuid, execute_delete=True)
        # delete_file_list 删除文件
        return response.json(LDR(data=result))

class GetTemplates(OriginHTTPMethodView):

    class GetTemplatesObj():
        component_uuid = doc.String("组件 UUID", name="component_uuid")

    @doc.consumes(GetTemplatesObj.component_uuid, content_type=DOC.FORM_DATA, location="formData", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取模板列表")
    async def post(self, request):
        component_uuid = request.json.get("component_uuid")
        tenant_uuid = request.json.get("tenant_uuid")
        result = await engine.user_access.select_univer_templates_by_component_uuid(component_uuid, tenant_uuid)
        return response.json(LDR(data=result))


bp.add_route(UploadFile.as_view(), "/upload_file.json")
bp.add_route(DeleteFile.as_view(), "/delete_file.json")
bp.add_route(GetTemplates.as_view(), "/get_templates.json")
bp.add_route(MultipartUploadFile.as_view(), "/multipart_upload.json")
bp.add_route(GetFileUrl.as_view(), "/get_file_url.json")
bp.add_route(FleGetFileUrl.as_view(), "/get/<file_name>")
bp.add_route(GetPackageFile.as_view(), "/get_package_url.json")
bp.add_route(ReactAssertFiles.as_view(), f"/reactpy/{ASSETS_PATH.name}/<path:path>")
bp.add_route(ReactModuleFiles.as_view(), f"/reactpy/{MODULES_PATH.name}/<path:path>")
