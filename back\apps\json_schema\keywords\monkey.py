from apps.base_utils import (
    check_lemon_uuid, check_common_name, check_document_func_name,
    check_document_name, check_calc_field_value, check_email, check_lemon_name,
    check_permission, check_sys_field
)
from jsonschema.exceptions import ValidationError
from apps.ide_const import LemonDesignerErrorCode as LDEC, ReturnCode
from apps.json_schema.const import UniqueName, ScopeType
from apps.json_schema.context import DocCtx, AppCtx, Resource, KeyWordCtx, JsonElement
from apps.json_schema.utils import ReferenceData
from apps import entity
from typing import Optional
from loguru import logger
import re

# todo 减少monkey的关键字


def properties(validator, properties, instance, schema):
    if not validator.is_type(instance, "object"):
        return

    # todo: 修正path
    parent_path = validator.doc_ctx.current_path[:]
    for property, subschema in properties.items():
        if property in instance:
            validator.doc_ctx.current_path = parent_path + [property]
            yield from validator.descend(
                instance[property],
                subschema,
                path=property,
                schema_path=property,
                parent=instance,
                parent_path=parent_path
            )
            validator.doc_ctx.current_path.pop()


def patternProperties(validator, patternProperties, instance, schema):
    if not validator.is_type(instance, "object"):
        return

    for pattern, subschema in patternProperties.items():
        for k, v in instance.items():
            if re.search(pattern, k):
                yield from validator.descend(
                    v, subschema, path=k, schema_path=pattern,
                )


def items(validator, items, instance, schema):
    if not validator.is_type(instance, "array"):
        return

    prefix = len(schema.get("prefixItems", []))
    total = len(instance)
    if items is False and total > prefix:
        message = f"Expected at most {prefix} items, but found {total}"
        yield ValidationError(message)
    else:

        parent_path = validator.doc_ctx.current_path[:]
        for index in range(prefix, total):
            validator.doc_ctx.current_path = parent_path + [index]
            yield from validator.descend(
                instance=instance[index],
                schema=items,
                path=index,
                parent=instance,
                parent_path=parent_path
            )
            validator.doc_ctx.current_path.pop()


def prefixItems(validator, prefixItems, instance, schema):
    if not validator.is_type(instance, "array"):
        return

    for (index, item), subschema in zip(enumerate(instance), prefixItems):
        yield from validator.descend(
            instance=item,
            schema=subschema,
            schema_path=index,
            path=index,
        )


def if_(validator, if_schema, instance, schema):
    if validator.evolve(schema=if_schema).is_valid(instance):
        if "then" in schema:
            then = schema["then"]
            yield from validator.descend(instance, then, schema_path="then")
    elif "else" in schema:
        else_ = schema["else"]
        yield from validator.descend(instance, else_, schema_path="else")
