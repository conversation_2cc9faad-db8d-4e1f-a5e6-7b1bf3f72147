from copy import deepcopy
from typing import List
from apps.ide_const import ComponentType

PAGE_COMPONENT_REF_MAP = {
    ComponentType.GRID: ""
}


def gen_reference_schema(source_ref_schema: dict, **reference_attr):
    schema_copy = deepcopy(source_ref_schema)
    schema_copy["reference"].update(**reference_attr)
    return schema_copy


def gen_const_condition_schema(const_value: int, ref: str, const_key: str = "type", then_property: str = None):
    if then_property is not None:
        then_case = {
            "properties": {
                then_property: {
                    "$ref": ref
                }
            },
        }
    else:
        then_case = {
            "$ref": ref
        }
    return {
        "if": {
            "properties": {
                const_key: {
                    "const": const_value
                }
            }
        },
        "then": then_case,
    }


def gen_page_container_children_schema(include_types: List[int] = None, exclude_types: List[int] = None):
    components = []
    if include_types:
        for component_type in include_types:
            components.append({"$ref": PAGE_COMPONENT_REF_MAP.get(component_type)})
        return {
            "allOf": [
                components
            ]
        }
    if exclude_types:
        for component_type in PAGE_COMPONENT_REF_MAP:
            if component_type not in exclude_types:
                components.append({"$ref": PAGE_COMPONENT_REF_MAP.get(component_type)})
        return {
            "allOf": [
                components
            ]
        }
