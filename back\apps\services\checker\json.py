# -*- coding:utf-8 -*-

import os
import base64
import asyncio
from hashlib import md5

import ujson

from baseutils.log import app_log
from apps.exceptions import <PERSON>NameError, CheckUUIDError, CheckUUIDUniqueError
from apps.exceptions import <PERSON><PERSON><PERSON><PERSON>rror, <PERSON><PERSON><PERSON><PERSON>rro<PERSON>, CheckUUIDUniqueError
from apps.entity import JsonTable
from apps.ide_const import Json
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker


class JsonCheckerService(CheckerService):

    attr_class = Json.ATTR
    uuid_error = LDEC.JSON_UUID_ERROR
    uuid_unique_error = LDEC.JSON_UUID_UNIQUE_ERROR
    name_error = LDEC.JSON_NAME_FAILED
    name_unique_error = LDEC.JSON_NAME_NOT_UNIQUE
    # allow_chinese_name = True
    allow_keywords = False
    allow_lemon_keywords = False
    
    def initialize(self):
        super().initialize()
        self.description = self.element.get("description")
        self.value = self.element.get("value")
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            JsonTable.app_uuid.name: self.app_uuid,
            JsonTable.module_uuid.name: self.module_uuid,
            JsonTable.document_uuid.name: self.document_uuid,
            JsonTable.json_uuid.name: self.element_uuid,
            JsonTable.json_name.name: self.element_name,
            JsonTable.description.name: self.description,
            JsonTable.value.name: self.value
        }
    
    def build_update_query_data(self):
        return {
            JsonTable.json_name.name: self.element_name,
            JsonTable.description.name: self.description,
            JsonTable.value.name: self.value,
            JsonTable.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return JsonTable.update(**query_data).where(
            JsonTable.json_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(json_uuid):
        return JsonTable.update(**{
                JsonTable.is_delete.name: True
            }).where(JsonTable.json_uuid==json_uuid)
    
    def check_modify(self, document_json_uuid_set):
        if self.element_uuid in document_json_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
    # 检查JSON值是否与JSON类型匹配，如果不匹配，会向错误列表添加一条报错信息
    @checker.run
    def check_json_value(self):
        attr = Json.ATTR.VALUE
        try:
            ujson.loads(self.value)
        except Exception:
            return_code = LDEC.JSON_VALUE_INCURRECT
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr=attr, return_code=return_code)


class JsonDocumentCheckerService(DocumentCheckerService):
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, document_version: int, 
        app_json_list: list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, JsonTable, *args, **kwargs)
        self.json_list = self.element.get("json_list", list())
        self.app_json_uuid_set = set()
        self.module_json_name_set = set()
        self.document_json_uuid_set = set()
        self.document_json_update_list = list()
        self.document_json_delete_list = list()
        for json in app_json_list:
            json_uuid = json.get("json_uuid", "")
            json_name = json.get("json_name", "")
            json_module_uuid = json.get("module_uuid", "")
            json_document_uuid = json.get("document_uuid", "")

            # 找到原文档中所有的 JSON，为了新增、更新、删除文档的 JSON
            if json_document_uuid == self.document_uuid:
                self.document_json_uuid_set.add(json_uuid)
            else:
                # 排除当前文档所有的 json_uuid ，获取应用的所有 json_uuid
                self.app_json_uuid_set.add(json_uuid)
                # 排除当前文档所有的 json_uuid ，获取模块的所有 json_uuid
                if json_module_uuid == self.module_uuid:
                    if not json.get("is_delete"):
                        self.module_json_name_set.add(json_name)
    
    @checker.run
    def check_json_list(self):
        this_document_json_uuid_set = set()
        for json in self.json_list:
            if self.is_copy:
                temp_name = json.get("name")
                json.update({"name": temp_name + "_" + str(self.document_number)})
                # self.module_json_name_set.add(json.get("name"))
            json_checker_service = JsonCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, 
                    self.document_uuid, self.document_name, json, is_copy=self.is_copy)
            json_uuid = json_checker_service.element_uuid
            this_document_json_uuid_set.add(json_uuid)
            try:
                json_checker_service.check_uuid()
                json_checker_service.check_uuid_unique(self.app_json_uuid_set)
                json_checker_service.check_name()
                json_checker_service.check_name_unique(self.module_json_name_set)
                json_checker_service.check_all()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(json_checker_service)
                raise e
            else:
                self.update_any_list(json_checker_service)

            # 找到新增 或 更新的 JSON
            json_checker_service.check_modify(self.document_json_uuid_set)
            if json_checker_service.insert_query_list:
                self.document_insert_list.extend(json_checker_service.insert_query_list)
            if json_checker_service.update_query_list:
                self.document_update_list.extend(json_checker_service.update_query_list)

        # 找出删除的 JSON，将其 is_delete 置为 True
        delete_json_uuid_set = self.document_json_uuid_set - this_document_json_uuid_set
        for this_json_uuid in delete_json_uuid_set:
            query = JsonCheckerService.build_delete_query(this_json_uuid)
            self.document_delete_list.append(query)
