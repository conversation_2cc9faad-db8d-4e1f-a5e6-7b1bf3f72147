# -*- coding:utf-8 -*-

from apps.services.document.value import ExprValue, StringValue, FieldValue
from apps.ide_const import (
    ComponentType, EventAction, EventResponseMethod, NewButtonEditType,
    PageOpenType, ValueEditorType, VariableType, ButtonClickEventType
)
from apps.utils import <PERSON><PERSON>, lemon_uuid
from tests.utils import UUID


class PageTitleValue(StringValue):

    def __init__(self, value):
        value_type = ValueEditorType.STRING
        super().__init__(value_type, value)


class StringTitleValue(StringValue):

    def __init__(self, value):
        value_type = ValueEditorType.STRING
        super().__init__(value_type, value)


class TitleValue(ExprValue):

    def __init__(self):
        value_type = VariableType.STRING
        expr = "'Please input'"
        super().__init__(value_type, expr, is_monitor=True, once=True)


class FormEditableValue(StringValue):
    
    def __init__(self):
        value_type = VariableType.BOOLEAN
        value = True
        super().__init__(value_type, value)


class UNEditableValue(StringValue):

    def __init__(self):
        value_type = VariableType.BOOLEAN
        value = False
        super().__init__(value_type, value)


class EditableValue(StringValue):

    def __init__(self):
        value_type = VariableType.BOOLEAN
        value = True
        super().__init__(value_type, value)


class InputPlaceHolderValue(StringValue):

    def __init__(self, value=None):
        value_type = VariableType.STRING
        value = value or ""
        super().__init__(value_type, value)


class SelectPlaceHolderValue(StringValue):

    def __init__(self, value=None):
        value_type = VariableType.STRING
        value = value or ""
        super().__init__(value_type, value)


class TextDataSourceValue(FieldValue):

    def __init__(self, field, path=None):
        value_type = VariableType.OBJECT
        field = field
        path = [] if path is None else path
        super().__init__(value_type, field, path, is_monitor=False, once=True)


class TextVisible(StringValue):

    def __init__(self):
        value_type = VariableType.BOOLEAN
        value = True
        super().__init__(value_type, value)


class InputTextAttribute(Json):

    def __init__(
        self, max_length_type=0, max_length=10, allow_clear=False, 
        *args, **kwargs):
        self.max_length_type = max_length_type
        self.max_length = max_length
        self.allow_clear = allow_clear
        super().__init__(*args, **kwargs)


class InputNumberAttribute(Json):

    def __init__(
        self, min=None, max=None, step=None, prefix="", suffix="", *args, **kwargs):
        if isinstance(min, int):
            self.min = min
        if isinstance(max, int):
            self.max = max
        if isinstance(step, int):
            self.step = step
        self.prefix = prefix
        self.suffix = suffix
        super().__init__(*args, **kwargs)


class InputVisible(StringValue):

    def __init__(self):
        value_type = VariableType.BOOLEAN
        value = True
        super().__init__(value_type, value, once=True)

class FormVisible(StringValue):
    
    def __init__(self):
        value_type = VariableType.BOOLEAN
        value = True
        super().__init__(value_type, value, once=True)


class SelectColumnItem(Json):

    def __init__(
        self, title, field, width, sortable=True, align="left", 
        format=None, style=None, *args, **kwargs):
        self.title = title
        self.field = field
        self.width = width
        self.sortable = sortable
        self.align = align
        self.format = format
        self.style = style
        super().__init__(*args, **kwargs)


class SearchButton(Json):

    def __init__(
        self, title="搜索", button_type="default", shape="default", size="middle", 
        event=None, endKeys=None, tabIndex=None, is_refresh=True, is_addIcon=None, is_danger=None,
        is_extension=None, is_ghost=None, buttonheight=None, buttonwidth=None, description=None,
        style=None, *args, **kwargs):

        self.button_type = button_type
        self.buttonheight = buttonheight or 30
        self.buttonwidth = buttonwidth or 65
        self.description = description or "button"
        self.disabled = StringValue(value_type=VariableType.BOOLEAN, value=True)
        self.events = {"click": []}
        self.function_bar = {"edit_type": 0}
        self.is_addIcon = is_addIcon or True
        self.is_danger = is_danger or False
        self.is_extension = is_extension or False
        self.is_ghost = is_ghost or False
        self.loading = StringValue(value_type=VariableType.BOOLEAN, value=False)
        self.selectedIcon = {"icon": "search2", "icon_type": 1}
        self.shape = shape
        self.size = size
        self.style = style or dict()
        self.title = StringValue(value_type=VariableType.STRING, value=title)
        self.name = self.title.value
        self.uuid = lemon_uuid()
        self.visible = {"is_visible": StringValue(value_type=VariableType.BOOLEAN, value=True).to_dict()}
        self.type = ComponentType.SEARCH_BUTTON
        self.search_bar = {"show": True, "items": [], "show_save_button": True, "show_delete_button": True}
        self.general_search = {"show": True}
        self.free_search = {"show": True, "show_save_button": True, "show_clear_button": True}
        super().__init__(*args, **kwargs)


class NewButton(Json):

    # def __init__(
    #     self, is_shown=True, edit_type=NewButtonEditType.NEW_PAGE,
    #     page="", page_title=None, *args, **kwargs):
    #     self.is_shown = is_shown
    #     self.edit_type = edit_type
    #     self.page = page
    #     if page_title is None:
    #         page_title = dict()
    #     self.page_title = page_title
    #     super().__init__(*args, **kwargs)
    def __init__(
        self, title="新建", button_type="default", shape="default", size="middle", 
        event=None, endKeys=None, tabIndex=None, is_refresh=True, is_addIcon=None, is_danger=None,
        is_extension=None, is_ghost=None, buttonheight=None, buttonwidth=None, description=None,
            style=None, *args, **kwargs):

        self.button_type = button_type
        self.buttonheight = buttonheight or 30
        self.buttonwidth = buttonwidth or 65
        self.description = description or "button"
        self.disabled = StringValue(value_type=VariableType.BOOLEAN, value=True)
        self.events = {"click": []}
        self.function_bar = {"edit_type": 0}
        self.is_addIcon = is_addIcon or True
        self.is_danger = is_danger or False
        self.is_extension = is_extension or False
        self.is_ghost = is_ghost or False
        self.loading = StringValue(value_type=VariableType.BOOLEAN, value=False)
        self.selectedIcon = {"icon": "icon2-14", "icon_type": 1}
        self.shape = shape
        self.size = size
        self.style = style or dict()
        self.title = StringValue(value_type=VariableType.STRING, value=title)
        self.name = self.title.value
        self.uuid = lemon_uuid()
        self.visible = {"is_visible": StringValue(value_type=VariableType.BOOLEAN, value=True).to_dict()}
        self.type = ComponentType.NEW_BUTTON
        super().__init__(*args, **kwargs)


class EditButton(Json):

    # def __init__(
    #     self, is_shown=True, is_inline=False, page="", page_title=None, *args, **kwargs):
    #     self.is_shown = is_shown
    #     self.is_inline = is_inline
    #     self.page = page
    #     if page_title is None:
    #         page_title = dict()
    #     self.page_title = page_title
    #     super().__init__(*args, **kwargs)
    def __init__(
        self, title="编辑", button_type="default", shape="default", size="middle", 
        event=None, endKeys=None, tabIndex=None, is_refresh=True, is_addIcon=None, is_danger=None,
        is_extension=None, is_ghost=None, buttonheight=None, buttonwidth=None, description=None,
        style=None, *args, **kwargs):

        self.button_type = button_type
        self.buttonheight = buttonheight or 30
        self.buttonwidth = buttonwidth or 65
        self.description = description or "button"
        self.disabled = StringValue(value_type=VariableType.BOOLEAN, value=True)
        self.events = {"click": []}
        self.function_bar = {"is_inline": False}
        self.is_addIcon = is_addIcon or True
        self.is_danger = is_danger or False
        self.is_extension = is_extension or False
        self.is_ghost = is_ghost or False
        self.loading = StringValue(value_type=VariableType.BOOLEAN, value=False)
        self.selectedIcon = {"icon": "icon2-16", "icon_type": 1}
        self.shape = shape
        self.size = size
        self.style = style or dict()
        self.title = StringValue(value_type=VariableType.STRING, value=title)
        self.name = self.title.value
        self.uuid = lemon_uuid()
        self.visible = {"is_visible": StringValue(value_type=VariableType.BOOLEAN, value=True).to_dict()}
        self.type = ComponentType.EDIT_BUTTON
        super().__init__(*args, **kwargs)


class DeleteButton(Json):

    # def __init__(
    #     self, is_shown=True, *args, **kwargs):
    #     self.is_shown = is_shown
    #     super().__init__(*args, **kwargs)
    def __init__(
        self, title="删除", button_type="default", shape="default", size="middle", 
        event=None, endKeys=None, tabIndex=None, is_refresh=True, is_addIcon=None, is_danger=None,
        is_extension=None, is_ghost=None, buttonheight=None, buttonwidth=None, description=None,
        style=None, *args, **kwargs):

        self.button_type = button_type
        self.buttonheight = buttonheight or 30
        self.buttonwidth = buttonwidth or 65
        self.description = description or "button"
        self.disabled = StringValue(value_type=VariableType.BOOLEAN, value=True)
        self.events = {"click": []}
        # self.function_bar = {"edit_type": 0}
        self.is_addIcon = is_addIcon or True
        self.is_danger = is_danger or False
        self.is_extension = is_extension or False
        self.is_ghost = is_ghost or False
        self.loading = StringValue(value_type=VariableType.BOOLEAN, value=False)
        self.selectedIcon = {"icon": "icon2-15", "icon_type": 1}
        self.shape = shape
        self.size = size
        self.style = style or dict()
        self.title = StringValue(value_type=VariableType.STRING, value=title)
        self.name = self.title.value
        self.uuid = lemon_uuid()
        self.visible = {"is_visible": StringValue(value_type=VariableType.BOOLEAN, value=True).to_dict()}
        self.type = ComponentType.DELETE_BUTTON
        super().__init__(*args, **kwargs)


class ExportButton(Json):

    # def __init__(
    #     self, is_shown=True, maximum_line_num=2000, file_name=None, format=0, *args, **kwargs):
    #     self.is_shown = is_shown
    #     self.maximum_line_num = maximum_line_num
    #     if file_name is None:
    #         file_name = dict()
    #     self.file_name = file_name
    #     self.format = format
    #     super().__init__(*args, **kwargs)
    def __init__(
        self, title="导出", button_type="default", shape="default", size="middle", 
        event=None, endKeys=None, tabIndex=None, is_refresh=True, is_addIcon=None, is_danger=None,
        is_extension=None, is_ghost=None, buttonheight=None, buttonwidth=None, description=None,
        style=None, *args, **kwargs):

        self.button_type = button_type
        self.buttonheight = buttonheight or 30
        self.buttonwidth = buttonwidth or 65
        self.description = description or "button"
        self.disabled = StringValue(value_type=VariableType.BOOLEAN, value=True)
        self.events = {"click": []}
        self.function_bar = {"edit_type": 0}
        self.is_addIcon = is_addIcon or True
        self.is_danger = is_danger or False
        self.is_extension = is_extension or False
        self.is_ghost = is_ghost or False
        self.loading = StringValue(value_type=VariableType.BOOLEAN, value=False)
        self.selectedIcon = {"icon": "icon2-18", "icon_type": 1}
        self.shape = shape
        self.size = size
        self.style = style or dict()
        self.title = StringValue(value_type=VariableType.STRING, value=title)
        self.name = self.title.value
        self.uuid = lemon_uuid()
        self.visible = {"is_visible": StringValue(value_type=VariableType.BOOLEAN, value=True).to_dict()}
        self.type = ComponentType.EXPORT_BUTTON
        super().__init__(*args, **kwargs)


class ImportButton(Json):

    # def __init__(
    #     self, is_shown=True, *args, **kwargs):
    #     self.is_shown = is_shown
    #     super().__init__(*args, **kwargs)
    def __init__(
        self, title="导入", button_type="default", shape="default", size="middle", 
        event=None, endKeys=None, tabIndex=None, is_refresh=True, is_addIcon=None, is_danger=None,
        is_extension=None, is_ghost=None, buttonheight=None, buttonwidth=None, description=None,
        style=None, *args, **kwargs):

        self.button_type = button_type
        self.buttonheight = buttonheight or 30
        self.buttonwidth = buttonwidth or 65
        self.description = description or "button"
        self.disabled = StringValue(value_type=VariableType.BOOLEAN, value=True)
        self.events = {"click": []}
        # self.function_bar = {"edit_type": 0}
        self.is_addIcon = is_addIcon or True
        self.is_danger = is_danger or False
        self.is_extension = is_extension or False
        self.is_ghost = is_ghost or False
        self.loading = StringValue(value_type=VariableType.BOOLEAN, value=False)
        self.selectedIcon = {"icon": "icon2-17", "icon_type": 1}
        self.shape = shape
        self.size = size
        self.style = style or dict()
        self.title = StringValue(value_type=VariableType.STRING, value=title)
        self.name = self.title.value
        self.uuid = lemon_uuid()
        self.visible = {"is_visible": StringValue(value_type=VariableType.BOOLEAN, value=True).to_dict()}
        self.type = ComponentType.IMPORT_BUTTON
        super().__init__(*args, **kwargs)

class FormCancelButton(Json):

    def __init__(self, title="取消", button_type="default", shape="default", size="middle", event=None, endKeys=None, tabIndex=None, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.title = StringValue(value_type=VariableType.STRING, value=title)
        self.name = self.title.value
        self.button_type = button_type
        self.shape = shape
        self.size = size
        self.endKeys = endKeys
        self.tabIndex = tabIndex
        self.visible = {"is_visible": StringValue(value_type=VariableType.BOOLEAN, value=True).to_dict()}
        self.loading = StringValue(value_type=VariableType.BOOLEAN, value=False)
        self.disabled = StringValue(value_type=VariableType.BOOLEAN, value=True)
        self.style = {"margin": {"top": 0, "left": 0,"right": 0, "bottom": 0}}
        self.type = kwargs.get("type", ComponentType.CANCEL_BUTTON)
        super().__init__(*args, **kwargs)
        if event is None:
            event = {"click": [{
                "action": EventAction.CANCEL, "response_method": EventResponseMethod.ACTION, "type": 1,
                "is_close": True, "modelEvent": True}]}
        self.events = event


class FormSubmitButton(Json):

    def __init__(
        self, title="保存", button_type="primary", shape="default", size="middle", 
        event=None, endKeys=None, tabIndex=None, is_refresh=True, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.title = StringValue(value_type=VariableType.STRING, value=title)
        self.name = self.title.value
        self.button_type = button_type
        self.shape = shape
        self.size = size
        self.endKeys = endKeys
        self.tabIndex = tabIndex
        self.visible = {"is_visible": StringValue(value_type=VariableType.BOOLEAN, value=True).to_dict()}
        self.loading = StringValue(value_type=VariableType.BOOLEAN, value=False)
        self.disabled = StringValue(value_type=VariableType.BOOLEAN, value=True)
        self.style = {"margin": {"top": 0, "left": 0, "right": 0, "bottom": 0}}

        self.type = kwargs.get("type", ComponentType.SUBMIT_BUTTON)
        if event is None:
            event = {"click": [{
                "action": EventAction.SAVE, "is_close": True, "response_method": EventResponseMethod.ACTION,
                "type": 1, "success_info": "保存成功!", "is_show_success_info": True, 
                "modelEvent": True, "is_refresh": is_refresh}]}
        self.events = event


class EventActionData(Json):

    def __init__(
        self, response_method=EventResponseMethod.ACTION, action=EventAction.OPEN, 
        is_close=True, component=None, *args, **kwargs):
        self.type = 1
        self.response_method = response_method
        self.action = action
        self.is_close = is_close
        self.component = component


class SubmitEvent(Json):

    def __init__(self, component, event):
        self.uuid = lemon_uuid()
        self.component = component
        self.type = ButtonClickEventType.SEND_EVENT
        self.event = event


class Pagination(Json):

    def __init__(
        self, is_shown=True, pageSize=10, show_empty_lines=False,
            horizontal_position="bottom", vertical_position="right", *args, **kwargs):
        self.is_shown = is_shown
        self.pageSize = pageSize
        self.show_empty_lines = show_empty_lines
        self.horizontal_position = horizontal_position
        self.vertical_position = vertical_position
        super().__init__(*args, **kwargs)


class SearchItem(Json):

    def __init__(
        self, uuid, name, description="", tab=9, title="名称", field=None, 
        input_control=None, operator=0, default=None, search_type=0, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.tab = tab
        self.title = title
        self.field = field
        self.input_control = input_control
        self.operator = operator
        self.default = default
        self.type = search_type
        super().__init__(*args, **kwargs)


class ColumnItem(Json):

    def __init__(
        self, title, dataIndex, is_many=False, width=None, editable=True, sortable=True, align="left",
        input_control=None, component=None, show_edit_field=False, edit_field=None, visible=None,
        aggregation=None, font_color=None, style=None, ChangeComponent=None, filterable=False,
        is_preview=None, preview=None, count_limit=None, extension=None, chunk_size=None, file_limit=None,
        cell_settings=None, uuid=None,
            *args, **kwargs):
        self.title = title
        self.dataIndex = dataIndex
        self.is_many = is_many
        self.width = width
        self.editable = editable
        self.sortable = sortable
        self.align = align
        self.input_control = input_control
        self.component = {} if component is None else component
        self.show_edit_field = show_edit_field
        self.edit_field = {} if edit_field is None else edit_field
        self.aggregation = aggregation
        self.font_color = {} if font_color is None else font_color
        self.style = style
        self.ChangeComponent = ChangeComponent
        self.filterable = filterable
        self.is_preview = is_preview
        self.preview = preview
        self.count_limit = count_limit
        self.extension = extension
        self.chunk_size = chunk_size
        self.file_limit = file_limit
        self.cell_settings = cell_settings
        self.uuid = uuid
        self.visible = {} if visible is None else visible
        super().__init__(*args, **kwargs)


class CardItem(Json):

    def __init__(
        self, show_title=True, title=None, show_operate=False, operate_position="top", 
        operations=None, re_class="class", style="style", *args, **kwargs):
        self.show_title = show_title
        self.title = {} if title is None else title
        self.show_operate = show_operate
        self.operate_position = operate_position
        self.operations = [] if operations is None else operations
        self.re_class = re_class
        self.style = style
        super().__init__(*args, **kwargs)


class SearchBar(Json):

    def __init__(
        self, is_shown=True, default_collapse=True, items=None, *args, **kwargs):
        self.is_shown = is_shown
        self.default_collapse = default_collapse
        self.items = items
        super().__init__(*args, **kwargs)


class Subtable(Json):

    def __init__(
        self, is_shown=True, association=None, preprocessing=None, 
        datalist=None, *args, **kwargs):
        self.is_shown = is_shown
        self.association = association
        self.preprocessing = preprocessing
        self.datalist = datalist
        super().__init__(*args, **kwargs)


class SubDatalist(Json):

    def __init__(
        self, uuid, name, description="", tab=9, columns=None, order=None, 
        with_maintable=False, style=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.tab = tab
        self.columns = columns
        self.order = order
        self.with_maintable = with_maintable
        self.style = style
        super().__init__(*args, **kwargs)


class Datalist(Json):
    
    def __init__(
        self, uuid, name, class_id, description="", tab=9, input_control=3, controller=None,
        data_source=None, column_width_type=0, columns=None, order=None, search_bar=None,
        function_bar=None, pagination=None, empty_image=None, subtable=None, popup_search_bar=None,
        style=None, visible=None, buttons=None, title=None, show_title=None, show_serial_number=None,
        parent_model=None, parent_path=None, purpose=None, candidate_preprocessing=None, is_many=None,
            show_buttons=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.DATALIST
        self.class_id = class_id
        self.description = description
        self.tab = tab
        self.input_control = input_control
        self.controller = controller
        self.data_source = data_source
        self.column_width_type = column_width_type
        self.columns = columns
        self.order = order
        self.search_bar = search_bar
        self.function_bar = function_bar
        self.pagination = pagination
        self.empty_image = empty_image
        self.subtable = [] if subtable is None else subtable
        self.style = style
        self.popup_search_bar = popup_search_bar
        self.visible = visible or dict()
        self.buttons = buttons or list()
        self.title = title or dict()
        self.show_title = show_title or True
        self.show_serial_number = show_serial_number or True
        self.show_buttons = show_buttons or True
        self.parent_model = parent_model
        self.parent_path = parent_path
        self.purpose = purpose
        self.candidate_preprocessing = candidate_preprocessing
        self.is_many = is_many
        super().__init__(*args, **kwargs)


class Cardlist(Json):

    def __init__(
        self, uuid, name, class_id, description="cardList", controller=None,
        data_source=None, pagination=None, popup_search_bar=None,
        style=None, visible=None, buttons=None, title=None, show_title=None,
        show_buttons=None, children=None, line_layout=None, card_item=None, emptyHide=None,
        is_extension=None, slide_delete=None, relation_linkage=None, button_show_method=None, titleStyle=None,
        parent_model=None, parent_path=None, purpose=None, candidate_preprocessing=None, is_many=None,
            *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.CARDLIST
        self.class_id = class_id
        self.description = description
        self.controller = controller
        self.data_source = data_source
        self.pagination = pagination
        self.style = style
        self.popup_search_bar = popup_search_bar
        self.visible = visible or dict()
        self.buttons = buttons or list()
        self.title = title or dict()
        self.show_title = show_title or True
        self.show_buttons = show_buttons or True
        self.line_layout = line_layout or dict()
        self.children = children or list()
        self.card_item = card_item or dict()
        self.emptyHide = emptyHide or False
        self.is_extension = is_extension or False
        self.slide_delete = slide_delete or True
        self.relation_linkage = relation_linkage or False
        self.button_show_method = button_show_method or "part"
        self.titleStyle = titleStyle
        self.parent_model = parent_model
        self.parent_path = parent_path
        self.purpose = purpose
        self.candidate_preprocessing = candidate_preprocessing
        self.is_many = is_many
        super().__init__(*args, **kwargs)


class Form(Json):
    # 加form_affairs控制 新建页面或编辑页面的断开时本地暂存按钮，跟前端约定2为开启。
    def __init__(
        self, uuid, name, class_id, description="", tab=9, form_layout="horizontal",
        size="default", controller=None, data_source=None, editable=None, uneditable_type=0,
        show_buttons=True, children=None, style=None, visible=None, buttons=None,
        form_affairs=2, form_tip=1, label_col=4, wrapper_col=20, popup_search_bar=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.FORM
        self.class_id = class_id
        self.description = description
        self.tab = tab
        self.form_layout = form_layout
        self.size = size
        self.controller = controller
        self.data_source = data_source
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.show_buttons = show_buttons
        self.children = children
        self.style = style
        self.form_affairs = form_affairs
        self.form_tip = form_tip
        self.visible = visible or dict()
        self.buttons = buttons or list()
        self.label_col = label_col
        self.wrapper_col = wrapper_col
        self.popup_search_bar = popup_search_bar
        super().__init__(*args, **kwargs)


class Calendar(Json):

    def __init__(
        self, uuid, name, class_id, description="", controller=None, data_source=None, 
        routine=None, children=None, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.CALENDAR
        self.class_id = class_id
        self.description = description
        self.controller = controller
        self.data_source = data_source
        self.routine = {} if routine is None else routine
        self.children = [] if children is None else children
        self.style = {} if style is None else style
        self.visible = {} if visible is None else visible
        super().__init__(*args, **kwargs)


class FormFieldInfo(Json):

    def __init__(self, uuid, check, placeholder, *args, **kwargs):
        self.uuid = uuid
        self.check = check
        self.placeholder = placeholder
        super().__init__(*args, **kwargs)


class FormAssociation(Json):

    def __init__(self, uuid, check, *args, **kwargs):
        self.uuid = uuid
        self.check = check
        super().__init__(*args, **kwargs)


class Input(Json):
    
    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, uneditable_type=0, placeholder=None, text_attribute=None,
        number_attribute=None, style=None, visible=None, endKeys=None, tabIndex=None, input_setting={}, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.INPUT
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.tabIndex = tabIndex
        self.endKeys = endKeys
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.placeholder = placeholder
        self.text_attribute = text_attribute
        self.number_attribute = number_attribute
        self.style = style
        self.input_setting = input_setting
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class Textarea(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, uneditable_type=0, placeholder=None, auto_size=False, row_number=3,
        allow_clear=False, max_length_type=0, max_length=10, show_counter=False, 
        style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.TEXTAREA
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.placeholder = placeholder
        self.auto_size = auto_size
        self.row_number = row_number
        self.allow_clear = allow_clear
        self.max_length_type = max_length_type
        self.max_length = max_length
        self.show_counter = show_counter
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class Radio(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, uneditable_type=0, option_type=0, layout=0, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.RADIO
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.option_type = option_type
        self.layout = layout
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class Checkbox(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, uneditable_type=0, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.CHECKBOX
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class Select(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, placeholder=None, uneditable_type=0, show_search=True, style=None, visible=None,
        tabIndex=None, endKeys=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.SELECT
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.tabIndex = tabIndex
        self.endKeys = endKeys
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.placeholder = placeholder
        self.uneditable_type = uneditable_type
        self.show_search = show_search
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class Datetime(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, placeholder=None, uneditable_type=0, show_date=True, show_time=True, 
        style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.DATETIME
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.placeholder = placeholder
        self.uneditable_type = uneditable_type
        self.show_date = show_date
        self.show_time = show_time
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class RelationSelect(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, data_source=None,
        description="", tab=9, show_title=True, title=None, totalCandWidth=0,
        editable=None, uneditable_type=0, new_page=None, view_page=None, placeholder=None,
        show_search=True, style=None, visible=None, endKeys=None, tabIndex=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.R_SELECT
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.data_source = dict() if data_source is None else data_source
        self.description = description
        self.tab = tab
        self.endKeys = endKeys
        self.tabIndex = tabIndex
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.new_page = new_page
        self.view_page = view_page
        self.placeholder = placeholder
        self.show_search = show_search
        self.style = style
        self.totalCandWidth = totalCandWidth
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class RelationSelectPopup(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, uneditable_type=0, modal_page=None, view_page=None, placeholder=None,
        style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.R_SELECT_POPUP
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.modal_page = modal_page
        self.view_page = view_page
        self.placeholder = placeholder
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class RelationSelectTable(Json):

    def __init__(
        self, uuid, name, class_id, description="", tab=9, controller=None, 
        data_source=None, column_width_type=0, columns=None, order=None, 
        relation_page=None, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.R_SELECT_TABLE
        self.class_id = class_id
        self.description = description
        self.tab = tab
        self.controller = controller
        self.data_source = data_source
        self.column_width_type = column_width_type
        self.columns = columns
        self.order = order
        self.relation_page = dict() if relation_page is None else relation_page
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class RelationCreateTable(Json):

    def __init__(
        self, uuid, name, class_id, description="", tab=9, controller=None, 
        data_source=None, column_width_type=0, columns=None, order=None, 
        function_bar=None, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.R_CREATE_TABLE
        self.class_id = class_id
        self.description = description
        self.tab = tab
        self.controller = controller
        self.data_source = data_source
        self.column_width_type = column_width_type
        self.columns = columns
        self.order = order
        self.function_bar = function_bar
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class RelationCascade(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        preprocessing=None, editable=None, uneditable_type=0, placeholder=None, 
        show_search=True, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.R_CASCADE
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.preprocessing = list() if preprocessing is None else preprocessing
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.placeholder = placeholder
        self.show_search = show_search
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class RelationTree(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        preprocessing=None, editable=None, uneditable_type=0, placeholder=None, 
        show_search=True, style=None, visible=None, data_source=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.R_TREE
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.preprocessing = dict() if preprocessing is None else preprocessing
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.placeholder = placeholder
        self.show_search = show_search
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        self.data_source = data_source or dict()
        super().__init__(*args, **kwargs)


class Slider(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, uneditable_type=0, max=10000, min=0, step=1, mark_interval=10, mark_suffix="", 
        dots=False, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.SLIDER
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.max = max
        self.min = min
        self.step = step
        self.mark_interval = mark_interval
        self.mark_suffix = mark_suffix
        self.dots = dots
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class Switch(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, uneditable_type=0, show_checked_text=False, checked_text="", un_checked_text="", 
        style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.SWITCH
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.show_checked_text = show_checked_text
        self.checked_text = checked_text
        self.un_checked_text = un_checked_text
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class UploadFile(Json):

    def __init__(
            self, uuid, name, class_id, field_info=None, description="", tab=9,
            show_title=True, title=None, editable=None, uneditable_type=0,
            file_limit=99, extension=None, file_size=100,
            style=None, visible=None, chunk_size=10, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.UPLOAD_FILE
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.file_limit = file_limit
        self.extension = ["png", "jpg", "zip", "txt", "docx", "exe", "xlsx", "pdf"] if extension is None else extension
        self.file_size = file_size
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        self.chunk_size = chunk_size
        super().__init__(*args, **kwargs)


class UploadImage(Json):
    
    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        editable=None, uneditable_type=0, count_limit=99, extension=None, max_file_size=100, 
        preview=None, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.UPLOAD_IMAGE
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.editable = editable
        self.uneditable_type = uneditable_type
        self.count_limit = count_limit
        self.extension = ["bmp", "jpg", "jpeg", "png", "gif"] if extension is None else extension
        self.max_file_size = max_file_size
        self.preview = {"width": 86, "height": 86} if preview is None else preview
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)

class DateTimeCycle(Json):
    
    def __init__(
        self, uuid, name, class_id, field_info=None, description="",
        style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.DATETIME_CYCLE
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        self.uneditable_type = 0
        self.editable = kwargs.get("editable")
        super().__init__(*args, **kwargs)


class Color(Json):

    def __init__(
        self, uuid, name, class_id, field_info=None, description="", tab=9, show_title=True, title=None,
        routine=None, style=None, visible=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.COLOR
        self.class_id = class_id
        self.field_info = dict() if field_info is None else field_info
        self.description = description
        self.tab = tab
        self.show_title = show_title
        self.title = title
        self.routine = color_routine if routine is None else routine
        self.style = style
        self.visible = InputVisible() if visible is None else visible
        super().__init__(*args, **kwargs)


class Text(Json):

    def __init__(
        self, uuid, name, class_id, title=None, data_source=None, visible=None, format=None, 
        style=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.TEXT
        self.class_id = class_id
        self.data_source = dict() if data_source is None else data_source
        self.visible = InputVisible() if visible is None else visible
        self.format = dict() if format is None else format
        self.style = style
        super().__init__(*args, **kwargs)


class Image(Json):
    
    def __init__(
        self, uuid, name, class_id, title=None, data_source=None, visible=None, format=None, 
        style=None, size=None, is_hover=True, is_extension=False, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.IMAGE
        self.class_id = class_id
        self.data_source = dict() if data_source is None else data_source
        self.visible = InputVisible() if visible is None else visible
        self.format = dict() if format is None else format
        self.style = style
        self.size = size
        self.is_hover = is_hover
        self.is_extension = is_extension
        super().__init__(*args, **kwargs)


class File(Json):
    
    def __init__(
        self, uuid, name, class_id, title=None, data_source=None, visible=None, format=None, 
        style=None,  is_extension=False, field_info = None, editable=True, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.FILE
        self.class_id = class_id
        self.data_source = dict() if data_source is None else data_source
        self.visible = InputVisible() if visible is None else visible
        self.format = dict() if format is None else format
        self.field_info = dict() if field_info is None else field_info
        self.style = style
        self.is_extension = is_extension
        self.editable = editable
        super().__init__(*args, **kwargs)


class Tag(Json):

    def __init__(
        self, uuid, name, class_id, data_source=None, visible=None, format=None, 
        style=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.TAG
        self.class_id = class_id
        self.data_source = dict() if data_source is None else data_source
        self.visible = dict() if visible is None else visible
        self.format = dict() if format is None else format
        self.style = style
        super().__init__(*args, **kwargs)


class LineBar(Json):

    def __init__(
        self, uuid, name, class_id, data_source=None, visible=None, textStyle=None, 
        style=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.LINE_BAR
        self.class_id = class_id
        self.data_source = dict() if data_source is None else data_source
        self.visible = dict() if visible is None else visible
        self.textStyle = dict() if textStyle is None else textStyle
        self.style = dict() if style is None else style
        super().__init__(*args, **kwargs)


class RingBar(Json):

    def __init__(
        self, uuid, name, class_id, data_source=None, visible=None, textStyle=None, 
        style=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.RING_BAR
        self.class_id = class_id
        self.data_source = dict() if data_source is None else data_source
        self.visible = dict() if visible is None else visible
        self.textStyle = dict() if textStyle is None else textStyle
        self.style = dict() if style is None else style
        super().__init__(*args, **kwargs)


class PrintControl(Json):

    def __init__(
        self, uuid, name, button_type="default", data_source=None, selectedPage=None, disabled=None, 
        title=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.PRINT
        self.button_type = button_type
        self.description = "print_control"
        self.data_source = dict() if data_source is None else data_source
        self.disabled = dict() if disabled is None else disabled
        self.isIconButton = True
        self.is_addIcon = True
        self.is_extension = True
        self.selectedIcon = "filterable"
        self.selectedPage = dict() if selectedPage is None else selectedPage
        self.shape = "round"
        self.size = "large"
        self.title = dict() if title is None else title
        super().__init__(*args, **kwargs)


class FormCol(Json):

    def __init__(self, uuid, name, description="", col_width_type=2, span=24, children=None):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.GRID_COL
        self.description = description
        self.col_width_type = col_width_type
        self.span = span
        if children is None:
            children = list()
        self.children = children


class FormRow(Json):

    def __init__(self, uuid, name, props=None, cols=None):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.GRID_ROW
        if props is None:
            props = {"align": "middle", "justify": "start", "gutter": [8, 8]}
        self.props = props
        if cols is None:
            cols = list()
        self.cols = cols


class FormGrid(Json):

    def __init__(self, uuid, name, rows=None):
        self.uuid = uuid
        self.name = name
        self.type = ComponentType.GRID
        if rows is None:
            rows = list()
        self.rows = rows


class Page(Json):

    def __init__(
        self, uuid, name, open_type=1, page_title=None, children=None, buttons=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.open_type = open_type
        self.page_title = page_title
        if self.open_type in [PageOpenType.POPUP, PageOpenType.DRAWER]:
            if kwargs.get("routine"):
                self.routine = kwargs.get("routine")
            else:
                self.routine = {"unit": "%", "width": 60}
            if kwargs.get("routine_modal_height"):
                self.routine_modal_height = kwargs.get("routine_modal_height")
            else:
                self.routine_modal_height = {"unit": "px", "width": 60, "width_auto": True}
            if kwargs.get("routine_modal_width"):
                self.routine_modal_width = kwargs.get("routine_modal_width")
            else:
                self.routine_modal_width = {"unit": "%", "width": 60}
        if children is None:
            children = list()
        self.children = children
        if buttons is None:
            buttons = list()
        self.buttons = buttons
        super().__init__(*args, **kwargs)


class Component_Buttons(Json):
    def create_datalist_buttons():
        button_items = [NewButton().to_dict(), DeleteButton().to_dict(), EditButton().to_dict(),
                        ImportButton().to_dict(), ExportButton().to_dict(), SearchButton().to_dict()]
        return button_items


input_text_field = InputTextAttribute(max_length_type=0).to_dict()
input_text_manual = InputTextAttribute(max_length_type=1, max_length=20).to_dict()
input_text_unlimit = InputTextAttribute(max_length_type=2).to_dict()
input_number_currency = InputNumberAttribute(prefix="￥").to_dict()
input_number_default = InputNumberAttribute().to_dict()
input_style = {
    "margin" : {
        "bottom": 0,
        "left": 0,
        "right": 0,
        "top": 0,
    }
}
datetime_cycle_style = {
                        "margin": {
                            "top": 0,
                            "left": 0,
                            "bottom": 0,
                            "right": 0
                        },
                      }
text_sytle = {
    "color": "rgba(0, 0, 0, 0.65)",
    "margin": {
        "top": 0,
        "left": 0,
        "right": 0,
        "bottom": 0
    },
    "is_visible": InputVisible().to_dict(),
    "justify": "flex-start",
    "fontSize": "14px",
    "fontWeight": "normal"
}
                               
input_visible = {
    "is_visible": InputVisible().to_dict()
}
color_routine = {
    "height_auto": True,
    "width_auto": True,
    "unit": "px",
    "width": 200,
    "height": 100,
    "show_color": False
}
select_style = input_style
form_style = {
    "margin" : {
        "bottom": 0,
        "left": 0,
        "right" : 0,
        "top": 0
    }
}
page_controller = {
    "state_machine": UUID.sm_page.state_machine,
    "watch_variables": [
        UUID.sm_page.variable_print_in_page,
        UUID.sm_page.variable_data_in_page,
        UUID.sm_page.variable_editor_result,
        UUID.sm_page.variable_select_in_column
    ]
}
form_controller = {
    "state_machine": UUID.sm_form.state_machine,
    "watch_variables": [
        # UUID.sm_form.variable_pk,
        UUID.sm_form.variable_form_in_page,
        UUID.sm_form.variable_validate_result,
        UUID.sm_form.variable_just_validate_result,
        UUID.sm_form.variable_save_result,
        UUID.sm_form.variable_select_in_column,
        UUID.sm_form.variable_delete_data,
        UUID.sm_form.variable_delete_result,
        UUID.sm_form.variable_scan_select_result,
        UUID.sm_form.variable_save_nested_result
    ]
}
datalist_controller = {
    "state_machine": UUID.sm_datalist.state_machine,
    "watch_variables": [
        UUID.sm_datalist.variable_list_in_page,
        UUID.sm_datalist.variable_select_in_search,
        UUID.sm_datalist.variable_save_result,
        UUID.sm_datalist.variable_inline_result,
        UUID.sm_datalist.variable_delete_result,
        UUID.sm_datalist.variable_dropdown_in_line,
        UUID.sm_datalist.variable_delete_data,
        UUID.sm_datalist.variable_scan_select_result,
        UUID.sm_datalist.variable_import_result,
        UUID.sm_datalist.variable_export_url,
        UUID.sm_datalist.variable_export_template,
        UUID.sm_datalist.variable_is_export_all,
        UUID.sm_datalist.variable_inline_save_result,
        # UUID.sm_datalist.variable_dlist_option,
        UUID.sm_datalist.variable_external_input_result,
        UUID.sm_datalist.variable_condition_list,
        UUID.sm_datalist.variable_selected_result,
        UUID.sm_datalist.variable_default_condition,
        UUID.sm_datalist.variable_drag_row_result,
    ]
}
excel_controller = {
    "state_machine": UUID.sm_excel.state_machine,
    "watch_variables": [
        UUID.sm_excel.variable_gain_unitid_result,
        UUID.sm_excel.variable_gain_latest_data_result,
        UUID.sm_excel.variable_run_func_result,
        UUID.sm_excel.variable_change_template_result,
    ]
}
cardlist_controller = {
    "state_machine": UUID.sm_datalist.state_machine,
    "watch_variables": [
        UUID.sm_datalist.variable_list_in_page,
        UUID.sm_datalist.variable_select_in_search,
        UUID.sm_datalist.variable_save_result,
        UUID.sm_datalist.variable_delete_result,
        UUID.sm_datalist.variable_dropdown_in_line,
        UUID.sm_datalist.variable_scan_select_result
    ]
}
calendar_controller = {
    "state_machine": UUID.sm_calendar.state_machine,
    "watch_variables": [
        UUID.sm_calendar.variable_list_in_page
    ]
}

tree_controller = {
    "state_machine": UUID.sm_tree.state_machine, 
    "watch_variables": [
        UUID.sm_tree.variable_source_in_page, 
        UUID.sm_tree.variable_single_floor_data, 
        UUID.sm_tree.variable_list_in_page, 
        UUID.sm_tree.variable_form_info,
        UUID.sm_tree.variable_delete_data,
        UUID.sm_tree.variable_permission_config,
        UUID.sm_tree.variable_selected_result
    ]
}

treelist_controller = {
    "state_machine": UUID.sm_treelist.state_machine,
    "watch_variables": [
        UUID.sm_treelist.variable_list_in_page,
        UUID.sm_treelist.variable_dropdown_in_line,
        UUID.sm_treelist.variable_delete_data,
        UUID.sm_treelist.variable_delete_result,
        UUID.sm_treelist.variable_dlist_option,
        UUID.sm_treelist.variable_import_result,
        UUID.sm_treelist.variable_export_url,
        UUID.sm_datalist.variable_select_in_search,
        UUID.sm_datalist.variable_selected_result
    ]
}

chart_controller = {
    "state_machine": UUID.sm_chart.state_machine, 
    "watch_variables": [
        UUID.sm_chart.variable_chart_data
    ]
}

react_controller = {
    "state_machine": "react_state_machine",
    "watch_variables": [
        "react_data"
    ]
}

all_component_info = [
    page_controller, form_controller, datalist_controller, excel_controller,
    calendar_controller, chart_controller, treelist_controller, 
    tree_controller, react_controller]

pin_machine_and_variables = []
for p in all_component_info:
    for k, v in p.items():
        if isinstance(v, str):
            pin_machine_and_variables.append(v)
        elif isinstance(v, list):
            pin_machine_and_variables.extend(v)

calendar_routine = {
    "unit": "px",
    "height_auto": True,
    "width_auto": True,
    "width": 200,
    "height": 100,
}
calendar_style = {
    "row_size": 0,
    "margin": {
        "top": 0,
        "left": 1,
        "bottom": 1,
        "right": 2
    },
    "align": {
        "horizontal": 0,
        "vertical": 0
    },
    "class": "class",
    "style": "style"
}
calendar_visible = {
    "show_role": False,
    "select_roles": "",
    "is_visible": {
        "type": "xxx",
        "value_type": 2,
        "value": ""
    },
    "show_pc": False,
    "show_pad": False,
    "show_mobile": False
}
datalist_style = {
    "row_size" : 2,
    "margin" : {
        "bottom": 0,
        "left": 0,
        "right" : 0,
        "top": 0
    }
}
datalist_column_style = {
    "class": "class",
    "style": "style"
}
datalist_function_bar = {
    "new_button": NewButton().to_dict(),
    "edit_button": EditButton().to_dict(),
    "delete_button": DeleteButton().to_dict(),
    "export_button": ExportButton().to_dict(),
    "import_button": ImportButton().to_dict()
}
r_create_table_function_bar = {
    "new_button": NewButton().to_dict(),
    "edit_button": EditButton().to_dict(),
    "delete_button": DeleteButton().to_dict()
}
datalist_column_width = {"auto": True, "value": 100}
datalist_number_format = {
    "group_digitals" : True,
    "precision" : 3
}
datalist_datetime_format = {
    "show_year" : False,
    "show_month" : True,
    "show_day" : True,
    "show_hour" : True,
    "show_minute" : True,
    "show_second" : True
}
datalist_enum_format = {
    "show_icon" : True,
    "use_text_color" : True
}
datalist_column_format = {
    "advanced_format": False,
    "number_format": datalist_number_format,
    "datatime_format": datalist_datetime_format,
    "enum_format": datalist_enum_format,
    "advanced_format_expression": {}
}
text_visible = {
    "show_role": False,
    "select_roles": "",
    "is_visible": TextVisible().to_dict(),
    "show_pc": False,
    "show_pad": False,
    "show_mobile": False
}
text_format = {
    "number_format": datalist_number_format,
    "datetime_format": datalist_datetime_format,
}
tag_format = {
    "enum_format": datalist_enum_format
}
image_datasource = {"type": 1,
                    "code_type": "code128",
                    "show_text": True,
                    "code_color": "#000000",
                    "code_width": 30,
                    "text_align": "center",
                    "code_height": 10,
                    "code_background_color": "#ffffff"}
image_size = {"value": 0,
              "width": 100,
              "height": 100,
              "row_size": True
              }
image_style = {"margin": {
    "top": 0,
    "left": 0,
    "right": 0,
    "bottom": 0
},
    "justify": "flex-start"
}
file_style = {"format": 0}
cardlist_style = {
    "classNames": [
      "cardList_3JXDQddj"
    ]
  }
cardlist_line_layout = {
    "pc": {
        "type": 0,
        "row_num": 2
        },
    "pad": {
        "type": 0,
        "row_num": 2
        },
    "mobile": {
        "type": 0,
        "row_num": 1,
        "show_type": 0
        }
    }

cardlist_popup_search_bar = {
    "show_search_button": True, "show_delete_button": True,
    "funcbar_search_button_text": "搜索"
    }
cardlist_visible = {
    "is_visible": {
      "once": True,
      "type": 0,
      "uuid": lemon_uuid(),
      "value": True,
      "is_monitor": True
    }
  }
cardlist_card_item = {
    "title": {
        "once": True,
        "type": 0,
        "uuid": lemon_uuid(),
        "value": "标题",
        "is_monitor": True
        },
    "events": {
        "click": []
        },
    "show_title": True,
    "show_operate": True,
    "operate_position": "top"
    }
cardlist_titleStyle = {
    "color": "rgba(0, 0, 0, 0.65)",
    "fontSize": 14,
    "fontWeight": "normal"
  }
cardlist_cardItemTitleStyle = {
    "fontSize": 14,
    "fontWeight": "normal"
  }

if __name__ == "__main__":
    print(pin_machine_and_variables)
    print(len(pin_machine_and_variables))
