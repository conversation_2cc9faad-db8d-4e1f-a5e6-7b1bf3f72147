# -*- coding:utf-8 -*-


from sanic import Sanic, Blueprint
from sanic.response import json
from sanic.views import HTTPMethodView

from sanic_openapi import doc, swagger_blueprint

app = Sanic(__name__)
app.blueprint(swagger_blueprint)
session_blueprint = Blueprint(name="session", url_prefix="/session")


@app.get("/test")
@doc.consumes(doc.String(name="filter"), doc.Integer(name="class"), location="query", required=True)
@doc.consumes(doc.String(name="X-API-VERSION", description="set api version", choices=[1, 2]), location="header", required=True)
async def test(request):
    return json({"Hello": "World"})


class SessionView(HTTPMethodView):

    """
    def get(self, request):
        return text("I am get method")

    def post(self, request):
        return text("I am post method")

    def put(self, request):
        return text("I am put method")

    def patch(self, request):
        return text("I am patch method")

    def delete(self, request):
        return text("I am delete method")

    def options(self, request):  # This will not be documented.
        return text("I am options method")
    """

    def get(self, request):
        return json(request.json)

    def post(self, request):
        return json(request.json)

session_blueprint.add_route(SessionView.as_view(), "/")
app.blueprint(session_blueprint)


if __name__ == "__main__":
    app.run(host="127.0.0.1", port=8881, debug=True, auto_reload=False)
