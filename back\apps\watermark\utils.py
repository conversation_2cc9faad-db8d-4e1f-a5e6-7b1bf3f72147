# -*- coding:utf-8 -*-


import os
import PyPDF2
import io
from functools import partial

from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.utils import ImageReader
from PIL import Image, ImageDraw, ImageFont

from baseutils.log import app_log
from apps.base_builder import BaseBuilderABC, ObjABC, add_builder
from apps.base_creator import BaseCreator


PATH = os.path.dirname(os.path.dirname(os.path.dirname(
    os.path.abspath(__file__))))


class FontPath():

    MSYH_NAME = "msyh"
    MSYHBD_NAME = "msyhbd"
    MSYHL_NAME = "msyhl"

    MSYH_PATH = f"{PATH}/packages/fonts/msyh.ttc"
    MSYHBD_PATH = f"{PATH}/packages/fonts/msyhbd.ttc"
    MSYHL_PATH = f"{PATH}/packages/fonts/msyhl.ttc"


class WatermarkType():

    STRING = 1
    IMAGE = 2


class FontWeight():

    NORMAL = "normal"
    LIGHT = "light"
    BOLD = "bold"


class WatermarkRange():

    PAGE = 0
    DOWNLOAD = 1

    ALL = [PAGE, DOWNLOAD]


class ResourceType():

    PDF = 0
    IMAGE = 1

    CONTENT_TYPE_TO_RESOURCE_TYPE = {
        "image/bmp": IMAGE,
        "image/jpeg": IMAGE,
        "image/png": IMAGE,
        "image/gif": IMAGE,
        "application/pdf": PDF,
    }


# pdf 注册字体
pdfmetrics.registerFont(TTFont(FontPath.MSYH_NAME, FontPath.MSYH_PATH))
pdfmetrics.registerFont(TTFont(FontPath.MSYHBD_NAME, FontPath.MSYHBD_PATH))
pdfmetrics.registerFont(TTFont(FontPath.MSYHL_NAME, FontPath.MSYHL_PATH))


class WatermarkABC(ObjABC):

    def initialize(self):
        pass

    def init_settings(self, **settings):
        self.color = settings.get("color", "rgba(0,0,0,0.15)")
        self._font = None
        self._font_path = None
        self._font_obj = None
        self.font_size = settings.get("fontSize", 16)
        self.font_weight = settings.get("fontWeight", FontWeight.NORMAL)
        self.gap_x = settings.get("gapX", 100)
        self.gap_y = settings.get("gapY", 100)
        self.image_height = settings.get("imageHeight", 64)
        self.image_width = settings.get("imageWidth", 120)
        self.image_info = settings.get("image", dict())
        self._image_url = None
        self.range = settings.get("range", WatermarkRange.ALL)
        self.rotate = settings.get("rotate", 215)
        self.watermark_type = settings.get("type", WatermarkType.STRING)
        self.rgba = self.split_rgba()
        self._alpha = self.rgba[-1]
        app_log.info(f"self.rgba: {self.rgba}")
        self.calc_rotate()
        self.calc_font()
        self.calc_image_url()

    @property
    def font(self):
        return self._font

    @property
    def font_path(self):
        return self._font_path

    @property
    def font_obj(self):
        return self._font_obj

    @property
    def alpha(self):
        return self._alpha

    @property
    def image_url(self):
        return self._image_url

    def calc_image_url(self):
        if self.watermark_type == WatermarkType.IMAGE:
            self._image_url = self.image_info.get("url")

    def calc_font(self):
        self._font = FontPath.MSYH_NAME
        self._font_path = FontPath.MSYH_PATH
        if self.font_weight == "normal":
            self._font = FontPath.MSYH_NAME
            self._font_path = FontPath.MSYH_PATH
        elif self.font_weight == "light":
            self._font = FontPath.MSYHL_NAME
            self._font_path = FontPath.MSYHL_PATH
        elif self.font_weight == "bold":
            self._font = FontPath.MSYHBD_NAME
            self._font_path = FontPath.MSYH_PATH

    def split_rgba(self):
        if not self.color.startswith("rgba("):
            return 0, 0, 0, 0.15
        i, j = 5, self.color.find(")")
        rgba_str = self.color[i:j]
        r, g, b, a = rgba_str.split(",")
        return int(r), int(g), int(b), float(a)

    def calc_rotate(self):
        if self.rotate < 0:
            self.rotate = 360 + self.rotate
        elif self.rotate > 360:
            self.rotate = self.rotate - 360
        self.rotate = 360 - self.rotate

    def get_text_list(self, kwargs):
        text_list = kwargs.get("text", ["测试水印"])
        if isinstance(text_list, str):
            text_list = [text_list]
        return text_list

    def resize_by_height(
            self, image_obj: Image.Image, width=120, height=64) -> Image.Image:
        # (x, y) = image_obj.size
        # x_s = int(x * height / y)
        # y_s = height
        image_obj = image_obj.resize((width, height), Image.LANCZOS)
        return image_obj

    def calc_proper_size(self, page_width, page_height):
        """原始图片需要旋转，所以将画布的宽高按原始图片增加一倍处理"""
        rotate_rate = 1
        page_width = int(page_width * (1 + rotate_rate)) + 1
        page_height = int(page_height * (1 + rotate_rate)) + 1
        return page_width, page_height

    def calc_rows(
            self, page_width, page_height, obj_width, obj_height,
            rotate_plus=True):
        # 计算页面 宽高尺寸的最大值
        # 计算文字或图片 宽高尺寸的最小值
        # 计算因旋转产生的尺寸变化
        max_page_size = float(max(page_width, page_height))
        min_text_size = min(obj_width + self.gap_x, obj_height + self.gap_y)
        rotate_rate = 0
        if rotate_plus:
            rotate_rate = self.rotate % 90 / 100
        # 计算需要绘制多少行、列
        rows = int(max_page_size * (1 + rotate_rate) / min_text_size) + 1
        app_log.info(f"{max_page_size}, {min_text_size}, {rows}")
        return rows

    def draw_text(self, draw_obj, text, x, y):
        raise NotImplementedError()

    def draw_image(self, draw_obj, image_obj, x, y):
        raise NotImplementedError()

    def save_by_string(self, *args, **kwargs):
        raise NotImplementedError()

    def save_by_image(self, *args, **kwargs):
        raise NotImplementedError()

    def return_image_obj(self, image_obj: Image.Image, **kwargs):
        image_path = kwargs.get("image_path")
        if image_path:
            image_obj = Image.open(image_path)
        elif isinstance(image_obj, io.BytesIO):
            image_obj = Image.open(image_obj)
        # 将水印素材的宽高调整为给定数值
        image_obj = self.resize_by_height(
            image_obj, self.image_width, self.image_height)
        return image_obj

    def save(self, file_obj, *args, **kwargs) -> io.BytesIO:
        if self.watermark_type == WatermarkType.STRING:
            return self.save_by_string(file_obj, *args, **kwargs)
        elif self.watermark_type == WatermarkType.IMAGE:
            return self.save_by_image(file_obj, *args, **kwargs)
        return None


class PDFWatermark(WatermarkABC):

    _obj_type = ResourceType.PDF

    def split_rgba(self):
        r, g, b, a = super().split_rgba()
        return float(r) / 255, float(g) / 255, float(b) / 255, float(a)

    def calc_x(self, column, text_width):
        return (text_width * column) + (text_width / 2)

    def calc_y(self, row, text_height, line_height=0, line=0, plus=True):
        row_height_total = (row * text_height) + (text_height / 2)
        if plus:
            return row_height_total + (line_height * line)
        return row_height_total - (line_height * line)

    def get_pdf_width_height(self, pdf: PyPDF2.PdfReader):
        width = pdf.pages[0].mediabox.width
        height = pdf.pages[0].mediabox.height
        return width, height

    def draw_text(self, draw_obj: canvas.Canvas, text, x, y):
        draw_obj.drawCentredString(x, y, text)

    def draw_image(
            self, draw_obj: canvas.Canvas, image_obj: ImageReader, x, y):
        draw_obj.drawImage(image_obj, x, y)

    def draw_each_text(
            self, draw_obj: canvas.Canvas, text_list, rotate, width, height,
            column_height, row=0, column=0):
        x = self.calc_x(row, width)
        for i, text in enumerate(text_list):
            plus_y = self.calc_y(
                column, height, column_height, i)
            minus_y = self.calc_y(
                column, height, column_height, i, plus=False)
            # app_log.info(f"rotate: {rotate}, x: {x}, y: {plus_y}, {minus_y}")
            if rotate <= 90:
                self.draw_text(draw_obj, text, x, -plus_y)
                self.draw_text(draw_obj, text, x, minus_y)
            elif rotate <= 180:
                self.draw_text(draw_obj, text, x, -plus_y)
                self.draw_text(draw_obj, text, -x, -minus_y)
            elif rotate <= 270:
                self.draw_text(draw_obj, text, -x, plus_y)
                self.draw_text(draw_obj, text, -x, -minus_y)
            else:
                self.draw_text(draw_obj, text, -x, plus_y)
                self.draw_text(draw_obj, text, x, minus_y)

    def draw_each_image(
            self, draw_obj: canvas.Canvas, image_obj: ImageReader,
            rotate, width, height, row=0, column=0):
        x = self.calc_x(row, width)
        y = self.calc_y(column, height)
        # app_log.info(f"rotate: {rotate}, x: {x}, y: {y}")
        if rotate <= 90:
            self.draw_image(draw_obj, image_obj, x, -y)
            self.draw_image(draw_obj, image_obj, x, y)
        elif rotate <= 180:
            self.draw_image(draw_obj, image_obj, x, -y)
            self.draw_image(draw_obj, image_obj, -x, -y)
        elif rotate <= 270:
            self.draw_image(draw_obj, image_obj, -x, y)
            self.draw_image(draw_obj, image_obj, -x, -y)
        else:
            self.draw_image(draw_obj, image_obj, -x, y)
            self.draw_image(draw_obj, image_obj, x, y)

    def draw_obj(
            self, can: canvas.Canvas, rows, rotate, obj_width, obj_height,
            text_list=None, image_obj=None):
        draw_func = None
        if text_list:
            app_log.info(f"text_list: {text_list}")
            draw_func = partial(
                self.draw_each_text, can, text_list, rotate,
                obj_width, obj_height, can._leading)
        elif image_obj:
            app_log.info(f"image_obj: {image_obj}")
            draw_func = partial(
                self.draw_each_image, can, image_obj, rotate,
                obj_width, obj_height)
        if draw_func:
            for row in range(rows):
                for column in range(rows):
                    draw_func(row=row, column=column)

    def gen_pdf_with_watermark(
            self, source_pdf: PyPDF2.PdfReader,
            watermark_page: PyPDF2.PageObject,
            testing=False) -> io.BytesIO:
        target_obj = io.BytesIO()
        target_output = PyPDF2.PdfWriter()
        for page in source_pdf.pages:
            page.merge_page(watermark_page)
            target_output.add_page(page)
        target_output.write(target_obj)

        # 生成为源文件加水印后的文件
        if testing:
            app_log.info(f"{PATH}/destination.pdf")
            with open(f"{PATH}/destination.pdf", "wb") as target_stream:
                target_output.write(target_stream)
        return target_obj

    def get_watermark_page(self, watermark_obj: io.BytesIO, testing=False):
        watermark_obj.seek(0)
        watermark_pdf = PyPDF2.PdfReader(watermark_obj)
        watermark_page = watermark_pdf.pages[0]
        # 生成测试水印的文件
        if testing:
            test_output = PyPDF2.PdfWriter()
            test_output.add_page(watermark_page)
            with open(f"{PATH}/watermark.pdf", "wb") as test_stream:
                test_output.write(test_stream)
        return watermark_page

    def gen_canvas_with_attrs(
            self, watermark_obj: io.BytesIO,
            page_width, page_height) -> canvas.Canvas:
        # 使用Reportlab创建一个新的PDF
        can = canvas.Canvas(watermark_obj, pagesize=(page_width, page_height))
        can.setFont(self.font, self.font_size)
        app_log.info(f"self.rgba: {self.rgba}")
        can.setFillColorRGB(self.rgba[0], self.rgba[1], self.rgba[2])
        can.setFillAlpha(self.alpha)
        can.rotate(self.rotate)
        return can

    def gen_text_watermark_canvas(
            self, watermark_obj: io.BytesIO, page_width, page_height,
            text_list) -> canvas.Canvas:
        can = self.gen_canvas_with_attrs(
            watermark_obj, page_width, page_height)
        text_list_length = len(text_list)
        text_width = 0
        for text in text_list:
            width = can.stringWidth(text, self.font, self.font_size)
            if width > text_width:
                text_width = width
        text_height = can._leading * text_list_length
        rows = self.calc_rows(page_width, page_height, text_width, text_height)
        width_total = text_width + self.gap_x
        height_total = text_height + self.gap_y
        self.draw_obj(
            can, rows, self.rotate, width_total, height_total,
            text_list=text_list)
        can.save()
        return can

    def gen_image_watermark_canvas(
            self, watermark_obj: io.BytesIO, page_width, page_height,
            image_obj: ImageReader) -> canvas.Canvas:
        can = self.gen_canvas_with_attrs(
            watermark_obj, page_width, page_height)
        width, height = image_obj.getSize()
        app_log.info(f"width: {width}, height: {height}")
        rows = self.calc_rows(page_width, page_height, width, height)
        width_total = width + self.gap_x
        height_total = height + self.gap_y
        self.draw_obj(
            can, rows, self.rotate, width_total, height_total,
            image_obj=image_obj)
        can.save()
        return can

    def gen_canvas(
            self, watermark_obj: io.BytesIO, page_width, page_height,
            text_list=None, image_obj=None) -> canvas.Canvas:
        if text_list:
            can = self.gen_text_watermark_canvas(
                watermark_obj, page_width, page_height, text_list)
        elif image_obj:
            can = self.gen_image_watermark_canvas(
                watermark_obj, page_width, page_height, image_obj)
        else:
            can = None
        return can

    def save_with_watermark(
            self, file_obj, text_list=None, image_obj=None,
            **kwargs) -> io.BytesIO:
        testing = kwargs.get("testing", False)
        source_obj = PyPDF2.PdfReader(file_obj)
        page_width, page_height = self.get_pdf_width_height(source_obj)
        app_log.info(f"width: {page_width}, height: {page_height}")

        watermark_obj = io.BytesIO()
        self.gen_canvas(
            watermark_obj, page_width, page_height,
            text_list=text_list, image_obj=image_obj)
        watermark_page = self.get_watermark_page(
            watermark_obj, testing=testing)
        target_obj = self.gen_pdf_with_watermark(
            source_obj, watermark_page, testing=testing)
        return target_obj

    def save_by_string(self, file_obj, **kwargs):
        text_list = self.get_text_list(kwargs)
        target_obj = self.save_with_watermark(
            file_obj, text_list=text_list, **kwargs)
        return target_obj

    def save_by_image(self, file_obj, image_obj: Image.Image = None, **kwargs):
        image_obj = self.return_image_obj(image_obj, kwargs=kwargs)
        image_obj = ImageReader(image_obj)
        app_log.info(f"image_obj: {image_obj}, {image_obj._image.size}")
        target_obj = self.save_with_watermark(
            file_obj, image_obj=image_obj, **kwargs)
        return target_obj


class ImageWatermark(WatermarkABC):

    _obj_type = ResourceType.IMAGE

    def calc_font(self):
        super().calc_font()
        self._font_obj = ImageFont.truetype(
            self.font_path, size=self.font_size)

    def calc_rotate(self):
        super().calc_rotate()

    def split_rgba(self):
        r, g, b, a = super().split_rgba()
        a = int(a * 255)
        return r, g, b, a

    def calc_x(self, column, text_width):
        if column == 0:
            return 0
        return (text_width * column)

    def calc_y(self, row, text_height, line_height=0, line=0, plus=True):
        if row == 0:
            return 0
        row_height_total = (row * text_height)
        if plus:
            return row_height_total + (line_height * line)
        return row_height_total - (line_height * line)

    def get_image_width_height(self, image_obj: Image.Image):
        return image_obj.size

    def get_watermark_image(self, watermark_obj: io.BytesIO, testing=False):
        watermark_obj.seek(0)
        watermark_pdf = PyPDF2.PdfReader(watermark_obj)
        watermark_page = watermark_pdf.pages[0]
        # 生成测试水印的文件
        if testing:
            test_output = PyPDF2.PdfWriter()
            test_output.add_page(watermark_page)
            with open(f"{PATH}/watermark.pdf", "wb") as test_stream:
                test_output.write(test_stream)
        return watermark_page

    def gen_image_with_attrs(
            self, image_width, image_height) -> Image.Image:
        image = Image.new(
            "RGBA", (image_width, image_height), (255, 255, 255, 0))
        return image

    def draw_text(self, draw_obj: ImageDraw.ImageDraw, text, x, y):
        if x > draw_obj.width or y > draw_obj.height:
            return
        # app_log.info(f"x: {x}, y: {y}")
        draw_obj.text(
            (int(x), int(y)), text, font=self.font_obj, fill=self.rgba)

    def draw_image(
            self, draw_obj: Image.Image, image: Image.Image, x, y, mask=None):
        if x > draw_obj.width or y > draw_obj.height:
            return
        # app_log.info(f"x: {x}, y: {y}")
        draw_obj.paste(image, (int(x), int(y)), mask=mask)

    def draw_each_text(
            self, draw_obj: ImageDraw.ImageDraw, text_list,
            width, height, column_height, row=0, column=0):
        # app_log.info(f"w: {width}, h: {height}, c_h: {column_height}")
        x = self.calc_x(row, width)
        for i, text in enumerate(text_list):
            plus_y = self.calc_y(column, height, column_height, i)
            # app_log.info(f"x: {x}, y: {plus_y}")
            self.draw_text(draw_obj, text, x, plus_y)

    def draw_each_image(
            self, draw_obj: Image.Image, image_obj: Image.Image,
            width, height, row=0, column=0, mask=None):
        x = self.calc_x(row, width)
        y = self.calc_y(column, height)
        self.draw_image(draw_obj, image_obj, x, y, mask=mask)

    def draw_obj(
            self, image_draw, rows, obj_width, obj_height,
            text_list=None, image_obj: Image.Image = None,
            column_height=None, alpha=255):
        draw_func = None
        if text_list:
            app_log.info(f"text_list: {text_list}")
            draw_func = partial(
                self.draw_each_text, image_draw, text_list,
                obj_width, obj_height, column_height)
        elif image_obj:
            app_log.info(f"image_obj: {image_obj}, {alpha}")
            # mask参数需要传入一个与粘贴的图像大小相同的单通道图像
            mask_img = Image.new("L", image_obj.size, alpha)
            draw_func = partial(
                self.draw_each_image, image_draw, image_obj,
                obj_width, obj_height, mask=mask_img)
        if draw_func:
            for row in range(rows):
                for column in range(rows):
                    draw_func(row=row, column=column)

    def set_watermark_image_attrs(
            self, image: Image.Image, image_width, image_height,
            source_image_width, source_image_height) -> Image.Image:
        image = image.rotate(self.rotate)
        app_log.info(f"w: {image_width}, h: {image_height}")
        left = int((image_width / 2) - (image_width / 4))
        upper = int((image_height / 2) - (image_height / 4))
        mmbox = (
            left, upper,
            left + source_image_width, upper + source_image_height
        )
        app_log.info(f"mmbox: {mmbox}")
        image = image.crop(mmbox)
        app_log.info(f"image.size: {image.size}")
        return image

    def gen_text_watermark_image(
            self, image_width, image_height,
            source_image_width, source_image_height,
            text_list) -> Image.Image:
        image = self.gen_image_with_attrs(image_width, image_height)
        image_draw = ImageDraw.Draw(image)
        text_list_length = len(text_list)
        text_width, text_height = 0, 0
        app_log.info(f"text_list: {text_list}")
        for text in text_list:
            _, _, width, height = image_draw.textbbox(
                (0, 0), text, font=self.font_obj)
            if width > text_width:
                text_width = width
            if height > text_height:
                text_height = height
        # column_height = text_height
        text_height = text_height * text_list_length

        rows = self.calc_rows(
            image_width, image_height, text_width, text_height,
            rotate_plus=False)
        width_total = text_width + self.gap_x
        height_total = text_height + self.gap_y

        row_image_width = width_total * int(image_width / width_total)
        row_image_height = text_height
        row_image = self.gen_image_with_attrs(
            row_image_width, row_image_height)
        row_image_draw = ImageDraw.Draw(row_image)
        for row in range(rows):
            row_image_draw.multiline_text(
                (width_total * row, 0), text="\n".join(text_list),
                font=self.font_obj, fill=self.rgba)
        # watermark_file_name = f"row_image.png"
        # app_log.info(f"{PATH}/{watermark_file_name}")
        # row_image.save(f"{PATH}/{watermark_file_name}")

        # image_draw.text(
        #     (100, 100), "测试水印", font=self.font_obj, fill=self.rgba)
        # self.draw_obj(
        #     image_draw, rows, width_total, height_total,
        #     text_list=text_list, column_height=column_height)
        rows = self.calc_rows(
            image_width, image_height, row_image_width, row_image_height,
            rotate_plus=False)
        width_total = row_image_width + self.gap_x
        height_total = row_image_height + self.gap_y
        self.draw_obj(
            image, rows, width_total, height_total, image_obj=row_image)
        image = self.set_watermark_image_attrs(
            image, image_width, image_height,
            source_image_width, source_image_height)
        return image

    def gen_image_watermark_image(
            self, image_width, image_height,
            source_image_width, source_image_height,
            image_obj: Image.Image) -> canvas.Canvas:
        image = self.gen_image_with_attrs(image_width, image_height)
        width, height = image_obj.size
        app_log.info(f"width: {width}, height: {height}")
        rows = self.calc_rows(
            image_width, image_height, width, height, rotate_plus=False)
        width_total = width + self.gap_x
        height_total = height + self.gap_y

        width_total = width + self.gap_x
        height_total = height + self.gap_y
        self.draw_obj(
            image, rows, width_total, height_total, image_obj=image_obj)
        image = self.set_watermark_image_attrs(
            image, image_width, image_height,
            source_image_width, source_image_height)
        return image

    def gen_image(
            self, image_width, image_height,
            source_image_width, source_image_height,
            text_list=None, image_obj=None) -> Image.Image:
        if text_list:
            image = self.gen_text_watermark_image(
                image_width, image_height,
                source_image_width, source_image_height, text_list)
        elif image_obj:
            image = self.gen_image_watermark_image(
                image_width, image_height,
                source_image_width, source_image_height, image_obj)
        else:
            image = None
        return image

    def save_with_watermark(
            self, file_obj, text_list=None, image_obj=None,
            **kwargs) -> io.BytesIO:
        testing = kwargs.get("testing", False)
        source_obj = Image.open(file_obj)
        format = source_obj.format
        app_log.info(f"format: {format}, {type(format)}")
        source_image_width, source_image_height = self.get_image_width_height(
            source_obj)
        app_log.info(f"w: {source_image_width}, h: {source_image_height}")
        image_width, image_height = self.calc_proper_size(
            source_image_width, source_image_height)
        app_log.info(f"w: {image_width}, h: {image_height}")

        watermark_layer = self.gen_image(
            image_width, image_height,
            source_image_width, source_image_height,
            text_list=text_list, image_obj=image_obj)
        if testing:
            watermark_file_name = "watermark.png"
            app_log.info(f"{PATH}/{watermark_file_name}")
            watermark_layer.save(f"{PATH}/{watermark_file_name}")

        source_layer = source_obj.convert("RGBA")
        target_image = Image.alpha_composite(source_layer, watermark_layer)
        if format != "PNG":
            # 非 PNG 图片，需要转成 RGB
            target_image = target_image.convert("RGB")
        if testing:
            target_file_name = f"destination.{format.lower()}"
            app_log.info(f"{PATH}/{target_file_name}")
            target_image.save(f"{PATH}/{target_file_name}")
        target_obj = io.BytesIO()
        target_image.save(target_obj, format=format)
        return target_obj

    def save_by_string(self, file_obj, **kwargs) -> io.BytesIO:
        """为图片添加文字水印"""
        text_list = self.get_text_list(kwargs)
        target_obj = self.save_with_watermark(
            file_obj, text_list=text_list, **kwargs)
        return target_obj

    def save_by_image(
            self, file_obj, image_obj: Image.Image = None,
            **kwargs) -> io.BytesIO:
        """为图片添加图片水印"""
        image_obj = self.return_image_obj(image_obj, kwargs=kwargs)
        target_obj = self.save_with_watermark(
            file_obj, image_obj=image_obj, **kwargs)
        return target_obj


class WatermarkBuilderABC(BaseBuilderABC):

    _obj_class: WatermarkABC = WatermarkABC

    def new(self, **settings) -> WatermarkABC:
        obj: WatermarkABC = self._obj
        obj.init_settings(**settings)
        obj.initialize()
        self.reset()
        return obj


class PDFWatermarkBuilder(WatermarkBuilderABC):

    _obj_class: PDFWatermark = PDFWatermark


class ImageWatermarkBuilder(WatermarkBuilderABC):

    _obj_class: ImageWatermark = ImageWatermark


class WatermarkCreator(BaseCreator):

    @add_builder(PDFWatermarkBuilder(), *[PDFWatermark])
    def create_pdf_watermark(
            self, builder: PDFWatermarkBuilder, **settings) -> PDFWatermark:
        return builder.new(**settings)

    @add_builder(ImageWatermarkBuilder(), *[ImageWatermark])
    def create_iamge_watermark(
            self, builder: ImageWatermarkBuilder,
            **settings) -> ImageWatermark:
        return builder.new(**settings)


watermark_creator = WatermarkCreator()


if __name__ == "__main__":
    pass
    # with open(f"/home/<USER>/Lemon API简述.pdf", "rb") as f_open:
    #     pdf_watermark = PDFWatermark()
    #     pdf_watermark.init_settings(
    #         **{"color": "rgba(0,0,0,0.25)", "rotate": 330})

    #     # 文本水印
    #     pdf_watermark.save_by_string(
    #         f_open, **{
    #             "text": ["好好工作", "好好吃饭", "好好休息"],
    #             "testing": True})

    #     # 图片水印
    #     pdf_watermark.save_by_image(
    #         f_open, image_obj=ImageReader("/home/<USER>/s1.jpg"), **{
    #             "image_path": "/home/<USER>/s2.jpg",
    #             "testing": True})

    #     # 默认水印
    #     pdf_watermark.save(f_open, **{"testing": True})

    # image_path_list = [
    #     "/home/<USER>/hy_app_source.png",
    #     "/home/<USER>/hy_app_source.jpg",
    #     "/home/<USER>/hy_app_source.gif",
    #     "/home/<USER>/hy_app_source.bmp"
    # ]
    # for image_path in image_path_list:
    #     with open(image_path, "rb") as f_open:
    #         image_watermark = ImageWatermark()
    #         image_watermark.init_settings(
    #             **{"color": "rgba(0,0,0,0.65)", "rotate": 360})

    #         # # 文本水印
    #         # image_watermark.save_by_string(
    #         #     f_open, **{
    #         #         "text": ["好好工作", "好好吃饭", "好好休息"],
    #         #         "testing": True})

    #         # # 图片水印
    #         # image_watermark.save_by_image(
    #         #     f_open, image_obj=Image.open("/home/<USER>/s1.jpg"), **{
    #         #     "image_path": "/home/<USER>/s2.jpg",
    #         #     "testing": True})

    #         # 默认水印
    #         image_watermark.save(f_open, **{"testing": True})

    #         # break
