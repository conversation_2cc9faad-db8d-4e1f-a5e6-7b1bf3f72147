from collections import defaultdict
from enum import Enum
from typing import Optional, Union, List, Iterable, Dict, Generator

from apps.base_engine import BaseEngine
from apps.entity import Func
from apps.ide_const import LemonDesignerErrorCode as LDEC

import ast
import parso
from parso.python.tree import PythonN<PERSON>, Decorator, Function, Name, Param, Module, Operator
from parso.tree import NodeOr<PERSON>eaf
from pydantic import Field, BaseModel

from apps.services import CheckerService
from apps.services.checker import checker
from baseutils.const import ReturnCode
from baseutils.log import app_log
from baseutils.utils import lemon_uuid


class MarkerSeverity(int, Enum):
    Error = 8
    Warning = 4
    Info = 2
    Hint = 1


class MarkerTag(Enum):
    Unnecessary = 1
    Deprecated = 2


class Code(BaseModel):
    value: str
    target: str


class RelatedInformation(BaseModel):
    location: str
    message: str


class MarkerData(BaseModel):
    code: Optional[Union[str, Code]] = Field(None, alias="code")
    severity: MarkerSeverity = Field(..., alias="severity")
    message: str = Field(..., alias="message")
    source: Optional[str] = Field(None, alias="source")
    start_line_number: int = Field(..., alias="startLineNumber")
    start_column: int = Field(..., alias="startColumn")
    end_line_number: int = Field(..., alias="endLineNumber")
    end_column: int = Field(..., alias="endColumn")
    related_information: Optional[List[RelatedInformation]] = Field(None, alias="relatedInformation")
    tags: Optional[List[MarkerTag]] = Field(None, alias="tags")


class FunctionArg(BaseModel):
    name: str
    description: str


class CloudFunction(BaseModel):
    func: str = ""
    icon: str = ""
    name: str = ""
    uuid: str = Field(default_factory=lemon_uuid)
    arg_list: List[FunctionArg] = Field(default_factory=list)
    description: str = ""

    return_list: List[FunctionArg] = Field(default_factory=list)
    install_sm_toolbox: bool = False
    start_line_no: int = 0


'''
    前端函数

    from reactpy import web

    script = """
    export function func1(props) {
        const {value} = props;
        return value;
    }
    """
    ssc = web.module_from_string(
        "utils",
        script,
        fallback="⌛",
    )  # 运行时js写到文件系统
    func1_export = web.export(ssc, "func1") # 导出成vdom

    @front_func(module="utils")
    def func1(props):
        return func1_export(props)
'''


class FrontFunction(BaseModel):
    func: str = ""
    icon: str = ""
    name: str = ""
    front_module: Optional[str] = None
    uuid: str = Field(default_factory=lemon_uuid)
    arg_list: List[FunctionArg] = Field(default_factory=list)
    description: str = ""

    return_list: List[FunctionArg] = Field(default_factory=list)
    install_sm_toolbox: bool = False
    start_line_no: int = 0


CLOUD_FUNC_DECORATOR_NAME = "cloud_func"


def marker_data(
        node: NodeOrLeaf, message: str, severity: MarkerSeverity = MarkerSeverity.Warning
) -> MarkerData:
    start_position, end_position = node.start_pos, node.end_pos
    return MarkerData(
        severity=severity, message=message,
        startLineNumber=start_position[0], startColumn=start_position[1],
        endLineNumber=start_position[0], endColumn=start_position[1] + 100
    )


class PyModuleCheckerService(CheckerService):
    def __init__(self, *args, module_funcs: List[Dict], **kwargs):
        super().__init__(*args, **kwargs)
        self.funcs_to_create: Dict[str, CloudFunction] = {}
        self.funcs_to_resume: Dict[str, CloudFunction] = {}
        self.funcs_to_keep: Dict[str, CloudFunction] = {}
        self.funcs_to_rename: Dict[str, CloudFunction] = {}
        self.module_funcs = module_funcs
        self.funcs_name_mapping: Dict[str, List[Dict]] = defaultdict(list)
        for func in self.module_funcs:
            self.funcs_name_mapping[func.get("func_name")].append(func)
        self.func_line_no_mapping: Dict[int, Dict] = {
            func.get("py_module_line_no"): func
            for func in module_funcs if func.get("document_uuid") == self.document_uuid
        }

    def add_warning_with_marker_data(
            self, code: ReturnCode, node: NodeOrLeaf, severity: MarkerSeverity = MarkerSeverity.Warning
    ):
        self._add_warning_list("", code, markers=[marker_data(node, code.message, severity).dict(by_alias=True)])

    def add_error_with_marker_data(
            self, code: ReturnCode, node: NodeOrLeaf, severity: MarkerSeverity = MarkerSeverity.Error

    ):
        app_log.error("PyModule document check error: %s", code.code)
        self._add_error_list("", code, markers=[marker_data(node, code.message, severity).dict(by_alias=True)])

    def convert_param_to_function_arg(self, param: Param) -> Optional[FunctionArg]:
        if not param.children:
            app_log.info(f"param [%s] invalid: no children", param.name)
            return None
        node = param.children[0]
        if isinstance(node, Operator) and node.value in {"*", "**"}:
            app_log.info(f"param [%s] invalid: arbitrary args", param.name)
            self.add_error_with_marker_data(LDEC.FUNC_ARBITRARY_ARGS_NOT_SUPPORT, node)
            return None

        annotation = ""
        if isinstance(param.annotation, Name):
            annotation = param.annotation.get_code()

        return FunctionArg(name=param.name.value, description=annotation)

    def extract_arg_list(self, function: Function) -> List[FunctionArg]:
        if len(function.children) < 3:
            app_log.info("function [%s] has no args, function children length: %s",
                         function.name, len(function.children))
            return []
        parameters_node = function.children[2]
        if not isinstance(parameters_node, PythonNode) or parameters_node.type != "parameters":
            app_log.info("function [%s] has no args, expected parameter node not found, node: %s",
                         function.name, parameters_node)
            return []

        params: Iterable[Param] = list(filter(lambda n: isinstance(n, Param), parameters_node.children))
        arg_list = list(filter(bool, [self.convert_param_to_function_arg(param) for param in params]))
        app_log.info("function [%s] arg list: %s, raw_arg_list: %s", function.name, arg_list, params)
        return arg_list

    @staticmethod
    def extract_return_list_from_trailer(trailer: PythonNode) -> List[FunctionArg]:
        tuple_elements: Iterable[PythonNode] = filter(lambda n: isinstance(n, PythonNode), trailer.children)
        return [FunctionArg(name="", description=element.get_code()) for element in tuple_elements]

    def extract_return_list(self, function: Function) -> List[FunctionArg]:
        anno = function.annotation
        if not anno:
            return []
        if isinstance(anno, Name):
            return [FunctionArg(name="", description=anno.value)]

        if not isinstance(anno, PythonNode) or not anno.children:
            return []
        first_children = function.annotation.children[0]
        if not (isinstance(first_children, Name) and first_children.value == "Tuple"):
            return [FunctionArg(name="", description=function.annotation.get_code())]

        if len(function.annotation.children) < 1:
            return []
        tuple_trailer = function.annotation.children[1]
        return self.extract_return_list_from_trailer(tuple_trailer)

    def get_previous_func_by_pos(self, node: PythonNode) -> Optional[Dict]:
        current_line_no = node.start_pos[0]
        return self.func_line_no_mapping.get(current_line_no, None)

    def get_func_by_name(self, func_name: str):
        func_list = self.funcs_name_mapping[func_name]
        for func in func_list:
            if not func.get("is_delete"):
                return func
        if func_list:
            return func_list[0]

    def extract_cloud_func_info(self, node: NodeOrLeaf):

        if not isinstance(node, PythonNode):
            return
        if node.type != "decorated":
            return

        if len(node.children) != 2:  # 暂时只支持直接装饰的，
            return
        decorator, function = node.children
        if not isinstance(decorator, Decorator) or not isinstance(function, Function):
            return

        if len(decorator.children) < 3:
            return
        _, decorator_name, *rest = decorator.children
        if not isinstance(decorator_name, Name):
            return
        decorator_name = decorator_name.get_code()
        if decorator_name not in ["cloud_func", "front_func"]:
            return

        function_name = function.name.value
        func_dict = self.get_func_by_name(function_name)
        if decorator_name == "cloud_func":
            func_info = CloudFunction(
                name=function_name, arg_list=self.extract_arg_list(function),
                return_list=self.extract_return_list(function), start_line_no=node.start_pos[0]
            )
        else:
            front_module = ""
            if len(rest) > 2 and isinstance(rest[1], PythonNode) and rest[1].type == "argument":
                children = rest[1].children
                if len(children) == 3 and children[0].value == "module" and children[2].type == "string":
                    front_module = ast.literal_eval(children[2].value)
            func_info = FrontFunction(
                name=function_name, front_module=front_module, arg_list=self.extract_arg_list(function),
                return_list=self.extract_return_list(function), start_line_no=node.start_pos[0]
            )
        if (function_name in self.funcs_to_create
                or function_name in self.funcs_to_keep
                or function_name in self.funcs_to_rename
                or function_name in self.funcs_to_resume):
            # 当前文件中有重复函数名
            app_log.info(
                "duplicated func name in this pymodule, "
                "func: %s, funcs_to_create: %s, funcs_to_keep: %s, funcs_to_resume: %s, funcs_to_rename: %s",
                func_info, self.funcs_to_create, self.funcs_to_keep, self.funcs_to_resume, self.funcs_to_rename)
            return self.add_error_with_marker_data(LDEC.FUNC_NAME_NOT_UNIQUE, function)

        if func_dict is None:
            if func_dict := self.get_previous_func_by_pos(node):
                func_info.uuid = func_dict.get("func_uuid")
                self.funcs_to_rename[function_name] = func_info
                return
            self.funcs_to_create[function_name] = func_info
            return

        func_info.uuid = func_dict.get("func_uuid")
        if func_dict.get("is_delete") is True:
            self.funcs_to_resume[function_name] = func_info
            return

        self.funcs_to_keep[function_name] = func_info
        if func_dict.get("document_uuid") != self.document_uuid:
            self.add_error_with_marker_data(LDEC.FUNC_NAME_NOT_UNIQUE, function)

    def funcs_to_delete(self) -> Generator[Dict, None, None]:
        all_uuids_remained = {
            func_info.uuid for func_info in
            [*self.funcs_to_rename.values(), *self.funcs_to_keep.values(), *self.funcs_to_resume.values()]
        }
        for func_name, func_list in self.funcs_name_mapping.items():
            for func_dict in func_list:
                if func_dict.get("document_uuid") != self.document_uuid or not func_dict.get("from_py_module"):
                    continue
                if func_dict.get("is_delete"):
                    continue
                if func_dict.get("func_uuid") not in all_uuids_remained:
                    app_log.info(f"func to delete name: %s, uuid: %s",
                                 func_dict.get("func_uuid"), func_dict.get("func_name"))
                    yield func_dict

    def extract_cloud_funcs(self):
        code = self.element.get("code", "")
        app_log.info(f"PyModuleCheckerService extract cloud funcs code: %s", code)

        tree = parso.parse(code)
        if not isinstance(tree, Module):
            return []
        for node in tree.children:
            self.extract_cloud_func_info(node)

    @checker.run
    def check(self):
        self.extract_cloud_funcs()

    async def create_func(self, engine: BaseEngine, func_info: CloudFunction) -> Func:
        return await engine.access.create_func(**{
            Func.app_uuid.column_name: self.app_uuid,
            Func.module_uuid.column_name: self.module_uuid,
            Func.document_uuid.column_name: self.document_uuid,
            Func.func_uuid.column_name: func_info.uuid,
            Func.func_name.column_name: func_info.name,
            Func.func.column_name: func_info.func,
            Func.description.column_name: func_info.description,
            Func.arg_list.column_name: [arg.dict() for arg in func_info.arg_list],
            Func.return_list.column_name: [arg.dict() for arg in func_info.return_list],
            Func.from_py_module.column_name: True,
            Func.py_module_line_no.column_name: func_info.start_line_no,
            Func.front_module.column_name: getattr(func_info, "func_info", None)
        })

    async def commit_modify(self, engine: BaseEngine):
        all_func_uuids_remained = set()
        for new_func in self.funcs_to_create.values():
            await self.create_func(engine, new_func)
            app_log.info(f"PyModule [%s] create func [%s]", self.document_name, new_func.name)
            all_func_uuids_remained.add(new_func.uuid)

        for func_name, func_info in {**self.funcs_to_resume, **self.funcs_to_rename, **self.funcs_to_keep}.items():
            n = await engine.access.update_py_module_func_info(
                func_info.uuid, self.module_uuid, func_info.name, func_info.start_line_no,
                [a.dict() for a in func_info.arg_list], [a.dict() for a in func_info.return_list],
                getattr(func_info, "front_module", None), False
            )
            app_log.info("PyModule [%s] update func [%s] line_no: [%s] affected: %s",
                         self.document_name, func_info.name, func_info.start_line_no, n)
            all_func_uuids_remained.add(func_info.uuid)

        await engine.access.delete_py_module_func_by_document_uuid(
            self.module_uuid, self.document_uuid, all_func_uuids_remained
        )

        # TODO: 更新函数行号
