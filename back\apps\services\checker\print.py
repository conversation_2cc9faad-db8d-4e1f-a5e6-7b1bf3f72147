# -*- coding:utf-8 -*-

from baseutils.log import app_log
from apps.entity import Model<PERSON>ield, Print as PrintModel, Page as PageModel
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.utils import PrintFinder, RelationshipFinder, PageFinder
from apps.ide_const import IDECode, PrintType, LemonDesignerErrorCode as LDEC
from apps.ide_const import (
    Print, PrintTable, PrintForm, PrintCardlist, PrintDatalist, PrintText,
    PrintImage, Datalist, ComponentType, PrintFormAttr
)
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker
from apps.base_utils import lemon_uuid


class ComponentCheckerService(CheckerService):

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.children = self.element.get("children", list())

    def _check_children(self, children, component_uuid_set, component_name_set, data_container=False, **kwargs):
        component_dict = {
            PrintType.FORM: FormCheckerService,
            PrintType.CARDLIST: CardlistCheckerService,
            PrintType.DATALIST: DatalistCheckerService,

            PrintType.TABLE: TableCheckerService,
            # PrintType.BREAK: BREAKCheckerService,

            # PrintType.VISUAL: VISUALCheckerService,
            PrintType.IMAGE: ImageCheckerService,
            PrintType.TEXT: TextCheckerService

        }
        model_uuid = getattr(self, "model_uuid", None)
        parent = {"type": self.type, "model_uuid": model_uuid} if data_container else {}
        kwargs.update({"parent": parent})
        page_info = {"page_px_info": self.page_px_info, "page_size": self.page_size}
        kwargs.update({"page_info": page_info})

        for child in children:
            child_type = child.get("type")
            component_checker_class = component_dict.get(child_type)
            if component_checker_class is None or not issubclass(component_checker_class, ComponentCheckerService):
                continue
            component_checker_service = component_checker_class(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, child, **kwargs)
            component_checker_service.document_other_info = self.document_other_info
            try:
                component_checker_service.check_uuid()
                component_checker_service.check_uuid_unique(component_uuid_set)
                component_checker_service.check_all()
                component_checker_service._check_datasource()
                # component_checker_service.check_name()
                # component_checker_service.check_name_unique(component_name_set)
                if child_type == PrintType.TABLE:
                    component_checker_service.check_children(
                        component_uuid_set, component_name_set, data_container=True)
                elif child_type == PrintType.FORM:
                    component_checker_service.check_children(
                        component_uuid_set, component_name_set, data_container=True)
                elif child_type == PrintType.CARDLIST:
                    component_checker_service.check_children(
                        component_uuid_set, component_name_set, data_container=True)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(component_checker_service)
                raise e
            else:
                self.update_any_list(component_checker_service)

    def _check_datasource(self):
        datasource = self.element.get("data_source")
        attr = self.get_attr_by_name("DATASOURCE")
        if datasource:
            self.update_reference_by_data_source(datasource, self.element, attr)


# 打印模板文档检查
class PrintTemplateCheckerService(ComponentCheckerService):

    def initialize(self, find_association=False,
        model_list=None, relationship_list=None, field_list=None, *args, **kwargs):
        super().initialize(*args, **kwargs)
        self.children = self.element.get("children", list())
        app_log.info(kwargs)
        self.relationship_finder = RelationshipFinder(
            model_list, relationship_list, field_list) if find_association else None
        self.page_finder = PageFinder(
        self.model_in_page, except_form=False, count_container=True,
        find_association=True, model_list=self.app_model_list,
        relationship_list=self.app_relationship_list,
        field_list=self.app_field_dict.values())

    # 检查打印模板图片高度，表单表格格子宽度
    def _check_print_image_hight(self):
        attr_class = Print.ATTR
        children = self.children
        row_nums = self.element.get('row_nums', [])
        for child in children:
            coordinate = child.get('coordinate', 0)
            data_source = child.get('data_source', {})
            is_auto = data_source.get('is_auto')
            image_type = data_source.get('type')
            width = child.get('width', 0)
            height = child.get('height', 0)
            if image_type in [0, 1]:
                height = min(height, width)
            if row_nums and coordinate and height and not is_auto:
                image_top = coordinate[0]
                image_bottom = coordinate[2]
                distance_top = sum(row_nums[:image_top]) + 60
                distance_bottom = sum(row_nums[image_bottom + 1:]) + 35
                out_size = (height - sum(row_nums[image_top:image_bottom + 1]))// 2 + 1
                if out_size > distance_top or out_size > distance_bottom:
                    return_code = LDEC.PRINT_HIGTH_ERROR
                    return_code.message = return_code.message.format(name=self.element_name)
                    self._add_error_list(attr=attr_class.HIGHT, return_code=return_code)

    # 检查容器组件字段绑定
    def _check_datasource_exists(self):
        attr = Print.ATTR.DATASOURCE
        data_source = self.element.get("data_source", {})
        if not data_source:
            return_code = LDEC.PRINT_DATASOURCE_DOESNOT_EXIST
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr, return_code=return_code)

    def _check_print_form_width(self):
        attr = Print.ATTR.WIDTH
        col_nums = self.element.get('col_nums', [])
        page_px_info = self.page_info.get('page_px_info', {})
        page_px_width = page_px_info.get('page_px_width', 0)
        if sum(col_nums) > page_px_width:
            return_code = LDEC.PRINT_WIDTH_ERROR
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr=attr, return_code=return_code)


# 表单
class FormCheckerService(PrintTemplateCheckerService):

    uuid_error = LDEC.PRINT_FORM_UUID_ERROR
    uuid_unique_error = LDEC.PRINT_FORM_UUID_UNIQUE_ERROR
    name_error = LDEC.PRINT_FORM_NAME_FAILED
    name_unique_error = LDEC.PRINT_FORM_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.children = self.element.get("children", list())

    # 检查 表单 的子组件
    def check_children(self, uuid_set, name_set, data_container=True):
        self._check_children(
            self.children, uuid_set, name_set, data_container=data_container, **self.kwargs)

    @checker.run
    def check_datasource_exists(self):
        self._check_print_form_width()
        self._check_datasource_exists()
        self._check_print_image_hight()


# 卡片列表 continue
class CardlistCheckerService(ComponentCheckerService):

    attr_class = PrintCardlist.ATTR
    uuid_error = LDEC.PRINT_CARDLIST_UUID_ERROR
    uuid_unique_error = LDEC.PRINT_CARDLIST_UUID_UNIQUE_ERROR
    name_error = LDEC.PRINT_CARDLIST_NAME_FAILED
    name_unique_error = LDEC.PRINT_CARDLIST_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()

    # 检查 打印卡片列表 的子组件
    def check_children(self, uuid_set, name_set, data_container=True):
        self._check_children(
            self.children, uuid_set, name_set, data_container=data_container, **self.kwargs)

    # @checker.run
    # def check_datasource_exists(self):
    #     self._check_print_form_width()
    #     self._check_datasource_exists()


# 数据列表 continue
class DatalistCheckerService(ComponentCheckerService):

    attr_class = PrintDatalist.ATTR
    uuid_error = LDEC.DATALIST_UUID_ERROR
    uuid_unique_error = LDEC.DATALIST_UUID_UNIQUE_ERROR
    name_error = LDEC.DATALIST_NAME_FAILED
    name_unique_error = LDEC.DATALIST_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.attr_class = PrintDatalist.ATTR

    # 检查容器组件字段绑定    
    def _check_datasource_exists(self):
        attr = PrintDatalist.ATTR.COLUMN
        data_source = self.element.get("data_source", {})
        columns = self.element.get("columns", list())
        for column in columns:
            data_source = column.get('column', {})
            if not data_source:
                return_code = LDEC.PRINT_DATASOURCE_DOESNOT_EXIST
                return_code.message = return_code.message.format(name=self.element_name)
                self._add_error_list(attr, return_code=return_code)

    @checker.run
    def check_columns(self):
        pass
        # self._check_datasource_exists()


# 表格
class TableCheckerService(PrintTemplateCheckerService):

    attr_class = PrintTable.ATTR
    uuid_error = LDEC.PRINT_TABLE_UUID_ERROR
    uuid_unique_error = LDEC.PRINT_TABLE_UUID_UNIQUE_ERROR
    name_error = LDEC.PRINT_NAME_FAILED
    name_unique_error = LDEC.PRINT_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.children = self.element.get("children", list())

    # 检查 表格 的子组件,不会运行
    def check_children(self, uuid_set, name_set, data_container=True):
        self._check_children(
            self.children, uuid_set, name_set, data_container=data_container, **self.kwargs)

    @checker.run
    def check_datasource_exists(self):
        self._check_print_image_hight()
        self._check_print_form_width()


# 分页符
class BREAKCheckerService(ComponentCheckerService):
    pass


# 图片
class ImageCheckerService(PrintTemplateCheckerService):

    uuid_error = LDEC.PRINT_IMAGE_UUID_ERROR
    uuid_unique_error = LDEC.PRINT_IMAGE_UUID_UNIQUE_ERROR
    name_error = LDEC.PRINT_IMAGE_NAME_FAILED
    name_unique_error = LDEC.PRINT_IMAGE_NAME_NOT_UNIQUE

    def initialize(self, find_association=False,
        model_list=None, relationship_list=None, field_list=None, *args, **kwargs):
        super().initialize(*args, **kwargs)
        self.children = self.element.get("children", list())
        app_log.info(kwargs)
        self.relationship_finder = RelationshipFinder(
            model_list, relationship_list, field_list) if find_association else None
        self.page_finder = PageFinder(
        self.model_in_page, except_form=False, count_container=True,
        find_association=True, model_list=self.app_model_list,
        relationship_list=self.app_relationship_list,
        field_list=self.app_field_dict.values())
        self.children = self.element.get("children", list())
        app_log.info(kwargs)

        self.attr_class = PrintImage.ATTR

    # 检查图片字段绑定
    def _check_image_field_exists(self):
        attr = PrintFormAttr.DATASOURCE
        child = self.element
        # 文本22、可视化20、图片21
        child_type = child.get('type', -1)
        data_source = child.get('data_source', {})
        image_type = data_source.get('type')
        # 图片类型 图片表 = 0
        if child_type == 21 and image_type == 0:
            code_data = child.get('data_source', {}).get('code_data', {})
            field_uuid = code_data.get('field', '')
            path = code_data.get("path")
            code_type = code_data.get('type', -1)
            image_info = data_source.get('image')
            # self.update_field_reference(field_uuid,)
            pass
        # 图片类型 图片字段 = 1
        if child_type == 21 and image_type == 1:
            pass
        # 图片类型 条码 = 2
        if child_type == 21 and image_type == 2:
            code_data = child.get('data_source', {}).get('code_data', {})
            field_uuid = code_data.get('field', '')
            path = code_data.get("path")
            code_type = code_data.get('type', -1)
            if code_type:
                if code_type == 3:
                    field_uuid = code_data.get('const', '')
            field_dict = self.app_field_dict.get(field_uuid, dict())
            # #字段未绑定
            if code_type == -1:
                return_code = LDEC.PRINT_FORM_FAILED_UNBOUND
                return_code.message = return_code.message.format(name=self.element_name+'—图片')
                self._add_error_list(attr=attr, return_code=return_code)
            # type= 0、      1 、   5、   2、   4、   3分别对应值类型
            # 文字常量、符号常量、  枚举、 变量、字段、表达式
            elif code_type == 0:
                pass
            elif code_type == 1 and code_data.get('value', 1) == 1:
                pass
                # return_code = LDEC.PRINT_FORM_FAILED_DOESNOT_EXIST
                # return_code.message = return_code.message.format(name=self.element_name+'—图片')
                # self._add_error_list(attr, return_code=return_code)
            elif code_type == 5:
                pass
            elif code_type == 2:
                if not code_data.get('variable_uuid'):
                    return_code = LDEC.PRINT_FORM_FAILED_DOESNOT_EXIST
                    return_code.message = return_code.message.format(name=self.element_name+'—图片')
                    self._add_error_list(attr=attr, return_code=return_code)
            elif code_type == 4:
                # 绑定自己
                if not path and field_uuid:
                    pass
                # 字段不存在
                elif not field_dict and field_uuid:
                        return_code = LDEC.PRINT_FORM_FAILED_DOESNOT_EXIST
                        return_code.message = return_code.message.format(name=self.element_name+'—图片')
                        self._add_error_list(attr, return_code=return_code)
                # 判断是否绑定
                elif path:
                    parent_model = self.element.get('data_source', {}).get('model', '')
                    for p in path:
                        r_finder = self.page_finder.relationship_finder
                        associaton_model = None
                        try:
                            associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                                p, parent_model)
                        except:
                            pass
                        if associaton_model is None:
                            return_code = LDEC.PRINT_FORM_RELATION_PATH_NOT_FOUND#关联关系不存在
                            return_code.message = return_code.message.format(name=self.element_name+'—图片')
                            self._add_error_list(attr, return_code=return_code)
            elif code_type == 3:
                field_exprArr = code_data.get('exprArr')
                # 表达式中引用序号多于占位符序号
                # field_expr = code_data.get('expr')
                # if len(field_expr)//4 + 1 > len(field_exprArr):
                #     return_code = LDEC.PLACEHOLDER_SEQUENCE_NUMBER_DOESNOT_EXIST
                #     return_code.message = return_code.message.format(name=self.element_name+'—图片')
                #     self._add_error_list(attr, return_code=return_code)
                for exprArr_i in field_exprArr:
                    exprArr_value = exprArr_i.get('value', {})
                    path = exprArr_i.get('value', {}).get('path', [])
                    parent_model = exprArr_value.get('model')
                    # 绑定自己
                    if not path and field_uuid:
                        pass
                    # 字段不存在
                    elif not field_dict and field_uuid:
                        return_code = LDEC.PRINT_FORM_FAILED_DOESNOT_EXIST
                        return_code.message = return_code.message.format(name=self.element_name+'—图片')
                        self._add_error_list(attr, return_code=return_code)
                    # 判断关联关系是否存在
                    elif path:
                        for p in path:
                            r_finder = self.page_finder.relationship_finder
                            associaton_model = None
                            try:
                                associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                                    p, parent_model)
                            except:
                                pass
                            if associaton_model is None:
                                pass
                                return_code = LDEC.PRINT_FORM_RELATION_PATH_NOT_FOUND  # 关联关系不存在
                                return_code.message = return_code.message.format(name=self.element_name+'—图片')
                                self._add_error_list(attr, return_code=return_code)

    # 打印模板图片字段引用
    def _update_reference_by_field_info(self):
        code_data = self.element.get("data_source", {}).get('code_data')
        if code_data:
            field_uuid = code_data.get("field")
            title = self.element.get("name")
            attr = self.attr_class.DATASOURCE
            element_uuid = self.element.get("uuid")
            self.update_field_reference(field_uuid, title, element_uuid, attr)

    @checker.run
    def check_datasource_exists(self):
        self._check_image_field_exists()
        self._update_reference_by_field_info()
        # self._check_print_image_hight()


# 可视化组件
class VISUALCheckerService():
    pass


class TextCheckerService(ComponentCheckerService):

    attr_class = PrintText.ATTR
    uuid_error = LDEC.PRINT_TEXT_UUID_ERROR
    uuid_unique_error = LDEC.PRINT_TEXT_UUID_UNIQUE_ERROR
    name_error = LDEC.PRINT_TEXT_NAME_FAILED
    name_unique_error = LDEC.PRINT_TEXT_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()


# 打印检查器服务
class PrintCheckerService(ComponentCheckerService):

    attr_class = Print.ATTR
    uuid_error = LDEC.PRINT_UUID_ERROR
    uuid_unique_error = LDEC.PRINT_UUID_UNIQUE_ERROR
    name_error = LDEC.PRINT_NAME_FAILED
    name_unique_error = LDEC.PRINT_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.open_type = self.element.get("open_type", 0)
        self.children = self.element.get("children", list())
        self.print_finder = PrintFinder()
        self.print_finder.find_func(self.element)
        self.component_uuid_set = set()
        self.component_name_set = set()
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
        self.data_source = self.element.get("data_source")
        self.a = self.children
    def build_insert_query_data(self):
        return {
            PrintModel.app_uuid.name: self.app_uuid,
            PrintModel.module_uuid.name: self.module_uuid,
            PrintModel.document_uuid.name: self.document_uuid,
            PrintModel.print_uuid.name: self.element_uuid,
            PrintModel.print_name.name: self.element_name,
            PrintModel.form_count.name: self.print_finder.form_count,
            PrintModel.container_model.name: self.print_finder.model_in_page,
            PrintModel.ext_tenant.name: self.ext_tenant
        }

    def build_update_query_data(self):
        return {
            PrintModel.print_name.name: self.element_name,
            PrintModel.form_count.name: self.print_finder.form_count,
            PrintModel.container_model.name: self.print_finder.model_in_page,
            PrintModel.is_delete.name: False
        }

    def build_update_query(self):
        query_data = self.build_update_query_data()
        return PrintModel.update(**query_data).where(
            PrintModel.print_uuid==self.element_uuid)

    @staticmethod
    def build_delete_query(print_uuid):
        return PrintModel.update(**{
                PrintModel.is_delete.name: True
            }).where(PrintModel.print_uuid==print_uuid)

    def check_modify(self, document_print_uuid_set):
        if self.element_uuid in document_print_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

    # 检查打印模板尺寸
    def check_size(self):
        attr = Print.ATTR.SIZE
        format = self.element.get("format")
        if format == "custom":
            size = self.element.get("size")
            element_data = {"format": format, "size": size}
            data_error = True
            if isinstance(size, list):
                if len(size) == 2:
                    w, h = size
                    if w != 0 and h != 0:
                        data_error = False
            if data_error is True:
                return_code = LDEC.PRINT_SIZE_ERROR
                self._add_error_list(attr=attr, return_code=return_code, element_data=element_data)

    # 检查打印模板页面宽度
    def check_page_width(self):
        attr = Print.ATTR.PAGE_WIDTH
        page_width = self.element.get("page_width")
        page_width = 0 if not page_width else page_width
        margin = self.element.get("margin")
        element_data = {"page_width": page_width, "margin": margin}
        page_width_error = False
        if isinstance(margin, list) and len(margin) == 4:
            right_size, left_size = margin[1], margin[3]
            if not isinstance(right_size, int) or not isinstance(left_size, int):
                page_width_error = True
            if page_width < right_size + left_size:
                return_code = LDEC.PRINT_PAGE_WIDTH_LESS_THAN_MARGIN
                self._add_error_list(attr=attr, return_code=return_code, element_data=element_data)
        else:
            page_width_error = True
        if page_width_error:
            return_code = LDEC.PRINT_PAGE_WIDTH_ERROR
            self._add_error_list(attr=attr, return_code=return_code, element_data=element_data)
        if any(not isinstance(obj, int) or obj < 0 for obj in margin):
            return_code = LDEC.PRINT_PAGE_MARGINS_ERROR
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查打印模板所有 children
    def check_children(self, **kwargs):
        self._check_children(self.children, self.component_uuid_set, self.component_name_set, **kwargs)

    def _check_field_info(self, field_list, title, element_uuid, attr):
        for field_info in field_list:
            field_uuid = field_info.get("field")
            r_path = field_info.get("path", [])
            for r_uuid in r_path:
                self.update_relationship_reference(r_uuid, title, element_uuid, attr)
            self.update_field_reference(field_uuid, title, element_uuid, attr)

    # def _check_model(self):
    #     data_source = self.data_source
    #     print('data_source=', self.element)#.get("data_source"
    #     print('1')

    @checker.run
    def runfun(self):
        self.check_page_width()
        self.check_size()
        # self._check_model()


# 打印文档检查器服务
class PrintDocumentCheckerService(DocumentCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str,
        element: dict, document_version: int,
        app_print_list: list, app_field_dict, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, PrintModel, *args, **kwargs)
        self.document_version = document_version
        self.app_field_dict = app_field_dict
        self.app_print_uuid_set = set()
        self.module_print_name_set = set()
        self.document_print_uuid_set = set()

        for print_dict in app_print_list:
            print_uuid = print_dict.get("print_uuid", "")
            print_name = print_dict.get("print_name", "")
            print_module_uuid = print_dict.get("module_uuid", "")
            print_document_uuid = print_dict.get("document_uuid", "")

            # 找到原文档中所有的 打印模板 ，为了新增、更新、删除文档的 打印模板
            if print_document_uuid == self.document_uuid:
                self.document_print_uuid_set.add(print_uuid)
            else:
                # 排除当前文档所有的 print_uuid ，获取应用的所有 print_uuid
                self.app_print_uuid_set.add(print_uuid)
                # 排除当前文档所有的 print_name ，获取模块的所有 print_name
                if print_module_uuid == self.module_uuid:
                    self.module_print_name_set.add(print_name)

    def check_print(self):
        if self.new_ext_doc:
            need_check = True
        else:
            is_ext = self.element.get("is_extension")
            need_check = True
            if self.ext_tenant and not is_ext:
                need_check = False
        print_checker_service = PrintCheckerService(
            self.app_uuid, self.module_uuid, self.module_name,
            self.document_uuid, self.document_name, self.element, ext_tenant=self.ext_tenant,
            is_copy=self.is_copy)
        print_checker_service.document_other_info = self.document_other_info
        print_uuid = print_checker_service.element_uuid
        try:
            print_checker_service.check_uuid()
            if self.is_copy:
                print_checker_service.check_uuid_unique(self.app_print_uuid_set)
            else:
                if need_check:
                    print_checker_service.check_uuid_unique(self.app_print_uuid_set)
            # print_checker_service.check_name()
            # print_checker_service.check_name_unique(self.module_print_name_set)
            print_checker_service.check_all()
            print_checker_service.check_children(app_field_dict=self.app_field_dict, 
                                                 app_relationship_list=self.app_relationship_list,
                                                 app_model_list=self.app_model_list)
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(print_checker_service)
            raise e
        else:
            self.update_any_list(print_checker_service)

        # 找到新增 或 更新的 打印模板
        if need_check:
            print_checker_service.check_modify(self.document_print_uuid_set)
        if print_checker_service.insert_query_list:
            self.document_insert_list.extend(print_checker_service.insert_query_list)
        if print_checker_service.update_query_list:
            self.document_update_list.extend(print_checker_service.update_query_list)

        # 找出删除的 打印模板 ，将其 is_delete 置为 True
        delete_print_uuid_set = self.document_print_uuid_set - set([print_uuid])
        for this_print_uuid in delete_print_uuid_set:
            query = print_checker_service.build_delete_query(this_print_uuid)
            self.document_delete_list.append(query)

    @checker.run
    def runfun(self):
        self.check_print()
