import time
from typing import Type<PERSON><PERSON>, Optional, Type

from pydantic import BaseModel

from apps.exceptions import InvalidContext, ClientRPCStatusError
from baseutils.log import app_log
from runtime.engine import engine
from runtime.metrics.business_metrics import RuntimeMetrics
from runtime.protocol.client_rpc import Client<PERSON>CCommand, ClientRPCRequest, ClientRPCStatus

ClientRPCRes_T = TypeVar("ClientRPCRes_T", bound=BaseModel)


async def call_client_rpc(
        sid: Optional[str],
        command: ClientRPCCommand,
        payload: Optional[BaseModel],
        response_class: Optional[Type[ClientRPCRes_T]] = None,
        need_result: bool = True
) -> Optional[ClientRPCRes_T]:
    if payload is None:
        payload = {}
    client = engine.clients.get(sid)
    if client is None:
        app_log.error("client not found for sid %s", sid)
        raise InvalidContext("page context required")
    request = ClientRPCRequest(command=command, payload=payload)
    error: Optional[Exception] = None
    start = time.time()
    try:
        app_log.info("[Client RPC] create request: %s", request)
        res = await client.send_client_rpc_request(request, need_result)
        if res.code != ClientRPCStatus.OK:
            raise ClientRPCStatusError(res.code, res.message)
    except Exception as e:
        error = e
        app_log.exception("client send failed: %s", e)
        raise
    finally:
        duration = time.time() - start
        # RuntimeMetrics.report_client_rpc(command, type(error), duration)
        app_log.info("[Client RPC] command: %s, error: %s, duration: %.4fs", command, error, duration)

    if response_class is None:
        return None
    return response_class.parse_obj(res.data)
