# -*- coding:utf-8 -*-


import asyncio
from sanic import Blueprint
from sanic.response import json
from apps.utils import LemonDictResponse as LDR, process_args
from agent.const import API
from runtime.utils import LemonHTTPMethodView as HTTPMethodView
from agent.manage import agent_server


API_NAME_VERSION = "_".join([API.NAME, API.V1])
url_prefix = "/api/" + "/".join([API.NAME, API.V1])
bp = Blueprint(API_NAME_VERSION, url_prefix=url_prefix)


class CheckAppAlive(HTTPMethodView):

    async def get(self, request):
        return json(LDR(data=list()))


class GetRuntimeStatus(HTTPMethodView):

    async def get(self, request):
        status = agent_server.app_status
        return json(LDR(data=status))


class DeployApp(HTTPMethodView):

    async def post(self, request):
        with process_args():
            secret = str(request.json.get("secret", ""))
            app_env = str(request.json.get("app_env", ""))
        app_status = await agent_server.deploy_new_version(secret, app_env)
        return json(LDR(data=app_status))


class StopApp(HTTPMethodView):

    async def post(self, request):
        with process_args():
            secret = str(request.json.get("secret", ""))
        await agent_server.stop_for_deploy(secret)
        return json(LDR(data={}))


class DeubgApp(HTTPMethodView):

    async def post(self, request):
        with process_args():
            secret = str(request.json.get("secret", ""))
        token = await agent_server.debug_app(secret)
        return json(LDR(data={'token': token}))


bp.add_route(CheckAppAlive.as_view(), "/check_app_alive.json")
bp.add_route(GetRuntimeStatus.as_view(), "/get_runtime_status.json")
bp.add_route(DeployApp.as_view(), "/deploy_app.json")
bp.add_route(StopApp.as_view(), "/stop_app.json")
bp.add_route(DeubgApp.as_view(), "/debug_app.json")