from enum import Enum


class ScopeType(str, Enum):
    GLOBAL = "global"
    APP = "app"
    MODULE = 'module'
    DOC = 'doc'


class UniqueName(str, Enum):
    UUID = "uuid"
    PAGE_URL = "page_url"


class ReferenceAttrType(str, Enum):
    MODEL = "model"
    MODEL_FIELD = "model_field"
    MODEL_RELATIONSHIP = "model_relationship"
    PAGE = "page"
    FUNC = "func"
    ENUM = "enum"
    ENUM_ITEM = "enum_item"
    PRINT = "print"
    WORKFLOW = "workflow"
    CONST = "const"
    LABEL_PRINT = "label_print"
    IMAGE = "image"
    MODULE_ROLE = "module_role"
    

class SchemaKeys(str, Enum):

    description = "description"
    type = "type"
    required = "required"
    default = "default"
    properties = "properties"
    items = "items"
    reference = "reference"
    anyOf = "anyOf"
    ref = "$ref"
    attr_name = "attr_name"
    is_element = "is_element"
