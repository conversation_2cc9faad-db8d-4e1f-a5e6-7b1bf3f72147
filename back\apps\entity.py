# -*- coding:utf-8 -*-

import os
import time
# from functools import partial

# import peeweedbevolve
from peewee import Model, SQL
from peewee import (
    FixedCharField, CharField, BooleanField, IntegerField, TextField, DecimalField,
    ModelSelect, VirtualField, BlobField, Field
)
from playhouse.shortcuts import model_to_dict
# from playhouse.hybrid import hybrid_property

from apps import COMPANY_KEY
from apps.peewee_ext import (
    JsonField, MediumTextField
)
from apps.base_entity import db_proxy, make_table_name, User, DesignBaseModel

user_db_proxy = db_proxy

"""
Field 自带的default，是peewee如何处理默认值，[SQL('DEFAULT ""')]是真正数据库的操作
"""

__all__ = [
    "user_db_proxy",
    "BaseModel",
    "User",
]


class MySQLTimestampField(Field):
    field_type = 'TIMESTAMP(3)'


class LemonDesignModelSelect(ModelSelect):

    def __init__(self, model, fields_or_models, is_default=False, with_sys=False):
        self.with_sys = with_sys
        super(LemonDesignModelSelect, self).__init__(
            model, fields_or_models, is_default=is_default)


class BaseModel(DesignBaseModel):

    @classmethod
    def select(cls, *fields, with_sys=False):
        is_default = not fields
        if not fields:
            fields = cls._meta.sorted_fields
        return LemonDesignModelSelect(
            cls, fields, is_default=is_default, with_sys=with_sys)

    def to_dict(self, recurse=False, fields_from_query=None):
        value_dict = model_to_dict(self, recurse=recurse, fields_from_query=fields_from_query)
        return value_dict

    class Meta:
        database = user_db_proxy
        legacy_table_names = False
        table_function = make_table_name
        evolve = False
        version_control = False
        version_key = "document_uuid"


class Login(BaseModel):
    user_uuid = FixedCharField(max_length=32, unique=True)
    login_timestamp = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True


class Policy(BaseModel):
    group_id = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    resource_id = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    action = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid", "resource_id", "action"), False),
        )


class ExtPolicy(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    resource_id = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    visible = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    additional = JsonField(default={}, constraints=[SQL('DEFAULT ("{}")')],
                           null=True)  # {"extendable": true, "new": []}
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta:
        evolve = True
        indexes = (
            (("app_uuid", "resource_id", "visible"), False),
        )


class Combine(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_owner = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    visible = BooleanField(default=True, constraints=[SQL('DEFAULT True')])
    permission = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0: 查看且编辑  1: 仅查看
    publish_permission = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 1<<1: 测试  1<<2: 正式
    team_uuid = FixedCharField(max_length=32, default=None, null=True, constraints=[SQL('DEFAULT NULL')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("user_uuid", "app_uuid"), True),
        )


class AppManager(BaseModel):
    # create view app_manager as (select app.manager_uuid, app.app_uuid, app.is_delete from lemon.app)
    manager_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_uuid = FixedCharField(max_length=32, unique=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        table_name = "lemon_app_manager"
        evolve = False
        indexes = (
            (("user_uuid", "app_uuid"), False),
        )


class APP(BaseModel):
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_uuid = FixedCharField(max_length=32, unique=True)
    app_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    app_description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    status = IntegerField(default=1, constraints=[SQL("DEFAULT 1")])  # 1上线 0下线
    create_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    app_color = CharField(max_length=10, default="", constraints=[SQL('DEFAULT ""')])
    app_icon = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    app_version = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    app_revision = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    app_version_test = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    app_revision_test = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    version_number = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    version_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    test_version_number = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    test_version_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    publish_app = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    publish_pc = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    delete_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    version_control = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    project_id = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    project_repo = FixedCharField(default=None, constraints=[SQL("comment 'project repo'")])
    middle_user = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        schema = "lemon"
        evolve = True
        indexes = (
            (("user_uuid",), False),
        )


class APPPublish(BaseModel):
    """记录应用发布信息"""
    app_uuid = FixedCharField(max_length=32)
    environment = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # pro / test / sandboxN
    app_version = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    app_revision = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    version_number = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    version_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    lemon_version = FixedCharField(max_length=16, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        schema = "lemon"
        evolve = True
        indexes = (
            (("app_uuid", "environment"), True),
        )


class AppGroup(BaseModel):

    app_id = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    group_id = IntegerField(default=None, null=True, constraints=[SQL("DEFAULT NULL")])

    class Meta():
        schema = "lemon"
        evolve = True
        indexes = (
            (("app_id", "group_id"), True),
        )


class PublishGroup(BaseModel):

    group_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    group_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    tenant_id = IntegerField(default=None, null=True, constraints=[SQL("DEFAULT NULL")])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        schema = "lemon"
        evolve = True
        indexes = (
            (("group_uuid", ), True),
            (("tenant_id", ), True),
        )


class APPSetting(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    permission_check = BooleanField(default=True, constraints=[SQL('DEFAULT True')])
    anonymous = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    anonymous_role = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid",), False),
            (("document_uuid",), False),
            (("document_uuid", "user_uuid", "branch_uuid"), False),
            (("user_uuid", "branch_uuid"), False),
        )


class APPExtension(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    deployment = JsonField(default={}, constraints=[SQL('DEFAULT ("{}")')], null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid", "tenant_uuid"), False),
        )


class APPBranch(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, unique=True)
    branch_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    create_timestamp = IntegerField(default=time.time, constraints=[SQL('DEFAULT 0')])
    status = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 创建分支的状态 0 分析项目 1 完成 2 写入项目
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
        )


class APPUserBranch(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, unique=False)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    checkout_timestamp = IntegerField(default=time.time, constraints=[SQL('DEFAULT 0')])
    status = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 读取分支的状态 0 分析项目 1 完成 2 读取项目
    is_active = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class Module(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    module_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    extend_from = IntegerField(default=0, constraints=[SQL('DEFAULT 0')], null=True)  # 记录extension表pk
    extension_type = IntegerField(default=0, null=True)  # 模板, 插件 ...
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_description = CharField(max_length=512, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        version_key = "module_uuid"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class ToolCategory(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    category_uuid = FixedCharField(max_length=32, unique=True)
    category_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    category_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    category_sort = DecimalField(max_digits=16, decimal_places=6, default=0,
                                 constraints=[SQL('DEFAULT 0')])  # 默认使用时间戳排序
    description = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    entity_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("category_type", "app_uuid", "entity_type", "category_name"), True),
            # (("category_name", "category_type", "entity_type"), False),
        )


# 为何工具分两张表，系统工具表相对稳定，用户工具表则会经常变更
# 若不分两张表，查询添加系统工具会比较麻烦
# 但是分两张表，查询系统工具，也比较麻烦
class SYSTool(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    category_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tool_uuid = FixedCharField(max_length=32, unique=True)
    tool_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    tool_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 工具分类：系统工具、用户工具
    tool_class = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 工具种类：云函数、页面组件。。。等等
    description = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    content = JsonField(null=True)  # 指向某个云函数UUID、页面组件UUID。。。等等
    icon_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    icon = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])  # 系统工具保存icon名称，前端自己调本地文件
    # drag_type = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])  # 绘制分类：某个工具作用到哪个区域
    sort = DecimalField(max_digits=16, decimal_places=10, default=0, constraints=[SQL('DEFAULT 0')])  # 排序使用
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid", "category_uuid"), False),
            (("category_uuid", "tool_name"), True),
        )


class UserTool(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    package_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    category_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tool_uuid = FixedCharField(max_length=32, unique=True)
    tool_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    tool_type = IntegerField(default=1, constraints=[SQL('DEFAULT 1')])  # 工具分类：系统工具、用户工具
    tool_class = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 工具种类：云函数、页面组件。。。等等
    description = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    content = JsonField(null=True)  # 指向某个云函数UUID、页面组件UUID。。。等等
    icon_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    icon = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])  # 用户工具保存图标库 或 图片库UUID，返回URL给前端
    icon_color = FixedCharField(max_length=30, null=True, default="", constraints=[SQL('DEFAULT ""')])
    # drag_type = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])  # 绘制分类：某个工具作用到哪个区域
    extend_from = IntegerField(default=0, constraints=[SQL('DEFAULT 0')], null=True)  # 记录extension表pk
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid", "category_uuid"), False),
            (("package_uuid",), False),
            (("app_uuid", "category_uuid", "tool_name"), True)
        )


class Document(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    document_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    document_puuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_path = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    document_rname = CharField(max_length=64, default="", constraints=[SQL('DEFAULT ""')])  # 用户输入的实际文件名
    document_version = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    document_number = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    order = DecimalField(max_digits=16, decimal_places=10, default=0, constraints=[SQL('DEFAULT 0')])
    create_timestamp = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    update_timestamp = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    source = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("module_uuid",), False),
            (("document_puuid",), False),
            (("document_path",), False),
            (("app_uuid", "document_type"), False),
            (("document_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )




class DocumentOrder(BaseModel):
    # 此表已废弃
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    father_uuid = FixedCharField(max_length=32, unique=True, help_text='可以是模块的uuid, 也可以是目录的uuid')
    document_uuid_order_list = JsonField(verbose_name="document的排序方式")

    class Meta():
        evolve = True
        schema = "lemon"


class DocumentContent(BaseModel):
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_content = JsonField(null=True)
    source_uuid = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    timestamp = MySQLTimestampField(constraints=[SQL('DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("document_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class DocumentDraft(BaseModel):
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_content = JsonField(null=True)
    timestamp = MySQLTimestampField(constraints=[SQL('DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)')])

    class Meta():
        evolve = True
        indexes = (
            (("document_uuid",), True),
        )


class DocumentReference(BaseModel):
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    field_reference = JsonField(null=True)
    model_reference = JsonField(null=True)
    r_reference = JsonField(null=True)
    func_reference = JsonField(null=True)
    enum_reference = JsonField(null=True)
    page_reference = JsonField(null=True)
    print_reference = JsonField(null=True)
    label_print_reference = JsonField(null=True)
    wf_reference = JsonField(null=True)
    enum_item_reference = JsonField(null=True)
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid", "document_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
            (("document_uuid",), False),
        )


class DocumentLinks(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    link_document = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)   # 引用的文档id
    link_type = FixedCharField(max_length=30, default=None, constraints=[SQL('DEFAULT NULL')], null=True)       # 引用资源类型，model/field/etc
    link_resource = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)   # 引用的资源id
    link_data = JsonField(null=True)

    class Meta:
        evolve = True
        indexes = (
            (("app_uuid", "document_uuid", "link_document"), False),
            (("app_uuid", "document_uuid", "link_type", "link_resource"), True)
        )


class ModelField(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    model_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    field_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    field_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    display_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    field_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    sort = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    length = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    decimals = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 小数位
    default = TextField(null=False, default="", constraints=[SQL('DEFAULT ""')])
    enum_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    calculate_field = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    calculate_function = JsonField(null=True)  # 值编辑器表达式
    generated_function = JsonField(null=True)  # 值编辑器表达式
    calculate_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0 表达式  1 云函数
    calculate_func = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_required = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_visible = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_serial = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    serial_prefix = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    serial_prefix_info = JsonField(null=True, default={})
    serial_num_length = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    serial_rule = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 流水号的生成规则  0 前缀+日期+流水号  1 前缀+流水号
    serial_gen_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 流水号的生成方式  0 预生成  1 后生成
    serial_start_num = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 流水号的起始值
    check_function = JsonField(null=True)  # 值编辑器表达式
    check_error_message = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_unique = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    aggre_field = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    aggre_func = JsonField(null=True)  # 值编辑器表达式
    hierarchy_field = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    hierarchy_list = JsonField(null=True)
    hide = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("model_uuid",), False),
            (("field_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class ModelIndex(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    model_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    index_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    index_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_unique = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    fields = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("model_uuid",), False),
            (("index_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class ModelBasic(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    model_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    model_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    display_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_temp = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    fields = JsonField(null=True)
    events = JsonField(null=True)
    position = JsonField(null=True)
    name_field = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    data_field = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # Deprecated
    sort_field = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # Deprecated
    check_field = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # Deprecated
    data_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])  # Deprecated
    user_model = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # # Deprecated这行数据所属用户关联
    department_model = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # # Deprecated 这行数据所属部门关联
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    is_import = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    # {"uuid": "", "table_id": "001", "action_idx": 1, "pk_bind": 10}
    # pk_bind绑定的外部pk字段类型，没有这个key或者pk_bind为null表示外部pk没有绑定到模型的系统字段externpk上
    import_binding = JsonField(null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_sys_model = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    select_enabled = BooleanField(default=True, constraints=[SQL('DEFAULT True')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("model_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class RelationshipBasic(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    relationship_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    relationship_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    display_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    relationship_type = IntegerField(default=0,
                                     constraints=[SQL('DEFAULT 0')])  # 0: ONE_TO_MANY  1: MANY_TO_MANY  2: ONE_TO_ONE
    source_model = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 外键模型
    target_model = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 目标模型
    frontref = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])  # 外键模型调用目标模型数据的指针
    backref = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])  # 目标模型回调外键模型的指针
    source_model_on_delete = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 外键模型有数据删除时，对目标模型所做的操作
    target_model_on_delete = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 目标模型有数据删除时，对外键模型所作的操作
    extra = JsonField(null=True)  # 放一些额外的数据
    is_system = BooleanField(default=False, constraints=[SQL('DEFAULT False')])  # 是否为系统创建的关联
    system_relationship = BooleanField(default=False, constraints=[SQL('DEFAULT False')])  # 是否为与系统表建立的关联
    to_field_name = CharField(max_length=30, default="id", constraints=[SQL('DEFAULT ""')])  # 关联target_model的字段名
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    # ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("source_model",), False),
            (("target_model",), False),
            (("relationship_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class Page(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    page_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    page_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    open_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    datalist_count = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    cardlist_count = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    datagrid_count = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    form_count = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    container_model = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("page_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class Print(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    print_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    print_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    form_count = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    container_model = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("print_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )

class LabelPrint(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    label_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    label_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    container_model = JsonField(null=True)
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    print_other_info = JsonField(null=True)
    
    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("module_uuid",), False),
            (("app_uuid",), False),
            (("document_uuid",), False),
            (("label_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class RuleChain(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    rule_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    rule_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    system_service = BooleanField(default=True, constraints=[SQL('DEFAULT True')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    variable_list = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("rule_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class StateMachine(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    sm_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    sm_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    display_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    multi_instance = BooleanField(default=True, constraints=[SQL('DEFAULT True')])
    strict_mode = BooleanField(default=True, constraints=[SQL('DEFAULT True')])
    variable_list = JsonField(null=True)
    states = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("sm_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class State(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    sm_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    state_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    state_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    state_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    variable_list = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("sm_uuid",), False),
            (("state_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class Event(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    sm_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    event_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    event_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    arg_list = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("sm_uuid",), False),
            (("event_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class Workflow(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    wf_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    wf_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    online_version = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    variables = JsonField(null=True)
    nodes = JsonField(null=True)
    type = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 工作流类型:  0-标准, 1-简易
    need_schedule = BooleanField(default=False, constraints=[SQL('DEFAULT False')])  # 是否需要定时发起
    modify_time = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    pc_from = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    mobile_from = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    pad_from = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("wf_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


# class Connector(BaseModel):
#     # connector定义文档
#     app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
#     ct_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
#     ct_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
#     document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

#     class Meta():
#         schema = 'lemon'
#         evolve = True
#         indexes = (
#             (("app_uuid",), False),
#             (("ct_uuid",), False),
#             (("document_uuid",), False),
#         )

# class ConnectorAction(BaseModel):
#     # connector动作
#     app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
#     ct_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
#     document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

# class ConnectorAppConfig(BaseModel):
#     # connector配置
#     app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
#     document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

#     class Meta():
#         schema = 'lemon'
#         evolve = True
#         indexes = (
#             (("app_uuid",), False),
#             (("document_uuid",), False),
#         )


class ConnectorTenantConfig(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    connector_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        schema = 'lemon'
        version_control = True
        evolve = True
        indexes = (
            (("app_uuid", "tenant_uuid"), False),
            (("document_uuid",), False),
            (("connector_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class Func(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    func_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    func_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    func = MediumTextField(null=False, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    icon_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    icon = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    install_sm_toolbox = BooleanField(default=True, constraints=[SQL('DEFAULT True')])
    sm_tool_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    sm_tool_category = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    arg_list = JsonField(null=True)
    return_list = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_sys_func = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    from_py_module = BooleanField(default=False, null=False, constraints=[SQL('DEFAULT False')])
    py_module_line_no = IntegerField(default=0, null=False, constraints=[SQL('DEFAULT 0')])
    front_module = CharField(max_length=255, default="", null=True, constraints=[SQL('DEFAULT NULL')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("func_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class ImageTable(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    image_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    image_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    image_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    # md5 = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    width = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    height = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    # storage_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    url = CharField(max_length=500, default="", constraints=[SQL('DEFAULT ""')])
    # s_url = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("image_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class ConstTable(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    const_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    const_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    const_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    value = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("const_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class RestfulTable(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    restful_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    restful_name = FixedCharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')])
    auth_type = IntegerField(default=None, null=True, constraints=[SQL('DEFAULT NULL')])
    restful_uri_list = JsonField(null=True)
    requests = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("restful_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class JsonTable(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    json_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    json_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    value = TextField(null=False, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("json_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class EnumTable(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    enum_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    enum_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    value = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("enum_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class Navigation(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    navigation_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    navigation_style = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    position = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    platform = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    mainpage = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    mainpage_title = CharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')])
    userpage = JsonField(null=True)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    active_key = JsonField(null=True, default=list())

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("navigation_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class NavigationItem(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    navigation_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    item_name = CharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')])
    item_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    item_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    order = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    level = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    master = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    icon_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    icon = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])  # 系统工具保存icon名称，前端自己调本地文件
    icon_color = FixedCharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])  # 系统工具保存icon名称，前端自己调本地文件
    page_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    page_url = CharField(max_length=2048, default="", constraints=[SQL('DEFAULT ""')])
    param = JsonField(null=True)  # 值编辑器表达式
    permission = JsonField(null=True)
    item_title = CharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    icon_text = IntegerField(null=True)   # 显示：1仅文字，2图标加文字，3仅图标
    item_position = IntegerField(null=True)   # 位置：1左侧，2上侧
    badge = JsonField(null=True)  # 徽标数
    permission_config = JsonField(null=True, default={})  # 权限资源配置

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("app_uuid",), False),
            (("navigation_uuid",), False),
            (("item_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class UserRole(BaseModel):
    # 用户角色
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    role_uuid = FixedCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="角色标识符")
    role_name = CharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""')], verbose_name="角色名")
    order = IntegerField(
        default=0, constraints=[SQL('DEFAULT 0')], verbose_name="顺序")
    is_admin = BooleanField(
        default=False, constraints=[SQL('DEFAULT False')], verbose_name="是否系统管理员")
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        display_name = "用户角色"
        ignore_tenant = True
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid", "role_uuid"), False),
            (("role_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class UserRoleContent(BaseModel):
    # 这个表的内容后面会改成 document_content 来存储 @chen.wenbo
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_role = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_role = JsonField(null=True, default=[])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("module_uuid", "user_role"), False),
            (("user_uuid", "branch_uuid"), False),
        )


class ModuleRole(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    role_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    role_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    # 发布的时候从document content取数据才能取到ext的数据
    pages = JsonField(null=True)
    funcs = JsonField(null=True)
    models = JsonField(null=True)
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("document_uuid",), False),
            (("app_uuid", "module_uuid"), False),
            (("role_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class RuntimeAccount(BaseModel):
    # 运行时测试账号
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    # account = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    # mobile_phone = CharField(max_length=20, default="", constraints=[SQL('DEFAULT ""')])
    # full_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    user_role = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_test = BooleanField(default=True, constraints=[SQL('DEFAULT True')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid", "user_uuid"), False),
        )


class Client(BaseModel):
    # 此表已废弃
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    client_uuid = FixedCharField(max_length=32, unique=True)
    client_secret = FixedCharField(max_length=64, default="", constraints=[SQL('DEFAULT ""')])
    client_type = FixedCharField(max_length=20, default="public")
    redirect_uri = CharField(max_length=255)
    scope = FixedCharField(max_length=32)
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        indexes = (
            (("user_uuid",), False),
        )


class CheckMessage(BaseModel):
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_version = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    error = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    error_message = JsonField(null=True)
    warning = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    warning_message = JsonField(null=True)
    info = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    info_message = JsonField(null=True)
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        indexes = (
            (("document_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
            (("error", ), False),
        )


class AppPublishMessage(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_revision = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    message_level = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0 info 1 warning 2 error
    message = JsonField(null=True)
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid", "user_uuid", "branch_uuid"), False),
            (("user_uuid", "branch_uuid"), False),
        )


class AuthorizedWX(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # lemon应用id
    appid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 微信公众号或小程序 appid
    refresh_token = CharField(default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    func_info = JsonField(null=True)
    auditid = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 微信代码审核id
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    media_dict = JsonField(null=True)

    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("app_uuid", ), True),
            (("appid", ), True),
        )


class SubmitAuditWX(BaseModel):

    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # lemon应用id
    appid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 微信公众号或小程序 appid access_token
    auditid = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 微信代码审核id
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    media_info = JsonField(null=True)
    media_submit_time = IntegerField(null=True, constraints=[SQL("DEFAULT 0")])  # 提交media时间

    auditstatus = IntegerField(default=2, constraints=[SQL('DEFAULT 0')])  # 0: 审核成功, 1: 审核被拒绝, 2: 审核中, 3: 已撤回, 4: 审核延后
    submit_info = JsonField(null=True)
    submit_time = IntegerField(null=True, constraints=[SQL("DEFAULT 0")])  # 提交审核时间


    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("auditid", ), True),
        )


class AppConfig(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_setting = JsonField(null=True)

    class Meta():
        evolve = True
        indexes = (
            (("app_uuid",), False),
        )


class Connector(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    connector_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    connector_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    connector_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("connector_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class TemplateTable(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    template_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    template_name = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    template = JsonField(null=True)
    template_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    state = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("template_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class ExportTemplate(BaseModel):

    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    template_uuid = FixedCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="模板标识符")
    template_name = CharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""')], verbose_name="模板名")
    template = JsonField(null=True, verbose_name="模板内容")
    template_type = IntegerField(
        default=0, constraints=[SQL('DEFAULT 0')], verbose_name="模板类型")
    operation_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')], verbose_name="操作类型")
    state = BooleanField(default=False, constraints=[SQL('DEFAULT False')], verbose_name="是否激活")
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    ext_tenant = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        display_name = "导出模板"
        ignore_tenant = True
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("template_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )


class Extension(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    extension_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 模块商城每个模块的独有uuid
    extension_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 导出的模块/应用实际名
    show_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 文档中配置的展示名
    publisher_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 发行商名称
    publisher = IntegerField(default=None, null=True, constraints=[SQL('DEFAULT 0')])  # 发行商pk
    # extension_info = JsonField(null=True)  # 导出的文档
    private = BooleanField(default=True, constraints=[SQL('DEFAULT True')])  # 私用: 0/公开: 1
    scope = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # ScopeType 0,1,2
    version = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    release_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 发布类型-> 0:模板 1:插件 2:解决方案 3:应用
    business = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 行业类型, 筛选用
    cover = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])  # 封面图
    preview = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])  # 预览图
    introduction = CharField(max_length=500, default="", constraints=[SQL('DEFAULT ""')])
    on_shelf_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 发布到私有的时间
    public_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 公开到商城的时间
    update_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 最新版本的更新时间
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("extension_uuid",), False),
            (("publisher",), False),
        )


class ExtensionDetail(BaseModel):
    extension_id = IntegerField(default=0, null=False, constraints=[SQL('DEFAULT 0')])
    extension_info = JsonField(null=True)  # 导出的文档

    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("extension_id",), True),
        )


class ImportRecord(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 导给的应用
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 导给的用户
    import_type = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 导入类型, 0: 自己导入 / 1: 发行商传输
    extension_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    extension_pk = IntegerField(default=0, null=True, constraints=[SQL("DEFAULT 0")])
    action_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 确认导入的时间
    action = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 是否已导入, 0: 等待, 1: 已接受; 2: 已拒绝
    apply_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 发起导入的时间
    apply_user_uuid = FixedCharField(max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True)  # 导出申请人
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("extension_uuid",), False),
        )


class Publisher(BaseModel):
    name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("name",), True),
        )


class AppletCodeManager(BaseModel):
    template_id = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    version = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    description = CharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    env = CharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        schema = "lemon"


class Sandbox(BaseModel):
    sandbox_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    sandbox_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_version = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    app_revision = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    version_number = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    version_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    is_active = BooleanField(default=True, constraints=[SQL('DEFAULT True')])
    sub_env = CharField(max_length=10, default="", constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("sandbox_uuid",), True),
            (("sandbox_uuid", "app_uuid"), True),
        )


class SubEnv(BaseModel):
    sandbox_uuid = FixedCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    version_time = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    sub_env = CharField(max_length=10, default="",
                        constraints=[SQL('DEFAULT ""')])

    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("sandbox_uuid", "sub_env"), True),
        )


class SandboxUser(BaseModel):
    sandbox_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_active = BooleanField(default=True, constraints=[SQL('DEFAULT True')])

    class Meta():
        evolve = True
        schema = "lemon"
        indexes = (
            (("sandbox_uuid",), False),
            (("user_uuid",), False),
            (("sandbox_uuid", "user_uuid"), True),
        )


class Localhistory(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    branch_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    timestamp = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])
    document_content = JsonField(null=True)

    class Meta:
        evolve = True
        schema = "lemon"
        version_control = True
        indexes = (
            (("branch_uuid", "user_uuid", "document_uuid", "timestamp", "app_uuid"), True),
        )


class LocalhistoryPatch(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    history_id = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    timestamp = IntegerField()
    patch = TextField(null=True)

    class Meta:
        evolve = True
        schema = "lemon"
        indexes = (
            (("app_uuid", "document_uuid", "history_id", "timestamp"), True),
        )


class Resource(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    resource_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    resource_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    resource_type = IntegerField(default=0, constraints=[SQL("DEFAULT 0")])  # 0 标签
    description = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    superior_resource = IntegerField(default=None, null=True, constraints=[SQL("DEFAULT NULL")])

    class Meta:
        evolve = True
        schema = "lemon"
        indexes = (
            (("resource_uuid", ), True),
            (("app_uuid",), False),
            (("document_uuid",), False),
        )


class Tag(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    module_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    document_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    resource_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    resource_id = IntegerField(default=None, null=True, constraints=[SQL("DEFAULT NULL")])
    tag_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tag_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    action = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    #  TODO  has_permission = action == all_action & action

    class Meta:
        evolve = True
        schema = "lemon"
        indexes = (
            (("resource_uuid", ), False),
            (("tag_uuid", ), True),
            (("tag_name", ), False),
            (("app_uuid",), False),
            (("document_uuid",), False),
        )


class IconFont(BaseModel):
    app_uuid = FixedCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    name = FixedCharField(max_length=64, default="",
                          constraints=[SQL('DEFAULT ""')])
    font_class = FixedCharField(max_length=64, default="",
                                constraints=[SQL('DEFAULT ""')])
    content = TextField(null=False, default="",
                        constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta:
        evolve = True
        schema = "lemon"
        indexes = (
            (("app_uuid", "name", ), True),
        )

class Packages(BaseModel):

    package_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    package_version = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    subpackage_info = JsonField(default=[], constraints=[SQL('DEFAULT ("[]")')], null=True)
    package_order_info = JsonField(default={}, constraints=[SQL('DEFAULT ("{}")')], null=True)
    timestamp = MySQLTimestampField(constraints=[SQL('DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta:
        evolve = True
        schema = "lemon"
        indexes = (
            (("package_name", ), False),
            (("package_version", ), False),
            (("package_version", "package_name",), True)
        )


_TRANSFER_TO_SYS_TABLE = [UserRole, ExportTemplate]
