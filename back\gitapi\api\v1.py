# -*- coding:utf-8 -*-

import asyncio
import hashlib
import traceback
import os
import pathlib
import time
import aiofiles
import ujson
from aiofiles import ospath as aospath
from aiofiles import os as aos
from collections import defaultdict

import aioredlock
from sanic import Blueprint
from sanic.response import json, HTTPResponse
from sanic.views import HTTPMethodView
from sanic_openapi import doc

from baseutils.log import app_log
from apps.base_utils import lemon_uuid, create_context_task
from apps.utils import LemonDictResponse as LDR
from apps.utils import process_args, set_current_workspace, get_current_workspace, CurrentWorkspace
from baseutils.const import Code, DOC, SyncTORepoType
from apps.entity import APP, APPBranch, Combine, APPUserBranch
from apps.engine import engine
from apps.ide_const import DocumentType
from apps.permission import Permission
from apps.exceptions import LemonGitAPIError

from gitapi.const import GitCode, GITLAB_USER_PROVIDER, APPUserBranchStatus, RepoType, PROJECT_STATUS
from gitapi.api.const import API
from gitapi.utils import (create_repo_connection, set_task_status, get_task_status,
    RepoConnection, create_user, AsyncGitDriller, Executor, CHANGE_TYPE, contains_chinese,
    get_repo_path, get_user_key_file, GitCommandError, sync_local_diff_to_db, sync_user_branch_to_origin_db)


API_NAME_VERSION = "_".join([API.NAME, API.V1])
url_prefix = "/" + "/".join([API.NAME, API.V1])
bp = Blueprint(API_NAME_VERSION, url_prefix=url_prefix)

COMMIT_TEMPLATE = """{commit_type}({scopes}):{subject}"""



async def handle_task_done(task, taskid=None):
    status = PROJECT_STATUS.INITIALIZE
    await set_task_status(taskid=taskid, status=status)
    try:
        result = await task
    except:
        app_log.error(traceback.format_exc())
        result = False
        status = PROJECT_STATUS.FAILED
    else:
        status = PROJECT_STATUS.DONE
    if isinstance(result, dict):
        extra_status = result
    elif isinstance(result, HTTPResponse):
        json_body = ujson.loads(result.body)
        data = json_body.get("data")
        extra_status = {"data": data}
        if json_body.get("code") != 200:
            status = PROJECT_STATUS.FAILED
            extra_status = json_body
    else:
        extra_status = {"data": result}
    await set_task_status(taskid=taskid, status=status, **extra_status)

class QueryTaskStatus(HTTPMethodView):
    class QueryTaskStatusObj:
        taskid = doc.String("taskid")
    @doc.consumes(QueryTaskStatusObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("查询task状态")
    async def post(self, request):
        with process_args():
            self.sid = request.json.get("sid", "")
            taskid = request.json.get("taskid")
            if not taskid:
                return json(LDR(Code.ARGS_ERROR))

        status = await get_task_status(taskid)
        if not status:
            return json(LDR(GitCode.INVALID_TASK_ID))
        status = ujson.loads(status)
        return json(LDR(data=status))

class CreateProject(HTTPMethodView):
    class CreateProjectObj:
        app_uuid = doc.String("app uuid")
    @doc.consumes(CreateProjectObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("创建项目或创建用户git空间")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            self.sid = request.json.get("sid", "")
            user_uuid = request.ctx.session.get("user_uuid")
            self.user_uuid = user_uuid
            user_name = request.ctx.session.get("user_name")
            full_name = request.ctx.session.get("full_name")
            branch_uuid = request.json.get("branch_uuid")
            self.branch_uuid = branch_uuid
            app_uuid = request.json.get("app_uuid")
            self.app_uuid = app_uuid
            repo_type = request.json.get("repo_type", 0)
            git_host = request.json.get("git_host", engine.config.GITLAB_HOST)

        if repo_type != RepoType.LEMON_GITLAB:
            return json(LDR(GitCode.GIT_REPO_NOT_SUPPORT))

        branch_name = engine.config.DEFAULT_BRANCH_NAME
        get_data = {
            APPBranch.app_uuid.name: app_uuid,
            APPBranch.branch_name.name: branch_name
        }
        if branch_uuid:
            get_data.update({APPBranch.branch_uuid.name: branch_uuid})
        app_branch = await engine.access.get_obj(APPBranch, **get_data)
        if app_branch and not branch_uuid:
            return json(LDR(GitCode.PROJECT_EXISTS))
        if app_branch and branch_uuid:
            get_data = {
                APPUserBranch.app_uuid.name: app_uuid,
                APPUserBranch.branch_uuid.name: branch_uuid,
                APPUserBranch.user_uuid.name: user_uuid
            }
            user_branch = await engine.access.get_obj(APPUserBranch, **get_data)
            if user_branch:
                return json(LDR(GitCode.PROJECT_EXISTS))

        resource_id = hashlib.md5(
            ("create_project_" + app_uuid).encode()).hexdigest()
        lock = await engine.editor_lock_manager.get_lock(resource_id)
        if lock:
            return json(LDR(GitCode.PROJECT_IS_CREATING))

        try:
            lock = await engine.editor_lock_manager.lock(
                resource_id, lock_timeout=10*60, lock_identifier=user_uuid)
        except aioredlock.LockError:
            return json(LDR(GitCode.PROJECT_LOCK_FAILED))
        
        taskid = create_context_task(task_type="git")
        asyncio.create_task(self.create_git(repo_type, git_host, user_name, full_name, branch_name, app_branch, lock, taskid))
        return json(LDR(data={"taskid": taskid}))


    async def create_git(self, repo_type, git_host, user_name, full_name, 
            branch_name, app_branch, lock, taskid=None):
        app_uuid = self.app_uuid
        branch_uuid = self.branch_uuid
        user_uuid = self.user_uuid
        app = await engine.access.get_app(**{"app_uuid": app_uuid})
        app_name = app.app_name
        if contains_chinese(app_name):
            app_name = app.app_uuid
        try:
            await set_task_status(status=PROJECT_STATUS.CREATE_USER)
            git_user = await create_user(self.sid, full_name, user_name, user_uuid, repo_type, git_host)
            user_conn = await create_repo_connection(host=git_host,
                user=user_uuid, token=git_user.get("git_token"), repo_type=repo_type)
            await set_task_status(status=PROJECT_STATUS.CREATE_PROJECT)
            if app_branch:
                project = await engine.gitlab.get_project(app.project_id)
                branch_uuid = app_branch.branch_uuid

                project_users = [git_user.get("user_instance")]
            else:
                project = await engine.gitlab.create_project(app_name, conn=user_conn.conn)
                branch_uuid = lemon_uuid()
                app_branch_data = {
                    APPBranch.app_uuid.name: app_uuid,
                    APPBranch.branch_uuid.name: branch_uuid,
                    APPBranch.branch_name.name: branch_name
                }
                await engine.access.create_obj(APPBranch, **app_branch_data)
                
                app_update_data = {
                    APP.project_id.name: project.id, 
                    APP.version_control.name: True,
                    APP.project_repo.name: project.http_url_to_repo }
                await engine.access.update_app(app_uuid, **app_update_data)

                # 首次开启git才会添加协作者的账号
                combine_list = await engine.access.list_combine_by_app_uuid(app_uuid)
                combine_users = set()
                user_uuid_key = Combine.user_uuid.name
                for combine_info in combine_list:
                    c_user_uuid = combine_info.get(user_uuid_key)
                    combine_users.add(c_user_uuid)
                if user_uuid in combine_users:
                    combine_users.remove(user_uuid)

                gitlab_users = await engine.gitlab.list_user()
                project_users = []
                for user in gitlab_users:
                    identities = user.identities
                    for _id in identities:
                        provider = _id.get("provider")
                        if provider == GITLAB_USER_PROVIDER:
                            extern_uid = _id.get("extern_uid")
                            if extern_uid in combine_users:
                                project_users.append(user)
            await set_task_status(status=PROJECT_STATUS.CREATE_PROJECT_MEMBERS)
            await engine.gitlab.create_project_members(project, project_users)

            data = {"branch_uuid": branch_uuid, "branch_name": branch_name}

            # 首次创建，需要同步文件
            await set_task_status(status=PROJECT_STATUS.UPDATE_DOC)
            await user_conn.sync_remote_files(
                app_uuid, app_name, branch_uuid, user_uuid, project_path=project.namespace["full_path"], init_repo=True, need_push=not app_branch)

            if lock:
                # 这里解锁，是因为，已经在数据库创建了项目数据
                # 如果有其他用户再请求创建，会直接报错
                await engine.editor_lock_manager.unlock(lock)
            await set_task_status(status=PROJECT_STATUS.DONE, **data)
            
        except Exception:
            app_log.error(traceback.format_exc())
            await set_task_status(status=PROJECT_STATUS.UNKNOWN)
            if lock:
                # 这里解锁，是因为，创建项目报错了
                await engine.editor_lock_manager.unlock(lock)
            await set_task_status(status=PROJECT_STATUS.FAILED, **LDR(GitCode.CREATE_PROJECT_FAILED))

class CheckoutBranch(HTTPMethodView):
    class CheckoutBranchObj:
        app_uuid = doc.String("应用 uuid")
        app_name = doc.String("应用名称")
        branch_uuid = doc.String("分支 uuid")
        branch_name = doc.String("分支名称")
        init_repo = doc.String("是否初始化仓库")
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("checkout分支到本地")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            gitlab_token = request.ctx.session.get("gitlab_token")
            app_uuid = request.json.get("app_uuid")
            app_name = request.json.get("app_name")
            branch_uuid = request.json.get("branch_uuid")
            branch_name = request.json.get("branch_name")
            init_repo = request.json.get("init_repo", False)
        app = await engine.access.get_app(**{"app_uuid": app_uuid})
        if not app:
            return json(LDR(GitCode.APP_NOT_EXISTS))
        if contains_chinese(app_name):
            app_name = app_uuid
        taskid = create_context_task(task_type="git")
        if not gitlab_token:
            access = await engine.access.get_user_git_access(user_uuid, repo_type=RepoType.LEMON_GITLAB)
            if access:
                gitlab_token = access.user_token
        asyncio.create_task(self.checkout_branch(app_uuid, branch_uuid, user_uuid, app_name, branch_name, app.project_id, gitlab_token, init_repo))
        return json(LDR(data={"taskid": taskid}))
    
    async def checkout_branch(self, app_uuid, branch_uuid, user_uuid, app_name, branch_name, project_id, gitlab_token, init_repo):
        await set_task_status(status=PROJECT_STATUS.FETCH_ORIGIN)
        user_conn = await create_repo_connection(
            user=user_uuid, token=gitlab_token)
        # 需要用root用户获取project，user_conn获取到的只有owner是自己的
        project = await engine.gitlab.get_project(project_id)
        await user_conn._update_app_user_branch_status(
            app_uuid, branch_uuid, user_uuid, status=APPUserBranchStatus.CREATE)
        await user_conn.sync_remote_files(
            app_uuid, app_name, branch_uuid, user_uuid, project_path=project.namespace["full_path"],
            branch_name=branch_name, init_repo=init_repo)
        data = {"branch_uuid": branch_uuid, "branch_name": branch_name}
        await set_task_status(status=PROJECT_STATUS.DONE, **data)


class SyncTORepo(HTTPMethodView):
    class SyncTORepoObj:
        sync_type = doc.String("操作类型")
        args = doc.String("操作对应的 位置参数")
        kwargs = doc.String("操作对应的 关键字参数")
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("同步操作到仓库")
    @Permission.transcation()
    async def post(self, request):
        with process_args():
            self.sid = request.json.get("sid", "")
            sync_type = request.json.get("sync_type")
            # task type 1: 不需要结果   0： 需要执行完并返回结果
            task_type = request.json.get("task_type")
            app_uuid = request.json.get("app_uuid", "")
            branch_uuid = request.json.get("branch_uuid", "")
            user_uuid = request.json.get("user_uuid", "")
            args = request.json.get("args", [])
            kwargs = request.json.get("kwargs", dict())
        set_current_workspace(user_uuid=user_uuid, sid=self.sid, branch_uuid=branch_uuid, app_uuid=app_uuid)
        self.repo_path = get_repo_path(app_uuid, branch_uuid, user_uuid)
        self.user_key_file = get_user_key_file(user_uuid)
        if self.repo_path and self.user_key_file:
            self.driller = AsyncGitDriller(self.repo_path, self.user_key_file)
        else:
            self.driller = None
        func_name = SyncTORepoType.SYNC_TO_ACTION.get(sync_type, "").lower()
        func = getattr(self, func_name, None)
        if callable(func) and asyncio.iscoroutinefunction(func):
            try:
                if task_type == 1:
                    taskid = create_context_task(task_type="git")
                    task = func(*args, **kwargs)
                    task = asyncio.ensure_future(task)
                    asyncio.create_task(handle_task_done(task, taskid=taskid))
                    return json(LDR(data={"taskid": taskid}))
                result = await func(*args, **kwargs)
            except Exception:
                app_log.error(traceback.format_exc())
                return json(LDR(GitCode.SYNC_TO_REPO_FAILED))
            else:
                if isinstance(result, HTTPResponse):
                    return result
                return json(LDR(data=result))
        else:
            return json(LDR(GitCode.SYNC_TYPE_NOT_FOUND))

    # commit and push / pull 需要强制先把数据库文档同步到本地
    # rename / create / move 等待daemon自动同步


    async def create_user(self, full_name, user_name, user_uuid, repo_type, git_host):
        return await create_user(self.sid, full_name, user_name, user_uuid, repo_type, git_host)

    async def fetch_origin(self, app_uuid, branch_uuid, user_uuid):
        model = APPUserBranch
        b_model = APPBranch
        fields = (
            model.branch_uuid,
            b_model.branch_name

        )
        query = model.select(*fields).join_from(model, b_model, on=(model.branch_uuid==b_model.branch_uuid)).where(
            model.app_uuid==app_uuid, model.user_uuid==user_uuid, model.branch_uuid==branch_uuid
        )
        
        branch = await engine.access.select_obj_by_query(model, query)
        if not branch:
            return json(LDR(GitCode.APP_BRANCH_NOT_FOUND))

        await self.driller.fetch()
        commit_status = await self.driller.branch_divergences(branch_name=branch["branch_name"])
        return {
            "commits_behind": commit_status[0],
            "commits_ahead": commit_status[1]
        }

    async def pull(self):
        last_commit = await self.driller.head_commit()
        try:
            await self.driller.pull()
        except GitCommandError as e:
            app_log.error(traceback.format_exc())
            if "Your local changes to the following files would be overwritten by merge" in e.stderr:
                return json(LDR(GitCode.PULL_ERROR_LOCAL_CHANGES_CONFLICT))
            if await self.driller.is_merging():
                current_commit = await self.driller.head_commit()
                # diff_index = last_commit.diff(current_commit)
                diff_index = self.driller.repo.index.diff("HEAD", R=True)
                if diff_index:
                    await sync_local_diff_to_db(self.driller, diff_index)
                return json(LDR(GitCode.CONFLICT_UNRESOLVED))  
            return json(LDR(GitCode.PULL_ERROR))
        else:
            # 本地文件变更同步到数据库
            current_commit = await self.driller.head_commit()
            diff_index = last_commit.diff(current_commit)
            if diff_index:
                await sync_local_diff_to_db(self.driller, diff_index)

            return True

    async def get_staged_changes(self, paths=None):
        return await self.driller.get_staged_changes(paths)

    async def get_conflicts(self):
        conflicts = []
        unmerged = await self.driller.get_unmerged()
        for file, value in unmerged.items():
            conflicts.append(file)
        return conflicts

    async def get_file_history(self, file_path, current_page=1, page_size=10, history_type=0, author=None, message=None):
        start = (current_page - 1)*page_size
        end = current_page*page_size
        history_info = []
        index = 0
        async for commit in self.driller.iter_file_commits(file_path, author=author, message=message):
            index += 1
            if index <= start:
                continue
            if index > end:
                break
            history_info.append({"hash": commit.hash, "author": commit.author.name, "timestamp": commit.committer_date.timestamp(), "msg": commit.msg, "type": 1})
        return history_info

    async def commit_modified(self, version, file_path, name_only=False):

        files = await self.driller.get_commit_modified(version, file_path)
        data = []
        if files:
            for file in files:
                change_type = file.change_type.name[0]
                d = { "path": file.new_path if change_type in [CHANGE_TYPE.DELETE] else file.old_path, "change_type": change_type }
                if not name_only:
                    d.update({"content": file.content,
                        "content_before": file.content_before})
                data.append(d)
        return data

    async def checkout(self, version, file_path, document_uuid=None):
        await self.driller.checkout(version, file_path)
        # update doc
        async with aiofiles.open(os.path.join(self.driller.git_path, file_path)) as f:
            document_content = await f.read()      
        if document_content:
            document_content = ujson.loads(document_content)
        await engine.access.update_document_content_by_document_uuid(document_uuid, **{"document_content": document_content})
        return True

    async def sync_doc(self, document_uuid):
        document_info = await engine.access.get_document_detail_by_document_uuid(document_uuid=document_uuid)
        if not document_info:
            return
        file_path = document_info["full_path"]
        if document_info["document_type"] == DocumentType.DIR:
            # todo: 
            pass
        else:
            file_content = document_info["document_content"]
            await self.driller.save_file(file_path, file_content)
        await self.driller.run_in_executor(self.driller.git.add, file_path)
        # 解决完所有冲突后自动commit，并尝试同步
        if (await self.driller.is_merging()) == 2:
            await self.driller.commit(no_edit=True)
            await self.sync_origin()
        return True

    async def sync_module(self, module_name):
        current_workspace = get_current_workspace()
        app_uuid = current_workspace.app_uuid
        
        module = await engine.access.get_module_by_app_uuid_module_name(app_uuid, module_name)
        module_path = os.path.join(self.repo_path, module.module_name)
        if not await aospath.exists(module_path):
            await aos.makedirs(module_path)
        await RepoConnection._sync_files(app_uuid, current_workspace.branch_uuid, current_workspace.user_uuid,
                    module.module_uuid, module_path, sync_local_only=True, init_repo=True)
        await self.driller.run_in_executor(self.driller.git.add, module_path)

    async def rename(self, name, new_name):
        await self.driller.rename(name, new_name)
        return True

    async def restore(self, name):
        await self.driller.restore(name, "--staged")
        await self.driller.restore(name)
        return True

    async def delete(self, file):
        await self.driller.delete(file)
        return True

    async def sync_app(self, app_uuid):
        if await self.driller.is_merging():
            return json(LDR(GitCode.CONFLICT_UNRESOLVED))
        current_workspace = get_current_workspace()
        module_list = await engine.access.list_module_by_app_uuid(app_uuid)
        await asyncio.gather(*([self.sync_module(module.get("module_name")) for module in module_list]
                 + [RepoConnection.sync_app_info_doc(app_uuid, current_workspace.branch_uuid, current_workspace.user_uuid, self.repo_path, sync_local_only=True)]))
        await self.driller.run_in_executor(self.driller.git.add, all=True)

    async def commit(self, **commit_info):
        if (await self.driller.is_merging()) == 1:
            return json(LDR(GitCode.CONFLICT_UNRESOLVED))  
        if not await self.driller.get_staged_changes():
            return json(LDR(GitCode.NO_FILES_TO_COMMIT))
        commit_type = commit_info.get("commit_type")
        scopes = commit_info.get("scopes", [])
        subject = commit_info.get("subject")
        body = commit_info.get("body")
        scopes = ",".join(scopes)
        commit_msg = COMMIT_TEMPLATE.format(commit_type=commit_type, scopes=scopes, subject=subject)
        if body:
            commit_msg += "\n" + body + '\n'
        await self.driller.commit(commit_msg)
        return True

    async def push(self):
        # 用户分支数据库docs同步到分支docs
        await sync_user_branch_to_origin_db()
        await self.driller.push()
        return True

    async def commit_and_sync(self, **commit_info):
        res = await self.commit(**commit_info)
        if res != True:
            return res
        # async with self.driller.atomic():
        return await self.sync_origin()

    async def sync_origin(self):
        res = await self.pull()
        if res != True:
            return res
        res = await self.push()
        return res
    
    async def get_unmerged_detail(self, file_path):
        unmerged = await self.driller.get_unmerged()
        blobs = unmerged.get(file_path, [])
        out_data = []

bp.add_route(CreateProject.as_view(), "/create_project.json")
bp.add_route(CheckoutBranch.as_view(), "/checkout_branch.json")
bp.add_route(SyncTORepo.as_view(), "/sync_to_repo.json")
bp.add_route(QueryTaskStatus.as_view(), "/query_task.json")