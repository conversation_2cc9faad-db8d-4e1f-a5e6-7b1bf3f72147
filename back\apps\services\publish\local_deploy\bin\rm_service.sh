#!/bin/bash
DIR="$( cd "$( dirname "$0"  )" && pwd  )"
cd $DIR
source ./config.sh
SERVICE_DIR=""
if [[ -d /usr/lib/systemd/system ]]  
then
    SERVICE_DIR=/etc/systemd/system
elif [[ -d /etc/systemd/system ]]  
then
    SERVICE_DIR=/usr/lib/systemd/system
fi

if [ $SERVICE_DIR ] 
then 
    cd $SERVICE_DIR
    systemctl disable ${app_env}.service
    rm -rf ${app_env}.service
else
    echo '您的系统暂不支持自动设置开机自启'
fi

