import os
import types

import peewee
from peewee import (SQL, BooleanField, Char<PERSON><PERSON>, FixedCharField, IntegerField)
from playhouse.hybrid import hybrid_property

from baseutils.utils import LEMON_FIELD_DEFINE
from apps.base_utils import check_lemon_uuid
from baseutils.const import SystemNamespace, gen_column_name, SystemField
from apps import COMPANY_KEY, Runtime
from apps.base_entity import (
    UUID, BaseTenantMember, Department, TenantDepartment, TenantDepartmentMember, TenantUser,
    UserRole, Resource as BaseResource, Tag, PermissionConfig,
    Navigation as BaseNavigation, NavigationItem as BaseNavigationItem,
    get_runtime_app_revision, get_runtime_app_uuid
)
from apps.base_entity import (UserModel, User as PlatformUser, 
    make_table_name as make_design_table_name, ModelBasic, Func, ModelCloudFunction)
from apps.base_utils import SYSTEM_MODEL_DICT, MODEL_COPY_DICT
from apps.entity import (
    _TRANSFER_TO_SYS_TABLE, Document, DocumentContent, Event, Func as BaseFunc, Module,
    Navigation, NavigationItem, Page, Print, LabelPrint, TemplateTable, Workflow, user_db_proxy,
    UserRole as BaseUserRole, ExportTemplate as BaseExportTemplate,
    ModelBasic as BaseModelBasic, ModelField, RelationshipBasic, Resource
)
from apps.peewee_ext import (
    JsonField, LemonBooleanField, LemonCharField, LemonTextField, LemonDateTimeField, LemonIntegerField, LemonSystemForeignKeyField,
    LemonAutoField, LemonJsonField)
from apps.runtime_entity import AppTenant, BaseModel, RoleMember as BaseRoleMember
from apps.ucenter_entity import DepartmentMember
from apps.utils import make_runtime_table_name


def make_table_name(model_class):
    company = os.environ.get(COMPANY_KEY) or "lemon"
    app_uuid = get_runtime_app_uuid()
    version = get_runtime_app_revision()
    model_name = model_class.__name__
    if hasattr(model_class._meta, "ignore_tenant"):
        meta_table_name = getattr(model_class._meta, "meta_table_name", None)
        meta_table_name = meta_table_name.lower() if meta_table_name else model_class._meta.table_name
        if meta_table_name in [t._meta.table_name for t in SystemTableHandler._runtime_model]:
            return make_runtime_table_name(app_uuid, None, meta_table_name)
        else:
            return make_runtime_table_name(app_uuid, str(version), meta_table_name)
    return "_".join([app_uuid[:16], str(version), company, model_name.lower()])


def __modify_design_model_attr(design=True, ucenter=True):
    design_model_list = [Event, Page, Print, LabelPrint, BaseFunc, Workflow, Document, DocumentContent, Navigation,
                         NavigationItem, TemplateTable, Module, UserRole, BaseExportTemplate, Resource,
                         BaseModelBasic, ModelField, RelationshipBasic]
    if os.environ.get("APP_UUID"):
        system_tables = sys_table_handler.make_sys_from_user_model()
        for table_name, table in system_tables.items():
            table._meta.set_table_name(make_table_name(table))
            table._meta.schema = os.environ.get('LEMON_MIDDLE_USER')
            table._meta.set_database(user_db_proxy)
            table._meta.sys_table = True
    if design:
        for model in design_model_list:
            model._meta.schema = os.environ.get('LEMON_MIDDLE_USER')
            model._meta.set_table_name(make_table_name(model))
            model._meta.set_database(user_db_proxy)
            model._meta.sys_table = True
    else:
        for model in design_model_list:
            model._meta.schema = os.environ.get(COMPANY_KEY) or "lemon"
    if ucenter:
        PlatformUser._meta.set_database(user_db_proxy)
        PlatformUser._meta.sys_table = True
        AppTenant._meta.set_database(user_db_proxy)
        AppTenant._meta.sys_table = True
        Department._meta.set_database(user_db_proxy)
        Department._meta.sys_table = True
        DepartmentMember._meta.set_database(user_db_proxy)
        DepartmentMember._meta.sys_table = True
        # PlatformUser._meta.set_table_name("platform_user")


class TenantMember(BaseTenantMember):
    """
    没有实际意义，为了方便其他地方导入类
    """
    pass


class FieldAutoIncrement(BaseModel):

    field_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    date_str = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    serial_prefix = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    value = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])

    class Meta():
        sys_table = True
        evolve = True
        indexes = (
            (("field_uuid", "tenant_uuid", "app_uuid", "date_str", "serial_prefix"), True),
        )


class FormTmp(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    form_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    _creator = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    path = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    update_time = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    pk = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])

    class Meta():
        sys_table = True
        evolve = True
        indexes = (
            (("form_uuid", "path"), True),
        )


class WorkflowInstance(UserModel):

    # tenant_uuid = LemonCharField(
    #     max_length=32, default="", constraints=[SQL('DEFAULT ""')],
    #     column_name=UUID.workflow_instance.tenant_uuid.value,
    #     aka="tenant_uuid")
    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'app_uuid'")],
        column_name=UUID.workflow_instance.app_uuid.value,
        aka="app_uuid")
    wf_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'wf_uuid'")],
        column_name=UUID.workflow_instance.wf_uuid.value,
        aka="wf_uuid")  # 工作流UUID
    wf_name = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'wf_name'")],
        verbose_name="名称", column_name=UUID.workflow_instance.wf_name.value,
        aka="wf_name")  # 工作流名称
    type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'type'")], verbose_name="类型",
        column_name=UUID.workflow_instance.type.value, aka="type")
    wf_version_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'wf_version_uuid'")],
        column_name=UUID.workflow_instance.wf_version_uuid.value,
        aka="wf_version_uuid")  # 工作流版本UUID
    wf_instance_uuid = LemonCharField(
        max_length=32, unique=True, verbose_name="工作流标识符",
        constraints=[SQL("comment 'wf_instance_uuid'")],
        column_name=UUID.workflow_instance.wf_instance_uuid.value,
        aka="wf_instance_uuid")  # 工作流实例UUID
    wf_version_no = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'wf_version_no'")],
        verbose_name="版本",
        column_name=UUID.workflow_instance.wf_version_no.value,
        aka="wf_version_no")  # 工作流版本号
    wf_dict = LemonJsonField(
        null=True, column_name=UUID.workflow_instance.wf_dict.value,
        constraints=[SQL("comment 'wf_dict'")],
        aka="wf_dict")  # 工作流版本定义
    outer_wf_instance_uuid = LemonCharField(
        max_length=32, null=True,
        column_name=UUID.workflow_instance.outer_wf_instance_uuid.value,
        constraints=[SQL("comment 'outer_wf_instance_uuid'")],
        aka="outer_wf_instance_uuid")  # 外层工作流实例UUID, 仅分支工作流中此处有值
    outer_wf_instance_id = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL'), SQL("comment 'outer_wf_instance_id'")],
        column_name=UUID.workflow_instance.outer_wf_instance_id.value,
        aka="outer_wf_instance_id")  # 外层工作流实例ID, 仅分支工作流中此处有值
    form_pk = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'form_pk'")],
        column_name=UUID.workflow_instance.form_pk.value,
        aka="form_pk")  # 对应表单的pk
    model_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'model_uuid'")],
        column_name=UUID.workflow_instance.model_uuid.value,
        aka="model_uuid")  # 对应表单的数据模型uuid
    form_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'form_uuid'")],
        column_name=UUID.workflow_instance.form_uuid.value,
        aka="form_uuid")  # 对应表单uuid
    creator = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'creator'")],
        column_name=UUID.workflow_instance.creator.value,
        aka="creator")  # 工作流实例发起人
    creator_id = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL'), SQL("comment 'creator_id'")],
        column_name=UUID.workflow_instance.creator_id.value,
        aka="creator_id")  # 工作流实例发起人ID
    node_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'node_uuid'")],
        column_name=UUID.workflow_instance.node_uuid.value,
        aka="node_uuid")  # 当前节点UUID
    node_name = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'node_name'")],
        column_name=UUID.workflow_instance.node_name.value,
        aka="node_name")  # 当前节点名称
    node_instance_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'node_instance_uuid'")],
        column_name=UUID.workflow_instance.node_instance_uuid.value,
        aka="node_instance_uuid")  # 当前节点实例UUID
    create_time = LemonDateTimeField(
        null=True, verbose_name="发起时间",
        constraints=[SQL("comment 'create_time'")],
        column_name=UUID.workflow_instance.create_time.value, aka="create_time")  # 工作流实例创建时间
    end_time = LemonDateTimeField(
        null=True, verbose_name="结束时间",
        constraints=[SQL("comment 'end_time'")],
        column_name=UUID.workflow_instance.end_time.value, aka="end_time")  # 工作流实例结束时间
    status = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'status'")], verbose_name="运行状态",
        column_name=UUID.workflow_instance.status.value, aka="status")  # 工作流实例运行状态 （运行中/已完成/已终止/异常/已暂停）
    message = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'message'")],
        column_name=UUID.workflow_instance.message.value, aka="message")  # 提示消息
    mainflow = LemonSystemForeignKeyField(
        'self', backref='子流程', constraints=[SQL("comment '上级主流程'")], null=True, on_delete="CASCADE",
        verbose_name="上级主流程", column_name=UUID.workflow_instance.mainflow.value)  # 自关联外键
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_delete'")],
        column_name=UUID.workflow_instance.is_delete.value, aka="is_delete")

    class Meta():
        aka = UUID.workflow_instance.meta_table_name.value
        model_name = UUID.workflow_instance.display_name.value
        is_sys_table = True
        table_name = UUID.workflow_instance.table_name.value
        meta_table_name = UUID.workflow_instance.meta_table_name.value
        # schema = "lemon_tenant_center"
        module_name = "系统模块"
        evolve = True
        indexes = (
            (("tenant_uuid", "app_uuid", "wf_uuid"), False),
            (("wf_instance_uuid", ), True),
            (("form_uuid", "form_pk", ), False)
        )

    @hybrid_property
    def 名称(self):
        return self.wf_name

    @hybrid_property
    def 工作流标识符(self):
        return self.wf_instance_uuid

    @hybrid_property
    def 版本(self):
        return self.wf_version_no

    @hybrid_property
    def 发起时间(self):
        return self.create_time

    @hybrid_property
    def 结束时间(self):
        return self.end_time

    @hybrid_property
    def 运行状态(self):
        return self.status

    @hybrid_property
    def 类型(self):
        return self.type

    @hybrid_property
    def 上级主流程(self):
        return self.mainflow


class NodeInstance(UserModel):

    # tenant_uuid = LemonCharField(
    #     max_length=32, default="", constraints=[SQL('DEFAULT ""')],
    #     column_name=UUID.node_instance.tenant_uuid.value,
    #     aka="tenant_uuid")
    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'app_uuid'")],
        column_name=UUID.node_instance.app_uuid.value,
        aka="app_uuid")
    wf_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'wf_uuid'")],
        column_name=UUID.node_instance.wf_uuid.value,
        aka="wf_uuid")  # 工作流UUID
    wf_version_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'wf_version_uuid'")],
        column_name=UUID.node_instance.wf_version_uuid.value,
        aka="wf_version_uuid")  # 工作流版本UUID
    wf_instance_uuid = LemonCharField(
        max_length=32,
        constraints=[SQL("comment 'wf_instance_uuid'")],
        column_name=UUID.node_instance.wf_instance_uuid.value,
        aka="wf_instance_uuid")  # 工作流实例UUID
    wf_instance_id = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL'), SQL("comment 'wf_instance_id'")],
        column_name=UUID.node_instance.wf_instance_id.value, aka="wf_instance_id")  # 工作流实例ID
    node_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'node_uuid'")],
        column_name=UUID.node_instance.node_uuid.value,
        aka="node_uuid")  # 工作流节点UUID
    node_name = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'node_name'")],
        verbose_name="名称", column_name=UUID.node_instance.node_name.value,
        aka="node_name")  # 工作流节点名称
    node_type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'node_type'")], verbose_name="类型",
        column_name=UUID.node_instance.node_type.value, aka="node_type"
    )  # 工作流节点类型
    node_instance_uuid = LemonCharField(
        max_length=32, unique=True, verbose_name="节点标识符",
        constraints=[SQL("comment 'node_instance_uuid'")],
        column_name=UUID.node_instance.node_instance_uuid.value,
        aka="node_instance_uuid")  # 工作流节点实例UUID
    handlers = LemonJsonField(
        null=True, column_name=UUID.node_instance.handlers.value,
        constraints=[SQL("comment 'handlers'")],
        aka="handlers")  # 节点处理人列表 （可能是多个，包含工位、 终端、 用户等）
    outer_wf_instance_uuid = LemonCharField(
        max_length=32, null=True,
        constraints=[SQL("comment 'outer_wf_instance_uuid'")],
        column_name=UUID.node_instance.outer_wf_instance_uuid.value,
        aka="outer_wf_instance_uuid")  # 外层工作流实例UUID, 仅分支工作流中此处有值
    outer_node_instance_uuid = LemonCharField(
        max_length=32, null=True,
        constraints=[SQL("comment 'outer_node_instance_uuid'")],
        column_name=UUID.node_instance.outer_node_instance_uuid.value,
        aka="outer_node_instance_uuid")  # 外层node实例UUID, 仅分支工作流中此处有值
    finished_branch = LemonJsonField(
        null=True, default=list(),
        constraints=[SQL("comment 'finished_branch'")],
        column_name=UUID.node_instance.finished_branch.value,
        aka="finished_branch")  # 已完成的分支
    pnode_instance = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'pnode_instance'")],
        column_name=UUID.node_instance.pnode_instance.value, null=True,
        aka="pnode_instance")  # 工作流父节点实例UUID
    status = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'status'")], verbose_name="节点状态",
        column_name=UUID.node_instance.status.value, aka="status")  # 节点状态（运行中/已完成/已终止/异常/已暂停）
    message = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'message'")],
        column_name=UUID.node_instance.message.value, aka="message")  # 提示消息
    create_time = LemonDateTimeField(
        null=True, verbose_name="进入节点时间",
        constraints=[SQL("comment 'create_time'")],
        column_name=UUID.node_instance.create_time.value, aka="create_time")  # 节点创建时间
    handle_time = LemonDateTimeField(
        null=True, verbose_name="处理完成时间",
        constraints=[SQL("comment 'handle_time'")],
        column_name=UUID.node_instance.handle_time.value, aka="handle_time")  # 节点处理完成时间
    actions = LemonJsonField(
        null=True,
        constraints=[SQL("comment 'actions'")],
        column_name=UUID.node_instance.actions.value, aka="actions")  # 节点处理人所做的处理动作
    action_result = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'action_result'")],
        column_name=UUID.node_instance.action_result.value,
        aka="action_result")  # 处理结果（动作UUID）
    return_from = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'return_from'")],
        column_name=UUID.node_instance.return_from.value, aka="return_from")  # 回退来源node_instance_uuid
    return_back = BooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'return_back'")],
        column_name=UUID.node_instance.return_back.value,
        aka="return_back")  # 同意后返回到回退来源
    is_start = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_start'")], verbose_name="是否实际发起节点",
        column_name=UUID.node_instance.is_start.value, aka="is_start")  # 是否为发起任务
    auto_processed = BooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'auto_processed'")],
        column_name=UUID.node_instance.auto_processed.value,
        aka="auto_processed")  # 是否为自动审批过的
    subflow_instance_uuid = LemonCharField(
        max_length=32, null=True, constraints=[SQL("comment 'subflow_instance_uuid'")],
        column_name=UUID.node_instance.subflow_instance_uuid.value,
        aka="subflow_instance_uuid")  # 子流程wf_instance_uuid
    is_delete = BooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_delete'")],
        column_name=UUID.node_instance.is_delete.value, aka="is_delete")

    class Meta():
        aka = UUID.node_instance.meta_table_name.value
        model_name = UUID.node_instance.display_name.value
        is_sys_table = True
        table_name = UUID.node_instance.table_name.value
        meta_table_name = UUID.node_instance.meta_table_name.value
        # schema = "lemon_tenant_center"
        module_name = "系统模块"
        evolve = True
        indexes = (
            # (("wf_instance_uuid", ), False),
            (("node_instance_uuid", ), True),
        )

    @hybrid_property
    def 名称(self):
        return self.node_name

    @hybrid_property
    def 类型(self):
        return self.node_type

    @hybrid_property
    def 节点标识符(self):
        return self.node_instance_uuid

    @hybrid_property
    def 节点状态(self):
        return self.status

    @hybrid_property
    def 进入节点时间(self):
        return self.create_time

    @hybrid_property
    def 处理完成时间(self):
        return self.handle_time


class NodeTask(UserModel):

    wf_instance_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'wf_instance_uuid'")],
        column_name=UUID.node_task.wf_instance_uuid.value, aka="wf_instance_uuid")  # 工作流实例UUID
    node_instance_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'node_instance_uuid'")],
        column_name=UUID.node_task.node_instance_uuid.value, aka="node_instance_uuid")  # 节点实例UUID
    node_instance_id = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL'), SQL("comment 'node_instance_id'")],
        column_name=UUID.node_task.node_instance_id.value, aka="node_instance_id")  # 工作流实例ID
    task_uuid = LemonCharField(
        max_length=32, unique=True, verbose_name="节点任务标识符",
        constraints=[SQL("comment 'task_uuid'")],
        column_name=UUID.node_task.task_uuid.value, aka="task_uuid")  # 节点任务UUID
    node_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'node_uuid'")],
        column_name=UUID.node_task.node_uuid.value, aka="node_uuid")  # 工作流节点UUID
    node_name = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'node_name'")],
        column_name=UUID.node_task.node_name.value, aka="node_name")  # 工作流节点名称
    node_type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'node_type'")],
        column_name=UUID.node_task.node_type.value, aka="node_type")  # 工作流节点类型
    handler = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'handler'")],
        column_name=UUID.node_task.handler.value, aka="handler")  # 任务处理人
    handler_id = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL'), SQL("comment 'handler_id'")],
        column_name=UUID.node_task.handler_id.value, aka="handler_id")  # 任务处理人ID
    outer_wf_instance_uuid = LemonCharField(
        max_length=32, null=True,
        constraints=[SQL("comment 'outer_wf_instance_uuid'")],
        column_name=UUID.node_task.outer_wf_instance_uuid.value,
        aka="outer_wf_instance_uuid")  # 外层工作流实例UUID, 仅分支工作流中此处有值
    outmost_wf_instance_uuid = LemonCharField(
        max_length=32, null=True,
        constraints=[SQL("comment 'outmost_wf_instance_uuid'")],
        column_name=UUID.node_task.outmost_wf_instance_uuid.value,
        aka="outmost_wf_instance_uuid")  # 最外层工作流实例UUID,  仅分支工作流中此处有值
    outer_node_instance_uuid = LemonCharField(
        max_length=32, null=True,
        constraints=[SQL("comment 'outer_node_instance_uuid'")],
        column_name=UUID.node_task.outer_node_instance_uuid.value,
        aka="outer_node_instance_uuid")  # 最外层工作流实例UUID, 仅分支工作流中此处有值
    create_time = LemonDateTimeField(
        null=True, verbose_name="创建任务时间",
        constraints=[SQL("comment 'create_time'")],
        column_name=UUID.node_task.create_time.value, aka="create_time")  # 节点任务创建时间
    handle_time = LemonDateTimeField(
        null=True, verbose_name="任务处理时间",
        constraints=[SQL("comment 'handle_time'")],
        column_name=UUID.node_task.handle_time.value, aka="handle_time")  # 节点任务处理时间
    action_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'action_uuid'")],
        column_name=UUID.node_task.action_uuid.value, aka="action_uuid")  # 处理动作UUID
    action_name = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'action_name'")],
        verbose_name="处理动作名称",
        column_name=UUID.node_task.action_name.value, aka="action_name")  # 处理动作名称
    status = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'status'")], verbose_name="处理状态",
        column_name=UUID.node_task.status.value, aka="status")  # 节点任务状态（未处理/已处理）
    result = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'result'")],
        column_name=UUID.node_task.result.value, aka="result")  # 节点执行动作的结果（0 未处理/1 成功/2 失败）
    message = LemonCharField(
        max_length=64, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'message'")],
        column_name=UUID.node_task.message.value, aka="message")  # 提示消息
    is_read = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_read'")],
        verbose_name="是否已读",
        column_name=UUID.node_task.is_read.value, aka="is_read")  # 是否已读
    is_assign = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_assign'")],
        column_name=UUID.node_task.is_assign.value, aka="is_assign")  # 是否为转交（指派）任务
    assigner = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'assigner'")],
        column_name=UUID.node_task.assigner.value, aka="assigner")  # 转交（指派）人
    assigner_id = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL'), SQL("comment 'assigner_id'")],
        column_name=UUID.node_task.assigner_id.value, aka="assigner_id")  # 转交（指派）人ID
    withdrawer = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'withdrawer'")],
        column_name=UUID.node_task.withdrawer.value, aka="withdrawer")  # (撤回/撤审)人
    withdrawer_id = LemonIntegerField(
        default=None, null=True, constraints=[SQL('DEFAULT NULL'), SQL("comment 'withdrawer_id'")],
        column_name=UUID.node_task.withdrawer_id.value, aka="withdrawer_id")  # (撤回/撤审)人ID
    assign_type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'assign_type'")],
        column_name=UUID.node_task.assign_type.value,
        aka="assign_type")  # 转交类型 NodeAssignType （0 正常转交/1 停用转交/2 删除转交/3 离职转交）
    auto_processed = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'auto_processed'")],
        column_name=UUID.node_task.auto_processed.value, aka="auto_processed")  # 是否为自动审批过的
    timeout_schedule = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'timeout_schedule'")],
        column_name=UUID.node_task.timeout_schedule.value, aka="timeout_schedule")  # 是否开启超时提醒
    interval = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL("comment 'interval'")],
        column_name=UUID.node_task.interval.value, aka="interval")  # 超时提醒间隔(秒)
    comment = LemonCharField(
        max_length=500, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'comment'")],
        verbose_name="审批意见",
        column_name=UUID.node_task.comment.value, aka="comment")  # 审批意见
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL("comment 'is_delete'")],
        column_name=UUID.node_task.is_delete.value, aka="is_delete")  # 是否删除
    countersign_node_instance_uuid = LemonCharField(
        max_length=32, null=True,
        constraints=[SQL("comment 'countersign_node_instance_uuid'")],
        column_name=UUID.node_task.countersign_node_instance_uuid.value,
        aka="countersign_node_instance_uuid")  # 当前任务加签操作对应的加签节点实例
    countersign_reason = LemonCharField(
        max_length=500, null=True, constraints=[SQL("comment 'countersign_reason'")],
        verbose_name="加签原因",
        column_name=UUID.node_task.countersign_reason.value, aka="countersign_reason")  # 加签原因

    class Meta():
        aka = UUID.node_task.meta_table_name.value
        model_name = UUID.node_task.display_name.value
        is_sys_table = True
        table_name = UUID.node_task.table_name.value
        meta_table_name = UUID.node_task.meta_table_name.value
        # schema = "lemon_tenant_center"
        module_name = "系统模块"
        evolve = True
        indexes = (
            (("wf_instance_uuid", ), False),
            (("node_instance_uuid", ), False),
            (("handler", "status"), False),
            (("task_uuid", ), True)
        )

    @hybrid_property
    def 节点任务标识符(self):
        return self.task_uuid

    @hybrid_property
    def 创建任务时间(self):
        return self.create_time

    @hybrid_property
    def 任务处理时间(self):
        return self.handle_time

    @hybrid_property
    def 处理动作名称(self):
        return self.action_name

    @hybrid_property
    def 是否已读(self):
        return self.is_read

    @hybrid_property
    def 审批意见(self):
        return self.comment

    @hybrid_property
    def 处理状态(self):
        return self.status

    @hybrid_property
    def 加签原因(self):
        return self.countersign_reason


class WFStore(UserModel):

    wf_instance_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL("comment 'wf_instance_uuid'")],
        column_name=UUID.wf_store.wf_instance_uuid.value, aka="wf_instance_uuid")
    variable_list = JsonField(
        null=False, default=[], column_name=UUID.wf_store.variable_list.value,
        constraints=[SQL("comment 'variable_list'")],
        aka="variable_list")  # 工作流变量
    runtime_handlers = JsonField(
        null=False, default={}, column_name=UUID.wf_store.runtime_handlers.value,
        constraints=[SQL("comment 'runtime_handlers'")],
        aka="runtime_handlers")  # 记录节点处理人信息

    class Meta():
        aka = UUID.wf_store.meta_table_name.value
        model_name = UUID.wf_store.display_name.value
        is_sys_table = True
        table_name = UUID.wf_store.table_name.value
        meta_table_name = UUID.wf_store.meta_table_name.value
        # schema = "lemon_tenant_center"
        module_name = "系统模块"
        evolve = True
        indexes = (
            (("wf_instance_uuid", ), True),
        )


SYSTEM_MODEL_DICT.update({
    TenantUser._meta.meta_table_name: TenantUser,
    TenantDepartment._meta.meta_table_name: TenantDepartment,
    TenantDepartmentMember._meta.meta_table_name: TenantDepartmentMember,
    WorkflowInstance._meta.model_name: WorkflowInstance,  # display_name
    NodeInstance._meta.model_name: NodeInstance,
    NodeTask._meta.model_name: NodeTask,
    WFStore._meta.model_name: WFStore
})


class SystemConfig(UserModel):
    number = LemonCharField(
        max_length=32, constraints=[SQL("comment 'number'")],
        verbose_name="编号", column_name=UUID.system_config.number.value)
    key = LemonCharField(
        max_length=32, is_unique=True, constraints=[SQL("comment 'key'")], is_required=True,
        verbose_name="键", column_name=UUID.system_config.key.value)
    config_value = LemonCharField(
        max_length=300, constraints=[SQL("comment 'value'")], is_required=True,
        verbose_name="值", column_name=UUID.system_config.config_value.value)
    json_value = LemonJsonField(
        null=True,
        column_name=UUID.system_config.json_value.value)
    description = LemonCharField(
        max_length=200, constraints=[SQL("comment 'description'")],
        verbose_name="描述", null=True, column_name=UUID.system_config.description.value)

    class Meta():
        display_name = UUID.system_config.display_name.value
        model_name = display_name
        sys_table = True
        evolve = True
        module_name = "系统模块"
        indexes = (
            (("key", ), False),
        )

    @hybrid_property
    def 编号(self):
        return self.number

    @编号.setter
    def 编号(self, value):
        self.number = value

    @hybrid_property
    def 键(self):
        return self.key

    @键.setter
    def 键(self, value):
        self.key = value

    @hybrid_property
    def 值(self):
        return self.config_value

    @值.setter
    def 值(self, value):
        self.config_value = value

    @hybrid_property
    def 描述(self):
        return self.description

    @描述.setter
    def 描述(self, value):
        self.description = value


class RoleMember(UserModel):
    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL('comment "app_uuid"')],
        column_name=UUID.role_member.app_uuid.value)   # 不同app会共用一个租户的组织架构，但权限不共用
    # tenant_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    role_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""'), SQL('comment "role_uuid"')],
        column_name=UUID.role_member.role_uuid.value)
    member_uuid = LemonCharField(
        max_length=33, default="", constraints=[SQL('DEFAULT ""'), SQL('comment "member_uuid"')],
        verbose_name="成员标识符",
        column_name=UUID.role_member.member_uuid.value)
    member_type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL('comment "member_type"')], verbose_name="成员类型",
        column_name=UUID.role_member.member_type.value)   # 0 user 1 department
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False'), SQL('comment "is_delete"')],
        column_name=UUID.role_member.is_delete.value)
    app_env = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0'), SQL('comment "app_env"')],
        column_name=UUID.role_member.app_env.value)  # 0生产环境  1测试环境

    to_user = LemonSystemForeignKeyField(
        TenantUser, backref="用户的角色", verbose_name="用户", null=True,
        column_name=UUID.role_member.to_user.value, on_delete="CASCADE")

    to_department = LemonSystemForeignKeyField(
        TenantDepartment, backref="部门的角色", verbose_name="部门", null=True,
        column_name=UUID.role_member.to_department.value, on_delete="CASCADE")

    role = LemonSystemForeignKeyField(
        UserRole, backref="角色成员", verbose_name="角色", null=True,
        column_name=UUID.role_member.to_role.value, on_delete="CASCADE")

    class Meta():
        display_name = UUID.role_member.display_name.value
        model_name = display_name
        sys_table = True
        is_sys_table = True
        module_name = "系统模块"
        evolve = True
        ignore_tenant = False
        indexes = (
            (("tenant_uuid", "role_uuid", "member_uuid", "app_env"), False),
        )

    @hybrid_property
    def 成员标识符(self):
        return self.member_uuid

    @hybrid_property
    def 成员类型(self):
        return self.member_type

    @hybrid_property
    def 用户(self):
        return self.to_user

    @用户.setter
    def 用户(self, value):
        self.to_user = value

    @hybrid_property
    def 部门(self):
        return self.to_department

    @部门.setter
    def 部门(self, value):
        self.to_department = value

    @hybrid_property
    def 角色(self):
        return self.role

    @角色.setter
    def 角色(self, value):
        self.role = value


class ExportTemplate(UserModel):

    app_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_exporttemplate.app_uuid.value)
    module_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_exporttemplate.module_uuid.value)
    document_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_exporttemplate.document_uuid.value)
    template_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="模板标识符",
        column_name=UUID.lemon_exporttemplate.template_uuid.value)
    template_name = LemonCharField(
        max_length=30, default="", constraints=[SQL('DEFAULT ""')], verbose_name="模板名",
        column_name=UUID.lemon_exporttemplate.template_name.value)
    template = LemonJsonField(
        null=True,
        column_name=UUID.lemon_exporttemplate.template.value)
    template_type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0')], verbose_name="模板类型",
        column_name=UUID.lemon_exporttemplate.template_type.value)
    operation_type = LemonIntegerField(
        default=0, constraints=[SQL('DEFAULT 0')], verbose_name="操作类型",
        column_name=UUID.lemon_exporttemplate.operation_type.value)
    state = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')], verbose_name="是否激活",
        column_name=UUID.lemon_exporttemplate.state.value)
    is_delete = LemonBooleanField(
        default=False, constraints=[SQL('DEFAULT False')],
        column_name=UUID.lemon_exporttemplate.is_delete.value)
    ext_tenant = LemonCharField(
        max_length=32, default=None, constraints=[SQL('DEFAULT NULL')], null=True,
        column_name=UUID.lemon_exporttemplate.ext_tenant.value)
    user_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_exporttemplate.user_uuid.value)
    branch_uuid = LemonCharField(
        max_length=32, default="", constraints=[SQL('DEFAULT ""')],
        column_name=UUID.lemon_exporttemplate.branch_uuid.value)

    class Meta():
        display_name = "导出模板"
        model_name = display_name
        module_name = "系统模块"
        table_function = make_design_table_name
        ignore_tenant = True
        evolve = True
        version_control = True
        schema = "lemon"
        indexes = (
            (("app_uuid",), False),
            (("module_uuid",), False),
            (("document_uuid",), False),
            (("template_uuid", "user_uuid", "branch_uuid"), True),
            (("user_uuid", "branch_uuid"), False),
        )

    @hybrid_property
    def 模板标识符(self):
        return self.template_uuid

    @hybrid_property
    def 模板名(self):
        return self.template_name

    @hybrid_property
    def 模板类型(self):
        return self.template_type

    @hybrid_property
    def 操作类型(self):
        return self.operation_type

    @hybrid_property
    def 是否激活(self):
        return self.state

# origin_table_name: uuid, 不会变化
# class_table_name: 定义模型时创建, tenant_user, 等同于peewee.make_table_name
# meta_table_name: class_table_name的固定, 即使copy和表名改变, 也不会变化
# model_name: 类似display_name, 用户看到并云函数中可使用
# table_name: 数据库实际名字


class SystemTableHandler:

    _lemon_model = [UserRole, ExportTemplate, ModelBasic, Func, BaseResource, Tag,
                    BaseNavigation, BaseNavigationItem]
    _runtime_model = [RoleMember, SystemConfig, ModelCloudFunction, PermissionConfig]

    def __init__(self) -> None:
        self.temp_sys_table_dict = {}

    def make_sys_table_name(self, instance, app_uuid, app_revision):
        version = str(app_revision)
        if instance in self._runtime_model:
            version = None
        return make_runtime_table_name(app_uuid, version, instance._meta.table_name)

    def process_field(self, instance):
        for field in instance._meta.sorted_fields:
            if not getattr(field, "verbose_name", None) and \
                    field.name not in SystemField.FIELD_NAME_LIST + ["id", "tenant_uuid"]:
                field.hide = True

    def make_sys_from_user_model(self, app_uuid=None, app_revision=None, must_new_model=False, sub_env=False):
        # 从UserModel, 构造sys_model
        sys_table_dict = dict()
        in_runtime = os.environ.get("APP_UUID")
        for index, instance in enumerate(self._lemon_model + self._runtime_model):
            model_name = instance._meta.table_name
            display_name = instance._meta.display_name
            ignore_tenant = getattr(instance._meta, "ignore_tenant", False)
            select_enabled = getattr(instance._meta, "select_enabled", True)
            if not check_lemon_uuid(model_name):
                table_name = getattr(SystemNamespace, model_name, None)
            if not table_name:
                continue
            origin_table_name = table_name
            # 子环境只更新带版本号的表
            if sub_env and instance in self._runtime_model:
                continue
            if app_uuid and app_revision:
                table_name = self.make_sys_table_name(instance, app_uuid, app_revision)
            if in_runtime and model_name in MODEL_COPY_DICT and not must_new_model:
                sys_table_dict.update({
                    model_name: MODEL_COPY_DICT[model_name].get("model"),
                    display_name: MODEL_COPY_DICT[display_name].get("model")
                })
                continue
            new_meta_dict = {
                "is_copy": True,
                "display_name": display_name,
                "model_name": display_name,  # 中文
                "origin_table_name": origin_table_name,  # uuid
                "meta_table_name": model_name,  # 英文
                "table_name": table_name,
                "evolve": True,
                "indexes": instance._meta.indexes,
                "module_name": "系统模块",
                "ignore_tenant": ignore_tenant,
                "select_enabled": select_enabled
            }
            new_meta = types.new_class("Meta", (object, ), {}, lambda x: x.update(new_meta_dict))
            data_dict = {
                "Meta": new_meta
            }
            new_instance = types.new_class(
                table_name, (instance, ), {}, lambda x: x.update(data_dict))
            self.process_field(new_instance)
            sys_table_dict.update({
                model_name: new_instance,
                display_name: new_instance})
            self.temp_sys_table_dict.update({model_name: new_instance})
            if in_runtime and not must_new_model:
                MODEL_COPY_DICT.update(
                    {model_name: {"model": new_instance, "table_name": model_name}, 
                     display_name: {"model": new_instance, "table_name": model_name}})
        self.temp_sys_table_dict.clear()
        return sys_table_dict

    def clear(self):
        self.temp_sys_table_dict.clear()


class UniverDocument(BaseModel):

    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    page_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')], null=True)
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    component_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="组件id")
    unitid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="文档id")
    file_type = IntegerField(default=2, constraints=[SQL('DEFAULT 2')], verbose_name="文档类型")

    unitname = LemonCharField(max_length=64, default="", constraints=[SQL('DEFAULT ""')], verbose_name="文档名")
    sheetorder = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')], verbose_name="文档拥有者")
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    history_default_template = LemonCharField(max_length=300, verbose_name="历史默认模板", null=True)
    current_template = LemonCharField(max_length=300, verbose_name="当前使用模板", null=True)

    class Meta():
        sys_table = True
        evolve = True
        indexes = (
            (("component_uuid", "tenant_uuid", "unitid"), True),
        )


class UniverTemplate(BaseModel):

    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    component_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    template_url = LemonCharField(max_length=300, default="", constraints=[SQL('DEFAULT ""')])
    template_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    template_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        sys_table = True
        evolve = True
        indexes = (
            (("template_uuid", "tenant_uuid", "component_uuid"), True),
        )


sys_table_handler = SystemTableHandler()


if __name__ == "__main__":
    
    # sys_table_handler.make_runtime_sys_table("456525ee99d25c42ab805b4e53c78ec1", 159)
    sys_table_handler.make_sys_from_user_model("456525ee99d25c42ab805b4e53c78ec1", 159)
    # sys_table_handler.make_runtime_sys_table()
