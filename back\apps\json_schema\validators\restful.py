from apps.json_schema.validators.base import BaseValidator
from apps.json_schema.refresolver import RefResolver
from apps.json_schema.context import AppCtx, AppModel
from apps.utils import PageFinder
from apps.ide_const import Document
from apps.json_schema.utils import id_of
from loguru import logger


restful_json_schema = {
    "is_element": True,
    "attr_name": "服务器API",
    "type": "object",
    "properties": {
        "authorization": {
            "type": "number"
        },
        "anonymous_role": {
            "type": "string"
        },
        "versions": {
            "type": "array",
            "items": {
                "attr_name": "版本",
                "type": "object",
                "properties": {
                    "version": {
                        "type": "string"
                    },
                    "status": {
                        "type": "number"
                    },
                    "resources": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "resource": {
                                    "type": "string"
                                },
                                "requests": {
                                    "type": "array",
                                    "items": {
                                        "attr_name": "请求",
                                        "type": "object",
                                        "properties": {
                                            "method": {
                                                "type": "string"
                                            },
                                            "func": {
                                                "$ref": "mem://common/func_ref"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "if": {
        "properties": {
            "authorization": {
                "const": 2
            }
        }
    },
    "then": {
        "properties": {
            "anonymous_role": {
                "$ref": "mem://common/module_role_ref"
            }
        }
    }
}


class RestfulValidator(BaseValidator):
    def __init__(self, app_ctx, version: str = '1.0') -> None:
        super().__init__(app_ctx, version)
        self.document_type = Document.TYPE.RESTFUL
        self.schema = restful_json_schema
        self.resolver = RefResolver(base_uri=id_of(restful_json_schema), referrer=restful_json_schema)
        self.model_in_page = {}
