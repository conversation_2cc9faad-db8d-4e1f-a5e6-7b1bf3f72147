# -*- coding:utf-8 -*-

import os
import base64
import asyncio
from hashlib import md5

from baseutils.log import app_log
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.entity import EnumTable
from apps.ide_const import Enum, Icon
from apps.ide_const import LemonDesignerErrorC<PERSON> as LDEC
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker


class EnumItemCheckerService(CheckerService):

    attr_class = Enum.ATTR
    attr_name = attr_class.ITEM_NAME
    attr_uuid = attr_class.ITEM_UUID
    uuid_error = LDEC.ENUM_ITEM_UUID_ERROR
    uuid_unique_error = LDEC.ENUM_ITEM_UUID_UNIQUE_ERROR
    name_error = LDEC.ENUM_ITEM_NAME_FAILED
    name_unique_error = LDEC.ENUM_ITEM_NAME_NOT_UNIQUE
    allow_chinese_name = True
    allow_keywords = False
    
    def initialize(self):
        super().initialize()
        self.description = self.element.get("description")
        self.icon_type = self.element.get("icon_type")
        self.icon = self.element.get("icon")
        self.color = self.element.get("color")
    
    # 检查 图标类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    @checker.run
    def check_enum_item_icon_type(self):
        if self.icon_type and self.icon_type not in Icon.TYPE.ALL:
            attr = Enum.ATTR.ITEM_ICON_TYPE
            return_code = LDEC.ENUM_ITEM_ICON_TYPE_NOT_SUPPORT
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 icon 是否在应用中存在，如果不存在，会向错误列表添加一条报错信息
    def check_enum_item_icon(self, app_icon_uuid_set, app_image_uuid_set):
        attr = Enum.ATTR.ITEM_ICON
        return_code = LDEC.ENUM_ITEM_ICON_NOT_EXISTS
        return_code.message = return_code.message.format(name=self.element_name)
        if self.icon_type == Icon.TYPE.ICON:
            if self.icon is not None and self.icon not in app_icon_uuid_set:
                self._add_error_list(attr=attr, return_code=return_code)
        elif self.icon_type == Icon.TYPE.IMAGE:
            if self.icon is not None and self.icon not in app_image_uuid_set:
                self._add_error_list(attr=attr, return_code=return_code)
                
    def check_enum_item_value(self):
        attr = Enum.ATTR.VALUE
        return_code = LDEC.ENUM_ITEM_VALUE_ERROR
        value = self.element.get("value")
        if not value:
            value = self.element.get("name")
            self.element.update({
                "value": value
            })
        elif not isinstance(value, str):  # 可能会有更明确的value规则吧
            self._add_error_list(attr=attr, return_code=return_code)


class EnumCheckerService(CheckerService):

    attr_class = Enum.ATTR
    uuid_error = LDEC.ENUM_UUID_ERROR
    uuid_unique_error = LDEC.ENUM_UUID_UNIQUE_ERROR
    name_error = LDEC.ENUM_NAME_FAILED
    name_unique_error = LDEC.ENUM_NAME_NOT_UNIQUE
    
    allow_chinese_name = True  # 同时控制枚举类和枚举项名称
    allow_lemon_keywords = False
    
    def initialize(self):
        super().initialize()
        self.description = self.element.get("description")
        self.value = self.element.get("value")
        self.enum_item_name_uuid_set = set()
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            EnumTable.app_uuid.name: self.app_uuid,
            EnumTable.module_uuid.name: self.module_uuid,
            EnumTable.document_uuid.name: self.document_uuid,
            EnumTable.enum_uuid.name: self.element_uuid,
            EnumTable.enum_name.name: self.element_name,
            EnumTable.description.name: self.description,
            EnumTable.value.name: self.value
        }
    
    def build_update_query_data(self):
        return {
            EnumTable.enum_name.name: self.element_name,
            EnumTable.description.name: self.description,
            EnumTable.value.name: self.value,
            EnumTable.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return EnumTable.update(**query_data).where(
            EnumTable.enum_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(enum_uuid):
        return EnumTable.update(**{
                EnumTable.is_delete.name: True
            }).where(EnumTable.enum_uuid==enum_uuid)
    
    def check_modify(self, document_enum_uuid_set):
        if self.element_uuid in document_enum_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
    # 检查枚举值是否为list，如果不匹配，会向错误列表添加一条报错信息
    def check_enum_value(self, app_icon_uuid_set, app_image_uuid_set):
        if isinstance(self.value, list):
            for enum_item in self.value:
                enum_item_checker_service = EnumItemCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, 
                    self.document_uuid, self.document_name, enum_item)
                try:
                    enum_item_checker_service.check_name()
                    enum_item_checker_service.check_name_unique(self.enum_item_name_uuid_set)
                    enum_item_checker_service.check_all()
                    enum_item_checker_service.check_enum_item_icon(
                        app_icon_uuid_set, app_image_uuid_set)
                    enum_item_checker_service.check_enum_item_value()
                except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                    self.update_any_list(enum_item_checker_service)
                    raise e
                else:
                    self.update_any_list(enum_item_checker_service)
        else:
            attr = Enum.ATTR.VALUE
            return_code = LDEC.ENUM_VALUE_INCURRECT
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr=attr, return_code=return_code)


class EnumDocumentCheckerService(DocumentCheckerService):
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str,
        element: dict, document_version: int,
        app_enum_list: list, app_icon_uuid_set: set,
            app_image_uuid_set: set, app_model_list: list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, EnumTable, app_model_list, *args, **kwargs)
        self.app_icon_uuid_set = app_icon_uuid_set
        self.app_image_uuid_set = app_image_uuid_set
        self.enum_list = self.element.get("enum_list", list())
        self.app_enum_uuid_set = set()
        self.module_enum_name_set = set()
        self.document_enum_uuid_set = set()
        self.document_enum_update_list = list()
        self.document_enum_delete_list = list()
        self.other_conflict_document_names_set = kwargs.get("other_conflict_document_names_set", set())

        for enum in app_enum_list:
            enum_uuid = enum.get("enum_uuid", "")
            enum_name = enum.get("enum_name", "")
            enum_module_uuid = enum.get("module_uuid", "")
            enum_document_uuid = enum.get("document_uuid", "")

            # 找到原文档中所有的 枚举 ，为了新增、更新、删除文档的 枚举
            if enum_document_uuid == self.document_uuid:
                self.document_enum_uuid_set.add(enum_uuid)
            else:
                # 排除当前文档所有的 enum_uuid ，获取应用的所有 enum_uuid
                self.app_enum_uuid_set.add(enum_uuid)
                # 排除当前文档所有的 enum_uuid ，获取模块的所有 enum_uuid
                if enum_module_uuid == self.module_uuid and not enum.get("is_delete"):
                    self.module_enum_name_set.add(enum_name)

        # model
        self.module_model_name_set = set()
        # self.module_model_uuid_dict = dict()
        # self.document_model_uuid_set = set()
        # self.app_model_uuid_dict = dict()
        # self.app_model_uuid_set = set()
        # self.module_model_uuid_set = set()
        for _app_model in app_model_list:
            # model_uuid = _app_model.get("model_uuid", "")
            model_name = _app_model.get("model_name", "")
            model_module_uuid = _app_model.get("module_uuid", "")
            model_document_uuid = _app_model.get("document_uuid", "")

            # 找到模块所有的数据模型，为了检查模块中数据模型，uuid、name 是否重复等
            if model_module_uuid == self.module_uuid:
                pass
                # self.module_model_uuid_dict.update({model_uuid: _app_model})

            # 找到原文档中所有的数据模型，为了新增、更新、删除文档的数据模型
            if model_document_uuid == self.document_uuid:
                pass
                # self.document_enum_uuid_set.add(model_uuid)
            else:
                # 排除当前文档所有的 model_uuid，获取应用的所有 model_uuid
                # self.app_enum_uuid_set.add(model_uuid)
                # self.app_model_uuid_dict.update({model_uuid: _app_model})
                # 排除当前文档所有的 model_uuid，获取模块的所有 model_uuid
                if model_module_uuid == self.module_uuid:
                    # pass
                    # self.module_model_uuid_set.add(model_uuid)
                    self.module_model_name_set.add(model_name)

    @checker.run
    def check_enum_list(self):
        this_document_enum_uuid_set = set()
        for enum in self.enum_list:
            if self.is_copy:
                temp_name = enum.get("name")
                enum.update({"name": temp_name + "_" + str(self.document_number)})
                # self.module_enum_name_set.add(enum.get("name"))
            enum_checker_service = EnumCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, 
                    self.document_uuid, self.document_name, enum, is_copy=self.is_copy)
            enum_uuid = enum_checker_service.element_uuid
            this_document_enum_uuid_set.add(enum_uuid)
            try:
                enum_checker_service.check_uuid()
                enum_checker_service.check_uuid_unique(self.app_enum_uuid_set)
                enum_checker_service.check_name()
                enum_checker_service.check_name_unique(self.module_enum_name_set)
                enum_checker_service.check_name_unique(self.other_conflict_document_names_set)
                enum_checker_service.check_enum_value(
                    self.app_icon_uuid_set, self.app_image_uuid_set)
                enum_checker_service.check_all()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(enum_checker_service)
                raise e
            else:
                self.update_any_list(enum_checker_service)

            # 找到新增 或 更新的 枚举
            enum_checker_service.check_modify(self.document_enum_uuid_set)
            if enum_checker_service.insert_query_list:
                self.document_insert_list.extend(enum_checker_service.insert_query_list)
            if enum_checker_service.update_query_list:
                self.document_update_list.extend(enum_checker_service.update_query_list)

        # 找出删除的 枚举 ，将其 is_delete 置为 True
        delete_enum_uuid_set = self.document_enum_uuid_set - this_document_enum_uuid_set
        for this_enum_uuid in delete_enum_uuid_set:
            query = EnumCheckerService.build_delete_query(this_enum_uuid)
            self.document_delete_list.append(query)
