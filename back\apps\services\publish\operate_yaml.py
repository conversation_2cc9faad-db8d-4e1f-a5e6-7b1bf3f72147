'''
Author: lv.jimin
Date: 2021-11-17 20:01:53
LastEditors: huangl
LastEditTime: 2021-11-18 09:42:54
Description: file content
FilePath: /lemon/back/apps/services/publish/operate_yaml.py
'''
service_list = ["test"]
new_path_list  = []
for app_name  in service_list:
    service_data = {
                    'pathType': 'Prefix',
                    'path': f'/{app_name}(/|$)(.*)',
                    'backend': {
                        'service': {
                            'name': f'app-{app_name}',
                            'port': {
                                'number': 7000
                            }
                        }
                    }
                }
    new_path_list.append(service_data)
import yaml
ingress_file = f"/home/<USER>/lemon/back/apps/services/publish/ingress.yaml"
with open(ingress_file, 'r', encoding='utf-8') as f:
    data = yaml.safe_load(f)
    data["spec"]["rules"][0]["http"]['paths'] = new_path_list
with open("/r/ingress.yaml", "w", encoding='utf-8') as f:
    yaml.dump(data, f)