from apps.json_schema.const import ScopeType, ReferenceAttrType
from apps.json_schema.schemas.utils import gen_const_condition_schema
from apps.ide_const import (Document, VariableLevel,
                            ValueEditorType, ValueEditorVariables,
                            DataSourceType)

lemon_uuid = {
    "lemon_uuid": True,
    "type": "string"
}

model_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.MODEL
    },
    "type": "string"
}

model_ref_hide = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.MODEL,
        "hide": True
    },
    "type": "string"
}

field_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.MODEL_FIELD
    },
    "type": "string"
}

relationship_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.MODEL_RELATIONSHIP
    },
    "type": "string"
}

page_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.PAGE
    },
    "type": "string"
}

func_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.FUNC
    },
    "type": "string"
}

const_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.CONST,
    },
    "type": "string"
}

enum_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.ENUM
    },
    "type": "string"
}

enum_item_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.ENUM_ITEM,    # 引用类型
        "res_type": ReferenceAttrType.ENUM,         # 资源类型, app_ctx里查找时用的名字, 默认和ref_type一致
        "required": ["enum_uuid", "enum_item_name"],
        "res_key": "enum_uuid",
        "res_attr_key": "value",
        "instance_key": "enum_uuid",    # 默认和res_key一致
        "instance_attr_key": "enum_item_name",  # 默认和res_attr_key一致
    },
    "type": "object",
    "dependentRequired": {
        "enum_item_name": ["enum_uuid"]
    }
}

workflow_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.WORKFLOW
    },
    "type": "string"
}

image_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.IMAGE
    },
    "type": "string"
}

module_role_ref = {
    "reference": {
        "scope": ScopeType.APP,
        "ref_type": ReferenceAttrType.MODULE_ROLE
    },
    "type": "string"
}

icon = {
    "type": "object",
    "properties": {
        "icon_type": {
            "type": "number"
        }
    },
    "if": {
        "properties": {
            "icon_type": {
                "const": 2
            }
        }
    },
    "then": {
        "properties": {
            "image_uuid": {
                "$ref": "mem://common/image_ref"
            }
        }
    }
}


value_editor_string = {
    "type": "object",
    "properties": {
        "uuid": {
            "$ref": "mem://common/lemon_uuid",
        },
        "type": {
            "type": "number",
            "const": ValueEditorType.STRING
        },
        "value": {
            "anyOf": [
                {"type": "string"},
                {"type": "number"},
                {"type": "boolean"}
            ]
        }
    },
    "required": ["value"]
}

value_editor_const = {
    "type": "object",
    "properties": {
        "uuid": {
            "$ref": "mem://common/lemon_uuid",
        },
        "type": {
            "type": "number",
            "const": ValueEditorType.CONST
        },
        "const": {
            "$ref": "mem://common/const_ref"
        },
    },
    "required": ["const"]
}

value_editor_variable = {
    "type": "object",
    "properties": {
        "uuid": {
            "$ref": "mem://common/lemon_uuid",
        },
        "type": {
            "type": "number",
            "const": ValueEditorType.VARIABLE
        },
        "variable_level": {
            "type": "number",
            "enum": [VariableLevel.SYSTEM, VariableLevel.SM, VariableLevel.GLOBAL, VariableLevel.LOCAL, VariableLevel.FLOW]
        },
        "variable_uuid": {
            "type": "string",
            "enum": [v.uuid for v in ValueEditorVariables.ALL]
        }
    },
    "required": ["variable_level", "variable_uuid"]
}

value_editor_expr = {
    "type": "object",
    "properties": {
        "uuid": {
            "$ref": "mem://common/lemon_uuid",
        },
        "type": {
            "type": "number",
            "const": ValueEditorType.EXPR
        },
        "expr": {
            "$ref": "mem://func/python_code"
        },
        "exprArr": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "number",
                        "minimum": 0,
                    },
                    "key": {
                        "type": "number",
                        "minimum": 0,
                    },
                    "value": {
                        "allOf": [
                            gen_const_condition_schema(ValueEditorType.CONST, ref="mem://common/value_editor_const"),
                            gen_const_condition_schema(ValueEditorType.ENUM, ref="mem://common/value_editor_enum"),
                            gen_const_condition_schema(ValueEditorType.VARIABLE, ref="mem://common/value_editor_variable"),
                            gen_const_condition_schema(ValueEditorType.FIELD, ref="mem://common/value_editor_field")
                        ]
                    }
                }
            }
        }
    },
    "required": ["expr", "exprArr"]
}

value_editor_expr_exclude_field = {
    "type": "object",
    "properties": {
        "uuid": {
            "$ref": "mem://common/lemon_uuid",
        },
        "type": {
            "type": "number",
            "const": ValueEditorType.EXPR
        },
        "expr": {
            "$ref": "mem://func/python_code"
        },
        "exprArr": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {
                        "type": "number",
                        "minimum": 0,
                    },
                    "key": {
                        "type": "number",
                        "minimum": 0,
                    },
                    "value": {
                        "allOf": [
                            gen_const_condition_schema(ValueEditorType.CONST, ref="mem://common/value_editor_const"),
                            gen_const_condition_schema(ValueEditorType.ENUM, ref="mem://common/value_editor_enum"),
                            gen_const_condition_schema(ValueEditorType.VARIABLE, ref="mem://common/value_editor_variable")
                        ]
                    }
                }
            }
        }
    },
    "required": ["expr", "exprArr"]
}

value_editor_field = {
    "type": "object",
    "properties": {
        "uuid": {
            "$ref": "mem://common/lemon_uuid",
        },
        "type": {
            "type": "number",
            "const": ValueEditorType.FIELD
        },
        "model": {
            "$ref": "mem://common/model_ref"
        },
        "field": {
            "$ref": "mem://common/field_ref"
        },
        "path": {
            "type": "array",
            "items": {
                "$ref": "mem://common/relationship_ref"
            }
        },
        "aggre_func": {
            "anyOf": [
                {"type": "null"},
                {"type": "number"}
            ]
        }
    },
    "required": ["model", "field", "path"]
}

value_editor_enum = {
    "allOf": [
        {
            "type": "object",
            "properties": {
                "uuid": {
                    "$ref": "mem://common/lemon_uuid",
                },
                "type": {
                    "type": "number",
                    "const": ValueEditorType.ENUM
                }
            }
        },
        {"$ref": "mem://common/enum_item_ref"}
    ]
}


value_editor_all = {
    "allOf": [
        gen_const_condition_schema(ValueEditorType.STRING, ref="mem://common/value_editor_string"),
        gen_const_condition_schema(ValueEditorType.CONST, ref="mem://common/value_editor_const"),
        gen_const_condition_schema(ValueEditorType.ENUM, ref="mem://common/value_editor_enum"),
        gen_const_condition_schema(ValueEditorType.VARIABLE, ref="mem://common/value_editor_variable"),
        gen_const_condition_schema(ValueEditorType.FIELD, ref="mem://common/value_editor_field"),
        gen_const_condition_schema(ValueEditorType.EXPR, ref="mem://common/value_editor_expr")
    ]
}

value_editor_exclude_field = {
    "allOf": [
        gen_const_condition_schema(ValueEditorType.STRING, ref="mem://common/value_editor_string"),
        gen_const_condition_schema(ValueEditorType.CONST, ref="mem://common/value_editor_const"),
        gen_const_condition_schema(ValueEditorType.ENUM, ref="mem://common/value_editor_enum"),
        gen_const_condition_schema(ValueEditorType.VARIABLE, ref="mem://common/value_editor_variable"),
        gen_const_condition_schema(ValueEditorType.EXPR, ref="mem://common/value_editor_expr_exclude_field")
    ]
}


data_source_form_context = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": DataSourceType.FORM_WITH_CONTEXT
        },
        "model": {
            "$ref": "mem://common/model_ref"
        }
    },
    "required": ["type", "model"]
}

data_source_form_func = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": DataSourceType.FORM_WITH_FUNC
        },
        "model": {
            "$ref": "mem://common/model_ref"
        },
        "func": {
            "$ref": "mem://common/func_ref"
        }
    },
    "required": ["type", "model", "func"]
}

data_source_form_linkage = {
    "type": "object",
    "properties": {
        "type": {
            "type": "number",
            "const": DataSourceType.FORM_LINKAGE
        }
    }
}

data_source_form = {
    "allOf": [
        gen_const_condition_schema(DataSourceType.FORM_WITH_CONTEXT, ref="mem://common/data_source_form_context"),
        gen_const_condition_schema(DataSourceType.FORM_WITH_FUNC, ref="mem://common/data_source_form_func"),
        gen_const_condition_schema(DataSourceType.FORM_LINKAGE, ref="mem://common/data_source_form_linkage")
    ]
}

data_source_field = {

}