from apps.json_schema.validators.base import BaseValidator
from apps.json_schema.refresolver import RefResolver
from apps.json_schema.context import AppCtx, AppModel
from apps.utils import PageFinder
from apps.ide_const import Document
from apps.json_schema.utils import id_of
from loguru import logger
from apps.json_schema.schemas.print import print_schema


class PrintValidator(BaseValidator):
    def __init__(self, app_ctx, version: str = '1.0') -> None:
        super().__init__(app_ctx, version)
        self.document_type = Document.TYPE.PRINT
        self.schema = print_schema
        self.resolver = RefResolver(base_uri=id_of(print_schema), referrer=print_schema)
        self.model_in_page = {}
