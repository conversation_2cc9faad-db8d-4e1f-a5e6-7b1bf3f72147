#paper
import re
ReturnCode = 1
app_log = 1
allow_chinese_name = False  # name 能否使用中文名
# 检查 name 格式，是否正确，如果不通过，会向错误列表添加一条报错信息 
def check_name(self):
    attr = self.attr_name
    return_code = self.name_error
    self._check_name(attr=attr, return_code=return_code, chinese=allow_chinese_name)
    if not self.allow_keywords:
            self._check_name_kwords(attr=attr)
def _check_name(self, attr: str, return_code: ReturnCode, chinese: bool = False):
    if not check_lemon_name(self.element_name, chinese=chinese, size=40):
        app_log.info(f"element_name: {self.element_name}")
        self._add_error_list(attr, return_code)
        
def check_lemon_name(name: str, chinese: bool = False, size: int = 40):
    if len(name) > size:
        return False
    if chinese:
        match_compile = re.compile(
            r"[a-zA-Z_\u4E00-\u9FFF]{1}[\w\u4E00-\u9FFF]{0,{y}}".replace("{y}", str(size - 1)))
    else:
        match_compile = re.compile(
            r"[a-zA-Z_]{1}[a-zA-Z0-9_]{0,{y}}".replace("{y}", str(size - 1)))
    return match_lemon_name(match_compile, name)
def match_lemon_name(compile: re.compile, name: str):
    match_obj = compile.match(name)
    if match_obj is None:
        return False
    match_name = match_obj.group()
    if match_name != name:
        return False
    return match_name