# -*- coding:utf-8 -*-

import asyncio

from baseutils.log import app_log
from apps.utils import check_lemon_name
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.entity import StateMachine as StateMachineModel
from apps.entity import Event as EventModel
from apps.entity import State as StateModel

from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.ide_const import (
    StateMachine, Event, Variable, Timer, State, Condition, Trigger, Transition, Action, 
    ValueEditor, VariableStateLocal
)
from apps.services import CheckerService, DocumentCheckerService
from apps.services import ValueCheckerService
from apps.services.checker import checker


class ConditionCheckerService(CheckerService):

    attr_class = Condition.ATTR
    uuid_error = LDEC.CONDITION_UUID_ERROR
    uuid_unique_error = LDEC.CONDITION_UUID_UNIQUE_ERROR
    name_error = LDEC.TRANSITION_CONDITION_NAME_FAILED
    name_unique_error = LDEC.TRANSITION_CONDITION_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.default = self.element.get("default")
        self.to_state = self.element.get("to_state")
        self.expr = self.element.get("expr")
        self.action_list = self.element.get("action_list")
        self.action_uuid_set = set()
    
    # 检查 目标状态 是否存在，如果不存在，会向错误列表添加一条报错信息
    def check_to_state(self, sm_state_uuid_set):
        if self.to_state not in sm_state_uuid_set:
            attr = Condition.ATTR.TO_STATE
            return_code = LDEC.TRANSITION_CONDITION_TO_STATE_NOT_EXISTS
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 条件表达式 是否准确，如果不正确，会向错误列表添加报错信息
    def check_expr(self):
        if self.expr is not None:
            value_checker_service = ValueCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, self.expr, self.element_uuid, self.element_name)
            value_checker_service.check_value(attr=Condition.ATTR.EXPR)
            self.update_any_list(value_checker_service)
    
    # 检查 条件转换列表
    def check_action_list(self, app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set):
        self._check_action_list(
            action_checker_class=ActionCheckerService, action_list=self.action_list, 
            app_func_uuid_dict=app_func_uuid_dict, action_uuid_set=self.action_uuid_set, 
            sm_variable_uuid_set=sm_variable_uuid_set, state_variable_uuid_set=state_variable_uuid_set)


class TransitionCheckerService(CheckerService):

    attr_class = Transition.ATTR
    uuid_error = LDEC.TRANSITION_UUID_ERROR
    uuid_unique_error = LDEC.TRANSITION_UUID_UNIQUE_ERROR
    name_error = LDEC.TRANSITION_NAME_FAILED
    name_unique_error = LDEC.TRANSITION_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.to_state = self.element.get("to_state")
        self.skip_entry_exit = self.element.get("skip_entry_exit")
        self.run_delay = self.element.get("run_delay")
        self.delay_time = self.element.get("delay_time")
        self.condition_list = self.element.get("condition_list")
        self.condition_uuid_set = set()
        self.condition_name_set = set()
    
    # 检查 自转换 和 转换目标 是否设定正确 或 存在，如果不正确，会向错误列表添加报错信息
    def check_skip_entry_exit(self, state_uuid, sm_state_uuid_set):
        to_self = False
        if self.condition_list:
            for condition in self.condition_list:
                to_state = condition.get("to_state")
                if to_state == state_uuid:
                    to_self = True
                    break
        else:
            if self.to_state == state_uuid:
                to_self = True
            if self.to_state not in sm_state_uuid_set:
                attr = Transition.ATTR.TO_STATE
                return_code = LDEC.TRANSITION_TO_STATE_NOT_EXISTS
                return_code.message = return_code.message.format(name=self.element_name)
                self._add_error_list(attr=attr, return_code=return_code)
        if to_self:
            if not isinstance(self.skip_entry_exit, bool):
                attr = Transition.ATTR.TO_SELF
                return_code = LDEC.TRANSITION_TO_SELF_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
        if self.skip_entry_exit is True:
            if not to_self:
                attr = Transition.ATTR.TO_SELF
                return_code = LDEC.TRANSITION_SKIP_ENTRY_EXIT_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 延迟转换 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_run_delay(self):
        if not isinstance(self.run_delay, bool):
            attr = Transition.ATTR.RUN_DELAY
            return_code = LDEC.TRANSITION_RUN_DELAY_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 延迟时间 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_delay_time(self):
        if self.run_delay is True:
            try:
                int(self.delay_time)
            except Exception:
                attr = Transition.ATTR.DELAY_TIME
                return_code = LDEC.TRANSITION_DELAY_TIME_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 条件列表
    def check_condition_list(
        self, sm_state_uuid_set, app_func_uuid_dict, 
        sm_variable_uuid_set, state_variable_uuid_set):
        condition_default_count = 0
        for condition in self.condition_list:
            condition_checker_service = ConditionCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, 
                self.document_name, condition)
            try:
                condition_checker_service.check_uuid()
                condition_checker_service.check_uuid_unique(self.condition_uuid_set)
                condition_checker_service.check_name()
                condition_checker_service.check_name_unique(self.condition_name_set)
                condition_checker_service.check_to_state(sm_state_uuid_set)
                condition_checker_service.check_expr()
                condition_checker_service.check_action_list(
                    app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(condition_checker_service)
                raise e
            else:
                self.update_any_list(condition_checker_service)
            if condition_checker_service.default is True:
                condition_default_count += 1
        if condition_default_count > 1:
            attr = Transition.ATTR.CONDITION_DEFAULT
            return_code = LDEC.TRANSITION_DEFAULT_CONDITION_LARGER_THEN_ONE
            self._add_error_list(attr=attr, return_code=return_code)


class ActionCheckerService(CheckerService):

    attr_class = Action.ATTR
    uuid_error = LDEC.ACTION_UUID_ERROR
    uuid_unique_error = LDEC.ACTION_UUID_UNIQUE_ERROR
    name_error = LDEC.ACTION_NAME_FAILED

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.action_list = self.element.get("action_list")
    
    # 检查 动作类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_action_type(self):
        if self.type not in Action.TYPE.ALL:
            attr = Action.ATTR.TYPE
            return_code = LDEC.ACTION_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 自动动作 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_auto(self):
        if self.type == Action.TYPE.FUNC:
            auto = self.element.get("auto", False)
            if not isinstance(auto, bool):
                attr = Action.ATTR.AUTO
                return_code = LDEC.ACTION_AUTO_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 云函数 是否在 应用 中存在，如果不存在，会向错误列表添加一条报错信息
    def check_func(self, app_func_uuid_dict):
        if self.type == Action.TYPE.FUNC:
            func_uuid = self.element.get("func")
            if func_uuid not in app_func_uuid_dict:
                attr = Action.ATTR.FUNC
                return_code = LDEC.ACTION_FUNC_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 异步执行 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_run_async(self):
        if self.type == Action.TYPE.FUNC:
            run_async = self.element.get("run_async", False)
            if not isinstance(run_async, bool):
                attr = Action.ATTR.RUN_ASYNC
                return_code = LDEC.ACTION_RUN_ASYNC_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 同步点类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_sync_type(self):
        if self.type == Action.TYPE.FUNC:
            run_async = self.element.get("run_async", False)
            if run_async is True:
                sync_type = self.element.get("sync_type", Action.SYNC_TYPE.NONE)
                if sync_type not in Action.SYNC_TYPE.ALL:
                    attr = Action.ATTR.SYNC_TYPE
                    return_code = LDEC.ACTION_SYNC_TYPE_NOT_SUPPORT
                    self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 延迟执行 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_run_delay(self):
        if self.type == Action.TYPE.FUNC:
            run_delay = self.element.get("run_delay", False)
            if not isinstance(run_delay, bool):
                attr = Action.ATTR.RUN_DELAY
                return_code = LDEC.ACTION_RUN_DELAY_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 延迟执行事件 是否设置正确，如果不正确，会向错误列表添加一条报错信息
    def check_delay_time(self):
        if self.type == Action.TYPE.FUNC:
            run_delay = self.element.get("run_delay", False)
            if run_delay is True:
                delay_time = self.element.get("delay_time")
                try:
                    int(delay_time)
                except Exception:
                    attr = Action.ATTR.DELAY_TIME
                    return_code = LDEC.ACTION_DELAY_TIME_INCURRECT
                    self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 数据绑定参数 与 云函数参数
    def check_arg_list(self, app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set):
        all_variable_uuid_set = sm_variable_uuid_set | state_variable_uuid_set
        if self.type == Action.TYPE.FUNC:
            func_uuid = self.element.get("func")
            if func_uuid in app_func_uuid_dict:
                func = app_func_uuid_dict.get(func_uuid, dict())
                func_arg_name_set = set()
                func_return_name_set = set()
                func_arg_list = func.get("arg_list", list())
                func_return_list = func.get("return_list", list())
                for arg in func_arg_list:
                    arg_name = arg.get("name")
                    func_arg_name_set.add(arg_name)
                for r in func_return_list:
                    r_name = r.get("name")
                    func_return_name_set.add(r_name)
                arg_list = self.element.get("arg_list", list())
                return_list = self.element.get("return_list", list())
                for arg in arg_list:
                    arg_name = arg.get("name")
                    arg_type = arg.get("type")
                    if arg_name not in func_arg_name_set:
                        attr = Action.ATTR.DATA_BIND_ARG
                        return_code = LDEC.ACTION_DATA_BIND_ARG_INCURRECT
                        return_code.message = return_code.message.format(name=arg_name)
                        self._add_error_list(attr=attr, return_code=return_code)
                    if arg_type not in Variable.TYPE.ALL:
                        attr = Action.ATTR.DATA_BIND_ARG_TYPE
                        return_code = LDEC.ACTION_DATA_BIND_ARG_TYPE_NOT_SUPPORT
                        return_code.message = return_code.message.format(name=arg_name)
                        self._add_error_list(attr=attr, return_code=return_code)
                for rn in return_list:
                    rn_name = rn.get("name")
                    rn_type = rn.get("type")
                    bind_to = rn.get("bind_to")
                    if rn_name not in func_return_name_set:
                        attr = Action.ATTR.DATA_BIND_RETURN
                        return_code = LDEC.ACTION_DATA_BIND_RETURN_INCURRECT
                        return_code.message = return_code.message.format(name=rn_name)
                        self._add_error_list(attr=attr, return_code=return_code)
                    if rn_type not in Variable.TYPE.ALL:
                        attr = Action.ATTR.DATA_BIND_RETURN_TYPE
                        return_code = LDEC.ACTION_DATA_BIND_RETURN_TYPE_NOT_SUPPORT
                        return_code.message = return_code.message.format(name=rn_name)
                        self._add_error_list(attr=attr, return_code=return_code)
                    if bind_to:
                        if bind_to not in all_variable_uuid_set:
                            attr = Action.ATTR.DATA_BIND_VARIABLE
                            return_code = LDEC.ACTION_DATA_BIND_VARIABLE_NOT_EXISTS
                            return_code.message = return_code.message.format(name=rn_name)
                            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 for动作循环变量 是否存在，如果不存在，会向错误列表添加一条错误信息
    def check_loop_variable(self, state_variable_uuid_set):
        if self.type == Action.TYPE.FOR:
            for_expr = self.element.get("for_expr", dict())
            loop_variable = for_expr.get("loop_variable")
            if loop_variable not in state_variable_uuid_set:
                attr = Action.ATTR.LOOP_VARIABLE
                return_code = LDEC.ACTION_FOR_LOOP_VARIABLE_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 for动作循环列表
    def check_loop_list(self):
        if self.type == Action.TYPE.FOR:
            for_expr = self.element.get("for_expr", dict())
            loop_list = for_expr.get("loop_list")
            value_checker_service = ValueCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, 
                self.document_name, loop_list, self.element_uuid, self.element_name)
            value_checker_service.check_value(attr=Action.ATTR.LOOP_LIST)
            self.update_any_list(value_checker_service)
    
    # 检查 if动作控制流 是否设置正确
    def check_control_flow(self):
        if self.type == Action.TYPE.IF:
            attr = Action.ATTR.CONTROL_FLOW
            return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
            if_expr = self.element.get("if_expr", dict())
            else_expr = self.element.get("else_expr", dict())
            elif_expr_list = self.element.get("elif_expr_list", list())
            if not isinstance(if_expr, dict):
                return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
                return_code.message = return_code.message.format(control="if")
                self._add_error_list(attr=attr, return_code=return_code)
            if not isinstance(else_expr, dict):
                return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
                return_code.message = return_code.message.format(control="else")
                self._add_error_list(attr=attr, return_code=return_code)
            if not if_expr:
                return_code = LDEC.ACTION_IF_CONTROL_FLOW_IF_LOST
                self._add_error_list(attr=attr, return_code=return_code)
            if not isinstance(elif_expr_list, list):
                return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
                return_code.message = return_code.message.format(control="elif")
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                for elif_expr in elif_expr_list:
                    if not isinstance(elif_expr, dict):
                        return_code = LDEC.ACTION_IF_CONTROL_FLOW_FAILED
                        return_code.message = return_code.message.format(control="elif")
                        self._add_error_list(attr=attr, return_code=return_code)


class TriggerCheckerService(CheckerService):

    attr_class = Trigger.ATTR
    uuid_error = LDEC.TRIGGER_UUID_ERROR
    uuid_unique_error = LDEC.TRIGGER_UUID_UNIQUE_ERROR
    name_error = LDEC.TRIGGER_NAME_FAILED
    name_unique_error = LDEC.TRIGGER_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.transition = self.element.get("transition")
    
    # 检查 触发器类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_trigger_type(self):
        if self.type not in Trigger.TYPE.ALL:
            attr = Trigger.ATTR.TYPE
            return_code = LDEC.TRIGGER_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 事件触发器事件列表
    def check_event_list(self, sm_event_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set):
        all_variable_uuid_set = sm_variable_uuid_set | state_variable_uuid_set
        if self.type == Trigger.TYPE.EVENT:
            event_list = self.element.get("event_list", list())
            for e in event_list:
                event_uuid = e.get("event")
                if event_uuid not in sm_event_uuid_dict:
                    attr = Trigger.ATTR.EVENT
                    return_code = LDEC.TRIGGER_EVENT_NOT_EXISTS
                    self._add_error_list(attr=attr, return_code=return_code)
                else:
                    event = sm_event_uuid_dict.get(event_uuid, dict())
                    event_arg_name_set = set()
                    arg_list = event.get("arg_list", [])
                    bind_list = e.get("bind_list", [])
                    # 构造 事件参数名称列表
                    for arg in arg_list:
                        arg_name = arg.get("name")
                        if arg_name:
                            event_arg_name_set.add(arg_name)
                    for bind in bind_list:
                        # 判断 事件参数名称 是否在 状态机事件参数列表 中
                        # 判断 变量UUID 是否在状态机变量列表 + 状态局部变量列表 中
                        a_name = bind.get("event_arg_name")
                        v_uuid = bind.get("variable_uuid")
                        if a_name not in event_arg_name_set:
                            attr = Trigger.ATTR.EVENT_ARG
                            return_code = LDEC.TRIGGER_EVENT_ARG_NOT_EXISTS
                            return_code.message = return_code.message.format(name=a_name)
                            self._add_error_list(attr=attr, return_code=return_code)
                        if v_uuid not in all_variable_uuid_set:
                            attr = Trigger.ATTR.EVENT_BIND_VARIABLE
                            return_code = LDEC.TRIGGER_EVENT_BIND_VARIABLE_NOT_EXISTS
                            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 定时触发器
    def check_timer(self, sm_timer_uuid_set):
        if self.type == Trigger.TYPE.TIMER:
            timer = self.element.get("timer")
            if timer not in sm_timer_uuid_set:
                attr = Trigger.ATTR.TIMER
                return_code = LDEC.TRIGGER_TIMER_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 转换
    def check_transition(
        self, state_transition_uuid_set, state_transition_name_set, state_uuid, 
        sm_state_uuid_set, app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set):
        if isinstance(self.transition, dict):
            transition_checker_service = TransitionCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, 
                self.document_name, self.transition)
            try:
                transition_checker_service.check_uuid()
                transition_checker_service.check_uuid_unique(state_transition_uuid_set)
                transition_checker_service.check_name()
                transition_checker_service.check_name_unique(state_transition_name_set)
                transition_checker_service.check_skip_entry_exit(state_uuid, sm_state_uuid_set)
                transition_checker_service.check_run_delay()
                transition_checker_service.check_delay_time()
                transition_checker_service.check_condition_list(
                    sm_state_uuid_set, app_func_uuid_dict, sm_variable_uuid_set, state_variable_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(transition_checker_service)
                raise e
            else:
                self.update_any_list(transition_checker_service)


class BaseTimerCheckerService(CheckerService):

    attr_class = Timer.ATTR
    uuid_error = LDEC.TIMER_UUID_ERROR
    uuid_unique_error = LDEC.TIMER_UUID_UNIQUE_ERROR
    name_error = LDEC.TIMER_NAME_FAILED
    name_unique_error = LDEC.TIMER_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.start_timestamp = self.element.get("start_timestamp")
    
    # 检查 定时器类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_timer_type(self):
        if self.type not in Timer.TYPE.ALL:
            attr = Timer.ATTR.TYPE
            return_code = LDEC.TIMER_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 开始时间 值编辑器数据是否合理，如果不合理，会向错误列表添加报错信息
    def check_start_timestamp(self):
        attr = Timer.ATTR.START_TIMESTAMP
        if isinstance(self.start_timestamp, int):
            if self.start_timestamp <= 0 or self.start_timestamp >= 9999999999:
                return_code = LDEC.TIMER_START_TIMESTAMP_VALUE_INCURRECT
                self._add_error_list(attr=attr, return_code=return_code)
        elif isinstance(self.start_timestamp, dict):
            value_check_service = ValueCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, self.start_timestamp, 
                element_uuid=self.element_uuid, element_name=self.element_name)
            value_check_service.check_value(attr=attr)
            self.update_any_list(value_check_service)
        else:
            return_code = LDEC.TIMER_START_TIMESTAMP_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    def check_timer(self):
        pass


class OnceTimerCheckerService(BaseTimerCheckerService):
    
    def check_timer(self):
        pass


class IntervalTimerCheckerService(BaseTimerCheckerService):

    def initialize(self):
        super().initialize()
        self.interval_type = self.element.get("interval_type")
        self.interval_value = self.element.get("interval_value")
    
    # 检查 间隔类型、间隔值 设定是否合理，如果不合理，会向错误列表添加报错信息
    def check_timer(self):
        if self.interval_type not in Timer.INTERVAL_TYPE.ALL:
            attr = Timer.ATTR.INTERVAL_TYPE
            return_code = LDEC.TIMER_INTERVAL_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
        try:
            int(self.interval_value)
        except Exception:
            attr = Timer.ATTR.INTERVAL_VALUE
            return_code = LDEC.TIMER_INTERVAL_VALUE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)


class PeriodTimerCheckerService(BaseTimerCheckerService):

    def initialize(self):
        super().initialize()
        self.period_type = self.element.get("period_type")
        self.period_value = self.element.get("period_value")
    
    # 检查 周期类型、周期值 设定是否合理，如果不合理，会向错误列表添加报错信息
    def check_timer(self):
        if self.period_type not in Timer.PERIOD_TYPE.ALL:
            attr = Timer.ATTR.PERIOD_TYPE
            return_code = LDEC.TIMER_PERIOD_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
        
        attr = Timer.ATTR.PERIOD_VALUE
        if not isinstance(self.period_value, list):
            return_code = LDEC.TIMER_PERIOD_VALUE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)
        if self.period_type == Timer.PERIOD_TYPE.WEEK:
            for v in self.period_value:
                if v not in list(range(7)):
                    return_code = LDEC.TIMER_PERIOD_VALUE_WEEK_INCURRECT
                    self._add_error_list(attr=attr, return_code=return_code)
                    break
        elif self.period_type == Timer.PERIOD_TYPE.MONTH:
            for v in self.period_value:
                if v not in list(range(1, 31)):
                    return_code = LDEC.TIMER_PERIOD_VALUE_MONTH_INCURRECT
                    self._add_error_list(attr=attr, return_code=return_code)
                    break


class TimerCheckerService(BaseTimerCheckerService):

    def initialize(self):
        super().initialize()
        self.timer_checker_class_dict = {
            Timer.TYPE.ONCE: OnceTimerCheckerService,
            Timer.TYPE.INTERVAL: IntervalTimerCheckerService,
            Timer.TYPE.PERIOD: PeriodTimerCheckerService
        }
    
    def check_timer(self):
        timer_checker_class = self.timer_checker_class_dict.get(self.type, BaseTimerCheckerService)
        if timer_checker_class:
            timer_checker_service = timer_checker_class(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, self.element,
                self.element_uuid, self.element_name)
            timer_checker_service.check_timer()
            self.update_any_list(timer_checker_service)


class VariableCheckerService(CheckerService):

    attr_class = Variable.ATTR
    uuid_error = LDEC.VARIABLE_UUID_ERROR
    uuid_unique_error = LDEC.VARIABLE_UUID_UNIQUE_ERROR
    name_error = LDEC.VARIABLE_NAME_FAILED
    name_unique_error = LDEC.VARIABLE_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.default = self.element.get("default")
        self.model = self.element.get("model")
    
    # 检查 变量类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_variable_type(self):
        attr = self.attr_class.TYPE
        if self.type not in Variable.TYPE.ALL:
            return_code = LDEC.VARIABLE_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 所选模型 是否存在并且 变量类型是否匹配，如果不匹配，会向错误列表添加一条报错信息
    def check_model(self, app_model_uuid_set, app_enum_uuid_set):
        attr = self.attr_class.MODEL
        # app_log.info((self.model, self.type))
        if self.model is None:
            if self.type == Variable.TYPE.ENUM:
                return_code = LDEC.VARIABLE_ENUM_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
            # elif self.type in Variable.TYPE.WITH_MODEL:
            #     return_code = LDEC.VARIABLE_MODEL_NOT_EXISTS
            #     self._add_error_list(attr=attr, return_code=return_code)
        else:
            if self.type == Variable.TYPE.ENUM:
                if self.model not in app_enum_uuid_set:
                    return_code = LDEC.VARIABLE_ENUM_NOT_EXISTS
                    self._add_error_list(attr=attr, return_code=return_code)
            elif self.type in Variable.TYPE.WITH_MODEL:
                if self.model not in app_model_uuid_set:
                    return_code = LDEC.VARIABLE_MODEL_NOT_EXISTS
                    self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 默认值 是否合理，如果不合理，会向错误列表添加一条报错信息
    def check_default(self):
        if isinstance(self.default, dict):
            value_check_service = ValueCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, self.default, 
                element_uuid=self.element_uuid, element_name=self.element_name)
            value_check_service.check_value(attr=self.attr_class.DEFAULT)
            self.update_any_list(value_check_service)


class EventCheckerService(CheckerService):

    attr_class = Event.ATTR
    uuid_error = LDEC.EVENT_UUID_ERROR
    uuid_unique_error = LDEC.EVENT_UUID_UNIQUE_ERROR
    name_error = LDEC.EVENT_NAME_FAILED
    name_unique_error = LDEC.EVENT_NAME_NOT_UNIQUE

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, sm_uuid: str, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.sm_uuid = sm_uuid

    def initialize(self):
        super().initialize()
        self.description = self.element.get("description", "")
        arg_list = self.element.get("arg_list", list())
        self.arg_list = list() if arg_list is None else arg_list
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            EventModel.app_uuid.name: self.app_uuid,
            EventModel.module_uuid.name: self.module_uuid,
            EventModel.document_uuid.name: self.document_uuid,
            EventModel.sm_uuid.name: self.sm_uuid,
            EventModel.event_uuid.name: self.element_uuid,
            EventModel.event_name.name: self.element_name,
            EventModel.description.name: self.description,
            EventModel.arg_list.name: self.arg_list
        }
    
    def build_update_query_data(self):
        return {
            EventModel.event_name.name: self.element_name,
            EventModel.description.name: self.description,
            EventModel.arg_list.name: self.arg_list,
            EventModel.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return EventModel.update(**query_data).where(
            EventModel.event_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(event_uuid):
        return EventModel.update(**{
                EventModel.is_delete.name: True
            }).where(EventModel.event_uuid==event_uuid)
    
    def check_modify(self, document_event_uuid_set):
        if self.element_uuid in document_event_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
    # 检查 参数列表，是否在可选范围内，如果不在，会向错误列表添加一条报错信息
    def check_arg_list(self, app_model_uuid_set, app_enum_uuid_set):

        def _check_arg_name(name):
            attr = Event.ATTR.ARG_NAME
            return_code = LDEC.EVENT_ARG_NAME_FAILED
            if not check_lemon_name(name):
                self._add_error_list(attr=attr, return_code=return_code)

        def _check_arg_name_unique(name, name_set):
            attr = Event.ATTR.ARG_NAME
            return_code = LDEC.EVENT_ARG_NAME_NOT_UNIQUE
            if name in name_set:
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                name_set.add(name)
        
        def _check_arg_type(arg_type):
            attr = Event.ATTR.ARG_TYPE
            return_code = LDEC.EVENT_ARG_TYPE_NOT_SUPPORT
            if arg_type not in Variable.TYPE.ALL:
                self._add_error_list(attr=attr, return_code=return_code)
        
        def _check_arg_model(arg_type, arg_model):
            if arg_model is None:
                if arg_type == Variable.TYPE.ENUM:
                    attr = Event.ATTR.ARG_ENUM
                    return_code = LDEC.EVENT_ARG_ENUM_NOT_EXISTS
                    self._add_error_list(attr=attr, return_code=return_code)
                # elif arg_type in Variable.TYPE.WITH_MODEL:
                #     attr = Event.ATTR.ARG_MODEL
                #     return_code = LDEC.EVENT_ARG_MODEL_NOT_EXISTS
                #     self._add_error_list(attr=attr, return_code=return_code)
            else:
                if arg_type == Variable.TYPE.ENUM:
                    if arg_model not in app_enum_uuid_set:
                        attr = Event.ATTR.ARG_ENUM
                        return_code = LDEC.EVENT_ARG_ENUM_NOT_EXISTS
                        self._add_error_list(attr=attr, return_code=return_code)
                elif arg_type in Variable.TYPE.WITH_MODEL:
                    if arg_model not in app_model_uuid_set:
                        attr = Event.ATTR.ARG_MODEL
                        return_code = LDEC.EVENT_ARG_MODEL_NOT_EXISTS
                        self._add_error_list(attr=attr, return_code=return_code)

        arg_name_set = set()
        for arg in self.arg_list:
            arg_name = arg.get("name")
            arg_type = arg.get("type")
            arg_model = arg.get("model")
            arg_default = arg.get("default")
            _check_arg_name(arg_name)
            _check_arg_name_unique(arg_name, arg_name_set)
            _check_arg_type(arg_type)
            _check_arg_model(arg_type, arg_model)
            if isinstance(arg_default, dict):
                value_check_service = ValueCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                    self.document_name, arg_default, 
                    element_uuid=self.element_uuid, element_name=self.element_name)
                value_check_service.check_value(attr=Event.ATTR.ARG_DEFAULT)
                self.update_any_list(value_check_service)


class BaseStateCheckerService(CheckerService):

    attr_class = State.ATTR
    uuid_error = LDEC.STATE_UUID_ERROR
    uuid_unique_error = LDEC.STATE_UUID_UNIQUE_ERROR
    name_error = LDEC.STATE_NAME_FAILED
    name_unique_error = LDEC.STATE_NAME_NOT_UNIQUE
    allow_chinese_name = True
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict, sm_uuid: str, app_sm_uuid_set: set, app_page_uuid_set: set, 
        app_user_role_uuid_set: set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.sm_uuid = sm_uuid
        self.app_sm_uuid_set = app_sm_uuid_set
        self.app_page_uuid_set = app_page_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type")
        self.entry_list = self.element.get("entry_list")
        self.exit_list = self.element.get("exit_list")
        self.trigger_list = self.element.get("trigger_list")
        self.variable_list = self.element.get("variable_list")
        self.entry_uuid_set = set()
        self.exit_uuid_set = set()
        self.trigger_uuid_set = set()
        self.trigger_name_set = set()
        self.transition_uuid_set = set()
        self.transition_name_set = set()
        self.variable_uuid_set = set()
        self.variable_name_set = set()
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            StateModel.app_uuid.name: self.app_uuid,
            StateModel.module_uuid.name: self.module_uuid,
            StateModel.document_uuid.name: self.document_uuid,
            StateModel.sm_uuid.name: self.sm_uuid,
            StateModel.state_uuid.name: self.element_uuid,
            StateModel.state_name.name: self.element_name,
            StateModel.state_type.name: self.type,
            StateModel.variable_list.name: self.variable_list
        }
    
    def build_update_query_data(self):
        return {
            StateModel.state_name.name: self.element_name,
            StateModel.state_type.name: self.type,
            StateModel.variable_list.name: self.variable_list,
            StateModel.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return StateModel.update(**query_data).where(
            StateModel.state_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(state_uuid):
        return StateModel.update(**{
                StateModel.is_delete.name: True
            }).where(StateModel.state_uuid==state_uuid)
    
    def check_modify(self, document_state_uuid_set):
        if self.element_uuid in document_state_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
    # 检查 状态类型 是否支持，如果不支持，会向错误列表添加一条报错信息
    def check_state_type(self):
        if self.type not in State.TYPE.ALL:
            attr = State.ATTR.TYPE
            return_code = LDEC.STATE_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查 进入动作列表
    def check_entry_list(self, app_func_uuid_dict, sm_variable_uuid_set):
        self._check_action_list(
            action_checker_class=ActionCheckerService, action_list=self.entry_list, 
            app_func_uuid_dict=app_func_uuid_dict, action_uuid_set=self.entry_uuid_set, 
            sm_variable_uuid_set=sm_variable_uuid_set, state_variable_uuid_set=self.variable_uuid_set)

    # 检查 退出动作列表
    def check_exit_list(self, app_func_uuid_dict, sm_variable_uuid_set):
        self._check_action_list(
            action_checker_class=ActionCheckerService, action_list=self.exit_list, 
            app_func_uuid_dict=app_func_uuid_dict, action_uuid_set=self.exit_uuid_set, 
            sm_variable_uuid_set=sm_variable_uuid_set, state_variable_uuid_set=self.variable_uuid_set)
    
    # 检查 局部变量 
    def check_variable_list(self, sm_variable_uuid_set, app_model_uuid_set, app_enum_uuid_set):
        self.variable_uuid_set = self.variable_uuid_set | sm_variable_uuid_set
        for variable in self.variable_list:
            variable_checker_service = VariableCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, variable,
                attr_class=VariableStateLocal)
            try:
                variable_checker_service.check_uuid()
                variable_checker_service.check_uuid_unique(self.variable_uuid_set)
                variable_checker_service.check_name()
                variable_checker_service.check_name_unique(self.variable_name_set)
                variable_checker_service.check_variable_type()
                variable_checker_service.check_model(app_model_uuid_set, app_enum_uuid_set)
                variable_checker_service.check_default()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(variable_checker_service)
                raise e
            else:
                self.update_any_list(variable_checker_service)
    
    # 检查 触发器
    def check_trigger_list(
        self, sm_event_uuid_dict, sm_variable_uuid_set, sm_timer_uuid_set, 
        sm_state_uuid_set, app_func_uuid_dict):
        for trigger in self.trigger_list:
            trigger_checker_service = TriggerCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, trigger)
            try:
                trigger_checker_service.check_uuid()
                trigger_checker_service.check_uuid_unique(self.trigger_uuid_set)
                trigger_checker_service.check_name()
                trigger_checker_service.check_name_unique(self.trigger_name_set)
                trigger_checker_service.check_trigger_type()
                trigger_checker_service.check_event_list(
                    sm_event_uuid_dict, sm_variable_uuid_set, self.variable_uuid_set)
                trigger_checker_service.check_timer(sm_timer_uuid_set)
                trigger_checker_service.check_transition(
                    self.transition_uuid_set, self.transition_name_set, self.element_uuid, sm_state_uuid_set,
                    app_func_uuid_dict, sm_variable_uuid_set, self.variable_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(trigger_checker_service)
                raise e
            else:
                self.update_any_list(trigger_checker_service)
    
    def check_state(self):
        pass


class AutoStateCheckerService(BaseStateCheckerService):
    
    def check_state(self):
        pass


class ManualStateCheckerService(BaseStateCheckerService):

    def initialize(self):
        super().initialize()
        self.handler_list = self.element.get("handler_list", list())
        self.handle_type = self.element.get("handle_type")
        self.handle_condition = self.element.get("handle_condition")
        self.handle_value = self.element.get("handle_value")
        self.page = self.element.get("page")
    
    # 检查 处理人类型、处理人值 是否合理，如果不合理，会向错误列表添加错误信息
    def check_handler_list(self):

        def _check_handler_type(handler_type):
            if handler_type not in State.HANDLER_TYPE.ALL:
                attr = State.ATTR.HANDLER_TYPE
                return_code = LDEC.STATE_MANUAL_HANDLER_TYPE_NOT_SUPPORT
                self._add_error_list(attr=attr, return_code=return_code)
        
        def _check_handler_role(handler_value):
            role_uuid = handler_value.get("role")
            if role_uuid not in self.app_user_role_uuid_set:
                attr = State.ATTR.HANDLER_VALUE
                return_code = LDEC.STATE_MANUAL_HANDLER_ROLE_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)

        def _check_handler_value(handler_value):
            if isinstance(handler_value, dict):
                value_check_service = ValueCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                    self.document_name, handler_value,
                    element_uuid=self.element_uuid, element_name=self.element_name)
                value_check_service.check_value(attr=State.ATTR.HANDLER_VALUE)
                self.update_any_list(value_check_service)

        for handler in self.handler_list:
            handler_type = handler.get("type")
            handler_value = handler.get("value")
            _check_handler_type(handler_type)
            if handler_type == State.HANDLER_TYPE.ROLE:
                _check_handler_role(handler_value)
            elif handler_type == State.HANDLER_TYPE.VALUE:
                _check_handler_value(handler_value)
    
    # 检查 多处理人规则 是否支持，如果不支持，会向错误列表添加一条错误信息
    def check_handle_type(self):
        if self.handle_type not in State.HANDLE_TYPE.ALL:
            attr = State.ATTR.HANDLE_TYPE
            return_code = LDEC.STATE_MANUAL_HANDLE_TYPE_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 多处理人状态跳转条件 是否支持，如果不支持，会向错误列表添加一条错误信息
    def check_handle_condition(self):
        if self.handle_condition not in State.HANDLE_CONDITION.ALL:
            attr = State.ATTR.HANDLE_CONDITION
            return_code = LDEC.STATE_MANUAL_HANDLE_CONDITION_NOT_SUPPORT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 多处理人状态跳转值 是否合理，如果不合理，会向错误列表添加一条错误信息
    def check_handle_value(self):
        try:
            int(self.handle_value)
        except Exception:
            attr = State.ATTR.HANDLE_VALUE
            return_code = LDEC.STATE_MANUAL_HANDLE_VALUE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 表单页面 是否存在，如果不存在，会向错误列表添加一条错误信息
    def check_page(self):
        if self.page and self.page not in self.app_page_uuid_set:
            attr = State.ATTR.PAGE
            return_code = LDEC.STATE_MANUAL_PAGE_NOT_EXISTS
            self._add_error_list(attr=attr, return_code=return_code)

    def check_state(self):
        self.check_handler_list()
        self.check_handle_type()
        self.check_handle_condition()
        self.check_handle_value()
        self.check_page()


class ChildStateCheckerService(BaseStateCheckerService):

    def initialize(self):
        super().initialize()
        self.child_sm = self.element.get("child_sm")
        self.recovery = self.element.get("recovery", True)
    
    def check_state(self):
        if not isinstance(self.recovery, bool):
            attr = State.ATTR.RECOVERY
            return_code = LDEC.STATE_CHILD_RECOVERY_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)
        if isinstance(self.child_sm, list):
            for c_sm in self.child_sm:
                if c_sm not in self.app_sm_uuid_set:
                    attr = State.ATTR.CHILD_SM
                    return_code = LDEC.STATE_CHILD_STATE_MACHINE_NOT_EXISTS
                    self._add_error_list(attr=attr, return_code=return_code)
        else:
            attr = State.ATTR.CHILD_SM
            return_code = LDEC.STATE_CHILD_STATE_MACHINE_INCURRECT
            self._add_error_list(attr=attr, return_code=return_code)


class StartStateCheckerService(BaseStateCheckerService):

    def initialize(self):
        super().initialize()
        self.state_name = "开始"
    
    def check_state(self):
        if self.element_name != self.state_name:
            attr = State.ATTR.NAME
            return_code = LDEC.STATE_START_NAME_MODIFY_FAILED
            self._add_error_list(attr=attr, return_code=return_code)


class EndStateCheckerService(BaseStateCheckerService):

    def initialize(self):
        super().initialize()
        self.state_name = "结束"
    
    def check_state(self):
        if self.element_name != self.state_name:
            attr = State.ATTR.NAME
            return_code = LDEC.STATE_END_NAME_MODIFY_FAILED
            self._add_error_list(attr=attr, return_code=return_code)


class StateCheckerService(BaseStateCheckerService):

    def initialize(self):
        super().initialize()
        self.state_checker_class_dict = {
            State.TYPE.AUTO: AutoStateCheckerService,
            State.TYPE.MANUAL: ManualStateCheckerService,
            State.TYPE.CHILD: ChildStateCheckerService,
            State.TYPE.START: StartStateCheckerService,
            State.TYPE.END: EndStateCheckerService
        }
    
    def check_state(self):
        state_checker_class = self.state_checker_class_dict.get(self.type, BaseStateCheckerService)
        state_checker_service = state_checker_class(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, self.element, self.sm_uuid, self.app_sm_uuid_set, 
                self.app_page_uuid_set, self.app_user_role_uuid_set)
        state_checker_service.check_state()
        self.update_any_list(state_checker_service)


class StateMachineCheckerService(CheckerService):

    attr_class = StateMachine.ATTR
    uuid_error = LDEC.SM_UUID_ERROR
    uuid_unique_error = LDEC.SM_UUID_UNIQUE_ERROR
    name_error = LDEC.SM_NAME_FAILED
    name_unique_error = LDEC.SM_NAME_NOT_UNIQUE

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict,  app_sm_uuid_set: set, app_model_uuid_set: set, 
        app_func_uuid_dict: dict, app_page_uuid_set: set, 
        app_const_uuid_set: set, app_enum_uuid_set: set, 
        app_user_role_uuid_set: set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.app_sm_uuid_set = app_sm_uuid_set
        self.app_model_uuid_set = app_model_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_uuid_set = app_page_uuid_set
        self.app_const_uuid_set = app_const_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set

    def initialize(self):
        super().initialize()
        self.display_name = self.element.get("display_name", "")
        self.description = self.element.get("description", "")
        self.multi_instance = self.element.get("multi_instance", True)
        self.strict_mode = self.element.get("strict_mode", True)
        self.start_state = self.element.get("start_state", dict())
        self.event_list = self.element.get("event_list", list())
        self.variable_list = self.element.get("variable_list", list())
        self.timer_list = self.element.get("timer_list", list())
        self.state_list = self.element.get("state_list", list())
        self.state_uuid_set = set()
        self.state_name_set = set()
        self.variable_uuid_set = set()
        self.variable_name_set = set()
        self.timer_uuid_set = set()
        self.timer_name_set = set()
        
        # 拿到当前状态机所有状态的UUID，判断转换的目标状态需要
        self.sm_state_uuid_set = set()
        self.sm_state_list = list()
        if isinstance(self.start_state, dict):
            state_uuid = self.start_state.get("uuid")
            self.sm_state_uuid_set.add(state_uuid)
            self.sm_state_list.append(self.start_state)
        for state in self.state_list:
            if isinstance(state, dict):
                state_uuid = state.get("uuid")
                self.sm_state_uuid_set.add(state_uuid)
                self.sm_state_list.append(state)
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
        self.event_insert_list = []
        self.event_update_list = []
        self.event_delete_list = []
        self.state_insert_list = []
        self.state_update_list = []
        self.state_delete_list = []
    
    def build_insert_query_data(self):
        return {
            StateMachineModel.app_uuid.name: self.app_uuid,
            StateMachineModel.module_uuid.name: self.module_uuid,
            StateMachineModel.document_uuid.name: self.document_uuid,
            StateMachineModel.sm_uuid.name: self.element_uuid,
            StateMachineModel.sm_name.name: self.element_name,
            StateMachineModel.display_name.name: self.display_name,
            StateMachineModel.description.name: self.description,
            StateMachineModel.multi_instance.name: self.multi_instance,
            StateMachineModel.strict_mode.name: self.strict_mode,
            StateMachineModel.variable_list.name: self.variable_list,
            StateMachineModel.states.name: list(self.state_name_set)
        }
    
    def build_update_query_data(self):
        return {
            StateMachineModel.sm_name.name: self.element_name,
            StateMachineModel.display_name.name: self.display_name,
            StateMachineModel.description.name: self.description,
            StateMachineModel.multi_instance.name: self.multi_instance,
            StateMachineModel.strict_mode.name: self.strict_mode,
            StateMachineModel.variable_list.name: self.variable_list,
            StateMachineModel.states.name: list(self.state_name_set),
            StateMachineModel.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        app_log.info(query_data)
        return StateMachineModel.update(**query_data).where(
            StateMachineModel.sm_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(sm_uuid):
        return StateMachineModel.update(**{
                StateMachineModel.is_delete.name: True
            }).where(StateMachineModel.sm_uuid==sm_uuid)
    
    def check_modify(self, document_sm_uuid_set):
        if self.element_uuid in document_sm_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)
    
    # 检查 多实例运行 是否正确设置，如果不正确，会向错误列表添加一条报错信息
    def check_multi_instance(self):
        if not isinstance(self.multi_instance, bool):
            attr = StateMachine.ATTR.MULTI_INSTANCE
            return_code = LDEC.SM_MULTI_INSTANCE_INCORRECT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 严格模式 是否正确设置，如果不正确，会向错误列表添加一条报错信息
    def check_strict_mode(self):
        if not isinstance(self.strict_mode, bool):
            attr = StateMachine.ATTR.STRICT_MODE
            return_code = LDEC.SM_STRICT_MODE_INCORRECT
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 开始状态 是否有且只有一个，如果不正确，会向错误列表添加报错信息
    def check_start_state(self):
        if not isinstance(self.start_state, dict):
            attr = StateMachine.ATTR.START_STATE
            return_code = LDEC.SM_START_STATE_MUST_ONLY_ONE
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查 事件列表
    def check_event_list(
        self, event_uuid_set, event_name_set, event_uuid_dict, 
        app_model_uuid_set, app_enum_uuid_set, document_event_uuid_set):
        this_event_uuid_set = set()
        for event in self.event_list:
            event_checker_service = EventCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, event, self.element_uuid)
            event_uuid = event_checker_service.element_uuid
            try:
                event_checker_service.check_uuid()
                event_checker_service.check_uuid_unique(event_uuid_set)
                event_checker_service.check_name()
                event_checker_service.check_name_unique(event_name_set)
                event_checker_service.check_arg_list(app_model_uuid_set, app_enum_uuid_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(event_checker_service)
                raise e
            else:
                self.update_any_list(event_checker_service)
            event_uuid_dict.update({event_uuid: event})
            this_event_uuid_set.add(event_uuid)

            # 找到新增 或 更新的 事件
            event_checker_service.check_modify(document_event_uuid_set)
            if event_checker_service.insert_query_list:
                self.event_insert_list.extend(event_checker_service.insert_query_list)
            if event_checker_service.update_query_list:
                self.event_update_list.extend(event_checker_service.update_query_list)
            
            # 找出删除的 事件，将其 is_delete 置为 True
        delete_event_uuid_set = document_event_uuid_set - this_event_uuid_set
        for this_event_uuid in delete_event_uuid_set:
            query = event_checker_service.build_delete_query(this_event_uuid)
            self.event_delete_list.append(query)
    
    # 检查 变量列表
    def check_variable_list(self, app_model_uuid_set, app_enum_uuid_set):
        for variable in self.variable_list:
            variable_checker_service = VariableCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, variable)
            try:
                variable_checker_service.check_uuid()
                variable_checker_service.check_uuid_unique(self.variable_uuid_set)
                variable_checker_service.check_name()
                variable_checker_service.check_name_unique(self.variable_name_set)
                variable_checker_service.check_variable_type()
                variable_checker_service.check_model(app_model_uuid_set, app_enum_uuid_set)
                variable_checker_service.check_default()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(variable_checker_service)
                raise e
            else:
                self.update_any_list(variable_checker_service)
    
    # 检查 定时器列表
    def check_timer_list(self):
        for timer in self.timer_list:
            timer_checker_service = TimerCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, timer)
            try:
                timer_checker_service.check_uuid()
                timer_checker_service.check_uuid_unique(self.timer_uuid_set)
                timer_checker_service.check_name()
                timer_checker_service.check_name_unique(self.timer_name_set)
                timer_checker_service.check_timer_type()
                timer_checker_service.check_start_timestamp()
                timer_checker_service.check_timer()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(timer_checker_service)
                raise e
            else:
                self.update_any_list(timer_checker_service)
    
    # 检查 各个状态
    def check_state_list(self, document_state_uuid_set, event_uuid_dict):
        this_state_uuid_set = set()
        for state in self.sm_state_list:
            state_checker_service = StateCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                self.document_name, state, self.element_uuid, self.app_sm_uuid_set, 
                self.app_page_uuid_set, self.app_user_role_uuid_set)
            state_uuid = state_checker_service.element_uuid
            try:
                state_checker_service.check_uuid()
                state_checker_service.check_uuid_unique(self.state_uuid_set)
                state_checker_service.check_name()
                state_checker_service.check_name_unique(self.state_name_set)
                state_checker_service.check_state_type()
                state_checker_service.check_entry_list(self.app_func_uuid_dict, self.variable_uuid_set)
                state_checker_service.check_exit_list(self.app_func_uuid_dict, self.variable_uuid_set)
                state_checker_service.check_variable_list(
                    self.variable_uuid_set, self.app_model_uuid_set, self.app_enum_uuid_set)
                state_checker_service.check_trigger_list(
                    event_uuid_dict, self.variable_uuid_set, self.timer_uuid_set, 
                    self.sm_state_uuid_set, self.app_func_uuid_dict)
                state_checker_service.check_state()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(state_checker_service)
                raise e
            else:
                self.update_any_list(state_checker_service)
            this_state_uuid_set.add(state_uuid)

            # 找到新增 或 更新的 状态
            state_checker_service.check_modify(document_state_uuid_set)
            if state_checker_service.insert_query_list:
                self.state_insert_list.extend(state_checker_service.insert_query_list)
            if state_checker_service.update_query_list:
                self.state_update_list.extend(state_checker_service.update_query_list)
            
            # 找出删除的 状态，将其 is_delete 置为 True
        delete_state_uuid_set = document_state_uuid_set - this_state_uuid_set
        for this_state_uuid in delete_state_uuid_set:
            query = state_checker_service.build_delete_query(this_state_uuid)
            self.state_delete_list.append(query)


class StateMachineDocumentCheckerService(DocumentCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str,
        element: dict, document_version: int, 
        app_sm_list: list, app_event_list: list, app_state_list: list,
        app_model_uuid_set: set, app_func_uuid_dict: dict, 
        app_page_uuid_set: set, app_const_uuid_set: set, 
        app_enum_uuid_set: set, app_user_role_uuid_set: set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, StateMachineModel, *args, **kwargs)
        self.document_version = document_version
        self.app_model_uuid_set = app_model_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_page_uuid_set = app_page_uuid_set
        self.app_const_uuid_set = app_const_uuid_set
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_user_role_uuid_set = app_user_role_uuid_set
        self.app_sm_uuid_set = set()
        self.app_event_uuid_set = set()
        self.app_event_uuid_dict = dict()
        self.module_sm_name_set = set()
        self.module_event_name_set = set()
        self.document_sm_uuid_set = set()
        self.document_event_uuid_set = set()
        self.document_state_uuid_set = set()
        self.event_insert_list = list()
        self.event_update_list = list()
        self.event_delete_list = list()
        self.state_insert_list = list()
        self.state_update_list = list()
        self.state_delete_list = list()
        for sm in app_sm_list:
            sm_uuid = sm.get("sm_uuid", "")
            sm_name = sm.get("sm_name", "")
            sm_module_uuid = sm.get("module_uuid", "")
            sm_document_uuid = sm.get("document_uuid", "")

            # 找到原文档中所有的状态机，为了新增、更新、删除文档的状态机
            if sm_document_uuid == self.document_uuid:
                self.document_sm_uuid_set.add(sm_uuid)
            else:
                # 排除当前文档所有的 sm_uuid，获取应用的所有 sm_uuid
                self.app_sm_uuid_set.add(sm_uuid)
                # 排除当前文档所有的 sm_name，获取模块的所有 sm_name
                if sm_module_uuid == self.module_uuid:
                    self.module_sm_name_set.add(sm_name)

        for event in app_event_list:
            event_uuid = event.get("event_uuid", "")
            event_name = event.get("event_name", "")
            event_module_uuid = event.get("module_uuid", "")
            event_document_uuid = event.get("document_uuid", "")

            # 找到原文档中所有的事件，为了新增、更新、删除文档的事件
            if event_document_uuid == self.document_uuid:
                self.document_event_uuid_set.add(event_uuid)
            else:
                # 排除当前文档所有的 event_uuid，获取应用的所有 event_uuid
                self.app_event_uuid_set.add(event_uuid)
                self.app_event_uuid_dict.update({event_uuid: event})
                # 排除当前文档所有的 event_name，获取模块的所有 event_name
                if event_module_uuid == self.module_uuid:
                    self.module_event_name_set.add(event_name)

        for state in app_state_list:
            state_uuid = state.get("state_uuid", "")
            state_document_uuid = state.get("document_uuid", "")

            # 找到原文档中所有的 状态，为了新增、更新、删除文档的 状态
            if state_document_uuid == self.document_uuid:
                self.document_state_uuid_set.add(state_uuid)

    @checker.run
    def check_state_machine(self):
        state_machine_checker_service = StateMachineCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, self.element,
                self.app_sm_uuid_set, self.app_model_uuid_set,
                self.app_func_uuid_dict, self.app_page_uuid_set,
                self.app_const_uuid_set, self.app_enum_uuid_set, 
                self.app_user_role_uuid_set)
        sm_uuid = state_machine_checker_service.element_uuid
        try:
            state_machine_checker_service.check_uuid()
            state_machine_checker_service.check_uuid_unique(self.app_sm_uuid_set)
            state_machine_checker_service.check_name()
            state_machine_checker_service.check_name_unique(self.module_sm_name_set)
            state_machine_checker_service.check_multi_instance()
            state_machine_checker_service.check_strict_mode()
            state_machine_checker_service.check_start_state()
            state_machine_checker_service.check_event_list(
                self.app_event_uuid_set, self.module_event_name_set, self.app_event_uuid_dict, 
                self.app_model_uuid_set, self.app_enum_uuid_set, self.document_event_uuid_set)
            state_machine_checker_service.check_variable_list(self.app_model_uuid_set, self.app_enum_uuid_set)
            state_machine_checker_service.check_timer_list()
            state_machine_checker_service.check_state_list(self.document_state_uuid_set, self.app_event_uuid_dict)
        
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(state_machine_checker_service)
            raise e
        else:
            self.update_any_list(state_machine_checker_service)

        # 找到新增 或 更新的状态机
        state_machine_checker_service.check_modify(self.document_sm_uuid_set)
        if state_machine_checker_service.insert_query_list:
            self.document_insert_list.extend(state_machine_checker_service.insert_query_list)
        if state_machine_checker_service.update_query_list:
            self.document_update_list.extend(state_machine_checker_service.update_query_list)
        
        # 找出删除的状态机，将其 is_delete 置为 True
        delete_sm_uuid_set = self.document_sm_uuid_set - set([sm_uuid])
        for this_sm_uuid in delete_sm_uuid_set:
            query = StateMachineCheckerService.build_delete_query(this_sm_uuid)
            self.document_delete_list.append(query)
        
        self.event_insert_list = state_machine_checker_service.event_insert_list
        self.event_update_list = state_machine_checker_service.event_update_list
        self.event_delete_list = state_machine_checker_service.event_delete_list

        self.state_insert_list = state_machine_checker_service.state_insert_list
        self.state_update_list = state_machine_checker_service.state_update_list
        self.state_delete_list = state_machine_checker_service.state_delete_list
    
    async def commit_modify(self, engine):
        document_update_func_list = []
        document_delete_func_list = []

        event_update_func_list = []
        event_delete_func_list = []

        state_update_func_list = []
        state_delete_func_list = []

        for query in self.document_update_list:
            func = engine.access.update_obj_by_query(self.model, query, need_delete=True)
            document_update_func_list.append(func)
        for query in self.document_delete_list:
            func = engine.access.update_obj_by_query(self.model, query, need_delete=True)
            document_delete_func_list.append(func)

        for query in self.event_update_list:
            func = engine.access.update_obj_by_query(EventModel, query, need_delete=True)
            event_update_func_list.append(func)
        for query in self.event_delete_list:
            func = engine.access.update_obj_by_query(EventModel, query, need_delete=True)
            event_delete_func_list.append(func)

        for query in self.state_update_list:
            func = engine.access.update_obj_by_query(StateModel, query, need_delete=True)
            state_update_func_list.append(func)
        for query in self.state_delete_list:
            func = engine.access.update_obj_by_query(StateModel, query, need_delete=True)
            state_delete_func_list.append(func)
        
        # 这里如果数据量大的话，会有性能问题
        async with engine.db.objs.atomic():
            if self.document_insert_list:
                app_log.info(f"Insert {self.model.__name__}, len: {len(self.document_insert_list)}")
                await engine.access.insert_many_obj(self.model, self.document_insert_list)

            app_log.info(f"Update {self.model.__name__}, len: {len(document_update_func_list)}")
            await asyncio.gather(*document_update_func_list)

            app_log.info(f"Update {self.model.__name__}.is_delete, len: {len(document_delete_func_list)}")
            await asyncio.gather(*document_delete_func_list)
            
            if self.event_insert_list:
                app_log.info(f"Insert {EventModel.__name__}, len: {len(self.event_insert_list)}")
                await engine.access.insert_many_obj(EventModel, self.event_insert_list)

            app_log.info(f"Update {EventModel.__name__}, len: {len(event_update_func_list)}")
            await asyncio.gather(*event_update_func_list)

            app_log.info(f"Update {EventModel.__name__}.is_delete, len: {len(event_delete_func_list)}")
            await asyncio.gather(*event_delete_func_list)
            
            if self.state_insert_list:
                app_log.info(f"Insert {StateModel.__name__}, len: {len(self.state_insert_list)}")
                await engine.access.insert_many_obj(StateModel, self.state_insert_list)

            app_log.info(f"Update {StateModel.__name__}, len: {len(state_update_func_list)}")
            await asyncio.gather(*state_update_func_list)

            app_log.info(f"Update {StateModel.__name__}.is_delete, len: {len(state_delete_func_list)}")
            await asyncio.gather(*state_delete_func_list)
    