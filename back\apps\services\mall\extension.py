import asyncio
import re
import time
from traceback import print_exc
from collections import OrderedDict

from sanic.response import json

from baseutils.utils import timeit_async
from baseutils.celery_task_utils import TaskResult
from baseutils.const import Action, ImportResult, ImportType, ResourceType, ReturnCode
from apps.base_utils import lemon_uuid
from apps.engine import engine
from apps.entity import (
    Document as DocumentModel,
    Extension,
    DocumentContent,
    ExtensionDetail,
    RelationshipBasic,
    UserRole,
)
from apps.ide_const import Document, DocumentType, IDECode, Module, ImportStatus
from apps.utils import (
    LemonDictResponse as LDR,
    app_log,
    gen_lemon_uuid_by_pin_uuid,
    new_relationship_uuid,
    replace_relationship_uuid,
    replace_uuid_in_dict,
    update_image_url,
    gen_uuid_update_dict
)
from apps.helper import DesignOss
from apps.services.mall.const import ScopeType, ReleaseType
from apps.services.mall.utils import gen_lemon_fixed_uuids


class Deploy:

    def __init__(self, extension_uuid) -> None:
        self.extension_uuid = extension_uuid

    def to_dict(self):
        raise NotImplementedError

    async def init_vars(self):
        raise NotImplementedError

    def update_deploy_content(self):
        raise NotImplementedError


class UIDeploy(Deploy):

    async def init_vars(self): ...


class ModuleDeploy(Deploy):

    def __init__(self, extension_uuid) -> None:
        super().__init__(extension_uuid)

    async def get_deploy_content(self):
        document = await engine.access.get_document_by_module_uuid_document_type(
            self.extension_uuid, DocumentType.MODULE_DEPLOY, need_delete=True
        )
        self.deploy_is_delete = document.is_delete
        if document:
            content = await engine.access.get_document_content_by_document_uuid(
                document.document_uuid
            )
            return content
        else:
            raise Exception(IDECode.EXTENSION_DEPLOY_LOSS)

    async def init_vars(self):
        content = await self.get_deploy_content()
        self.deploy_uuid, self.deploy_content = (
            content.document_uuid,
            content.document_content,
        )
        self.on_shelf = self.deploy_content.get("on_shelf", False)
        self.on_shelf_time = self.deploy_content.get("on_shelf_time")
        self.show_name = self.deploy_content.get("show_name")
        self.release_type = self.deploy_content.get("release_type")
        self.current_version = self.deploy_content.get("current_version")
        self.previous_version = self.deploy_content.get("previous_version")
        self.included_module = self.deploy_content.get("included_module", "")

    def to_dict(self):
        return {
            "on_shelf": self.on_shelf,
            "on_shelf_time": self.on_shelf_time,
            "show_name": self.show_name,
            "release_type": self.release_type,
            "current_version": self.current_version,
            "previous_version": self.previous_version,
            "included_module": self.included_module,
        }

    def export(self):
        self.on_shelf = True
        self.on_shelf_time = int(time.time())

    def version_update(self):
        self.previous_version = self.current_version
        self.current_version = ""

    def cancel_export(self):
        self.on_shelf = False
        # 之前上架的信息, 不清除
        # self.on_shelf_time = 0
        # self.previous_version = ""
        # self.current_version = ""

    def update_deploy_content(self):
        self.deploy_content.update(self.to_dict())


class APPDeploy(Deploy):

    def __init__(self, document_uuid) -> None:
        super().__init__(document_uuid)


class BaseExtension:
    _fields = [
        "app_uuid",
        "extension_uuid",
        "extension_name",
        "business",
        "cover",
        "preview",
        "introduction",
        "need_init",
        "publisher",
        "user_uuid",
        "extension_id"
    ]

    def __init__(self, *args, **kwargs):
        if len(args) > len(self._fields):
            raise TypeError("Expected {} arguments".format(len(self._fields)))

        # Set all of the positional arguments
        for name, value in zip(self._fields, args):
            setattr(self, name, value)

        # Set the remaining keyword arguments
        for name in self._fields[len(args):]:
            setattr(self, name, kwargs.pop(name, None))

        if kwargs:
            app_log.info(f"init Extension, extra argument(s): {','.join(kwargs)}")

        self.deploy = Deploy(self.extension_uuid)
        self.extension_data = dict()
        self.extension_update_data = dict()
        self.extension_detail_update_data = dict()
        # self.need_init = self.need_init

    async def init_vars(self, *args, **kwargs):
        raise NotImplementedError

    async def init_extension_data(self, *args, **kwargs):
        raise NotImplementedError

    def base_check(self):
        raise NotImplementedError

    def check_public(self):
        if not self.publisher:
            raise Exception(IDECode.ONLY_PUBLISHER_DO_PUBLIC)

    async def handle(self, action):
        # XXX check 的方法写的不好
        try:
            await self.init_vars()
            await self.deploy.init_vars()
            await self.init_extension_data(action=action)

            self.base_check()
            check_func = getattr(self, "check_" + action, None)
            if check_func and callable(check_func):
                check_func()
        except Exception as e:
            if len(e.args):
                return json(LDR(e.args[0]))

        func = getattr(self, "do_" + action, None)
        if func and callable(func):
            return await func() or json(LDR())
        else:
            return json(LDR(IDECode.INVALID_ACTION))

    async def create_extension(self):
        extension_info = self.extension_data.pop("extension_info", {})
        extension_obj = await engine.access.create_obj(Extension, **self.extension_data)
        self.extension_id = extension_obj.id
        await engine.access.create_obj(
            ExtensionDetail,
            extension_id=self.extension_id,
            extension_info=extension_info
        )

    async def update_extension(self):
        query = Extension.update(**self.extension_update_data).where(
            Extension.id == self.extension_id
        )
        await engine.access.update_obj_by_query(Extension, query)
        if self.extension_detail_update_data:
            query = ExtensionDetail.update(**self.extension_detail_update_data).where(
                ExtensionDetail.extension_id == self.extension_id
            )
            await engine.access.update_obj_by_query(ExtensionDetail, query)

    async def update_deploy(self):
        self.deploy.update_deploy_content()
        update_query = DocumentContent.update(
            **{"document_content": self.deploy.deploy_content}
        ).where(DocumentContent.document_uuid == self.deploy.deploy_uuid)
        await engine.access.update_obj_by_query(DocumentContent, update_query)

    def to_dict(self):
        return {
            Extension.app_uuid.name: self.app_uuid,
            Extension.extension_uuid.name: self.extension_uuid,
            Extension.extension_name.name: self.extension_name,
            Extension.business.name: self.business,
            Extension.cover.name: self.cover,
            Extension.preview.name: self.preview,
            Extension.introduction.name: self.introduction,
        }

    async def do_public(self):
        self.extension_update_data.update(
            {
                Extension.private.name: False,
                Extension.scope.name: ScopeType.PUBLIC,
                Extension.public_time.name: int(time.time()),
            }
        )
        await self.update_extension()

    async def do_private(self):
        self.extension_update_data.update(
            {Extension.private.name: True, Extension.scope.name: ScopeType.PRIVATE}
        )
        await self.update_extension()


class UIComponent(BaseExtension):

    _fields = [
        "app_uuid",
        "extension_uuid",
        "extension_name",
        "business",
        "cover",
        "preview",
        "introduction",
        "current_version",
        "publisher",
        "extension_info_add",
        "user_uuid",
        "extension_id"
    ]
    release_type = ReleaseType.UI

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.extension_uuid = self.extension_uuid or lemon_uuid()
        self.deploy = UIDeploy(self.extension_uuid)
        self.extension_info_add = getattr(self, "extension_info_add", dict()) or dict()

    def base_check(self): ...

    async def init_vars(self): ...

    async def do_export(self):
        await self.create_extension()

    async def do_debug(self):
        await self.create_extension()

    async def do_update(self):
        self.extension_info = await self.process_extension_body(action="update")
        self.extension_update_data.update(
            {
                Extension.update_time.name: int(time.time()),
                Extension.version.name: self.current_version,
                Extension.extension_name.name: self.extension_name,
                Extension.show_name.name: self.extension_name,
            }
        )
        self.extension_detail_update_data.update(
            {ExtensionDetail.extension_info.name: self.extension_info}
        )
        await self.update_extension()

    async def do_save(self):
        self.extension_update_data.update(self.to_dict())
        self.extension_update_data.update(
            {Extension.show_name.name: self.extension_name}
        )
        await self.update_extension()

    async def do_public(self):
        self.extension_update_data.update(
            {
                Extension.scope.name: ScopeType.PUBLIC,
                Extension.public_time.name: int(time.time()),
            }
        )
        await self.update_extension()

    async def init_extension_data(self, action):
        if action in ["export", "debug"]:
            if action == "export":
                scope = ScopeType.PRIVATE
            else:
                scope = ScopeType.DEBUG
            self.extension_info = await self.process_extension_body(action=action)
            self.extension_data = {
                Extension.scope.name: scope,
                Extension.show_name.name: self.extension_name,
                Extension.version.name: self.current_version,
                Extension.on_shelf_time.name: int(time.time()),
                Extension.update_time.name: int(time.time()),
                Extension.release_type.name: self.release_type,
                Extension.publisher.name: self.publisher,
                Extension.version.name: self.extension_info.get("version"),
                Extension.user_uuid.name: self.user_uuid,
            }
            self.extension_data.update({"extension_info": self.extension_info})
            self.extension_data.update(self.to_dict())
        else:
            query = Extension.select().where(
                Extension.extension_uuid == self.extension_uuid
            )
            self.extension_data = await engine.access.select_obj_by_query(
                Extension, query, as_dict=True
            )

    async def process_extension_body(self, action="export"):
        extension_info = dict()
        if action == "debug":
            extension_info.update(
                {
                    "component_name": "本地调试工具",
                    "component_id": "local-test-tool",
                    "version": "0.1",
                    "local_debug": True,
                }
            )
        elif action == "update":
            extension_data_dict = await engine.access.select_extension(
                self.app_uuid, self.extension_uuid, need_detail=True)
            extension_info = extension_data_dict.get("extension_info", dict())
        extension_info.update(self.extension_info_add)
        return extension_info

    async def do_delete(self):
        self.extension_update_data.update({Extension.is_delete.name: True})
        await self.update_extension()


class Template(BaseExtension):

    release_type = ReleaseType.TEMPLATE

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.deploy = ModuleDeploy(self.extension_uuid)

    async def init_vars(self):
        if self.need_init:
            query = Extension.select().where(
                Extension.extension_uuid == self.extension_uuid
            )
            extension: Extension = await engine.access.select_obj_by_query(
                Extension, query, as_dict=False
            )
            if not extension:
                raise Exception(IDECode.EXTENSION_NOT_FOUND)
            else:
                self.extension_name = extension.extension_name
                self.business = extension.business
                self.cover = extension.cover
                self.preview = extension.preview
                self.introduction = extension.introduction

    def base_check(self):
        if not self.extension_data:
            raise Exception(IDECode.EXTENSION_NOT_FOUND)
        if not all([self.business, self.cover, self.preview, self.introduction]):
            raise Exception(ReturnCode(553, "必填项填写不完整"))

    def check_export(self):
        if self.deploy.on_shelf == True:
            raise Exception(IDECode.EXTENSION_ALREADY_ON_SHELFED)
        if self.deploy.release_type != ReleaseType.TEMPLATE:
            raise Exception(IDECode.EXTENSION_ONLY_TEMPLATE)
        if not self.deploy.current_version:
            raise Exception(IDECode.EXTENSION_VERSION_NOT_SET)

    def check_delete(self):
        if not self.extension_data.get(Extension.private.name):
            raise Exception(IDECode.EXTENSION_PRIVATE_NOT_ALLOW_DELETE)

    def check_update(self):
        if not self.deploy.current_version:
            raise Exception(IDECode.EXTENSION_VERSION_NOT_SET)
        if self.deploy.deploy_is_delete:
            raise Exception(IDECode.EXTENSION_DEPLOY_LOSS)

    async def init_extension_data(self, action=None):
        if action == "export":
            self.extension_info = await self.process_extension_body()
            self.extension_data = {
                Extension.private.name: True,
                Extension.scope.name: ScopeType.PRIVATE,
                Extension.publisher.name: self.publisher,
            }
            self.extension_data.update({"extension_info": self.extension_info})
            self.extension_data.update(self.to_dict())
            self.extension_data.update(self.gen_base_publish_info())
        else:
            query = Extension.select().where(
                Extension.extension_uuid == self.extension_uuid
            )
            self.extension_data = await engine.access.select_obj_by_query(
                Extension, query, as_dict=True
            )

    async def process_extension_body(self):
        documents = await engine.access.list_document_by_module_uuid(
            self.extension_uuid
        )
        contents = await engine.access.list_document_content_by_module_uuid(
            self.extension_uuid
        )

        data = dict()
        for d in documents:
            data[d["document_uuid"]] = {"document": d}

        for c in contents:
            document_uuid = c.get("document_uuid")
            if document_uuid in data:
                data[document_uuid].update({"content": c})
        return data

    def gen_base_publish_info(self):
        return {
            Extension.show_name.name: self.deploy.show_name,
            Extension.version.name: self.deploy.current_version,
            Extension.on_shelf_time.name: self.deploy.on_shelf_time,
            Extension.update_time.name: self.deploy.on_shelf_time,
            Extension.release_type.name: self.deploy.release_type,
        }

    async def do_export(self):
        self.deploy.version_update()
        self.deploy.export()
        self.extension_data.update(
            {
                Extension.version.name: self.deploy.previous_version,  # deploy中已经将当前版本置为上一版本,因此这里取上一版本
                Extension.on_shelf_time.name: self.deploy.on_shelf_time,
                Extension.update_time.name: self.deploy.on_shelf_time,
                Extension.user_uuid.name: self.user_uuid,
            }
        )
        deploy_document = self.extension_info.get(self.deploy.deploy_uuid, dict())
        deploy_content = deploy_document.get("content", dict()).get(
            "document_content", dict()
        )
        deploy_content.update(
            {
                "on_shelf": self.deploy.on_shelf,
                "on_shelf_time": self.deploy.on_shelf_time,
                "previous_version": self.deploy.previous_version,
                "current_version": self.deploy.current_version,
            }
        )
        await self.create_extension()
        await self.update_deploy()

    async def do_update(self):
        self.deploy.version_update()
        self.extension_info = await self.process_extension_body()
        self.extension_update_data.update(
            {
                Extension.update_time.name: int(time.time()),
                Extension.version.name: self.deploy.previous_version,
            }
        )
        self.extension_detail_update_data.update(
            {ExtensionDetail.extension_info.name: self.extension_info}
        )
        await self.update_extension()
        await self.update_deploy()

    async def do_save(self):
        self.extension_update_data.update(self.to_dict())
        await self.update_extension()

    async def do_public(self):
        self.extension_update_data.update(
            {Extension.version.name: self.deploy.previous_version}
        )
        await super().do_public()

    async def do_delete(self):
        self.deploy.cancel_export()
        self.extension_update_data.update({Extension.is_delete.name: True})
        await self.update_extension()
        # 需要修改deploy, 以备再次发布; 需要让用户能再次修改配置吗?
        await self.update_deploy()

    async def do_import(self):
        # TODO 是否应该将 导入/拒绝导入 之类也放到这儿来
        # 会使结构更清晰吗

        ...

    async def do_push(self):  # 推送导入请求
        ...

    async def do_refuse(self):  # 拒绝导入
        ...

    def to_dict(self):
        return {
            Extension.app_uuid.name: self.app_uuid,
            Extension.extension_uuid.name: self.extension_uuid,
            Extension.extension_name.name: self.extension_name,
            Extension.business.name: self.business,
            Extension.cover.name: self.cover,
            Extension.preview.name: self.preview,
            Extension.introduction.name: self.introduction,
        }

    #####################

    # async def create_extension_module(self, app_uuid, user_uuid):
    #     new_module_info = await engine.service.query.create_module(
    #         app_uuid, self.extension_name, user_uuid, json_return=False,
    #         extend_from=self.extension_uuid, extension_type=ReleaseType.TEMPLATE)
    #     if not isinstance(new_module_info, dict):
    #         # 不是dict类型, 就是报错了, 返回报错信息
    #         raise Exception(new_module_info)
    #     return new_module_info

    # async def process_import(self, app_uuid, user_uuid):
    #     new_module_info = await self.create_extension_module(app_uuid, user_uuid)
    #     uuid_template_to_app.update({
    #         module_uuid: new_module_info.get("module_uuid")
    #     })

    # async def process_record(self):
    #     ...

    # async def check_document(self):
    #     ...


IMPORT_AGENT_DICT = dict()


def clear_timeout_helper(helper_id: str = None):
    if helper_id in IMPORT_AGENT_DICT:
        return IMPORT_AGENT_DICT.pop(helper_id, None)
    for hid in list(IMPORT_AGENT_DICT.keys()):
        item = IMPORT_AGENT_DICT.get(hid, None)
        if item and time.time() - item.start_timestamp > 600:
            IMPORT_AGENT_DICT.pop(hid)


class ImportHelper:

    design_oss = DesignOss()

    def __init__(
        self,
        app_uuid,
        middle_user,
        user_uuid,
        extension: dict,
        record,
        module_name=None,
        publisher_name=None,
    ) -> None:
        self.app_uuid = app_uuid
        self.middle_user = middle_user
        self.user_uuid = user_uuid
        self.publisher_name = publisher_name
        self.module_uuid = gen_lemon_uuid_by_pin_uuid(
            extension.get("extension_uuid"), app_uuid
        )
        self.extension = extension
        self.document_infos = list(extension.get("extension_info", dict()).values())
        self.record = record
        self.uuid_template_to_app = None
        self.extra_reg = None
        self.module_name = (
            module_name if module_name else extension.get("extension_name")
        )
        self.module_exist = None
        self.start_timestamp = int(time.time())
        self.status = ImportStatus.DOING
        self.step = 0
        self.return_code = IDECode.OK

    @property
    def state(self):
        return LDR(return_code=self.return_code, data={"step": self.step, "status": self.status})

    async def gen_uuid_template_to_app(self):
        uuid_template_to_app = {
            self.extension.get("app_uuid"): self.app_uuid,
            self.extension.get("extension_uuid"): self.module_uuid,
        }
        if self.module_name != self.extension.get("extension_name"):
            uuid_template_to_app.update(
                {
                    f"lemon.{self.extension.get('extension_name')}.": f"lemon.{self.module_name}."
                }
            )
        uuid_template_to_app = await gen_lemon_fixed_uuids(uuid_template_to_app)
        return uuid_template_to_app

    def gen_extra_reg(self):
        extra_reg = {"search": {}, "match": {}}
        if self.module_name != self.extension.get("extension_name"):
            extra_reg["search"].update(
                {
                    self.extension.get("extension_name"): {
                        "reg": rf"lemon\.{self.extension.get('extension_name')}\.",
                        "old": self.extension.get("extension_name"),
                        "new": f"lemon.{self.module_name}.",
                    }
                }
            )
        return extra_reg

    def sort_document(self):
        """
        有一些大致的顺序需要满足, 避免单个文档检查时有依赖报错, 检查无法继续进行
        """
        doc_check_order = [
            DocumentType.CONST,
            DocumentType.JSON,
            DocumentType.IMAGE,
            DocumentType.ENUM,
            DocumentType.FUNC,
            DocumentType.MODEL,
            DocumentType.SECURITY,
            DocumentType.MODULE_DEPLOY,
            DocumentType.PAGE,
        ]
        self.document_infos.sort(
            key=lambda x: (
                doc_check_order.index(x.get("document", dict()).get("document_type"))
                if x.get("document", dict()).get("document_type") in doc_check_order
                else 100
            )
        )

    async def check_module_exist(self):
        exist_module = await engine.access.get_module_by_app_module_uuid(
            self.app_uuid, self.module_uuid, need_delete=True
        )
        if exist_module and not exist_module.is_delete:
            self.return_code = IDECode.TEMPLATE_MODULE_ALREADY_EXISTS
            self.status = ImportStatus.FAILED
            return json(self.state)
        self.module_exist = exist_module is not None
        check_result_invalid = await engine.service.query.check_create_module(
            self.app_uuid, self.module_name
        )
        if check_result_invalid:
            self.return_code = check_result_invalid
            self.status = ImportStatus.FAILED
            return json(self.state)

    async def create_module(self):
        module_dict = self.gen_module_dict()
        if not self.module_exist:
            await engine.access.create_module(self.user_uuid, **module_dict)
        else:
            await engine.access.update_module_by_module_uuid(
                need_delete=True, **module_dict
            )
            await self.process_app_user_role()

    async def process_app_user_role(self):
        """
        应用角色可能已经选择过模块角色, 这儿更新一下应用角色中的模块角色选择
        """
        user_role_list = await engine.access.list_user_role_by_app_uuid(
            app_uuid=self.app_uuid, fields=(UserRole.role_uuid,)
        )
        user_role_list = list(user_role_list)
        user_role_list_without_key = [
            user_role["role_uuid"] for user_role in user_role_list
        ]

        role_content_list = (
            await engine.access.list_user_role_content_by_user_role_list(
                user_role_list_without_key, need_delete=True
            )
        )
        module_role_dict = dict()
        for role_content in role_content_list:
            module_role_dict.update(
                {role_content["user_role"]: role_content["module_role"]}
            )

        await engine.access.add_module_relationship_on_user_role_content(
            self.app_uuid, self.module_uuid, user_role_list, module_role_dict
        )

    def make_self_relationship_uuid(self, content, self_relationship_uuids: dict):
        c = content.get("document_content", dict())
        relationships = c.get("relationships", list())
        for r in relationships:
            relationship_uuid = r.get("uuid")
            source_uuid = replace_relationship_uuid(
                r, RelationshipBasic, from_source=False, replace=False
            )
            target_uuid = replace_relationship_uuid(r, RelationshipBasic, replace=False)
            if source_uuid and target_uuid:
                self_relationship_uuids.update(
                    {relationship_uuid: [source_uuid, target_uuid]}
                )

    # 系统字段（SystemField.RELATIONSHIP_FIELD）
    def gen_lemon_system_relationships(self, content):
        system_relationships = content.get("document_content", {}).get("system_relationships", [])
        for relationship in system_relationships:
            old_model_uuid = relationship.get("source_model")
            new_model_uuid = gen_uuid_update_dict(
                relationship.get("source_model"), self.uuid_template_to_app, self.extra_reg, pin_uuid=self.app_uuid)
            new_uuid = new_relationship_uuid(new_model_uuid, relationship.get("frontref"))
            self.uuid_template_to_app.update({relationship.get("uuid"): new_uuid})
            self.uuid_template_to_app.update({old_model_uuid: new_model_uuid})

    def add_uuid_template_to_app(
        self, uuid_template_to_app, old_self_uuids, new_self_uuids
    ):
        for old_r_uuid, old_uuids in old_self_uuids.items():
            new_r_uuid = uuid_template_to_app.get(old_r_uuid)
            new_uuids = new_self_uuids.get(new_r_uuid)
            uuid_template_to_app.update(
                {old_uuids[0]: new_uuids[0], old_uuids[1]: new_uuids[1]}
            )

    def replace_uuid(self):
        for info in self.document_infos:
            document = info.get("document", dict())
            content = info.get("content", dict())
            if document:
                replace_uuid_in_dict(
                    document,
                    self.uuid_template_to_app,
                    self.extra_reg,
                    pin_uuid=self.app_uuid,
                )
                document["document_version"] = 0
            if content:
                old_self_uuids, new_self_uuids = dict(), dict()
                if document.get("document_type") == DocumentType.MODEL:
                    self.make_self_relationship_uuid(content, old_self_uuids)
                    self.gen_lemon_system_relationships(content)
                replace_uuid_in_dict(
                    content,
                    self.uuid_template_to_app,
                    self.extra_reg,
                    pin_uuid=self.app_uuid,
                )
                if document.get("document_type") == DocumentType.MODEL:
                    self.make_self_relationship_uuid(content, new_self_uuids)
                    self.add_uuid_template_to_app(
                        self.uuid_template_to_app, old_self_uuids, new_self_uuids
                    )

    async def create_policy(self, document_uuid):
        await engine.access.create_policy(
            group_id=self.middle_user,
            resource_id=document_uuid,
            action=Action.DELETE,  # 此处需要用用户身份权限来确认
            app_uuid=self.app_uuid,
            resource_type=ResourceType.DOCUMENT,
        )

    async def copy_oss_resource(self, document_type, content):
        # 导入模块时将各类oss资源复制到对应目录
        try:
            if document_type == DocumentType.IMAGE:
                await self.copy_oss_image(content)
            elif document_type == DocumentType.EXPORT_TEMPLATE:
                await self.copy_oss_export_template(content)
        except Extension:
            print_exc()

    async def copy_oss_export_template(self, content):
        data = content.get("data", [])
        for d in data:
            new_url = d.get("template_url")
            await self.copy_oss_object(new_url)

    async def copy_oss_image(self, content):
        image_list = content.get("image_list", [])
        for image in image_list:
            new_url = image.get("url", "")
            await self.copy_oss_object(new_url)

    async def copy_oss_object(self, new_url):
        old_url = update_image_url(
            new_url, self.old_uuid_template_to_app, {}, pin_uuid=self.app_uuid
        )
        await self.design_oss.copy_object(old_url, new_url)

    def update_template_deploy(self, content: dict):
        content.update(
            {
                "on_shelf": False,
                "show_name": self.module_name,
                "previous_version": "",
                "current_version": "1.0",
                "publisher_name": self.publisher_name,
                "imported": True,
            }
        )

    def update_workflow_status(self, content):
        for version in content.get("versions", []):
            if version.get("uuid") == content.get("online_version"):
                version["status"] = 0

    def gen_document_content(self, document_uuid, content):
        return {
            "app_uuid": self.app_uuid,
            "module_uuid": self.module_uuid,
            "document_uuid": document_uuid,
            "document_version": 1,
            "document_content": content,
        }

    def gen_module_dict(self):
        return dict(
            app_uuid=self.app_uuid,
            module_uuid=self.module_uuid,
            module_name=self.module_name,
            module_type=Module.TYPE.OWN,
            extend_from=self.extension.get("id"),
            extension_type=ReleaseType.TEMPLATE,
            is_delete=False,
        )

    async def process_record(self):
        to_accept = self.record is not None
        if to_accept:
            # 更新record
            await engine.db.objs.execute(
                self.record.update(
                    action=ImportResult.IMPORTED, action_time=int(time.time())
                ).where(self.record._pk_expr())
            )
        else:
            # 新建record, accepted=True
            await engine.access.create_extension_import_record(
                self.app_uuid,
                self.user_uuid,
                self.extension.get("extension_uuid"),
                self.extension.get("id"),
                action=ImportResult.IMPORTED,
                import_type=ImportType.ACTIVE_IMPORT,
                apply_user_uuid=self.user_uuid,
            )

    @timeit_async
    async def check_import_task_done(self, res_id_list):
        async def wait_for_result(res_id):
            return await TaskResult(res_id, engine).done()
        i = 0
        while True:
            # 循环时, 把检查通过的res_id从列表中移除
            app_log.info(f"waiting single check result i : {len(res_id_list)}")
            results = await asyncio.gather(*[wait_for_result(res_id) for res_id in res_id_list])
            finished_res_id_list = [res_id for res_id, result in zip(res_id_list, results) if result]
            res_id_list = [res_id for res_id in res_id_list if res_id not in finished_res_id_list]
            if all(results):
                break
            elif i > 150:
                app_log.warning(f"left res_id: {len(res_id_list)}")
                break
            await asyncio.sleep(1)

    async def check_all(self):
        app_log.info("start check_all")
        all_document = (
            await engine.access.list_document_by_module_uuid_join_check_message(
                self.app_uuid, self.module_uuid, ignore_version=True
            )
        )
        await engine.service.document_check.check_all_document2(
            self.app_uuid, all_document, priority="high", check_commit=True
        )

    @timeit_async
    async def handle_import(self):
        try:
            self.step += 1
            if check_result := await self.check_module_exist():
                return check_result
            self.uuid_template_to_app = await self.gen_uuid_template_to_app()
            self.extra_reg = self.gen_extra_reg()
            self.step += 1
            res_id_list = await self.do_import()
        except Exception as e:
            print_exc()
            self.status = ImportStatus.FAILED
            self.return_code = IDECode.ERROR
            app_log.info("uuid_template_to_app: ")
            map_ = {v: k for k, v in self.uuid_template_to_app.items()}
            pattern = re.compile(r"[a-z0-9]{32}")
            e = str(e)
            uuid = re.findall(pattern, e)
            if uuid and uuid[0] in map_:
                old_uuid = map_.get(uuid[0])
                app_log.info(f"old_uuid: {old_uuid}")
            return json(self.state)
        else:
            await self.process_record()
            await self.check_import_task_done(res_id_list)
            self.step += 1
            await self.check_all()
            self.step += 1
            self.status = ImportStatus.SUCCEED
            return json(LDR())

    async def do_import(self):
        res_id_list = list()
        await self.create_module()
        self.sort_document()
        self.replace_uuid()
        self.old_uuid_template_to_app = {
            value: key for key, value in self.uuid_template_to_app.items()
        }
        exist_document_dict, exist_content_dict = await self.check_document_exist()
        document_id_dict = dict()
        for info in self.document_infos:
            document = info.get("document", dict())
            old_id = document.pop("id", None)
            document_uuid = document.get("document_uuid")
            path = document.get("document_path")

            if document:
                document_exist = exist_document_dict.get(document.get("document_uuid"))
                content_exist = exist_content_dict.get(document.get("document_uuid"))
                new_id = await self.process_document(
                    document, document_exist, content_exist
                )
                self.process_document_id(old_id, new_id, document_id_dict, path)
                await self.create_policy(document_uuid)

            content = info.get("content", dict())
            content = content.get("document_content", dict())
            document_type = document.get("document_type")
            if content:
                await self.copy_oss_resource(document_type, content)
                await self.process_content(document_type, content)
                res_id = await self.check_document(document_type, document_uuid, content)
                if res_id:
                    res_id_list.append(res_id)
        for _, path_info in document_id_dict.items():
            await self.process_document_path(path_info, document_id_dict)
        return res_id_list

    async def check_document(self, document_type, document_uuid, content):
        document_check_service = engine.service.document_check.create_service(
            document_type
        )
        res_id = None
        if document_check_service:
            json_data = self.gen_document_content(document_uuid, content)
            res_id = await document_check_service.check(
                json_data, document_type, json_return=False, is_import=True
            )
        return res_id

    async def process_document(self, document, document_exist, content_exist):
        if document_exist:
            document.update(
                {
                    "is_delete": False,
                    "document_version": 1,  # version=1, 认为document_content存在, 进行更新
                }
            )
            document_uuid = document.pop("document_uuid")
            await engine.access.update_document_by_document_uuid(
                document_uuid, need_delete=True, **document
            )
            if not content_exist:
                await engine.access.create_document_content(document_uuid=document_uuid)
            new_id = document_exist.get("id")
        else:
            new_document = await engine.access.create_obj(
                DocumentModel, create_timestamp=int(time.time()), **document
            )
            new_id = new_document.id
        return new_id

    async def process_content(self, document_type, content):
        if document_type == Document.TYPE.MODULE_DEPLOY:
            self.update_template_deploy(content)
        elif document_type == Document.TYPE.WORKFLOW:
            self.update_workflow_status(content)

    def process_document_id(self, old_id, new_id, document_id_dict: dict, path):
        if old_id and new_id:
            document_id_dict.update({old_id: {"new_id": new_id, "path": path}})

    async def process_document_path(self, path_info, document_id_dict: dict):
        new_id = path_info.get("new_id")
        old_document_path = path_info.get("path")
        new_document_path = "/"
        for id in old_document_path.split("/"):
            if id:
                new_document_path += str(document_id_dict.get(int(id)).get("new_id"))
                new_document_path += "/"
        query = DocumentModel.update(document_path=new_document_path).where(
            DocumentModel.id == new_id
        )
        await engine.access.update_obj_by_query(DocumentModel, query, need_delete=True)

    async def check_document_exist(self):
        exist_document_dict, exist_content_dict = dict(), dict()
        document_uuids = [
            info.get("document", dict()).get("document_uuid")
            for info in self.document_infos
        ]
        documents = await engine.access.list_document_by_document_id_list(
            document_uuid_list=document_uuids, need_delete=True
        )
        contents = await engine.access.list_document_content_by_module_uuid(
            self.module_uuid,
            fields=(DocumentContent.id, DocumentContent.document_uuid),
            need_delete=True,
        )
        for d in documents:
            exist_document_dict.update({d.get("document_uuid"): d})
        for c in contents:
            exist_content_dict.update({c.get("document_uuid"): c})
        return exist_document_dict, exist_content_dict
