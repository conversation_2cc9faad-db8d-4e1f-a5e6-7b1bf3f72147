# -*- coding:utf-8 -*-

import sys
import abc
import copy
import asyncio
from functools import partial
from typing import Any, Dict
import weakref

import ujson
from transitions.extensions.asyncio import HierarchicalAsyncMachine as Machine
from peewee import Field, Model
from aiohttp import ClientSession

from baseutils.log import app_log
from baseutils.const import Code
from apps.entity import DocumentContent
from apps.utils import <PERSON><PERSON><PERSON>r<PERSON>inder, PrintDatalistFinder, PrintFinder, lemon_uuid
from apps.ide_const import ComponentType, DataSourceType, FieldType, ImageSourceType, PrintType, StateType, ValueEditorType
from apps.base_entity import ModelEventType, ModelEventTopic
from runtime.core.func_api import <PERSON><PERSON>unt<PERSON><PERSON>out<PERSON>, LemonRuntimeContext, LemonViewAPI, RuntimeDocumentsAPI, \
    LemonDevicesAPI, LemonAuthAPI

from runtime.engine import engine
from runtime.utils import lemon_where, build_join, LemonMessage
from runtime.utils import init_list_in_page, LemonDictResponsePrint
from runtime.const import LemonStateMachineType, LemonRuntimeErrorCode
from runtime.core.data_preprocess import DataPreprocess
from runtime.core.connector import ClientConnector
from runtime.core.utils import (
    LemonCommand, MessageBox, Console, PopupSubmit, build_topic, lemon_model,
    lemon_field, lemon_model, lemon_model_node, LemonActor
)
from runtime.core.actor import LemonPubsubActor
from runtime.core.variable import Variable, VariableProxy
from runtime.core.base_sm import BaseLemonStateMachine
from runtime.core.base_sm import BaseCommondHandler
from runtime.core.func_run import FuncRun
from runtime.core.func import LemonSMValueEditor
from runtime.core.value_editor import BaseValueEditor, LemonValueEditor
from runtime.component.utils import public_data_format
from tests.utils import UUID  # TODO: 引用tests不太好
from tests.document.sm_root import sm_root
from tests.document.sm_page import sm_page
from tests.document.sm_excel import sm_excel
from tests.document.sm_form import sm_form
from tests.document.sm_datalist import sm_datalist
from tests.document.sm_calendar import sm_calendar
from tests.document.sm_chart import sm_chart
from tests.document.rc_scan import rc_scan
from tests.document.rc_timeline import rc_timeline
from tests.document.sm_react import sm_react
from tests.document.sm_tree import sm_tree
from tests.document.sm_treelist import sm_treelist


class StoreType(object):

    NORMAL = 0
    DICT = 1
    LIST = 2
    FIELD = 3
    MODEL = 4


class ComponentCommandHandler(BaseCommondHandler):

    async def handle_get_variables(self, msg):
        data = msg.data
        sid = msg.sid
        rid = msg.rid
        app_log.info(f"data: {str(data)[:150]}")
        variable_dict = LemonMessage()
        variables = data.get("variables")
        lsm = self.lsm
        if lsm.current_state.type == StateType.CHILD:
            lsm = lsm.current_state.lsm
        for v_uuid in variables:
            variable = lsm.global_variable_uuid_dict.get(v_uuid)
            if isinstance(variable, Variable):
                variable_dict.update({v_uuid: variable.value})
            elif v_uuid in lsm.value_editor_dict:
                value_editor = lsm.value_editor_dict.get(v_uuid)
                value = value_editor.get_value
                variable_dict.update({v_uuid: value})
        return_key = "return"
        return_topic = build_topic(sid, lsm.machine_id, rid, return_key)
        # app_log.info(f"return topic: {return_topic}, variable_dict: {variable_dict}")
        await lsm.connector.call(variable_dict, topic=return_topic)


class PageCommandHandler(ComponentCommandHandler):

    def __init__(self, lsm):
        self.lsm: PageLemonStateMachine = lsm
        self.print_data_source = self.lsm.component.print_data_source

    def clean(self):
        super().clean()
        self.print_data_source = dict()
        self.filter_data_dict = dict()


class ExcelCommandHandler(ComponentCommandHandler):

    pass


class BaseDatalistCommandHandler(ComponentCommandHandler):

    def __init__(self, lsm):
        self.lsm: DatalistStateMachine = lsm
        self.forward_data_list = list()
        self.forward_count = 0

    def clean(self):
        super().clean()
        self.forward_data_list = list()


class TreeCommandHandler(ComponentCommandHandler):

    def set_list_in_page(self):
        single_floor_data = self.lsm.global_variable_name_dict.get(
            "single_floor_data")
        data = single_floor_data.value
        list_in_page_variable = self.lsm.global_variable_name_dict.get(
            "list_in_page")
        list_in_page_value = list_in_page_variable.value
        if isinstance(list_in_page_value, dict) and list_in_page_value:
            list_in_page_value.update({
                "data": data
            })
        list_in_page_variable.value = list_in_page_value


class TreelistCommandHandler(ComponentCommandHandler):

    pass


class FormCommandHandler(ComponentCommandHandler):

    def __init__(self, lsm):
        self.lsm: FormStateMachine = lsm
        self.forward_control_data_dict = dict()
        self.forward_children_data_dict = dict()

    def clean(self):
        super().clean()
        self.forward_control_data_dict = dict()
        self.forward_children_data_dict = dict()

    async def _send_form_event_view(self):
        event_uuid = UUID.sm_form.event_view
        event_topic = self.lsm.build_event_topic(event_uuid)
        event_msg = LemonMessage(data={"event_args": {}}, topic=event_topic)
        app_log.info(f"publish form event_view: event_topic {event_topic}")
        asyncio.create_task(self.lsm.connector.call(event_msg))

    async def handle_action_model_update(self, msg):
        # 获取状态机变量
        current_state = self.lsm.current_state
        state_name = current_state.name
        app_log.info(f"on action update, state_name: {state_name}")
        if state_name == "state_view":
            # 暂时只考虑表单为只读状态时的更新
            # TODO: 判断一下 pk 是否为当前行的 pk
            # TODO: 这里还需要定时，比如两秒钟再同步
            await self._send_form_event_view()


class DatalistCommandHandler(BaseDatalistCommandHandler):

    async def _send_datalist_sub_data(self, main_pk_list):
        event_uuid = UUID.sm_datalist.event_sub_data
        event_topic = self.lsm.build_event_topic(event_uuid)
        for main_pk in main_pk_list:
            event_msg = LemonMessage(
                data={"event_args": {"main_pk": main_pk}}, topic=event_topic)
            app_log.info(
                f"publish datalist sub_data: event_topic {event_topic}")
            asyncio.create_task(self.lsm.connector.call(event_msg))

    async def handle_action_model_update(self, msg):
        # 获取状态机变量
        current_state = self.lsm.current_state
        data_model_variable = current_state.variable_name_dict.get(
            "data_model")
        data_columns_format_variable = current_state.variable_name_dict.get(
            "data_columns_format")
        data_columns_agg_variable = current_state.variable_name_dict.get(
            "data_columns_agg")
        data_columns_variable = current_state.variable_name_dict.get(
            "data_columns")
        sub_model_variable = current_state.variable_name_dict.get("sub_model")
        sub_columns_variable = current_state.variable_name_dict.get(
            "sub_columns")
        pk_in_page_variable = self.lsm.global_variable_name_dict.get(
            "pk_in_page")
        agg_in_page_variable = self.lsm.global_variable_name_dict.get(
            "agg_in_page")
        list_in_page_variable = self.lsm.global_variable_name_dict.get(
            "list_in_page")
        source_in_page_variable = self.lsm.global_variable_name_dict.get(
            "source_in_page")
        sub_in_page_variable = self.lsm.global_variable_name_dict.get(
            "sub_in_page")
        sub_with_maintable = current_state.variable_name_dict.get(
            "sub_with_maintable")

        data_model = data_model_variable.value
        data_columns_format = data_columns_format_variable.value
        data_columns_agg = data_columns_agg_variable.value
        data_columns = data_columns_variable.value
        sub_model = sub_model_variable.value
        sub_columns = sub_columns_variable.value
        pk_in_page = pk_in_page_variable.value
        agg_in_page = agg_in_page_variable.value
        list_in_page = list_in_page_variable.value
        source_in_page = source_in_page_variable.value
        sub_in_page = sub_in_page_variable.value
        sub_with_maintable = sub_with_maintable.value
        data_model_uuid = data_model._meta.table_name
        sub_model_uuid = "" if sub_model is None else sub_model._meta.table_name

        topic = msg.topic
        action = msg.action
        data_list = msg.data
        app_log.info(f"topic: {topic}")
        app_log.info(f"action: {action}")
        app_log.info(f"data: {data_list}")
        model_uuid = topic.split("-")[-1]
        # TODO: 可以直接拿到 组件上的 func
        datalist_data_format_func_uuid = ""
        datalist_data_agg_func_uuid = ""

        list_change = False

        # 新建 的数据，没办法更新，因为排序原因，pk不连续
        # 没办法确定新建数据的pk，是不是应该在当前页

        # TODO: 数据列表的 inited 需要控制，暂定两秒钟更新一次
        # 增加一个队列，如果需要 inited 数据列表
        # 则判断队列是否为空，为空时，则向队列增加一条数据
        # 写一个 asyncio.wait 函数，从队列中取数据
        # 取到数据后，比对上次 inited 的时间，进行 asyncio.sleep

        if action == ModelEventType.INSERT:
            # TODO: 子表的新增，没更新到
            # insert 没办法知道当前页需不需要这个 pk ；所以直接 inited 数据列表
            app_log.info(f"insert actioin, do inited")
            await self.lsm.component.datalist_inited()
            return None

        if action in [ModelEventType.UPDATE, ModelEventType.DELETE]:

            if model_uuid == sub_model_uuid:
                # 子表的数据推送
                if sub_with_maintable:
                    # 跟随主表数据发送的，直接刷新页面
                    await self.lsm.component.datalist_inited()
                    return None
                else:
                    # 不跟随主表数据发送的，判断子表的 pk 分别在主表的 main_pk
                    # 刷新这些 main_pk 对应的子表数据
                    main_pk_set, sub_pk_dict = set(), dict()
                    for main_pk, sub_line in sub_in_page.items():
                        if isinstance(sub_line, list) and len(sub_line) >= 1:
                            sub_pk = sub_line[0]
                            sub_pk_dict.setdefault(sub_pk, list())
                            sub_pk_dict[sub_pk].append(main_pk)
                    for data_dict in data_list:
                        sub_pk = data_dict.pop("pk")
                        main_pk_list = sub_pk_dict.get(sub_pk, list())
                        main_pk_set.update(set(main_pk_list))
                    await self._send_datalist_sub_data(list(main_pk_set))
                    return None

            format_func_wrapper = await FuncRun.get_func(datalist_data_format_func_uuid)
            agg_func_wrapper = await FuncRun.get_func(datalist_data_agg_func_uuid)
            model = engine.uuids.model_uuid_map.get(model_uuid)
            model_columns = model._meta.columns
            model_pk_dict = pk_in_page.get(model_uuid, dict())
            column_index_dict = dict()
            for index, column in enumerate(data_columns):
                column_index_dict.setdefault(column.column_name, list())
                column_index_dict[column.column_name].append(index)
            app_log.info(f"column_index_dict: {column_index_dict}")
            column_data_dict = dict()
            for s_data in source_in_page:
                for index, column in enumerate(data_columns[1:], start=1):
                    column_data = s_data[index]
                    column_data_dict.setdefault(index, list())
                    column_data_dict[index].append(column_data)
            for data_dict in data_list:
                pk = data_dict.pop("pk")
                pk_index_list = model_pk_dict.get(pk, list())
                if not pk_index_list:
                    continue
                for pk_index in pk_index_list:
                    for name, source_data in data_dict.items():
                        column = model_columns.get(name)
                        app_log.info(f"column: {column}")
                        column_name = column.column_name
                        column_index_list = column_index_dict.get(
                            column_name, list())
                        app_log.info(f"column_index_list: {column_index_list}")
                        for column_index in column_index_list:
                            column_format = data_columns_format[column_index]
                            column_agg = data_columns_agg[column_index]
                            if model_uuid != data_model_uuid and column_name == "id":
                                continue
                            if model_uuid == data_model_uuid:
                                column_data_dict[column_index][pk_index] = source_data
                                format_arg_dict = {
                                    "source_data": source_data, "column": column,
                                    "column_format": column_format
                                }
                                result = await FuncRun.run(format_func_wrapper, lsm=self.lsm, **format_arg_dict)
                                result_data = result.get("data", source_data)
                                list_in_page[pk_index][column_index] = result_data
                            else:
                                relation_column_data = list_in_page[pk_index][column_index]
                                relation_value_list = relation_column_data.get(
                                    "value", [])
                                relation_data_list = relation_column_data.get(
                                    "data", [])
                                source_data_list = list()
                                for r_data in relation_data_list:
                                    r_pk, r_d = r_data.get(
                                        "id"), r_data.get("data")
                                    if r_pk == pk:
                                        r_data["data"] = source_data
                                        source_data_list.append(source_data)
                                    else:
                                        source_data_list.append(r_d)
                                column_data_dict[column_index][pk_index] = source_data_list
                                format_arg_dict = {
                                    "source_data": source_data_list, "column": column,
                                    "column_format": column_format, "relation_column": True
                                }
                                format_result = await FuncRun.run(format_func_wrapper, lsm=self.lsm, **format_arg_dict)
                                format_data = format_result.get(
                                    "data", source_data)
                                list_in_page[pk_index][column_index] = {
                                    "id": 1, "name": format_data,
                                    "value": relation_value_list,
                                    "data": relation_data_list
                                }
                            column_data_list = column_data_dict.get(
                                column_index)
                            app_log.info(
                                f"agg column_data_list: {column_data_list}")
                            agg_arg_dict = {
                                "source_data": column_data_list, "agg": column_agg}
                            agg_result = await FuncRun.run(agg_func_wrapper, lsm=self.lsm, **agg_arg_dict)
                            # agg_in_page 由于没有 主模型的id字段，所以索引需要小1
                            agg_in_page[column_index -
                                        1] = agg_result.get("data", "")
                            list_change = True
            if list_change:
                list_in_page_variable.value = list_in_page
                agg_in_page_variable.value = agg_in_page


class CalendarCommandHandler(ComponentCommandHandler):

    def __init__(self, lsm):
        self.lsm: CalendarStateMachine = lsm


class SubTableCommandHandler(BaseDatalistCommandHandler):

    def __init__(self, lsm):
        super().__init__(lsm)
        self.lsm: SubTableLemonStateMachine = lsm


class RTableCommandHandler(BaseDatalistCommandHandler):

    def __init__(self, lsm):
        super().__init__(lsm)
        self.lsm: RTableLemonStateMachine = lsm


class RSelectCommandHandler(BaseDatalistCommandHandler):

    def __init__(self, lsm):
        super().__init__(lsm)
        self.lsm: RSelectLemonStateMachine = lsm

    async def handle_action_model_update(self, msg):
        app_log.info(f"model_update: {msg}")


class TransferCommandHandler(BaseDatalistCommandHandler):

    def __init__(self, lsm):
        super().__init__(lsm)
        self.lsm: TransferLemonStateMachine = lsm

    async def handle_action_model_update(self, msg):
        app_log.info(f"model_update: {msg}")


class RSelectPopupCommandHandler(RSelectCommandHandler):

    def __init__(self, lsm):
        super().__init__(lsm)
        self.lsm: RSelectPopupLemonStateMachine = lsm


class ChartCommandHandler(ComponentCommandHandler):

    async def _send_form_event_view(self):
        event_uuid = UUID.sm_chart.event_inited
        event_topic = self.lsm.build_event_topic(event_uuid)
        event_msg = LemonMessage(data={"event_args": {}}, topic=event_topic)
        app_log.info(f"publish form event_view: event_topic {event_topic}")
        asyncio.create_task(self.lsm.connector.call(event_msg))

    async def handle_action_model_update(self, msg):
        # 获取状态机变量
        current_state = self.lsm.current_state
        state_name = current_state.name
        app_log.info(f"on action update, state_name: {state_name}")
        if state_name == "state_chart":
            # 暂时只考虑表单为只读状态时的更新
            # TODO: 判断一下 pk 是否为当前行的 pk
            # TODO: 这里还需要定时，比如两秒钟再同步
            await self._send_form_event_view()


class LemonStateMachine(BaseLemonStateMachine): pass


class BaseComponentLemonStateMachine(BaseLemonStateMachine):

    """
    # 注意： 在内嵌容器 或 组件的情况下， machine_id 和 component_dict 的 uuid 是不一样的
    """
    lsm_type = LemonStateMachineType.COMPONENT
    run_condition = False  # 组件状态机 不执行触发器的条件

    def __init__(self, component, nested=False, subscribe_models=None, **kwargs):
        self._component = None
        self.component = component
        connector = component.connector
        machine_id = component.machine_id
        p_context = component.p_context
        root_machine_id = component.root_machine_id
        parent_machine_id = component.parent_machine_id
        parent = component.parent
        parent_lsm = parent.lsm if parent else None
        self.page_machine_id = getattr(component, "page_machine_id", None)
        self._connector = None
        self.connector: ClientConnector = connector
        self.sid = connector.sid
        self.nested = nested
        self.forward = True
        self.subscribe_models = list() if subscribe_models is None else subscribe_models
        super().__init__(
            machine_id=machine_id, root_machine_id=root_machine_id,
            parent_machine_id=parent_machine_id, parent=parent_lsm,
            p_context=p_context, **kwargs)

    def initialize(self, init_start=True, kwargs=None):
        # self._context_dict = {"pk": self.p_context.get("pk")}
        # self._context = VariableProxy(self._context_dict)
        self.command_handler = ComponentCommandHandler(self)
        self.value_editor_dict = dict()
        self.build_topic_func = partial(build_topic, self.sid)
        self.lemon_actor = LemonActor(self.sid)
        self.lemon.utils.lemon_actor = self.lemon_actor
        self.lemon.utils.message_box = MessageBox(self.sid, self.machine_id)
        self.lemon.utils.console = Console(self.sid, self.machine_id)
        self.lemon.router = LemonRuntimeRouter(self.lemon, self.sid, self.machine_id)
        self.lemon.devices = LemonDevicesAPI(self.lemon, self.sid, self.machine_id)
        self.lemon.view = LemonViewAPI(self.lemon, self.sid, self.machine_id)
        self.lemon.ctx = LemonRuntimeContext(self.lemon, self.component, self.sid, self.machine_id)
        self.lemon.documents = RuntimeDocumentsAPI(self.lemon)
        self.lemon.auth = LemonAuthAPI(self.lemon, self.sid, self.machine_id)
        self.lemon.utils.popup_submit = PopupSubmit(self.sid, self.machine_id)
        super().initialize(init_start, kwargs)

    @property
    def component(self):
        if self._component is None:
            return None
        return self._component()

    @component.setter
    def component(self, value):
        self._component = None if value is None else weakref.ref(value)

    @property
    def connector(self) -> ClientConnector:
        return None if self._connector is None else self._connector()

    @connector.setter
    def connector(self, value: ClientConnector):
        self._connector = None if value is None else weakref.ref(value)

    def create_model_actor(self):
        topics = list()
        for model_uuid in self.subscribe_models:
            topic = ModelEventTopic(model_uuid=model_uuid)
            topic = topic.to_str()
            # topic = "-".join([engine.config.REDIS_CHANNEL_NOTIFY_FRONT, model_uuid])
            topics.append(topic)
        if topics:
            # return LemonPubsubActor(*topics, new_pubsub=True)
            return LemonPubsubActor(*topics)
        return None

    async def async_start_machine(self):
        self.connector.add_callback_result(self.result_topic)
        await super().async_start_machine()

    def get_value_editor_value(self, value_editor, value_editor_data_dict):
        if value_editor.is_monitor:
            if value_editor.once:
                value_editor_data_dict.update(
                    {value_editor.uuid: value_editor.get_value})
                if value_editor.type == ValueEditorType.EXPR:
                    if not value_editor._once_send:
                        value_editor._once_send = True
            else:
                value_editor_data_dict.update(
                    {value_editor.uuid: value_editor.get_value})

    def get_value_editor_data(self, value_editor_dict=None):
        if value_editor_dict is None:
            value_editor_dict = self.value_editor_dict
        value_editor_data_dict = self.value_editor_dict
        return value_editor_data_dict

    def update_value_editor(self, value_editor, control_uuid=None):
        value = value_editor.get_value
        uuid = value_editor.uuid
        if control_uuid is None:
            self.value_editor_dict[uuid] = value
        elif isinstance(control_uuid, list):
            if not control_uuid:
                return
            control_value_editor_dict = self.value_editor_dict
            for c_uuid in control_uuid[:-1]:
                control_value_editor_dict = control_value_editor_dict.setdefault(
                    c_uuid, {})
            control_value_editor_dict.setdefault(control_uuid[-1], {})
            control_value_editor_dict[control_uuid[-1]][uuid] = value
        else:
            control_value_editor_dict = self.value_editor_dict.setdefault(
                control_uuid, {})
            control_value_editor_dict[uuid] = value

    def clean_value_editor(self, value_editor, control_uuid=None):
        uuid = value_editor.uuid
        if control_uuid is None:
            if uuid in self.value_editor_dict:
                del self.value_editor_dict[uuid]
        elif isinstance(control_uuid, list):
            if not control_uuid:
                return
            control_value_editor_dict = self.value_editor_dict
            for c_uuid in control_uuid:
                control_value_editor_dict = control_value_editor_dict.get(
                    c_uuid, {})
            if uuid in control_value_editor_dict:
                del control_value_editor_dict[uuid]
        else:
            control_value_editor_dict = self.value_editor_dict.get(
                control_uuid, {})
            if uuid in control_value_editor_dict:
                del control_value_editor_dict[uuid]

    async def publish_update_value(self, uuid, name, value):
        data = {
            "component": self.machine_id,
            "component_id": None if self.nested else self.component.uuid,
            "component_type": self.component.component_type
        }
        if self.component.parent:
            data.update({
                "parent_component_type": self.component.parent.component_type,
                "parent_component_id": self.component.parent.uuid
            })
        result = LemonMessage(result="component_event",
                              data=data, topic=self.result_topic)
        if isinstance(value, (list, dict)):
            values = [{"uuid": uuid, "value": value}]
            data.update({"variables": [uuid], "values": values})
            await self.connector.send_messages.put(result)
            # app_log.info(f"publish: {name}, topic: {self.result_topic}")
        elif isinstance(value, (int, str)):
            data.update({"variable_update": {uuid: value}})
            await self.connector.send_messages.put(result)
            # app_log.info(f"publish: {name}, topic: {self.result_topic}")

    async def variable_publish(self, variable: Variable):
        # 为什么有个变量叫 watch_variables 呢？
        # 这个全局变量是为了前端页面推送数据变化而设计的
        # 前端页面组件的定义中，有个key 叫 watch_variables
        # 它的值是需要监听变化的所有 状态机变量 的列表
        # 在创建组件状态机时，watch_variables 通过 p_context 传进来
        # 如果当前状态机的定义中有个全局变量的 name 是 watch_variables 的话
        # 会把这个全局变量直接赋值为传进来的 watch_variables 对应的值
        w_variable = self.globals.get("watch_variables", None)
        if isinstance(w_variable, Variable):
            watch_variables = w_variable.value
            # app_log.info(f"watch_variables: {watch_variables}")
            in_watch_variables = variable.uuid in watch_variables
            # app_log.debug(f"variable: {variable.name}, in_watch_variables: {in_watch_variables}")
            if in_watch_variables and self.is_stopping is False:
                await self.publish_update_value(
                    variable.uuid, variable.name, variable.value)

    async def clean(self):
        await super().clean()
        # app_log.info(f"self.component: {self.component}")
        # app_log.info(f"sys.getrefcount(self.component): {sys.getrefcount(self.component)}")
        # self.component = None
        # self.value_editor_dict = dict()
        # if hasattr(self, "model_actor"):
        #     model_actor = getattr(self, "model_actor", None)
        #     if model_actor is not None:
        #         app_log.info(f"m_id: {self.machine_id} stop model_actor.")
        #         await self.model_actor.stop()


class ComponentLemonStateMachine(BaseComponentLemonStateMachine):
    
    pass


class RootLemonStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_root.to_dict()


class PageLemonStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_page.to_dict()

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = PageCommandHandler(self)


class ExcelStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_excel.to_dict()

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = ExcelCommandHandler(self)


class FormStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_form.to_dict()

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = FormCommandHandler(self)
        self.forward = True if self.nested else False


class BaseDatalistStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_datalist.to_dict()


class TreeStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_tree.to_dict()

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = TreeCommandHandler(self)

    async def variable_publish(self, variable: Variable):
        if variable.name == "single_floor_data":
            self.command_handler.set_list_in_page()
        else:
            await super().variable_publish(variable)


class TreelistStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_treelist.to_dict()

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = TreelistCommandHandler(self)


class DatalistStateMachine(BaseDatalistStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = DatalistCommandHandler(self)


class CalendarStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_calendar.to_dict()

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = CalendarCommandHandler(self)
        self.forward = True if self.nested else False


class ChartStateMachine(BaseComponentLemonStateMachine):

    sm_dict = sm_chart.to_dict()

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = ChartCommandHandler(self)


class TimeLineStateMachine(BaseComponentLemonStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = ChartCommandHandler(self)


class RFormLemonStateMachine(FormStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)


class SubTableLemonStateMachine(BaseDatalistStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = SubTableCommandHandler(self)


class RTableLemonStateMachine(BaseDatalistStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = RTableCommandHandler(self)


class SelectRTableLemonStateMachine(BaseDatalistStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = RTableCommandHandler(self)


class DeleteRTableLemonStateMachine(BaseDatalistStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = RTableCommandHandler(self)


class DatagridRTableLemonStateMachine(BaseDatalistStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = RTableCommandHandler(self)


class RelationControlLemonStateMachine(BaseDatalistStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)


class RSelectLemonStateMachine(RelationControlLemonStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = RSelectCommandHandler(self)


class TransferLemonStateMachine(RelationControlLemonStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = TransferCommandHandler(self)


class RSelectPopupLemonStateMachine(RelationControlLemonStateMachine):

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = RSelectPopupCommandHandler(self)


class ReactCommandHandler(ComponentCommandHandler):

    def __init__(self, lsm):
        self.lsm: ReactLemonStateMachine = lsm

    async def handle_deliver(self, msg):
        # return
        await self.lsm.component.handle_recv(msg)

    async def handle_init(self, msg):
        # return
        await self.lsm.component.handle_init(msg)


class ReactLemonStateMachine(BaseComponentLemonStateMachine):
    sm_dict = sm_react.to_dict()

    def __init__(self, component, **kwargs):
        super().__init__(component=component, **kwargs)
        self.command_handler = ReactCommandHandler(self)
