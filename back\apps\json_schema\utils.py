from collections import deque
from typing import Union, Any
from referencing.jsonschema import _dollar_id as id_of
import ast
from collections import namedtuple


class ReferenceData:
    def __init__(
        self,
        ref_type: str,
        ref_uuid: str,
        ref_instance_attr: Union[str, None] = None,
        attr_uuid: str = "",
        title: str = "",
        attr: str = "",
        control_type: Union[str, int, None] = None,
        control_uuid: Union[str, None] = None,
        ref_document: Union[str, None] = None,
        element: Union[dict, None] = None,
        path: tuple = (),
        schema_path: tuple = (),
        parent: Any = None,
        hide: bool = False,
    ) -> None:
        self.ref_type = ref_type
        self.ref_uuid = ref_uuid
        self.uuid = attr_uuid
        self.title = title
        self.attr = attr
        self.control_type = control_type
        self.control_uuid = control_uuid
        self.element = element
        self.path = self.relative_path = deque(path)
        self.schema_path = self.relative_schema_path = deque(schema_path)
        self.parent = parent
        self.ref_document = ref_document
        self.hide = hide

    def to_dict(self):
        return {self.uuid: {
            "uuid": self.uuid,
            "title": self.title,
            "attr": self.attr,
            "control_type": self.control_type,
            "control_uuid": self.control_uuid,
            "link_document": self.ref_document,
            "hide": self.hide,
        }}

    def __repr__(self):
        return f"<{self.__class__.__name__}: {self.uuid!r}, {self.attr}>"

    @property
    def absolute_path(self):
        parent = self.parent
        if parent is None:
            return self.relative_path

        path = deque(self.relative_path)
        path.extendleft(reversed(parent.absolute_path))
        return path

    @property
    def absolute_schema_path(self):
        parent = self.parent
        if parent is None:
            return self.relative_schema_path

        path = deque(self.relative_schema_path)
        path.extendleft(reversed(parent.absolute_schema_path))
        return path

    @property
    def json_path(self):
        path = "$"
        for elem in self.absolute_path:
            if isinstance(elem, int):
                path += "[" + str(elem) + "]"
            else:
                path += "." + elem
        return path


class LemonAttributeVisitor(ast.NodeVisitor):
    def __init__(self):
        # 初始化一个列表来存储找到的变量
        self.found_attributes = []
    
    def visit_Attribute(self, node):
        # 递归检索属性链
        attributes_chain = self._get_attributes_chain(node)
        if attributes_chain and attributes_chain[0] == 'lemon' and len(attributes_chain) > 2:
            # 如果属性链以lemon开头且长度大于2，则添加到结果列表
            self.found_attributes.append('.'.join(attributes_chain))
        # 继续遍历下一个节点
        self.generic_visit(node)

    def _get_attributes_chain(self, node):
        """ 递归检索属性链 """
        if isinstance(node, ast.Attribute):
            inner_chain = self._get_attributes_chain(node.value)
            if inner_chain is not None:
                return inner_chain + [node.attr]
        elif isinstance(node, ast.Name):
            return [node.id]
        return None

    def get_found_attributes(self):
        # 返回去重后的属性列表
        return list(set(self.found_attributes))


if __name__ == "__main__":
    # 解析代码
    code = """



order = lemon.主模块.订单.select().first()
detail: lemon.主模块.订单明细 = order.订单明细
print(list(detail))
"""
    tree = ast.parse(code)

    # 创建访问者实例并遍历抽象语法树
    visitor = LemonAttributeVisitor()
    visitor.visit(tree)

    # 获取所有找到的lemon属性
    found_attributes = visitor.get_found_attributes()

    # 打印或以其他方式处理找到的属性
    print(found_attributes)