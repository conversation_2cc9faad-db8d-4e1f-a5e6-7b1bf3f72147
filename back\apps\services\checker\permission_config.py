from apps.entity import Resource, Tag
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker
from baseutils.const import TagPermissionAction
from apps.ide_const import LemonDesignerErrorCode as LDEC


class TagCheckerService(CheckerService):
    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, document_name: str,
            element: dict, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, *args, **kwargs)

        self.resource_uuid = kwargs.get("resource_uuid")
        self.resource_id = kwargs.get("resource_id")
        self.module_tag_uuid_list = kwargs.get("module_tag_uuid_list", list())

        self.tag_insert_data_list = list()
        self.tag_update_query_list = list()
        self.active_tag_uuid_list = list()

    def build_tag_insert_query_data(self, tag):
        data = dict(
            app_uuid=self.app_uuid,
            module_uuid=self.module_uuid,
            document_uuid=self.document_uuid,
            resource_uuid=self.resource_uuid,
            resource_id=self.resource_id,
            tag_uuid=tag.get("tag_uuid"),
            tag_name=tag.get("tag_name"),
            action=tag.get("action"),
            is_delete=False
            )
        return data

    def build_tag_update_query_data(self, tag):
        data = dict(
            resource_uuid=self.resource_uuid,
            resource_id=self.resource_id,
            tag_name=tag.get("tag_name"),
            action=tag.get("action"),
            is_delete=False
            )
        return data

    def build_tag_update_query(self, tag):
        model = Tag
        data = self.build_tag_update_query_data(tag)
        tag_uuid = tag.get("tag_uuid")
        query = model.update(**data).where(model.tag_uuid == tag_uuid)
        return query

    def check_modify(self, tag):

        tag_uuid = tag.get("tag_uuid")
        self.active_tag_uuid_list.append(tag_uuid)
        if tag_uuid in self.module_tag_uuid_list:
            query = self.build_tag_update_query(tag)
            self.tag_update_query_list.append(query)
        else:
            data = self.build_tag_insert_query_data(tag)
            self.tag_insert_data_list.append(data)

    def check_Tag_action(self, tag):
        tag_name = tag.get("tag_name")
        action = tag.get("action")
        if action and int(action, 2) >= TagPermissionAction.MAX_ACTION:
            return_code = LDEC.PERMISSION_CONFIG_ACTION_NOT_EXISTS
            return_code.message = return_code.message.format(
                element_name=tag_name)
            self._add_error_list("操作配置", return_code)


class ResourceCheckerService(CheckerService):

    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, document_name: str,
            element: dict, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, element, *args, **kwargs)
        self.resource_insert_data_list = list()
        self.resource_update_query_list = list()
        self.active_resource_uuid_list = list()

        self.module_resource_uuid_dict = kwargs.get("module_resource_uuid_dict", dict())
        self.module_tag_uuid_list = kwargs.get("module_tag_uuid_list", list())
        self.module_resource_tag_dict = kwargs.get("module_resource_tag_dict", dict())

    def build_resource_insert_query_data(self, resource):
        data = dict(
            app_uuid=self.app_uuid,
            module_uuid=self.module_uuid,
            document_uuid=self.document_uuid,
            resource_uuid=resource.get("resource_uuid"),
            resource_name=resource.get("resource_name"),
            resource_type=resource.get("resource_type"),
            superior_resource=resource.get("superior_resource"),
            description=resource.get("description"),
            is_delete=False
            )
        return data

    def build_resource_update_query_data(self, resource):
        data = dict(
            resource_name=resource.get("resource_name"),
            resource_type=resource.get("resource_type"),
            superior_resource=resource.get("superior_resource"),
            description=resource.get("description"),
            is_delete=False
            )
        return data

    def build_resource_update_query(self, resource):
        model = Resource
        data = self.build_resource_update_query_data(resource)
        resource_uuid = resource.get("resource_uuid")
        query = model.update(**data).where(model.resource_uuid == resource_uuid)
        return query

    def check_modify(self, resource):
        resource_uuid = resource.get("resource_uuid")
        self.active_resource_uuid_list.append(resource_uuid)
        if resource_uuid in self.module_resource_uuid_dict:
            query = self.build_resource_update_query(resource)
            self.resource_update_query_list.append(query)
        else:
            data = self.build_resource_insert_query_data(resource)
            self.resource_insert_data_list.append(data)

        resource_id = self.module_resource_uuid_dict.get(resource_uuid, dict()).get("id")
        for tag in resource.get("tag", list()):
            tag_checker_service = TagCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, self.document_name,
                self.element, is_copy=self.is_copy, resource_uuid=resource_uuid, resource_id=resource_id,
                module_tag_uuid_list=self.module_tag_uuid_list
                )
            tag_checker_service.check_Tag_action(tag)
            self.update_any_list(tag_checker_service)
            tag_checker_service.check_modify(tag)
            tag_database_update_key = [
                "tag_insert_data_list",
                "tag_update_query_list",
                "active_tag_uuid_list"
            ]
            for key in tag_database_update_key:
                tag_attr = getattr(tag_checker_service, key, [])
                resource_attr = getattr(self, key, [])
                if resource_attr:
                    resource_attr.extend(tag_attr)
                else:
                    setattr(self, key, tag_attr)


class PermissionConfigDocumentCheckerService(DocumentCheckerService):

    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str,
            document_uuid: str, document_name: str,
            element: dict, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, Resource, *args, **kwargs)

        self.resource_insert_data_list = list()
        self.resource_update_query_list = list()
        self.resource_delete_query_list = list()
        self.active_resource_uuid_list = list()
        self.tag_insert_data_list = list()
        self.tag_update_query_list = list()
        self.tag_delete_query_list = list()
        self.active_tag_uuid_list = list()

        self.module_resource_uuid_dict = kwargs.get("module_resource_uuid_dict", dict()).get(self.module_uuid, dict())
        self.module_tag_uuid_list = kwargs.get("module_tag_uuid_dict", dict()).get(self.module_uuid, list())
        self.module_resource_tag_dict = kwargs.get("module_resource_tag_dict", dict())

    def build_resource_delete_query(self, resource_uuid):
        model = Resource
        data = {
            "is_delete": True
        }
        query = model.update(**data).where(model.resource_uuid == resource_uuid)
        return query

    def build_tag_delete_query(self, tag_uuid):
        model = Tag
        data = {
            "is_delete": True
        }
        query = model.update(**data).where(model.tag_uuid == tag_uuid)
        return query

    @checker.run
    def check_permission(self):
        for resource in self.element.get("resource"):
            if "superior_resource" in resource:
                del resource["superior_resource"]
            self.check_resource(resource)

    def check_resource(self, resource):
        resource_checker_service = ResourceCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, self.document_name,
                self.element, is_copy=self.is_copy, module_resource_uuid_dict=self.module_resource_uuid_dict,
                module_tag_uuid_list=self.module_tag_uuid_list,
                module_resource_tag_dict=self.module_resource_tag_dict)
        resource_checker_service.check_modify(resource)
        self.update_any_list(resource_checker_service)

        database_update_key = [
            "resource_insert_data_list",
            "resource_update_query_list",
            "active_resource_uuid_list",
            "tag_insert_data_list",
            "tag_update_query_list",
            "active_tag_uuid_list"
        ]
        for key in database_update_key:
            resource_attr = getattr(resource_checker_service, key, [])
            p_attr = getattr(self, key, [])
            if p_attr:
                p_attr.extend(resource_attr)
            else:
                setattr(self, key, resource_attr)

        # 递归检查所有标签分组
        resource_uuid = resource.get("resource_uuid")
        resource_id = self.module_resource_uuid_dict.get(resource_uuid, dict()).get("id")
        child_resources = resource.get("children", list())
        if child_resources:
            for child_resource in child_resources:
                child_resource.update({"superior_resource": resource_id})
                self.check_resource(child_resource)

    async def commit_modify(self, engine):

        resource_update_funcs = list()
        resource_delete_funcs = list()
        tag_update_funcs = list()
        tag_delete_funcs = list()

        async with engine.db.objs.atomic():
            for u in self.resource_update_query_list:
                func = engine.access.update_obj_by_query(Resource, u, need_delete=True)
                resource_update_funcs.append(func)
                await func

            resource_delete_set = set(self.module_resource_uuid_dict.keys()) - set(self.active_resource_uuid_list)
            for resource_uuid in resource_delete_set:
                query = self.build_resource_delete_query(resource_uuid)
                self.resource_delete_query_list.append(query)
            for d in self.resource_delete_query_list:
                func = engine.access.update_obj_by_query(Resource, d)
                resource_delete_funcs.append(func)
                await func

            for u in self.tag_update_query_list:
                func = engine.access.update_obj_by_query(Tag, u, need_delete=True)
                tag_update_funcs.append(func)
                await func

            tag_delete_set = set(self.module_tag_uuid_list) - set(self.active_tag_uuid_list)
            for tag_uuid in tag_delete_set:
                query = self.build_tag_delete_query(tag_uuid)
                self.tag_delete_query_list.append(query)
            for d in self.tag_delete_query_list:
                func = engine.access.update_obj_by_query(Tag, d, need_delete=True)
                tag_delete_funcs.append(func)
                await func

            if self.resource_insert_data_list:
                await engine.access.insert_many_obj(Resource, self.resource_insert_data_list)
            if self.tag_insert_data_list:
                await engine.access.insert_many_obj(Tag, self.tag_insert_data_list)
