# -*- coding:utf-8 -*-

import copy
import re
# import asyncio
# import traceback

from baseutils.log import app_log
from baseutils.const import CalculateType, CustomComponentType
from apps.base_utils import lemon_uuid, safe_pop
from apps.entity import (
    ModelBasic, Model<PERSON>ield, Page as PageModel, RelationshipBasic
)
from apps.exceptions import (
    CheckNameError, CheckUUIDError, CheckUUIDUniqueError
)
from apps.utils import Page<PERSON><PERSON>, restore_adapter, replace_relationship_uuid
from baseutils.const import ReturnCode, SystemTable, WorkflowType, ResponseMethod

from apps.ide_const import (
    AddMethodType, BaseInput, BaseInputAttr, Button, Calendar, Cardlist, Chart,
    Collapse, DatalistColumnType, DynamicColumnType, DynamicSourceType, Panel, Color, ComponentType, SelectType,
    DataSourceType, DatetimeCycleAttr, ConstantCode,
    EventAction, FieldType, ImageShow, InputAdapter,
    InputAttr, InputType, LemonDesignerErrorCode as LDEC, OperatorType,
    PageOpenType, PreprocessType, PresentCircleProgressAttr,
    PresentFileAttr, PresentImageAttr, PresentProgressAttr, PresentTagAttr,
    PrintButton, PurposeType, RCreateTable, IDECode,
    ScanAttr, ScanCountAttr, TextAttr, TimeLineAttr, Tree, Treelist,
    ValueEditorType, CustomComponent, RestfulAuthorizationType, FileSourceType,
    AddButtonEditType, ButtonSearchType, OpenPageType, ImageSourceType, CardlistType,
    AggregationFunc, AggregationType, ReactAttr, TextStyle, PresentTagStyle, DropdownStyle
)
from apps.ide_const import (
    Input, Textarea, Radio, Checkbox, Select, Datetime, RSelect, RSelectPopup, RTile,
    RSelectTable, Form, Datalist, Datagrid, Grid, GridRow, GridCol, Page,
    Slider, Switch, UploadFile, UploadImage, Tabs, Tab, Container, SplitPage, Split,
    RTree, RCascade, visual, Transfer, DropdownMenu, Menu, RSelectAttr, ReportTableAttr,
    Univer
)
from apps.services import CheckerService, DocumentCheckerService
from apps.services.document.page import (
    Pagination, Page as PageDocument, PageTitleValue, FormCancelButton,
    FormSubmitButton, EventActionData, UNEditableValue, FormVisible
)
from apps.services.checker import checker
from apps.value_editor_utils import LemonBaseValueEditor
from itertools import groupby


class ComponentCheckerService(CheckerService):

    def initialize(self):
        super().initialize()
        self.type = self.element.get("type", ComponentType.GRID)
        self.button_service_dict = {
            ComponentType.NORMAL_BUTTON: ButtonCheckService,
            ComponentType.SEARCH_BUTTON: SearchButtonCheckService,
            ComponentType.NEW_BUTTON: NewButtonCheckService,
            ComponentType.DELETE_BUTTON: DeleteButtonCheckService,
            ComponentType.ADD_BUTTON: AddButtonCheckService,
            ComponentType.REMOVE_BUTTON: RemoveButtonCheckService,
            ComponentType.EDIT_BUTTON: EditButtonCheckService,
            ComponentType.IMPORT_BUTTON: ImportButtonCheckService,
            ComponentType.EXPORT_BUTTON: ExportButtonCheckService,
            ComponentType.SHOW_BUTTON: ShowButtonCheckService,
            ComponentType.CANCEL_BUTTON: ButtonCheckService,
            ComponentType.SUBMIT_BUTTON: ButtonCheckService,
            ComponentType.EXTERNAL_BUTTON: ExternalInputButtonCheckService,
        }
        self.form_button_service_dict = {
            ComponentType.NORMAL_BUTTON: ButtonCheckService,
            ComponentType.CANCEL_BUTTON: ButtonCheckService,
            ComponentType.SUBMIT_BUTTON: ButtonCheckService
        }

    def _check_children(
            self, children, component_uuid_set, component_name_set,
            data_container=False, **kwargs):
        component_dict = {
            ComponentType.GRID: GridCheckerService,
            ComponentType.SPLIT_PAGE: SplitPageCheckerService,
            ComponentType.GRID_ROW: GridRowCheckerService,
            ComponentType.GRID_COL: GridColCheckerService,
            ComponentType.DATALIST: DatalistCheckerService,
            ComponentType.CARDLIST: CardlistCheckerService,
            ComponentType.FORM: FormCheckerService,
            ComponentType.CONTAINER: ContainerCheckerService,
            ComponentType.TABS: TabsCheckerService,
            ComponentType.TAB: TabCheckerService,
            ComponentType.COLLAPSE: CollapseCheckerService,
            ComponentType.PANEL: PanelCheckerService,
            ComponentType.INPUT: InputCheckerService,
            ComponentType.TEXTAREA: TextareaCheckerService,
            ComponentType.RADIO: RadioCheckerService,
            ComponentType.CHECKBOX: CheckboxCheckerService,
            ComponentType.SELECT: SelectCheckerService,
            ComponentType.DATETIME: DatetimeCheckerService,
            ComponentType.CUSTOM_INPUT: CustomInputCheckerService,
            ComponentType.R_SELECT: RSelectCheckerService,
            ComponentType.R_SELECT_POPUP: RSelectPopupCheckerService,
            ComponentType.R_TILE: RTileCheckerService,
            ComponentType.R_SELECT_TABLE: RSelectTableCheckerService,
            ComponentType.R_CREATE_TABLE: RSelectTableCheckerService,
            ComponentType.R_TREE: RTreeCheckerService,
            ComponentType.R_CASCADE: RCascadeCheckerService,
            ComponentType.SLIDER: SliderCheckerService,
            ComponentType.SWITCH: SwitchCheckerService,
            ComponentType.UPLOAD_FILE: UploadFileCheckerService,
            ComponentType.UPLOAD_IMAGE: UploadImageCheckerService,
            ComponentType.COLOR: ColorCheckerService,
            ComponentType.CALENDAR: CalendarCheckerService,
            ComponentType.NORMAL_BUTTON: ButtonCheckService,
            ComponentType.SEARCH_BUTTON: SearchButtonCheckService,
            ComponentType.NEW_BUTTON: NewButtonCheckService,
            ComponentType.DELETE_BUTTON: DeleteButtonCheckService,
            ComponentType.ADD_BUTTON: AddButtonCheckService,
            ComponentType.REMOVE_BUTTON: RemoveButtonCheckService,
            ComponentType.EDIT_BUTTON: EditButtonCheckService,
            ComponentType.IMPORT_BUTTON: ImportButtonCheckService,
            ComponentType.EXPORT_BUTTON: ExportButtonCheckService,
            ComponentType.SHOW_BUTTON: ShowButtonCheckService,
            ComponentType.EXTERNAL_BUTTON: ExternalInputButtonCheckService,
            ComponentType.CANCEL_BUTTON: ButtonCheckService,
            ComponentType.SUBMIT_BUTTON: ButtonCheckService,
            ComponentType.IMAGE: PresentImageCheckService,
            ComponentType.TEXT: TextCheckService,
            ComponentType.TIMELINE: TimeLineCheckService,
            ComponentType.TAG: TagCheckService,
            ComponentType.LINE_BAR: LineBarCheckService,
            ComponentType.RING_BAR: RingBarCheckService,
            ComponentType.FILE: FileCheckService,
            ComponentType.DATETIME_CYCLE: DatetimeCycleCheckService,
            ComponentType.PRINT: PrintCheckerService,
            ComponentType.TREE: TreeCheckerService,
            ComponentType.DATAGRID: DatagridCheckerService,
            ComponentType.SCAN: ScanCheckService,
            ComponentType.REACT: ReactCheckService,
            ComponentType.TREELIST: TreelistCheckerService,
            ComponentType.SCAN_COUNT: ScanCountCheckService,
            ComponentType.CUSTOM_PRESENT: CustomPresentCheckService,

            ComponentType.STACKED_BAR: StackedBarCheckService,
            ComponentType.STACKED_COLUMN: StackedColumnCheckService,
            ComponentType.CLUSTERED_BAR: ClusteredBarCheckService,
            ComponentType.CLUSTERED_COLUMN: ClusteredColumnCheckService,
            ComponentType.PER_STACKED_BAR: PerStackedBarCheckService,
            ComponentType.PER_STACKED_COLUMN: PerStackedColumnCheckService,
            ComponentType.LINE: LineCheckService,
            ComponentType.LINE_STACKED_COLUMN: LineStackColumnCheckService,
            ComponentType.LINE_CLUSTERED_COLUMN: LineClusteredColumnCheckService,
            ComponentType.PIE: PieCheckService,
            ComponentType.DONUT: DonutCheckService,
            ComponentType.TREEMAP: TreelistCheckerService,
            ComponentType.GAUGE: GAUGECheckService,
            ComponentType.CARD: CARDCheckService,
            ComponentType.TABLE: TABLECheckService,
            ComponentType.SUMMARY_TABLE: SummaryTableCheckService,
            ComponentType.ITEM_FILTER: ItemFilterCheckService,
            # ComponentType.RANGE_FILTER: RANGE_FILTERCheckService,
            ComponentType.SEARCH_BAR: SearchBarCheckService,
            ComponentType.CUSTOM_EXTEND: CustomExtendCheckService,
            ComponentType.TRANSFER: TransferCheckerService,
            ComponentType.CUSTOM_REPORT: ReportTableCheckService,
            ComponentType.DROPDOWN_MENU: DropdownMenuCheckerService,
            ComponentType.MENU: MenuCheckerService,
            ComponentType.EXECL_TABLE: ExcelCheckerService
        }
        model_uuid = getattr(self, "model_uuid", None)
        filtered_group = getattr(self, "filtered_group", None)
        parent = {
            "type": self.type, "model_uuid": model_uuid, "filtered_group": filtered_group,
            "element_uuid": self.element_uuid
        } if data_container else {}
        kwargs.update({"parent": parent})

        for child in children:
            if child is None:
                continue
            # 要保证check_children最晚执行
            child_type = child.get("type")
            if not data_container and child_type in ComponentType.ALL_INPUT_CONTROL_LIST:
                attr = Page.ATTR.INPUT_CONTROL
                return_code = LDEC.PAGE_HAS_INPUT_CONTROL
                child = {
                    "element_data": {
                        "name": child.get("name"),
                        "type": child.get("type"),
                        "uuid": child.get("uuid")
                    }}
                self._add_error_list(
                    attr=attr, return_code=return_code, **child)
                continue
            # ComponentType.DATA_VISUALIZE_COMPONENT
            # if child_type in ComponentType.DATA_VISUALIZE_COMPONENT:
            #     component_checker_class = DataVisualCheckerService
            # else:
            component_checker_class = component_dict.get(child_type)
            if component_checker_class is None or not issubclass(component_checker_class, ComponentCheckerService):
                continue
            component_checker_service = component_checker_class(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, child,  **kwargs)
            component_checker_service.component_uuid_set = component_uuid_set
            component_checker_service.document_other_info = self.document_other_info
            try:
                component_checker_service.check_uuid()
                component_checker_service.check_uuid_unique(component_uuid_set)
                component_checker_service.check_all()
                component_checker_service._check_datasource()  # 检查数据源
                # component_checker_service.check_name()
                # component_checker_service.check_name_unique(component_name_set)
                if child_type == ComponentType.GRID_COL:
                    component_checker_service.check_children(
                        component_uuid_set, component_name_set, data_container=data_container)
                elif child_type == ComponentType.GRID_ROW:
                    component_checker_service.check_cols(
                        component_uuid_set, component_name_set, data_container=data_container)
                elif child_type == ComponentType.GRID:
                    component_checker_service.check_rows(
                        component_uuid_set, component_name_set, data_container=data_container)
                elif child_type == ComponentType.TABS:
                    component_checker_service.check_tabs(
                        component_uuid_set, component_name_set, data_container=data_container)
                elif child_type == ComponentType.COLLAPSE:
                    component_checker_service.check_panels(
                        component_uuid_set, component_name_set, data_container=data_container)
                elif child_type == ComponentType.SPLIT_PAGE:
                    component_checker_service.check_split(
                        component_uuid_set, component_name_set, data_container=data_container)
                elif child_type == ComponentType.CONTAINER:
                    component_checker_service.check_children(
                        component_uuid_set, component_name_set, data_container=data_container)
                elif child_type == ComponentType.FORM:
                    is_in_card = component_checker_service.check_form_in_cardlist()
                    if is_in_card is False:
                        component_checker_service.check_children(
                            component_uuid_set, component_name_set, data_container=True)
                elif child_type == ComponentType.CARDLIST:
                    component_checker_service.check_buttons()
                    component_checker_service.check_children(
                        component_uuid_set, component_name_set, data_container=True)
                elif child_type == ComponentType.DATALIST:
                    component_checker_service.check_buttons()
                    component_checker_service.check_subtable()  # 保证最后执行
                elif child_type == ComponentType.DATAGRID:
                    component_checker_service.check_buttons()
                elif child_type == ComponentType.TREELIST:
                    component_checker_service.check_buttons()
                elif child_type == ComponentType.TIMELINE:
                    component_checker_service.check_data_model()
                elif child_type == ComponentType.RING_BAR:
                    component_checker_service.check_field()
                elif child_type == ComponentType.LINE_BAR:
                    component_checker_service.check_field()
                elif child_type == ComponentType.TAG:
                    component_checker_service.check_value_edit()
                # elif child_type in ComponentType.DATA_VISUALIZE_COMPONENT:
                #     component_checker_service.check_filter()
                elif child_type == ComponentType.DROPDOWN_MENU:
                    component_checker_service.check_menus()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(component_checker_service)
                raise e
            else:
                self.update_any_list(component_checker_service)

    def _check_field_type(self, attr):
        field_info = self.element.get("field_info", {})
        # app_log.info(f"field_info: {field_info}")
        self._update_field_placeholder(field_info)
        self.element["field_info"] = field_info
        if field_info:
            field_uuid = field_info.get("uuid")
            if field_uuid:
                field_dict = self.app_field_dict.get(field_uuid, dict())
                if field_dict:
                    field_model_uuid = field_dict.get(
                        ModelField.model_uuid.name)
                    field_type = field_dict.get(ModelField.field_type.name)
                    field_type_list = ComponentType.COMPONENT_TO_FIELD.get(
                        self.type, [])
                    if self.type == ComponentType.SELECT:
                        if self.element.get("data_source", {}).get(
                                "type", DataSourceType.SELECT_FIELD) == DataSourceType.SELECT_FUNC:
                            field_type_list = [FieldType.STRING, FieldType.INTEGER, FieldType.DECIMAL,
                                               FieldType.DATETIME, FieldType.BOOLEAN, FieldType.ENUM]
                    app_log.info(
                        f"model_uuid: {self.model_uuid}, field_model_uuid: {field_model_uuid}")
                    if self.model_uuid:
                        if field_model_uuid == self.model_uuid:
                            if field_type not in field_type_list:
                                return_code = LDEC.INPUT_FIELD_TYPE_ERROR
                                self._add_error_list(
                                    attr, return_code=return_code)
                        else:
                            return_code = LDEC.INPUT_FIELD_AND_MODEL_NOT_EQUAL
                            self._add_error_list(attr, return_code=return_code)
                else:
                    return_code = LDEC.INPUT_FIELD_NOT_EXISTS
                    self._add_error_list(attr, return_code=return_code)
            else:
                return_code = LDEC.INPUT_FIELD_NOT_FOUND
                self._add_error_list(attr, return_code=return_code)
        else:
            return_code = LDEC.INPUT_FIELD_NOT_FOUND
            self._add_error_list(
                attr, return_code=return_code, element_data=self.element)

    def _check_relation_type(self, attr, hierarchy_check=False):
        field_info = self.element.get("field_info", {})
        if not field_info:
            field_info = self.element.get("edit_field", {})
        # app_log.info(f"field_info: {field_info}")
        if field_info:
            self.update_reference_by_field_info(field_info, self.element, attr)
            field_uuid = field_info.get("uuid")
            field_placeholder = field_info.setdefault("placeholder", dict())
            path = field_info.get("path")
            r_finder = self.page_finder.relationship_finder
            if field_uuid:
                field_dict = self.app_field_dict.get(field_uuid, dict())
                if field_dict:
                    field_model_uuid = field_dict.get(
                        ModelField.model_uuid.name)
                    is_required = field_dict.get("is_required", False)
                    app_log.info(
                        f"model_uuid: {self.model_uuid}, field_model_uuid: {field_model_uuid}")
                    if self.model_uuid:
                        if field_model_uuid == self.model_uuid:
                            return_code = LDEC.RELATION_FIELD_AND_MODEL_EQUAL
                            # self._add_error_list(attr, return_code=return_code)
                        else:
                            if path and isinstance(path, list):
                                if len(path) > 1:
                                    return_code = LDEC.RELATION_PATH_TOO_LARGE
                                    self._add_error_list(
                                        attr, return_code=return_code)
                                elif len(path) == 1:
                                    p = path[0]
                                    associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                                        p, self.model_uuid, [p])
                                    if associaton_model is None:
                                        return_code = LDEC.RELATION_PATH_NOT_FOUND
                                        self._add_error_list(
                                            attr, return_code=return_code)
                                    if is_many is True:
                                        field_placeholder.update(
                                            {"is_many": 1})
                                    else:
                                        field_placeholder.update(
                                            {"is_many": 0})
                                    relationship = r_finder.relationship_basic_map.get(
                                        p, {})
                                    if relationship:
                                        relation_extra = relationship.extra
                                        if relationship.source_model == field_model_uuid:
                                            is_required = relation_extra.get(
                                                "source_required", False)
                                        else:
                                            is_required = relation_extra.get(
                                                "target_required", False)
                                        # relation_required = False if is_many else is_required
                                        field_placeholder.update(
                                            {"relation_required": is_required})
                                        field_placeholder.update(
                                            {"is_required": is_required})
                                    field_info.update(
                                        {"placeholder": field_placeholder})
                            else:
                                if hasattr(self, "page_finder"):
                                    join_path = r_finder.find_model_join_path(
                                        self.model_uuid, field_model_uuid)
                                    if not join_path:
                                        return_code = LDEC.RELATION_PATH_NOT_FOUND
                                        self._add_error_list(
                                            attr, return_code=return_code)
                                    elif len(join_path) > 2:
                                        return_code = LDEC.RELATION_PATH_TOO_LARGE
                                        self._add_error_list(
                                            attr, return_code=return_code)
                    if hierarchy_check:
                        hierarchy_field = field_dict.get(
                            ModelField.hierarchy_field.name)
                        if hierarchy_field not in [1, 2]:  # 1 普通层次字段  2 时间日期层次字段
                            return_code = LDEC.RELATION_FIELD_NOT_HIERARCHY_FIELD
                            self._add_error_list(attr, return_code=return_code)
                else:
                    return_code = LDEC.RELATION_FIELD_NOT_EXISTS
                    self._add_error_list(attr, return_code=return_code)
            else:
                return_code = LDEC.RELATION_FIELD_NOT_FOUND
                self._add_error_list(attr, return_code=return_code)
        else:
            return_code = LDEC.RELATION_FIELD_NOT_FOUND
            self._add_error_list(attr, return_code=return_code)

    def _update_field_placeholder(self, field_info):
        if field_info:
            field_uuid = field_info.get("uuid")
            field_placeholder = dict()
            if field_uuid:
                field_dict = self.app_field_dict.get(field_uuid, dict())
                if field_dict:
                    field_is_required = field_dict.get(
                        ModelField.is_required.name)
                    field_type = field_dict.get(ModelField.field_type.name)
                    field_placeholder.update(
                        {"is_required": field_is_required, "type": field_type})
                    if field_type == FieldType.ENUM:
                        field_enum_uuid = field_dict.get(
                            ModelField.enum_uuid.name)
                        field_placeholder.update(
                            {"enum_uuid": field_enum_uuid})
                    field_info.update({"placeholder": field_placeholder})

    def _check_relation_page(
            self, page_name: str, attr: str, return_code_lose: ReturnCode, return_code_delete: ReturnCode):
        page_info = self.element.get(page_name)
        if isinstance(page_info, dict) and page_info.get("show"):
            page_uuid = page_info.get("page", "")
            if page_uuid:
                page = self.app_page_dict.get(page_uuid, {})
                if page and page.get("is_delete"):
                    self._add_error_list(attr, return_code=return_code_delete)
            else:
                self._add_error_list(attr, return_code=return_code_lose)

    def _check_datasource(self):
        element_uuid = self.element_uuid
        model_dict = self.page_finder.relationship_finder.node_uuid_map
        code = IDECode.MODEL_NOT_EXISTS
        datasource = self.model_in_page.get(element_uuid, {})
        # attr = self.get_attr_by_name("DATASOURCE") or DatalistAttr.DATASOURCE
        if datasource:
            # self.update_reference_by_data_source(datasource, self.element, attr)
            datasource_model = datasource.get("model")
            if datasource_model:
                model_info = model_dict.get(datasource_model, {})
                if not model_info:
                    self._add_error_list(self.element.get("name"), code)

    def check_events(self, attr="", events=[], element_data=None, **kwargs):
        for event in events:
            self.update_reference_by_event(event, element_data, attr)
            self.check_wf_action(attr, event, **kwargs)
            response_method = event.get("response_method")
            if response_method == ResponseMethod.FUNC:
                self.check_action_func(attr, event, element_data=element_data)
            elif response_method == ResponseMethod.ACTION:
                self.check_action_value_editor(attr, event)
                self.check_label_print_model(attr, event, element_data)

    def check_label_print_model(self, attr, event, element_data):
        """
        element_data: 要跳转的组件
        model_component: 拥有模型 uuid 的组件
        """
        action = event.get("action")
        if action != EventAction.LABEL_PRINT:
            return
        container_model_uuid = getattr(self, "container_model_uuid", None)
        parent_model_uuid = getattr(self, "parent", {}).get("model_uuid")
        model_uuid = getattr(self, "model_uuid", None)
        model_uuid = container_model_uuid or parent_model_uuid or model_uuid
        label_print_uuid = event.get("page")
        document_content = self.app_label_print_dict.get(label_print_uuid)
        if document_content:
            label_print_model_uuid = document_content.get(
                "document_content").get("data_source", {}).get("model")
            if label_print_model_uuid:
                if label_print_model_uuid != model_uuid:
                    return_code = LDEC.LABEL_PRINT_MODEL_DIFFERENT
                    self._add_error_list(
                        attr, return_code=return_code, element_data=element_data)

    def _check_event(self, attr, element_data=None, **kwargs):
        def __get_whole_events(element, kwargs):
            card_list_check = self.element.get(
                "type") == ComponentType.CARDLIST
            if card_list_check:
                kwargs.update({"in_cardlist": False})
                whole_events = self.element.get("card_item").get("events", {})
            else:
                if element_data:
                    whole_events = element_data.get("events", {})
                else:
                    whole_events = self.element.get("events", {})
            return whole_events
        whole_events = __get_whole_events(self.element, kwargs)
        for _, events in whole_events.items():
            events: list
            self.check_max_count(attr, events, **kwargs)
            self.check_events(attr, events, element_data, **kwargs)

    def check_max_count(self, attr, events, **kwargs):
        allow_max_count = 10
        count_return_code = LDEC.EVENT_MAX_COUNT_ERR
        count_return_code.message = count_return_code.message.format(
            count=allow_max_count)
        if len(events) > allow_max_count:
            self._add_error_list(attr, count_return_code, **kwargs)

    def check_wf_action(self, attr, event, **kwargs):
        action = event.get("action", None)
        workflow_uuid = event.get("workflow", False)
        if action == EventAction.START_WF:
            cur_wf = list(
                filter(lambda x: x["wf_uuid"] == workflow_uuid, self.app_workflow_list))
            if not workflow_uuid:
                return_code = LDEC.EVENT_START_WF_NEED_WF_UUID
                self._add_error_list(attr, return_code, **kwargs)
            elif cur_wf:
                # 按钮不在容器中发起工作流
                if event.get("showWfPageType") == 1:
                    cur_wf = cur_wf[0]
                    self.update_event_from_setting(cur_wf, event)
                # 事件中绑定发起页面
                elif event.get("showWfPageType") == 2:
                    # 当前页面作为发起
                    # 当前页面表单数量=1时才可以
                    self.model_in_page
                    page_forms = {
                        k: v for k, v in self.model_in_page.items()
                        if v.get("component_type") == ComponentType.FORM and v.get("is_outer_container")}
                    if len(page_forms) != 1:
                        self._add_error_list(
                            attr, return_code=LDEC.WF_FROM_PAGE_NEED_ONE_FORM)
                    else:
                        container, _ = page_forms.popitem()
                        event.update({
                            "pc_from": self.document_uuid,
                            "mobile_from": self.document_uuid,
                            "pad_from": self.document_uuid,
                            "pc_from_container": container,
                            "mobile_from_container": container,
                            "pad_from_container": container
                        })
                else:
                    cur_wf = cur_wf[0]
                    pc_from, mobile_from, pad_from = \
                        self.update_event_from_setting(cur_wf, event)
                    pc_from_container, pc_from_model_uuid = self.add_wf_submit_page_form_uuid(
                        pc_from)
                    mobile_from_container, mobile_from_model_uuid = self.add_wf_submit_page_form_uuid(
                        mobile_from)
                    pad_from_container, pad_from_model_uuid = self.add_wf_submit_page_form_uuid(
                        pad_from)
                    # pc_from_container,mobile_from_container,pad_from_container可能已弃用
                    event.update({
                        "pc_from_container": pc_from_container,
                        "mobile_from_container": mobile_from_container,
                        "pad_from_container": pad_from_container
                    })
                    # cur_wf上配置的发起页面上的表单的数据模型
                    # 需要与当前页面的数据模型一致
                    if getattr(self, "container_model_uuid", None):
                        # 子表按钮区parent为表单，container_model_uuid为子表模型
                        cur_model_uuid = self.container_model_uuid
                    else:
                        cur_model_uuid = self.model_uuid or self.parent.get(
                            "model_uuid")  # 可能在数据容器内
                    if pc_from_model_uuid and pc_from_model_uuid != cur_model_uuid:
                        from_model_return_code = LDEC.PAGE_MODEL_DIFFERENT_WF_FROM_MODEL if cur_wf.get(
                            "type") == WorkflowType.STANDARD else LDEC.PAGE_MODEL_DIFFERENT_AF_TO_MODEL
                        from_model_return_code.message = from_model_return_code.message.format(
                            name="PC")
                        self._add_error_list(
                            attr, return_code=from_model_return_code)
                    if mobile_from_model_uuid and mobile_from_model_uuid != cur_model_uuid:
                        from_model_return_code = LDEC.PAGE_MODEL_DIFFERENT_WF_FROM_MODEL if cur_wf.get(
                            "type") == WorkflowType.STANDARD else LDEC.PAGE_MODEL_DIFFERENT_AF_TO_MODEL
                        from_model_return_code.message = from_model_return_code.message.format(
                            name="手机")
                        self._add_error_list(
                            attr, return_code=from_model_return_code)
                    if pad_from_model_uuid and pad_from_model_uuid != cur_model_uuid:
                        from_model_return_code = LDEC.PAGE_MODEL_DIFFERENT_WF_FROM_MODEL if cur_wf.get(
                            "type") == WorkflowType.STANDARD else LDEC.PAGE_MODEL_DIFFERENT_AF_TO_MODEL
                        from_model_return_code.message = from_model_return_code.message.format(
                            name="平板")
                        self._add_error_list(
                            attr, return_code=from_model_return_code)
            else:
                return_code = LDEC.EVENT_NO_WORKFLOW_ERR
                self._add_error_list(attr, return_code, **kwargs)
            if event.get("showWfPageType") == 0:
                # 不显示发起页面时, 需配置页面参数
                if not event.get("page_props"):
                    return_code = LDEC.PAGE_PROPS_ERROR
                    self._add_error_list(attr, return_code, **kwargs)

    def update_event_from_setting(self, cur_wf, event):
        pc_from = cur_wf.get("pc_from", "")
        mobile_from = cur_wf.get("mobile_from", "")
        pad_from = cur_wf.get("pad_from", "")
        event.update({
            "pc_from": pc_from,
            "mobile_from": mobile_from,
            "pad_from": pad_from,
        })
        return pc_from, mobile_from, pad_from

    def check_action_func(self, attr, event, **kwargs):
        func_uuid = event.get("func")
        if func_uuid:
            if func_uuid not in self.app_func_uuid_dict:
                self._add_error_list(
                    attr=attr, return_code=LDEC.ACTION_FUNC_NOT_EXISTS, **kwargs)
        else:
            self._add_error_list(
                attr=attr, return_code=LDEC.ACTION_FUNC_NOT_EXISTS, **kwargs)

    def check_action_value_editor(self, attr, event):
        action = event.get("action")
        value_editors = []

        if action == EventAction.OPEN:
            value_editors.append(event.get("page_props", None))
        elif action == EventAction.LINK:
            value_editors.append(event.get("link_addr", None))
            value_editors.append(event.get("page_props", None))
        elif action == EventAction.MESSAGE_BOX:
            if event.get("show_button", False):
                value_editors.append(event.get("button_content", None))
            value_editors.append(event.get("modal_content", None))
            if event.get("show_title", False):
                value_editors.append(event.get("modal_title", None))
        elif action == EventAction.START_WF:
            if event.get("showWfPageType", False):
                value_editors.append(event.get("page_props", None))
        elif action == EventAction.FORM_PRINT:
            value_editors.append(event.get("page_props", None))
        elif action == EventAction.EXPORT:
            value_editors.append(event.get("default_file_name", None))
        else:
            return

        if not hasattr(self, "page_finder") or not self.page_finder:
            self.page_finder = PageFinder(
                self.model_in_page, except_form=False, count_container=True,
                find_association=True, model_list=self.app_model_list,
                relationship_list=self.app_relationship_list,
                field_list=self.app_field_list)

        if not hasattr(self, "app_field_dict"):
            self.app_field_dict = None

        if not hasattr(self, "app_relationship_dict"):
            self.app_relationship_dict = None

        r_finder = self.page_finder.relationship_finder
        for value_editor in value_editors:
            if value_editor:
                self._check_value_editor(
                    value_editor, attr, self.app_field_dict, self.app_relationship_dict, r_finder)

    def add_wf_submit_page_form_uuid(self, page_uuid):
        # 发布时的文档检查能确保当页面中表单数量=1时,返回该表单uuid, 和表单模型uuid
        page_detail = self.app_page_dict.get(page_uuid, {})
        container_model = page_detail.get("container_model")
        form_info = list()
        if container_model and isinstance(container_model, dict):
            for container_id, container_detail in container_model.items():
                container_type = container_detail.get("component_type")
                is_outer_container = container_detail.get("is_outer_container")
                # if container_type == ComponentType.FORM and is_outer_container:
                #     model_uuid = container_detail.get("model")
                #     return container_id, model_uuid  # TODO 全局文档检查不能添加is_outer_container, 导致有些页面没有这个值
                if container_type == ComponentType.FORM:
                    model_uuid = container_detail.get("model")
                    form_info.append(
                        (container_id, model_uuid, is_outer_container))

        # TODO 如果每个拥有最外层是form的page的container_model里都有标记is_outer_container
        # 那就不要这么写, 直接返回is_outer_container就行
        if len(form_info) > 1:
            for info in form_info:
                container_id, model_uuid, is_outer_container = info
                if is_outer_container:
                    break
                container_id, model_uuid = "", ""
        elif len(form_info) == 1:
            container_id, model_uuid, is_outer_container = form_info[0]
        else:
            container_id, model_uuid = "", ""
        return container_id, model_uuid

    def check_preprocessing_info(self, attr, preprocessing):
        if preprocessing:
            r_finder = self.page_finder.relationship_finder
            field_uuid_map = r_finder.field_uuid_map
            having_condition = preprocessing.get("group", [])
            # if having_condition:
            #     group_by = preprocessing.get("groupFilter")
            #     if not group_by:
            #         attr = Datalist.ATTR.DATASOURCE
            #         return_code = LDEC.DATALIST_PREPROCESS_GROUP_BY_UNSELECT
            #         self._add_error_list(attr, return_code= return_code)
            condition_list = preprocessing.get("condition", [])
            for conditions in condition_list:
                if not isinstance(conditions, list):
                    conditions = [conditions]
                for condition in conditions:
                    conditon_type = condition.get("type")
                    if conditon_type == 0:  # 条件的简单设置包含字段
                        self.check_preprocessing_field(
                            attr, condition, field_uuid_map)
            order_list = preprocessing.get("order", [])
            for order_info in order_list:
                self.check_preprocessing_field(
                    attr, order_info, field_uuid_map)
            for havings in having_condition:
                if not isinstance(havings, list):
                    havings = [havings]
                for having in havings:
                    self.check_preprocessing_field(
                        attr, having, field_uuid_map)

    def check_preprocessing_field(self, attr, field_info, field_uuid_map):
        field_uuid = field_info.get("field")
        self.update_reference_by_field_info(field_info, self.element, attr)
        if field_uuid:
            lemon_field = field_uuid_map.get(field_uuid)
            if lemon_field:
                if lemon_field.calculate_field and lemon_field.calculate_type != CalculateType.GENERATED_COLUMN:
                    return_code = LDEC.CALCULATE_FIELD_NOT_SUPPORT
                    self._add_error_list(attr, return_code=return_code)
            else:
                return_code = LDEC.RELATION_FIELD_NOT_FOUND
                self._add_error_list(attr, return_code=return_code)

    def _check_is_paginated(self):
        attr = Form.ATTR.CONVENTION
        page_dict = self.app_page_dict.get(self.page_uuid)
        form_count = None
        if page_dict:
            is_paginated = self.element.get("is_paginated", False)
            if is_paginated:
                form_count = page_dict.get(PageModel.form_count.name)
        if isinstance(form_count, int) and form_count > 1:
            return_code = LDEC.FORM_IS_PAGINATED_PAGE_FORM_COUNT_ERROR
            self._add_error_list(attr, return_code=return_code)

    # 检查数据源是否绑定
    def _check_datasource_exists(self):
        attr = Datalist.ATTR.DATASOURCE
        data_source = self.element.get("data_source", {})
        self.update_reference_by_data_source(data_source, self.element, attr)
        if data_source.get("type") in [DataSourceType.ASSOCIATION, DataSourceType.ASSOCIATION_MEMORY] and \
                not self.parent:
            # 关联子表从表单里拖到表单外，数据源类型文档检查
            return_code = LDEC.DATALIST_DATASOURCE_ERROR
            return_code.message = return_code.message.format(
                name=self.element_name)
            self._add_error_list(attr, return_code=return_code)
        if data_source.get("type", DataSourceType.MODEL_WITH_PRE) == DataSourceType.MODEL_WITH_PRE:
            if not data_source.get("model"):
                return_code = LDEC.DATASOURCE_DOESNOT_EXIST
                return_code.message = return_code.message.format(
                    name=self.element_name)
                self._add_error_list(attr, return_code=return_code)
        elif data_source.get("type") == DataSourceType.MODEL_WITH_EDITOR:
            if not data_source.get("model"):
                return_code = LDEC.DATASOURCE_DOESNOT_EXIST
                return_code.message = return_code.message.format(
                    name=self.element_name)
                self._add_error_list(attr, return_code=return_code)
            value_editor_dict = data_source.get("edit_value")
            self._check_value_editor(
                value_editor_dict, attr, self.app_field_dict, self.app_relationship_dict)
        elif data_source.get("type") == DataSourceType.ASSOCIATION and not isinstance(self, BaseDatalistCheckerService):
            # 关联表与check_relation_data_source重复
            if not data_source.get("association"):
                return_code = LDEC.DATASOURCE_RELATION_NOT_EXIST
                return_code.message = return_code.message.format(
                    name=self.element_name)
                self._add_error_list(attr, return_code=return_code)
            else:
                path = data_source.get("path")
                if path:
                    p = path[0]
                    parent_model = self.parent.get("model_uuid")
                    r_finder = self.page_finder.relationship_finder
                    associaton_model = None
                    try:
                        associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                            p, parent_model)
                    except Exception:
                        pass
                    if associaton_model is None:
                        return_code = LDEC.RELATION_PATH_NOT_FOUND
                        self._add_error_list(attr, return_code=return_code)
        elif data_source.get("type") == DataSourceType.FORM_LINKAGE:
            component = data_source.get("component")
            if not component or component not in self.model_in_page:
                return_code = LDEC.DATASOURCE_COMPONENT_NOT_EXIST
                return_code.message = return_code.message.format(
                    name=self.element_name)
                self._add_error_list(attr, return_code=return_code)
            if self.is_copy:
                data_source_component = data_source.get("component")
                if data_source_component:
                    if self.component_copy_dict.get(data_source_component):
                        data_source.update(
                            {"component": self.component_copy_dict.get(data_source_component)})
                    else:
                        temp_uuid = lemon_uuid()
                        self._update_component_copy_dict(
                            data_source_component, temp_uuid)
                        data_source.update({"component": temp_uuid})
        elif data_source.get("type") == DataSourceType.MODEL_WITH_FUNC:
            if not data_source.get("model"):
                return_code = LDEC.DATASOURCE_DOESNOT_EXIST
                return_code.message = return_code.message.format(
                    name=self.element_name)
                self._add_error_list(attr, return_code=return_code)
            func_uuid = data_source.get("func")
            if func_uuid:
                if not self.app_func_uuid_dict.get(func_uuid):
                    self._add_error_list(
                        attr, return_code=LDEC.FUNC_IS_DELETED)
            else:
                self._add_error_list(attr, return_code=LDEC.FUNC_NOT_EXIST)

    def _prepare_children_datasource_check(self):
        container_children = [
            child for child in self.children
            if child.get("type") in ComponentType.CONTAINER_LIST and child.get("data_source").get("association")]
        container_children = sorted(container_children, key=lambda child: child.get(
            "data_source").get("association"))
        # 将属于数据容器的子组件按数据源中的关联分组
        children_group_by_model = {key: list(group) for key, group in
                                   groupby(container_children,
                                           key=lambda child: child.get("data_source").get("association"))}
        # 剔除同一分组中数据源类型不同时存在数据库存储和内存存储的分组
        self.filtered_group = {key: [child.get("uuid") for child in group]
                               for key, group in children_group_by_model.items()
                               if set(child.get("data_source").get("type") for child in group).issuperset(
                                   {DataSourceType.ASSOCIATION, DataSourceType.ASSOCIATION_MEMORY})}

    def _check_datasource_type_multiple(self, attr):
        association = self.element.get("data_source", {}).get("association")
        filter_group = self.parent.get("filtered_group")
        if filter_group and association in filter_group:
            return_code = LDEC.CONTAINER_DATASOURCE_NOT_MULTIPLE
            association_name = self.app_relationship_dict.get(
                association).get("relationship_name")
            return_code.message = return_code.message.format(
                association_name=association_name)
            self._add_error_list(attr, return_code=return_code)

    def _find_dict_inner(self, data: dict, need_update: dict):
        """在data最里层添加一层"""
        for k, v in data.items():
            if k == "position_info" and isinstance(v, dict):
                self._find_dict_inner(v, need_update)
                break
        else:
            data["position_info"] = need_update

    def process_layout_uuid(self, kwargs: dict, this_layout_uuid, **d):
        """更新kwargs, 添加最近布局容器位置信息"""
        position_info: dict = kwargs.setdefault("position_info", dict())
        this_layout = {"layout_uuid": this_layout_uuid}
        this_layout.update(d)
        if position_info:
            self._find_dict_inner(position_info, this_layout)
        else:
            position_info.update(this_layout)

    def build_field_dict(
            self, field_info, field_list, app_relationship_dict, search_items=None):
        field_uuid = field_info.get("field") or field_info.get("uuid")
        lemon_field_info = self.app_field_dict.get(field_uuid)
        if lemon_field_info:
            path = field_info.get("path", [])
            path_name = ""
            if len(path) == 1:
                path_name = app_relationship_dict.get(
                    path[0], {}).get("relationship_name")
            lemon_field = {
                "path": path,
                "path_name": path_name,
                "aggre_func": field_info.get("aggre_func", None)
            }
            lemon_field.update(lemon_field_info)
            field_list.append(lemon_field)
            if isinstance(search_items, list):
                search_support = field_info.get("search_support")
                if search_support:
                    search_info = field_info.get("search_info", {})
                    # field_uuid = field_info.get("field")
                    # fieldsValue = {"field": field_uuid, "path": path}
                    # search_info.update({"fieldsValue": fieldsValue})
                    search_items.append(search_info)

    def check_input_col(self):
        label_layout_type = self.element.get("label_layout_type")
        if label_layout_type == 1:
            label_col = self.element.get("label_col")
            wrapper_col = self.element.get("wrapper_col")
            if isinstance(label_col, int) and isinstance(wrapper_col, int) and label_col + wrapper_col > 24:
                attr = Page.ATTR.INPUT_CONTROL
                self._add_error_list(
                    attr=attr, return_code=LDEC.FORM_INPUT_COL_ERROR)

    def _check_custom(self):
        attr = "自定义组件值编辑器"
        attr_bind = self.element.get("custom", {}).get("attr_bind", {})
        for _, bind_info in attr_bind.items():
            component = bind_info.get("component")
            if component == CustomComponentType.VALUE_EDITOR:
                value_editor_dict = bind_info.get("value")
                if value_editor_dict:
                    self._check_value_editor(
                        value_editor_dict, attr,
                        self.app_field_dict, self.app_relationship_dict)

    def _check_button_page_with_page_uuid(
            self, page_uuid, attr, page_not_exists_code, form_count_error_code,
            form_model_error_code, button_dict=None):
        if page_uuid in self.app_page_dict:
            page_dict = self.app_page_dict.get(page_uuid)
            form_count = page_dict.get(PageModel.form_count.name)
            if page_dict.get(PageModel.is_delete.name):
                return_code = page_not_exists_code
                button_dict = {}
                self._add_error_list(
                    attr, return_code=return_code, element_data=button_dict)
            else:
                title = self.element.get("name")
                element_uuid = self.element.get("uuid")
                self.update_page_reference(
                    page_uuid, title, element_uuid, attr)
            container_model = page_dict.get(
                PageModel.container_model.name)
            if isinstance(form_count, int) and form_count > 1:
                return_code = form_count_error_code
                self._add_error_list(
                    attr, return_code=return_code, element_data=button_dict)
            find_same_model = False
            for _, container_dict in container_model.items():
                model = container_dict.get("model")
                if model == self.container_model_uuid:
                    find_same_model = True
                    break
            if not find_same_model and not page_dict.get(PageModel.is_delete.name):
                return_code = form_model_error_code
                self._add_error_list(
                    attr, return_code=return_code, element_data=button_dict)
            # open_type = page_dict.get("open_type")
            # if open_type in [PageOpenType.JUMP]:
            #     return_code = page_open_type_code
            #     self._add_error_list(attr, return_code=return_code, element_data=button_dict)
        else:
            return_code = page_not_exists_code
            self._add_error_list(
                attr, return_code=return_code, element_data=button_dict)

    def _check_cloud_event(self, attr, element_data=None, **kwargs):
        events = self.element.get("events", [])
        for event_info in events:
            element_uuid = self.element_uuid
            title = self.element_name
            func_uuid = event_info.get("func")
            if func_uuid:
                if self.app_func_uuid_dict.get(func_uuid):
                    self.update_cloud_func_reference(
                        func_uuid, title, element_uuid, attr)
                else:
                    self._add_error_list(
                        attr, return_code=LDEC.FUNC_IS_DELETED, element_data=self.element)
            else:
                self._add_error_list(
                    attr, return_code=LDEC.FUNC_NOT_EXIST, element_data=self.element)

    @checker.run
    def check_permission_config(self, element_data=None):
        permission_config = self.element.get("permission_config", {})
        self._check_permission_config(
            permission_config, self.module_tag_dict, element_data=element_data)

    def check_aggre_cloud_func(self, aggre_setting, attr, position, return_code_param_unbound, return_code_param_not_exist, element_uuid):
        func_uuid = aggre_setting.get("func_uuid")
        if func_uuid:
            if self.app_func_uuid_dict.get(func_uuid):
                self.update_cloud_func_reference(
                    func_uuid, self.element.get("name"), element_uuid, attr)
            else:
                return_code = LDEC.FUNC_IS_DELETED
                return_code.position = return_code.position.format(
                    position=position)
                self._add_error_list(
                    attr, return_code=return_code, dataIndex=element_uuid)
        else:
            return_code = LDEC.FUNC_NOT_EXIST
            return_code.position = position
            self._add_error_list(
                attr, return_code=return_code, dataIndex=element_uuid)
        if "editorValues" in aggre_setting:
            param_list = aggre_setting.get("editorValues", [])
            for param in param_list:
                self._check_value_editor(
                    param, attr, self.app_field_dict,
                    self.app_relationship_dict, position=position)
        else:
            param_list = aggre_setting.get("params", [])  # 不会出现得到 None 的情况
            for param in param_list:
                if not param:
                    self._add_error_list(
                        attr, return_code=return_code_param_unbound, dataIndex=element_uuid)
                    continue
                field_uuid = param.get("uuid")
                if field_uuid == False:
                    self._add_error_list(
                        attr, return_code=return_code_param_unbound, dataIndex=element_uuid)
                    continue
                if field_uuid not in self.app_field_dict:
                    self._add_error_list(
                        attr, return_code=return_code_param_not_exist, dataIndex=element_uuid)
                else:
                    self.update_field_reference(
                        field_uuid, self.element.get("name"), element_uuid, attr)

    def _check_sort_field(self, attr, sort_field, position="行"):
        if sort_field:
            field_uuid = sort_field.get("uuid")
            if not field_uuid:
                return_code = LDEC.SORT_FIELD_NOT_SELECT
                return_code.position = position
                self._add_error_list(attr, return_code=return_code)
            else:
                field_dict = self.app_field_dict.get(field_uuid, {})
                if not field_dict:
                    return_code = LDEC.SORT_FIELD_NOT_EXISTS
                    return_code.position = position
                    self._add_error_list(attr, return_code=return_code)
                elif field_dict.get("field_type") not in [FieldType.INTEGER, FieldType.DECIMAL]:
                    return_code = LDEC.SORT_FIELD_NOT_INTEGER_DECIMAL
                    return_code.position = position
                    self._add_error_list(attr, return_code=return_code)
        else:
            return_code = LDEC.SORT_FIELD_NOT_SELECT
            return_code.position = position
            self._add_error_list(attr, return_code=return_code)

    def check_dynamic_definition(self):
        style = self.source_element.get("style", {})
        dynamic_definition = self.style_class.DYNAMIC_DEFINITION
        class_name_dict = style.get("class_name_value_editor")
        if class_name_dict:
            page_finder = getattr(self, "page_finder", None)
            r_finder = page_finder.relationship_finder if page_finder else None
            self._check_value_editor(
                class_name_dict, dynamic_definition, self.app_field_dict,
                self.app_relationship_dict, r_finder=r_finder, position=dynamic_definition)


class BaseInputCheckerService(ComponentCheckerService):

    attr_class = BaseInput.ATTR

    def initialize(self):
        super().initialize()
        self.model_uuid = getattr(self, "model_uuid", None)

    @checker.run
    def check_input_col(self):
        super().check_input_col()

    @checker.run
    def check_custom(self):
        self._check_custom()

    @checker.run
    def check_event(self):
        attr = BaseInputAttr.EVENT
        self._check_event(attr)


class BaseNormalInputCheckerService(BaseInputCheckerService):

    def initialize(self):
        super().initialize()
        self._column_field = None

    @checker.run
    def check_field_type(self):
        attr = self.attr_class.TYPE
        self._check_field_type(attr=attr)
        if self.type in [ComponentType.SELECT]:
            self._check_datasource_cloud_func()

    @checker.run
    def get_field_reference(self):
        attr = BaseInputAttr.DATASOURCE
        field_info = self.element.get("field_info", {})
        if field_info:
            self.update_reference_by_field_info(field_info, self.element, attr)

    @property
    def column_field(self):
        field_dict = {}
        field_info = self.element.get("field_info", {})
        if field_info:
            field_uuid = field_info.get("uuid")
            if field_uuid:
                field_dict = self.app_field_dict.get(field_uuid, dict())
        return field_dict

    @checker.run
    def check_calculate(self):
        calc_field = self.column_field.get(ModelField.calculate_field.name)
        if calc_field:
            editable = self.element.get("editable", {})
            editable.update({"type": 0, "value": False})

    #流水号字段，允许编辑；未输入时，自动生成
    # @checker.run
    # def check_serial(self):
    #     serial_field = self.column_field.get(ModelField.is_serial.name)
    #     if serial_field:
    #         editable = self.element.get("editable", {})
    #         editable.update({"type": 0, "value": False})

    @checker.run
    def check_edit_default_value(self):
        attr = BaseInputAttr.DATASOURCE
        field_info = self.element.get("field_info", {})
        if field_info:
            edit_default_value = field_info.get("edit_default_value", {})
            if edit_default_value:
                self._check_value_editor(
                    edit_default_value, attr, self.app_field_dict, self.app_relationship_dict)

    def _check_datasource_cloud_func(self):
        datasource = self.element.get("data_source", {})
        datasource_type = datasource.get("type")
        if datasource_type == DataSourceType.SELECT_FUNC:
            func_uuid = datasource.get("func")
            attr = self.attr_class.DATASOURCE
            element_uuid = self.element_uuid
            title = self.element_name
            if func_uuid:
                if self.app_func_uuid_dict.get(func_uuid):
                    self.update_cloud_func_reference(
                        func_uuid, title, element_uuid, attr)
                else:
                    return_code = LDEC.FUNC_IS_DELETED
                    return_code.position = attr
                    self._add_error_list(
                        attr, return_code=return_code, element_data=self.element)
            else:
                return_code = LDEC.FUNC_NOT_EXIST
                return_code.position = attr
                self._add_error_list(
                    attr, return_code=return_code, element_data=self.element)


class BaseRelationInputCheckerService(BaseInputCheckerService):

    @checker.run
    def check_relation_type(self):
        attr = self.attr_class.TYPE
        self._check_relation_type(attr=attr)


class InputCheckerService(BaseNormalInputCheckerService):

    attr_class = Input.ATTR
    uuid_error = LDEC.INPUT_UUID_ERROR
    uuid_unique_error = LDEC.INPUT_UUID_UNIQUE_ERROR
    name_error = LDEC.INPUT_NAME_FAILED
    name_unique_error = LDEC.INPUT_NAME_NOT_UNIQUE

    @checker.run
    def check_input_setting(self):
        input_setting = self.element.get("input_setting", {})
        input_type = input_setting.get("type", InputType.keyboard)
        if input_type in [InputType.external_adapter]:
            adapter_ = input_setting.get("adapter", "")
            settings = input_setting.get("settings", {})
            if not settings:
                adapter_template = InputAdapter.adapters.get(adapter_)
                all_editors = restore_adapter(adapter_template)
                input_setting["settings"] = all_editors
        elif input_type == InputType.keyboard:
            # input_setting.pop("adapter", None)
            input_setting.pop("interval", None)
            input_setting.pop("settings", None)

    @checker.run
    def check_number_attribute(self):
        number_attribute = self.element.get("number_attribute", {})
        max = number_attribute.get("max")
        min = number_attribute.get("min")
        if isinstance(max, (int, float)) and isinstance(min, (int, float)) and min > max:
            self._add_error_list(
                attr=InputAttr.SETTING, return_code=LDEC.INPUT_NAME_NUMVER_ATTRIBUTE_SETTING_ERROR,
                element_data=self.element)


class TextareaCheckerService(BaseNormalInputCheckerService):

    attr_class = Textarea.ATTR
    uuid_error = LDEC.TEXTAREA_UUID_ERROR
    uuid_unique_error = LDEC.TEXTAREA_UUID_UNIQUE_ERROR
    name_error = LDEC.TEXTAREA_NAME_FAILED
    name_unique_error = LDEC.TEXTAREA_NAME_NOT_UNIQUE

    @checker.run
    def check_max_length(self):
        max_length_type = self.element.get("max_length_type")
        component_max_length = self.element.get("max_length", 0)
        field_uuid = self.element.get("field_info", {}).get("uuid")
        field_data = self.app_field_dict.get(field_uuid)
        if field_uuid and field_data:
            field_max_length = field_data.get("length")
            if max_length_type == 0:
                self.element.update({"max_length": field_max_length})
            elif max_length_type == 1:
                if component_max_length > field_max_length:
                    self.element.update({"max_length": field_max_length})
            else:
                self.element.update({"max_length": field_max_length})


class RadioCheckerService(BaseNormalInputCheckerService):

    attr_class = Radio.ATTR
    uuid_error = LDEC.RADIO_UUID_ERROR
    uuid_unique_error = LDEC.RADIO_UUID_UNIQUE_ERROR
    name_error = LDEC.RADIO_NAME_FAILED
    name_unique_error = LDEC.RADIO_NAME_NOT_UNIQUE


class CheckboxCheckerService(BaseNormalInputCheckerService):

    attr_class = Checkbox.ATTR
    uuid_error = LDEC.CHECKBOX_UUID_ERROR
    uuid_unique_error = LDEC.CHEBKBOX_UUID_UNIQUE_ERROR
    name_error = LDEC.CHECKBOX_NAME_FAILED
    name_unique_error = LDEC.CHECKBOX_NAME_NOT_UNIQUE


class SelectCheckerService(BaseNormalInputCheckerService):

    attr_class = Select.ATTR
    uuid_error = LDEC.SELECT_UUID_ERROR
    uuid_unique_error = LDEC.SELECT_UUID_UNIQUE_ERROR
    name_error = LDEC.SELECT_NAME_FAILED
    name_unique_error = LDEC.SELECT_NAME_NOT_UNIQUE

    # @checker.run
    def check_preprocessing(self):
        element = self.source_element or {}
        preprocessing = element.get("preprocessing", {})
        is_open = preprocessing.get("open", False)
        edit_value_dict = preprocessing.get("edit_value", {})
        if is_open and not edit_value_dict:
            attr = self.attr_class.CANDIDATES
            return_code = LDEC.SELECT_PREPROCESSING_DEIT_NOT_EXISTS
            self._add_error_list(attr=attr, return_code=return_code)


class DatetimeCheckerService(BaseNormalInputCheckerService):

    attr_class = Datetime.ATTR
    uuid_error = LDEC.DATETIME_UUID_ERROR
    uuid_unique_error = LDEC.DATETIME_UUID_UNIQUE_ERROR
    name_error = LDEC.DATETIME_NAME_FAILED
    name_unique_error = LDEC.DATETIME_NAME_NOT_UNIQUE


class SliderCheckerService(BaseNormalInputCheckerService):

    attr_class = Slider.ATTR
    uuid_error = LDEC.SLIDER_UUID_ERROR
    uuid_unique_error = LDEC.SLIDER_UUID_UNIQUE_ERROR
    name_error = LDEC.SLIDER_NAME_FAILED
    name_unique_error = LDEC.SLIDER_NAME_NOT_UNIQUE

    @checker.run
    def check_default_value(self):
        field_info = self.element.get("field_info", {})
        edit_default_value = field_info.get("edit_default_value")
        attr = self.attr_class.DEFAULT_NAME
        if edit_default_value:
            if edit_default_value.get("type") == 0:
                value = edit_default_value.get("value")
                if value and isinstance(value, int):
                    max_value = self.element.get("max")
                    min_value = self.element.get("min")
                    if min_value > value:
                        return_code = LDEC.SLIDER_NOT_DEFAULT_VALUE_TOO_SMALL
                        return_code.message = return_code.message.format(
                            name=self.element_name)
                        self._add_error_list(
                            attr=attr, return_code=return_code)
                    elif value > max_value:
                        return_code = LDEC.SLIDER_NOT_DEFAULT_VALUE_TOO_LARGE
                        return_code.message = return_code.message.format(
                            name=self.element_name)
                        self._add_error_list(
                            attr=attr, return_code=return_code)
        # else:
        #     return_code = LDEC.SLIDER_NOT_DEFAULT_VALUE
        #     return_code.message = return_code.message.format(name=self.element_name)
        #     self._add_error_list(attr=attr, return_code=return_code)


class SwitchCheckerService(BaseNormalInputCheckerService):

    attr_class = Switch.ATTR
    uuid_error = LDEC.SWITCH_UUID_ERROR
    uuid_unique_error = LDEC.SWITCH_UUID_UNIQUE_ERROR
    name_error = LDEC.SWITCH_NAME_FAILED
    name_unique_error = LDEC.SWITCH_NAME_NOT_UNIQUE


class UploadFileCheckerService(BaseNormalInputCheckerService):

    attr_class = UploadFile.ATTR
    uuid_error = LDEC.UPLOAD_FILE_UUID_ERROR
    uuid_unique_error = LDEC.UPLOAD_FILE_UUID_UNIQUE_ERROR
    name_error = LDEC.UPLOAD_FILE_NAME_FAILED
    name_unique_error = LDEC.UPLOAD_FILE_NAME_NOT_UNIQUE


class UploadImageCheckerService(BaseNormalInputCheckerService):

    attr_class = UploadImage.ATTR
    uuid_error = LDEC.UPLOAD_IMAGE_UUID_ERROR
    uuid_unique_error = LDEC.UPLOAD_IMAGE_UUID_UNIQUE_ERROR
    name_error = LDEC.UPLOAD_IMAGE_NAME_FAILED
    name_unique_error = LDEC.UPLOAD_IMAGE_NAME_NOT_UNIQUE


class ColorCheckerService(BaseNormalInputCheckerService):

    attr_class = Color.ATTR
    uuid_error = LDEC.COLOR_UUID_ERROR
    uuid_unique_error = LDEC.COLOR_UUID_UNIQUE_ERROR
    name_error = LDEC.COLOR_NAME_FAILED
    name_unique_error = LDEC.COLOR_NAME_NOT_UNIQUE


class CustomInputCheckerService(BaseNormalInputCheckerService):

    attr_class = CustomComponent.ATTR
    uuid_error = LDEC.CUSTOM_UUID_ERROR
    uuid_unique_error = LDEC.CUSTOM_UUID_UNIQUE_ERROR
    name_error = LDEC.CUSTOM_NAME_FAILED
    name_unique_error = LDEC.CUSTOM_NAME_NOT_UNIQUE


class RSelectCheckerService(BaseRelationInputCheckerService):

    attr_class = RSelect.ATTR
    uuid_error = LDEC.R_SELECT_UUID_ERROR
    uuid_unique_error = LDEC.R_SELECT_UUID_UNIQUE_ERROR
    name_error = LDEC.R_SELECT_NAME_FAILED
    name_unique_error = LDEC.R_SELECT_NAME_NOT_UNIQUE

    '''
    @description: 检查新建页面按钮打开时是否有选择页面
    @Date: 2021-04-27 11:10:43
    @LastEditors: lv
    @param {*} self
    '''
    @checker.run
    def check_new_page(self):
        attr = self.attr_class.NEW_PAGE
        return_code_lose = LDEC.R_SELECT_NEW_PAGE_LOST
        return_code_delete = LDEC.R_SELECT_NEW_PAGE_IS_DELETE
        page_name = "new_page"
        self._check_relation_page(
            page_name, attr, return_code_lose, return_code_delete)

    '''
    @description: 检查显示查看按钮打开是是否有选择页面
    @Date: 2021-04-27 11:10:35
    @LastEditors: lv
    @param {*} self
    '''
    @checker.run
    def check_view_page(self):
        attr = self.attr_class.VIEW_PAGE
        return_code_lose = LDEC.R_SELECT_VIEW_PAGE_LOST
        return_code_delete = LDEC.R_SELECT_VIEW_PAGE_IS_DELETE
        page_name = "view_page"
        self._check_relation_page(
            page_name, attr, return_code_lose, return_code_delete)

    @checker.run
    def check_candidates_func(self):
        candidate_preprocessing = self.element.get(
            "candidate_preprocessing", {})
        func_uuid = candidate_preprocessing.get("func")
        if func_uuid:
            attr = RSelectAttr.DATASOURCE
            title = self.element.get("name")
            element_uuid = self.element.get("uuid")
            self.update_cloud_func_reference(
                func_uuid, title, element_uuid, attr)

        data_type = candidate_preprocessing.get("dataType")

        func_dict = self.app_func_uuid_dict.get(func_uuid, {})
        arg_list = func_dict.get("arg_list", [])
        arg_num = len(arg_list)
        if arg_num != 2 and data_type == 3:
            attr = self.attr_class.CANDIDATES
            return_code = LDEC.R_SELECT_CANDIDATES_FUNC_ARG_ERROR
            self._add_error_list(attr, return_code=return_code)

    @checker.run
    def check_candidates_preprocessing_field(self):
        attr = self.attr_class.CANDIDATES
        preprocessing = self.element.get("candidate_preprocessing", {})
        self.check_preprocessing_info(attr, preprocessing)


class RSelectPopupCheckerService(BaseRelationInputCheckerService):

    attr_class = RSelectPopup.ATTR
    uuid_error = LDEC.R_SELECT_POPUP_UUID_ERROR
    uuid_unique_error = LDEC.R_SELECT_POPUP_UUID_UNIQUE_ERROR
    name_error = LDEC.R_SELECT_POPUP_NAME_FAILED
    name_unique_error = LDEC.R_SELECT_POPUP_NAME_NOT_UNIQUE

    '''
    @description: 检查显示弹窗按钮打开时是否有选择页面
    @Date: 2021-04-26 13:50:22
    @LastEditors: lv
    @param {*} self
    '''
    # 弹窗按钮会自动生成页面并选择
    # @checker.run
    # def check_modal_page(self):
    #     attr = RSelectPopup.ATTR.MODAL_PAGE
    #     return_code = LDEC.R_SELECT_POPUP_MODAL_PAGE_LOST
    #     page_name = "modal_page"
    #     self._check_relation_page(page_name, attr, return_code)

    '''
    @description: 检查显示查看按钮打开是是否有选择页面
    @Date: 2021-04-27 10:08:29
    @LastEditors: lv
    @param {*} self
    '''
    @checker.run
    def check_view_page(self):
        attr = RSelectPopup.ATTR.VIEW_PAGE
        return_code_lose = LDEC.R_SELECT_POPUP_VIEW_PAGE_LOST
        return_code_delete = LDEC.R_SELECT_POPUP_VIEW_PAGE_IS_DELETE
        page_name = "view_page"
        self._check_relation_page(
            page_name, attr, return_code_lose, return_code_delete)

    @checker.run
    def check_pop_field(self):
        attr = RSelectPopup.ATTR.POP_FIELD
        return_code = LDEC.R_SELECT_POPUP_FIELD_NOT_SELECT
        popup_field = self.element.get(
            "modal_page", {}).get("modal_fields", [])
        if not popup_field:
            return
        for field_info in popup_field:
            self.update_reference_by_field_info(field_info, self.element, attr)
            if not field_info.get("field"):
                self._add_error_list(
                    attr, return_code=return_code, element_data=self.element)

    @checker.run
    def update_select_page(self):
        from apps.engine import engine
        component_type = ComponentType.DATALIST
        app_relationship_dict = self.app_relationship_dict
        create_service = engine.service.page_create.create_service(
            component_type)
        select_field = self.element.get(
            "modal_page", {}).get("modal_fields", [])
        if not select_field:
            self.element["select_page"] = {}
        if not self.element.get("select_page"):
            field_list = []
            search_items = []
            for field_info in select_field:
                self.build_field_dict(
                    field_info, field_list, app_relationship_dict, search_items)
            search_operation_relation = self.element.get("modal_page", {}).get(
                "search_operation_relation", 0)
            popup_search_bar = {
                "operation_relation": search_operation_relation,
                "show_search_button": True, "show_delete_button": True,
                "funcbar_search_button_text": "搜索", "items": search_items
            }
            element = self.element
            # 父组件可能是表单或数据列表
            element_field = element.get(
                "field_info") or element.get("edit_field")
            field_model = element_field.get("model")
            relationship_finder = self.page_finder.relationship_finder
            if not field_model:
                return False
            # 这里可能会导致model_instance为None
            app_log.info(f"field_model: {field_model}")
            model_instance = relationship_finder.model_uuid_map.get(
                field_model)
            model_display_name = model_instance.display_name
            create_service.field_list = field_list
            page_title = f"弹窗{model_display_name}"
            create_service_info = {
                "page_name": page_title, "page_title": page_title,
                "editable": True, "relationship_list": [],
                "model_uuid": field_model, "open_type": 0,
                "popup_search_bar": popup_search_bar
            }
            attr = self.attr_class.POP_FIELD
            for search_item in search_items:
                fieldsValue = search_item.get("fieldsValue")
                self.update_reference_by_value_edit(
                    fieldsValue, search_item, attr)
            for key, attr in create_service_info.items():
                setattr(create_service, key, attr)
            page = create_service.create_component(request_component_type=True)
            page_content = page.to_dict()
            children_list = page_content.get("children")
            data_list = children_list[0]
            is_visible = FormVisible()
            data_list["visible"] = {"is_visible": is_visible.to_dict()}
            data_list["editable"] = {
                "is_monitor": True, "once": True, "type": 0, "uuid": lemon_uuid(), "value": True}
            data_list["show_checkbox_select"] = True
            self.element["select_page"] = page_content
            # app_log.info(create_service)
            p = element_field.get("path")[0]
            _, _, is_many, _ = relationship_finder.lemon_association(
                p, self.model_uuid)
            if is_many:
                is_many = 1
            else:
                is_many = 0
            data_list["data_source"]["is_many"] = is_many
            candidate_preprocessing = self.element.get(
                "candidate_preprocessing", dict())
            if candidate_preprocessing:
                data_list["data_source"]["preprocessing"] = candidate_preprocessing

    @checker.run
    def check_r_select_page(self):

        return_modal_page_code_lose = LDEC.R_SELECT_POPUP_MODAL_PAGE_LOST
        return_modal_page_code_delete = LDEC.R_SELECT_POPUP_MODAL_PAGE_IS_DELETE
        self.check_r_select_page_by_name(
            return_modal_page_code_lose, return_modal_page_code_delete, page_name="modal_view_page")

    def check_r_select_page_by_name(self, return_code_lose, return_code_delete, page_name="modal_view_page"):
        attr = RSelectPopup.ATTR.VIEW_PAGE
        return_code_model_error = LDEC.DATALIST_SELECT_PAGE_MODEL_ERROR
        return_open_page_error = LDEC.VIEW_PAGE_CANNOT_JUMP
        modal_view_page_info = self.element.get(page_name)
        if isinstance(modal_view_page_info, dict) and modal_view_page_info.get("show"):
            page_uuid = modal_view_page_info.get("page", "")
            if page_uuid:
                self.update_page_reference(
                    page_uuid, self.document_name, self.element_uuid, attr)
                page = self.app_page_dict.get(page_uuid, {})
                component_model_uuid = self.element.get("field_info", {}).get("model") or \
                    self.element.get("edit_field", {}).get("model", {})
                if page and page.get("is_delete"):
                    self._add_error_list(attr, return_code=return_code_delete)
                else:
                    self.update_page_reference(
                        page_uuid, self.document_name, self.element_uuid, attr)
                    page_dict = self.app_page_dict.get(page_uuid, {})
                    open_type = page_dict.get("open_type")
                    container_model = page_dict.get(
                        PageModel.container_model.name)
                    find_same_model = False
                    for _, container_dict in container_model.items():
                        model = container_dict.get("model")
                        if model == component_model_uuid:
                            find_same_model = True
                            break
                    if not find_same_model and not page_dict.get(PageModel.is_delete.name):
                        self._add_error_list(
                            attr, return_code=return_code_model_error)
                    if open_type == OpenPageType.JUMP:
                        self._add_error_list(
                            attr, return_code=return_open_page_error)
            else:
                self._add_error_list(attr, return_code=return_code_lose)

    @checker.run
    def check_field_reference(self):
        attr = RSelectPopup.ATTR.TYPE
        path = self.element.get("field_info", {}).get("path")
        if not path:
            # 数据列表的列为可编辑时，输入组件为关联弹窗的情况下字段名称不一致
            path = self.element.get("edit_field", {}).get("path")
            if not path:
                return_code = LDEC.R_SELECT_POPUP_PATH_NOT_SELECT
                self._add_error_list(attr, return_code=return_code)

    @checker.run
    def update_is_required(self):

        element = self.element
        element_field = element.get("field_info") or element.get("edit_field")
        field_placeholder = element_field.setdefault("placeholder", {})
        relationship_finder = self.page_finder.relationship_finder
        paths = element_field.get("path")
        if paths:
            p = paths[0]
            relationship = relationship_finder.relationship_basic_map.get(p, {
            })

            edit_field_uuid = element_field.get("uuid")
            field_dict = self.app_field_dict.get(edit_field_uuid) or {}
            field_model_uuid = field_dict.get(ModelField.model_uuid.name)
            if relationship:
                relation_extra = relationship.extra
                if relationship.source_model == field_model_uuid:
                    is_required = relation_extra.get(
                        "source_required", False)
                else:
                    is_required = relation_extra.get(
                        "target_required", False)
                field_placeholder.update({"is_required": is_required})

    @checker.run
    def check_candidates_preprocessing_field(self):
        attr = RSelectPopup.ATTR.CANDIDATE_PREPROCESSING
        preprocessing = self.element.get("candidate_preprocessing")
        self.check_preprocessing_info(attr, preprocessing)


class RTileCheckerService(BaseRelationInputCheckerService):

    attr_class = RTile.ATTR
    uuid_error = LDEC.R_TILE_UUID_ERROR
    uuid_unique_error = LDEC.R_TILE_UUID_UNIQUE_ERROR
    name_error = LDEC.R_TILE_NAME_FAILED
    name_unique_error = LDEC.R_TILE_NAME_NOT_UNIQUE

    @checker.run
    def check_candidates_preprocessing_field(self):
        attr = RTile.ATTR.CANDIDATE_PREPROCESSING
        preprocessing = self.element.get("candidate_preprocessing")
        self.check_preprocessing_info(attr, preprocessing)


class RTreeCheckerService(BaseRelationInputCheckerService):

    attr_class = RTree.ATTR
    uuid_error = LDEC.R_TREE_UUID_ERROR
    uuid_unique_error = LDEC.R_TREE_UUID_UNIQUE_ERROR
    name_error = LDEC.R_TREE_NAME_FAILED
    name_unique_error = LDEC.R_TREE_NAME_NOT_UNIQUE

    @checker.run
    def check_data_source(self):
        attr = self.attr_class.DATA_SOURCE
        data_source = self.element.get("data_source", {})
        if data_source:
            data_source_type = data_source.get("type")
            if data_source_type == DataSourceType.RTREE_SELF_REFERENTIAL:
                association = data_source.get("association", {})
                if not association or not association.get("path"):
                    self._add_error_list(
                        attr, return_code=LDEC.SELF_ASSOCIATION_NOT_SELECT)
                elif association:
                    relationship_uuid = association.get("relationship_uuid")
                    if not relationship_uuid:
                        return_code = LDEC.R_TREE_RELATION_NOT_FOUND
                        self._add_error_list(attr, return_code=return_code)
        else:
            self.element.update(
                {"data_source": {"type": DataSourceType.RTREE_HIERARCHY}})


class RCascadeCheckerService(BaseRelationInputCheckerService):

    attr_class = RCascade.ATTR
    uuid_error = LDEC.R_CASCADE_UUID_ERROR
    uuid_unique_error = LDEC.R_CASCADE_UUID_UNIQUE_ERROR
    name_error = LDEC.R_CASCADE_NAME_FAILED
    name_unique_error = LDEC.R_CASCADE_NAME_NOT_UNIQUE


class RSelectTableCheckerService(BaseRelationInputCheckerService):

    attr_class = RSelectTable.ATTR
    uuid_error = LDEC.R_SELECT_TABLE_UUID_ERROR
    uuid_unique_error = LDEC.R_SELECT_TABLE_UUID_UNIQUE_ERROR
    name_error = LDEC.R_SELECT_TABLE_NAME_FAILED
    name_unique_error = LDEC.R_SELECT_TABLE_NAME_NOT_UNIQUE


class RCreateTableCheckerService(BaseRelationInputCheckerService):

    attr_class = RCreateTable.ATTR
    uuid_error = LDEC.R_CREATE_TABLE_UUID_ERROR
    uuid_unique_error = LDEC.R_CREATE_TABLE_UUID_UNIQUE_ERROR
    name_error = LDEC.R_CREATE_TABLE_NAME_FAILED
    name_unique_error = LDEC.R_CREATE_TABLE_NAME_NOT_UNIQUE


class CalendarCheckerService(ComponentCheckerService):

    attr_class = Color.ATTR
    uuid_error = LDEC.CALENDAR_UUID_ERROR
    uuid_unique_error = LDEC.CALENDAR_UUID_UNIQUE_ERROR
    name_error = LDEC.CALENDAR_NAME_FAIELD
    name_unique_error = LDEC.CALENDAR_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.model_uuid = getattr(self, "model_uuid", None)

    @checker.run
    def check_field_type(self):
        attr = Calendar.ATTR.DATE_FIELD
        data_source = self.element.get("data_source", {})
        association = data_source.get("association")
        field_info = data_source.get("date_field", {})
        self._update_field_placeholder(field_info)
        data_source["date_field"] = field_info
        self.element["data_source"] = data_source
        date_field_set = False
        if field_info:
            self.update_reference_by_field_info(field_info, self.element, attr)
            field_uuid = field_info.get("uuid")
            if field_uuid:
                date_field_set = True
                field_dict = self.app_field_dict.get(field_uuid, dict())
                if field_dict:
                    field_type = field_dict.get(ModelField.field_type.name)
                    if data_source.get("method") == 1:
                        if field_type != FieldType.DATETIME:
                            return_code = LDEC.CALENDAR_DATE_FIELD_TYPE_ERROR
                            return_code.message = return_code.message.format(
                                name="时间日期")
                            self._add_error_list(attr, return_code=return_code)
                    else:
                        if field_type != FieldType.DATETIME_CYCLE:
                            return_code = LDEC.CALENDAR_DATE_FIELD_TYPE_ERROR
                            return_code.message = return_code.message.format(
                                name="时间循环")
                            self._add_error_list(attr, return_code=return_code)
                else:
                    return_code = LDEC.CALENDAR_DATE_FIELD_NOT_EXISTS
                    self._add_error_list(attr, return_code=return_code)
        if date_field_set is False and not association:
            return_code = LDEC.CALENDAR_DATE_FIELD_NOT_SET
            self._add_error_list(attr, return_code=return_code)

    # 检查数据源是否绑定
    @checker.run
    def check_datasource_exists(self):
        self._check_datasource_exists()


class FormCheckerService(ComponentCheckerService):

    attr_class = Form.ATTR
    uuid_error = LDEC.FORM_UUID_ERROR
    uuid_unique_error = LDEC.FORM_UUID_UNIQUE_ERROR
    name_error = LDEC.FORM_NAME_FAILED
    name_unique_error = LDEC.FORM_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.kwargs.update({"model_uuid": self.model_uuid})
        self.app_func_uuid_dict = self.kwargs.get("app_func_uuid_dict", {})
        self.module_tag_dict = self.kwargs.get("module_tag_dict", {})
        self.children = self.element.get("children", list())

    def check_form_in_cardlist(self):
        if self.kwargs.get("is_cardlist", False) is True:
            attr = Cardlist.ATTR.COLUMN
            self._add_error_list(
                attr=attr, return_code=LDEC.FORM_IN_CARDLIST_NOT_ALLOW)
            return True
        return False

    # 检查 表单 的子组件
    def check_children(self, uuid_set, name_set, data_container=True):
        self._prepare_children_datasource_check()
        self._check_children(
            self.children, uuid_set, name_set, data_container=data_container, **self.kwargs)

    @checker.run
    def check_buttons(self):
        buttons = self.element.get("buttons", [])
        kwargs = {}
        kwargs.update(self.kwargs)
        kwargs.update({
            "model_in_page": self.model_in_page,
            "app_page_dict": self.app_page_dict,
            "app_workflow_list": self.app_workflow_list,
            "parent": {"element_uuid": self.element_uuid, "model_uuid": self.model_uuid},
            "app_func_uuid_dict": self.app_func_uuid_dict,
            "module_tag_dict": self.module_tag_dict
        })
        for btn in buttons:
            btn_type = btn.get("type")
            btn_service_class = self.form_button_service_dict.get(
                btn_type, None)
            if btn_service_class:
                btn_service = btn_service_class(
                    self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, self.document_name,
                    btn, document_other_info=self.document_other_info, **kwargs)
                btn_service.document_other_info = self.document_other_info
                btn_service.check_all()
                self.update_any_list(btn_service)

    @checker.run
    def check_datasource_exists(self):
        self._check_datasource_exists()

    @checker.run
    def check_is_paginated(self):
        self._check_is_paginated()

    @checker.run
    def check_datasource_type(self):
        attr = Form.ATTR.DATASOURCE
        self._check_datasource_type_multiple(attr)

    @checker.run
    def check_relate_to_approvalflow(self):
        # 检查页面是否关联到审批流
        for wf in self.app_workflow_list:
            wf_type = wf.get("type")
            if wf_type == WorkflowType.SIMPLE:
                pc_from = wf.get("pc_from")
                pad_from = wf.get("pad_from")
                mobile_from = wf.get("mobile_from")
                if self.document_other_info.get("uuid") in [pc_from, pad_from, mobile_from]:
                    self.element.update({"relate_approvalflow": True})
                    break
        else:
            self.element.update({"relate_approvalflow": False})

    @checker.run
    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_cloud_event(attr)

    @checker.run
    def check_input_col(self):
        label_col = self.element.get("label_col")
        wrapper_col = self.element.get("wrapper_col")
        if isinstance(label_col, int) and isinstance(wrapper_col, int) and label_col + wrapper_col > 24:
            attr = Page.ATTR.INPUT_CONTROL
            self._add_error_list(
                attr=attr, return_code=LDEC.FORM_INPUT_COL_ERROR)


class PresentComponentCheckService(ComponentCheckerService):

    def initialize(self):
        self.data_source = self.element.get("data_source")
        return super().initialize()

    def _check_model(self, attr, return_code):
        if not self.data_source.get("model"):
            return_code.message = return_code.message.format(
                name=self.element_name)
            self._add_error_list(
                attr, return_code=return_code, element_data=self.element)
            return False
        else:
            attr = self.get_attr_by_name("DATASOURCE")
            self.update_reference_by_data_source(
                self.data_source, self.element, attr)
        return True


class TextCheckService(PresentComponentCheckService):
    style_class = TextStyle

    def initialize(self):
        super().initialize()
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.kwargs.update({"model_uuid": self.model_uuid})
        self.page_finder = PageFinder(
            self.model_in_page, except_form=False, count_container=True,
            find_association=True, model_list=self.app_model_list,
            relationship_list=self.app_relationship_list,
            field_list=self.app_field_list)
        self.page_finder.find_func(self.element)
        self.relationship_finder = self.page_finder.relationship_finder

    def _get_check_parent_model(self):
        parent_type = self.parent.get("type")
        if parent_type in [
            ComponentType.DATALIST, ComponentType.CARDLIST,
            ComponentType.DATAGRID, ComponentType.FORM,
            ComponentType.TREELIST, ComponentType.CALENDAR
        ]:
            return True
        return False

    @checker.run
    def check_value_edit(self):
        attr = TextAttr.DATASOURCE
        value_edit = self.data_source.get("valueEdit")
        check_parent_model = self._get_check_parent_model()
        self._check_value_editor(
            value_edit, attr, self.app_field_dict, self.app_relationship_dict,
            r_finder=self.relationship_finder, check_parent_model=check_parent_model)

    @checker.run
    def check_title(self):
        attr = TextAttr.TITLE
        show_title = self.element.get("show_title", False)
        if show_title:
            value_edit = self.element.get("title")
            check_parent_model = self._get_check_parent_model()
            self._check_value_editor(
                value_edit, attr, self.app_field_dict, self.app_relationship_dict,
                r_finder=self.relationship_finder, check_parent_model=check_parent_model)    

    # 查看多端关联字段是否有聚合函数 // 0  一对多  1  多对多  2  一对一
    @checker.run
    def check_aggre_func(self, **kwarg):
        attr = TextAttr.DATASOURCE
        return_code = LDEC.MULT_FIELD_REQUIRE_AGGRE_FUNC
        data_source = self.element.get("data_source", {})
        valueEdit = data_source.get("valueEdit")

        if valueEdit:
            aggre_func = valueEdit.get("aggre_func", None)
            path_list = valueEdit.get("path", [])
            is_many = False
            if aggre_func is None:
                field_dict = self.app_field_dict.get(
                    valueEdit.get("field"), dict())
                field_type = field_dict.get(ModelField.field_type.name)
                if field_type in ComponentType.AGG_FUNC_TO_FIELD.get(0):
                    r_finder = self.page_finder.relationship_finder
                    model_uuid = self.parent.get("model_uuid")
                    for path in path_list:
                        # 前端传回的 path 需要按顺序传回，否则可能会影响结果
                        field_model, _, is_many, _ = r_finder.lemon_association(
                            path, model_uuid)
                        if field_model:
                            model_uuid = field_model.uuid
                            if is_many is True:
                                self._add_error_list(
                                    attr, return_code=return_code, element_data=self.element)
                                break

    @checker.run
    def check_badge(self):
        attr_badge = TextAttr.BADGE
        badge = self.element.get("badge", {})
        self._check_badge(attr_badge, badge)

    @checker.run
    def check_style(self):
        self.check_dynamic_definition()


class ScanCountCheckService(PresentComponentCheckService):

    @checker.run
    def check_exist(self):
        attr = ScanCountAttr.DATASOURCE
        return_code = LDEC.COMPONENT_REMOVED_PLEASE_dELETE
        self._add_error_list(
            attr, return_code=return_code)


class ReactCheckService(PresentComponentCheckService):

    @checker.run
    def check_datasource(self):
        attr = ReactAttr.DATASOURCE
        func_uuid = self.element.get("func_uuid")
        return_code = LDEC.FUNC_NOT_EXIST
        if not self.app_func_uuid_dict.get(func_uuid):
            self._add_error_list(
                attr, return_code=return_code)


class ScanCheckService(PresentComponentCheckService):

    @checker.run
    def check_datasource(self):
        attr = ScanAttr.DATASOURCE
        return_code = LDEC.SCAN_FIELD_REPEAT
        data_source = self.element.get("data_source", {})
        is_linkage = data_source.get("is_linkage")
        if is_linkage:
            model_uuid = data_source.get("model")
            field_uuid = data_source.get("field")
            if model_uuid:
                pass
            else:
                return_code = LDEC.SCAN_MODEL_NOT_FOUND
                self._add_error_list(attr, return_code=return_code)
            if field_uuid:
                field_dict = self.app_field_dict.get(field_uuid, dict())
                if field_dict:
                    field_model_uuid = field_dict.get(
                        ModelField.model_uuid.name)
                    if model_uuid:
                        if field_model_uuid != model_uuid:
                            return_code = LDEC.SCAN_FIELD_AND_MODEL_NOT_EQUAL
                            self._add_error_list(attr, return_code=return_code)
                else:
                    return_code = LDEC.SCAN_FIELD_NOT_EXISTS
                    self._add_error_list(attr, return_code=return_code)
            else:
                return_code = LDEC.SCAN_FIELD_NOT_FOUND
                self._add_error_list(attr, return_code=return_code)

    @checker.run
    def check_event(self):
        attr = ScanAttr.EVENT
        self._check_event(attr)


class TimeLineCheckService(PresentComponentCheckService):

    def check_data_model(self):
        datasource_field = TimeLineAttr.DATASOURCE_FIELD
        attr = TimeLineAttr.DATASOURCE
        return_code = LDEC.DATASOURCE_DOESNOT_EXIST
        if self._check_model(attr, return_code):
            for timeline_field in datasource_field:
                if timeline_field not in self.data_source:
                    return_code = LDEC.TIMELINE_FIELD_NOT_EXIST
                    return_code.message = return_code.message.format(
                        name=self.element_name, field_name=datasource_field[timeline_field])
                    self._add_error_list(
                        attr, return_code=return_code, element_data=self.element)


class PresentImageCheckService(PresentComponentCheckService):

    attr_class = ImageShow.ATTR
    uuid_error = LDEC.IMAGE_SHOW_UUID_ERROR
    uuid_unique_error = LDEC.IMAGE_SHOW_UUID_UNIQUE_ERROR

    def initialize(self):
        super().initialize()
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.kwargs.update({"model_uuid": self.model_uuid})
        self.children = self.element.get("children", list())

    @checker.run
    def check_field(self):
        attr = PresentImageAttr.DATASOURCE
        type = self.data_source.get("type")
        if type == DataSourceType.Image.IMAGE_TABLE:
            if not self.data_source.get("image_name"):
                return_code = LDEC.IMAGE_NOT_EXIST
                self._add_error_list(
                    attr, return_code=return_code, element_data=self.element)
        elif type == DataSourceType.Image.IMAGE_FIELD:
            # TODO 感觉有很多地方都是这样做,考虑复用代码
            field_info = self.data_source.get("field_info", {})
            field_uuid = field_info.get("uuid", "")
            if not field_uuid:
                # 没选字段
                return_code = LDEC.IMAGE_NOT_EXIST
                self._add_error_list(attr, return_code=return_code)
            else:
                field_basic_info = self.app_field_dict.get(field_uuid)
                if not field_basic_info:
                    # 字段不存在
                    return_code = LDEC.MODEL_FIELD_NOT_EXISTS
                    self._add_error_list(attr, return_code=return_code)
                else:
                    title = self.element.get("name")
                    element_uuid = self.element.get("uuid")
                    self.update_field_reference(
                        field_uuid, title, element_uuid, attr)
                    field_type = field_basic_info.get("field_type")
                    if field_type not in [FieldType.ENUM, FieldType.IMAGE]:
                        # 只能选图片 枚举
                        return_code = LDEC.IMAGE_TYPE_ERROR
                        self._add_error_list(attr, return_code=return_code)
        else:
            if not self.data_source.get("code_data"):
                return_code = LDEC.CODE_NOT_EXIST
                self._add_error_list(
                    attr, return_code=return_code, element_data=self.element)

    @checker.run
    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_event(attr)

    @checker.run
    def check_badge(self):
        attr_badge = self.attr_class.BADGE
        badge = self.element.get("badge", {})
        self._check_badge(attr_badge, badge)


class TagCheckService(PresentComponentCheckService):
    style_class = PresentTagStyle

    def check_value_edit(self):
        attr = PresentTagAttr.DATASOURCE
        value_edit = self.data_source.get("valueEdit")
        self._check_value_editor(
            value_edit, attr, self.app_field_dict, self.app_relationship_dict)

    @checker.run
    def check_style(self):
        self.check_dynamic_definition()


class ProgressCheckService(PresentComponentCheckService):

    def _check_field(self, attr, datasource_field):
        datasource_field = PresentCircleProgressAttr.DATASOURCE_FIELD
        attr = PresentCircleProgressAttr.DATASOURCE
        for field in datasource_field:
            if field not in self.data_source:
                return_code = LDEC.PROGRESS_FIELD_NOT_EXIST
                return_code.message = return_code.message.format(
                    name=self.element_name, field_name=datasource_field[field])
                self._add_error_list(
                    attr, return_code=return_code, element_data=self.element)


class LineBarCheckService(ProgressCheckService):

    def check_field(self):
        datasource_field = PresentProgressAttr.DATASOURCE_FIELD
        attr = PresentProgressAttr.DATASOURCE
        self._check_field(attr, datasource_field)


class RingBarCheckService(ProgressCheckService):

    def check_field(self):
        datasource_field = PresentCircleProgressAttr.DATASOURCE_FIELD
        attr = PresentCircleProgressAttr.DATASOURCE
        self._check_field(attr, datasource_field)


class CustomPresentCheckService(PresentComponentCheckService):

    @checker.run
    def check_custom(self):
        self._check_custom()


class FileCheckService(PresentComponentCheckService):

    @checker.run
    def check_field(self):
        attr = PresentFileAttr.FIELD_INFO
        return_code = LDEC.FILE_FIELD_NOT_EXIST
        if self.data_source:
            source_type = self.data_source.get("type")
            if source_type == FileSourceType.FIELD:
                field_info = self.data_source.get("field_info")
            elif source_type == FileSourceType.LINK:
                self.check_link_data_source(attr)
                field_info = None
            else:
                field_info = None
        else:
            if not self.element.get("field_info"):
                self._add_error_list(
                    attr, return_code=return_code, element_data=self.element)
                field_info = None
            else:
                field_info = self.element.get("field_info")
        if field_info is not None:
            field_uuid = field_info.get("uuid", "")
            if not field_uuid:
                # 没选字段
                return_code = LDEC.FILE_FIELD_NOT_EXIST
                self._add_error_list(attr, return_code=return_code)
            else:
                field_basic_info = self.app_field_dict.get(field_uuid)
                if not field_basic_info:
                    # 字段不存在
                    return_code = LDEC.MODEL_FIELD_NOT_EXISTS
                    self._add_error_list(attr, return_code=return_code)
                else:
                    title = self.element.get("name")
                    element_uuid = self.element.get("uuid")
                    self.update_field_reference(
                        field_uuid, title, element_uuid, attr)
                    field_type = field_basic_info.get("field_type")
                    if field_type not in [FieldType.FILE]:
                        # 只能选文件
                        return_code = LDEC.FILE_TYPE_ERROR
                        self._add_error_list(attr, return_code=return_code)

    def check_link_data_source(self, attr):
        value_edit = self.data_source.get("valueEdit")
        self._check_value_editor(
            value_edit, attr, self.app_field_dict, self.app_relationship_dict)


class ButtonCheckService(ComponentCheckerService):
    attr_class = Button.ATTR
    style_class = Button.STYLE
    uuid_error = LDEC.BUTTON_UUID_ERROR
    uuid_unique_error = LDEC.BUTTON_UUID_UNIQUE_ERROR

    def initialize(self):
        super().initialize()
        self.container_model_uuid = getattr(self, "container_model_uuid", None)
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.parent = getattr(self, "parent", {})
        if not self.model_dict:
            parent_element_uuid = self.parent.get("element_uuid")
            parent_uuid = self.old_component_dict.get(
                parent_element_uuid) or parent_element_uuid
            self.model_dict = self.model_in_page.get(parent_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.kwargs.update({"model_uuid": self.model_uuid})
        self.children = self.element.get("children", list())
        self.app_excel_template_dict = self.kwargs.get(
            "app_excel_template_dict", {})

    @checker.run
    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_event(attr)

    def _check_button_page(
            self, button_dict, attr, page_not_exists_code, form_count_error_code,
            form_model_error_code, page_not_select_code):
        if button_dict and isinstance(button_dict, dict):
            function_bar = button_dict.get("function_bar", {})
            edit_type = function_bar.get("edit_type")
            page_title = function_bar.get("page_title", {})
            page_uuid = function_bar.get("page")
            if page_uuid:
                self._check_button_page_with_page_uuid(
                    page_uuid, attr, page_not_exists_code, form_count_error_code,
                    form_model_error_code, button_dict=button_dict)
            else:
                if edit_type == AddButtonEditType.open_new_page:
                    return_code = page_not_select_code
                    self._add_error_list(
                        attr, return_code=return_code, element_data=button_dict)
                if self.element.get("type") == ComponentType.EDIT_BUTTON:
                    if not function_bar.get("is_inline"):
                        if not page_uuid:
                            return_code = page_not_select_code
                            self._add_error_list(
                                attr, return_code=return_code, element_data=button_dict)

            if edit_type == AddButtonEditType.open_new_page:
                if page_title:
                    self._check_value_editor(
                        page_title, attr, self.app_field_dict, self.app_relationship_dict,
                        r_finder=self.page_finder.relationship_finder)
            if self.element.get("type") == ComponentType.EDIT_BUTTON:
                if not function_bar.get("is_inline") and page_title:
                    self._check_value_editor(
                        page_title, attr, self.app_field_dict, self.app_relationship_dict,
                        r_finder=self.page_finder.relationship_finder)

    def get_available(self) -> bool:
        # 根据按钮属性-常规-是否可用确定按钮设置文档检查是否启用
        is_available = True
        disabled = self.element.get("disabled", {})
        disabled_type = disabled.get("type")
        if disabled_type == 0:
            disabled_value = disabled.get("value")
            if isinstance(disabled_value, bool):
                is_available = disabled_value
            elif isinstance(disabled_value, str):
                is_available = disabled_value != ""
            elif disabled_value == 0:
                is_available = False
        return is_available

    @checker.run
    def check_style(self):
        self._check_normal_display()
        self.check_dynamic_definition()

    def _check_normal_display(self):
        button_style = self.source_element.get("style", {})
        self._check_color_value_editor(button_style, "fontColor")
        self._check_color_value_editor(button_style, "backgroundColor")

    def _check_color_value_editor(self, style, field):
        normal_display = self.style_class.NORMAL_DISPLAY
        color_dict = style.get(field, {})
        color_type = color_dict.get("type")
        if color_type == "2":
            color_editor = color_dict.get("color")
            self.app_relationship_dict = getattr(
                self, "app_relationship_dict", {})
            self._check_value_editor(
                color_editor, normal_display, self.app_field_dict, self.app_relationship_dict,
                position=normal_display)

    @checker.run
    def check_badge(self):
        attr_badge = self.attr_class.BADGE
        badge = self.element.get("badge", {})
        self._check_badge(attr_badge, badge)

    def _check_excel_document_model(self, return_code):
        attr = Datalist.ATTR.FUNCTION_BAR
        excel_document = self.element.get("excel_document")
        parent = self.parent.get("element_uuid")
        if parent is not None and excel_document in self.app_excel_template_dict:
            model = self.app_excel_template_dict.get(excel_document, {})
            parent_data_source_type = self.element.get(
                "parent_data_source_type")
            if model != self.container_model_uuid and parent_data_source_type not in [DataSourceType.MODEL_WITH_PRE]:
                return_code = return_code
                self._add_error_list(attr, return_code=return_code)

    @checker.run
    def check_title_value_editor(self):
        attr = self.attr_class.COMMON
        position = self.attr_class.COMMON
        title_value_editor = self.element.get("title")
        if title_value_editor:
            self._check_value_editor(
                title_value_editor, attr, self.app_field_dict, self.app_relationship_dict, position=position)


class SearchButtonCheckService(ButtonCheckService):

    def initialize(self):
        super().initialize()

    def _check_search_fields(self, search_data: dict, attr: str, search_type=ButtonSearchType.GENERAL):
        show: bool = search_data.get("show", False)
        fields: list = search_data.get("fields", [])
        if search_type == ButtonSearchType.GENERAL:
            if show and not fields:
                return_code = LDEC.SEARCH_FIELD_NOT_EXISTS
                return_code.message = return_code.message.format(name=attr)
                return_code.position = attr
                self._add_error_list(attr=attr, return_code=return_code)
        if not fields or not show:
            return
        for field_info in fields:
            field_uuid = field_info.get("field")
            field_dict = self.app_field_dict.get(field_uuid, {})
            if not field_dict:
                return_code = LDEC.SEARCH_FIELD_NOT_EXISTS
                return_code.message = return_code.message.format(name=attr)
                return_code.position = attr
                self._add_error_list(
                    attr=attr, return_code=return_code)
            if isinstance(field_dict, dict):
                field_type = field_dict.get(ModelField.field_type.name)
                calculate_field = all([
                    field_dict.get("calculate_field"),
                    field_dict.get("calculate_type") != CalculateType.GENERATED_COLUMN])
                if any([field_type in [FieldType.DATETIME_CYCLE, FieldType.FILE, FieldType.IMAGE],
                        calculate_field]):
                    app_log.info(
                        f"field_type: {field_type}, calculate_field: {calculate_field}")
                    self._add_error_list(
                        attr, return_code=LDEC.SEARCH_ITEM_ONLY_NORMAL_FIELD)

            path = field_info.get('path', list())
            parent_model = self.container_model_uuid
            for p in path:
                r_finder = self.page_finder.relationship_finder
                associaton_model = None
                try:
                    associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                        p, parent_model)
                except (BaseException, Exception):
                    pass
                if associaton_model is None:
                    return_code = LDEC.VISUALIZATION_SEARCH_RELATIONSHIP_DOESNOT_EXIST  # 关联关系不存在
                    return_code.message = return_code.message.format(
                        name=attr)
                    self._add_error_list(
                        attr=attr, return_code=return_code)

    def _check_search_bar(self):
        attr = self.attr_class.SEARCH_ITEM
        search_bar: dict = self.element.get("search_bar", {})
        show = search_bar.get("show", True)  # 兼容处理，之前只有这种搜索方式
        search_items: list = search_bar.get("items", [])
        if not search_items or not show:
            return
        for item in search_items:
            operator = item.get("operator")
            field_value_list = item.get('field_value_list', [])  # 新版本
            fieldsValue = item.get('fieldsValue', [])  # 老版本
            if not field_value_list:
                if isinstance(fieldsValue, list):
                    field_value_list = fieldsValue
                else:
                    field_value_list = [fieldsValue]
            for field_info in field_value_list:
                field_type = field_info.get("field_type")
                calculate_field = all([
                    field_info.get("calculate_field"),
                    field_info.get("calculate_type") != CalculateType.GENERATED_COLUMN])
                app_log.info(f"calculate_field: {calculate_field}")
                self.update_reference_by_value_edit(
                    field_info, self.element, self.attr_class.SEARCH_ITEM)
                if any([field_type in [FieldType.DATETIME_CYCLE, FieldType.FILE, FieldType.IMAGE],
                        calculate_field]):
                    app_log.info(
                        f"field_type: {field_type}, calculate_field: {calculate_field}")
                    self._add_error_list(
                        attr, return_code=LDEC.SEARCH_ITEM_ONLY_NORMAL_FIELD)
            input_control = item.get("input_control")
            if input_control == "TREESELECT":
                data_source = item.get("data_source", {})
                data_source_type = data_source.get("type")
                if data_source_type not in [
                        DataSourceType.RTREE_HIERARCHY,
                        DataSourceType.RTREE_SELF_REFERENTIAL]:
                    self._add_error_list(
                        attr, return_code=LDEC.SEARCH_DATA_SOURCE_TYPE_NOT_FOUND)
                elif data_source_type == DataSourceType.RTREE_SELF_REFERENTIAL:
                    association_info = data_source.get("association", {})
                    path = association_info.get("path", [])
                    if not path:
                        self._add_error_list(
                            attr, return_code=LDEC.SEARCH_SELF_REFERENTIAL_PATH_ERROR)
            if input_control in ["SELECT", "RELATIONSELECT", "TREESELECT"]:
                operator = item.get("operator")
                if operator != OperatorType.CO:
                    self._add_error_list(
                        attr, return_code=LDEC.OPERATOR_TYPE_NOT_ALLOWED)
            self._check_event(attr, item)
            # 搜索按钮文档检查
            return_code = LDEC.VISUALIZATION_SEARCH_TERM_FIELDS_UNBOUND
            return_code.message = return_code.message.format(name=attr)
            if field_value_list:
                field_dict_info = self.app_field_dict
                for fields_i in field_value_list:
                    path = fields_i.get('path', list())
                    field_uuid_i = fields_i.get('field', '')
                    field_dict = field_dict_info.get(field_uuid_i, dict())
                    if not field_uuid_i:
                        self._add_error_list(
                            attr=self.attr_class.SEARCH_ITEM,
                            return_code=return_code)
                    # 判断绑定字段是否存在
                    elif not field_dict:
                        return_code = LDEC.VISUALIZATION_SEARCH_TERM_FIELDS_DOESNOT_EXIST
                        return_code.message = return_code.message.format(
                            name=attr)
                        self._add_error_list(
                            attr=self.attr_class.SEARCH_ITEM,
                            return_code=return_code)
                    # 判断是否绑定关联
                    elif path:
                        # parent_model = self.model_dict.get('model', '')
                        parent_model = self.container_model_uuid
                        for p in path:
                            r_finder = self.page_finder.relationship_finder
                            associaton_model = None
                            try:
                                associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                                    p, parent_model)
                            except (BaseException, Exception):
                                pass
                            if associaton_model is None:
                                return_code = LDEC.VISUALIZATION_SEARCH_RELATIONSHIP_DOESNOT_EXIST  # 关联关系不存在
                                return_code.message = return_code.message.format(
                                    name=attr)
                                self._add_error_list(
                                    attr=self.attr_class.SEARCH_ITEM, return_code=return_code)
            else:
                self._add_error_list(
                    attr=self.attr_class.SEARCH_ITEM,
                    return_code=return_code)

    def _check_general_search(self):
        general_search: dict = self.element.get("general_search", {})
        self._check_search_fields(
            general_search, self.attr_class.GENERAL_SEARCH)

    def _check_free_search(self):
        search_type = ButtonSearchType.FREE
        free_search: dict = self.element.get("free_search", {})
        self._check_search_fields(
            free_search, self.attr_class.FREE_SEARCH, search_type=search_type)

    @checker.run
    def check_field_type(self):
        self._check_search_bar()
        self._check_general_search()
        self._check_free_search()


class NewButtonCheckService(ButtonCheckService):

    @checker.run
    def check_button(self):
        attr = Datalist.ATTR.FUNCTION_BAR
        is_available = self.get_available()
        if is_available:
            self._check_button_page(
                self.element, attr,
                LDEC.DATALIST_NEW_BUTTON_PAGE_NOT_EXISTS,
                LDEC.DATALIST_NEW_BUTTON_PAGE_FORM_COUNT_ERROR,
                LDEC.DATALIST_NEW_BUTTON_PAGE_FORM_MODEL_ERROR,
                LDEC.DATALIST_NEW_BUTTON_PAGE_NOT_SELECT
            )
            self._check_all_uneditable()

    def _check_all_uneditable(self):
        # 检查当所有列都不可编辑, 无法首行, 末行新建
        function_bar = self.element.get("function_bar", {})
        edit_type = function_bar.get("edit_type")
        attr = Datalist.ATTR.FUNCTION_BAR
        if self.element.pop("all_uneditable", False) is True:
            if edit_type in [1, 2]:
                return_code = LDEC.DATALIST_NEW_BUTTON_CANNOT_NEW_INLINE
                self._add_error_list(attr, return_code=return_code)


class DeleteButtonCheckService(ButtonCheckService):

    ...


class AddButtonCheckService(ButtonCheckService):
    ...


class RemoveButtonCheckService(ButtonCheckService):
    ...


class EditButtonCheckService(ButtonCheckService):

    @checker.run
    def check_button(self):
        attr = Datalist.ATTR.FUNCTION_BAR
        is_available = self.get_available()
        if is_available:
            self._check_button_page(
                self.element, attr,
                LDEC.DATALIST_EDIT_BUTTON_PAGE_NOT_EXISTS,
                LDEC.DATALIST_EDIT_BUTTON_PAGE_FORM_COUNT_ERROR,
                LDEC.DATALIST_EDIT_BUTTON_PAGE_FORM_MODEL_ERROR,
                LDEC.DATALIST_EDIT_BUTTON_PAGE_NOT_SELECT
            )
            self._check_all_uneditable()

    def _check_all_uneditable(self):
        # 检查当所有列都不可编辑, 无法首行, 末行新建
        function_bar = self.element.get("function_bar", {})
        is_inline = function_bar.get("is_inline")
        attr = Datalist.ATTR.FUNCTION_BAR
        if self.element.pop("all_uneditable", False) is True:
            if is_inline:
                return_code = LDEC.DATALIST_EDIT_BUTTON_CANNOT_EDIT_INLINE
                self._add_error_list(attr, return_code=return_code)


class ImportButtonCheckService(ButtonCheckService):

    @checker.run
    def check_excel_document_model(self):
        return_code = LDEC.DATALIST_IMPORT_BUTTON_MODEL_ERROR
        self._check_excel_document_model(return_code)


class ExportButtonCheckService(ButtonCheckService):

    @checker.run
    def check_excel_document_model(self):
        return_code = LDEC.DATALIST_EXPORT_BUTTON_MODEL_ERROR
        self._check_excel_document_model(return_code)


class ShowButtonCheckService(ButtonCheckService):
    ...


class ExternalInputButtonCheckService(ButtonCheckService):

    def initialize(self):
        super().initialize()
        self.settings = self.element.get("settings", {})

    def check_event(self):
        pass

    @checker.run
    def check_page(self):
        attr = Datalist.ATTR.FUNCTION_BAR
        pc_page_info = self.settings.get("pc_form_page", {})
        mobile_page_info = self.settings.get("mobile_form_page", {})
        pc_page = pc_page_info.get("page")
        mobile_page = mobile_page_info.get("page")
        title = self.element.get("name")
        element_uuid = self.element.get("uuid")
        if pc_page not in self.app_page_dict:
            return_code = LDEC.DATALIST_EXTERNAL_BUTTON_PC_PAGE_NOT_EXISTS
            self._add_error_list(
                attr, return_code=return_code, element_data=self.element)
        else:
            self.update_page_reference(pc_page, title, element_uuid, attr)
        if mobile_page not in self.app_page_dict:
            return_code = LDEC.DATALIST_EXTERNAL_BUTTON_PAGE_NOT_EXISTS
            self._add_error_list(
                attr, return_code=return_code, element_data=self.element)
        else:
            self.update_page_reference(mobile_page, title, element_uuid, attr)

    @checker.run
    def check_roel(self):
        role = self.settings.get("role", "")
        if not role:
            attr = Datalist.ATTR.FUNCTION_BAR
            return_code = LDEC.DATALIST_EXTERNAL_BUTTON_ROLE_NOT_BIND
            self._add_error_list(
                attr, return_code=return_code, element_data=self.element)
        else:
            module_role = self.modules_role.get(self.module_uuid, list())
            module_role_uuids = {r["role_uuid"] for r in module_role}
            if role not in module_role_uuids:
                return_code = LDEC.USER_ROLE_NOT_EXISTS
                self._add_error_list(
                    attr=Datalist.ATTR.FUNCTION_BAR, return_code=return_code)


class BaseDatalistCheckerService(ComponentCheckerService):

    attr_class = Datalist.ATTR
    uuid_error = LDEC.DATALIST_UUID_ERROR
    uuid_unique_error = LDEC.DATALIST_UUID_UNIQUE_ERROR
    name_error = LDEC.DATALIST_NAME_FAILED
    name_unique_error = LDEC.DATALIST_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.container_model_uuid = self.model_uuid
        self.all_uneditable = True

    def check_monitor(self, data_source):
        attr = self.attr_class.DATASOURCE
        monitor_type = data_source.get("monitor_type")
        add_method = data_source.get("add_method")
        if add_method == AddMethodType.SCAN:
            if monitor_type == ConstantCode.MONITOR_TYPE_NEED_FOCUS:
                copy_data_source = dict(data_source)
                page_monitor = self.document_other_info.get("page_monitor")
                if page_monitor:
                    return_code = LDEC.PAGE_MONITOR_REPEAT
                    self._add_error_list(
                        attr, return_code=return_code, element_data=copy_data_source)
                page_monitor[self.element_uuid] = True

    def update_is_required(self):

        element_field = self.element.get("data_source", dict())
        field_placeholder = element_field.setdefault("placeholder", dict())
        relationship_finder = self.page_finder.relationship_finder
        paths = element_field.get("path")
        if paths:
            p = paths[0]
            relationship = relationship_finder.relationship_basic_map.get(p, {
            })
            field_model_uuid = self.model_uuid
            if relationship:
                relation_extra = relationship.extra
                if relationship.source_model == field_model_uuid:
                    is_required = relation_extra.get(
                        "source_required", False)
                else:
                    is_required = relation_extra.get(
                        "target_required", False)
                field_placeholder.update({"is_required": is_required})

    def check_relation_data_source(self, data_source):
        attr = self.attr_class.DATASOURCE
        association = data_source.get("association")
        path = data_source.get("path")
        purpose = data_source.get("purpose")
        r_finder = self.page_finder.relationship_finder
        self.check_monitor(data_source)
        if association:
            if path and isinstance(path, list):
                if purpose != PurposeType.VIEW:
                    if len(path) > 1:
                        return_code = LDEC.RELATION_PATH_TOO_LARGE
                        self._add_error_list(attr, return_code=return_code)
                    elif len(path) == 1:
                        p = path[0]
                        parent_model = self.parent.get("model_uuid")
                        associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                            p, parent_model)
                        if is_many is True:
                            data_source.update({"is_many": 1})
                        else:
                            data_source.update({"is_many": 0})
                        if associaton_model is None:
                            return_code = LDEC.RELATION_PATH_NOT_FOUND
                            self._add_error_list(
                                attr, return_code=return_code)
                        else:
                            self._check_relation_exit(
                                association, parent_model)
            else:
                return_code = LDEC.RELATION_PATH_NOT_FOUND
                self._add_error_list(
                    attr, return_code=return_code)
        else:
            return_code = LDEC.RELATION_ASSOCIATION_NOT_EXISTS
            self._add_error_list(attr, return_code=return_code)

    def _check_relation_exit(self, association, parent_model):
        attr = Datalist.ATTR.DATASOURCE
        relationship = self.app_relationship_dict.get(association)
        if relationship and parent_model:
            if relationship.get("target_model") != parent_model and \
                    relationship.get("source_model") != parent_model:
                self._add_error_list(
                    attr, return_code=LDEC.RELATION_PATH_NOT_FOUND)

    def check_relation_subtable(self):
        attr = Datalist.ATTR.SUBTABLE
        parent_model_uuid = self.parent.get("model_uuid")
        element_data_source = self.element.get("data_source")
        self.check_relation_data_source(element_data_source)
        add_method = element_data_source.get("add_method")
        if add_method in [AddMethodType.HAND_INPUT, AddMethodType.SELECT]:
            purpose = element_data_source.get("purpose")
            path = element_data_source.get("path", [])
            is_many = element_data_source.get("is_many", 0)
            if not element_data_source.get("select_page"):
                r_finder = self.page_finder.relationship_finder
                model_basic = r_finder.lemon_model(self.model_uuid)
                model_name = model_basic.display_name or model_basic.name if model_basic else "模型"
                candidate_preprocessing = element_data_source.get(
                    "candidate_preprocessing")
                datalist_uuid = lemon_uuid()
                select_datalist_data = copy.deepcopy(self.element)
                popup_page_search = select_datalist_data.get(
                    "popup_page_search", {})
                search_items = popup_page_search.get("items", [])
                for search_item in search_items:
                    fieldsValue = search_item.get("fieldsValue")
                    self.update_reference_by_value_edit(
                        fieldsValue, search_item, attr)
                if popup_page_search:
                    popup_page_search.update(
                        {"show_search_button": True, "show_delete_button": True,
                            "funcbar_search_button_text": "搜索"})
                select_datalist_data["popup_search_bar"] = popup_page_search
                select_datalist_data_buttons = select_datalist_data.get(
                    "buttons", []) or []
                select_datalist_data_buttons.clear()  # 不需要父类的按钮区
                select_datalist_data.update({
                    "uuid": datalist_uuid, "show_row_selection": True})
                data_source_type = DataSourceType.MODEL_WITH_PRE
                data_source = copy.deepcopy(element_data_source)
                data_source.update({
                    "type": data_source_type, "model": self.model_uuid, "parent_model": parent_model_uuid,
                    "parent_path": path, "purpose": purpose, "preprocessing": candidate_preprocessing,
                    "is_many": is_many, "relsect_popup": 1
                })
                # 去除 data_source 的 select_page 否则会嵌套多层
                safe_pop(data_source, "select_page")
                select_datalist_data.update({
                    "data_source": data_source, "order": list(), "show_checkbox_select": True, 'show_buttons': False,
                    "pagination": Pagination(pageSize=20).to_dict(), "subtable": list()
                })
                select_component_type = select_datalist_data.get("type")
                if select_component_type == ComponentType.CARDLIST:
                    # 清空选择页面的卡片事件
                    select_datalist_data.update({"events": {"click": []}})
                event_action_data = EventActionData(
                    action=EventAction.SUBMIT, component=datalist_uuid)
                event_action_data = {"click": [event_action_data.to_dict()]}
                form_submit_button_title = "选择" if add_method == AddMethodType.SELECT else "保存"
                select_page = PageDocument(
                    uuid=lemon_uuid(), name="select_page", children=[select_datalist_data],
                    open_type=0, page_title=PageTitleValue("选择" + model_name + "数据"),
                    buttons=[FormCancelButton(), FormSubmitButton(
                        event=event_action_data, title=form_submit_button_title)]
                )
                element_data_source.update(
                    {"select_page": select_page.to_dict()})

    @checker.run
    def update_select_page(self):
        parent_type = self.parent.get("type")
        if parent_type in [ComponentType.FORM, ComponentType.TABS]:
            data_source_type = self.element.get("data_source", {}).get("type")
            if data_source_type in [
                    DataSourceType.ASSOCIATION, DataSourceType.ASSOCIATION_MEMORY]:
                self.check_relation_subtable()

    # 检查数据源是否绑定
    @checker.run
    def check_datasource_exists(self):
        self._check_datasource_exists()

    # @checker.run  # 为了确保最后执行,这个检查手动调用
    def check_buttons(self):
        attr = self.attr_class.FUNCTION_BAR
        is_check_buttons = False
        show_buttons = self.element.get("show_buttons")
        if self.type == ComponentType.CARDLIST:
            if self.element.get("card_type", CardlistType.NORMAL) == CardlistType.NORMAL:
                is_check_buttons = show_buttons
        elif self.type == ComponentType.DATAGRID:
            is_check_buttons = True
        elif show_buttons:
            is_check_buttons = True

        # 容器可编辑检查
        editable = self.element.get("editable")
        editable_value = False
        if isinstance(editable, dict) and editable:
            editable_value_editor = LemonBaseValueEditor(**editable)
            editable_value = True
            if editable_value_editor.type in [ValueEditorType.CONST, ValueEditorType.STRING]:
                editable_value = editable_value_editor.value
        is_check_buttons = is_check_buttons and editable_value

        if is_check_buttons:
            buttons = self.element.get("buttons", [])
            # 子表的话parent为父表单, 否则为数据列表本身
            parent = {
                "element_uuid": self.element.get("element_uuid"),
                "model_uuid": self.model_uuid
            }
            parent = getattr(self, "parent", {}) or parent
            kwargs = {
                "model_in_page": self.model_in_page,
                "app_field_dict": self.app_field_dict,
                "app_page_dict": self.app_page_dict,
                "app_label_print_dict": self.app_label_print_dict,
                "parent": parent,
                "container_model_uuid": self.model_uuid,
                "app_workflow_list": self.app_workflow_list,
                "position_info": self.kwargs.get("position_info", dict()),
                "modules_role": self.kwargs.get("modules_role", dict()),
                "app_func_uuid_dict": self.app_func_uuid_dict,
                "page_finder": self.page_finder,
                "module_tag_dict": self.module_tag_dict,
                "app_excel_template_dict": self.app_excel_template_dict,
                "app_relationship_dict": self.app_relationship_dict
            }
            for btn in buttons:
                btn_type = btn.get("type", -1)
                btn_service_class = self.button_service_dict.get(
                    btn_type, None)
                if btn_type == ComponentType.NEW_BUTTON:
                    btn.update({"all_uneditable": self.all_uneditable})
                elif btn_type == ComponentType.EDIT_BUTTON:
                    btn.update({"all_uneditable": self.all_uneditable})
                elif btn_type == ComponentType.EXPORT_BUTTON:
                    data_source_type = self.element.get(
                        "data_source", {}).get("type", None)
                    btn.update({"parent_data_source_type": data_source_type})
                if btn_service_class:
                    btn_service = btn_service_class(
                        self.app_uuid, self.module_uuid, self.module_name,
                        self.document_uuid, self.document_name, btn,
                        document_other_info=self.document_other_info, **kwargs)
                    btn_service.document_other_info = self.document_other_info
                    btn_service.check_all()
                    self.update_any_list(btn_service)
        # 返回数据列表内按钮引用详情
        for columns_i in self.element.get("columns", []):
            component_list = columns_i.get('component_list', [])
            for component_list_i in component_list:
                component_type = component_list_i.get('type')
                if component_type != ComponentType.NORMAL_BUTTON:
                    break
                click_list = component_list_i.get(
                    "events", {}).get('click', [])
                title = component_list_i.get("title")
                attr = self.attr_class.DATASOURCE
                element_uuid = component_list_i.get("uuid")
                for click in click_list:
                    page_uuid = click.get("page")
                    action = click.get("action")
                    self.update_print_template_reference(
                        page_uuid, title, element_uuid, attr)
                    if action == EventAction.LABEL_PRINT:
                        self.update_label_print_reference(
                            page_uuid, title, element_uuid, attr)

    @checker.run
    def check_editable(self):
        if not self.element.get("editable", dict()):
            self.element.update({
                "editable": {
                    "is_monitor": True,
                    "once": True,
                    "type": 0,
                    "uuid": lemon_uuid(),
                    "value": True
                }
            })

    @checker.run
    def check_row_settings(self):
        attr = Datalist.ATTR.ROW_SETTINGS
        row_settings = self.element.get("row_settings", {})
        is_edit = row_settings.get("is_edit", False)
        editable = row_settings.get("editable", {})
        editable_value = False
        if isinstance(editable, dict) and editable:
            editable_value_editor = LemonBaseValueEditor(**editable)
            editable_value = True   # 默认为 可编辑为 True
            if editable_value_editor.type in [
                ValueEditorType.CONST, ValueEditorType.STRING
            ]:
                editable_value = editable_value_editor.value
        if editable_value and is_edit:
            page_info = row_settings.get("edit_page", {})
            page_uuid = page_info.get("page")
            if page_uuid:
                self._check_button_page_with_page_uuid(
                    page_uuid, attr, LDEC.DATALIST_INLINE_EDIT_PAGE_NOT_EXISTS,
                    LDEC.DATALIST_INLINE_EDIT_PAGE_FORM_COUNT_ERROR,
                    LDEC.DATALIST_INLINE_EDIT_PAGE_FORM_MODEL_ERROR
                )
            else:
                return_code = LDEC.DATALIST_INLINE_EDIT_PAGE_NOT_SELECT
                self._add_error_list(
                    attr, return_code=return_code)

    @checker.run
    def check_select_page(self):
        check_flag = False
        if self.type == ComponentType.DATALIST:
            check_flag = True
            attr = Datalist.ATTR.SELECT_PAGE
            return_code = LDEC.DATALIST_SELECT_PAGE_NOT_SELECT
            return_code_model_error = LDEC.DATALIST_SELECT_PAGE_MODEL_ERROR
        elif self.type == ComponentType.CARDLIST:
            check_flag = True
            attr = Cardlist.ATTR.DATASOURCE
            return_code = LDEC.CARDIST_SELECT_PAGE_NOT_SELECT
            return_code_model_error = LDEC.CARDLIST_SELECT_PAGE_MODEL_ERROR
        if check_flag:

            data_source = self.element.get('data_source', {})
            view_page = data_source.get("view_page", {})
            add_method = data_source.get("add_method", {})
            data_source_type = data_source.get("type")
            if add_method == AddMethodType.SELECT and (
                    data_source_type in [DataSourceType.ASSOCIATION_MEMORY, DataSourceType.ASSOCIATION]):
                return_code_delete = LDEC.DATALIST_CARDLIST_VIEW_PAGE_IS_DELETE
                page_uuid = view_page.get("page")
                if not page_uuid:
                    self._add_error_list(
                        attr, return_code=return_code)
                else:
                    self.update_page_reference(
                        page_uuid, self.document_name, self.element_uuid, attr)
                    page_dict = self.app_page_dict.get(page_uuid, {})
                    open_type = page_dict.get("open_type")
                    if page_dict and page_dict.get("is_delete"):
                        self._add_error_list(
                            attr, return_code=return_code_delete)
                    container_model = page_dict.get(
                        PageModel.container_model.name)
                    find_same_model = False
                    for _, container_dict in container_model.items():
                        model = container_dict.get("model")
                        if model == self.model_uuid:
                            find_same_model = True
                            break
                    if not find_same_model and not page_dict.get(PageModel.is_delete.name):
                        self._add_error_list(
                            attr, return_code=return_code_model_error)
                    if open_type == OpenPageType.JUMP:
                        return_open_page_error = LDEC.VIEW_PAGE_CANNOT_JUMP
                        self._add_error_list(
                            attr, return_code=return_open_page_error)

    @checker.run
    def check_preprocessing(self):
        attr = self.attr_class.DATASOURCE
        preprocessing = self.element.get(
            "data_source", {}).get("preprocessing", {})
        self.check_preprocessing_info(attr, preprocessing)

    def _check_pagination(self):
        attr = self.attr_class.PAGINATION
        pagination = self.element.get("pagination", {})
        is_shown = pagination.get("is_shown", True)
        if is_shown:
            page_size_list = pagination.get("page_size_list", [])
            if len(page_size_list) > 10:
                return_code = LDEC.PAGINATION_PAGE_SIZE_TOO_MANY
                return_code.message = return_code.message.format(
                    name=self.element_name)
                self._add_error_list(attr, return_code=return_code)
            for page_size in page_size_list:
                if page_size > 1000:
                    return_code = LDEC.PAGINATION_PAGE_SIZE_TOO_LARGE
                    return_code.message = return_code.message.format(
                        name=self.element_name)
                    self._add_error_list(attr, return_code=return_code)

    def check_multicolumn_aggre(self):
        attr = Datalist.ATTR.MULTICOLUMN_AGGRE
        multicolumn_aggre_settings = self.element.get("aggregatedColumns", [])
        column_len = len(self.element.get("columns", []))
        selected_column_ranges = []
        for aggre_setting in multicolumn_aggre_settings:
            from_column_index = aggre_setting.get("from")
            to_column_index = aggre_setting.get("to")
            index = aggre_setting.get("index")
            is_success = self._check_aggre_column_index(
                attr, from_column_index, to_column_index, column_len, index)
            if is_success:
                selected_column_ranges.append(
                    (from_column_index, to_column_index, index))
            component_uuid = aggre_setting.get("component_uuid")
            if not component_uuid:
                return_code = LDEC.AGGRE_MULTICOLUMN_COLUMN_NOT_EXIST
                return_code.message = return_code.message.format(name=index)
                self._add_error_list(attr, return_code=return_code)
            aggre_func = aggre_setting.get("func")
            if aggre_func not in AggregationFunc.ALL:
                return_code = LDEC.AGGRE_MULTICOLUMN_FUNC_UNBOUND
                return_code.message = return_code.message.format(name=index)
                self._add_error_list(attr, return_code=return_code)
            if aggre_func == AggregationFunc.FUNC:
                return_code_param_unbound = LDEC.AGGRE_MULTICOLUMN_PARAM_UNBOUND
                return_code_param_unbound.message = return_code_param_unbound.message.format(
                    name=index)
                return_code_param_not_exist = LDEC.AGGRE_MULTICOLUMN_PARAM_NOT_EXIST
                return_code_param_not_exist.message = return_code_param_not_exist.message.format(
                    name=index)
                self.check_aggre_cloud_func(aggre_setting, attr, attr,
                                            return_code_param_unbound, return_code_param_not_exist,
                                            self.element.get("uuid"))
        self._check_aggre_column_repetition(selected_column_ranges)

    def _check_aggre_column_index(self, attr, from_column_index, to_column_index, column_len, index):
        min_column_index = 1
        is_success = True
        if from_column_index == to_column_index:
            return_code = LDEC.AGGRE_MULTICOLUMN_SAME_INDEX_ERROR
            return_code.message = return_code.message.format(name=index)
            self._add_error_list(attr, return_code=return_code)
            is_success = False
        if from_column_index > to_column_index:
            return_code = LDEC.AGGRE_MULTICOLUMN_ILLEGAL_ERROR
            return_code.message = return_code.message.format(name=index)
            self._add_error_list(attr, return_code=return_code)
            is_success = False
        if from_column_index < min_column_index or to_column_index < min_column_index:
            return_code = LDEC.AGGRE_MULTICOLUMN_MIN_INDEX_ERROR
            return_code.message = return_code.message.format(name=index)
            self._add_error_list(attr, return_code=return_code)
            is_success = False
        if from_column_index > column_len or to_column_index > column_len:
            return_code = LDEC.AGGRE_MULTICOLUMN_MAX_INDEX_ERROR
            return_code.message = return_code.message.format(name=index)
            self._add_error_list(attr, return_code=return_code)
            is_success = False
        return is_success

    def _check_aggre_column_repetition(self, selected_column_ranges):
        selected_column_ranges.sort(key=lambda x: x[0])
        for i in range(1, len(selected_column_ranges)):
            if selected_column_ranges[i][0] <= selected_column_ranges[i-1][1]:
                return_code = LDEC.AGGRE_MULTICOLUMN_REPETITION_ERROR
                return_code.message = return_code.message.format(
                    name1=min(
                        selected_column_ranges[i][2], selected_column_ranges[i-1][2]),
                    name2=max(selected_column_ranges[i][2], selected_column_ranges[i-1][2]))
                self._add_error_list(
                    Datalist.ATTR.MULTICOLUMN_AGGRE, return_code=return_code)


class ColumnCheckerServiceMixin(ComponentCheckerService):
    def initialize(self):
        super().initialize()
        self.page_finder = PageFinder(
            self.model_in_page, except_form=False, count_container=True,
            find_association=True, model_list=self.app_model_list,
            relationship_list=self.app_relationship_list,
            field_list=self.app_field_list)
        self.page_finder.find_func(self.element)
        # app_log.info(f"model_in_page: {self.model_in_page}")
        self.relationship_finder = self.page_finder.relationship_finder
        self.column_edit_fields = []

    def check_field_type(self, column):
        attr = self.attr_class.COLUMN
        component: dict = column.get("component", {})
        data_source = component.get("data_source", {})
        component_field_info = component.setdefault("field_info", dict())
        # self.update_reference_by_data_source(data_source, component, attr)
        if isinstance(data_source, dict):
            # 这里先判断是值编辑器选了字段,或是字段选择器
            value_edit = data_source.get("valueEdit", {}) or {}
            is_value_edit_field = value_edit.get(
                "type") == ValueEditorType.FIELD
            field_info = data_source.get("field_info", {})
            field_uuid = None

            if is_value_edit_field:
                field_uuid = value_edit.get("field")
            elif field_info:
                field_uuid = field_info.get("uuid")
            if field_uuid:
                field_dict = self.app_field_dict.get(field_uuid)
                if isinstance(field_dict, dict):
                    field_type = field_dict.get(ModelField.field_type.name)
                    component_type = column.get("component", {}).get("type")
                    if component_type not in [ComponentType.LINE_BAR]:
                        if field_type and component_type not in ComponentType.FIELD_TO_SHOW_COMPONENT.get(field_type):
                            self._add_error_list(
                                attr, return_code=LDEC.DATALIST_COMPONENT_TYPE_ERROR, dataIndex=column.get("dataIndex"))
                    component_field_info.update({
                        "field_type": field_type,
                        "calculate_field": field_dict.get(ModelField.calculate_field.name),
                        "enum_uuid": field_dict.get(ModelField.enum_uuid.name)
                    })
                    return field_type

    def check_column_button(self, column: dict):
        attr = self.attr_class.COLUMN
        all_component_list = []
        component = column.get("component", {})
        if component:
            all_component_list.append(component)
        component_list = column.get("component_list", [])
        all_component_list.extend(component_list)
        filterable = column.get("filterable")
        dataIndex = column.get("dataIndex")
        button_count = 0
        for component in all_component_list:
            component_type = component.get("type", None)
            kwargs = {"dataIndex": column.get("dataIndex")}
            if component_type == ComponentType.NORMAL_BUTTON:
                button_count += 1
                self._check_event(attr, component, **kwargs)
                title = component.get("title")
                if title:
                    self.check_filter_sort_only_field_value_editor(
                        title, filterable, attr, dataIndex)

        if filterable and button_count > 1:
            self._add_error_list(attr, return_code=LDEC.FILTER_SORT_TOO_MANY_BUTTON,
                                 dataIndex=dataIndex)

    def check_image_type_is_edit(self, component_type, field_type, source_type, dataIndex, attr):
        if component_type == ComponentType.IMAGE:
            if field_type == FieldType.IMAGE:
                if isinstance(source_type, int):
                    if source_type != ImageSourceType.FIELD:
                        return_code = LDEC.DATALIST_COLUMN_IMAGE_EDIT_ERROR
                        self._add_error_list(
                            attr, return_code=return_code, dataIndex=dataIndex)

    def check_file_type_is_edit(self, component_type, field_type, source_type, dataIndex, attr):

        if component_type == ComponentType.FILE:
            if field_type == FieldType.FILE:
                if source_type != FileSourceType.FIELD:
                    return_code = LDEC.DATALIST_COLUMN_FIELD_EDIT_ERROR
                    self._add_error_list(
                        attr, return_code=return_code, dataIndex=dataIndex)

    def is_form_model_error_code(self, page_dict, container_model_uuid):

        find_same_model = False
        container_model = page_dict.get(
            PageModel.container_model.name)
        for _, container_dict in container_model.items():
            model = container_dict.get("model")
            if model == container_model_uuid:
                find_same_model = True
                break
        if not find_same_model and page_dict.get(PageModel.is_delete.name, None) is not None:
            return True
        else:
            return False

    def check_dynamic_columns(self, column, attr):

        def _check_default():
            dynamic_setting = column.get("dynamic_setting", {})
            dynamic_source_type = dynamic_setting.get("dynamic_source_type")
            dataIndex = column.get("dataIndex")
            if dynamic_source_type == DynamicSourceType.JSON:
                # 检查json字段是否选择
                data_field = dynamic_setting.get("data_field", {})
                if not data_field:
                    return_code = LDEC.DYNAMIC_DATA_FIELD_NOT_CHOOSE
                    self._add_error_list(
                        attr, return_code=return_code, dataIndex=dataIndex)
            elif dynamic_source_type == DynamicSourceType.SUBTABLE:
                data_source_field = dynamic_setting.get(
                    "data_source_field", {})
                title_field = dynamic_setting.get("title_field", {})
                if not data_source_field:
                    return_code = LDEC.DYNAMIC_DATA_SOURCE_FIELD_NOT_CHOOSE
                    self._add_error_list(
                        attr, return_code=return_code, dataIndex=dataIndex)
                if not title_field:
                    return_code = LDEC.DYNAMIC_TITLE_FIELD_NOT_CHOOSE
                    self._add_error_list(
                        attr, return_code=return_code, dataIndex=dataIndex)
                data_source_field_uuid = data_source_field.get("field")
                title_field_uuid = title_field.get("field")
                # 检查数据源字段是否为计算字段
                if data_source_field:
                    field_dict = self.app_field_dict.get(
                        data_source_field_uuid, {})
                    calculate_field = field_dict.get(
                        ModelField.calculate_field.name)
                    calculate_type = field_dict.get(
                        ModelField.calculate_type.name)
                    if calculate_field and calculate_type != CalculateType.GENERATED_COLUMN:
                        return_code = LDEC.DYNAMIC_DATA_SOURCE_FIELD_INVALID
                        self._add_error_list(
                            attr, return_code=return_code, dataIndex=dataIndex)
                # 检查两个字段是否都有选择, 是否是统一模型
                if data_source_field and title_field and data_source_field.get("model") != title_field.get("model"):
                    return_code = LDEC.DYNAMIC_MODEL_DIFFERENT
                    self._add_error_list(
                        attr, return_code=return_code, dataIndex=dataIndex)
                # 检查数据源字段和标题字段是否为同一字段
                if data_source_field and title_field and data_source_field_uuid == title_field_uuid:
                    return_code = LDEC.DYNAMIC_FIELD_SAME
                    self._add_error_list(
                        attr, return_code=return_code, dataIndex=dataIndex)
            else:
                return_code = LDEC.DYNAMIC_DATA_SOURCE_INVALID
                self._add_error_list(
                    attr, return_code=return_code, dataIndex=dataIndex)

        def _check_template():
            dynamic_setting = column.get("dynamic_setting", {})
            mapping_key = dynamic_setting.get("mapping_key", {})
            # 检查示例列的映射键是否设置
            if not mapping_key:
                return_code = LDEC.DYNAMIC_MAPPING_KEY_NOT_CHOOSE
                self._add_error_list(
                    attr, return_code=return_code, dataIndex=column.get("dataIndex"))

        dynamic_setting = column.get("dynamic_setting", {})
        if dynamic_setting.get("dynamic_column_type") == DynamicColumnType.DEFAULT:
            _check_default()
        else:
            _check_template()

    def _check_column_aggregation(self, column, attr, dataIndex):
        aggre_setting = column.get("aggregation", {})
        use_aggre = aggre_setting.get("use_aggregation", False)
        aggre_type = aggre_setting.get("type", AggregationType.SINGLE_COLUMN)
        if use_aggre and aggre_type == AggregationType.SINGLE_COLUMN:
            func = aggre_setting.get("func")
            if func not in AggregationFunc.ALL:
                self._add_error_list(
                    attr, return_code=LDEC.AGGRE_COLUMN_FUNC_UNBOUND, dataIndex=dataIndex)
            if func == AggregationFunc.FUNC:
                self.check_aggre_cloud_func(aggre_setting, attr, "汇总",
                                            LDEC.AGGRE_COLUMN_PARAM_UNBOUND,
                                            LDEC.AGGRE_COLUMN_PARAM_NOT_EXIST, dataIndex)

    @checker.run
    def check_columns(self):
        attr = self.attr_class.COLUMN
        r_finder = self.page_finder.relationship_finder
        columns = self.element.get("columns", list())
        if not self.model_uuid:
            # 如果数据列表 没有绑定模型，就不再校验列了
            return
        for column in columns:
            element_data = column
            if element_data.get("datalist_column_type") == DatalistColumnType.DYNAMIC:
                self.check_dynamic_columns(element_data, attr)
            permission_config = element_data.get("permission_config", {})
            title = column.get("title")
            # 旧数据，title可能是str
            if isinstance(title, dict) and title:
                name = title.get("value")
            else:
                name = title
            column_data = {
                "name": name,
                "uuid": column.get("uuid")
            }
            self._check_permission_config(
                permission_config, self.module_tag_dict, element_data=column_data)
            self.check_column_button(element_data)
            field_type = self.check_field_type(column)
            editable_value = False
            dataIndex = column.get("dataIndex")
            if isinstance(title, dict) and title:
                title_value_editor = LemonBaseValueEditor(**title)
                if not self.parent:
                    if title_value_editor.path_columns:
                        return_code = LDEC.DATALIST_COLUMN_TITLE_IS_FIELD_EDITOR
                        self._add_error_list(
                            attr, return_code=return_code, dataIndex=dataIndex)
            editable = column.get("editable")
            if isinstance(editable, dict) and editable:
                editable_value_editor = LemonBaseValueEditor(**editable)
                editable_value = True   # 默认为 可编辑为 True
                if not self.parent:
                    if editable_value_editor.path_columns:
                        return_code = LDEC.DATALIST_COLUMN_EDITABLE_IS_FIELD_EDITOR
                        self._add_error_list(
                            attr, return_code=return_code, dataIndex=dataIndex)
                if editable_value_editor.type in [ValueEditorType.CONST, ValueEditorType.STRING]:
                    editable_value = editable_value_editor.value
            else:
                editable_value = editable
            input_control = column.get("input_control")
            select_type = column.get("select_data_source", {}).get("type")
            if input_control == ComponentType.RADIO:
                return_code = LDEC.DATALIST_COLUMN_INPUT_CONTROL_IS_RADIO
                self._add_error_list(
                    attr, return_code=return_code, dataIndex=dataIndex)
            edit_field = column.get("edit_field")
            self.update_reference_by_field_info(edit_field, column, attr)
            if isinstance(edit_field, dict):
                edit_field_uuid = edit_field.get("uuid")
                edit_field_dict = edit_field
            else:
                edit_field_uuid = edit_field
                edit_field_dict = {"uuid": edit_field}
            preprocessing = column.get("candidate_preprocessing", {})
            events = column.get("events", {})
            if events:
                error_element = {
                    "element_data": {
                        "name": self.element.get("name"), "uuid": dataIndex, "type": ComponentType.COLUMN}}
                focus = events.get("focus", []) or events.get("click", [])
                self.check_events(
                    attr=attr, events=focus, **error_element)
            if preprocessing:
                self.check_preprocessing_info(attr, preprocessing)
            if editable_value is True:
                if input_control == ComponentType.SELECT:
                    select_data_source = column.get("select_data_source", {})
                    select_type = select_data_source.get("type")
                    select_func_uuid = select_data_source.get("func", "")
                    if select_type == SelectType.CLOUD_FUNCTION:
                        if select_func_uuid:
                            if self.app_func_uuid_dict.get(select_func_uuid):
                                self.update_cloud_func_reference(
                                    select_func_uuid, self.element.get("name"), dataIndex, attr)
                            else:
                                self._add_error_list(
                                    attr, return_code=LDEC.FUNC_IS_DELETED, dataIndex=dataIndex)
                        else:
                            self._add_error_list(
                                attr, return_code=LDEC.FUNC_NOT_EXIST, dataIndex=dataIndex)
                elif input_control in [ComponentType.UPLOAD_IMAGE, ComponentType.UPLOAD_FILE]:
                    if not column.get("extension"):
                        self._add_error_list(
                            attr,
                            return_code=LDEC.DATALIST_COLUMN_USLOAD_EXTENSION_ERROR,
                            dataIndex=dataIndex)
                if edit_field_uuid:
                    field_dict = self.app_field_dict.get(edit_field_uuid)
                    if field_dict:
                        field_is_required = field_dict.get(
                            ModelField.is_required.name)
                        field_model_uuid = field_dict.get(
                            ModelField.model_uuid.name)
                        field_type = field_dict.get(ModelField.field_type.name)
                        field_default = field_dict.get(ModelField.default.name)
                        field_is_serial = field_dict.get(
                            ModelField.is_serial.name)
                        field_placeholder = edit_field_dict.setdefault(
                            "placeholder", {})
                        field_placeholder.update(
                            {"is_required": field_is_required, "type": field_type})
                        path = edit_field_dict.get("path", list())

                        # 非关联的系统字段不可编辑
                        if field_dict.get("is_system") and editable_value and \
                                input_control not in ComponentType.RELATION_CONTROL_LIST:
                            return_code = LDEC.SYS_FIELD_UNEDITABLE
                            self._add_error_list(
                                attr, return_code=return_code, dataIndex=dataIndex)
                        if field_type == FieldType.ENUM:
                            field_enum_uuid = field_dict.get(
                                ModelField.enum_uuid.name)
                            field_placeholder.update(
                                {"enum_uuid": field_enum_uuid})
                        component_type = column.get(
                            "component", {}).get('type')
                        source_type = column.get("component", {}).get(
                            'data_source', {}).get("type")
                        self.check_image_type_is_edit(
                            component_type, field_type, source_type, dataIndex, attr
                        )
                        self.check_file_type_is_edit(
                            component_type, field_type, source_type, dataIndex, attr)
                        if field_model_uuid == self.model_uuid and not path:
                            # 流水号字段，允许编辑；未输入时，自动生成
                            # if field_is_serial:
                            #     editable = UNEditableValue().to_dict()
                            #     column.update({"editable": editable})
                            #     editable_value = False
                            component_type_list = ComponentType.FIELD_TO_COMPONENT.get(
                                field_type)
                            select_field_type_list = SelectType.ALL.get(
                                input_control, {}).get(select_type)
                            if input_control not in component_type_list and \
                                    not (select_field_type_list and field_type in select_field_type_list):
                                return_code = LDEC.DATALIST_COLUMN_INPUT_CONTROL_ERROR
                                self._add_error_list(
                                    attr, return_code=return_code, dataIndex=dataIndex)
                            if field_is_required and not field_default:
                                return_code = LDEC.FIELD_IS_REQUIRED_NO_DEFAULT_VALUE
                                self._add_error_list(
                                    attr, return_code=return_code, dataIndex=dataIndex)
                        else:
                            if input_control not in ComponentType.RELATION_CONTROL_LIST:
                                field_path = edit_field_dict.setdefault(
                                    "path", {})
                                parent_model = field_dict.get('model_uuid', '')
                                for f_path in field_path:
                                    r_finder = self.page_finder.relationship_finder
                                    associaton_model = None
                                    try:
                                        associaton_model,    is_source, is_many, r_type = r_finder.lemon_association(
                                            f_path, parent_model)
                                    except Exception:
                                        pass
                                    if associaton_model is None:
                                        return_code = LDEC.DATALIST_REL_INLINE_EDIT_ERROR
                                        self._add_error_list(
                                            attr, return_code=return_code, dataIndex=dataIndex)
                            if path and isinstance(path, list):
                                if len(path) > 1:
                                    return_code = LDEC.RELATION_PATH_TOO_LARGE
                                    self._add_error_list(
                                        attr, return_code=return_code, dataIndex=dataIndex)
                                elif len(path) == 1:
                                    p = path[0]
                                    associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                                        p, self.model_uuid)
                                    is_many = 1 if is_many is True else 0
                                    is_source = 1 if is_source is True else 0
                                    field_placeholder.update(
                                        {"is_many": is_many, "is_source": is_source})
                                    relationship = r_finder.relationship_basic_map.get(
                                        p, {})
                                    if relationship:
                                        relation_extra = relationship.extra
                                        if relationship.source_model == field_model_uuid:
                                            is_required = relation_extra.get(
                                                "source_required", False)
                                        else:
                                            is_required = relation_extra.get(
                                                "target_required", False)
                                        # relation_required = False if is_many else is_required
                                        field_placeholder.update(
                                            {"relation_required": is_required})
                                        field_placeholder.update(
                                            {"is_required": is_required})

                            if input_control == ComponentType.R_TREE:
                                edit_field_data_source = column.get(
                                    "edit_field_data_source", dict())
                                if edit_field_data_source.get("type") == 1:  # 自关联
                                    association = edit_field_data_source.get(
                                        "association", dict())
                                    text_association = column.get(
                                        "component", {}).get("data_source", {}).get("association", {})
                                    if not association.get("path") or not text_association.get("path"):
                                        self._add_error_list(
                                            attr, return_code=LDEC.SELF_ASSOCIATION_NOT_SELECT,
                                            dataIndex=dataIndex)

                        if input_control == ComponentType.R_SELECT:
                            new_page = column.get("new_page", {})
                            if associaton_model:
                                column_model = associaton_model.uuid
                            else:
                                column_model = \
                                    column.get("component", {}).get("data_source", {}).get(
                                        "valueEdit", {}).get("model")
                            show_button = new_page.get("show", False)
                            page = new_page.get("page", False)
                            # 引用详情
                            if page:
                                self.update_page_reference(
                                    page, self.document_name, dataIndex, attr)
                            if show_button:
                                if not page:
                                    return_code = LDEC.DATALIST_COLUMN_R_SELECT_NOT_CREATE_PAGE
                                    self._add_error_list(
                                        attr, return_code=return_code, dataIndex=dataIndex)
                                else:
                                    page_dict = self.app_page_dict.get(page)
                                    is_form_model_error_code = self.is_form_model_error_code(
                                        page_dict, column_model)
                                    if is_form_model_error_code:
                                        return_code = LDEC.DATALIST_COLUMN_R_SELECT_PAGE_FORM_MODEL_ERROR
                                        self._add_error_list(
                                            attr, return_code=return_code, dataIndex=dataIndex)
                        inline_component_dict = {
                            ComponentType.R_SELECT_POPUP: RSelectPopupCheckerService
                        }
                        inline_check_class = inline_component_dict.get(
                            input_control)
                        if inline_check_class:
                            inline_check_service = inline_check_class(
                                self.app_uuid, self.module_uuid,
                                self.module_name, self.document_uuid,
                                self.document_name, column,
                                parent=self.element,
                                app_page_dict=self.app_page_dict,
                                app_func_uuid_dict=self.app_func_uuid_dict,
                                app_relationship_list=self.app_relationship_list,
                                app_relationship_dict=self.app_relationship_dict,
                                app_field_dict=self.app_field_dict,
                                page_finder=self.page_finder,
                                model_uuid=self.model_uuid,
                                module_tag_dict=self.module_tag_dict)
                            inline_check_service.check_all()
                            self.update_any_list(inline_check_service)
                        calculate_field = field_dict.get(
                            ModelField.calculate_field.name)
                        if calculate_field and input_control in ComponentType.INPUT_CONTROL_LIST:
                            return_code = LDEC.DATALIST_CALC_INLINE_EDIT_ERROR
                            self._add_error_list(
                                attr, return_code=return_code, dataIndex=dataIndex)
                        edit_field_dict.update(
                            {"placeholder": field_placeholder})
                        column["edit_field"] = edit_field_dict
                    else:
                        return_code = LDEC.DATALIST_COLUMN_NO_EDITABLE_COLUMN
                        self._add_error_list(
                            attr, return_code=return_code, dataIndex=dataIndex)
                    self.column_edit_fields.append((edit_field_dict, editable))
                else:
                    return_code = LDEC.DATALIST_COLUMN_NO_EDITABLE_COLUMN
                    self._add_error_list(
                        attr, return_code=return_code, dataIndex=dataIndex)
            if editable_value is True:
                self.all_uneditable = False
            column_component = column.get("component", {})
            filterable = column.get("filterable")
            data_source = column_component.get("data_source", {})
            component_type = column_component.get("type")
            component_dict = {ComponentType.IMAGE: PresentImageCheckService,
                              ComponentType.FILE: FileCheckService,
                              ComponentType.TEXT: TextCheckService,
                              ComponentType.SCAN_COUNT: ScanCountCheckService,
                              ComponentType.LINE_BAR: LineBarCheckService,
                              ComponentType.RING_BAR: RingBarCheckService,
                              }
            component_checker_class = component_dict.get(component_type)
            if component_checker_class:
                column_component = column.get("component")
                parent = {
                    "type": self.type, "model_uuid": self.model_uuid
                }
                component_checker_service = component_checker_class(
                    self.app_uuid, self.module_uuid, self.module_name,
                    self.document_uuid, self.document_name, column_component,
                    app_field_dict=self.app_field_dict,
                    app_page_dict=self.app_page_dict,
                    app_func_uuid_dict=self.app_func_uuid_dict,
                    app_relationship_list=self.app_relationship_list,
                    app_relationship_dict=self.app_relationship_dict,
                    app_model_list=self.app_model_list,
                    app_field_list=self.app_field_list, parent=parent,
                    position_info=self.kwargs.get("position_info", dict()),
                    module_tag_dict=self.module_tag_dict,
                    app_label_print_dict=self.app_label_print_dict,
                )
                component_checker_service.document_other_info = self.document_other_info
                component_checker_service.check_all()
                if component_type == ComponentType.LINE_BAR:
                    component_checker_service.check_field()
                elif component_type == ComponentType.RING_BAR:
                    component_checker_service.check_field()
                self.update_any_list(component_checker_service)
            else:
                self.update_reference_by_data_source(data_source, column, attr)
            valueEdit = data_source.get("valueEdit", {}) or {}
            value_editor_type_field, is_normal_field = self._check_value_editor_normal_field(
                valueEdit, self.app_field_dict, attr, element_data)
            value_editor_type_expr = valueEdit.get(
                "type") == ValueEditorType.EXPR

            path = valueEdit.get("path", [])
            if path and self.model_uuid:
                for p in path:
                    associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                        p, self.model_uuid)
                    if associaton_model is None:
                        return_code = LDEC.RELATION_PATH_NOT_FOUND
                        self._add_error_list(
                            attr, return_code=return_code, element_data=column.get("component", {}))
            aggre_func = valueEdit.get("aggre_func")
            if aggre_func is not None and field_type is not None:
                if aggre_func in ComponentType.AGG_FUNC_TO_FIELD and \
                        field_type not in ComponentType.AGG_FUNC_TO_FIELD.get(aggre_func):
                    return_code = LDEC.FIELD_AGG_FUNC_ERR
                    self._add_error_list(
                        attr, return_code=return_code, dataIndex=dataIndex)

            if filterable and self.element.get("data_source", {}).get("type") == DataSourceType.ASSOCIATION_MEMORY:
                self._add_error_list(
                    attr, return_code=LDEC.FILTER_SORT_NOT_ASSOCIATION_MEMORY, dataIndex=dataIndex)

            if value_editor_type_field and not is_normal_field:
                field_uuid = valueEdit.get("field", "")
                field_basic_info = self.app_field_dict.get(field_uuid)
                if filterable and field_basic_info:
                    self._add_error_list(attr, return_code=LDEC.FILTER_SORT_ONLY_NORMAL_FIELD,
                                         dataIndex=dataIndex)
                allow_group = column.get("isGroup")
                if allow_group:
                    self._add_error_list(attr, return_code=LDEC.GROUPBY_ONLY_NORMAL_FIELD,
                                         dataIndex=dataIndex)
            elif value_editor_type_expr:
                exprArr = valueEdit.get("exprArr", list())
                for arg in exprArr:
                    value = arg.get("value")
                    v_type = value.get("type")
                    if v_type == ValueEditorType.FIELD:
                        model = value.get("model")
                        path = value.get("path", list())
                        if model == self.model_uuid:
                            # 判断字段是否为模型字段
                            field = value.get("field")
                            field_info = self.app_field_dict.get(field, dict())
                            if field_info.get("model_uuid") != self.model_uuid:
                                return_code = LDEC.FIELD_NOT_EXISTS_IN_THIS_MODEL
                                return_code.message = return_code.message.format(
                                    name=field_info.get("field_name", "未知字段"))
                                self._add_error_list(
                                    attr, return_code, element_data=column.get("component", {}))
                        else:
                            # 判断是否包含关联
                            for p in path:
                                associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                                    p, self.model_uuid)
                                if associaton_model is None:
                                    return_code = LDEC.RELATION_PATH_NOT_FOUND
                                    self._add_error_list(
                                        attr, return_code=return_code, element_data=column.get("component", {}))

            # 按钮类型已在 check_column_button 检查过
            if data_source and component_type in [ComponentType.TAG]:
                self.check_filter_sort_only_field_value_editor(
                    valueEdit, filterable, attr, dataIndex)

            if not editable_value:
                field_info = {}
                if component_type == ComponentType.IMAGE:
                    data_source = column_component.get("data_source", {})
                    data_source_type = data_source.get("type")
                    if data_source_type == DataSourceType.Image.IMAGE_FIELD:
                        field_info = data_source.get("field_info", {})
                elif component_type == ComponentType.FILE:
                    field_info = column_component.get("field_info", {})
                field_uuid = field_info.get("uuid")
                path = field_info.get("path")
                if field_uuid and not path:
                    self.column_edit_fields.append((field_info, True))

            # 汇总
            if self.type in [ComponentType.DATALIST, ComponentType.DATAGRID]:
                self._check_column_aggregation(column, attr, dataIndex)
        app_log.info("test")

    def check_filter_sort_only_field_value_editor(self, value_editor, filterable, attr, dataIndex):
        # 检查列上的表头排序筛选操作
        if filterable:
            value_editor_type = value_editor.get("type")
            if value_editor_type != ValueEditorType.FIELD:
                self._add_error_list(
                    attr,
                    return_code=LDEC.FILTER_SORT_ONLY_FIELD_VALUE_EDITOR,
                    dataIndex=dataIndex)


class DatalistCheckerService(BaseDatalistCheckerService, ColumnCheckerServiceMixin):

    # 检查 子表 是否设置了功能栏 和 搜索栏
    def check_subtable(self):
        attr = Datalist.ATTR.SUBTABLE
        subtable = self.element.get("subtable", list())
        for sub in subtable:
            element_data = sub
            self.process_layout_uuid(
                self.kwargs, self.element.get("uuid"), type=self.type)
            sub_name = sub.get("name", "")
            data_source = sub.get("data_source", {})
            sub_kwargs = {k: v for k, v in self.kwargs.items()}
            sub_kwargs["parent"] = {
                "model_uuid": self.model_uuid, "type": self.type}
            sub_datalist = DatalistCheckerService(
                app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
                document_uuid=self.document_uuid, document_name=self.document_name, element=sub, **sub_kwargs)
            sub_datalist.component_uuid_set = self.component_uuid_set
            sub_datalist.document_other_info = self.document_other_info
            if data_source.get("type") not in [
                DataSourceType.ASSOCIATION,
                DataSourceType.ASSOCIATION_MEMORY
            ]:
                return_code = LDEC.DATALIST_SUBTABLE_DATASOURCE_ERROR
                return_code.message = return_code.message.format(name=sub_name)
                self._add_error_list(
                    attr, return_code=return_code, element_data=element_data)
            buttons = sub.get("buttons", [])
            self.update_reference_by_data_source(
                data_source, element_data, attr)
            for btn in buttons:
                if sub.get("show_buttons") and btn.get("type", -1) in ComponentType.SPECIAL_BUTTON:
                    # 现在子表没有功能栏和搜索栏，这里检查错误暂时不执行
                    break
                    return_code = LDEC.DATALIST_SUBTABLE_ERROR
                    return_code.message = return_code.message.format(
                        name=sub_name)
                    self._add_error_list(
                        attr, return_code=return_code, element_data=element_data)
                    break
            if self.is_copy:
                sub_datalist.check_uuid()
                sub_datalist.check_uuid_unique(self.component_uuid_set)
                sub_datalist.check_all()
            else:
                # 如果以后子表一定会执行check_all可以去掉这里的check_columns和check_row_settings
                sub_datalist.check_row_settings()
                sub_datalist.check_columns()
                sub_datalist.check_datasource_exists()
            self.update_any_list(sub_datalist)
            path = data_source.get("path", [])
            data_source_attr = Datalist.ATTR.DATASOURCE
            self.check_path_exist(
                data_source_attr, LDEC.SUBTABLE_PATH_NOT_FOUND, path, element_data)

    def check_path_exist(self, attr, return_code, path, element_data):
        if not path:
            return
        p = path[0]
        parent_model = self.model_uuid
        r_finder = self.page_finder.relationship_finder
        associaton_model = None
        try:
            associaton_model, is_source, is_many, r_type = r_finder.lemon_association(
                p, parent_model)
        except Exception:
            pass
        if associaton_model is None:
            return_code = LDEC.RELATION_PATH_NOT_FOUND
            self._add_error_list(
                attr, return_code=return_code, element_data=element_data)

    @checker.run
    def update_data(self):
        self.update_is_required()

    @checker.run
    def check_datasource_type(self):
        attr = Datalist.ATTR.DATASOURCE
        self._check_datasource_type_multiple(attr)

    @checker.run
    def check_candidate_preprocessing(self):
        attr = Datalist.ATTR.DATASOURCE
        data_source = self.element.get("data_source", {})
        add_method = data_source.get("add_method")
        c_preprocessing = data_source.get("candidate_preprocessing", {})
        if c_preprocessing:
            data_type = c_preprocessing.get("dataType")
            if data_type == PreprocessType.PathLimit:
                self.check_preprossing_pathlimit(
                    attr, add_method, c_preprocessing)

    def check_preprossing_pathlimit(self, attr, add_method, c_preprocessing: dict):
        return_code = LDEC.DATASOURCE_PREPROCESSING_PATH_NOT_FOUND
        # 添加方式不同, 功能前端展示名也不同
        if add_method == AddMethodType.HAND_INPUT:
            function_front_show_title = "预填写"
        else:
            function_front_show_title = "候选项预处理"
        return_code.message = return_code.message.format(
            name=function_front_show_title)
        relation_path = c_preprocessing.get("relationPath", {})
        path = relation_path.get("path", [])
        for p in path:
            if p not in self.app_relationship_dict:
                self._add_error_list(attr, return_code=return_code)

    @checker.run
    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_cloud_event(attr)

    @checker.run
    def check_pagination(self):
        self._check_pagination()

    @checker.run
    def check_multicolumn_aggre(self):
        super().check_multicolumn_aggre()

    @checker.run
    def check_row_settings(self):
        super().check_row_settings()
        attr = Datalist.ATTR.ROW_SETTINGS
        row_settings = self.element.get("row_settings", {})
        is_sort = row_settings.get("is_sort", False)
        if is_sort:
            sort_field = row_settings.get("sort_field", {})
            self._check_sort_field(attr, sort_field)


class CardlistCheckerService(BaseDatalistCheckerService):

    attr_class = Cardlist.ATTR
    style_class = Cardlist.STYLE
    uuid_error = LDEC.CARDLIST_UUID_ERROR
    uuid_unique_error = LDEC.CARDLIST_UUID_UNIQUE_ERROR
    name_error = LDEC.CARDLIST_NAME_FAILED
    name_unique_error = LDEC.CARDLIST_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.kwargs.update({"model_uuid": self.model_uuid})
        self.kwargs.update({"is_cardlist": True})
        self.children = self.element.get("children", list())

    # 检查 卡片列表 的子组件
    def check_children(self, uuid_set, name_set, data_container=True):
        self._prepare_children_datasource_check()
        self._check_children(
            self.children, uuid_set, name_set, data_container=data_container, **self.kwargs)

        operations = self.element.get("card_item", {}).get("operations", [])
        self._check_children(
            operations, uuid_set, name_set, data_container=data_container, **self.kwargs)

    @checker.run
    def update_data(self):
        self.update_is_required()

    @checker.run
    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_event(attr)

    @checker.run
    def check_datasource_type(self):
        attr = Cardlist.ATTR.DATASOURCE
        self._check_datasource_type_multiple(attr)

    @checker.run
    def check_pagination(self):
        self._check_pagination()

    @checker.run
    def check_sort_field(self):
        attr = Cardlist.ATTR.DRAG
        is_sort = self.element.get("is_sort", False)
        if is_sort:
            sort_field = self.element.get("sort_field", {})
            self._check_sort_field(attr, sort_field, "拖动")

    @checker.run
    def check_style(self):
        self.check_dynamic_definition()


class DatagridCheckerService(BaseDatalistCheckerService, ColumnCheckerServiceMixin):

    attr_class = Datagrid.ATTR
    uuid_error = LDEC.DATAGRID_UUID_ERROR
    uuid_unique_error = LDEC.DATAGRID_UUID_UNIQUE_ERROR
    name_error = LDEC.DATAGRID_NAME_FAILED
    name_unique_error = LDEC.DATAGRID_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.all_uneditable = True

    @checker.run
    def update_data(self):
        self.update_is_required()

    # 检查数据源是否绑定
    @checker.run
    def check_datasource_exists(self):
        self._check_datasource_exists()

    @checker.run
    def check_datasource_type(self):
        attr = Datagrid.ATTR.DATASOURCE
        self._check_datasource_type_multiple(attr)

    @checker.run
    def check_search_field_type(self):
        search_items: list = self.element.get(
            "search_bar", {}).get("items", [])
        attr = self.attr_class.SEARCH_BAR
        if search_items:
            for item in search_items:
                field_info = item.get("fieldsValue", {})
                if field_info:
                    field_uuid = field_info.get("field", "")
                else:
                    field_uuid = item.get("field", "")
                r_finder = self.page_finder.relationship_finder
                lemon_field = r_finder.field_uuid_map.get(field_uuid)
                field_type = lemon_field.field_type
                self.update_reference_by_field_info(
                    field_uuid, self.element, attr)
                if any([field_type in [
                    FieldType.DATETIME_CYCLE, FieldType.FILE, FieldType.IMAGE],
                        lemon_field.calculate_field]):
                    self._add_error_list(
                        attr, return_code=LDEC.SEARCH_ITEM_ONLY_NORMAL_FIELD,
                        element_data=item)

    # @checker.run  # Task 2162  3. 电子表格展开编辑功能去掉
    def check_edit_page(self):
        from apps.engine import engine
        component_type = ComponentType.FORM
        data_model = self.model_uuid
        if not data_model:
            return

        relationship_finder = self.page_finder.relationship_finder
        model_basic = relationship_finder.model_uuid_map.get(data_model)
        # app_log.info(f"model_basic: {model_basic}, columns: {model_basic.columns}")
        if not model_basic:
            return

        edit_fields = list()
        edit_field_set = set([])
        for field_result in self.column_edit_fields:
            field_info, editable = field_result
            field_uuid = field_info.get("uuid")
            if field_uuid in edit_field_set:
                continue
            edit_field_set.add(field_uuid)
            edit_fields.append((field_info, editable))

        create_service = engine.service.page_create.create_service(
            component_type)
        field_list = []
        relationship_list = []
        for field_result in edit_fields:
            field_info, editable = field_result
            placeholder = field_info.get("placeholder", {})
            app_log.info(f"field_info: {field_info}")
            app_log.info(f"editable: {editable}")
            field_uuid = field_info.get("uuid")
            lemon_field_info = self.app_field_dict.get(field_uuid)
            app_log.info(f"lemon_field_info: {lemon_field_info}")
            if lemon_field_info:
                lemon_field = dict()
                lemon_field.update({"placeholder": placeholder})
                lemon_field.update({"editable": editable})
                path = field_info.get("path", [])
                if len(path) == 1:
                    is_many = placeholder.get("is_many", 0)
                    is_source = placeholder.get("is_source", 0)
                    relationship_dict = copy.deepcopy(
                        self.app_relationship_dict.get(path[0]))
                    relationship_dict.update(lemon_field_info)
                    relationship_dict.update(
                        {"is_many": is_many, "is_source": is_source})
                    relationship_list.append(relationship_dict)
                elif not path:
                    lemon_field.update({"path": path})
                    lemon_field.update(lemon_field_info)
                    field_list.append(lemon_field)
        model_display_name = model_basic.display_name
        create_service.field_list = field_list
        page_title = f"编辑 {model_display_name}"
        action_kwargs = {"submit_is_refresh": True, "create_is_many": True}
        create_service_info = {"page_name": page_title, "page_title": page_title,
                               "editable": True, "action_kwargs": action_kwargs,
                               "relationship_list": relationship_list,
                               "model_uuid": data_model, "kwargs": {},
                               "open_type": PageOpenType.POPUP,
                               "self_associate_relations": []}
        for key, attr in create_service_info.items():
            setattr(create_service, key, attr)
        page = create_service.create_component()
        page_content = page.to_dict()
        self.element["edit_page"] = page_content

    @checker.run
    def check_multicolumn_aggre(self):
        super().check_multicolumn_aggre()


class TabsCheckerService(ComponentCheckerService):

    attr_class = Tabs.ATTR
    uuid_error = LDEC.TABS_UUID_ERROR
    uuid_unique_error = LDEC.TABS_UUID_UNIQUE_ERROR
    name_error = LDEC.TABS_NAME_FAILED
    name_unique_error = LDEC.TABS_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.tabs = self.element.get("tabs", list())

    # 检查 页签 的子组件
    def check_tabs(self, uuid_set, name_set, data_container=False):
        self._check_children(self.tabs, uuid_set, name_set,
                             data_container=data_container, **self.kwargs)
        for tab in self.tabs:
            if not tab.get("type"):
                # 老的单个页签中缺少type
                tab["type"] = ComponentType.TAB
            children = tab.get("children", list())
            # child_kwargs = copy.deepcopy(self.kwargs)  # 保证self.kwargs不变动, 每一tab都使用同样的初始

            # self.process_layout_uuid(child_kwargs, tab["uuid"])
            self._check_children(
                children, uuid_set, name_set, data_container=data_container, **self.kwargs)


class TabCheckerService(ComponentCheckerService):
    attr_class = Tab.ATTR
    uuid_error = LDEC.TAB_UUID_ERROR
    uuid_unique_error = LDEC.TAB_UUID_UNIQUE_ERROR
    name_error = LDEC.TAB_NAME_FAILED
    name_unique_error = LDEC.TAB_NAME_NOT_UNIQUE

    @checker.run
    def check_badge(self):
        attr_badge = self.attr_class.BADGE
        badge = self.element.get("badge", {})
        self._check_badge(attr_badge, badge)


class CollapseCheckerService(ComponentCheckerService):

    attr_class = Collapse.ATTR
    uuid_error = LDEC.COLLAPSE_UUID_ERROR
    uuid_unique_error = LDEC.COLLAPSE_UUID_UNIQUE_ERROR
    name_error = LDEC.COLLAPSE_NAME_FAILED
    name_unique_error = LDEC.COLLAPSE_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.panels = self.element.get("panels", list())

    # 检查 折叠面板 的子组件
    def check_panels(self, uuid_set, name_set, data_container=False):
        self._check_children(self.panels, uuid_set, name_set,
                             data_container=data_container, **self.kwargs)
        for panel in self.panels:
            children = panel.get("children", list())
            # child_kwargs = copy.deepcopy(self.kwargs)
            # self.process_layout_uuid(child_kwargs, panel["uuid"])
            self._check_children(
                children, uuid_set, name_set, data_container=data_container, **self.kwargs)


class PanelCheckerService(ComponentCheckerService):
    attr_class = Panel.ATTR
    uuid_error = LDEC.PANEL_UUID_ERROR
    uuid_unique_error = LDEC.PANEL_UUID_UNIQUE_ERROR
    name_error = LDEC.PANEL_NAME_FAILED
    name_unique_error = LDEC.PANEL_NAME_NOT_UNIQUE

    @checker.run
    def check_badge(self):
        attr_badge = self.attr_class.BADGE
        badge = self.element.get("badge", {})
        self._check_badge(attr_badge, badge)


class SplitPageCheckerService(ComponentCheckerService):

    attr_class = SplitPage.ATTR
    uuid_error = LDEC.SPLIT_PAGE_UUID_ERROR
    uuid_unique_error = LDEC.SPLIT_PAGE_UNIQUE_ERROR
    name_error = LDEC.SPLIT_PAGE_NAME_FAILED
    name_unique_error = LDEC.SPLIT_PAGE_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.panels = self.element.get("panels", list())

    # 检查分割组件的子组件
    def check_split(self, uuid_set, name_set, data_container=False):
        self._check_children(self.panels, uuid_set, name_set,
                             data_container=data_container, **self.kwargs)
        for panel in self.panels:
            children = panel.get("children", list())
            self._check_children(
                children, uuid_set, name_set, data_container=data_container, **self.kwargs)


class SplitCheckerService(ComponentCheckerService):

    attr_class = Split.ATTR
    uuid_error = LDEC.PANEL_UUID_ERROR
    uuid_unique_error = LDEC.PANEL_UUID_UNIQUE_ERROR
    name_error = LDEC.PANEL_NAME_FAILED
    name_unique_error = LDEC.PANEL_NAME_NOT_UNIQUE

    @checker.run
    def check_badge(self):
        attr_badge = self.attr_class.BADGE
        badge = self.element.get("badge", {})
        self._check_badge(attr_badge, badge)


class ContainerCheckerService(ComponentCheckerService):

    attr_class = Container.ATTR
    style_class = Container.STYLE
    uuid_error = LDEC.CONTAINER_UUID_ERROR
    uuid_unique_error = LDEC.CONTAINER_UUID_UNIQUE_ERROR
    name_error = LDEC.CONTAINER_NAME_FAILED
    name_unique_error = LDEC.CONTAINER_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.children = self.element.get("children", list())

    # 检查 容器 的子组件
    def check_children(self, uuid_set, name_set, data_container=False):
        self._check_children(
            self.children, uuid_set, name_set, data_container=data_container, **self.kwargs)

    @checker.run
    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_event(attr)

    @checker.run
    def check_style(self):
        self.check_dynamic_definition()


class GridRowCheckerService(ComponentCheckerService):

    attr_class = GridRow.ATTR
    uuid_error = LDEC.GRIDROW_UUID_ERROR
    uuid_unique_error = LDEC.GRIDROW_UUID_UNIQUE_ERROR
    name_error = LDEC.GRIDROW_NAME_FAILED
    name_unique_error = LDEC.GRIDROW_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.cols = self.element.get("cols", list())

    def check_cols(self, uuid_set, name_set, data_container=False):
        for col in self.cols:
            col_checker_service = GridColCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, col, **self.kwargs)
            col_checker_service.document_other_info = self.document_other_info
            col_checker_service.check_uuid()
            col_checker_service.check_uuid_unique(uuid_set)
            # col_checker_service.check_name()
            # col_checker_service.check_name_unique(name_set)
            name = col.get("name") if col.get("name") else "列"
            element_data = {
                "uuid": col.get("uuid"),
                "name": name
            }
            col_checker_service.check_permission_config(
                element_data=element_data)
            col_checker_service.check_children(
                uuid_set, name_set, data_container=data_container)
            self.update_any_list(col_checker_service)


class GridColCheckerService(ComponentCheckerService):

    attr_class = GridCol.ATTR
    uuid_error = LDEC.GRIDCOL_UUID_ERROR
    uuid_unique_error = LDEC.GRIDCOL_UUID_UNIQUE_ERROR
    name_error = LDEC.GRIDCOL_NAME_FAILED
    name_unique_error = LDEC.GRIDCOL_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.children = self.element.get("children", list())

    # 检查栅格列的子组件
    def check_children(self, uuid_set, name_set, data_container=False):
        self._check_children(
            self.children, uuid_set, name_set, data_container=data_container, **self.kwargs)


class GridCheckerService(ComponentCheckerService):

    attr_class = Grid.ATTR
    uuid_error = LDEC.GRID_UUID_ERROR
    uuid_unique_error = LDEC.GRID_UUID_UNIQUE_ERROR
    name_error = LDEC.GRID_NAME_FAILED
    name_unique_error = LDEC.GRID_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()
        self.rows = self.element.get("rows", list())

    # 检查 rows
    def check_rows(self, uuid_set, name_set, data_container=False):
        for row in self.rows:
            row_check_service = GridRowCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, row, **self.kwargs)
            row_check_service.document_other_info = self.document_other_info
            row_check_service.check_uuid()
            row_check_service.check_uuid_unique(uuid_set)
            # row_check_service.check_name()
            # row_check_service.check_name_unique(name_set)
            row_check_service.check_cols(
                uuid_set, name_set, data_container=data_container)
            self.update_any_list(row_check_service)


class DatetimeCycleCheckService(ComponentCheckerService):

    attr_class = DatetimeCycleAttr

    @checker.run
    def check_exist(self):
        attr = self.attr_class.TYPE
        return_code = LDEC.COMPONENT_REMOVED_PLEASE_dELETE
        self._add_error_list(
            attr, return_code=return_code)


class PageCheckerService(ComponentCheckerService):

    attr_class = Page.ATTR
    uuid_error = LDEC.PAGE_UUID_ERROR
    uuid_unique_error = LDEC.PAGE_UUID_UNIQUE_ERROR
    name_error = LDEC.PAGE_NAME_FAILED
    name_unique_error = LDEC.PAGE_NAME_NOT_UNIQUE
    page_finder: PageFinder

    def initialize(self):
        super().initialize()
        self.open_type = self.element.get("open_type", 0)
        self.children = self.element.get("children", list())
        self.page_url = self.element.get("custom_path", {}.get("path"))
        self.component_uuid_set = set()
        self.component_name_set = set()
        self.container_uuid_dict = dict()
        self.app_func_uuid_dict = self.kwargs.get("app_func_uuid_dict", {})
        self.model_in_page = self.page_finder.model_in_page
        self.module_tag_dict = self.kwargs.get("module_tag_dict", {})
        self.app_excel_template_dict = self.kwargs.get(
            "app_excel_template_dict", {})
        # self.page_finder.find_func(self.element)
        self.relationship_finder = self.page_finder.relationship_finder
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []

    def build_insert_query_data(self):
        return {
            PageModel.app_uuid.name: self.app_uuid,
            PageModel.module_uuid.name: self.module_uuid,
            PageModel.document_uuid.name: self.document_uuid,
            PageModel.page_uuid.name: self.element_uuid,
            PageModel.page_name.name: self.element_name,
            PageModel.open_type.name: self.open_type,
            PageModel.datalist_count.name: self.page_finder.datalist_count,
            PageModel.cardlist_count.name: self.page_finder.cardlist_count,
            PageModel.datagrid_count.name: self.page_finder.datagrid_count,
            PageModel.form_count.name: self.page_finder.form_count,
            PageModel.container_model.name: self.model_in_page,
            PageModel.ext_tenant.name: self.ext_tenant
        }

    def build_update_query_data(self):
        return {
            PageModel.page_name.name: self.element_name,
            PageModel.open_type.name: self.open_type,
            PageModel.datalist_count.name: self.page_finder.datalist_count,
            PageModel.cardlist_count.name: self.page_finder.cardlist_count,
            PageModel.datagrid_count.name: self.page_finder.datagrid_count,
            PageModel.form_count.name: self.page_finder.form_count,
            PageModel.container_model.name: self.model_in_page,
            PageModel.is_delete.name: False
        }

    def build_update_query(self):
        query_data = self.build_update_query_data()
        return PageModel.update(**query_data).where(
            PageModel.page_uuid == self.element_uuid)

    @staticmethod
    def build_delete_query(page_uuid):
        return PageModel.update(**{
            PageModel.is_delete.name: True
        }).where(PageModel.page_uuid == page_uuid)

    def check_modify(self, document_page_uuid_set):
        if self.is_copy:
            model_in_page = dict()
            for k, v in self.model_in_page.items():
                new_uuid = self.component_copy_dict.get(k, k)
                model_in_page[new_uuid] = v
            self.model_in_page = model_in_page
        if self.element_uuid in document_page_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

    # 检查页面所有 children
    def check_children(self, **kwargs):
        kwargs.update({
            "model_in_page": self.model_in_page,
            "page_finder": self.page_finder,
            "app_workflow_list": self.app_workflow_list,
            "modules_role": kwargs.get("modules_role"),
            "app_restful_dict": self.app_restful_dict,
            "app_field_list": self.app_field_list,
            "app_model_list": self.app_model_list,
            "app_relationship_dict": self.app_relationship_dict,
            "module_tag_dict": self.module_tag_dict,
            "app_excel_template_dict": self.app_excel_template_dict,
            "page_uuid": self.element_uuid
        })
        self._check_children(
            self.children, self.component_uuid_set, self.component_name_set,
            **kwargs)
        # app_log.info(self.document_other_info)

    @checker.run
    def check_refresh(self):
        if self.element.get("refresh", {}).get("is_auto"):
            if "gap" not in self.element.get("refresh"):
                self._add_error_list(
                    attr=self.attr_class.REFRESH, return_code=LDEC.PAGE_REFRESH_GAP_NOT_SET)

    @checker.run
    def check_all_scan(self):
        if self.page_finder.scan_focus == self.page_finder.scan_global_focus > 1:
            self._add_error_list(
                attr=self.attr_class.SCAN_CONTROL, return_code=LDEC.MULTI_SCAN_NO_FOCUSFAILED)

    def check_string(s):
        pattern = re.compile(
            r'^[a-zA-Z0-9!@#$%^&*()_+{}\[\]:;<>,.?\/\-=|\s]+$')
        return bool(pattern.match(s))

    @checker.run
    def check_custom_path(self):
        custom_path = self.element.get("custom_path", {})
        path = custom_path.get("path")
        if custom_path.get("show"):
            if path:
                page_uuid = self.app_url_dict.get(path)
                if page_uuid and page_uuid != self.element_uuid:
                    self._add_error_list(
                        attr=self.attr_class.CUSTOM_PATH,
                        return_code=LDEC.PAGE_URL_NOT_UNIQUE)
                # pattern = re.compile(r'^[a-zA-Z0-9!@#$%^&*()_+{}\[\]:;<>,.?\/\-=|\s]+$')
                pattern = re.compile(
                    r'^[a-zA-Z0-9!@#$%^&*()_+{}\[\]:;<>,.?\/\-=|]+$')
                if not bool(pattern.match(path)):
                    self._add_error_list(
                        attr=self.attr_class.CUSTOM_PATH,
                        return_code=LDEC.PAGE_URL_INVALID)
            else:
                self._add_error_list(
                    attr=self.attr_class.CUSTOM_PATH,
                    return_code=LDEC.PAGE_URL_IS_NULL)

    @checker.run
    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_event(attr)

    def _check_event(self, attr, element_data=None, **kwargs):
        events = self.element.get("events", [])
        element_uuid = self.element_uuid
        title = self.element_name
        # 检查云函数是否存在和更新云函数引用
        for event_info in events:
            func_uuid = event_info.get("func")
            if func_uuid:
                if self.app_func_uuid_dict.get(func_uuid):
                    self.update_cloud_func_reference(
                        func_uuid, title, element_uuid, attr)
                else:
                    self._add_error_list(
                        attr, return_code=LDEC.FUNC_IS_DELETED, element_data=self.element)
            else:
                self._add_error_list(
                    attr, return_code=LDEC.FUNC_NOT_EXIST, element_data=self.element)
            # 值编辑器检查
            value_editor = event_info.get("error_tip")
            if value_editor:
                self._check_value_editor(
                    value_editor, attr, r_finder=self.relationship_finder)


class PageDocumentCheckerService(DocumentCheckerService):

    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str,
            document_uuid: str, document_name: str,
            element: dict, document_version: int,
            app_page_list: list, app_field_list,
            app_model_list, app_relationship_list,
            app_workflow_list, app_label_print_dict,
            modules_role, app_func_uuid_dict, app_url_dict,
            app_restful_dict, module_tag_dict, app_excel_template_dict, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, PageModel, *args, **kwargs)
        self.document_version = document_version
        self.app_field_list = app_field_list
        self.app_model_list = app_model_list
        self.app_relationship_list = app_relationship_list
        self.app_relationship_dict = dict()
        self.app_label_print_dict = app_label_print_dict
        self.app_field_dict = dict()
        self.app_model_dict = dict()
        self.app_page_dict = dict()
        self.app_page_uuid_set = set()
        self.module_page_name_set = set()
        self.document_page_uuid_set = set()
        self.app_workflow_list = app_workflow_list
        self.app_func_uuid_dict = app_func_uuid_dict
        self.modules_role = modules_role
        self.model_in_page = dict()
        self.page_finder = PageFinder(
            self.model_in_page, except_form=False, count_container=True,
            find_association=True,
            model_list=self.app_model_list,
            relationship_list=self.app_relationship_list,
            field_list=self.app_field_list)
        self.page_finder.find_func(self.element)
        # app_log.info(f"model_in_page: {self.model_in_page}")
        self.relationship_finder = self.page_finder.relationship_finder
        self.app_url_dict = app_url_dict
        self.app_restful_dict = app_restful_dict
        self.module_tag_dict = module_tag_dict.get(self.module_uuid, dict())
        self.app_excel_template_dict = app_excel_template_dict

        for page in app_page_list:
            page_uuid = page.get("page_uuid", "")
            page_name = page.get("page_name", "")
            page_module_uuid = page.get("module_uuid", "")
            page_document_uuid = page.get("document_uuid", "")
            self.app_page_dict.update({page_uuid: page})

            # 找到原文档中所有的 页面，为了新增、更新、删除文档的 页面
            if page_document_uuid == self.document_uuid:
                self.document_page_uuid_set.add(page_uuid)
            else:
                # 排除当前文档所有的 page_uuid ，获取应用的所有 page_uuid
                self.app_page_uuid_set.add(page_uuid)
                # 排除当前文档所有的 page_name ，获取模块的所有 page_name
                if page_module_uuid == self.module_uuid:
                    self.module_page_name_set.add(page_name)
        for field_info in self.app_field_list:
            field_uuid = field_info.get(ModelField.field_uuid.name)
            self.app_field_dict[field_uuid] = field_info
        for model_info in self.app_model_list:
            model_uuid = model_info.get(ModelBasic.model_uuid.name)
            self.app_model_dict[model_uuid] = model_info
        self.build_app_relation_dict()

    def build_app_relation_dict(self):
        for relationship_dict in self.app_relationship_list:
            r_uuid = relationship_dict.get("relationship_uuid")
            self.app_relationship_dict[r_uuid] = relationship_dict
            source_model = relationship_dict.get("source_model")
            target_model = relationship_dict.get("target_model")
            if source_model == target_model:
                self.process_self_relationship(relationship_dict)
                continue
            if target_model in SystemTable.UUIDS:
                continue
            for r_model_uuid in [target_model, source_model]:
                r_model = self.relationship_finder.model_uuid_map.get(
                    r_model_uuid)
                if r_model:
                    # 关联下拉框的 placeholder 需要 data_name
                    relationship_dict.update({
                        ModelBasic.data_name.name: r_model.display_name})

    def process_self_relationship(self, relationship_dict):
        from_source = copy.copy(relationship_dict)
        from_target = copy.copy(relationship_dict)
        new_uuid = replace_relationship_uuid(from_source, RelationshipBasic)
        self.app_relationship_dict[new_uuid] = from_source
        new_uuid = replace_relationship_uuid(
            from_target, RelationshipBasic, from_source=False)
        self.app_relationship_dict[new_uuid] = from_target

    @checker.run
    def check_page(self):
        if self.new_ext_doc:
            need_check = True
        else:
            is_ext = self.element.get("is_extension")
            need_check = True
            if self.ext_tenant and not is_ext:
                need_check = False
        page_checker_service = PageCheckerService(
            self.app_uuid, self.module_uuid, self.module_name,
            self.document_uuid, self.document_name, self.element,
            app_field_list=self.app_field_list,
            app_model_list=self.app_model_list,
            app_relationship_list=self.app_relationship_list,
            app_relationship_dict=self.app_relationship_dict,
            app_func_uuid_dict=self.app_func_uuid_dict,
            app_workflow_list=self.app_workflow_list,
            app_url_dict=self.app_url_dict,
            app_restful_dict=self.app_restful_dict,
            page_finder=self.page_finder,
            ext_tenant=self.ext_tenant, is_copy=self.is_copy,
            module_tag_dict=self.module_tag_dict,
            app_excel_template_dict=self.app_excel_template_dict,
            app_label_print_dict=self.app_label_print_dict)
        page_checker_service.document_other_info = self.document_other_info
        page_uuid = page_checker_service.element_uuid
        try:
            page_checker_service.check_uuid()
            if self.is_copy:
                page_checker_service.check_uuid_unique(self.app_page_uuid_set)
            else:
                if need_check:
                    page_checker_service.check_uuid_unique(
                        self.app_page_uuid_set)
            # page_checker_service.check_name()
            # page_checker_service.check_name_unique(self.module_page_name_set)
            page_checker_service.check_all()
            page_checker_service.check_children(
                app_field_dict=self.app_field_dict,
                app_page_dict=self.app_page_dict,
                app_relationship_list=self.app_relationship_list,
                app_relationship_dict=self.app_relationship_dict,
                is_copy=self.is_copy,
                modules_role=self.modules_role,
                app_func_uuid_dict=self.app_func_uuid_dict,
                app_model_list=self.app_model_list,
                app_field_list=self.app_field_list,
                module_tag_dict=self.module_tag_dict,
                app_excel_template_dict=self.app_excel_template_dict,
                app_label_print_dict=self.app_label_print_dict)
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(page_checker_service)
            raise e
        else:
            self.update_any_list(page_checker_service)

        # 找到新增 或 更新的 页面
        if need_check:
            page_checker_service.check_modify(self.document_page_uuid_set)
        if page_checker_service.insert_query_list:
            self.document_insert_list.extend(
                page_checker_service.insert_query_list)
        if page_checker_service.update_query_list:
            self.document_update_list.extend(
                page_checker_service.update_query_list)

        # 找出删除的 页面 ，将其 is_delete 置为 True
        delete_page_uuid_set = self.document_page_uuid_set - set([page_uuid])
        for this_page_uuid in delete_page_uuid_set:
            query = page_checker_service.build_delete_query(this_page_uuid)
            self.document_delete_list.append(query)


class PrintCheckerService(ComponentCheckerService):

    attr_class = PrintButton.ATTR

    def check_is_copy_datasource(self):

        data_source = self.element.get("data_source")
        if self.is_copy and data_source:
            for key, value in data_source.items():
                if self.component_copy_dict.get(value):
                    data_source.update(
                        {key: self.component_copy_dict.get(value)})
                else:
                    temp_uuid = lemon_uuid()
                    self._update_component_copy_dict(value, temp_uuid)
                    data_source.update({key: temp_uuid})

    def check_template(self):
        select_page = self.element.get("selectedPage")
        if select_page:
            print_uuid = select_page.get("page_uuid")
            title = self.element.get("name")
            attr = self.attr_class.DATASOUCE
            element_uuid = self.element.get("uuid")
            self.update_print_template_reference(
                print_uuid, title, element_uuid, attr)

    def check_datasource(self):
        title = self.element.get("name")
        select_page = self.element.get("selectedPage")
        attr = self.attr_class.DATASOUCE
        if select_page:
            pass
        else:
            return_code = LDEC.PRINT_BUTTON_DATASOURCE_DOESNOT_EXIST
            return_code.message = return_code.message.format(name=title)
            self._add_error_list(attr=attr, return_code=return_code)

    # @checker.run
    # async def check_document(self):
    #     title = self.element.get("name")
    #     select_page = self.element.get("selectedPage")
    #     attr = self.attr_class.DATASOUCE
    #     if select_page:
    #         document_uuid = select_page.get("document_uuid")
    #         if document_uuid:
    #             from apps.dbaccess import DBAccess
    #             document = await DBAccess.get_document_by_document_uuid(document_uuid)
    #             if not document:
    #                 return_code = LDEC.PRINT_BUTTON_DOCUMENT_NOT_EXISTS
    #                 return_code.message = return_code.message.format(name=title)
    #                 self._add_error_list(attr=attr, return_code=return_code)

    @checker.run
    def _check_datasource(self):
        self.check_is_copy_datasource()
        self.check_template()
        self.check_datasource()


class TreeCheckerService(ComponentCheckerService):

    attr_class = Tree.ATTR
    uuid_error = LDEC.TREE_UUID_ERROR
    uuid_unique_error = LDEC.TREE_UUID_UNIQUE_ERROR

    def initialize(self):
        super().initialize()
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.data_source = self.element.get("data_source", {})

    @checker.run
    def check_datasource(self):
        # app_log.info(self.element)
        self._check_datasource_exists()
        self.check_model()
        self.check_self_association()

    def check_model(self):
        model_uuid = self.model_dict.get("model")
        if model_uuid:
            exist_self_association = list(filter(
                lambda x: x["source_model"] == x["target_model"] == model_uuid,
                self.app_relationship_list))
            if not exist_self_association:
                # 选择的模型不包含自关联
                self._add_error_list(
                    attr=self.attr_class.DATASOURCE,
                    return_code=LDEC.TREE_MODEL_NOT_SELF_ASSOCIATION)

    @checker.run
    def check_name_field(self):
        name = self.data_source.get("name", {})
        name_field_uuid = name.get("field")
        name_field_info = self.app_field_dict.get(name_field_uuid, {})
        if not name_field_info:
            return_code = LDEC.TREE_NAME_FIELD_NOT_SELECT
            return_code.message = return_code.message.format(
                name=self.element_name)
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)
        else:
            name_field_model = name_field_info.get("model_uuid")
            data_model_uuid = self.model_dict.get("model")
            if data_model_uuid and name_field_model != data_model_uuid:
                field_info = self.element.get("data_source", {}).get("name")
                self.check_name_field_relationship_type(field_info)

                operate = self.element.get("operate", dict())
                operate_keys = ["draggable", "new_node",
                                "editable", "delete_node", "searchable"]
                if any([operate.get(key) for key in operate_keys]):
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=LDEC.TREE_ASSOCIATION_OPERATE_ERROR)

    def check_name_field_relationship_type(self, field_info):
        attr = self.attr_class.DATASOURCE
        path_list = field_info.get("path")
        r_finder = self.page_finder.relationship_finder
        model_uuid = self.model_uuid
        for path in path_list:
            field_model, _, is_many, _ = r_finder.lemon_association(
                path, model_uuid)
            model_uuid = field_model.uuid
            if is_many:
                self._add_error_list(
                    attr, return_code=LDEC.TREE_NAME_FIELD_IS_MANY, element_data=self.element)
                break

    @checker.run
    def check_child_form_control(self):
        name = self.data_source.get("name", {})
        name_field_uuid = name.get("field")
        for control in self.element.get("child_form_control", []):
            control["field_info"]["uuid"] = name_field_uuid

    def check_self_association(self):
        data_source = self.element.get("data_source", dict())
        association = data_source.get("association", dict())
        if not association.get("path", list()):
            self._add_error_list(
                self.attr_class.DATASOURCE,
                return_code=LDEC.SELF_ASSOCIATION_NOT_SELECT)

    @checker.run
    def check_operate_settings(self):
        self.check_new_node()
        self.check_edit_node()

    def check_new_node(self):
        attr = Tree.ATTR.OPERATE
        operate = self.element.get("operate", {})
        new_node = operate.get("new_node")
        new_noding = operate.get("new_noding")
        if new_node is True and new_noding == 1:
            new_page_uuid = self.element.get(
                "new_function_bar", {}).get("page")
            not_select_error = LDEC.TREE_NEW_PAGE_NOT_SELECT
            is_delete_error = LDEC.TREE_NEW_PAGE_IS_DELETE
            form_model_error = LDEC.TREE_NEW_PAGE_FORM_MODEL_ERROR
            self.check_page(attr, new_page_uuid, not_select_error,
                            is_delete_error, form_model_error)

    def check_edit_node(self):
        attr = Tree.ATTR.OPERATE
        operate = self.element.get("operate", {})
        editable = operate.get("editable")
        editing = operate.get("editing")
        if editable is True and editing == 1:
            edit_page_uuid = self.element.get(
                "edit_function_bar", {}).get("page")
            not_select_error = LDEC.TREE_EDIT_PAGE_NOT_SELECT
            is_delete_error = LDEC.TREE_EDIT_PAGE_IS_DELETE
            form_model_error = LDEC.TREE_EDIT_PAGE_FORM_MODEL_ERROR
            self.check_page(attr, edit_page_uuid, not_select_error,
                            is_delete_error, form_model_error)

    def check_page(self, attr, page_uuid, not_select_error, is_delete_error, form_model_error):
        if not page_uuid:
            self._add_error_list(attr, return_code=not_select_error)
        else:
            page = self.app_page_dict.get(page_uuid, {})
            if page.get("is_delete") is True:
                self._add_error_list(attr, return_code=is_delete_error)
            else:
                container_model = page.get("container_model")
                find_same_model = False
                for _, container_dict in container_model.items():
                    model = container_dict.get("model")
                    is_outer_container = container_dict.get(
                        "is_outer_container")
                    if is_outer_container and model == self.model_uuid:
                        find_same_model = True
                        break
                if find_same_model is False:
                    self._add_error_list(attr, return_code=form_model_error)

    @checker.run
    def check_operate_permission_config(self):
        operate = self.element.get("operate", {})
        need_calc_keys = ["editable", "new_node",
                          "draggable", "searchable", "delete_node"]
        for key in need_calc_keys:
            if operate.get(key):
                permission_config_key = f"{key}_permission"
                permission_config = operate.get(
                    permission_config_key, {}).get("permission_config", {})
                self._check_permission_config(
                    permission_config, self.module_tag_dict)

    @checker.run
    def check_events(self):
        attr = Tree.ATTR.EVENT
        events = self.element.get("events", {})
        all_events = [event for event_list in events.values()
                      for event in event_list]
        super().check_events(attr, all_events)


class ChartCheckerService(ComponentCheckerService):

    attr_class = Chart.ATTR

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source")

    def _check_field_info(self, field_list, title, element_uuid, attr):
        for field_info in field_list:
            field_uuid = field_info.get("field")
            r_path = field_info.get("path", [])
            for r_uuid in r_path:
                self.update_relationship_reference(
                    r_uuid, title, element_uuid, attr)
            self.update_field_reference(field_uuid, title, element_uuid, attr)

    def _check_model(self,  title):
        data_source = self.data_source
        model_uuid = data_source.get("model")
        attr = self.attr_class.DATASOURCE
        element_uuid = data_source.get("uuid")
        self.update_model_reference(model_uuid, title, element_uuid, attr)

    def _check_aixs(self, title):
        data_source = self.data_source
        aixs = data_source.get("aixs", [])
        attr = self.attr_class.AXIS
        element_uuid = data_source.get("uuid")
        self._check_field_info(aixs, title, element_uuid, attr)

    def _check_legend(self, title):
        data_source = self.data_source
        legend = data_source.get("legend")
        if legend:
            attr = self.attr_class.LEGEND
            element_uuid = data_source.get("uuid")
            field_uuid = legend.get("field")
            r_path = legend.get("path", [])
            for r_uuid in r_path:
                self.update_relationship_reference(
                    r_uuid, title, element_uuid, attr)
            self.update_field_reference(field_uuid, title, element_uuid, attr)

    def _check_values(self, title):
        data_source = self.data_source
        values = data_source.get("values", [])
        attr = self.attr_class.VALUES
        element_uuid = data_source.get("uuid")
        self._check_field_info(values, title, element_uuid, attr)

    def _check_tooltips(self, title):
        data_source = self.data_source
        tooltips = data_source.get("tooltips", [])
        attr = self.attr_class.TOOLTIPS
        element_uuid = data_source.get("uuid")
        self._check_field_info(tooltips, title, element_uuid, attr)

    def _check_details(self, title):
        line_values = self.data_source.get("details", [])
        element_uuid = self.data_source.get("uuid")
        attr = self.attr_class.LINE_VALUES
        self._check_field_info(line_values, title, element_uuid, attr)

    @checker.run
    def check_datasource(self):
        title = self.element.get("name")
        self._check_aixs(title)
        self._check_model(title)
        self._check_legend(title)
        self._check_values(title)
        self._check_tooltips(title)


class DataVisualCheckerService(ComponentCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    def check_filter(self):
        filters = self.element.get("filters", {})
        filter_uuid = filters.get("component")
        filters_type = filters.get("type")
        if self.is_copy:
            if filters_type is not None:
                if filter_uuid:
                    if filters_type == 0:
                        if not self.component_copy_dict.get(filter_uuid):
                            temp_uuid = lemon_uuid()
                            self._update_component_copy_dict(
                                filter_uuid, temp_uuid)
                            filters.update({"component": temp_uuid})
                        else:
                            filters.update(
                                {"component": self.component_copy_dict.get(filter_uuid)})
                    else:
                        index = 0
                        for filter_component in filter_uuid:
                            if not self.component_copy_dict.get(filter_component):
                                temp_uuid = lemon_uuid()
                                self._update_component_copy_dict(
                                    filter_component, temp_uuid)

                                filter_uuid[index] = temp_uuid
                            else:
                                filter_uuid[index] = self.component_copy_dict.get(
                                    filter_component)
                            index += 1

    def _check_axle_exists(self):
        # 检查轴是否绑定
        visual_axle = self.data_source.get('axis', list())
        return_code = LDEC.VISUALIZATION_AXLE_DOESNOT_EXIST
        return_code.message = return_code.message.format(
            name=self.element_name)
        if visual_axle:
            for axle_i in visual_axle:
                axle_field_uuid = axle_i.get('field', '')
                self.update_field_reference(
                    axle_field_uuid, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
                if not axle_field_uuid:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)
                else:
                    # 判断绑定字段是否存在
                    field_dict = self.app_field_dict.get(
                        axle_field_uuid, dict())
                    if not field_dict:
                        return_code = LDEC.VISUALIZATION_AXLE_FIELD_DOESNOT_EXIST
                        return_code.message = return_code.message.format(
                            name=self.element_name)
                        self._add_error_list(attr=self.attr_class.DATASOURCE,
                                             return_code=return_code)
        else:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)

    # 检查图例是否绑定
    def _check_legend_exists(self):
        return_code = LDEC.VISUALIZATION_LEGENT_FIELD_UNBOUND
        return_code.message = return_code.message.format(
            name=self.element_name)

        field_uuid = self.data_source.get("legend", {}).get("field")
        if field_uuid:
            self.update_field_reference(
                field_uuid, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
            field_dict = self.app_field_dict.get(field_uuid, dict())
            if not field_dict:
                self._add_error_list(attr=self.attr_class.DATASOURCE,
                                     return_code=return_code)

    # 检查可增加图例字段是否绑定
    def _check_list_legend_exists(self):
        return_code = LDEC.VISUALIZATION_LEGENT_FIELD_DOESNOT_EXIST
        return_code.message = return_code.message.format(
            name=self.element_name)

        legend_values = self.data_source.get("legend", [])

        if legend_values:
            for legend_values_i in legend_values:
                # if isinstance(legend_values, dict):
                #     legend_values_i = legend_values
                vaule_i_field_uuid = legend_values_i.get("field")
                self.update_field_reference(
                    vaule_i_field_uuid, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
                field_dict = self.app_field_dict.get(
                    vaule_i_field_uuid, dict())
                if not vaule_i_field_uuid:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)
                elif not field_dict:
                    return_code = LDEC.VISUALIZATION_LEGENT_FIELD_UNBOUND
                    return_code.message = return_code.message.format(
                        name=self.element_name)
                    self._add_error_list(attr=self.attr_class.DATASOURCE,
                                         return_code=return_code)
        else:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)

    # 检查值是否绑定
    def _check_value_exists(self):
        values = self.data_source.get('values', list())
        unbound_return_code = LDEC.VISUALIZATION_VALUE_UNBOUND
        unbound_return_code.message = unbound_return_code.message.format(name=self.element_name)
        not_exist_return_code = LDEC.VISUALIZATION_VALUE_FIELD_DOESNOT_EXIST
        not_exist_return_code.message = not_exist_return_code.message.format(name=self.element_name)
        too_long_return_code = LDEC.VISUALIZATION_VALUE_NAME_TOO_LONG
        too_long_return_code.message = too_long_return_code.message.format(name=self.element_name)

        if values:
            for value in values:
                field_uuid = value.get("field")
                self.update_field_reference(
                    field_uuid, self.element_name,
                    self.element_uuid, self.attr_class.DATASOURCE)
                if not field_uuid:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=unbound_return_code)
                else:
                    # 判断绑定字段是否存在
                    field_dict = self.app_field_dict.get(
                        field_uuid, dict())
                    if not field_dict:
                        self._add_error_list(
                            attr=self.attr_class.DATASOURCE,
                            return_code=not_exist_return_code)

                # 检查值显示名称
                value_name = value.get("value_name", "")
                if len(value_name) > 20:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=too_long_return_code)
        else:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=unbound_return_code)
        # 检查共享轴

    # 检查共享轴是否存在
    def _check_shared_axis(self):
        shared_axis = self.data_source.get('shared_axis', [])
        return_code = LDEC.VISUALIZATION_SHARED_AXIS_DOESNOT_EXIST
        return_code.message = return_code.message.format(
            name=self.element_name)
        # 判断是否有新建共享轴框框
        if shared_axis:
            # 判断框框内是否绑定字段
            for shared_axis_i in shared_axis:
                field_uuid_i = shared_axis_i.get('field', '')
                self.update_field_reference(
                    field_uuid_i, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
                field_dict = self.app_field_dict.get(field_uuid_i, dict())
                if not field_uuid_i:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)
                # 判断绑定字段是否存在
                elif not field_dict:
                    return_code = LDEC.VISUALIZATION_SHARED_AXIS_FIELD_DOESNOT_EXIST
                    return_code.message = return_code.message.format(
                        name=self.element_name)
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)

        else:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)
    # 检查列图例绑定值是否存在

    def _check_column_series(self):
        return_code = LDEC.VISUALIZATION_COLUMN_SERIES_FIELD_DOESNOT_EXIST
        return_code.message = return_code.message.format(
            name=self.element_name)
        column_series = self.data_source.get("column_series", {})

        field_uuid = column_series.get('field', '')
        self.update_field_reference(
            field_uuid, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
        field_dict = self.app_field_dict.get(field_uuid, dict())
        if field_uuid and not field_dict:
            return False
        else:
            return True
    # 检查行值
    # name_value = "line_values"

    def _check_line_rows_values(self, name_value):
        line_values = self.data_source.get(name_value, [])
        return_code = LDEC.VISUALIZATION_LINE_VALUES_DOESNOT_EXIST
        return_code.message = return_code.message.format(
            name=self.element_name)
        # 判断是否有行值框框
        if line_values:
            for line_values_i in line_values:
                field_uuid_i = line_values_i.get('field', '')
                self.update_field_reference(
                    field_uuid_i, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
                field_dict = self.app_field_dict.get(field_uuid_i, dict())
                # 判断框框内是否绑定字段
                if not field_uuid_i:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)
                # 判断绑定字段是否存在

                elif not field_dict:
                    return_code = LDEC.VISUALIZATION_LINE_FIELD_DOESNOT_EXIST
                    return_code.message = return_code.message.format(
                        name=self.element_name)
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)
        else:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)
    # 检查列值name_values = "column_values"

    def _check_column_values(self, name_values):
        column_values = self.data_source.get(name_values, [])
        return_code = LDEC.VISUALIZATION_COLUMN_VALUES_DOESNOT_EXIST
        return_code.message = return_code.message.format(
            name=self.element_name)
        if column_values:

            # 判断框框内是否绑定字段
            for column_values_i in column_values:
                field_uuid_i = column_values_i.get('field', '')
                self.update_field_reference(
                    field_uuid_i, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
                field_dict = self.app_field_dict.get(field_uuid_i, dict())
                if not field_uuid_i:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)
                # 判断绑定字段是否存在
                elif not field_dict:
                    return_code = LDEC.VISUALIZATION_COLUMN_FIELD_DOESNOT_EXIST
                    return_code.message = return_code.message.format(
                        name=self.element_name)
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)
        else:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)

    # 字段里的字段field - field
    def _check_field_field(self):
        return_code = LDEC.VISUALIZATION_FIELD_DOESNOT_EXIST
        return_code.message = return_code.message.format(
            name=self.element_name)

        card_field_uuid = self.data_source.get('field', {}).get('field', '')
        self.update_field_reference(
            card_field_uuid, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
        field_dict = self.app_field_dict.get(card_field_uuid, dict())
        if not card_field_uuid:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)

        elif not field_dict:
            return_code = LDEC.VISUALIZATION_FIELD_VALUES_DOESNOT_EXIST
            return_code.message = return_code.message.format(
                name=self.element_name)
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)

    # 字段里的字段list_field - field
    def _check_list_field_field(self):
        return_code = LDEC.VISUALIZATION_FIELD_DOESNOT_EXIST
        return_code.message = return_code.message.format(
            name=self.element_name)

        card_field = self.data_source.get('field', [])
        # field_dict =  self.app_field_dict.get(card_field, dict())
        if card_field:
            for card_field_i in card_field:
                card_field_uuid = card_field_i.get('field', '')
                self.update_field_reference(
                    card_field_uuid, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)
                field_dict = self.app_field_dict.get(card_field_uuid, dict())
                if not card_field_uuid:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)

                elif not field_dict:
                    return_code = LDEC.VISUALIZATION_FIELD_VALUES_DOESNOT_EXIST
                    return_code.message = return_code.message.format(
                        name=self.element_name)
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=return_code)
        else:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)

    # 更新聚合字段被引用详情
    # def _update_reference_by_field_info(self):
    #     data_source = self.element.get("data_source", {})

    #     attr = self.attr_class.DATASOURCE
    #     element_uuid = self.element.get("uuid")
    #     title = self.element.get("name")

    #     values_list = data_source.get('values', [])
    #     value_dict = data_source.get('value', {})
    #     line_values_list = data_source.get('line_values', [])
    #     column_values_list = data_source.get('column_values', [])
    #     visual_axle_list = self.data_source.get('axis', list())
    #     legend_dict = self.data_source.get("legend", {})
    #     # 仪表值上传
    #     if value_dict:
    #         field_uuid = value_dict.get("field")
    #         self.update_field_reference(field_uuid, title, element_uuid, attr)

    #     # 值引用信息上传
    #     for values in values_list:
    #         field_uuid = values.get("field")
    #         self.update_field_reference(field_uuid, title, element_uuid, attr)

    #     # 行值引用信息上传
    #     for line_values in line_values_list:
    #         field_uuid = line_values.get("field")
    #         self.update_field_reference(field_uuid, title, element_uuid, attr)

    #     # 列值引用信息上传
    #     for column_values in column_values_list:
    #         field_uuid = column_values.get("field")
    #         self.update_field_reference(field_uuid, title, element_uuid, attr)

    #     # 轴引用信息上传
    #     for visual_axle in visual_axle_list:
    #         field_uuid = visual_axle.get("field")
    #         if field_uuid:
    #             self.update_field_reference(field_uuid, title, element_uuid, attr)

    #     # 图例引用信息上传
    #     if legend_dict:
    #         field_uuid = legend_dict.get("field")
    #         if field_uuid:
    #             self.update_field_reference(field_uuid, title, element_uuid, attr)

    @checker.run
    def check_datasource_exists(self):
        self._check_datasource_exists()


class StackedBarCheckService(DataVisualCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_StackedBar(self):
        self._check_axle_exists()
        self._check_legend_exists()
        self._check_value_exists()


class StackedColumnCheckService(DataVisualCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_StackedColumn(self):
        self._check_axle_exists()
        self._check_legend_exists()
        self._check_value_exists()


class ClusteredBarCheckService(DataVisualCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_ClusteredBar(self):
        self._check_axle_exists()
        self._check_legend_exists()
        self._check_value_exists()


class ClusteredColumnCheckService(DataVisualCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_ClusteredColumn(self):
        self._check_axle_exists()
        self._check_legend_exists()
        self._check_value_exists()


class PerStackedBarCheckService(DataVisualCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_PerStackedBar(self):
        self._check_axle_exists()
        self._check_legend_exists()
        self._check_value_exists()


class PerStackedColumnCheckService(DataVisualCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_PerStackedColumn(self):
        self._check_axle_exists()
        self._check_legend_exists()
        self._check_value_exists()


class LineCheckService(DataVisualCheckerService):
    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.model_dict = self.model_in_page.get(self.element_uuid, {})
        self.model_uuid = self.model_dict.get("model")
        self.kwargs.update({"model_uuid": self.model_uuid})
        self.attr_class = visual.ATTR

    @checker.run
    def check_LineCheck(self):
        self._check_axle_exists()
        self._check_legend_exists()
        self._check_value_exists()

    def _check_value_exists(self):
        axis_y_list = self.data_source.get('values', [])
        secondary_y_list = self.data_source.get('secondary_y', [])
        is_axis_exist = False

        if axis_y_list or secondary_y_list:
            not_exist_return_code = LDEC.VISUALIZATION_AXIS_Y_DOESNOT_EXIST
            not_exist_return_code.message = not_exist_return_code.message.format(name=self.element_name)
            too_long_return_code = LDEC.VISUALIZATION_AXIS_Y_NAME_TOO_LONG
            too_long_return_code.message = too_long_return_code.message.format(name=self.element_name)
            for axis_y in axis_y_list:
                axis_y_field_uuid = axis_y.get("field")
                if axis_y_field_uuid:
                    field_dict = self.app_field_dict.get(axis_y_field_uuid, {})
                    if not field_dict:
                        self._add_error_list(
                            attr=self.attr_class.DATASOURCE, return_code=return_code)
                    else:
                        is_axis_exist = True
                        self.update_field_reference(
                            axis_y_field_uuid, self.element_name,
                            self.element_uuid, self.attr_class.DATASOURCE)

                # 检查 Y 轴显示名称
                value_name = axis_y.get("value_name", "")
                if len(value_name) > 20:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=too_long_return_code)

            not_exist_return_code = LDEC.VISUALIZATION_SECONDARY_Y_DOESNOT_EXIST
            not_exist_return_code.message = not_exist_return_code.message.format(name=self.element_name)
            too_long_return_code = LDEC.VISUALIZATION_SECONDARY_Y_NAME_TOO_LONG
            too_long_return_code.message = too_long_return_code.message.format(name=self.element_name)
            for secondary_y in secondary_y_list:
                secondary_y_field_uuid = secondary_y.get("field")
                if secondary_y_field_uuid:
                    field_dict = self.app_field_dict.get(secondary_y_field_uuid, {})
                    if not field_dict:
                        self._add_error_list(
                            attr=self.attr_class.DATASOURCE, return_code=return_code)
                    else:
                        is_axis_exist = True
                        self.update_field_reference(
                            secondary_y_field_uuid, self.element_name,
                            self.element_uuid, self.attr_class.DATASOURCE)

                # 检查辅助 Y 轴显示名称
                value_name = secondary_y.get("value_name", "")
                if len(value_name) > 20:
                    self._add_error_list(
                        attr=self.attr_class.DATASOURCE,
                        return_code=too_long_return_code)

        if not is_axis_exist:
            return_code = LDEC.VISUALIZATION_Y_DOESNOT_EXIST
            return_code.message = return_code.message.format(name=self.element_name)
            self._add_error_list(attr=self.attr_class.DATASOURCE, return_code=return_code)


class LineStackColumnCheckService(DataVisualCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_LineStackColumn(self):
        self._check_shared_axis()
        self._check_column_values("column_values")
        self._check_line_rows_values("line_values")


class LineClusteredColumnCheckService(DataVisualCheckerService):
    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_LineClusteredColumn(self):
        self._check_shared_axis()
        self._check_column_values("column_values")
        self._check_line_rows_values("line_values")


class PieCheckService(DataVisualCheckerService):
    # 饼图的图例可添加
    def initialize(self):
        super().initialize()

    @checker.run
    def check_StackedBar(self):
        self._check_list_legend_exists()
        self._check_value_exists()


class DonutCheckService(DataVisualCheckerService):
    def initialize(self):
        super().initialize()

    def _update_details_field_reference(self):
        details = self.data_source.get("details", {})
        if details:
            details_uuid = details.get("field")
            self.update_field_reference(
                details_uuid, self.element_name, self.element_uuid, self.attr_class.DATASOURCE)

    @checker.run
    def check_StackedBar(self):
        self._check_list_legend_exists()
        self._check_value_exists()
        self._update_details_field_reference()


class ReportTableCheckService(ComponentCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR
        self.datasets = self.element.get("datasets", [])

    @checker.run
    def check_datasets(self):
        for dataset in self.datasets:
            attr = ReportTableAttr.DATASET
            data_source = dataset.get("data_source", {})
            if data_source.get("type") == 0:
                if not data_source.get("model"):
                    return_code = LDEC.DATASET_DATASOURCE_DOESNOT_EXIST
                    return_code.message = return_code.message.format(
                        name=self.element_name)
                    self._add_error_list(attr, return_code=return_code)


class TreelistCheckerService(BaseDatalistCheckerService, ColumnCheckerServiceMixin):
    attr_class = Treelist.ATTR
    uuid_error = LDEC.TREELIST_UUID_ERROR
    uuid_unique_error = LDEC.TREELIST_UUID_UNIQUE_ERROR

    @checker.run
    def check_self_association(self):
        data_source = self.element.get("data_source", dict())
        association = data_source.get("parent_association", dict())
        if not association.get("path", list()):
            self._add_error_list(
                self.attr_class.DATASOURCE,
                return_code=LDEC.SELF_ASSOCIATION_NOT_SELECT)

    @checker.run
    def check_datasource_type(self):
        attr = Treelist.ATTR.DATASOURCE
        self._check_datasource_type_multiple(attr)

    @checker.run
    def update_data(self):
        self.update_is_required()

    @checker.run
    def check_name_field(self):
        data_source = self.element.get("data_source", dict())
        name = data_source.get("name", {})
        name_field_uuid = name.get("field")
        name_field_info = self.app_field_dict.get(name_field_uuid, {})
        if not name_field_info:
            return_code = LDEC.TREE_NAME_FIELD_NOT_SELECT
            return_code.message = return_code.message.format(
                name=self.element_name)
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)
        else:
            name_field_model = name_field_info.get("model_uuid")
            data_model_uuid = self.model_dict.get("model")
            if data_model_uuid and name_field_model != data_model_uuid:
                # 名称字段是关联模型的字段
                self._add_error_list(
                    attr=self.attr_class.DATASOURCE,
                    return_code=LDEC.TREE_NAME_FIELD_NOT_SELF_MODEL)

    @checker.run
    def check_pagination(self):
        self._check_pagination()


class GAUGECheckService(DataVisualCheckerService):
    attr_class = visual.ATTR

    def initialize(self):
        super().initialize()

    # 检查值是否绑定  name_values = 'value'
    def _check_value(self):
        visual_value = self.data_source.get('value', {})
        return_code = LDEC.VISUALIZATION_VALUE_UNBOUND
        return_code.message = return_code.message.format(
            name=self.element_name)

        vaule_field_uuid = visual_value.get('field', '')
        field_dict = self.app_field_dict.get(vaule_field_uuid, dict())
        if not vaule_field_uuid:
            self._add_error_list(
                attr=self.attr_class.DATASOURCE,
                return_code=return_code)
        elif not field_dict:
            return_code = LDEC.VISUALIZATION_VALUE_FIELD_DOESNOT_EXIST
            return_code.message = return_code.message.format(
                name=self.element_name)
            self._add_error_list(attr=self.attr_class.DATASOURCE,
                                 return_code=return_code)

    @checker.run
    def check_StackedColumn(self):
        self._check_value()


class CARDCheckService(DataVisualCheckerService):
    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_CARD(self):
        self._check_field_field()


class TABLECheckService(DataVisualCheckerService):
    def initialize(self):
        super().initialize()

    @checker.run
    def check_TABLE(self):
        self._check_value_exists()


class SummaryTableCheckService(DataVisualCheckerService):
    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_TABLE(self):
        # name_values = "columns"
        self._check_column_values("columns")
        # name_values = 'rows'
        self._check_line_rows_values("rows")
        self._check_value_exists()


class ItemFilterCheckService(DataVisualCheckerService):

    def initialize(self):
        super().initialize()
        self.data_source = self.element.get("data_source", dict())
        self.attr_class = visual.ATTR

    @checker.run
    def check_TABLE(self):

        self._check_list_field_field()


class SearchBarCheckService(DataVisualCheckerService):
    def initialize(self):
        super().initialize()

    def check_search_term_fields(self):
        data_source = self.element.get("data_source", {})
        return_code = LDEC.VISUALIZATION_SEARCH_TERM_FIELDS_UNBOUND
        return_code.message = return_code.message.format(
            name=self.element_name)
        search_terms = self.element.get('items', [])  # 外部搜索项 search_term
        # 如果搜索栏的数据源为无数据源时，不需要检查搜索项
        if data_source.get("type") == 1:
            return None
        variable_on = self.element.get("variable_on", False)
        for search_term in search_terms:
            if variable_on and search_term.get("input_control") not in ['SELECT', 'CASCADERSELECT',
                                                                        'TREESELECT', 'RELATIONSELECT']:
                continue
            field_value_list = search_term.get(
                'field_value_list', [])  # 字段内部 search_term_fields，老版本
            # 字段内部 search_term_fields,新版本
            fieldsValue = search_term.get('fieldsValue', [])
            preprocessing = search_term.get("relation_preprocessing", {})
            if preprocessing:
                self.check_preprocessing_info(attr=self.attr_class.DATASOURCE,
                                              preprocessing=preprocessing)
            if not field_value_list:
                if isinstance(fieldsValue, dict):
                    field_value_list = [fieldsValue]
                else:
                    field_value_list = fieldsValue
            if field_value_list:
                for fields_i in field_value_list:
                    field_uuid_i = fields_i.get('field', '')
                    field_dict = self.app_field_dict.get(field_uuid_i, dict())
                    if not field_uuid_i:
                        self._add_error_list(
                            attr=self.attr_class.DATASOURCE,
                            return_code=return_code)
                    # 判断绑定字段是否存在
                    elif not field_dict:
                        return_code = LDEC.VISUALIZATION_SEARCH_TERM_FIELDS_DOESNOT_EXIST
                        return_code.message = return_code.message.format(
                            name=self.element_name)
                        self._add_error_list(
                            attr=self.attr_class.DATASOURCE,
                            return_code=return_code)
            else:
                self._add_error_list(
                    attr=self.attr_class.DATASOURCE,
                    return_code=return_code)

    def check_event(self):
        attr = self.attr_class.SEARCH_TERM
        search_terms = self.element.get("items", [])
        for search_term in search_terms:
            for field_dict in search_term.get("field_value_list", []):
                self.container_model_uuid = field_dict.get("model", "")
            self._check_event(attr, search_term)

    @checker.run
    def check_search_bar(self):
        self.check_search_term_fields()
        self.check_event()

    @checker.run
    def check_datasource_exists(self):
        data_source = self.element.get("data_source", {})
        if data_source.get("type") != 1:
            self._check_datasource_exists()


class CustomExtendCheckService(ComponentCheckerService):

    def _check_key_exists(self, key, values, attr, return_code):
        if key not in values:
            self._add_error_list(attr=attr, return_code=return_code)
            return False
        return True

    def _check_component_page(self, value_info, attr):
        if not value_info:
            return_code = LDEC.CUSTOM_RESTFUL_PAGE_ERROR
            self._add_error_list(attr=attr, return_code=return_code)
            return
        page_uuid = value_info.get("page_uuid")
        if page_uuid not in self.app_page_dict:
            return_code = LDEC.CUSTOM_RESTFUL_PAGE_NOT_FOUND
            self._add_error_list(attr=attr, return_code=return_code)

    def _check_component_api(self, value_info, attr):
        if not value_info:
            return_code = LDEC.CUSTOM_RESTFUL_API_ERROR
            self._add_error_list(attr=attr, return_code=return_code)
            return
        restful_uuid = value_info.get("restful_uuid")
        if restful_uuid not in self.app_restful_dict:
            return_code = LDEC.CUSTOM_RESTFUL_API_ERROR
            self._add_error_list(attr=attr, return_code=return_code)
            return
        restful_info = self.app_restful_dict.get(restful_uuid)
        auth_type = value_info.get("auth_type")
        restful_auth_type = restful_info.get("auth_type")
        if restful_auth_type != auth_type:
            value_info.update({"auth_type": restful_auth_type})
        if restful_auth_type == RestfulAuthorizationType.BASIC:
            return_code = LDEC.CUSTOM_RESTFUL_API_AUTH_ERROR
            self._add_error_list(attr=attr, return_code=return_code)
        version = value_info.get("version")
        restful_versions = restful_info.get("versions", dict())
        if not self._check_key_exists(
                version, restful_versions, attr=attr,
                return_code=LDEC.CUSTOM_RESTFUL_API_VERSION_NOT_FOUND):
            return
        resource = value_info.get("resource")
        restful_version_info = restful_versions.get(version, dict())
        restful_resources = restful_version_info.get("resources", dict())
        if not self._check_key_exists(
                resource, restful_resources, attr=attr,
                return_code=LDEC.CUSTOM_RESTFUL_API_RESOURCE_FOUND):
            return
        method = value_info.get("method")
        restful_resource_info = restful_resources.get(resource, dict())
        restful_methods = restful_resource_info.get("methods", dict())
        self._check_key_exists(
            method, restful_methods, attr=attr,
            return_code=LDEC.CUSTOM_RESTFUL_API_METHOD_FOUND)

    @checker.run
    def check_custom(self):
        ...
        # 检查选择的 API 是否还存在
        attr = "自定义组件RESTful API"
        attr_bind = self.element.get("custom", {}).get("attr_bind", {})
        for _, bind_info in attr_bind.items():
            component = bind_info.get("component")
            value_info = bind_info.get("value", dict())
            if component == CustomComponentType.API:
                self._check_component_api(value_info, attr)
            if component == CustomComponentType.PAGE:
                self._check_component_page(value_info, attr)


class TransferCheckerService(BaseInputCheckerService):

    attr_class = Transfer.ATTR
    uuid_error = LDEC.TRANSFER_UUID_ERROR
    uuid_unique_error = LDEC.TRANSFER_UUID_UNIQUE_ERROR
    name_error = LDEC.TRANSFER_NAME_FAILED
    name_unique_error = LDEC.TRANSFER_NAME_NOT_UNIQUE

    @checker.run
    def check_datasource_exists(self):
        data_source = self.element.get("data_source", {})
        if data_source:
            path = data_source.get("path", [])
            if path:
                p = path[0]
                r_finder = self.page_finder.relationship_finder
                relationship = r_finder.relationship_basic_map.get(p, {})
                if relationship:
                    relation_extra = relationship.extra
                    if relationship.source_model != self.parent.get("model_uuid"):
                        is_required = relation_extra.get(
                            "source_required", False)
                    else:
                        is_required = relation_extra.get(
                            "target_required", False)
                    placeholder = data_source.setdefault("placeholder", dict())
                    # relation_required = False if is_many else is_required
                    placeholder.update(
                        {"is_required": is_required})
        self._check_datasource_exists()

    @checker.run
    def check_display_columns(self):
        attr = self.attr_class.DISPLAY_COLUMN
        display_columns = self.element.get("display_columns", [])
        left_column = False
        for column in display_columns:
            self._check_value_editor(
                column, attr, self.app_field_dict, self.app_relationship_dict)
            used_in_right = column.get("used_in_right")
            left_column = left_column or used_in_right
        if not left_column:
            self._add_error_list(
                attr, return_code=LDEC.TRANSFER_RIGHT_COLUMN_NOT_EXIST)

    @checker.run
    def check_search_columns(self):
        attr = self.attr_class.SERACH_COLUMN
        search_setting = self.element.get("search", {})
        if search_setting.get("open"):
            search_columns = search_setting.get("columns")
            for column in search_columns:
                self._check_value_editor(
                    column, attr, self.app_field_dict, self.app_relationship_dict)


class DropdownMenuCheckerService(ComponentCheckerService):
    attr_class = DropdownMenu.ATTR
    uuid_error = LDEC.DROPDOWN_MENU_UUID_ERROR
    uuid_unique_error = LDEC.DROPDOWN_MENU_UUID_UNIQUE_ERROR
    name_error = LDEC.DROPDOWN_MENU_NAME_FAILED
    name_unique_error = LDEC.DROPDOWN_MENU_NAME_NOT_UNIQUE

    def initialize(self):
        super().initialize()

    @checker.run
    def check_text_content(self):
        attr = self.attr_class.DISPLAY
        style = self.element.get("dropdown_style", 0)
        if style == DropdownStyle.TEXT_STYLE or style == DropdownStyle.BUTTON_STYLE:
            text_content = self.element.get("dropdown_text_content", {})
            if text_content:
                self._check_value_editor(text_content, attr, self.app_field_dict,
                    self.app_relationship_dict, position=attr)

    def check_menus(self):
        menus = self.element.get("dropdown", [])
        kwargs = {
            "model_uuid": getattr(self, "model_uuid", None),
            "app_func_uuid_dict": self.app_func_uuid_dict,
            "document_other_info": self.document_other_info,
            "module_tag_dict": self.module_tag_dict,  # 权限资源标签用
            "app_model_list": self.app_model_list,
            "app_field_dict": self.app_field_dict,
            "app_field_list": self.app_field_list,
            "app_relationship_list": self.app_relationship_list,
            "app_relationship_dict": self.app_relationship_dict,
            "app_workflow_list": self.app_workflow_list,
            "app_page_dict": self.app_page_dict,
            "app_label_print_dict": self.app_label_print_dict
        }

        for menu in menus:
            if menu:
                menu_checker_service = MenuCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, self.document_uuid,
                    self.document_name, menu, **kwargs)
                menu_checker_service.check_all()
                self.update_any_list(menu_checker_service)


class MenuCheckerService(ComponentCheckerService):
    attr_class = Menu.ATTR
    uuid_error = LDEC.MENU_UUID_ERROR
    uuid_unique_error = LDEC.MENU_UUID_UNIQUE_ERROR
    name_error = LDEC.MENU_NAME_FAILED
    name_unique_error = LDEC.MENU_NAME_NOT_UNIQUE

    def initialize(self):
        return super().initialize()

    @checker.run
    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_event(attr)

    @checker.run
    def check_menu_content(self):
        # 检查菜单显示内容
        menu_content = self.element.get("menu_content", None)
        self._check_value_editor(
            menu_content, self.attr_class.BASIC_CONF, self.app_field_dict,
            self.app_relationship_dict, position=self.attr_class.BASIC_CONF)


class ExcelCheckerService(ComponentCheckerService):

    attr_class = Univer.ATTR

    def initialize(self):
        return super().initialize()

    @checker.run
    def check_data_variables(self):
        attr = self.attr_class.DATAVARIABLES
        variables = self.element.get("variables") or list()
        variables_name = set()
        return_code = LDEC.UNIVER_VARIABLES_NAME_NOT_UNIQUE
        for index in range(len(variables)):
            variable = variables[index]
            variable_name = variable.get("name")
            if variable_name in variables_name:
                return_code.message = return_code.message.format(name=f"数据变量{index}")
                self._add_error_list(attr, return_code=return_code, element_data=self.element)
            else:
                variables_name.add(variable_name)
