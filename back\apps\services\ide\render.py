# -*- coding:utf-8 -*-
import os
from typing import Dict

from jinja2 import Template
import ujson

from apps.utils import lemon_uuid
from apps.ide_const import ConstType, LemonDesignerErrorCode as LDEC
from apps.services.document import Func
from apps.services.document import EnumTable

ENUM_TEMPLATE = """class {{ enum_name }}(LemonEnum):
    {% if value -%}
    {% for v in value -%}
    {% set name = v.get("name") -%}
    {% if name -%}
    {% set value = v.get("value", name) -%}
    {{ name }} = ("{{ value }}", {{ v.get("icon_type") }}, '{{ ujson.dumps(v.get("icon")) }}', "{{ v.get("color") }}", "{{ v.get("uuid") }}")
    {% endif -%}
    {% endfor %}
    {% else -%}
    pass
    {% endif -%}"""
FUNC_TEMPLATE = """{{ func_name }} = FuncWrapper({{ func_dict }}, {{ module_dict }})"""
PY_MODULE_FUNC_TEMPLATE = """{{ func_name }} = FuncWrapper({{ func_dict }}, {{ module_dict }}, lemon_py_module.py_module_uuid_mapping.get("{{ func_dict.get("document_uuid") }}").get_local("{{func_name}}"))"""
PY_MODULE_TEMPLATE = """{{ py_module_name }} = PyModuleWrapper({{ py_module_dict }}, {{ module_dict }})
py_module_uuid_mapping["{{ document_uuid }}"] = {{ py_module_name }}"""

class BaseRender(object):

    def __init__(self):
        pass

    @property
    def template(self):
        return None

    @property
    def vars(self):
        return dict()

    def render(self):
        if self.template is None:
            return ""
        tmpl = Template(self.template)
        return tmpl.render(self.vars)


class ConstRender(BaseRender):

    def __init__(self, const):
        super().__init__()
        self.const_type = const.const_type
        self.const_name = const.const_name
        self.value = const.value

    @property
    def template(self):
        if self.const_type == ConstType.STRING:
            return "{{ const_name }} = {{ value.__repr__() }}"
        return "{{ const_name }} = {{ value }}"

    @property
    def vars(self):
        return dict(
            const_name=self.const_name,
            value=self.value
        )


class EnumRender(BaseRender):

    def __init__(self, enum):
        super().__init__()
        self.enum_name = enum.enum_name
        self.value = enum.value
        self.uuid = enum.uuid

    @property
    def template(self):
        return ENUM_TEMPLATE

    @property
    def vars(self):
        return dict(
            enum_name=self.enum_name,
            value=self.value,
            ujson=ujson
        )


class FuncRender(BaseRender):

    def __init__(self, func, module_dict):
        super().__init__()
        self.func_name = func.func_name
        self.func_dict: Func = Func(
            uuid=func.func_uuid,
            name=func.func_name,
            description=func.description,
            func=func.func,
            arg_list=func.arg_list,
            return_list=func.return_list,
            is_async=False,
            is_system=func.is_system,
            from_py_module=func.from_py_module,
            document_uuid=func.document_uuid
        ).to_dict()
        self.module_dict = module_dict

    @property
    def template(self):
        if self.func_dict.get("from_py_module"):
            return PY_MODULE_FUNC_TEMPLATE
        return FUNC_TEMPLATE

    @property
    def vars(self):
        return dict(
            func_name=self.func_name,
            func_dict=self.func_dict,
            module_dict=self.module_dict
        )
class PyModuleRender(BaseRender):
    def __init__(self, document_uuid: str, py_module_dict: Dict, module_dict):
        super().__init__()
        self.py_module_name = py_module_dict.get("name")
        self.doc = py_module_dict
        self.module_dict = module_dict
        self.document_uuid = document_uuid


    @property
    def template(self):
        return PY_MODULE_TEMPLATE

    @property
    def vars(self):
        return dict(
            py_module_name=self.py_module_name,
            py_module_dict=self.doc,
            module_dict=self.module_dict,
            document_uuid=self.document_uuid
        )



class WfRender(BaseRender):

    def __init__(self, workflow):
        super().__init__()
        self.wf_name = workflow.wf_name
        self.wf_dict = {
            "wf_name": workflow.wf_name,
            "wf_uuid": workflow.wf_uuid
        }

    @property
    def template(self):
        return "{{ wf_name }} = {{ wf_dict }}"

    @property
    def vars(self):
        return dict(
            wf_name=self.wf_name,
            wf_dict=self.wf_dict,
        )


class BaseRenderService(object):

    def __init__(self, save_path, obj_list):
        self.save_path = save_path
        self.obj_list = obj_list

    @property
    def coding_str(self):
        return "# -*- coding:utf-8 -*-"

    @property
    def import_str(self):
        return ""

    def do_render(self):
        render_str_list = []
        for obj in self.obj_list:
            render_str_list.append(self.render_class(obj).render())
        return "\n".join(render_str_list) + "\n"

    def save(self):
        render_str = self.do_render()
        if not os.path.exists(self.save_path):
            try:
                os.makedirs(self.save_path)
            except:
                # 异步执行可能目录已存在,临时解决方式
                pass
        file_str = "\n\n".join([self.coding_str, self.import_str, render_str])
        file_path = "/".join([self.save_path, self.file_name])
        with open(file_path, "w+") as file_open:
            file_open.write(file_str)


class ConstRenderService(BaseRenderService):

    def __init__(self, save_path, obj_list):
        super().__init__(save_path, obj_list)
        self.render_class = ConstRender
        self.file_name = "lemon_const.py"


class EnumRenderService(BaseRenderService):

    def __init__(self, save_path, obj_list):
        super().__init__(save_path, obj_list)
        self.render_class = EnumRender
        self.file_name = "lemon_enum.py"

    @property
    def import_str(self):
        return "from runtime.utils import LemonEnum"


class FuncRenderService(BaseRenderService):

    def __init__(self, save_path, obj_list):
        super().__init__(save_path, obj_list)
        self.render_class = FuncRender
        self.file_name = "lemon_func.py"

    @property
    def import_str(self):
        import_str_list = [
            "import traceback",
            "from runtime.core.func import FuncWrapper",
            "from contextlib import suppress",
            "with suppress(Exception):",
            "    from . import lemon_py_module"
        ]
        return "\n".join(import_str_list)

    def do_render(self):
        render_str = ""
        try_str = "try:\n    "
        except_str = "\nexcept Exception:\n    traceback.print_exc()\n\n"
        for obj, module in self.obj_list:
            r_str = self.render_class(obj, module).render()
            render_str += try_str + r_str + except_str
        return render_str

class PyModuleRenderService(BaseRenderService):
    def __init__(self, save_path, obj_list):
        super().__init__(save_path, obj_list)
        self.file_name = "lemon_py_module.py"

    @property
    def import_str(self):
        return "\n".join([
            "import traceback",
            "from runtime.core.py_module import PyModuleWrapper",
            "py_module_uuid_mapping = {}"
        ])

    def do_render(self):
        render_result = []
        indent = " " * 4
        for document_uuid, obj, module in self.obj_list:
            r_str = PyModuleRender(document_uuid, obj, module).render()
            definition_lines = [f"{indent}{line}" for line in r_str.splitlines()]
            render_result.extend([
                "try:",
                *definition_lines,
                "except Exception:",
                f"{indent}traceback.print_exc()"
            ])
        return "\n".join(render_result)

class WfRenderService(BaseRenderService):

    def __init__(self, save_path, obj_list):
        super().__init__(save_path, obj_list)
        self.render_class = WfRender
        self.file_name = "lemon_workflow.py"


if __name__ == "__main__":
    enum_value = [
        {"name": "idle", "icon_type": 0, "icon": None, "color": "FFFFFF"},
        {"name": "active", "icon_type": 0, "icon": None, "color": "000000"},
    ]
    enum = EnumTable(uuid=lemon_uuid(), name="status", description="", value=enum_value)
    enum.enum_name = enum.name
    enum_render = EnumRender(enum)
    print(enum_render.render())

    func = Func(
        uuid=lemon_uuid(), name="send_email", description="",
        func="await asyncio.sleep(0.01)", arg_list=[], return_list=[]
    )
    func.func_uuid = func.uuid
    func.func_name = func.name
    module_dict = {"module_uuid": lemon_uuid(), "name": "main"}
    func_render = FuncRender(func, module_dict)
    print(func_render.render())
