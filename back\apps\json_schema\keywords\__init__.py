from apps.json_schema.keywords.main import (
    lemon_uuid, reference, unique, error_message,
    is_element, attr_name
)
from apps.json_schema.keywords.monkey import (
    properties, items, prefixItems, if_
)
from apps.json_schema.keywords.func import python_block

KEYWORDS_STORE = {
    "properties": properties,
    "items": items,
    "lemon_uuid": lemon_uuid,
    "reference": reference,
    "unique": unique,
    "errorMessage": error_message,
    "is_element": is_element,
    "attr_name": attr_name,
    "if": if_,
    "python_block": python_block,
}


KEYS_PRIORITES = {
    "is_element": 0,
    "attr_name": 0.1,
    "properties": 10,
    "errorMessage": 99
}
