# -*- coding:utf-8 -*-

import weakref
import asyncio
from functools import partial
from concurrent.futures.process import ProcessPoolExecutor

from baseutils.log import app_log
from apps.base_utils import (
    join_search_path, is_main_thread, lemon_uuid, make_tenant_sys_table_copy
)
from apps.ide_const import OperatorType, ComponentType, DictWrapper
from apps.utils import gen_password_salt_hash, Flow
from apps.exceptions import BackGroundError, PopupSubmitCloseError
from apps.base_entity import CurrentUserModel, CurrentDepartment
from baseutils.file_obj import RuntimeFileObj, ImageFieldValue, FileFieldValue, ExportTemplate
from runtime.ext import ModuleWrapper
from runtime.const import WhereExprType
from runtime.utils import (
    add_tenant_member, build_join_query,
    count_next_datetime_by_step, del_role_member, del_tenant_member,
    lemon_where, create_tenant_user, create_tenant_department, add_job_number_user,
    del_tenant_user, del_tenant_department, list_department_members, manage_nodetask, manage_workflow,
    update_department_user_rel, submit_flow, handle_flow, make_current_user, get_page_url_json, get_page_list,
    get_tenant_id, process_role_member_fk_model, Scheduler, gen_user_model_graph, list_relationship_route,
    get_user_menu_list, list_enable_tags, log_out_role_user, send_role_member_change_msg, get_ctx_tenant_uuid,
    alter_user_password, batch_assign_workflow, delete_dlist_option, do_login, update_user_enable_status,
    get_tenant_member_obj, list_active_system_users
)
from runtime.engine import engine
from runtime.core.func import LemonSMValueEditor, FuncWrapper
from runtime.core.utils import (
    LemonActor, LemonCommand, LemonEvent, PopupSubmit, build_join,
    build_hierarchy_query, build_relation_query, get_page, get_container,
    lemon_field, lemon_model, lemon_model_node, lemon_association,
    MessageBox, Console, page_refresh, page_print, container_refresh, LemonLock,
    get_obj_by_name, get_container_by_name, OSSBucket, get_main_subordinate_data,
    CurrentEnv, run_in_atomic, run_out_atomic, container_validate, get_container_control_info,
    container_reset, KafkaConsumer, run_async_query, async_sleep, async_add_task, async_add_task_later,
    run_in_atomic_nowait
)
from runtime.core.data_preprocess import DataPreprocess
from runtime.log import runtime_log_sync


"""
柠檬命名空间
lemon
    system : 系统变量和引用
        user : 当前用户
        roles : 当前角色的列表
        context : dict 形式的上下文对象
        sqcode : 扫码完成后的结果值
        report_variables : 报表变量
    utils : 工具
        actor : 发送事件或命令
        value_editor : 运行值编辑器
    sm : 状态机的引用
        machine_id : 状态机ID
        current_event : 状态机当前事件
        current_state : 状态机当前状态
        globals : 状态机定义的全局变量
        locals : 状态机当前状态定义的局部变量
"""


def run_in_process(func, *args, **kwargs):
    async_func = _run_in_process(func, *args, **kwargs)
    future = asyncio.run_coroutine_threadsafe(async_func, engine.loop)
    return future.result()


class UserWrapper(DictWrapper):

    def initialize(self):
        self._uuid = None
        self._user_name = None
        self._full_name = None

    @property
    def uuid(self):
        return self._uuid

    @uuid.setter
    def uuid(self, value):
        self._uuid = value

    @property
    def user_name(self):
        return self._user_name

    @user_name.setter
    def user_name(self, value):
        self._user_name = value

    @property
    def full_name(self):
        return self._full_name

    @full_name.setter
    def full_name(self, value):
        self._full_name = value


class ModelWrapper(DictWrapper):

    def initialize(self):
        self._uuid = None
        self._name = None
        self._module_name = None
        self.update({
            "uuid": self._uuid, "name": self._name,
            "module_name": self._module_name
        })

    @property
    def uuid(self):
        return self._uuid

    @uuid.setter
    def uuid(self, value):
        self._uuid = value
        self.update({"uuid": self._uuid})

    @property
    def name(self):
        return self._name

    @name.setter
    def name(self, value):
        self._name = value
        self.update({"name": self._name})

    @property
    def module_name(self):
        return self._module_name

    @module_name.setter
    def module_name(self, value):
        self._module_name = value
        self.update({"module_name": self._module_name})


class ContextWrapper(DictWrapper):

    def initialize(self):
        self._pk = None
        self._model = ModelWrapper()
        self.update({"pk": self._pk, "model": self._model})

    @property
    def pk(self):
        return self._pk

    @pk.setter
    def pk(self, value):
        self._pk = value
        self.update({"pk": self._pk})

    @property
    def model(self):
        return self._model

    def clone(self):
        obj = super().clone()
        obj._model = self._model.clone()
        return obj


class ReportVariable(DictWrapper):

    def initialize(self):
        self._system_variable = {}

    def __getattr__(self, name):
        if name in self._system_variable:
            return self._system_variable.get(name)
        if name in self:
            return self.get(name)
        return None

    def set_system_variable(self, varlaible: dict):
        self._system_variable = varlaible

    def clone(self):
        obj = super().clone()
        obj.initialize()
        return obj


class ExceptionWrapper(DictWrapper):

    # 注意是 **类**
    _background_error = BackGroundError
    _popup_submit_close_error = PopupSubmitCloseError

    @property
    def background_error(self):
        return self._background_error

    @background_error.setter
    def background_error(self, value):
        self._background_error = value

    @property
    def popup_submit_close_error(self):
        return self._popup_submit_close_error

    @popup_submit_close_error.setter
    def popup_submit_close_error(self, value):
        self._popup_submit_close_error = value


class SystemWrapper(DictWrapper):

    exceptions = ExceptionWrapper()
    current_env = CurrentEnv()

    def initialize(self):
        self.init_attrs()
        self.current_env = CurrentEnv()
        self._user = UserWrapper()
        self._context = ContextWrapper()
        self._report_variables = ReportVariable()

    def init_attrs(self):
        self._roles = []
        self._sqcode = None
        self._obj = None
        self._objs = []
        self._parent_obj = None
        self._context_trigger_obj = None
        self._current_user = None
        self._current_dep = None
        self._extern_data = None
        self._cell_data = None  # excel导入一个单元格的数据
        self._cell_row = -1  # excel导入单元格行号
        self._excel_info = dict()  # excel导入信息
        self._dynamic_data = None
        self._mapping_key = None
        self._current_obj_class = None

    def clone(self):
        obj = super().clone()
        obj.init_attrs()
        obj._user = self._user.clone()
        obj._context = self._context.clone()
        obj._report_variables = self._report_variables.clone()
        return obj

    @property
    def current_obj_class(self):
        return self._current_obj_class

    @current_obj_class.setter
    def current_obj_class(self, value):
        self._current_obj_class = value

    @property
    def dynamic_data(self):
        return self._dynamic_data

    @dynamic_data.setter
    def dynamic_data(self, value):
        # 和mapping_key 类似 dynamic_data 也和form有关
        self._dynamic_data = value

    @property
    def mapping_key(self):
        # TODO 应该首先return control_obj.mapping_key_result
        return self._mapping_key

    @mapping_key.setter
    def mapping_key(self, value):
        self._mapping_key = value

    @property
    def mapping_value(self):
        if isinstance(self._dynamic_data, dict):
            return self._dynamic_data.get(self._mapping_key)
        return None

    @property
    def user(self):
        return self._current_user

    @property
    def roles(self):
        return self._roles

    @roles.setter
    def roles(self, value):
        self._roles = value

    @property
    def context(self):
        return self._context

    @context.setter
    def context(self, value):
        self._context = value

    @property
    def sqcode(self):
        return self._sqcode

    @sqcode.setter
    def sqcode(self, value):
        self._sqcode = value

    @property
    def obj(self):
        return self._obj

    @obj.setter
    def obj(self, value):
        self._obj = value

    @property
    def objs(self):
        return self._objs

    @objs.setter
    def objs(self, value):
        self._objs = value

    @property
    def selected_objs(self):
        # 凡哥说：objs 这个名字取的不好，应该用 selected_objs
        return self.objs

    @property
    def parent_obj(self):
        if self._parent_obj is None:
            return None
        obj = self._parent_obj()
        if callable(obj):
            return obj()
        return None

    @parent_obj.setter
    def parent_obj(self, value):
        self._parent_obj = value

    @property
    def context_trigger_obj(self):
        if self._context_trigger_obj is None:
            return None
        obj = self._context_trigger_obj()
        if callable(obj):
            return obj()
        return None

    @context_trigger_obj.setter
    def context_trigger_obj(self, value):
        self._context_trigger_obj = value

    @property
    def current_user(self):
        return self._current_user

    @current_user.setter
    def current_user(self, value):
        self._current_user = value

    @property
    def current_dep(self):
        return self._current_dep

    @current_dep.setter
    def current_dep(self, value):
        self._current_dep = value

    def update_user(self, current_user: CurrentUserModel):
        if current_user is not None:
            self.roles = current_user.roles
            self.current_user = current_user
            self.current_dep = self.update_department(current_user)

    def update_department(self, current_user):
        current_dep_obj = None
        departments = current_user.departments
        for department_uuid, department in departments.items():
            if department.get("is_major"):
                department_data = {
                    "id": department.get("department_pk"),
                    "department_uuid": department_uuid,
                    "department_name": department.get("department_name"),
                    "department_level": department.get("department_level"),
                    "job": department.get("job")
                }
                current_dep_obj = CurrentDepartment(**department_data)
        return current_dep_obj

    @property
    def extern_data(self):
        return self._extern_data

    @extern_data.setter
    def extern_data(self, value):
        self._extern_data = value

    @property
    def cell_data(self):
        return self._cell_data

    @cell_data.setter
    def cell_data(self, value):
        self._cell_data = value

    @property
    def cell_row(self):
        return self._cell_row

    @cell_row.setter
    def cell_row(self, value):
        self._cell_row = value

    @property
    def excel_info(self):
        return self._excel_info

    @excel_info.setter
    def excel_info(self, value):
        self._excel_info = value

    @property
    def report_variables(self):
        return self._report_variables

    @report_variables.setter
    def report_variables(self, value):
        self._report_variables = value

    @property
    def app_location(self):
        localcation_list = [engine.config.APP_LOCALTION,
                            self._current_user.tenant_uuid]
        return "/".join(localcation_list)

    @property
    def real_app_location(self):
        localcation_list = [engine.config.REAL_APP_LOCALTION,
                            self._current_user.tenant_uuid]
        return "/".join(localcation_list)

    @property
    def flow_link(self):
        return "/".join([self.app_location, "isShareWorkflow?isShare=true&task_uuid="])

    @property
    def wf_instance(self):
        return "/".join([self.app_location, "isShareWorkflow?isShare=true&isManage=true&wf_instance_uuid="])

    @property
    def node_task(self):
        return "/".join([self.app_location, "isShareWorkflow?isShare=true&isManage=false&task_uuid="])

    @property
    def permission_config(self):
        return "show_permission_config&"

    @property
    def tenant_id(self):
        return get_tenant_id()

    @property
    def tenant_uuid(self):
        return get_ctx_tenant_uuid()

    @property
    def app_env(self):
        return engine.config.APP_ENV_NAME

    @property
    def local_deploy(self):
        return engine.config.LOCAL_DEPLOY


class ConstWrapper(DictWrapper):

    operator_type = OperatorType
    where_expr_type = WhereExprType
    component_type = ComponentType


class UtilsWrapper(DictWrapper):

    def initialize(self):
        self.lemon_log = app_log
        self.log = runtime_log_sync
        self.sleep = async_sleep
        self.add_task = async_add_task
        self.add_task_later = async_add_task_later
        self.lemon_editor = LemonSMValueEditor()
        self.lemon_actor = LemonActor()
        self.lemon_event = LemonEvent
        self.lemon_command = LemonCommand
        self.message_box = MessageBox()
        self.console = Console()
        self.popup_submit = PopupSubmit()
        self.lemon_lock = LemonLock()
        self.oss_bucket = OSSBucket(engine)
        self.scheduler = Scheduler()
        self.eval = eval
        self.data_preprocessing = DataPreprocess
        self.build_join = build_join
        self.join_search_path = join_search_path
        self.build_join_query = build_join_query
        self.build_relation_query = build_relation_query
        self.build_hierarchy_query = build_hierarchy_query
        self.count_next_datetime_by_step = count_next_datetime_by_step
        self.get_page = get_page
        self.page_refresh = page_refresh
        self.page_print = page_print
        self.get_container = get_container
        self.container_refresh = container_refresh
        self.container_reset = container_reset
        self.lemon_where = lemon_where
        self.lemon_field = lemon_field
        self.lemon_model = lemon_model
        self.lemon_model_node = lemon_model_node
        self.lemon_association = lemon_association
        self.lemon_uuid = lemon_uuid
        self.gen_password_salt_hash = gen_password_salt_hash
        self.make_tenant_sys_table_copy = make_tenant_sys_table_copy
        self.create_tenant_user = create_tenant_user
        self.create_tenant_department = create_tenant_department
        self.del_tenant_user = del_tenant_user
        self.del_tenant_department = del_tenant_department
        self.update_department_user_rel = update_department_user_rel
        self.add_tenant_member = add_tenant_member
        self.del_tenant_member = del_tenant_member
        self.list_department_members = list_department_members
        self.get_obj_by_name = get_obj_by_name
        self.get_container_by_name = get_container_by_name
        self.submit_flow = submit_flow
        self.handle_flow = handle_flow
        self.manage_nodetask = manage_nodetask
        self.manage_workflow = manage_workflow
        self.batch_assign_workflow = batch_assign_workflow
        self.make_current_user = make_current_user
        self.get_main_subordinate_data = get_main_subordinate_data
        self.del_role_member = del_role_member
        self.run_in_atomic = run_in_atomic
        self.run_in_atomic_nowait = run_in_atomic_nowait
        self.run_out_atomic = run_out_atomic
        self.run_in_process = run_in_process
        self.run_async_query = run_async_query
        self.add_job_number_user = add_job_number_user
        self.container_validate = container_validate
        self.get_container_control_info = get_container_control_info
        self.process_role_member_fk_model = process_role_member_fk_model
        self.user_menu_list = get_user_menu_list
        self.list_enable_tags = list_enable_tags
        self.log_out_role_user = log_out_role_user
        self.flows = engine.workflows
        self.sys_uuid = engine.uuids
        self.pubsub = engine.pubsub
        self._models = {}
        self.send_role_member_change_msg = send_role_member_change_msg
        self._user_model_graph = {}
        self.list_relationship_route = list_relationship_route
        self.user_relationship_route = {}
        self.kafka_consumer = KafkaConsumer
        self.alter_user_password = alter_user_password
        self.delete_dlist_option = delete_dlist_option
        self.do_login = do_login
        self.update_user_enable_status = update_user_enable_status
        self.get_tenant_member_obj = get_tenant_member_obj
        self.list_active_system_users = list_active_system_users

    def clone(self):
        return super().clone()

    @property
    def urls(self):
        return get_page_url_json()

    @property
    def page_list(self):
        return get_page_list()

    @property
    def models(self):
        models_dict = {}
        if not self._models:
            for module_name, models in engine.models.items():
                if module_name == "系统模块":
                    # XXX 如果之后系统模块有用户创建的模型, 这儿会有问题
                    module_models = models_dict.setdefault(module_name, {})
                    for name, model in models.items():
                        if '\u4e00' <= name[0] <= '\u9fa5':
                            module_models[name] = model
                else:
                    models_dict[module_name] = models
            self._models.update(models_dict)
        return self._models

    @property
    def user_model_graph(self):
        if not self._user_model_graph:
            self._user_model_graph = gen_user_model_graph()
        return self._user_model_graph


class SMWrapper(DictWrapper):

    pass


class FileSystemWrappe():

    @property
    def create_file(self):
        return RuntimeFileObj

    @property
    def image_field(self):
        return ImageFieldValue

    @property
    def file_field(self):
        return FileFieldValue

    @property
    def export_template(self):
        return ExportTemplate


class LemonWrapper(DictWrapper):

    _sm = SMWrapper()
    consts = ConstWrapper()
    filesystem = FileSystemWrappe()

    def initialize(self):
        self._system = SystemWrapper()
        self._utils = UtilsWrapper()
        self.flow = Flow()

    @property
    def system(self):
        return self._system

    @system.setter
    def system(self, value):
        self._system = value

    @property
    def utils(self):
        return self._utils

    @utils.setter
    def utils(self, value):
        self._utils = value

    def __getattr__(self, name):
        obj = getattr(self.system, name, None)
        if not obj:
            obj = engine.modules.get(name)
        app_log.info(f"namespace, name: {name}, is_main_t: {is_main_thread()}")
        return obj

    def clone(self):
        obj = super().clone()
        obj._system = self._system.clone()
        obj._utils = self._utils.clone()
        return obj


lemon_wrapper = LemonWrapper()


def __run_in_process_by_str(module_name, func_name, *args, **kwargs):
    module_wrapper = getattr(lemon_wrapper, module_name, None)
    if module_wrapper:
        func_wrapper = getattr(module_wrapper, func_name, None)
        if func_wrapper:
            return func_wrapper(*args, **kwargs)
    return None


async def _run_in_process(func, *args, **kwargs):
    app_log.info(f"in process, func: {func}")
    with ProcessPoolExecutor(max_workers=1) as pool:
        if isinstance(func, FuncWrapper):
            module_name = func.module_name
            func_name = func.func_name
            result = await engine.loop.run_in_executor(
                pool, partial(
                    __run_in_process_by_str, module_name, func_name,
                    *args, **kwargs)
            )
        else:
            func = partial(func, *args, **kwargs)
            result = await engine.loop.run_in_executor(pool, func)
    return result
