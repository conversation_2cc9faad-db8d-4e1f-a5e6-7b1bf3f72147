# -*- coding:utf-8 -*-

from baseutils.log import app_log
from apps.entity import ConstTable
from apps.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CheckUUI<PERSON>rror, CheckUUIDUniqueError
from apps.ide_const import Const
from apps.ide_const import LemonDesigner<PERSON>rrorCode as LDEC
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker


class ConstCheckerService(CheckerService):

    attr_class = Const.ATTR
    uuid_error = LDEC.CONST_UUID_ERROR
    uuid_unique_error = LDEC.CONST_UUID_UNIQUE_ERROR
    name_error = LDEC.CONST_NAME_FAILED
    name_unique_error = LDEC.CONST_NAME_NOT_UNIQUE
    allow_chinese_name = True
    allow_keywords = False
    allow_lemon_keywords = False
    
    def initialize(self):
        super().initialize()
        self.description = self.element.get("description")
        self.type = self.element.get("type")
        self.value = self.element.get("value")
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            ConstTable.app_uuid.name: self.app_uuid,
            ConstTable.module_uuid.name: self.module_uuid,
            ConstTable.document_uuid.name: self.document_uuid,
            ConstTable.const_uuid.name: self.element_uuid,
            ConstTable.const_name.name: self.element_name,
            ConstTable.description.name: self.description,
            ConstTable.const_type.name: self.type,
            ConstTable.value.name: self.value
        }
    
    def build_update_query_data(self):
        return {
            ConstTable.const_name.name: self.element_name,
            ConstTable.description.name: self.description,
            ConstTable.const_type.name: self.type,
            ConstTable.value.name: self.value,
            ConstTable.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return ConstTable.update(**query_data).where(
            ConstTable.const_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(const_uuid):
        return ConstTable.update(**{
                ConstTable.is_delete.name: True
            }).where(ConstTable.const_uuid==const_uuid)
    
    def check_modify(self, document_const_uuid_set):
        if self.value:
            if self.element_uuid in document_const_uuid_set:
                query = self.build_update_query()
                query_list = self.update_query_list
            else:
                query = self.build_insert_query_data()
                query_list = self.insert_query_list
            query_list.append(query)
    
    # 检查常量类型是否支持，如果不支持，会向错误列表添加一条报错信息
    @checker.run
    def check_const_type(self):
        attr = self.attr_class.TYPE
        return_code = LDEC.CONST_TYPE_NOT_SUPPORT
        return_code.message = return_code.message.format(name=self.element_name)
        if self.type not in Const.TYPE.ALL:
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查常量值是否与常量类型匹配，如果不匹配，会向错误列表添加一条报错信息
    @checker.run
    def check_const_value(self):
        attr = self.attr_class.VALUE
        python_class_dict = Const.TYPE.TO_PYTHON_CLASS
        python_class = python_class_dict.get(self.type, None)
        if python_class is not None and self.value is not None:
            # 根据指定的类型来尝试转换, 无法成功转换则type-value不匹配
            return_code = LDEC.CONST_VALUE_INCURRECT
            return_code.message = return_code.message.format(name=self.element_name)

            if python_class != python_class_dict.get(Const.TYPE.BOOLEAN):
                try:
                    # 整数也可认为是小数
                    python_class(self.value)
                except ValueError as e:
                    self._add_error_list(attr=attr, return_code=return_code)
            else:
                if self.value not in ["True", "False"]:
                    self._add_error_list(attr=attr, return_code=return_code)


class ConstDocumentCheckerService(DocumentCheckerService):
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, document_version: int, 
        app_const_list: list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, ConstTable, *args, **kwargs)
        self.const_list = self.element.get("const_list", list())
        self.app_const_uuid_set = set()
        self.module_const_name_set = set()
        self.document_const_uuid_set = set()
        self.document_const_update_list = list()
        self.document_const_delete_list = list()
        for const in app_const_list:
            const_uuid = const.get("const_uuid", "")
            const_name = const.get("const_name", "")
            const_module_uuid = const.get("module_uuid", "")
            const_document_uuid = const.get("document_uuid", "")

            # 找到原文档中所有的 常量，为了新增、更新、删除文档的 常量
            if const_document_uuid == self.document_uuid:
                self.document_const_uuid_set.add(const_uuid)
            else:
                # 排除当前文档所有的 const_uuid ，获取应用的所有 const_uuid
                self.app_const_uuid_set.add(const_uuid)
                # 排除当前文档所有的 const_uuid ，获取模块的所有 const_uuid
                if const_module_uuid == self.module_uuid:
                    self.module_const_name_set.add(const_name)
        self.other_conflict_document_names_set = kwargs.get("other_conflict_document_names_set", set())

    @checker.run
    def check_const_list(self):
        this_document_const_uuid_set = set()
        for const in self.const_list:
            if self.is_copy:
                temp_name = const.get("name")
                const.update({"name": temp_name + "_" + str(self.document_number)})
                # self.module_const_name_set.add(const.get("name"))
            const_checker_service = ConstCheckerService(
                    self.app_uuid, self.module_uuid, self.module_name, 
                    self.document_uuid, self.document_name, const, is_copy=self.is_copy)
            const_uuid = const_checker_service.element_uuid
            this_document_const_uuid_set.add(const_uuid)
            try:
                const_checker_service.check_uuid()
                const_checker_service.check_uuid_unique(self.app_const_uuid_set)
                const_checker_service.check_name()
                const_checker_service.check_name_unique(self.module_const_name_set)
                const_checker_service.check_name_unique(self.other_conflict_document_names_set)
                const_checker_service.check_all()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(const_checker_service)
                raise e
            else:
                self.update_any_list(const_checker_service)

            # 找到新增 或 更新的 常量
            const_checker_service.check_modify(self.document_const_uuid_set)
            if const_checker_service.insert_query_list:
                self.document_insert_list.extend(const_checker_service.insert_query_list)
            if const_checker_service.update_query_list:
                self.document_update_list.extend(const_checker_service.update_query_list)

        # 找出删除的 常量，将其 is_delete 置为 True
        delete_const_uuid_set = self.document_const_uuid_set - this_document_const_uuid_set
        for this_const_uuid in delete_const_uuid_set:
            query = ConstCheckerService.build_delete_query(this_const_uuid)
            self.document_delete_list.append(query)
