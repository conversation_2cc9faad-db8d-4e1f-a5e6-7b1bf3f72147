#!/bin/bash
RED='\033[0;31m'
NC='\033[0m'  
systemctl start docker || true
DIR="$( cd "$( dirname "$0"  )" && pwd  )"
cd $DIR
source ./config.sh
docker network create --subnet=$subnet --gateway=$gateway --opt "com.docker.network.bridge.name"="lemon-bridge" lemon-bridge || true
cd "$(dirname "$DIR" )"
docker-compose -p $app_env up -d
NEW_MAX_CONNECTIONS=${mysql_max_size}
web_code="404"
for i in `seq 1 60`
    do 
        if [ $web_code == "200" ]; then
            break
        else
            web_code=$(curl --max-time 5  --write-out %{http_code} --silent --output /dev/null http://127.0.0.1:${nginx_port}/${app_env}/api/runtime/v1/check_app_alive.json)
        fi
        if [ $web_code != "200" ]
        then
        sleep 2
        fi
    done
if [ $web_code != "200" ] 
then
echo -e "${RED}服务连接超时！${NC}"
else
docker exec -it mysql-$app_env  mysql -u${mysql_user} -p${mysql_pwd} -h${mysql_host} -P${mysql_real_port} -e "SET GLOBAL max_connections = $NEW_MAX_CONNECTIONS;"
echo -e  "\e[32m服务已启动\e[0m"
fi