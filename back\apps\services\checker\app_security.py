import asyncio

from apps.entity import <PERSON><PERSON><PERSON><PERSON>, UserRoleContent, APPSetting
from apps.services import DocumentCheckerService, CheckerService
from apps.services.checker import checker
from baseutils.log import app_log


class APPSecurityCheckerService(CheckerService):
    
    def __init__(self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, document_name: str, element: dict, *args, **kwargs):
        super().__init__(app_uuid, module_uuid, module_name, document_uuid, document_name, element, *args, **kwargs)
        self.app_module_map = kwargs.get("app_module_map")
        self.user_role_list_exist = kwargs.get("user_role_list_exist")
        self.app_user_role_content = kwargs.get("app_user_role_content")
        self.admin_user_role_uuid = [user_role["role_uuid"] for user_role in self.user_role_list_exist if user_role["is_admin"]]
        self.user_role_uuid_list_exist = [user_role["role_uuid"] for user_role in self.user_role_list_exist]
        self.app_module_role = kwargs.get("app_module_role")
        self.app_setting = kwargs.get("app_setting")

        self.user_role_insert = list()
        self.user_role_update = list()
        self.role_content_insert = list()
        self.role_content_update = list()
        self.app_setting_insert = list()
        self.app_setting_update = list()
        
        
    def gen_user_role_query(self):
        user_role_list = self.element.get("user_role_list", list())

        for idx, user_role in enumerate(user_role_list):
            user_role_uuid = user_role["uuid"]
            data = {
                    UserRole.app_uuid.name: self.app_uuid,
                    UserRole.role_name.name: user_role["name"],
                    UserRole.order.name: idx,
                    UserRole.is_delete.name: False, 
                    UserRole.document_uuid.name: self.document_uuid
                }
            if user_role_uuid in self.user_role_uuid_list_exist:
                query = UserRole.update(**data).where(UserRole.role_uuid==user_role_uuid)
                self.user_role_update.append(query)
                self.user_role_uuid_list_exist.remove(user_role_uuid)
            else:
                data.update({
                    UserRole.role_uuid.name: user_role_uuid, 
                    UserRole.is_admin.name: user_role.get("is_admin", False)
                })
                self.user_role_insert.append(data)
            role_content_list_exist = self.app_user_role_content.get(user_role_uuid, list())
            role_content_module_uuid_list_exist = [content["module_uuid"] for content in role_content_list_exist]
            
            for module_permission in user_role.get("permission", []):
                module_name = module_permission["name"]
                permission = module_permission["content"]
                if module_name not in self.app_module_map:   # 用户传了错误的模块名，或模块已删除
                    module_uuid = module_permission.get("module_uuid")
                else:
                    module_uuid = self.app_module_map[module_name]
                if not module_uuid:
                    continue
                module_role_list = self.app_module_role.get(module_uuid, list())
                module_role_uuid_map = {module_role["role_uuid"]: module_role["role_name"] for module_role in module_role_list}
                permission = [{"uuid": role["uuid"]} for role in permission if role["uuid"] in module_role_uuid_map]     #去掉不属于该模块的模块角色

                data = {
                    UserRoleContent.module_role.name: permission,
                    UserRoleContent.is_delete.name: False, 
                    UserRoleContent.document_uuid.name: self.document_uuid
                }
                
                if module_uuid in role_content_module_uuid_list_exist:
                    query = UserRoleContent.update(**data).where(UserRoleContent.user_role==user_role_uuid, UserRoleContent.module_uuid==module_uuid)
                    self.role_content_update.append(query)
                    role_content_module_uuid_list_exist.remove(module_uuid)
                else:
                    data.update(
                        {UserRoleContent.user_role.name: user_role_uuid,
                        UserRoleContent.module_uuid.name: module_uuid}
                    )
                    self.role_content_insert.append(data)
            for module_uuid in role_content_module_uuid_list_exist:
                data = dict(
                    is_delete = True
                )
                query = UserRoleContent.update(**data).where(UserRoleContent.user_role==user_role_uuid, UserRoleContent.module_uuid==module_uuid)
                self.role_content_update.append(query)
        for user_role_uuid in self.user_role_uuid_list_exist:
            if user_role_uuid in self.admin_user_role_uuid:
                #系统管理员不可删除
                continue
            data = dict(
                is_delete = True
            )
            query = UserRole.update(**data).where(UserRole.role_uuid==user_role_uuid)
            self.user_role_update.append(query)
        
    def gen_security_query(self):
        security_setting = self.element.get("app_setting", dict())
        # TODO 如果app_setting在数据苦衷不存在
        data = dict(
            permission_check = security_setting.get("permission_check"),
            anonymous = security_setting.get("anonymous"),
            anonymous_role = security_setting.get("anonymous_role"), 
            document_uuid = self.document_uuid,
        )
        model = APPSetting
        if self.app_setting:
            query = model.update(**data).where(model.app_uuid==self.app_uuid)
            self.app_setting_update.append(query)
        else:
            data.update({
                # APPSetting.document_uuid.name: self.document_uuid, 
                APPSetting.app_uuid.name: self.app_uuid
            })
            self.app_setting_insert.append(data)
        
        
    def check_modify(self):
        self.gen_user_role_query()
        self.gen_security_query()


class APPSecurityDocumentCheckerService(DocumentCheckerService):
    
    def __init__(self, app_uuid: str, module_uuid: str, module_name: str, document_uuid: str, document_name: str, element: dict, model: UserRole, *args, **kwargs):
        super().__init__(app_uuid, module_uuid, module_name, document_uuid, document_name, element, model, *args, **kwargs)
        self.app_module_map = kwargs.get("app_module_map")
        self.user_role_list_exist = kwargs.get("user_role_list_exist")
        self.app_user_role_content = kwargs.get("app_user_role_content")
        self.app_module_role = kwargs.get("app_module_role")
        self.app_setting = kwargs.get("app_setting")

        self.user_role_insert = list()
        self.user_role_update = list()
        self.role_content_insert = list()
        self.role_content_update = list()
        self.app_setting_insert = list()
        self.app_setting_update = list()

    @checker.run
    def check_app_security(self):
        app_security_checker_service = APPSecurityCheckerService(
            self.app_uuid, self.module_uuid, self.module_name, 
            self.document_uuid, self.document_name, self.element,
            is_copy=self.is_copy, app_module_map=self.app_module_map, 
            user_role_list_exist=self.user_role_list_exist, 
            app_user_role_content=self.app_user_role_content, 
            app_module_role=self.app_module_role, app_setting=self.app_setting)
        app_security_checker_service.document_other_info = self.document_other_info
        app_security_checker_service.check_modify()
        
        for key in ["user_role_insert", "user_role_update", "role_content_insert", 
                    "role_content_update", "app_setting_update", "app_setting_insert"]:
            l = getattr(app_security_checker_service, key, None)
            if l:
                setattr(self, key, l)
        
    async def commit_modify(self, engine):
        # user_role_update_funcs = list()
        # role_content_update_funcs = list()
        # app_setting_update_funcs = list()
        async with engine.db.objs.atomic():
            for u in self.user_role_update:
                func = engine.access.update_obj_by_query(UserRole, u, need_delete=True)
                # user_role_update_funcs.append(func)
                await func
            for u in self.role_content_update:
                func = engine.access.update_obj_by_query(UserRoleContent, u, need_delete=True)
                # role_content_update_funcs.append(func)
                await func
            for u in self.app_setting_update:
                func = engine.access.update_obj_by_query(APPSetting, u, need_delete=True)
                # app_setting_update_funcs.append(func)
                await func

            if self.user_role_insert:
                await engine.access.insert_many_obj(UserRole, self.user_role_insert)
            if self.role_content_insert:
                await engine.access.insert_many_obj(UserRoleContent, self.role_content_insert)
            if self.app_setting_insert:
                await engine.access.insert_many_obj(APPSetting, self.app_setting_insert)

            # await asyncio.gather(*user_role_update_funcs)
            # await asyncio.gather(*role_content_update_funcs)
            # await asyncio.gather(*app_setting_update_funcs)
