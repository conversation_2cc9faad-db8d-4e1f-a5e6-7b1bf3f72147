# -*- coding:utf-8 -*-

import copy
import decimal
import datetime
import time
import asyncio
from traceback import print_exc
from typing import Any, Callable
from functools import partial
from collections import OrderedDict
from baseutils.const import CalculateType, UserTableStatus
import ujson
import re
from playhouse import shortcuts
from peewee import (BigBitField, BooleanField, DecimalField, Field, DoubleField,
                 CharField, TextField, FixedCharField, ForeignKeyAccessor, IntegerField, ManyToManyField,
                 ManyToManyFieldAccessor, ManyToManyQuery, SchemaManager, VirtualField, BigIntegerField,
                 TimestampField, ForeignKeyField, BackrefAccessor, AutoField, 
                 ObjectIdAccessor, DeferredThroughModel, FieldAccessor, ModelSelect)
from asgiref.sync import AsyncToSync
import peewee
import peewee_async
import weakref
import weakreflist

from apps.ide_const import FieldType, PermissionDeploy, DictWrapper, HierarchyType
from baseutils.const import SystemTable
from apps.base_utils import (
    is_main_thread, GlobalVars,
    get_sys_field_uuid, is_system_field, default_select_fields,
    safe_pop, get_memory_id
)
from baseutils.log import app_log
from baseutils.utils import (
    LemonContextVar, make_tenant_sys_table_copy, LEMON_FIELD_DEFINE, CVE
)
from baseutils.file_obj import RuntimeFileObj, FileFieldValue, ImageFieldValue


__all__ = [
    "CurrencyField",
    "PhoneField"
]


"""
Field Type	Sqlite	Postgresql	MySQL
AutoField	integer	serial	integer
BigAutoField	integer	bigserial	bigint
IntegerField	integer	integer	integer
BigIntegerField	integer	bigint	bigint
SmallIntegerField	integer	smallint	smallint
IdentityField	not supported	int identity	not supported
FloatField	real	real	real
DoubleField	real	double precision	double precision
DecimalField	decimal	numeric	numeric
CharField	varchar	varchar	varchar
FixedCharField	char	char	char
TextField	text	text	text
BlobField	blob	bytea	blob
BitField	integer	bigint	bigint
BigBitField	blob	bytea	blob
UUIDField	text	uuid	varchar(40)
BinaryUUIDField	blob	bytea	varbinary(16)
DateTimeField	datetime	timestamp	datetime
DateField	date	date	date
TimeField	time	time	time
TimestampField	integer	integer	integer
IPField	integer	bigint	bigint
BooleanField	integer	boolean	bool
BareField	untyped	not supported	not supported
ForeignKeyField	integer	integer	integer
"""

class DatetimeFn(peewee.Function):
    def __init__(self, name, arguments, coerce=True, python_value=None, db_value=None):
        super().__init__(name, arguments, coerce=coerce, python_value=python_value)
        self._db_value = db_value


    def __eq__(self, rhs):
        if not isinstance(rhs, peewee.Node):
            rhs = peewee.Value(rhs, converter=self._db_value, unpack=False)
        return super().__eq__(rhs)

    def __ne__(self, rhs):
        if not isinstance(rhs, peewee.Node):
            rhs = peewee.Value(rhs, converter=self._db_value, unpack=False)
        return super().__ne__(rhs)

    def contains(self, rhs):
        if not isinstance(rhs, peewee.Node):
            rhs = peewee.Value(rhs, converter=self._db_value, unpack=False)
        return super().contains(rhs)
    def startswith(self, rhs):
        if not isinstance(rhs, peewee.Node):
            rhs = peewee.Value(rhs, converter=self._db_value, unpack=False)
        return super().startswith(rhs)
    def endswith(self, rhs):
        if not isinstance(rhs, peewee.Node):
            rhs = peewee.Value(rhs, converter=self._db_value, unpack=False)
        return super().endswith(rhs)
    def between(self, lo, hi):
        if not isinstance(lo, peewee.Node):
            lo = peewee.Value(lo, converter=self._db_value, unpack=False)
        if not isinstance(hi, peewee.Node):
            hi = peewee.Value(hi, converter=self._db_value, unpack=False)
        return super().between(lo, hi)
    def concat(self, rhs):
        if not isinstance(rhs, peewee.Node):
            rhs = peewee.Value(rhs, converter=self._db_value, unpack=False)
        return super().conconcattains(rhs)
    def regexp(self, rhs):
        if not isinstance(rhs, peewee.Node):
            rhs = peewee.Value(rhs, converter=self._db_value, unpack=False)
        return super().regexp(rhs)
    def iregexp(self, rhs):
        if not isinstance(rhs, peewee.Node):
            rhs = peewee.Value(rhs, converter=self._db_value, unpack=False)
        return super().iregexp(rhs)

    def __getattr__(self, attr):
        def decorator(*args, **kwargs):
            return DatetimeFn(attr, args, **kwargs)
        return decorator


datetime_fn = DatetimeFn(None, None)

_quarter_chinese = ["第一季度", "第二季度", "第三季度", "第四季度"]
_quarter_en = ["Q1", "Q2", "Q3", "Q4"]
_quarter_all = [_quarter_chinese, _quarter_en]

_month_num = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]
_month_chinese = ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]
_month_en_abbr = []
_month_en_full = []
_month_all = [_month_num, _month_en_abbr, _month_en_full, _month_chinese]

_weekday_chinese = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"]
_weekday_en_abbr = []
_weekday_en_full = []
_weekday_int = [7, 1, 2, 3, 4, 5, 6]
_weekday_all = [_weekday_en_abbr, _weekday_en_full, _weekday_chinese, _weekday_int]


def date_part_format_int(value):
    return value

def date_part_format_str(value):
    return value

def date_part_format_str_int(value):
    return (str(value) if value >= 10 else f"0{value}") if value is not None else None

def date_part_format_quarter(value, format):
    try:
        return _quarter_all[format][value - 1]
    except:
        return value

def date_part_quarter_db_value(value, format):
    try:
        return _quarter_all[format].index(value) + 1
    except:
        return value

def date_part_format_month(value, format):
    try:
        return _month_all[format][value - 1]
    except:
        return value

def date_part_month_db_value(value, format):
    try:
        return _month_all[format].index(value) + 1
    except:
        return value

def date_part_format_week(value):
    return f"第{value}周"

def date_part_week_db_value(value):
    try:
        return int(re.findall(r"第(\d+)周", value)[0])
    except:
        return value

def date_part_format_day_no(value):
    return f"今年第{value}天"

def date_part_day_no_db_value(value):
    try:
        return int(re.findall(r"今年第(\d+)天", value)[0])
    except:
        return value

def date_part_format_day(value):
    return f"{value}日"

def date_part_format_weekday(value, format):
    try:
        return _weekday_all[format][int(value)]
    except:
        return value

def date_part_weekday_db_value(value, format):
    try:
        return _weekday_all[format].index(value) + 1
    except:
        return value

def date_part_format_hour(value):
    if value <= 12:
        return f"{date_part_format_str_int(value)} am"
    else:
        return f"{date_part_format_str_int(value - 12)} pm"

def date_part_hour_db_value(value):
    try:
        value_int = int(value[:-3])
        if "am" in value:
            return value_int
        elif "pm" in value:
            return value_int + 12
        return value
    except:
        return value

def extract_date(date_part, date_field, part_python_format, db_value=None):
    return datetime_fn.EXTRACT(peewee.NodeList((peewee.SQL(date_part), peewee.SQL('FROM'), date_field)), 
            python_value=part_python_format, db_value=db_value)

def _date_format(date_format, date_field, part_python_format, db_value=None):
    return datetime_fn.DATE_FORMAT(date_field, date_format, python_value=part_python_format, db_value=db_value)

def lemon_date_part(date_part, part_python_format=date_part_format_int, db_value=None):
    def dec(self):
        # app_log.info(f"lemon_date_part: {part_python_format}")
        val = extract_date(date_part, datetime_fn.FROM_UNIXTIME(self), part_python_format, db_value=db_value)
        # app_log.info(f"lemon_date_part: {val}, {isinstance(val, DatetimeFn)}")
        return val
    return dec

def lemon_data_format(date_format, part_python_format=date_part_format_int, db_value=None):
    def dec(self):
        val = _date_format(date_format, datetime_fn.FROM_UNIXTIME(self), part_python_format, db_value=db_value)
        # app_log.info(f"lemon_data_format: {val}")
        return val
    return dec
"""
"part_format_map": { 
                "0": [2020],
                "1": ["第一季度", "Q1", 1],
                "2": ["1-12月", "Jan-Dec", "January-December", "1-12"],
                "3": ["第几周"],
                "4": ["0-31", "00-31", "1-31日", "今年第x天"],
                "5": ["Mon-Sun", "Monday-Sunday", "星期一-星期日", "1-7"],
                "6": ["00-24", "00-12 am/pm"],
                "7": ["00-59"],
                "8": ["00-59"]
              }
"""
date_part_format_map = {
    "0": [lemon_date_part("year", part_python_format=date_part_format_int)],
    "1": [
        lemon_date_part("quarter", partial(date_part_format_quarter, format=0), db_value=partial(date_part_quarter_db_value, format=0)), 
        lemon_date_part("quarter", partial(date_part_format_quarter, format=1), db_value=partial(date_part_quarter_db_value, format=0)),
        lemon_date_part("quarter", date_part_format_int)
        ],
    "2": [
        lemon_date_part("month", partial(date_part_format_month, format=0), db_value=partial(date_part_month_db_value, format=0)),
        lemon_data_format("%b", date_part_format_str),
        lemon_data_format("%M", date_part_format_str),
        lemon_date_part("month", date_part_format_int)
    ],
    "3": [lemon_date_part("week", date_part_format_week, db_value=date_part_week_db_value)],
    "4": [
        lemon_date_part("day", date_part_format_int),
        lemon_date_part("day", date_part_format_str_int),
        lemon_date_part("day", date_part_format_day),
        lemon_data_format("%j", date_part_format_day_no, db_value=date_part_day_no_db_value)   # %j	年的天 (001-366)
    ],
    "5": [  
        lemon_data_format("%a", date_part_format_str),   # %w 周的天 （0=星期日, 6=星期六）
        lemon_data_format("%W", date_part_format_str),
        lemon_data_format("%w", partial(date_part_format_weekday, format=2), db_value=partial(date_part_weekday_db_value, format=2)),
        lemon_data_format("%w", partial(date_part_format_weekday, format=3), db_value=partial(date_part_weekday_db_value, format=3))
    ],
    "6": [lemon_date_part("hour", date_part_format_str_int), 
        lemon_date_part("hour", date_part_format_hour, db_value=date_part_hour_db_value)],
    "7": [lemon_date_part("minute", date_part_format_str_int)],
    "8": [lemon_date_part("second", date_part_format_str_int)],
}
date_attr_map = ["year", "quarter", "month", "week", "day", "weekday", "hour", "minute", "second"]
freq_date_type_dict = {"年": "Y", "月": "M", "日": "D", "时": "H", "分": "T", "秒": "S"}
freq_hierarchy_type_dict = {
    HierarchyType.YEAR: 'Y', HierarchyType.QUARTER: '3M', HierarchyType.MONTH: "M",
    HierarchyType.WEEK: "7D", HierarchyType.DAY: "D", HierarchyType.WEEKDAY: "D",
    HierarchyType.HOUR: "H", HierarchyType.MINUTE: "T", HierarchyType.SECOND: "S"}


def add_db_obj(query, value=None, validate=False):
    if value is not None:
        value._add_instance(force_update=True)
    if not is_main_thread() and not query._v_is_backref_dirty:
        add_obj_from_db = False
        for obj in query:
            if value is not None and obj == value:
                # 要 add 的value，有可能已经在 query 里了
                continue
            add_obj_from_db = True
            pk = obj.get_id()
            query._v_backrefs_dict.update({pk: obj})
            query._v_backrefs.append(obj)
            query._backrefs_dict.update({pk: obj})
            query._backrefs.append(obj)
        if add_obj_from_db:
            query._add_dirty(validate=validate)


class LemonDelete(peewee.Delete):
    def __sql__(self, ctx: peewee.Context):
        super(peewee.Delete, self).__sql__(ctx)
        # pprint.pprint((1111, self.table))
        with ctx.scope_values(subquery=True):
            ext_model = getattr(self.table, "_ext_model", None)
            if ext_model is not None:
                # print(666666, ext_model)
                # print(333333333333, ctx.query(), ctx.scope)
                table = self.table._meta.table
                ext_table = ext_model._meta.table
                if table._alias:
                    ctx.alias_manager[table] = table._alias
                if ext_table._alias:
                    ctx.alias_manager[ext_table] = ext_table._alias
                entity = peewee.Entity(ctx.alias_manager[table])

                ext_entity = peewee.Entity(ctx.alias_manager[ext_table])

                (ctx.literal("DELETE ").sql(entity).literal(", ").sql(ext_entity).literal(" FROM ").sql(
                    table).literal(' AS ').sql(entity).literal(' %s ' % peewee.JOIN.LEFT_OUTER).sql(
                    ext_table).literal(' AS ').sql(ext_entity))
                # (ctx
                #     .sql(self.table)
                #     .literal(' %s ' % peewee.JOIN.LEFT_OUTER)
                #     .sql(ext_model).literal(' ON ').sql(self.table._meta.primary_key==ext_model.relation_id))
                # print(555555, ctx.query())
                ctx.literal(' ON ').literal(" ( ").sql(entity).literal(".").sql(self.table._meta.primary_key).literal(
                    " = ").sql(ext_entity).literal(".").sql(ext_model.relation_id).literal(" ) ")
            else:
                ctx.literal('DELETE FROM ').sql(self.table)
            if self._where is not None:
                with ctx.scope_normal():
                    ctx.literal(' WHERE ').sql(self._where)

            self._apply_ordering(ctx)
            return self.apply_returning(ctx)


class LemonModelQueryHelper(object):

    # 当用户自己编写 query 并执行 query.execute() 的时候
    # 会执行这里的逻辑

    _objs_: peewee_async.Manager = None
    _main_loop_: asyncio.events.AbstractEventLoop = None

    def _execute(self, database):
        if self._objs_ is None:
            return super()._execute(database)
        else:
            sync_exector = AsyncToSync(self._objs_.submit_execute)
            result = sync_exector(self, self._main_loop_)
            self._cursor_wrapper = result
            return result

    def _get_backrefs_and_backrefs_dict(self, validate):
        if validate:
            _backrefs = self._v_backrefs
            _backrefs_dict = self._v_backrefs_dict
            _remove_backrefs = self._v_remove_backrefs
        else:
            _backrefs = self._backrefs
            _backrefs_dict = self._backrefs_dict
            _remove_backrefs = self._remove_backrefs
        return _backrefs, _backrefs_dict, _remove_backrefs

    def _add_when_not_validate(
            self, value, pk, memory_pk, clear_existing=True):
        if clear_existing and pk > 0 and memory_pk in self._v_backrefs_dict:
            del self._v_backrefs_dict[memory_pk]
        self._v_backrefs_dict.update({pk: value})
        self._v_backrefs.append(value)
        if value in self._v_remove_backrefs:
            self._v_remove_backrefs.remove(value)
        self._add_dirty(validate=True)

    def _add_value(
            self, value, _backrefs: list, _backrefs_dict: dict, _remove_backrefs: list,
            pk, memory_pk, validate=False, clear_existing=True):
        if clear_existing and pk > 0 and memory_pk in _backrefs_dict:
            del _backrefs_dict[memory_pk]
        _backrefs_dict.update({pk: value})
        _backrefs.append(value)
        if value in _remove_backrefs:
            _remove_backrefs.remove(value)
        self._add_dirty(validate=validate)

    def _replace_value(
            self, value, _backrefs: list, _backrefs_dict: dict, _remove_backrefs: list,
            des_attr=None, validate=False, clear_existing=False):
        if value:
            memory_pk = get_memory_id(value)
            pk = value.get_id() or memory_pk
            self._add_value(
                value, _backrefs, _backrefs_dict, _remove_backrefs, pk, memory_pk,
                validate=validate, clear_existing=clear_existing)
            if not validate:
                if des_attr:
                    setattr(value, des_attr, self._instance)
                self._add_when_not_validate(
                    value, pk, memory_pk, clear_existing=clear_existing)

    def _pre_replace(
            self, _backrefs: list, _backrefs_dict: dict, _remove_backrefs: list, extend, validate):
        if not extend:
            _backrefs.clear()
            _backrefs_dict.clear()
            if not validate:
                self._v_backrefs.clear()
                self._v_backrefs_dict.clear()
        self._add_dirty(validate=validate, replace=True)


class LemonExtFieldAccessor(dict):
    def __getattr__(self, key):
        return self.get(key)

    def add_field(self, name, field):
        self.update({name: field})


class LemonExtModelAccessor(dict):
    def __getattr__(self, key):
        return self.get(key)

    def add_model(self, model):
        self.update({model.__name__: model})


class LemonHierarchyItem(peewee.Column):
    pass


class LemonColumnFactory(object):
    __slots__ = ('node',)

    def __init__(self, node):
        self.node = node

    def __getattr__(self, attr):
        return LemonHierarchyItem(self.node, attr)


class Lemon_DynamicColumn(object):
    __slots__ = ()

    def __get__(self, instance, instance_type=None):
        app_log.info(instance)
        if instance is not None:
            return LemonColumnFactory(instance)  # Implements __getattr__().
        return self


class LemonHierarchyAccessor(peewee.FieldAccessor):
    pass


class LemonFieldMixin(object):
    hierarchy_entity = OrderedDict()
    hierarchy_accessor = LemonHierarchyAccessor
    # def __new__(cls, *args, **kwargs):
    #     instance = super().__new__(cls)
    #     instance.init_base(*args, **kwargs)
    #     return instance
    # todo: aggre field, hierarchy field 改成 virtual field

    def __init__(self, kwargs):
        self.is_define = True
        self._init_kwargs(kwargs)
        if self.calculate_field is True or self.aggre_field is True:
            self.hierarchy_field = 0
            kwargs.update({"null": True})
        # if self.is_unique:
        #     kwargs.update({"unique": True})
        self._init_hierarchy_attr()

    @property
    def model(self):
        if (
            self._model_class
            and
            self._model_class._meta.table_name in SystemTable.UUIDS
            and not getattr(self._model_class._meta, "is_copy", False)
        ):
            # 兼容处理； 运行时系统表 需要查找出 实际的模型类
            current_user = None
            try:
                current_user = LemonContextVar.current_user.get()
            except:
                app_log.info("current_user key not exist")
            if current_user and current_user.tenant_id:
                self._model_class = make_tenant_sys_table_copy(self._model_class)
        return self._model_class

    @model.setter
    def model(self, value):
        self._model_class = value

    def _init_kwargs(self, kwargs):
        self.lemon_type = safe_pop(kwargs, "lemon_type", FieldType.INTEGER)
        self.is_unique = safe_pop(kwargs, "is_unique", False)
        self.is_required = safe_pop(kwargs, "is_required", False)
        self.is_visible = safe_pop(kwargs, "is_visible", True)
        self.calculate_field = safe_pop(kwargs, "calculate_field", False)
        self.calculate_function = safe_pop(kwargs, "calculate_function", {})
        self.generated_function = safe_pop(kwargs, "generated_function", {})
        self.generation_expression = safe_pop(kwargs, "generation_expression", "")
        self.calculate_type = safe_pop(kwargs, "calculate_type", 0)
        self.calculate_func = safe_pop(kwargs, "calculate_func", "")
        self.aggre_field = safe_pop(kwargs, "aggre_field", False)
        self.aggre_function = safe_pop(kwargs, "aggre_func", {})
        self.chart_aggre_function = copy.deepcopy(self.aggre_function)
        self.chart_aggre_field = copy.deepcopy(self.aggre_field)
        self.hierarchy_field = safe_pop(kwargs, "hierarchy_field", 0)
        self.hierarchy_list = safe_pop(kwargs, "hierarchy_list", [])
        self.check_function = safe_pop(kwargs, "check_function", {})
        self.check_error_message = safe_pop(kwargs, "check_error_message", "")
        self.default_value_dict = safe_pop(kwargs, "default_value_dict", {})
        self.check_expr_instance = None
        self.is_serial = safe_pop(kwargs, "is_serial", False)
        self.serial_prefix = safe_pop(kwargs, "serial_prefix", "")
        self.serial_prefix_info = safe_pop(kwargs, "serial_prefix_info", dict())
        self.serial_rule = safe_pop(kwargs, "serial_rule", 0)
        self.serial_num_length = safe_pop(kwargs, "serial_num_length", 0)
        self.serial_gen_type = safe_pop(kwargs, "serial_gen_type", 0)
        self.serial_start_num = safe_pop(kwargs, "serial_start_num", 1)
        self.is_system = safe_pop(kwargs, "is_system", False)
        self.is_relationship = safe_pop(kwargs, "is_relationship", False)
        self.hide = safe_pop(kwargs, "hide", False)

    def _init_hierarchy_attr(self):

        if self.hierarchy_field == 1:
            pass
        elif self.hierarchy_field == 2:
            if self.hierarchy_list is not None:
                # if len(self.hierarchy_list) == 0:
                #     for date_part in ["year", "month", "day", "hour", "minute", "second"]:
                #         attr = property(lemon_date_part(date_part))
                #         setattr(LemonFieldMixin, date_part, attr)    #? 不知道为啥不能用self
                #         self.hierarchy_entity[date_part] = attr
                for hierarchy in self.hierarchy_list:
                    date_part = str(hierarchy.get("date_part"))
                    part_format = hierarchy.get("part_format")
                    part_format_func = date_part_format_map.get(date_part, [])[part_format]
                    hierarchy_name = date_attr_map[int(date_part)]
                    hierarchy_attr = property(part_format_func)

                    setattr(LemonFieldMixin, hierarchy_name, hierarchy_attr)    # ? 不知道为啥不能用self
                    self.hierarchy_entity[hierarchy_name] = hierarchy_attr


calc_depend_all_field_map = {}
calc_depend_field_map, calc_fields_map = {}, {}  # models中进行初始化


def convert_field_value(field, value):
    lemon_type = getattr(field, "lemon_type", None)
    if lemon_type == FieldType.DATETIME:
        if value is None:
            return value
        value = value if isinstance(value, int) else 0
        value = datetime.datetime.fromtimestamp(value)
    return value


def add_instance_when_set_pk(field, instance):
    if not is_main_thread():
        if field.name == instance._meta.primary_key.name:
            instance._add_instance(force_update=True)


class LemonFieldAccessor(FieldAccessor):

    def __init__(self, model, field, name):
        super().__init__(model, field, name)

    def __get__(self, instance, instance_type=None):
        if getattr(self.field, "calculate_field", False) and (
                getattr(self.field, "calculate_type", 0) != CalculateType.GENERATED_COLUMN):
            model_uuid = self.model._meta.table_name
            if instance and calc_fields_map.get(model_uuid):
                # 把计算字段都计算一遍
                calc_fields_topo: list[Field] = calc_fields_map[model_uuid]
                eval_globals = DictWrapper()
                for column_name, field in self.model._meta.columns.items():
                    name = field.name
                    value = instance.get_data(name)  # 避免调用自身
                    value = convert_field_value(field, value)
                    if is_system_field(field):
                        column_name = get_sys_field_uuid(
                            model_uuid, field.name)
                    eval_globals.update({column_name: value, name: value})
                this_column_name = self.field.column_name
                from apps.base_entity import lemon_entity
                value = None
                for field in calc_fields_topo:
                    if field.calculate_type == 0:  # 表达式计算
                        f = AsyncToSync(lemon_entity.async_eval_expr)
                        value = f(field.calculate_function, globals=eval_globals)
                        if field.lemon_type == FieldType.DECIMAL:
                            value = value if value is None else decimal.Decimal(str(value))
                    else:
                        f = AsyncToSync(lemon_entity.async_eval_func)
                        value = f(field.calculate_func, instance)
                    if field.column_name == this_column_name:
                        break
                    eval_globals.update({field.column_name: value,
                                         field.name: value})
                # 最后一个value
                return value
        if instance is not None:
            if self.field and self.field.lemon_type == FieldType.DATETIME:
                if not is_main_thread():
                    _time = instance.get_data(self.name)
                    if _time is None:
                        return _time
                    if isinstance(_time, (datetime.datetime, datetime.date)):
                        return _time
                    current_user = LemonContextVar.current_user.get()
                    if current_user is not None:
                        timezone = current_user.timezone
                    else:
                        timezone = 8
                    _time += timezone * 3600
                    return datetime.datetime.utcfromtimestamp(_time)
            return instance.get_data(self.name)
        return self.field

    def __set__(self, instance, value):
        if self.field and self.field.lemon_type == FieldType.DATETIME:
            if not is_main_thread():
                if isinstance(value, (datetime.datetime, datetime.date)):
                    # if value.tzinfo:
                    #     timezone = int((value.tzinfo.utcoffset(value).total_seconds())/3600)
                    # else:
                    #     timezone_info = \
                    #         datetime.datetime.now(datetime.timezone.utc
                    #                               ).astimezone().timetz().utcoffset()
                    #     timezone = int(timezone_info.total_seconds()/3600)
                    # value = time.mktime(value.timetuple()) - timezone*3600
                    value = time.mktime(value.timetuple())
        validate = True if self.field.calculate_field is True else False
        instance.update_validate({self.name: value}, validate=validate, diff=True)
        super().__set__(instance, value)
        # add_instance_when_set_pk(self.field, instance)


class LemonFileFieldAccessor(LemonFieldAccessor):

    def __set__(self, instance, value):
        if isinstance(value, FileFieldValue):
            value = value.file_list
        elif isinstance(value, RuntimeFileObj):
            field_value = FileFieldValue()
            field_value.add(value)
            value = field_value.file_list
        return super().__set__(instance, value)


class LemonImageFieldAccessor(LemonFieldAccessor):

    def __set__(self, instance, value):
        if isinstance(value, ImageFieldValue):
            value = value.file_list
        elif isinstance(value, RuntimeFileObj):
            field_value = ImageFieldValue()
            field_value.add(value)
            value = field_value.file_list
        return super().__set__(instance, value)


class LemonFileObject(object):
    def __init__(self, files) -> None:
        self.files = files


class LemonFileAccessor(FieldAccessor):

    def __init__(self, model, field, name):
        super().__init__(model, field, name)

    def __get__(self, instance, instance_type=None):
        value = []
        if instance:
            value = instance.get_data(self.name)
        return LemonFileObject(value)

    def __set__(self, instance, value):
        if isinstance(value, LemonFileObject):
            value = value.files
        instance.update_validate({self.name: value})
        return super().__set__(instance, value)


class LemonImageObject(object):
    def __init__(self, images) -> None:
        self.images = images


class LemonImageAccessor(FieldAccessor):
    def __init__(self, model, field, name):
        super().__init__(model, field, name)

    def __get__(self, instance, instance_type=None):
        value = []
        if instance:
            value = instance.get_data(self.name)
        return LemonImageObject(value)

    def __set__(self, instance, value):
        if isinstance(value, LemonImageObject):
            value = value.images
        instance.update_validate({self.name: value})
        return super().__set__(instance, value)


class CurrencyField(DoubleField):
    pass


class PhoneField(CharField):
    pass


class JsonField(Field):

    field_type = 'JSON'

    def db_value(self, value):
        if value is None:
            value = dict()
        return ujson.dumps(value)

    def python_value(self, value):
        if value is None:
            return dict()
        return ujson.loads(value)


class EnumField(CharField):
    """
    class Choices(Enum):
        STOP = 0
        START = 1
        PAUSE = 2

    run_status = EnumField(null=Fasle, choices=Choices)
    """
    accessor_class = LemonFieldAccessor

    def __init__(self, choices: Callable, *args: Any, **kwargs: Any) -> None:
        super(CharField, self).__init__(*args, **kwargs)
        self.choices = choices
        self.max_length = 255

    def db_value(self, value: Any):
        try:
            return self.choices(value).value
        except:
            return value

    def python_value(self, value: Any):
        try:
            return self.choices(value).value
        except Exception:
            return value


class DatetimeField(IntegerField):

    pass


class FileField(JsonField):
    pass


class MediumTextField(TextField):
    field_type = 'MEDIUMTEXT'


class LemonAutoField(LemonFieldMixin, AutoField):

    lemon_type = FieldType.INTEGER
    accessor_class = LemonFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.INTEGER})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)


class LemonIntegerField(LemonFieldMixin, IntegerField):

    lemon_type = FieldType.INTEGER
    accessor_class = LemonFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.INTEGER})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)
        # if self.calculate_field:
        #     self.column_name = self.default = self.model = self.name = None
        #     self.primary_key = False

    # def __sql__(self, ctx):
    #     sql = ctx.sql(self.column)
    #     # app_log.info(sql.query())
    #     # app_log.info(sql.subquery)
    #     # app_log.info(sql.scope)
    #     # app_log.info((sql, 2222222222222222222222222))
    #     return sql


class LemonCharField(LemonFieldMixin, CharField):

    lemon_type = FieldType.STRING
    accessor_class = LemonFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.STRING})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)
        # if self.max_length > 255:
        #     self.field_type = "VARCHAR"

    def db_value(self, value: Any):
        if self.column_name == "_status":
            try:
                return str(int(value))
            except:
                return UserTableStatus.status_desc2_code.get(value, None)
        elif self.column_name == "externpk":
            try:
                if value is None:
                    return value
                return ujson.dumps(value)
            except:
                return super().db_value(value)
        return super().db_value(value)

    def python_value(self, value: Any):
        if self.column_name in ("_status", ):
            return UserTableStatus.status_code2_desc.get(value, None)
        elif self.column_name == "externpk":
            try:
                if value is None:
                    return value
                return ujson.loads(value)
            except:
                return super().python_value(value)
        return super().python_value(value)


class LemonTextField(LemonFieldMixin, TextField):
    lemon_type = FieldType.TEXT
    accessor_class = LemonFieldAccessor
    
    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.TEXT})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)


class LemonMediumTextField(LemonFieldMixin, MediumTextField):
    lemon_type = FieldType.MEDIUMTEXT
    accessor_class = LemonFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.MEDIUMTEXT})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)


class LemonDecimalField(LemonFieldMixin, DecimalField):

    lemon_type = FieldType.DECIMAL
    accessor_class = LemonFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.DECIMAL})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)

    def python_value(self, value):
        if value is not None:
            if isinstance(value, decimal.Decimal):
                return value
            return 0

    def load_json_value(self, value):
        return decimal.Decimal(str(value)).quantize(
            decimal.Decimal(10) ** (-self.decimal_places))


class LemonJsonField(LemonFieldMixin, JsonField):
    lemon_type = FieldType.JSON
    accessor_class = LemonFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.JSON})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)

    # def python_value(self, value):
    #     if value is not None:
    #         return ujson.dumps(value)
    #     return value


class LemonDateTimeField(LemonFieldMixin, BigIntegerField):

    lemon_type = FieldType.DATETIME
    accessor_class = LemonFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.DATETIME})
        # kwargs.update({"hierarchy_field": 2})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)

    def db_value(self, value):
        if isinstance(value, datetime.datetime):
            value = time.mktime(value.timetuple())
        return super().db_value(value)


class LemonDateTimeCycleField(LemonFieldMixin, JsonField):

    lemon_type = FieldType.DATETIME_CYCLE

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.DATETIME_CYCLE})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)

    # year = property(lemon_date_part("year"))
    # month = property(lemon_date_part('month'))
    # day = property(lemon_date_part('day'))
    # hour = property(lemon_date_part('hour'))
    # minute = property(lemon_date_part('minute'))
    # second = property(lemon_date_part('second'))

    # def bind(self, model, name, set_attribute=True):
    #     app_log.info(f"bind {model}, {name}, {self.hierarchy_accessor}")

    #     super(IntegerField, self).bind(model, name, set_attribute)
    #     for hierarchy in self.hierarchy_accessor:
    #         name, attr = hierarchy[0], hierarchy[1]
    #         app_log.info((name, attr))
    #         setattr(self, name, attr)
        # self.model = model
        # self.name = self.safe_name = name
        # self.column_name = self.column_name or name
        # if set_attribute:
        #     setattr(model, name, self.accessor_class(model, self, name))

# class LemonVirtualField(LemonFieldMixin, VirtualField):
#     def __init__(self, *args, **kwargs):
#         kwargs.update({"lemon_type": FieldType.DATETIME})
#         super().__init__(kwargs)
#         super(LemonFieldMixin, self).__init__(*args, **kwargs)
#         self.column_name = self.

# class LemonDateTimeField(LemonFieldMixin, ForeignKeyField):
#     def __init__(self, *args, **kwargs):
#         kwargs.update({"lemon_type": FieldType.DATETIME})
#         super().__init__(kwargs)

#         kwargs.update({"model": lemon_sys_day})
#         super(LemonFieldMixin, self).__init__(*args, **kwargs)
#         # lemon_sys_day._meta.set_database(self.ref_model._meta.database)

#     def python_value(self, value):
#         value_month = int(value / 31) + 1
#         value_day = value % 31
#         time_value = datetime.datetime(year=2020, month=value_month, day=value_day)
#         return_value = int(time_value.timestamp())
#         return return_value

#     def db_value(self, value):
#         time_value = datetime.datetime.fromtimestamp(value)
#         value = time_value.day  + (time_value.month - 1) * 31
#         return value


class LemonBooleanField(LemonFieldMixin, BooleanField):

    lemon_type = FieldType.BOOLEAN
    accessor_class = LemonFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.BOOLEAN})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)


class LemonEnumField(LemonFieldMixin, EnumField):

    lemon_type = FieldType.ENUM

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.ENUM})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)


class LemonFileField(LemonFieldMixin, FileField):

    lemon_type = FieldType.FILE
    accessor_class = LemonFileFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.FILE})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)


class LemonImageField(LemonFieldMixin, FileField):

    lemon_type = FieldType.IMAGE
    accessor_class = LemonImageFieldAccessor

    def __init__(self, *args, **kwargs):
        kwargs.update({"lemon_type": FieldType.IMAGE})
        super().__init__(kwargs)
        super(LemonFieldMixin, self).__init__(*args, **kwargs)


class LemonBackrefQuery(LemonModelQueryHelper, ModelSelect):
    def __init__(self, instance, accessor, rel, *args, **kwargs):
        self._instance = instance
        self._accessor = accessor
        self._fields = default_select_fields(rel)
        super(LemonBackrefQuery, self).__init__(rel, self._fields, *args, **kwargs)
        self._model = self.model
        self._is_default_set = set()
        self._default_backrefs = list()
        # query 对象比较特殊？ 只用True 或 False 做标记 每次会被重置
        self._v_is_backref_dirty = set()
        self._v_backrefs = list()
        self._v_backrefs_dict = weakref.WeakValueDictionary()
        self._is_backref_dirty = set()
        self._backrefs = list()
        self._backrefs_dict = weakref.WeakValueDictionary()
        self._remove_backrefs = list()
        self._v_remove_backrefs = list()

    def __len__(self):
        if self._v_is_backref_dirty:
            return len(self._v_backrefs)
        elif self._is_backref_dirty:
            return len(self._backrefs)
        else:
            return super().__len__()

    def __iter__(self):
        if self._v_is_backref_dirty:
            return iter(self._v_backrefs)
        elif self._is_backref_dirty:
            return iter(self._backrefs)
        else:
            return super().__iter__()

    def _add_dirty(self, validate=False, replace=False):
        backref_name = self._accessor.field.backref
        self._instance._s_dirty.add(backref_name)
        self._v_is_backref_dirty.add(1)
        self._instance._v_dirty.add(backref_name)
        self._instance._v_dirty_backref_set.add(backref_name)
        if replace:
            # add 和 extend 只是添加了多端数据，但还要从数据库查询原有的数据
            # replace 替换数据，不再从数据库查询数据了
            self._instance._v_replace_backref_set.add(backref_name)
        if not validate:
            self._is_backref_dirty.add(1)
            self._instance._dirty_backref_set.add(backref_name)

    @property
    def all(self):
        if self._v_is_backref_dirty:
            return self._v_backrefs
        return self._backrefs

    @property
    def remove_all(self):
        if self._v_is_backref_dirty:
            return self._v_remove_backrefs
        return self._remove_backrefs

    @property
    def is_diff(self):
        if self._is_default_set:
            if self._default_backrefs == self.all:
                return False
        elif not self._v_is_backref_dirty:
            return False
        return True

    async def default_all(self):
        self._is_default_set.clear()
        self._default_backrefs.clear()
        dest = self._accessor.field.rel_field.name
        pk = getattr(self._instance, dest)
        if pk and pk > 0:
            loop = asyncio.get_running_loop()
            backref_all = await loop.run_in_executor(CVE, self.execute)
            self._default_backrefs.extend(backref_all)
        self._is_default_set.add(1)
        self._instance._default_backref_set.add(self._accessor.field.backref)
        return self._default_backrefs

    def replace(self, value_list, extend=False, sync=False, validate=False):
        des_attr = self._accessor.field.name
        clear_existing = True if extend else False
        _backrefs, _backrefs_dict, _remove_backrefs = self._get_backrefs_and_backrefs_dict(
            validate)
        if not extend:
            add_db_obj(self, validate=validate)
            # 注意这里的 [::] 复制了一份 backrefs
            # 因为 remove 会移除 backrefs 的原始数据
            [self.remove(value, validate=validate) for value in _backrefs[::]]
        self._pre_replace(_backrefs, _backrefs_dict, _remove_backrefs, extend, validate)
        # 使用列表推导的方式，优化大数据量情况下遍历的效率
        [self._replace_value(
            value, _backrefs, _backrefs_dict, _remove_backrefs,
            des_attr, validate, clear_existing=clear_existing)
            for value in value_list]

    def add(self, value, clear_existing=False, sync=False, validate=False):
        if clear_existing:
            self._backrefs.clear()
            self._backrefs_dict.clear()
        add_db_obj(self, value, validate=validate)
        des_attr = self._accessor.field.name
        if value:
            memory_pk = get_memory_id(value)
            pk = value.get_id() or memory_pk
            _backrefs, _backrefs_dict, _remove_backrefs = self._get_backrefs_and_backrefs_dict(
                validate)
            if value not in _backrefs:
                self._add_value(
                    value, _backrefs, _backrefs_dict, _remove_backrefs, pk, memory_pk,
                    validate=validate)
                if not validate:
                    if des_attr:
                        setattr(value, des_attr, self._instance)
                    if value not in self._v_backrefs:
                        self._add_when_not_validate(value, pk, memory_pk)

    def remove(self, value, sync=False, validate=False, check=True):
        # src_id = getattr(self._instance, "id")
        des_attr = self._accessor.field.name
        pk = value
        if validate:
            _backrefs_dict = self._v_backrefs_dict
            _backrefs = self._v_backrefs
            _remove_backrefs = self._v_remove_backrefs
        else:
            _backrefs_dict = self._backrefs_dict
            _backrefs = self._backrefs
            _remove_backrefs = self._remove_backrefs
        if isinstance(value, peewee.Model):
            pk = value.get_id() or get_memory_id(value)
        else:
            value = _backrefs_dict.get(pk)
        value_des_pk = value.__data__.get(des_attr, None)
        instance_pk = self._instance.get_id()
        set_value_des_attr_none = True
        if check:
            if value_des_pk is None or (
                value_des_pk > 0 and value_des_pk != instance_pk
            ):
                set_value_des_attr_none = False
        memory_pk = get_memory_id(value)
        if value in _backrefs:
            if memory_pk in _backrefs_dict:
                del _backrefs_dict[memory_pk]
            if pk in _backrefs_dict:
                del _backrefs_dict[pk]
            _backrefs.remove(value)
            self._add_dirty(validate=validate)
        if pk > 0 and memory_pk in _backrefs_dict:
            del _backrefs_dict[memory_pk]
        if value not in _remove_backrefs:
            _remove_backrefs.append(value)
        if not validate:
            if set_value_des_attr_none:
                setattr(value, des_attr, None)
            if value in self._v_backrefs:
                if memory_pk in self._v_backrefs_dict:
                    del self._v_backrefs_dict[memory_pk]
                if pk in self._v_backrefs_dict:
                    del self._v_backrefs_dict[pk]
                self._v_backrefs.remove(value)
            if pk > 0 and memory_pk in self._v_backrefs_dict:
                del self._v_backrefs_dict[memory_pk]
            if value not in self._v_remove_backrefs:
                self._v_remove_backrefs.append(value)

    def clear(self, sync=False):
        self._backrefs_dict.clear()
        self._backrefs.clear()
        self._remove_backrefs.clear()
        self._is_backref_dirty.clear()
        self._add_dirty()

    def validate_clear(self):
        self._v_backrefs_dict.clear()
        self._v_backrefs.clear()
        self._v_remove_backrefs.clear()
        self._v_is_backref_dirty.clear()
        # self._add_dirty(validate=True)

    def cancel(self):
        self._v_backrefs_dict.clear()
        self._v_backrefs.clear()
        self._v_is_backref_dirty.clear()
        self._v_remove_backrefs.clear()


class LemonBackrefAccessor(BackrefAccessor):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.rel_model_uuid = self.rel_model._meta.table_name
        self._resource_id = ".".join([self.model._meta.table_name, self.rel_model_uuid])

    def __get__(self, instance, instance_type=None):
        self.model._predicate_accessor_get(self._resource_id)
        if instance is not None:
            dest = self.field.rel_field.name
            app_log.info(f"_select_query_dict: {instance._select_query_dict}")
            select_query_dict = instance._select_query_dict.get(
                self.rel_model_uuid, {})
            select_query = select_query_dict.get(self.field.name)
            if select_query is not None:
                select_query._where = None
            else:
                select_query = LemonBackrefQuery(instance, self, self.rel_model)
                instance._select_query_dict.setdefault(self.rel_model_uuid, {})
                instance._select_query_dict[self.rel_model_uuid].update(
                    {self.field.name: select_query})
                # select_query = self.rel_model.select()

            pk = getattr(instance, dest)
            if pk is None or pk <= 0:
                select_query = select_query.where(0==1)
            else:
                select_query = select_query.where(self.field==getattr(instance, dest))
            # select_query._accessor = self
            # select_query._instance = instance
            # select_query._model = select_query.model
            return select_query
        return self


class LemonForeignKeyAccessor(ForeignKeyAccessor):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._resource_id = self.field.column_name

    def __get__(self, instance, instance_type=None):
        if instance:
            try:
                self.model._predicate_accessor_get(self._resource_id)
            except:
                return PermissionDeploy.NoPermissionFieldShow.get(-1)
            if self.name in instance.__v_dirty__:
                value = instance.get_data(self.name)
                if isinstance(value, int):
                    obj = instance.get_data(self.name, fk=True)
                    if obj:
                        return obj
                if isinstance(value, peewee.Model) or value is None:
                    return value
        return super().__get__(instance, instance_type)

    def __set__(self, instance, obj):
        # try:
        #     self.model._predicate_accessor_set(self._resource_id)
        # except:
        #     app_log.warning(f"{self._resource_id} permission error")
        # else:
        instance.update_validate({self.name: obj}, diff=True)
        # 当首次赋值的外键对象为 内存模型对象 时
        # __data__ 存储的会是 None  __rel__ 存储的会是 内存模型对象
        # 因为 __data__ 存储的是 内存模型对象的 主键值
        # 这种情况下如果第二次赋值外键为 None 会导致 __rel__ 的数据不会被更新为 None
        if obj is None and self.name in instance.__data__:
            instance.__rel__[self.name] = None
        if isinstance(obj, peewee.Model):
            obj._add_instance()
        super().__set__(instance, obj)


class LemonForeignKeyField(ForeignKeyField):
    accessor_class = LemonForeignKeyAccessor

    def bind(self, model, name, set_attribute=True):
        super().bind(model, name, set_attribute=set_attribute)
        if set_attribute:
            setattr(model, self.object_id_name, ObjectIdAccessor(self))
            if self.backref not in '!+':
                setattr(self.rel_model, self.backref, LemonBackrefAccessor(self))

    # def python_value(self, value):
    #     if getattr(self, "_no_permission", False):
    #         return "**"
    #     app_log.debug(repr(value))
    #     app_log.debug(type(value))
    #     if isinstance(value, self.rel_model):
    #         return value
    #     return self.rel_field.python_value(value)


class LemonSystemForeignKeyAccessor(ForeignKeyAccessor):

    def get_rel_instance(self, instance):
        value = instance.__data__.get(self.name)
        try:
            if value is not None or self.name in instance.__rel__:
                if self.name not in instance.__rel__:
                    current_user = LemonContextVar.current_user.get()
                    if not current_user:
                        raise self.rel_model.DoesNotExist
                    tenant_uuid = current_user.tenant_id
                    origin_rel_field = self.field.rel_field
                    rel_model = make_tenant_sys_table_copy(
                        self.rel_model, tenant_uuid)
                    rel_field = getattr(rel_model, origin_rel_field.column_name, None)
                    if not rel_field:
                        rel_field = getattr(rel_model, origin_rel_field.name)
                    obj = rel_model.get(rel_field == value)
                    instance.__rel__[self.name] = obj
                return instance.__rel__[self.name]
            elif not self.field.null:
                raise self.rel_model.DoesNotExist
        except Exception:
            pass
        return value

    def __get__(self, instance, instance_type=None):
        if instance is not None:
            if self.name in instance.__v_dirty__:
                value = instance.get_data(self.name)
                if isinstance(value, int):
                    obj = instance.get_data(self.name, fk=True)
                    if obj:
                        return obj
                if isinstance(value, peewee.Model) or value is None:
                    return value
        try:
            return super().__get__(instance, instance_type)
        except Exception:
            app_log.error(print_exc())
            return None

    def __set__(self, instance, obj):
        # try:
        #     self.model._predicate_accessor_set(self._resource_id)
        # except:
        #     app_log.warning(f"{self._resource_id} permission error")
        # else:
        if isinstance(obj, peewee.Model):
            if obj._meta.meta_table_name in SystemTable.META_NAMES:
                instance.__data__[self.name] = getattr(
                    obj, self.field.rel_field.name)
                instance.__rel__[self.name] = obj
                instance._dirty.add(self.name)
                obj._add_instance()
                instance.update_validate({self.name: obj}, diff=True)
        elif obj is None:
            instance.update_validate({self.name: obj}, diff=True)
            if self.name in instance.__data__:
                instance.__data__[self.name] = None
                instance.__rel__[self.name] = None
                instance._dirty.add(self.name)
        else:
            instance.update_validate({self.name: obj}, diff=True)
            super().__set__(instance, obj)


class LemonSystemForeignKeyField(LemonForeignKeyField):

    accessor_class = LemonSystemForeignKeyAccessor


class LemonManyToManyQuery(LemonModelQueryHelper, ManyToManyQuery):
    def __init__(self, instance, accessor, rel, *args, **kwargs):
        self._instance = instance
        self._accessor = accessor
        self.raccessor = accessor
        self._src_attr = accessor.src_fk.rel_field.name
        self._dest_attr = accessor.dest_fk.rel_field.name
        self._fields = default_select_fields(rel)
        super(ManyToManyQuery, self).__init__(rel, self._fields, *args, **kwargs)
        self._is_default_set = set()
        self._default_backrefs = list()
        self._v_is_backref_dirty = set()
        self._v_backrefs = list()
        self._v_backrefs_dict = weakref.WeakValueDictionary()
        self._is_backref_dirty = set()
        self._backrefs = list()
        self._backrefs_dict = weakref.WeakValueDictionary()
        self._remove_backrefs = list()
        self._v_remove_backrefs = list()

    def __len__(self):
        if self._v_is_backref_dirty:
            return len(self._v_backrefs)
        elif self._is_backref_dirty:
            return len(self._backrefs)
        else:
            return super().__len__()

    def __iter__(self):
        if self._v_is_backref_dirty:
            return iter(self._v_backrefs)
        elif self._is_backref_dirty:
            return iter(self._backrefs)
        else:
            return super().__iter__()

    def _add_dirty(self, validate=False, replace=False):
        field_name = self._accessor.field.name
        self._instance._s_dirty.add(field_name)
        self._v_is_backref_dirty.add(1)
        self._instance._v_dirty.add(field_name)
        self._instance._v_dirty_many_to_many_set.add(field_name)
        if replace:
            # add 和 extend 只是添加了多端数据，但还要从数据库查询原有的数据
            # replace 替换数据，不再从数据库查询数据了
            self._instance._v_replace_backref_set.add(field_name)
        if not validate:
            self._is_backref_dirty.add(1)
            self._instance._dirty_many_to_many_set.add(field_name)

    @property
    def all(self):
        if self._v_is_backref_dirty:
            return self._v_backrefs
        return self._backrefs

    @property
    def remove_all(self):
        if self._v_is_backref_dirty:
            return self._v_remove_backrefs
        return self._remove_backrefs

    @property
    def is_diff(self):
        if self._is_default_set:
            if self._default_backrefs == self.all:
                return False
        elif not self._v_is_backref_dirty:
            return False
        return True

    async def default_all(self):
        self._is_default_set.clear()
        self._default_backrefs.clear()
        loop = asyncio.get_running_loop()
        backref_all = await loop.run_in_executor(CVE, self.execute)
        self._default_backrefs.extend(backref_all)
        self._is_default_set.add(1)
        self._instance._default_backref_set.add(self._accessor.field.name)
        return self._default_backrefs

    def replace(self, value_list, extend=False, sync=False, validate=False):
        clear_existing = True if extend else False
        _backrefs, _backrefs_dict, _remove_backrefs = self._get_backrefs_and_backrefs_dict(
            validate)
        self._pre_replace(_backrefs, _backrefs_dict, _remove_backrefs, extend, validate)
        # 使用列表推导的方式，优化大数据量情况下遍历的效率
        [self._replace_value(
            value, _backrefs, _backrefs_dict, _remove_backrefs,
            validate=validate, clear_existing=clear_existing)
            for value in value_list]

    def extend(self, value_list, sync=False, validate=False):
        _backrefs, _backrefs_dict, _remove_backrefs = self._get_backrefs_and_backrefs_dict(
            validate)
        # 使用列表推导的方式，优化大数据量情况下遍历的效率
        [self._replace_value(
            value, _backrefs, _backrefs_dict, _remove_backrefs,
            validate=validate, clear_existing=True)
            for value in value_list]

    def add(self, value, clear_existing=False, sync=False, validate=False):
        self.model._predicate_accessor_set(self._accessor._resource_id)
        if sync:
            super().add(value, clear_existing=clear_existing)
        else:
            add_db_obj(self, value, validate=validate)
            if value:
                memory_pk = get_memory_id(value)
                pk = value.get_id() or get_memory_id(value)
                _backrefs, _backrefs_dict, _remove_backrefs = self._get_backrefs_and_backrefs_dict(
                    validate)
                if value not in _backrefs:
                    self._add_value(
                        value, _backrefs, _backrefs_dict, _remove_backrefs, pk, memory_pk,
                        validate=validate)
                    if not validate and value not in self._v_backrefs:
                        self._add_when_not_validate(value, pk, memory_pk)

    def remove(self, value, sync=False, validate=False):
        self.model._predicate_accessor_set(self._accessor._resource_id)
        if sync:
            super().remove(value)
        else:
            pk = value
            if validate:
                _backrefs_dict = self._v_backrefs_dict
                _backrefs = self._v_backrefs
                _remove_backrefs = self._v_remove_backrefs
            else:
                _backrefs_dict = self._backrefs_dict
                _backrefs = self._backrefs
                _remove_backrefs = self._remove_backrefs
            if isinstance(value, peewee.Model):
                pk = value.get_id()
                pk = value.get_id() or get_memory_id(value)
            else:
                value = _backrefs_dict.get(pk)
            memory_pk = get_memory_id(value)
            if value in _backrefs:
                if memory_pk in _backrefs_dict:
                    del _backrefs_dict[memory_pk]
                if pk in _backrefs_dict:
                    del _backrefs_dict[pk]
                _backrefs.remove(value)
                # 多对多 看起来没地方用到 _remove_backrefs
                # if value not in _remove_backrefs:
                #     _remove_backrefs.append(value)
                self._add_dirty(validate=validate)
            if not validate:
                if value in self._v_backrefs:
                    if memory_pk in self._v_backrefs_dict:
                        del self._v_backrefs_dict[memory_pk]
                    if pk in self._v_backrefs_dict:
                        del self._v_backrefs_dict[pk]
                    self._v_backrefs.remove(value)
                    # if value not in self._v_remove_backrefs:
                    #     self._v_remove_backrefs.append(value)

    def clear(self, sync=False):
        self.model._predicate_accessor_set(self._accessor._resource_id)
        if sync:
            super().clear()
        else:
            self._backrefs.clear()
            self._backrefs_dict.clear()
            self._remove_backrefs.clear()
            self._is_backref_dirty.clear()
            self._add_dirty()

    def validate_clear(self):
        self._v_backrefs_dict.clear()
        self._v_backrefs.clear()
        self._v_remove_backrefs.clear()
        self._v_is_backref_dirty.clear()
        # self._add_dirty(validate=True)

    def cancel(self):
        self._v_backrefs_dict.clear()
        self._v_backrefs.clear()
        self._v_is_backref_dirty.clear()
        self._v_remove_backrefs.clear()


class LemonManyToManyFieldAccessor(ManyToManyFieldAccessor):

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self._resource_id = ".".join([self.model._meta.table_name, self.rel_model._meta.table_name])

    def __get__(self, instance, instance_type=None, force_query=False):

        if instance is not None:
            # ? 是不是这里检查权限？？？？
            self.model._predicate_accessor_get(self._resource_id)
            if not force_query and self.src_fk.backref != '+':
                backref = getattr(instance, self.src_fk.backref)
                if isinstance(backref, list):
                    return [getattr(obj, self.dest_fk.name) for obj in backref]

            dest_fk_name = self.dest_fk.name
            src_id = getattr(instance, self.src_fk.rel_field.name)
            select_query = instance._select_query_dict.get(dest_fk_name)
            if select_query is not None:
                select_query._where = None
            else:
                select_query = LemonManyToManyQuery(
                    instance, self, self.rel_model).join(
                        self.through_model, on=(self.rel_model.id==self.dest_fk)
                        ).join(self.model, on=(self.model.id==self.src_fk))
                instance._select_query_dict.update({dest_fk_name: select_query})
            select_query = select_query.where(self.src_fk == src_id)
            return select_query

        return self.field

    def __set__(self, instance, value):
        self.model._predicate_accessor_set(self._resource_id)
        return super().__set__(instance, value)


class LemonManyToManyField(ManyToManyField):
    accessor_class = LemonManyToManyFieldAccessor

    def _get_descriptor(self):
        return LemonManyToManyFieldAccessor(self)

    def bind(self, model, name, set_attribute=True):
        super().bind(model, name, set_attribute)

        if not self._is_backref:
            many_to_many_field = LemonManyToManyField(
                self.model,
                backref=name,
                through_model=self.through_model,
                on_delete=self._on_delete,
                on_update=self._on_update,
                _is_backref=True)
            self.rel_model._meta.add_field(self.backref, many_to_many_field)


LEMON_FIELD_DEFINE.LEMON_FOREIGN_KEY_FIELD = LemonForeignKeyField
LEMON_FIELD_DEFINE.LEMON_MANY_TO_MANY_FIELD = LemonManyToManyField
LEMON_FIELD_DEFINE.LEMON_BACKREF_ACCESSOR = LemonBackrefAccessor

if __name__ == "__main__":
    a = IntegerField(default=30)
    print(a.default)
    uid = LemonIntegerField(null=False, lemon_type=11, default=20)
    print(uid.lemon_type, uid.default, uid.field_type)
    b = LemonCharField(**{"default": "b", "max_length": 300})
    print(b.lemon_type, b.default, b.field_type)
