# -*- coding:utf-8 -*-
import hashlib
import traceback
import types
import ujson
import asyncio
from sanic.views import HTTPMethodView
from sanic.headers import parse_content_header
from functools import reduce, wraps, partial
from baseutils.log import app_log
from apps import exceptions as lemon_exceptions
from apps.engine import engine
from apps.utils import get_ext_tenant
from apps.base_utils import get_current_workspace, set_current_workspace
from baseutils.editor_locker import LockError

class EditControllerCheckRoutes:
    route_map = dict()
    route_map_add = dict()

    @classmethod
    def add_route(cls, bp, uri, resource_name, detail_name=""):
        route = bp.url_prefix + uri
        cls.route_map[route] = resource_name
        if resource_name != "app_uuid":
            detail_name = ""
        cls.route_map_add[route] = detail_name


# check_type = 0: save perimssion check
# check_type = 1: version control args check
def save_check_decorator(func, check_type=0):
    @wraps(func)
    async def wrap_func(request, *args, **kwargs):
        app_log.info(f"{check_type = }")
        request_path = request.path
        app_log.info(request_path)
        esid = request.headers.get("esid")
        route_map = EditControllerCheckRoutes.route_map
        route_map_add = EditControllerCheckRoutes.route_map_add
        request.ctx.condition = condition = asyncio.Condition()
        if request.method.lower() == "post":
            content_type = request.headers.get("Content-Type")
            content_type, params = parse_content_header(content_type)
            if content_type == "application/json":
                request_body = getattr(request, "json", dict()) or dict()
            elif content_type == "multipart/form-data":
                request_body = getattr(request, "form", dict()) or dict()
            else:
                request_body = dict()
        else:
            request_body = dict()

        user_uuid = request.ctx.session.get("user_uuid")
        if check_type == 1:
            set_current_workspace(user_uuid, request.ctx.session.sid)
        current_workspace = get_current_workspace()
        branch_uuid = request_body.get("branch_uuid")
        app_uuid = request_body.get("app_uuid")
        module_uuid = request_body.get("module_uuid")
        document_uuid = request_body.get("document_uuid")
        to_update_obj = None
        version_control = False
        async with engine.db.objs.atomic():
            if check_type == 1: # version control 
                if not app_uuid:
                    raise lemon_exceptions.EditPermissionPolicy("arg app_uuid not found", status_code=708)
                
                # if app_uuid is None:
                #     if module_uuid is not None:
                #         query_func = partial(engine.access.get_module_by_module_uuid, module_uuid)
                #     elif document_uuid is not None:
                #         query_func = partial(engine.access.get_document_by_document_uuid, document_uuid)
                #     else:
                #         raise lemon_exceptions.EditPermissionPolicy("arg app_uuid not found", status_code=708)
                #     if branch_uuid:
                #         to_update_obj = await query_func()
                #     else:
                #         async with engine.access.with_workspace(None):
                #             to_update_obj = await query_func()
                #     if to_update_obj is None:
                #         raise lemon_exceptions.EditPermissionPolicy("resource id error", status_code=708)
                #     app_uuid = to_update_obj.app_uuid
                # # 获取到的appuuid写道current_workspace
                
                async with engine.access.with_workspace(None):
                    branchs = await engine.access.list_app_branch(app_uuid)  # 是否开启版本控制
                if branchs: 
                    if not branch_uuid:
                        raise lemon_exceptions.EditPermissionPolicy("arg branch_uuid not found", status_code=708)
                    if not sum([branch["branch_uuid"] == branch_uuid for branch in branchs]):
                        raise lemon_exceptions.EditPermissionPolicy("invalid branch uuid", status_code=708)
                    version_control = True
                user_uuid = current_workspace.user_uuid
                if version_control:
                    current_workspace.app_uuid = app_uuid
                    current_workspace.branch_uuid = branch_uuid
                    request.ctx.version_control = version_control
                else:
                    set_current_workspace(None)
            
            if request_path in route_map:
                if not esid:
                    raise lemon_exceptions.EditPermissionPolicy("esid not found", status_code=707)
                to_update_obj_key = route_map[request_path]
                if to_update_obj_key in request_body:
                    to_update_obj_uuid = request_body[to_update_obj_key]
                    resource_id = getattr(to_update_obj, to_update_obj_key, None)
                    if resource_id is None:
                        if to_update_obj_key == "module_uuid":
                            to_update_obj = await engine.access.get_module_by_module_uuid(to_update_obj_uuid)
                        elif to_update_obj_key == "document_uuid":
                            to_update_obj = await engine.access.get_document_by_document_uuid(to_update_obj_uuid)
                        else:
                            meta = {
                                "app_uuid": app_uuid,
                                "resource_id_add": route_map_add.get(request_path, "")
                            }
                            to_update_obj = types.new_class("to_update_obj", (), {}, lambda ns: ns.update(meta))
                        if not to_update_obj:
                            raise lemon_exceptions.EditPermissionPolicy("resource not found", status_code=708)
                        resource_id = getattr(to_update_obj, to_update_obj_key, None)
                        if resource_id is None:
                            raise lemon_exceptions.EditPermissionPolicy("Policy not found or denied")
                    resource_id_add = getattr(to_update_obj, "resource_id_add", "")
                    resource_id += resource_id_add
                    if check_type == 1 and version_control:
                        resource_id = resource_id + branch_uuid + user_uuid
                        # if "check_document_content.json" not in request_path:
                        # pre check action     has_unmerged
                    app_log.info(f"{resource_id=}, {app_uuid=}, {user_uuid=}, {branch_uuid=}")
                    origin_resource_id = resource_id

                    if app_uuid:
                        # 版本控制正在初始化，不允许保存
                        app_lock_id = hashlib.md5(
                                ("create_project_" + app_uuid).encode()).hexdigest()
                        lock = await engine.editor_lock_manager.get_lock(app_lock_id)
                        if lock:
                            raise lemon_exceptions.EditPermissionPolicy("Policy denied", status_code=702, resource_id=origin_resource_id)
                    resource_id = hashlib.md5(("editor_locker_" + resource_id).encode()).hexdigest()
                    
                    lock = await engine.editor_lock_manager.get_lock(resource_id)
                    # app_log.info(lock)
                    if lock is None:
                        try:
                            await engine.editor_lock_manager.lock(resource_id, lock_timeout=10*60, lock_identifier=esid)
                        except LockError:
                            app_log.error(traceback.format_exc())
                    else:
                        if lock.id.decode() != esid:
                            raise lemon_exceptions.EditPermissionPolicy("Policy not found or denied", resource_id=origin_resource_id)
                        else:
                            await engine.editor_lock_manager.extend(lock, lock_timeout=10*60)

        if check_type == 1 and version_control:
            response = await func( request, *args, **kwargs) 
            result_code = ujson.loads(response.body).get("code")
            if result_code == 200:
                git_actions = getattr(request.ctx, "GIT_ACTIONS", list())
                git_action_type = getattr(request.ctx, "GIT_ACTION_TYPE", 0)
                for action in git_actions:
                    if git_action_type == 1:
                        await action()
                    else:
                    # if asyncio.iscoroutinefunction(action):
                        asyncio.create_task(action())
        else:
            async with engine.access.with_workspace(None):
                response = await func( request, *args, **kwargs) 
        async with condition:
            condition.notify_all()
        return response
    return wrap_func

class LemonHTTPMethodView(HTTPMethodView):

    decorators = [save_check_decorator]

    def dispatch_request(self, request, *args, **kwargs):
        handler = getattr(self, request.method.lower(), None)
        return handler(request, *args, **kwargs)

class LemonVCHTTPMethodView(HTTPMethodView):

    decorators = [partial(save_check_decorator, check_type=1)]

    @classmethod
    def set_git_action_type(cls, request, action_type=0):
        request.ctx.GIT_ACTION_TYPE = action_type

    @classmethod
    def add_git_action(cls, request, func):
        actions = getattr(request.ctx, "GIT_ACTIONS", None)
        if not actions:
            request.ctx.GIT_ACTIONS = []
        request.ctx.GIT_ACTIONS.append(func)

    def dispatch_request(self, request, *args, **kwargs):
        handler = getattr(self, request.method.lower(), None)
        # git_actions = getattr(self, "GIT_ACTIONS", None)
        # request.ctx.git_actions = git_actions
        result = handler(request, *args, **kwargs)
        return result
