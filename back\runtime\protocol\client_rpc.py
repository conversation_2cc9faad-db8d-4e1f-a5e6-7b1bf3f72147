import uuid
from enum import Enum
from typing import Literal, Dict, Any, Optional, TypeVar, Union, List

from pydantic import BaseModel, Field


class ClientRPCCommand(str, Enum):
    ROUTE_HISTORIES = "router/get_histories"
    PUSH_HISTORY = "router/push_history"
    POP_HISTORY = "router/pop_history"
    OPEN_EXTERNAL_LINK = "router/open_external_link"
    GET_PRINTER_LIST = "devices/get_printer_list"
    LOGIN = "auth/login"
    LOGOUT = "auth/logout"
    SET_LOCALSTORAGE = "localstorage/set_item"
    GET_LOCALSTORAGE = "localstorage/get_item"
    GET_UID = "devices/get_uid"
    GET_MAC_ADDRESS_LIST = "devices/get_mac_address_list"


class ClientRPCStatus(int, Enum):
    OK = 0


class ClientRPCRequest(BaseModel):
    """
    服务端的请求结构
    """
    rid: str = Field(default_factory=lambda: uuid.uuid4().hex)
    command: ClientRPCCommand
    payload: Union[BaseModel, Dict]


class ClientRPCResponse(BaseModel):
    command: Literal['response'] = "response"
    rid: str
    code: ClientRPCStatus
    message: str
    data: Dict[str, Any]


class RouterContext(BaseModel):
    pk: Optional[int]
    current_page: Optional[str]


class PageRouterHistory(BaseModel):
    url: str
    page_uuid: str
    page_name: str = ""
    builtin: bool = False
    context: Optional[RouterContext]
    query: Dict[str, Any]


class RouterHistoriesResponse(BaseModel):
    histories: List[PageRouterHistory]


class PopRouterHistoryRequest(BaseModel):
    n: int


class PushRouterHistoryRequest(BaseModel):
    page_uuid: str
    parent_machine_id: Optional[str]
    page_title: Optional[str]
    query: Dict[str, Any]
    context: Optional[RouterContext]
    ignore_parent_machine_id: Optional[bool]
    with_tab: Optional[bool]
    intervalInfo: Dict[str, Any]


class OpenExternalLinkRequest(BaseModel):
    url: str
    target: Literal['_self', '_blank']


class PrinterInfo(BaseModel):
    name: str
    port_name: str = Field(alias="portName")
    driver_name: str = Field(alias="driverName")
    print_processor: str = Field(alias="printProcessor")
    data_type: str = Field(alias="datatype")
    status: List[str]
    status_number: int = Field(alias="statusNumber")
    attributes: List[str]
    priority: int
    default_priority: int = Field(alias="defaultPriority")
    average_ppm: int = Field(alias="averagePPM")


class GetPrinterListResponse(BaseModel):
    printers: List[PrinterInfo]


class GetUIDResponse(BaseModel):
    uid: str


class MacAddressInfo(BaseModel):
    name: str
    address: str
    status: Literal["up", "disconnected", "disabled"]


class GetMACAddressListResponse(BaseModel):
    mac_addresses: List[MacAddressInfo]


class LoginType(int, Enum):
    PASSWORD = 0
    VERIFY_CODE = 1
    WECHAT = 2
    APPLETS = 3
    JOBNUMBER = 4
    ACCESSKEY = 5
    THIRD_PARTY = 6


class LoginResult(BaseModel):
    access_token: str
    user_name: str
    user_uuid: str
    auto_login: bool = True
    login_type: LoginType
    redirect_path: str = None
    query: Dict[str, Any] = None
    extra_res: Dict[str, Any] = None


class SetItemRequest(BaseModel):
    key: str
    value: str


class GetItemRequest(BaseModel):
    key: str


class GetItemResponse(BaseModel):
    value: Optional[str]
