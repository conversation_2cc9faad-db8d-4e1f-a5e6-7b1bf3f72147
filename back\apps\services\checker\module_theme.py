from apps.entity import ImageTable
from apps.services import DocumentCheckerService
from apps.services.checker import checker
from apps.services.checker.image import ImageDocumentCheckerService


class ModuleThemeDocumentCheckerService(DocumentCheckerService):

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str,
            element: dict, document_version: int, app_image_list: list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, None, *args, **kwargs)
        self.document_version = document_version
        self.app_image_list = app_image_list
        self.image_list = self.element.get("image_list", list())

    def check_theme_image_list(self):
        self.model = ImageTable
        document_checker_service = ImageDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.element, document_version=self.document_version, document_number=self.document_number,
            app_image_list=self.app_image_list, is_copy=self.is_copy, ignore_name=True
        )
        document_checker_service.check_image_list()
        self.document_update_list.extend(document_checker_service.document_update_list)
        self.document_delete_list.extend(document_checker_service.document_delete_list)
        self.document_insert_list.extend(document_checker_service.document_insert_list)

    @checker.run
    def check_theme(self):
        self.check_theme_image_list()
