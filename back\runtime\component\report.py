# -*- coding: utf-8 -*-

import os
import re
import json
import weakref
from typing import Any
from functools import partial

import openpyxl
import datetime
from openpyxl.cell import Cell
from openpyxl.styles import Side, Border
from yattag import Doc
import xlsxwriter
from decimal import Decimal

from apps.ide_const import FieldType
from baseutils.log import app_log
from runtime.engine import engine
from runtime.api.helper import RuntimeOssHelper
from runtime.core.utils import (
    lemon_model, build_column_info, build_agg_query_by_relation, lemon_field)
from runtime.core.func import LemonSMValueEditor
from runtime.core.data_preprocess import AsyncDataPreprocess
from baseutils.utils import lemon_uuid
from runtime.core.func_run import FuncRun


class HtmlStyleHelper():

    def __init__(self) -> None:
        pass

    def get_cell_style(self, cell: Cell):
        style_dict = {}
        border = cell.border
        border_syle = self.get_border_style(border)
        style_dict.update(border_syle)
        style_dict.update(self.handle_alignment(cell))
        style_dict.update(self.handel_font_style(cell.font))
        all_style = ''
        for key, value in style_dict.items():
            all_style += f' {key}:{value};'
        return all_style

    def handel_font_style(self, font):
        style_dict = {}
        if font:
            if font.bold:
                style_dict['font-weight'] = 'bold'
            if font.italic:
                style_dict['font-style'] = 'italic'
            if font.underline:
                style_dict['text-decoration'] = 'underline'
            if font.size:
                style_dict['font-size'] = f'{font.size}pt'
            if font.color:
                font_color = font.color
                if font_color.type == 'rgb':
                    color = font_color.rgb[2:]
                else:
                    color = '000000'
                style_dict['color'] = f'#{color}'
        return style_dict

    def handle_alignment(self, cell: Cell):
        style_dict = {}
        alignment = cell.alignment
        if alignment:
            if alignment.horizontal:
                horizontal = alignment.horizontal

                css_horizontal = horizontal
            else:
                css_horizontal = 'right'
            style_dict['text-align'] = css_horizontal
            if alignment.vertical:
                vertical = alignment.vertical
                if vertical == 'center':
                    css_vertical = 'middle'
                else:
                    css_vertical = vertical
            else:
                css_vertical = 'bottom'
            style_dict['vertical-align'] = css_vertical
        return style_dict

    def get_border_style(self, border: Border):
        if not border:
            return {}
        left = self.handle_side_style(border.left)
        right = self.handle_side_style(border.right)
        top = self.handle_side_style(border.top)
        bottom = self.handle_side_style(border.bottom)
        border_style = {'border-left': left, 'border-right': right,
                        'border-top': top, 'border-bottom': bottom}
        return border_style

    def handle_side_style(self, side: Side):
        style = side.style
        if style:
            if style == 'thin':
                border = '1px'
            elif style == 'medium':
                border = '2px'
            elif style == 'thick':
                border = '3px'
            else:
                border = '1px'

            side_color = side.color
            if side_color.type == 'rgb':
                color = side_color.rgb
                result = f'{border} solid #{color[2:]}'
            else:
                color = '#000000'
                result = f'{border} solid {color}'
            result = f'{border} solid #{color[2:]}'
        else:
            result = 'none'
        return result


class ExcelStyleHelper():

    def __init__(self) -> None:
        pass

    def get_cell_style(self, cell: Cell):
        style_dict = {}
        border_syle = self.get_border_style(cell.border)
        style_dict.update(border_syle)
        style_dict.update(self.handle_alignment(cell))
        style_dict.update(self.handel_font_style(cell.font))
        return style_dict

    def handle_alignment(self, cell: Cell):
        style_dict = {}
        alignment = cell.alignment
        if alignment:
            if alignment.horizontal:
                horizontal = alignment.horizontal

                css_horizontal = horizontal
            else:
                css_horizontal = 'right'

            style_dict['align'] = css_horizontal
            if alignment.vertical:
                vertical = alignment.vertical
                if vertical == 'center':
                    css_vertical = 'vcenter'
                else:
                    css_vertical = vertical
            else:
                css_vertical = 'bottom'
            style_dict['valign'] = css_vertical
        return style_dict

    def get_border_style(self, border: Border):
        style_dict = dict()
        if not border:
            return style_dict
        for name in ['left', 'right', 'top', 'bottom']:
            side = getattr(border, name)
            style_dict.update(self.handle_side_style(side, name))
        return style_dict

    def handle_color(self, side_color):
        if side_color.type == 'rgb':
            color = side_color.rgb
        else:
            color = '#000000'
        return color

    def handle_side_style(self, side: Side, name):
        style = side.style
        style_dict = {}
        if style:
            if style == 'thin':
                border = 1
            elif style == 'medium':
                border = 2
            elif style == 'thick':
                border = 3
            else:
                border = 1
            style_dict[name] = border
            color = self.handle_color(side.color)
            style_dict[f'{name}_color'] = color
        return style_dict

    def handel_font_style(self, font):
        style_dict = {}
        if font:
            if font.bold:
                style_dict['bold'] = True
            if font.italic:
                style_dict['italic'] = True
            # if font.underline:
            #     style_dict['text-decoration'] = 'underline'
            if font.size:
                style_dict['font_size'] = f'{font.size}'
            if font.color:
                font_color = font.color
                if font_color.type == 'rgb':
                    color = font_color.rgb[2:]
                else:
                    color = '000000'
                style_dict['font_color'] = f'#{color}'
        return style_dict


html_style_helper = HtmlStyleHelper()
excel_style_helper = ExcelStyleHelper()


class Report:

    oss_helper = RuntimeOssHelper()
    support_action = ['group', 'select', 'header', 'content', 'dep']

    def __init__(self, config_info: dict, lsm) -> None:
        self.dataset_dict = {}
        self.dataset_list = []
        self.namespace_list = []
        self.config_info = config_info
        self.cell_dict = {}
        self.max_row = 0
        self.max_col = 0
        self.ignore_dict = {}
        self.sheet = None
        self._lsm = None
        self.lsm = lsm
        self.variables = {}
        self.root_path = engine.app.config.EXPORT_TEMPLATE_PATH
        self.template_info = config_info.get('template_list')[0] if config_info.get(
            'template_list') else {}
        self.suffix = 'xlsx'
        self.tmp_path = "/tmp"
        self.file_uuid = lemon_uuid()
        self.export_type = 'html'
        self.row_height_dict = {}
        self.column_width_dict = {}
        self.column_ingrease_dict = {}
        self.last_dataset = None
        self.first_dataset = None
        self.condion_cells = []
        self.init_func = config_info.get('init_func')
        self.func_report_info = {}

    @property
    def lsm(self):
        return None if self._lsm is None else self._lsm()

    @lsm.setter
    def lsm(self, value):
        self._lsm = None if value is None else weakref.ref(value)

    @property
    def template_path(self):
        return "/".join([self.root_path, f"{self.template_uuid}.{self.suffix}"])

    @property
    def template_url(self):
        return self.template_info.get("template_url")

    @property
    def template_uuid(self):
        return self.template_info.get("uuid")

    def set_export_type(self, export_type):
        self.export_type = export_type

    @property
    def style_helper(self):
        if self.export_type == 'excel':
            return excel_style_helper
        else:
            return html_style_helper

    def set_row_height(self, row, height):
        self.row_height_dict[row] = height

    def ingrease_column_row(self, column, add=0):
        self.column_ingrease_dict[column] = self.column_ingrease_dict.get(
            column, 0) + add

    def get_column_row(self, start, column):
        return self.column_ingrease_dict.get(column, 0) + start

    async def get_template_file(self):
        if not os.path.exists(self.root_path):
            os.mkdir(self.root_path)
        if not os.path.exists(self.template_path):
            await self.oss_helper.get_oss_object(
                url=self.template_url, to_file=self.template_path)

    def get_excel_column_name(self, n):
        string = ""
        while n > 0:
            n, remainder = divmod(n - 1, 26)
            string = chr(65 + remainder) + string
        return string

    def get_column_width(self, sheet, column):
        if self.init_func:
            col_width = self.column_width_dict.get(column)
        else:
            col_width = sheet.column_dimensions[column].width
        col_width = col_width or 8.43
        return col_width

    def row_height(self, sheet, row):
        if self.init_func:
            row_height = self.row_height_dict.get(row)
        else:
            row_height = sheet.row_dimensions[row].height
        row_height = row_height or 13.5
        return row_height

    def convert_width(self, width):
        return width / 93.86 * 20

    def convert_height(self, height):
        return height / 283.45 * 10

    def update_dataset_dict(self, ):
        cell = self.sheet.cell(row=1, column=1)
        comment = cell.comment
        dataset_sets = set()
        if comment.text:
            datasets = re.findall(r"datasets=(\{.*\})", comment.text)
            dataset = datasets[0]
            print(dataset)
            set_dict = json.loads(dataset)
            for name, config_dict in set_dict.items():
                value = config_dict.get('range')
                row_list = config_dict.get('rowList', [])
                for row_info in row_list:
                    row_info["actions"] = []
                    row_info["action_before"] = []
                    row_info["action_after"] = []
                    row_info["row_add"] = row_info.get(
                        'end') - row_info.get('start') + 1
                row_list.sort(key=lambda x: x.get('start'))
                config_dict['rowList'] = row_list
                start_cell, end_cell = value.split(':')
                dataset: BaseDataSet = self.dataset_dict.get(name)
                start_row, start_col = self.excel_to_row_col(
                    start_cell)
                end_row, end_col = self.excel_to_row_col(end_cell)
                dataset.start_row = start_row
                dataset.start_col = start_col
                dataset.range = {
                    'start_row': start_row, 'start_col': start_col, 'end_row': end_row, 'end_col': end_col}
                dataset.start_cell = start_cell
                dataset.end_cell = end_cell
                dataset.config_info = config_dict
                dataset_sets.add(dataset)
        self.dataset_list = sorted(dataset_sets, key=lambda x: x.start_row)

    def collect_conditional_formatting(self):
        conditional_formats = self.sheet.conditional_formatting
        for rule in conditional_formats:
            cells = rule.cells
            for cell in cells:
                rule_list = []
                cell_dict = {"start_row": cell.min_row, "end_row": cell.max_row, "start_col": cell.min_col,
                             "end_col": cell.max_col, "rule_list": rule_list}
                for cf_rule in conditional_formats[rule]:
                    if cf_rule.type == 'cellIs':
                        formula = cf_rule.formula
                        expression = None
                        if cf_rule.operator == 'lessThan':
                            expression = f'value < {formula[0]}'
                        elif cf_rule.operator == 'greaterThan':
                            expression = f'value > {formula[0]}'
                        elif cf_rule.operator == 'equal':
                            expression = f'value == {formula[0]}'
                        elif cf_rule.operator == 'between':
                            expression = f'{formula[0]} < value < {formula[1]}'
                        if expression:
                            dxf = cf_rule.dxf
                            border_style = self.style_helper.get_border_style(
                                dxf.border)
                            rule_list.append(
                                {"type": "cellIs", "expression": expression, "style": border_style})
                        # cell_dict.update({"type": "cellIs",})
                        # rule_list.append(
                        #     {"type": "cellIs", "operator": cf_rule.operator, "formula": cf_rule.formula, "style": cf_rule.style})
                if rule_list:
                    self.condion_cells.append(cell_dict)

    def excel_to_row_col(self, cell_address):
        col_str = ''
        row_str = ''
        for char in cell_address:
            if char.isalpha():
                col_str += char
            else:
                row_str += char
        col = 0
        for i, char in enumerate(reversed(col_str)):
            col += (ord(char.upper()) - 64) * (26 ** i)
        row = int(row_str)
        return row, col

    def get_dataset(self, cell: Cell):
        for dataset in self.dataset_list:
            range_info = dataset.range
            start_row = range_info.get('start_row')
            end_row = range_info.get('end_row')
            start_col = range_info.get('start_col')
            end_col = range_info.get('end_col')
            if start_row <= cell.row <= end_row and start_col <= cell.column <= end_col:
                self.last_dataset = dataset
                return dataset
        if self.last_dataset:
            return self.last_dataset
        else:
            # 如果没有找到数据集，取最近的数据集
            dataset_list = sorted(
                self.dataset_list, key=lambda x: abs(x.start_row - cell.row))
            return dataset_list[0]

    def get_condition_list(self, cell):
        condition_list = []
        for condition in self.condion_cells:
            start_row = condition.get('start_row')
            end_row = condition.get('end_row')
            start_col = condition.get('start_col')
            end_col = condition.get('end_col')
            if start_row <= cell.row <= end_row and start_col <= cell.column <= end_col:
                condition_list.append(condition)
        return condition_list

    async def prepare(self):
        if self.init_func:
            return None
        await self.get_template_file()
        open_excel = openpyxl.load_workbook(self.template_path, data_only=True)
        # open_excel = openpyxl.load_workbook(
        #     '/home/<USER>/runtime_apps/test/test2/report.xlsx', data_only=True)
        sheet = open_excel.active
        # sheet = open_excel['Sheet2']
        self.sheet = sheet
        min_col = 1
        min_row = 1
        max_col = sheet.max_column
        max_row = sheet.max_row
        merge_cells = sheet.merged_cells
        merge_dict = {}
        datasets = self.config_info.get('datasets', [])
        for dataset in datasets:
            name = dataset.get('name')
            data_source = dataset.get('data_source')
            datasource_type = data_source.get('type')
            if datasource_type == 6:
                self.dataset_dict[name] = BaseDataSet(
                    dataset, merge_dict, sheet, report_obj=self)
            else:
                self.dataset_dict[name] = ModelDataSet(
                    dataset, merge_dict, sheet, report_obj=self)

        self.update_dataset_dict()
        self.collect_conditional_formatting()
        for merge_cell in merge_cells:
            start_col, start_row, end_col, end_row = merge_cell.bounds
            start_cell, end_cell = merge_cell.coord.split(':')
            width = 0
            for row in range(start_row, end_row + 1):
                for column in range(start_col, end_col + 1):
                    self.ignore_dict[f'{row}:{column}'] = True
                    column_name = self.get_excel_column_name(column)
                    width += self.get_column_width(sheet, column_name)
            merge_dict[start_cell] = {
                'start_col': start_col, 'start_row': start_row,
                'rowspan': end_row - start_row, 'colspan': end_col - start_col,
                'width': width
            }
            self.ignore_dict.pop(f'{start_row}:{start_col}')
        for row in range(min_row, max_row + 1):
            for column in range(min_col, max_col + 1):
                cell = sheet.cell(row=row, column=column)
                dataset: BaseDataSet = self.get_dataset(cell)
                cell_value = cell.value
                if self.ignore_dict.get(f'{row}:{column}'):
                    continue
                # if dataset:
                action_obj = None
                if cell_value:
                    cell_value = str(cell_value)
                    for action in self.support_action:
                        if cell_value.startswith(action):
                            action_obj: Action = eval(
                                cell_value, dataset.global_dict)
                            break
                else:
                    cell_value = ''
                if not action_obj:
                    action_obj = dataset.content(cell_value)
                action_obj.set_cell(cell)
                action_obj.start()
                dataset.choose_row_config(action_obj)
        for dataset in self.dataset_list:
            dataset.calculate_place()

    async def export(self):
        if self.init_func:
            stdout, result = await FuncRun.run_func(
                self.init_func, need_stdout=True, lsm=self.lsm)
            self.func_report_info = result
            self.row_height_dict = result.get('row_height_dict', {})
            self.column_width_dict = result.get("column_width_dict", {})
            self.max_row = result.get('max_row', 0)
            self.max_col = result.get('max_col', 0)
            self.cell_dict = result.get('cell_dict', {})
            self.template_info = {'name': self.config_info.get("name")}
        else:
            for namespace in self.dataset_list:
                await namespace.export()
        if self.export_type == 'excel':
            url = await self.export_with_excel(self.sheet)
            return {'success': True, 'message': '导出成功', 'data': {'url': url, 'type': 'excel'}}
        else:
            html_table = await self.export_with_html(self.sheet)
            return {'success': True, 'message': '导出成功', 'data': {'html': html_table, 'type': 'html'}}

    def add_cell(self, start_row, start_col, cell_info):
        self.max_row = max(self.max_row, start_row +
                           cell_info.get('rowspan', 0))
        self.max_col = max(self.max_col, start_col +
                           cell_info.get('colspan', 0))
        self.cell_dict[f'{start_row}:{start_col}'] = cell_info

    def get_cell(self, start_row, start_col):
        return self.cell_dict.get(f'{start_row}:{start_col}')

    def append_cell_style(self, style_str, key, value):
        return f'{style_str}; {key }:{value}'

    async def export_with_html(self, sheet):
        doc, tag, text = Doc().tagtext()
        with tag('table', border="1", style="border-collapse: collapse;"):
            for row in range(1, self.max_row + 1):
                height = self.row_height(sheet, row)
                with tag('tr', style=f"height: {self.convert_height(height)}cm;"):
                    for column in range(1, self.max_col + 1):
                        cell_info = self.cell_dict.get(f'{row}:{column}')
                        if cell_info:
                            col_width = cell_info.get('width') or 8.43

                            rowspan = cell_info.get('rowspan', 0) + 1
                            colspan = cell_info.get('colspan', 0) + 1
                            cell_style = self.append_cell_style(
                                cell_info.get('style'), 'width', f'{self.convert_width(col_width)}cm')
                            with tag('td', style=cell_style, rowspan=rowspan,   colspan=colspan):
                                text(cell_info.get('value', ''))
        html_table = doc.getvalue()
        return html_table

    async def export_with_excel(self, sheet):
        write_func = partial(self.excel_save, sheet)
        await engine.app.loop.run_in_executor(None, write_func)
        excel_file_path = f"{self.tmp_path}/report/{self.file_uuid}.{self.suffix}"
        file_name = self.template_info.get('name')
        url = self.oss_helper.gen_oss_tmp_path(
            middle_user_id=engine.config.MIDDLE_USER_UUID,
            app_uuid=self.lsm.lemon.system.current_user.app_uuid,
            tenant_uuid=self.lsm.lemon.system.current_user.tenant_id,
            page_uuid="template_export",
            control_uuid=lemon_uuid()) + "/" + f'{file_name}.{self.suffix}'
        await self.oss_helper.put_oss_object(url, excel_file_path, local_file=True)
        return url

    def excel_save(self, sheet):
        if not os.path.exists(f"{self.tmp_path}/report"):
            os.mkdir(f"{self.tmp_path}/report")
        excel_file_path = f"{self.tmp_path}/report/{self.file_uuid}.{self.suffix}"
        write_excel = xlsxwriter.Workbook(excel_file_path, {
            "default_format_properties": {
                "font_name": "等线",
                "valign": "vcenter"
            }
        })
        sheet1 = write_excel.add_worksheet("Sheet1")
        for row in range(1, self.max_row + 1):
            height = self.row_height(sheet, row)
            sheet1.set_row(row - 1, height)
            for column in range(1, self.max_col + 1):
                cell_info = self.cell_dict.get(f'{row}:{column}')
                if cell_info:
                    col_width = cell_info.get('width')
                    sheet1.set_column(column - 1, column - 1, col_width)
                    rowspan = cell_info.get('rowspan', 0)
                    colspan = cell_info.get('colspan', 0)
                    style = cell_info.get(
                        'excle_style') or cell_info.get('style')
                    if style:
                        cell_format = write_excel.add_format(
                            style)
                    else:
                        cell_format = None
                    if rowspan or colspan:
                        sheet1.merge_range(
                            row - 1, column - 1, row + rowspan - 1, column +
                            colspan - 1, cell_info.get('value', ''),
                            cell_format)
                    else:
                        sheet1.write(row - 1, column - 1,
                                     cell_info.get('value', ''), cell_format)
        write_excel.close()

    def set_variables(self, variables: dict):
        self.variables.clear()
        self.variables.update(variables)
        self.lsm.lemon.system.report_variables.set_system_variable(variables)
        app_log.info(f'设置报表变量: {variables}')


class BaseDataSet():

    def __init__(self, dataset: dict, merge_dict, sheet, report_obj: Report) -> None:
        self.dataset = dataset
        self.name = self.dataset.get('name')
        self.display_fields = self.dataset.get('display_fields')
        self.lemon_editor: LemonSMValueEditor = LemonSMValueEditor(self)
        self.config_info = dict()
        self.data_query = None
        self.lsm = None
        self.data_model = None
        self.columns = []
        self.field_dict = {}
        self.cell_dict = {}
        self.merge_dict = merge_dict
        self.sheet = sheet
        self.action_obj_list = []
        self.row_before_action = []
        self.row_after_action = []
        self.row_action_obj = []
        self.all_action_obj = []
        self.dep_action_obj = []
        # 定义命名空间的起始位置
        self.start_row = -1
        self.start_col = -1
        # 判断是否和其它命名空间并列
        self.end_row = -1
        self.end_col = -1
        self.row_add = 0
        self.column_add = 0
        self.report_obj = report_obj
        self.lsm = report_obj.lsm
        self.place_dict = {}
        self.group_action_list = []
        self.alias_columns = []
        self.relation_column_dict = {}
        self.hierarchy_column_dict = {}
        self.start_cell = 'A1'
        self.end_cell = 'A1'
        self.range = {}
        self.init_func = dataset.get('init_func')
        self.global_dict = {'group': self.group, 'select': self.select, 'header': self.header,
                            'content': self.content, 'dep': self.dep}
        self.init_status()
        self.prepare_field_dict()

    def add_action(self, action):
        self.action_obj_list.append(action)
        self.all_action_obj.append(action)

    # def add_row_action(self, action):
    #     self.row_action_obj.append(action)
    #     self.all_action_obj.append(action)

    async def process_init_func(self):
        if self.init_func:
            stdout, result = await FuncRun.run_func(
                self.init_func, need_stdout=True, lsm=self.lsm)

    def ingrease_column_row(self, column, add=0):
        return self.report_obj.ingrease_column_row(column, add)

    def get_column_row(self, start, column):
        return self.report_obj.get_column_row(start, column)

    def choose_row_config(self, action):
        cell = action.cell
        row = cell.row
        row_list = self.config_info.get('rowList', [])
        for index, row_info in enumerate(row_list):
            if row_info.get('start') <= row:
                if row <= row_info.get('end'):
                    row_info['actions'].append(action)
                    self.all_action_obj.append(action)
                    return True
                else:
                    if index + 1 < len(row_list):
                        next_row_info = row_list[index + 1]
                        if row <= next_row_info.get('start'):
                            row_info['action_after'].append(action)
                            self.all_action_obj.append(action)
                            return True
                    else:
                        row_info['action_after'].append(action)
                        self.all_action_obj.append(action)
                        return True
            else:
                row_info['action_before'].append(action)
                self.all_action_obj.append(action)
                return True

        self.add_action(action)

    def add_group_action(self, action):
        self.group_action_list.append(action)
        self.all_action_obj.append(action)

    async def collect_cell(self):
        pass

    def group(self, *args: Any, **kwds: Any):
        action = GroupAction(self)
        # self.add_group_action(action)
        action(*args, **kwds)
        return action

    def select(self, *args: Any, **kwds: Any):
        action = SelectAction(self)
        # self.add_row_action(action)
        action(*args, **kwds)
        return action

    def header(self, *args: Any, **kwds: Any):
        action = HeaderAction(self)
        # self.add_action(action)
        action(*args, **kwds)
        return action

    def content(self, *args: Any, **kwds: Any):
        action = ContentAction(self)
        # self.add_row_action(action)
        action(*args, **kwds)
        return action

    def dep(self, *args: Any, **kwds: Any):
        action = DepAction(self)
        # self.add_row_action(action)
        self.dep_action_obj.append(action)
        action(*args, **kwds)
        return action

    def add_cell(self, start_row, start_col, cell_info):
        return self.report_obj.add_cell(start_row, start_col, cell_info)

    def get_cell(self, start_row, start_col):
        return self.report_obj.get_cell(start_row, start_col)

    def calculate_place(self):
        for action in self.all_action_obj:
            action.calculate_place()
        for action in self.dep_action_obj:
            action.find_dep_cell()

    def reset_start_end(self):
        for action in self.all_action_obj:
            action.reset_start_end()

    async def export(self):
        await self.process_init_func()
        self.action_sort()
        await self.prepare()
        self.reset_start_end()
        for action in self.all_action_obj:
            await action.handle_row({}, 0)

    def prepare_field_dict(self):
        for column_info in self.display_fields:
            editor_dict = column_info.get('value')
            alias = column_info.get('name')
            editor = self.lemon_editor.init(editor_dict)
            # default_value = await self.lemon_editor.get_value(default_value_editor)
            self.field_dict[alias] = editor_dict
            build_column_info(
                editor.path_columns, self.data_model, alias_columns=self.alias_columns,
                relation_column_dict=self.relation_column_dict,
                hierarchy_column_dict=self.hierarchy_column_dict)

    async def prepare(self):
        self.calculate_place()

    def action_sort(self):
        self.all_action_obj.sort(key=lambda x: x.level)
        for row_info in self.config_info.get('rowList', []):
            row_info['actions'].sort(key=lambda x: x.level)
        # self.row_action_obj.sort(key=lambda x: x.level)

    def init_status(self):
        pass


"""
 以模型为数据源
"""


class ModelDataSet(BaseDataSet):

    def __init__(self, dataset: dict, merge_dict, sheet, report_obj: Report) -> None:
        super().__init__(dataset, merge_dict, sheet, report_obj)

    async def prepare(self):
        self.calculate_place()
        preprocessing_dict = self.dataset.get(
            "data_source", {}).get('preprocessing')

        if preprocessing_dict:
            pre_process_obj = AsyncDataPreprocess(
                self.data_model, preprocessing_dict, lsm=self.lsm, rename_model=True)
            self.data_query = await pre_process_obj()
        else:
            self.data_query = self.data_model.select()
        self.data_query = self.data_query.group_by(self.data_model.id)
        self.build_select_columns_query()

    def build_select_columns_query(self):
        data_query = build_agg_query_by_relation(
            self.data_model, self.relation_column_dict, self.data_query)
        alias_columns = list(set(self.alias_columns))
        self.data_query = data_query.select_extend(*alias_columns).dicts()
        return data_query

    async def export(self):
        await self.process_init_func()
        self.action_sort()
        await self.prepare()
        self.reset_start_end()
        app_log.info(f'开始导出数据集: {self.data_query}')
        data_list = await engine.userdb.objs.execute(self.data_query)
        row_dict = data_list[0] if data_list else {}
        dataset_add = 0
        for action in self.action_obj_list:
            await action.handle_row(row_dict, 0)
        for row_config in self.config_info.get('rowList', []):
            row_add = row_config.get('row_add', 1)
            for action in row_config.get('action_before', []):
                await action.handle_row(row_dict, dataset_add)
            for row_index, row_data in enumerate(data_list):
                for action in row_config.get('actions', []):
                    await action.handle_row(row_data, dataset_add)
                dataset_add += row_add
                self.start_row += 1
            dataset_add -= row_add
            for action in row_config.get('action_after', []):
                await action.handle_row(row_dict, dataset_add)
        dataset_start_col = self.range.get('start_col')
        dataset_end_col = self.range.get('end_col')
        for column in range(dataset_start_col, dataset_end_col + 1):
            self.ingrease_column_row(column, dataset_add)

    def init_status(self):
        self.data_source = self.dataset.get('data_source')
        model = self.data_source.get('model')
        self.data_model = lemon_model(model)


class Action():
    level = 0

    def __init__(self, namespace: BaseDataSet) -> None:
        self.namespace = namespace
        self.cell_info = {}
        self.editor = None
        self.start_row = -1
        self.start_col = -1
        self.anchor = None
        self.anchor_obj = None
        self.parent_group = {}
        self.cell: Cell = None
        self.calculate = False
        self.row_add = 1
        self.row_ingrease = -1
        self.condition_list = []

    def ingrease_column_row(self, column, add=0):
        return self.namespace.ingrease_column_row(column, add)

    def get_column_row(self, start, column):
        return self.namespace.get_column_row(start, column)

    def set_cell(self, cell: Cell):
        self.cell = cell

    def set_condition_list(self, cell):
        condition_list = self.namespace.report_obj.get_condition_list(cell)
        self.condition_list = condition_list

    def prepare(self):
        pass

    def start(self):
        self.handle_place_info()

    @property
    def style_helper(self):
        return self.namespace.report_obj.style_helper

    @property
    def column_width(self):
        column = self.cell.column
        column_name = self.namespace.report_obj.get_excel_column_name(column)
        col_width = self.cell.parent.column_dimensions[column_name].width or 8.43
        return col_width

    def handle_place_info(self):
        merge_info = self.namespace.merge_dict.get(self.cell.coordinate)
        if merge_info:
            cell_info = merge_info
        else:
            cell_info = {'start_col': self.cell.column,
                         'start_row': self.cell.row, 'rowspan': 0, 'colspan': 0, 'width': self.column_width}
        cell_style = self.style_helper.get_cell_style(self.cell)
        cell_info.update({'style': cell_style})
        self.start_row = cell_info.get('start_row')
        self.start_col = cell_info.get('start_col')
        self.cell_info.update(cell_info)
        # self.namespace.end_row = max(self.namespace.end_row, cell_info.get(
        #     'start_row') + cell_info.get('rowspan', 0))
        # self.namespace.end_col = max(self.namespace.end_col, cell_info.get(
        #     'start_col') + cell_info.get('colspan', 0))
        self.namespace.place_dict[self.cell.coordinate] = self

    def calculate_place(self):
        self.anchor_obj = self.namespace
        if self.calculate:
            # 如果是其它单元的锚点, 可能已经计算过位置
            return
        if self.anchor:
            self.anchor_obj = self.namespace.place_dict.get(self.anchor)
            # 如果锚点没有计算位置，先计算锚点位置
            if not self.anchor_obj.calculate:
                self.anchor_obj.calculate_place()
        start_col_add = self.start_col - self.anchor_obj.start_col
        start_row_add = self.start_row - self.anchor_obj.start_row
        self.cell_info.update(
            {'start_row_add': start_row_add, 'start_col_add': start_col_add})
        self.calculate = True

    # 重置和其它命名空间相对的起始位置
    def reset_start_end(self):
        if self.anchor_obj != self.namespace:
            self.anchor_obj.reset_start_end()
        self.start_row = self.anchor_obj.start_row + \
            self.cell_info.get('start_row_add')
        self.start_col = self.anchor_obj.start_col + \
            self.cell_info.get('start_col_add')

    async def handle_row(self, row_data: dict, row: int):
        # default_value = await self.namespace.lemon_editor.get_value(self.editor)
        cell_info = {'value': self.value}
        cell_info.update(self.cell_info)
        self.add_cell(self.start_row + row, self.start_col, cell_info)

    def add_cell(self, start_row, start_col, cell_info):
        row_height = self.namespace.sheet.row_dimensions[self.start_row].height or 13.5
        finally_row = self.get_column_row(start_row, start_col)
        self.namespace.report_obj.set_row_height(finally_row, row_height)

        return self.namespace.add_cell(finally_row, start_col, cell_info)

    def get_cell(self, start_row, start_col):
        return self.namespace.get_cell(start_row, start_col)

    def __call__(self, value, *args: Any, **kwds: Any) -> Any:
        self.value = value

    async def get_editor_value(self, **kwds: Any):
        value = await self.namespace.lemon_editor(self.editor, **kwds)
        editor_type = self.editor.get('type')
        if editor_type == 4:
            field = lemon_field(self.editor.get("field"))
            field_lemon_type = getattr(field, "lemon_type", None)
            if value is not None:
                if field_lemon_type == FieldType.DATETIME:
                    if isinstance(value, (int, float)):
                        value = datetime.datetime.fromtimestamp(value)
                        value = value.strftime('%Y/%m/%d %H:%M:%S')
                elif field_lemon_type == FieldType.BOOLEAN:
                    value = '是' if value else '否'
        return value


class GroupAction(Action):

    def __init__(self, namespace: BaseDataSet) -> None:
        super().__init__(namespace)
        self.child_action_list = []
        self.child_list = []
        self.level = 10000
        self.data_dict = {}
        self.data_list = []

    def __call__(self, field, *args: Any, **kwds: Any) -> Any:
        self.child_list = kwds.get('child', [])
        self.level = kwds.get('level', 10000)
        self.editor = self.namespace.field_dict.get(field)
        return self

    async def handle_row(self, row_data: dict, data_index: int):
        default_value = await self.namespace.lemon_editor.get_value(self.editor, **row_data)
        if self.data_dict.get(default_value):
            return
        self.data_dict[default_value] = {
            'value': default_value, 'children': [], 'index': data_index}

    async def wirte(self):
        pass

    async def wirte_with_parent(self, parent_value):
        pass


class SelectAction(Action):
    level = 1

    def __call__(self, field, *args: Any, **kwds: Any) -> Any:
        self.editor = self.namespace.field_dict.get(field)
        return self

    async def handle_row(self, row_data: dict, row: int):
        # self.editor.clear()
        default_value = await self.get_editor_value(**row_data)
        if default_value is None or default_value is False:
            default_value = ''
        if isinstance(default_value, Decimal):
            default_value = str(default_value)
        cell_info = {'value': default_value}
        cell_info.update(self.cell_info)
        self.add_cell(self.start_row + row, self.start_col, cell_info)


class DepAction(Action):
    level = 100

    def __init__(self, namespace: BaseDataSet) -> None:
        super().__init__(namespace)
        self.editor = None
        self.expr = None
        self.real_expr = None
        self.dep_dict = {}

    def __call__(self, expr, *args: Any, **kwds: Any) -> Any:
        self.expr = expr
        return self

    def find_dep_cell(self):
        matches = re.findall(r'({[A-Z]\d+})', self.expr)
        self.real_expr = self.expr
        for match in matches:
            rel_match = match[1:-1]
            self.real_expr = self.real_expr.replace(match, rel_match)
            cell = self.namespace.place_dict.get(rel_match)
            if cell:
                self.dep_dict[rel_match] = cell

    async def handle_row(self, row_data: dict, row: int):
        value_dict = {}
        for cell_name, cell in self.dep_dict.items():
            if not isinstance(cell, DepAction):
                cell_info = self.get_cell(cell.start_row + row, cell.start_col)
                if cell_info:
                    value = cell_info.get('value')
                else:
                    value = ''
                value_dict[cell_name] = value
            else:
                cell_info = self.get_cell(cell.start_row + row, cell.start_col)
                if not cell_info:
                    await cell.handle_row(row_data, row)
                cell_info = self.get_cell(cell.start_row + row, cell.start_col)
                value_dict[cell_name] = cell_info.get('value')
        try:
            default_value = eval(self.real_expr, value_dict)
        except Exception as e:
            default_value = ''
        cell_info = {'value': default_value}
        cell_info.update(self.cell_info)
        self.add_cell(self.start_row + row, self.start_col, cell_info)


class HeaderAction(Action):
    level = 0

    def __call__(self, field, *args: Any, **kwds: Any) -> Any:
        self.value = field
        return self


class ContentAction(Action):
    level = 0

    def __call__(self, field, *args: Any, **kwds: Any) -> Any:
        self.value = field
        return self
