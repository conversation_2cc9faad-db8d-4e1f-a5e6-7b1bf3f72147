
from apps.services import DocumentCheckerService
from apps.services.checker import checker
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.ide_const import Theme


class ThemeDocumentCheckerService(DocumentCheckerService):

    @checker.run
    def check_theme(self):
        self.check_module_theme_exist()
        self.check_app_layout_exist()

    def check_module_theme_exist(self):
        theme_uuid = self.element.get("theme_uuid")
        if theme_uuid and theme_uuid not in self.app_module_theme_dict:
            kwargs = {
                "element_data": {
                    "name": self.document_name,
                    "uuid": self.document_uuid,
                },
                "is_theme": True
            }
            self._add_error_list(attr=Theme.ATTR.THEME_SELECTED,
                                 return_code=LDEC.MODULE_THEME_SELECTED_DOESNOT_EXIST, **kwargs)

    def check_app_layout_exist(self):
        applayout_uuid = self.element.get("applayout_uuid")
        if applayout_uuid and applayout_uuid not in self.app_layout_dict:
            kwargs = {
                "element_data": {
                    "name": self.document_name,
                    "uuid": self.document_uuid,
                },
                "is_theme": True
            }
            self._add_error_list(attr=Theme.ATTR.APPLAYOUT_SELECTED,
                                 return_code=LDEC.APP_LAYOUT_SELECTED_DOESNOT_EXIST, **kwargs)
