import inspect
import logging
import time
from logging import Logger
from typing import Dict, Type, Optional, Literal

from baseutils.common_metrics import CUSTOM_REGISTRY, extract_error_name
from prometheus_client import Histogram

from tests import utils

EVENT_TO_NAME_MAPPING: Dict[str, str] = {}


def init_event_to_name_mapping():
    for attr_name in dir(utils):
        if not attr_name.endswith("UUID"):
            continue

        attr = getattr(utils, attr_name)
        if not inspect.isclass(attr):
            continue

        for class_prop_name in dir(attr):
            class_prop_value = getattr(attr, class_prop_name)
            if not isinstance(class_prop_value, str) or len(class_prop_value) != 32:
                continue
            EVENT_TO_NAME_MAPPING[class_prop_value] = f"[{attr_name}]{class_prop_name}"


init_event_to_name_mapping()


def try_extract_event_name(event_uuid: str) -> str:
    if not event_uuid:
        return ""
    return EVENT_TO_NAME_MAPPING.get(event_uuid, "unknown")


class RuntimeMetrics:
    WSRequestHistogram = Histogram(
        "runtime_ws_request",
        "",
        ["command_name", "event", "error"],
        registry=CUSTOM_REGISTRY
    )
    CLOUD_FUNCTION_RUN = Histogram(
        "runtime_cloud_function_run",
        "",
        ["function_name", "error"]
    )
    VALUE_EDITOR_RUN = Histogram(
        "runtime_value_editor_run",
        "",
        ["type", "mode", "error"]
    )
    CLIENT_RPC = Histogram(
        "runtime_client_rpc",
        "",
        ["command", "error"]
    )

    @classmethod
    def report_client_rpc(cls, command: str, error_class: Optional[Type[Exception]], duration: float):
        error_name = extract_error_name(error_class)
        cls.CLIENT_RPC.labels(command, error_name).observe(duration)

    @classmethod
    def report_function_run(cls, function_name: str, error_class: Optional[Type[Exception]], duration: float):
        error_name = extract_error_name(error_class)
        cls.CLOUD_FUNCTION_RUN.labels(function_name, error_name).observe(duration)

    @classmethod
    def report_value_editor_run(
            cls, value_editor_type: str, mode: str, error_class: Optional[Type[Exception]], duration: float
    ):
        error_name = extract_error_name(error_class)
        cls.VALUE_EDITOR_RUN.labels(value_editor_type, mode, error_name).observe(duration)

    @classmethod
    def report_ws_request(
            cls, command_name: str, event_uuid: str, error_class: Optional[Type[Exception]], time_cost: float
    ):
        error = extract_error_name(error_class)
        cls.WSRequestHistogram.labels(
            command_name,
            try_extract_event_name(event_uuid),
            error
        ).observe(time_cost)


class FuncInstrumentContext:
    SLOW_THRESHOLD = 0.5

    def __init__(self, function_name: str, logger: Optional[logging.Logger] = None):
        self.function_name = function_name
        self.start_time: float = 0.0
        self.logger: Optional[logging.Logger] = logger

    def __enter__(self):
        self.start_time = time.time()

    def __exit__(self, exc_type: Optional[Type[Exception]], exc_val, exc_tb):
        duration = time.time() - self.start_time
        # RuntimeMetrics.report_function_run(self.function_name, exc_type, duration)

        if not self.logger:
            return
        label = "slow_func_duration" if duration > self.SLOW_THRESHOLD else "func_duration"
        self.logger.info("[FuncMetrics] %s=%.4fs func_name=%s", label, duration, self.function_name)


class ValueEditorInstrumentContext:
    SLOW_THRESHOLD = 0.05

    def __init__(self, value_editor_type: Literal['expr', 'field'], mode: Literal['sync', 'async'],
                 expr: str = "", action_expr: str = "", logger: Optional[Logger] = None):
        self.value_editor_type = value_editor_type
        self.mode = mode
        self.start_time: float = 0.0
        self.expr = expr
        self.action_expr = action_expr
        self.logger = logger

    def __enter__(self):
        self.start_time = time.time()

    def __exit__(self, exc_type: Optional[Type[Exception]], exc_val, exc_tb):
        # duration = time.time() - self.start_time
        # RuntimeMetrics.report_value_editor_run(
        #     self.value_editor_type, self.mode, exc_type, duration
        # )
        # if not self.logger:
        #     return
        # label = "slow_expr_duration" if duration > self.SLOW_THRESHOLD else "expr_duration"
        # self.logger.info("[ValueEditorMetrics] %s=%.4fs expr=%s action_expr=%s mode=%s",
        #                  label, duration, self.expr, self.action_expr, self.mode)
        pass
