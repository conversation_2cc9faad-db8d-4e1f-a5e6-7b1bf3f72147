# -*- coding:utf-8 -*-

from copy import copy, deepcopy
import time
import asyncio
from datetime import datetime

from sanic.response import json


from baseutils.const import Code
from baseutils.log import app_log
from apps.utils import lemon_uuid, replace_relationship_uuid
from apps.entity import (
    ModelBasic, ModelField, RelationshipBasic, Page as PageModel, Document as DocumentModel
)
from apps.utils import process_args
from apps.utils import LemonDictResponse as LDR
from apps.ide_const import (
    ComponentType, Document, ButtonClickEventType, EventAction, RSelectPopup, FileSourceType,
    TypeSystem, AddMethodType, DataSourceType, FieldType, IDECode, PageToolName, InputSetting)
from apps.services.document.page import (
    DateTimeCycle, InputVisible, NewButton, EditButton, DeleteButton, ExportButton, ImportButton, Pagination, RelationCascade, RelationTree, 
    SearchItem, ColumnItem, SearchBar, Subtable, SubDatalist, Datalist, Page, TextVisible, UploadFile, UploadImage, Cardlist,
    Form, Input, Select, Radio, Datetime, Checkbox, Textarea, RelationSelect, RelationSelectTable,
    TitleValue, EditableValue, FormEditableValue, InputPlaceHolderValue, SelectPlaceHolderValue, Text,
    input_text_field, input_text_manual, input_text_unlimit, input_number_currency, input_number_default, 
    input_style, input_visible as base_input_visible, select_style, form_style, form_controller, PageTitleValue, FormGrid, FormRow, FormCol, 
    FormCancelButton, FormSubmitButton, datalist_column_style, datalist_style, datalist_controller,
    datalist_function_bar, datalist_column_width, datalist_column_format, datetime_cycle_style, calendar_visible, StringTitleValue, SubmitEvent,
    UNEditableValue, FormFieldInfo, text_sytle, FormVisible, Image, image_datasource, image_size, image_style, File, InputTextAttribute,
    EventActionData,  Component_Buttons, cardlist_style, cardlist_line_layout, cardlist_card_item, cardlist_popup_search_bar,
    cardlist_titleStyle, cardlist_cardItemTitleStyle, cardlist_card_item, cardlist_visible, cardlist_controller
)
from apps.services.document.value import FieldValue
from apps.services.ide import IDEService
from tests.utils import UUID
from collections import defaultdict


class BaseComponentCreateService(IDEService):

    component_type = ComponentType.INPUT
    component_name = PageToolName.INPUT

    def __init__(self, engine):
        super().__init__(engine)
        self.type_index = 1
        base_input_visible.get("is_visible", {})["uuid"] = lemon_uuid()
        self.input_visible = deepcopy(base_input_visible)
    
    def relationship_field_info(self):
        relation_extra = self.extra or {}
        placeholder = {"is_many": self.is_many}
        if self.is_many != 1:
            if self.is_source == 1:
                is_required = relation_extra.get("source_required", False)
            else:
                is_required = relation_extra.get("target_required", False)
        else:
            is_required = False
        placeholder["is_required"] = is_required
        placeholder["model"] = self.model_uuid
        field_info = FormFieldInfo(
            uuid=self.field_uuid if self.field_uuid else self.name_field,
            check={"type": ComponentType.COMPONENT_TO_FIELD.get(self.component_type)},
            path=[self.relationship_uuid], placeholder=placeholder
        )
        return field_info

    def create_component(self, request_component_type=None):
        raise NotImplementedError()
    
    def get_index_name(self):
        return self.component_name + str(self.type_index)
    
    def create_field(self, field_dict, editable=True, path=None, endKeys=None, tabIndex=None,
                     aggre_func=None, input_setting={}):
        self.field_uuid = field_dict.get(ModelField.field_uuid.name)
        self.field_name = field_dict.get(ModelField.field_name.name)
        self.field_type = field_dict.get(ModelField.field_type.name)
        self.field_length = field_dict.get(ModelField.length.name)
        self.field_is_required = field_dict.get(ModelField.is_required.name)
        self.enum_uuid = field_dict.get(ModelField.enum_uuid.name)
        display_name = field_dict.get(ModelField.display_name.name)
        self.display_name = display_name or self.field_name
        self.path = path
        self.aggre_func = aggre_func
        if isinstance(editable, dict) and editable:
            # 替换掉传递来的 uuid
            editable.update({"uuid": lemon_uuid()})
            self.editable_value = editable
        elif editable:
            self.editable_value = EditableValue()
        else:
            self.editable_value = UNEditableValue()
        self.endKeys = endKeys
        self.tabIndex = tabIndex
        self.input_setting = input_setting
        return self.create_component()

    def create_relationship(self, relationship_dict, editable=True,
                            endKeys=None, tabIndex=None):
        self.field_uuid = relationship_dict.get(ModelField.field_uuid.name)
        self.name_field = relationship_dict.get(ModelBasic.name_field.name)
        self.data_name = relationship_dict.get(ModelBasic.data_name.name)
        self.model_name = relationship_dict.get(ModelBasic.model_name.name)
        self.model_uuid = relationship_dict.get(ModelBasic.model_uuid.name)
        self.is_many = relationship_dict.get("is_many")
        self.extra = relationship_dict.get("extra", {})
        self.is_source = relationship_dict.get("is_source")
        frontref = relationship_dict.get(RelationshipBasic.frontref.name)
        backref = relationship_dict.get(RelationshipBasic.backref.name)
        target_model = relationship_dict.get(RelationshipBasic.target_model.name)
        source_model = relationship_dict.get(RelationshipBasic.source_model.name)
        if not hasattr(self, "relationship_display_name"):
            if self.model_uuid == target_model:
                self.relationship_display_name = frontref
            elif self.model_uuid == source_model:
                self.relationship_display_name = backref
            else:
                self.relationship_display_name = relationship_dict.get(RelationshipBasic.relationship_name.name)
        if editable:
            self.editable_value = EditableValue()
        else:
            self.editable_value = UNEditableValue()
        self.relationship_uuid = relationship_dict.get("relationship_uuid")
        self.endKeys = endKeys
        self.tabIndex = tabIndex
        return self.create_component()

    def create_relationship_with_data_source(
        self, relationship_dict, editable=True,
            endKeys=None, tabIndex=None, **kwargs):
        for k, arg in kwargs.items():
            setattr(self, k, arg)
        return self.create_relationship(relationship_dict,
                                        editable, endKeys, tabIndex)


class InputCreateService(BaseComponentCreateService):

    component_type = ComponentType.INPUT
    component_name = PageToolName.INPUT

    def create_component(self, request_component_type=None):
        field_info = FormFieldInfo(
            uuid=self.field_uuid,
            check={"type": ComponentType.COMPONENT_TO_FIELD.get(
                ComponentType.INPUT)},
            placeholder={"is_required": self.field_is_required,
                         "type": self.field_type, "enum_uuid": ""}
        )
        placeholder_value = "请输入{}".format(self.display_name)
        text_attribute, number_attribute = dict(), dict()
        if self.field_type == FieldType.STRING:
            text_attribute = InputTextAttribute(
                max_length_type=0, max_length=self.field_length).to_dict()
        elif self.field_type in [FieldType.INTEGER, FieldType.DECIMAL]:
            number_attribute = input_number_default
        return Input(
            uuid=lemon_uuid(), name=self.get_index_name(),
            class_id=lemon_uuid(),field_info=field_info,
            title=StringTitleValue(self.display_name),
            editable=self.editable_value, endKeys=self.endKeys,
            tabIndex=self.tabIndex, input_setting=self.input_setting,
            placeholder=InputPlaceHolderValue(placeholder_value),
            text_attribute=text_attribute, number_attribute=number_attribute,
            style=input_style, visible=self.input_visible
        )


class SelectCreateService(BaseComponentCreateService):

    component_type = ComponentType.SELECT
    component_name = PageToolName.SELECT

    def create_component(self, request_component_type=None):
        enum_uuid = ""
        if self.field_type == FieldType.ENUM:
            enum_uuid = self.enum_uuid
        field_info = FormFieldInfo(
            uuid=self.field_uuid, 
            check={"type": ComponentType.COMPONENT_TO_FIELD.get(
                ComponentType.SELECT)},
            placeholder={"is_required": self.field_is_required,
                         "type": self.field_type, "enum_uuid": enum_uuid}
        )
        return Select(
            uuid=lemon_uuid(), name=self.get_index_name(),
            class_id=lemon_uuid(), field_info=field_info,
            title=StringTitleValue(self.display_name),
            endKeys=self.endKeys, tabIndex=self.tabIndex,
            editable=self.editable_value, placehodler=SelectPlaceHolderValue(),
            style=select_style, visible=self.input_visible
        )


class RadioCreateService(BaseComponentCreateService):

    component_type = ComponentType.RADIO
    component_name = PageToolName.RADIO
    

    def create_component(self, request_component_type=None):
        field_info = FormFieldInfo(
            uuid=self.field_uuid, 
            check={"type": ComponentType.COMPONENT_TO_FIELD.get(ComponentType.RADIO)},
            placeholder={"is_required": self.field_is_required, "type": self.field_type, "enum_uuid": ""}
        )
        return Radio(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(),
            field_info=field_info, title=StringTitleValue(self.display_name), 
            editable=self.editable_value, style=select_style, visible=self.input_visible
        )


class DatetimeCreateService(BaseComponentCreateService):

    component_type = ComponentType.DATETIME
    component_name = PageToolName.DATETIME

    def create_component(self, request_component_type=None):
        field_info = FormFieldInfo(
            uuid=self.field_uuid, 
            check={"type": ComponentType.COMPONENT_TO_FIELD.get(ComponentType.DATETIME)},
            placeholder={"is_required": self.field_is_required}
        )
        return Datetime(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(),
            field_info=field_info, title=StringTitleValue(self.display_name), 
            editable=self.editable_value, placehodler=SelectPlaceHolderValue(), 
            style=select_style, visible=self.input_visible
        )


class UploadFileCreateService(BaseComponentCreateService):

    component_type = ComponentType.UPLOAD_FILE
    component_name = PageToolName.UPLOAD_FILE

    def create_component(self, request_component_type=None):
        field_info = FormFieldInfo(
            uuid=self.field_uuid, 
            check={"type": ComponentType.COMPONENT_TO_FIELD.get(ComponentType.UPLOAD_FILE)},
            placeholder={"is_required": self.field_is_required}
        )
        return UploadFile(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(),
            field_info=field_info, title=StringTitleValue(self.display_name), 
            editable=self.editable_value, style=select_style, visible=self.input_visible
        )

class UploadImageCreateService(BaseComponentCreateService):

    component_type = ComponentType.UPLOAD_IMAGE
    component_name = PageToolName.UPLOAD_IMAGE
    
    def create_component(self, request_component_type=None):
        field_info = FormFieldInfo(
            uuid=self.field_uuid, 
            check={"type": ComponentType.COMPONENT_TO_FIELD.get(ComponentType.UPLOAD_IMAGE)},
            placeholder={"is_required": self.field_is_required}
        )
        return UploadImage(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(),
            field_info=field_info, title=StringTitleValue(self.display_name), 
            editable=self.editable_value, style=select_style, visible=self.input_visible
        )


class DateTimeCycleCreateService(BaseComponentCreateService):

    component_type = ComponentType.DATETIME_CYCLE
    component_name = PageToolName.DATETIME_CYCLE
    
    def create_component(self, request_component_type=None):
        field_info = {"uuid": self.field_uuid, "placeholder": {
            "type": ComponentType.COMPONENT_TO_FIELD.get(ComponentType.DATETIME_CYCLE)[0],
            "is_required": True}}
        return DateTimeCycle(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(),
            field_info=field_info, title=StringTitleValue(self.display_name), 
            style=datetime_cycle_style, visible=self.input_visible, editable=self.editable_value
        )

class TextCreateService(BaseComponentCreateService):

    component_type = ComponentType.TEXT
    component_name = PageToolName.TEXT
    
    def create_component(self, request_component_type=None):
        valueEdit = FieldValue(
            None, self.field_uuid, once=True, is_monitor=True, 
            path=self.path, aggre_func=self.aggre_func)
        data_source = {"valueEdit": valueEdit.to_dict()}
        if self.field_type == FieldType.DECIMAL:
            format = {"number_format": {"precision": 2}}
        else:
            format = {}
        return Text(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(),
            data_source=data_source,
            title=StringTitleValue(self.display_name),
            style=select_style, visible=text_sytle,
            format=format
        )

class ImageCreateService(BaseComponentCreateService):

    component_type = ComponentType.IMAGE
    component_name = PageToolName.IMAGE
    
    def create_component(self, request_component_type=None):
        is_visible = FormVisible()
        data_source = image_datasource
        return Image(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(),
            data_source=data_source,
            title=StringTitleValue(self.display_name),
            style=image_style, visible={"is_visible": is_visible.to_dict()}, size=image_size
        )

class FileCreateService(BaseComponentCreateService):

    component_type = ComponentType.FILE
    component_name = PageToolName.FILE
    
    def create_component(self, request_component_type=None):
        is_visible = FormVisible()
        field_info = {"uuid": self.field_uuid}
        return File(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(),
            title=StringTitleValue(self.display_name),
            style=image_style, visible={"is_visible": is_visible.to_dict()}, field_info=field_info
        )


class RSelectCreateService(BaseComponentCreateService):

    component_type = ComponentType.R_SELECT
    component_name = PageToolName.R_SELECT

    def create_component(self, request_component_type=None):
        field_info = self.relationship_field_info()
        placeholder_value = "请选择{}".format(self.data_name)
        return RelationSelect(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(), field_info=field_info,
            title=StringTitleValue(self.relationship_display_name), editable=self.editable_value, new_page=dict(), view_page=dict(),
            endKeys=self.endKeys, tabIndex=self.tabIndex, placeholder=SelectPlaceHolderValue(placeholder_value),
            style=select_style, visible=self.input_visible
        )


class RCascadeCreateService(BaseComponentCreateService):

    component_type = ComponentType.R_CASCADE
    component_name = PageToolName.R_CASCADE

    def create_component(self, request_component_type=None):
        field_info = self.relationship_field_info()
        placeholder_value = "请选择{}".format(self.data_name)
        return RelationCascade(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(), 
            field_info=field_info, title=StringTitleValue(self.display_name), 
            editable=self.editable_value, placeholder=SelectPlaceHolderValue(placeholder_value), 
            style=select_style, visible=self.input_visible
        )


class RTreeCreateService(BaseComponentCreateService):

    component_type = ComponentType.R_TREE
    component_name = PageToolName.R_TREE

    def create_component(self, request_component_type=None):
        field_info = self.relationship_field_info()
        data_source = self.create_data_source()
        placeholder_value = "请选择{}".format(self.data_name)
        return RelationTree(
            uuid=lemon_uuid(), name=self.get_index_name(), class_id=lemon_uuid(), 
            field_info=field_info, title=StringTitleValue(self.relationship_display_name), 
            editable=self.editable_value, placeholder=SelectPlaceHolderValue(placeholder_value), 
            style=select_style, visible=self.input_visible, data_source=data_source
        )

    def create_data_source(self):
        data_source = {
            "type": 1,
            "association": {
                "is_source": 0,
                "model": self.model_uuid,
                "path": [self.relationship_uuid],
                "relationship_uuid": self.relationship_uuid
            }
        }
        return data_source


class ComponentCreateService(IDEService):

    def __init__(self, engine):
        super().__init__(engine)
        self.component_create_class_dict = {
            ComponentType.INPUT: InputCreateService,
            ComponentType.SELECT: SelectCreateService,
            ComponentType.RADIO: RadioCreateService,
            ComponentType.DATETIME: DatetimeCreateService,
            ComponentType.UPLOAD_FILE: UploadFileCreateService,
            ComponentType.UPLOAD_IMAGE: UploadImageCreateService,
            ComponentType.R_SELECT: RSelectCreateService,
            ComponentType.R_CASCADE: RCascadeCreateService,
            ComponentType.DATETIME_CYCLE: DateTimeCycleCreateService,
            ComponentType.TEXT: TextCreateService,
            ComponentType.IMAGE: ImageCreateService,
            ComponentType.FILE: FileCreateService,
            ComponentType.R_TREE: RTreeCreateService
        }
    
    def create_service(self, component_type, index_dict=None) -> BaseComponentCreateService:
        service_class = self.component_create_class_dict.get(component_type)
        # 计算同页面相同组件的个数
        if index_dict:
            type_index = index_dict.get(component_type, 0)
            type_index += 1
            service_class.type_index = type_index
            index_dict[component_type] = type_index
        if service_class:
            return service_class(self.engine)
        return None


class BasePageCreateService(IDEService):

    show_many = True
    find_name_field = False  # 是否获取关联字段信息

    def __init__(self, engine):
        super().__init__(engine)
        self.kwargs = dict()
        self.document = None

    def create_component(self, request_component_type=None) -> dict:
        raise NotImplementedError()

    def create_select_page(self):
        from apps.base_utils import safe_pop
        import copy
        # parent_type = self.parent.get("type")
        # if parent_type in [ComponentType.FORM, ComponentType.TABS]: 更新在折叠面板内报错
        self.element.update({"name": self.page_name})
        parent_model_uuid = self.parent.get("data_source", {}).get("model")
        element_data_source = self.element.get("data_source")
        # 去除 data_source 的 select_page 否则会嵌套多层
        safe_pop(element_data_source, "select_page")
        # self.check_relation_data_source(element_data_source)
        add_method = element_data_source.get("add_method")
        if add_method in [AddMethodType.HAND_INPUT, AddMethodType.SELECT]:
            purpose = element_data_source.get("purpose", 0)
            path = element_data_source.get("path", [])
            is_many = element_data_source.get("is_many", 0)
            candidate_preprocessing = element_data_source.get(
                "candidate_preprocessing")
            datalist_uuid = lemon_uuid()
            select_datalist_data = copy.deepcopy(self.element)
            popup_page_search = select_datalist_data.get(
                "popup_page_search", {})
            # from apps.ide_const import Datalist
            # attr = Datalist.ATTR.SUBTABLE
            # search_items = popup_page_search.get("items", [])
            # for search_item in search_items:
            #     fieldsValue = search_item.get("fieldsValue")
            #     self.update_reference_by_value_edit(
            #         fieldsValue, search_item, attr)
            if popup_page_search:
                popup_page_search.update(
                    {"show_search_button": True, "show_delete_button": True,
                        "funcbar_search_button_text": "搜索"})
            columns = select_datalist_data.get("columns", [])
            for column in columns:
                column.update({"filterable": True})
            select_datalist_data["popup_search_bar"] = popup_page_search
            select_datalist_data_buttons = select_datalist_data.get(
                "buttons", []) or []
            select_datalist_data_buttons.clear()  # 不需要父类的按钮区
            select_datalist_data.update({
                "uuid": datalist_uuid, "show_row_selection": True, "name": self.page_title})
            data_source_type = DataSourceType.MODEL_WITH_PRE
            data_source = copy.deepcopy(element_data_source)
            data_source.update({
                "type": data_source_type, "model": self.model_uuid, "parent_model": parent_model_uuid,
                "parent_path": path, "purpose": purpose, "preprocessing": candidate_preprocessing,
                "is_many": is_many, "relsect_popup": 1
            })
            select_datalist_data.update({
                "data_source": data_source, "order": list(), "show_checkbox_select": True, 'show_buttons': True,
                "pagination": Pagination(pageSize=20, show_empty_lines=False).to_dict(), "subtable": list(), "class_id": lemon_uuid(),
                "is_select_page": 1
            })
            select_component_type = select_datalist_data.get("type")
            if select_component_type == ComponentType.CARDLIST:
                # 清空选择页面的卡片事件
                select_datalist_data.update({"events": {"click": []}})
            event_action_data = EventActionData(
                action=EventAction.SUBMIT, component=datalist_uuid)
            event_action_data = {"click": [event_action_data.to_dict()]}
            form_submit_button_title = "选择" if add_method == AddMethodType.SELECT else "保存"
            button_items = [FormCancelButton(), FormSubmitButton(
                    event=event_action_data, title=form_submit_button_title)]
            select_page = Page(
                uuid=lemon_uuid(), name=self.page_name, children=[select_datalist_data],
                open_type=self.open_type, page_title=PageTitleValue(self.page_title),
                buttons=button_items
            )
        return select_page


    async def create_page_document(self, request):
        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            model_uuid = str(request.json.get("model_uuid", ""))
            p_page_uuid = str(request.json.get("p_page_uuid"))
            self.p_component_type = request.json.get("p_component_type")
            self.p_component_path = request.json.get("p_component_path")
            self.p_component_relationship_uuid = request.json.get("p_component_relationship_uuid")
            open_type = int(request.json.get("open_type"))
            page_title = str(request.json.get("page_title"))
            editable = request.json.get("editable", True)
            self.kwargs = request.json.get("kwargs", dict())
            self.action_kwargs = request.json.get("action_kwargs", dict())

        p_page = await self.engine.access.select_page_document_by_page_uuid(
            page_uuid=p_page_uuid)
        if not p_page:
            return json(LDR(IDECode.P_PAGE_NOT_EXISTS))

        # now = datetime.now()
        # _name = now.strftime("%Y-%m-%d %H:%H:%M.%f")
        app_uuid = p_page.get(PageModel.app_uuid.name)
        self.module_uuid = p_page.get(PageModel.module_uuid.name)
        # p_page_name = p_page.get(PageModel.page_name.name)
        # p_page_document_name = p_page.get(DocumentModel.document_name.name)
        self.p_page_document_puuid = p_page.get(DocumentModel.document_puuid.name)
        p_page_document_path = p_page.get(DocumentModel.document_path.name)
        # page_name = "_".join([p_page_name, _name])
        page_name = "_".join([page_title])
        # page_document_name = "_".join([p_page_document_name, _name])
        page_document_name = "_".join([page_title])
        self.document_uuid = lemon_uuid()
        self.document_type = Document.TYPE.PAGE
        now_timestamp = int(time.time())

        if model_uuid:
            self.field_list = await self.engine.access.list_field_by_model_uuid(model_uuid)
            self.relationship_list = list()
            self.self_associate_relations = list()
            self.model_uuid = model_uuid
            relationship_model_list = await self.engine.access.list_relationship(
                model_uuid, show_many=self.show_many, find_name_field=self.find_name_field,
                allow_self_associate=False)
            if self.p_component_type in [ComponentType.TREELIST, ComponentType.TREE]:
                self_associates = await self.engine.access.list_relationship(
                    model_uuid, show_many=self.show_many, find_name_field=self.find_name_field,
                    allow_self_associate=True)
                self.self_associate_relations.extend(self_associates)
            self.relationship_list.extend(relationship_model_list)
        else:
            self.field_list = list()
            self.relationship_list = list()

        self.model_uuid = model_uuid
        self.open_type = open_type
        self.page_title = page_title
        self.page_name = page_name
        self.editable = editable

        document_dict = dict(
            app_uuid=app_uuid,
            module_uuid=self.module_uuid,
            document_uuid=self.document_uuid,
            document_name=page_document_name,
            document_type=self.document_type,
            document_path=p_page_document_path,
            document_puuid=self.p_page_document_puuid,
            create_timestamp=now_timestamp,
            update_timestamp=now_timestamp
        )
        self.document = await self.engine.access.create_document(user_uuid, **document_dict)

    def get_element_and_parent_by_content_uuid(self, element, parent_uuid, target_uuid):
        result = None
        if isinstance(element, list):
            for e in element:
                if isinstance(e, (list, dict)) and e:
                    result = self.get_element_and_parent_by_content_uuid(e, parent_uuid, target_uuid)
                if result:
                    return result
        elif isinstance(element, dict):
            _parent_uuid = element.get("dataIndex") or element.get("uuid")
            if _parent_uuid == target_uuid:
                return element, parent_uuid
            for e in list(element.values()):
                if isinstance(e, (dict, list)) and e:
                    result = self.get_element_and_parent_by_content_uuid(e, _parent_uuid, target_uuid)
                if result:
                    return result
        if result:
            return result

    async def create_document(self, request):
        from apps.engine import engine
        await self.create_page_document(request)
        self.model = await engine.access.select_model_by_model_uuid(self.model_uuid)
        content_uuid = request.json.get("element_uuid")
        document_uuid = request.json.get("document_uuid")
        app_uuid = request.json.get("app_uuid")
        document = await engine.access.get_document_content_by_document_uuid(document_uuid)
        if document:
            self.document_content = document.document_content
            if self.document_content and content_uuid:
                try:
                    self.element, self.parent_uuid = self.get_element_and_parent_by_content_uuid(
                        element=self.document_content, parent_uuid="", target_uuid=content_uuid)
                    self.parent, _ = self.get_element_and_parent_by_content_uuid(
                        element=self.document_content, parent_uuid="", target_uuid=self.parent_uuid)
                except Exception as e:
                    app_log.info(f"create_document error : {e}")
                    return json(LDR(IDECode.CONTENT_NOT_EXISTS))
        request_component_type = request.parsed_json.get("request_component_type", None)
        page = self.create_component(request_component_type=request_component_type)
        if isinstance(page, dict):
            page_dict = page
            uuid = page.get("uuid")
        else:
            page_dict = page.to_dict()
            uuid = page.uuid
        json_data = {
            "app_uuid": app_uuid,
            "module_uuid": self.module_uuid,
            "document_uuid": self.document_uuid,
            "document_version": 1,
            "document_content": page_dict
            }
        document_check_service = self.engine.service.document_check.create_service(self.document_type)
        if document_check_service:
            await document_check_service.check(json_data, self.document_type, force_in_current_proc=True)
        return json(LDR(data={
            "document_uuid": self.document_uuid, "uuid": uuid,
            "document_puuid": self.p_page_document_puuid
            }))


class EmptyPageCreateService(BasePageCreateService):

    def create_component(self, request_component_type=None) -> dict:
        page = Page(
            uuid=lemon_uuid(), name=self.page_name, children=[], open_type=self.open_type,
            page_title=PageTitleValue(self.page_title), buttons=[]
        )
        return page


class FormCreateService(BasePageCreateService):

    async def create_page_document(self, request):
        result = await super().create_page_document(request)
        await self.create_item_info()
        return result

    async def create_item_info(self):

        many_rela_uuid_list = list()
        many_field_list_dict = dict()
        many_rela_item_dict = dict()
        many_rela_item = list()
        many_ass_rela_dict = defaultdict(list)
        many_ass_unrela_dict = dict()
        # self.model_uuid
        for relationship_dict in self.relationship_list:
            # 判断是否为多端
            if relationship_dict.get("model_uuid") != relationship_dict.get("source_model"):
                continue
            if relationship_dict.get("relationship_type") != 0:
                continue
            # 最多生成三个子表
            if len(many_rela_uuid_list) > 2:
                break
            many_rela_uuid = relationship_dict.get("relationship_uuid")
            many_ass_rela_dict[many_rela_uuid].append(relationship_dict)
            many_rela_uuid_list.append(many_rela_uuid)

            many_model_uuid = relationship_dict.get("source_model")
            many_target_uuid = relationship_dict.get("target_model")
            if many_model_uuid:
                many_field_list = await self.engine.access.list_field_by_model_uuid(many_model_uuid)
                many_rela_list = list()
                many_relationship_model_list = await self.engine.access.list_relationship(
                    many_model_uuid, show_many=self.show_many, find_name_field=self.find_name_field,
                    allow_self_associate=False)
                many_rela_list.extend(many_relationship_model_list)

                many_field_list_dict[many_rela_uuid] = many_field_list
                many_rela_item_dict[many_rela_uuid] = many_rela_list
            else:
                many_field_list_dict[many_rela_uuid] = list()
                many_rela_item.extend(list())
                many_rela_item_dict[many_rela_uuid] = list()

            # 为数据模型生成关联字段
            many_ass_rela_list = list()
            many_ass_unrela_list = list()
            
            for many_associate_rela in many_rela_list:
                if many_target_uuid != many_associate_rela.get("model_uuid"):
                    many_ass_unrela_list.append(many_associate_rela)
                else:
                    many_ass_rela_list.append(many_associate_rela)
            many_ass_rela_dict[many_rela_uuid].append(many_ass_rela_list)  # many_ass_rela_list可以为[]
            many_ass_unrela_dict[many_rela_uuid] = many_ass_unrela_list

        self.many_field_item = many_field_list_dict
        self.many_rela_item = many_rela_item_dict
        self.many_rela_uuid_list = many_rela_uuid_list
        self.many_ass_rela_dict = many_ass_rela_dict
        self.many_ass_unrela_dict = many_ass_unrela_dict

    def create_association_datalist(self):

        from apps.engine import engine

        component_type = ComponentType.DATALIST
        search_items = []
        attr_class = RSelectPopup.ATTR
        datalist_items = list()

        try:
            many_field_item = self.many_field_item
            many_rela_item = self.many_rela_item
            many_ass_unrela_dict = self.many_ass_unrela_dict
            many_rela_uuid_list = self.many_rela_uuid_list
            many_ass_rela_dict = self.many_ass_rela_dict
        except:
            many_field_item = list()
            many_rela_item = dict()
            many_ass_unrela_dict = dict()
            many_rela_uuid_list = list()
            many_ass_rela_dict = dict()

        for many_rela_uuid in many_rela_uuid_list:

            many_rela_list = many_ass_rela_dict.get(many_rela_uuid, list())
            if many_rela_list:
                many_relationship_dict = many_rela_list[0]
            else:
                many_relationship_dict = dict()

            many_field_list = many_field_item.get(many_rela_uuid)
            many_ass_rela_list = many_ass_unrela_dict.get(many_rela_uuid)

            relation_extra = many_relationship_dict.get("extra", dict())
            target_model = many_relationship_dict.get("target_model")
            field_model_uuid = many_relationship_dict.get("model_uuid")
            if target_model == field_model_uuid:
                rela_is_required = relation_extra.get("source_required", False)
            else:
                rela_is_required = relation_extra.get("target_required", False)

            create_service = engine.service.page_create.create_service(component_type)
            create_service.field_list = many_field_list
            create_service_info = {
                "page_name": "", "page_title": "", "editable": True, "relationship_list": [],
                "model_uuid": many_relationship_dict.get("model_uuid"), "open_type": 0
            }
            attr = attr_class.POP_FIELD
            for search_item in search_items:
                fieldsValue = search_item.get("fieldsValue")
                self.update_reference_by_value_edit(fieldsValue, search_item, attr)
            for key, attr in create_service_info.items():
                setattr(create_service, key, attr)

            page = create_service.create_component(many_ass_rela_list=many_ass_rela_list)
            page_content = page.to_dict()
            children_list = page_content.get("children")
            data_list = deepcopy(children_list[0])
            association = many_relationship_dict.get("relationship_uuid")
            columns = data_list.get("columns")
            for col in columns:
                col.update({"filterable": False})
            datalist_data_source = {
                "add_method": 0, "association": association, "data_source": dict(),
                "is_many": many_relationship_dict.get("is_many"),
                "is_source": many_relationship_dict.get("is_source"), "path": [association],
                "placeholder": {"is_required": rela_is_required},
                "purpose": 0, "show_associated_data": True, "type": DataSourceType.ASSOCIATION_MEMORY
                }

            is_visible = FormVisible()
            visible = {"is_visible": is_visible.to_dict()}

            # frontref = many_relationship_dict.get(RelationshipBasic.frontref.name)
            backref = many_relationship_dict.get(RelationshipBasic.backref.name)

            event_action_data = EventActionData(action=EventAction.NEW, component=lemon_uuid())
            event_action_data = {"click": [event_action_data.to_dict()]}
            button_items = Component_Buttons.create_datalist_buttons()
            datalist_title = {"is_monitor": True, "once": True, "type": 0, "uuid": lemon_uuid(),
                              "value": backref}

            data_list.update({"name": backref, "controller": datalist_controller, "data_source": datalist_data_source,
                              "style": datalist_style, "visible": visible, "title": datalist_title,
                              "show_buttons": True, "buttons": button_items})
            datalist_items.append(data_list)
        return datalist_items

    def create_component(self, request_component_type=None) -> dict:
        column_items = []
        tabIndex = 0
        endKeys = {'r': True, 't': True, 'r_n': True}
        index_dict = {}
        for field_dict in self.field_list:
            field_editable = field_dict.get("editable")
            aggre_field = field_dict.get(ModelField.aggre_field.name)
            if aggre_field:
                continue
            hierarchy_field = field_dict.get(ModelField.hierarchy_field.name)
            if hierarchy_field == 1:
                continue
            else:
                field_type = field_dict.get(ModelField.field_type.name)
                component_type_list = ComponentType.FIELD_TO_COMPONENT.get(field_type)
                component_type = component_type_list[0] if len(component_type_list) >= 1 else None
                if field_type == FieldType.STRING and field_dict.get("is_serial"):
                    field_editable = False
                elif field_dict.get("calculate_field"):
                    field_editable = False
            component_create_service = self.engine.service.component_create.create_service(component_type,
                                                                                           index_dict=index_dict)
            # 自定义editable 覆盖前端指定的
            editable = self.editable if field_editable is None else field_editable
            addition = [ComponentType.INPUT, ComponentType.R_SELECT,
                        ComponentType.SUBMIT_BUTTON, ComponentType.CANCEL_BUTTON, ComponentType.SELECT]
            if component_create_service is not None:
                if component_type in addition:
                    column_items.append(component_create_service.create_field(
                        field_dict, editable=editable, endKeys=endKeys, tabIndex=tabIndex,
                        input_setting=InputSetting.FORM_INPUTSETTING))
                    tabIndex += 1
                else:
                    column_items.append(component_create_service.create_field(field_dict, editable=editable))
        for relationship_dict in self.relationship_list:
            component_type = ComponentType.R_SELECT
            #  在为数据模型生成表单的时候，只为从这个数据模型发出的关联（数据模型位于键尾）生成关联组件
            create_is_many = self.action_kwargs.get("create_is_many", False)
            if relationship_dict.get("is_source") == 1 and not create_is_many:
                if relationship_dict.get("relationship_type") == 0:
                    if relationship_dict.get("source_model") == relationship_dict.get("model_uuid"):
                        continue
                elif relationship_dict.get("relationship_type") == 2:
                    continue
            if relationship_dict.get("calculate_field"):
                continue
            relationship_field_type = relationship_dict.get("field_type")
            if (relationship_field_type != FieldType.ENUM and relationship_field_type != FieldType.STRING
                    and relationship_field_type != FieldType.INTEGER and relationship_field_type != FieldType.DECIMAL):
                continue
            component_create_service = self.engine.service.component_create.create_service(ComponentType.R_SELECT,
                                                                                           index_dict=index_dict)
            if component_create_service is not None:
                name_field = relationship_dict.get(ModelBasic.name_field.name)
                if name_field or relationship_dict.get(ModelField.field_uuid.name):
                    column_items.append(component_create_service.create_relationship(
                        relationship_dict, editable=self.editable, endKeys=endKeys, tabIndex=tabIndex))
                    tabIndex += 1

        need_create_r_tree = []
        tree_node_child = deepcopy(self.self_associate_relations)
        for relationship_dict in self.self_associate_relations:
            replace_relationship_uuid(relationship_dict, RelationshipBasic)
            renamed_relationship_uuid = relationship_dict.get("relationship_uuid")
            if self.p_component_type in [ComponentType.TREELIST, ComponentType.TREE] and (
                    renamed_relationship_uuid == self.p_component_relationship_uuid):
                relationship_dict.update({"relationship_display_name": "父节点"})
                need_create_r_tree.append(relationship_dict)
        # 自关联多对一的关联
        for relationship_dict in tree_node_child:
            replace_relationship_uuid(
                relationship_dict, RelationshipBasic, from_source=False, keep=True)
            if self.p_component_type in [ComponentType.TREE]:
                relationship_dict.update({"relationship_display_name": "子节点"})
                need_create_r_tree.append(relationship_dict)        

        for relationship_dict in need_create_r_tree:
            if self.p_component_path and self.p_component_relationship_uuid:
                component_type = ComponentType.R_TREE
                component_create_service = self.engine.service.component_create.create_service(
                    component_type, index_dict=index_dict)
                name_field = relationship_dict.get(ModelBasic.name_field.name)  # TODO 需要拿到的是那条关联
                relationship_display_name = relationship_dict.get("relationship_display_name")
                if name_field or relationship_dict.get(ModelField.field_uuid.name):
                    c = component_create_service.create_relationship_with_data_source(
                        relationship_dict, editable=self.editable, endKeys=endKeys, tabIndex=tabIndex,
                        relationship_display_name=relationship_display_name
                        )
                    column_items.append(c)
                    tabIndex += 1

        associated_datalist = self.create_association_datalist()
        column_items.extend(associated_datalist)
        data_source = {"type": DataSourceType.FORM_WITH_CONTEXT,
                       "model": self.model_uuid}
        form_uuid = lemon_uuid()
        submit_is_refresh = self.action_kwargs.get("submit_is_refresh", True)
        button_items = [FormCancelButton(endKeys=endKeys, tabIndex=tabIndex),
                        FormSubmitButton(
                            endKeys=endKeys, tabIndex=tabIndex+1,
                            is_refresh=submit_is_refresh)]
        tabIndex += 2
        if self.editable is None:
            self.editable = True
        editable = FormEditableValue() if self.editable else UNEditableValue()
        uneditable_type = 0 if self.editable else 1
        is_visible = FormVisible()
        visible = {"is_visible": is_visible.to_dict()}
        # form_affairs控制 新建页面或编辑页面的断开时本地暂存按钮，2为开启，跟前端约定
        form_affairs = 2
        form = Form(
            uuid=form_uuid, name=self.page_name, class_id=lemon_uuid(),
            editable=editable, style=form_style, data_source=data_source,
            controller=form_controller, uneditable_type=uneditable_type,
            buttons=button_items, children=column_items, visible=visible,
            form_affairs=form_affairs
        )
        page = Page(
            uuid=lemon_uuid(), name=self.page_name, children=[form],
            open_type=self.open_type,
            page_title=PageTitleValue(self.page_title), **self.kwargs
        )
        return page


class DatalistCreateService(BasePageCreateService):

    show_many = False
    find_name_field = True

    def __init__(self, engine):
        super().__init__(engine)

    async def create_page_document(self, request):
        result = await super().create_page_document(request)
        await FormCreateService.create_item_info(self)
        return result

    def create_r_select_page(self):
        from apps.engine import engine
        component_type = ComponentType.DATALIST
        # app_relationship_dict = self.app_relationship_dict
        create_service = engine.service.page_create.create_service(
            component_type)
        select_field = self.element.get(
            "modal_page", {}).get("modal_fields", [])
        if not select_field:
            self.element["select_page"] = {}
        field_list = []
        search_items = []
        # for field_info in select_field:
        #     self.build_field_dict(
        #         field_info, field_list, app_relationship_dict, search_items)
        search_operation_relation = self.element.get("modal_page", {}).get(
            "search_operation_relation", 0)
        popup_search_bar = {
            "operation_relation": search_operation_relation,
            "show_search_button": True, "show_delete_button": True,
            "funcbar_search_button_text": "搜索", "items": search_items
        }
        element = self.element
        # 父组件可能是表单或数据列表
        element_field = element.get("field_info") or element.get("edit_field")
        field_model = element_field.get("model")
        if not field_model:
            return False
        # 这里可能会导致model_instance为None
        app_log.info(f"field_model: {field_model}")
        create_service.field_list = field_list
        create_service_info = {
            "page_name": self.page_name, "page_title": self.page_title,
            "editable": True, "relationship_list": [],
            "model_uuid": field_model, "open_type": 0,
            "popup_search_bar": popup_search_bar
        }
        attr = RSelectPopup.ATTR.POP_FIELD
        # for search_item in search_items:
        #     fieldsValue = search_item.get("fieldsValue")
        #     self.update_reference_by_value_edit(fieldsValue, search_item, attr)
        for key, attr in create_service_info.items():
            setattr(create_service, key, attr)
        page = self.create_component(request_component_type=True)
        page_content = page.to_dict()
        children_list = page_content.get("children")
        data_list = children_list[0]
        data_list["show_checkbox_select"] = True
        # self.element["select_page"] = page_content
        # app_log.info(create_service)
        self.model_uuid  # 修行者
        self.parent.get("data_source", {}).get("model", "")  # 境界
        is_many = self.element.get("field_info", {}).get("placeholder", {}).get("is_many", 1)
        data_list["data_source"]["is_many"] = is_many
        data_list.update({"is_select_page": 1})
        candidate_preprocessing = self.element.get(
            "candidate_preprocessing", dict())
        data_list["data_source"]["path"] = element_field.get("path", [])
        if candidate_preprocessing:
            data_list["data_source"]["preprocessing"] = candidate_preprocessing
        # add_method
        return page_content

    def create_component(self, request_component_type=None, many_ass_rela_list=list()) -> dict:
        column_items = []
        if request_component_type == ComponentType.DATALIST:
            _page = self.create_select_page()
            return _page
        if request_component_type == ComponentType.R_SELECT_POPUP:
            _page = self.create_r_select_page()
            return _page
        for field_dict in self.field_list:
            count_limit, extension, preview, chunk_size, file_limit = None, None, None, None, None
            filterable = False
            field_path = field_dict.get("path", [])
            aggre_func = field_dict.get("aggre_func", None)
            hierarchy_field = field_dict.get(ModelField.hierarchy_field.name)
            if hierarchy_field == 1:  # 自动生成数据列表时，忽略层次字段
                continue
            aggre_field = field_dict.get(ModelField.aggre_field.name)
            if aggre_field:
                continue
            field_uuid = field_dict.get(ModelField.field_uuid.name)
            field_path_name = field_dict.get("path_name")
            field_type = field_dict.get(ModelField.field_type.name)
            if field_type == TypeSystem.DATETIME_CYCLE:  # 不生成时间循环字段
                continue
            field_display_name = field_path_name or field_dict.get(ModelField.display_name.name)
            # 关联弹框页面自动生成会把关联字段也加入fileld_list
            if field_path:
                if len(field_path) > 1:
                    input_control = ComponentType.TEXTAREA
                else:
                    input_control = ComponentType.R_SELECT
            else:
                input_control_list = ComponentType.FIELD_TO_DATALSIT_COMPONENT.get(field_type)
                input_control = input_control_list[0] if len(input_control_list) >= 1 else None
            component_type_list = ComponentType.FIELD_TO_SHOW_COMPONENT.get(field_type)
            component_type = component_type_list[0] if len(component_type_list) >= 1 else None
            component_create_service = self.engine.service.component_create.create_service(component_type)
            placeholder = {"is_required": field_dict.get(ModelField.is_required.name),
                           "type": field_type}
            if field_type == FieldType.ENUM:
                placeholder["enum_uuid"] = field_dict.get(
                    ModelField.enum_uuid.name)
            edit_field = FormFieldInfo(
                uuid=field_uuid, check={"type": ComponentType.COMPONENT_TO_FIELD.get(
                    input_control)}, placeholder=placeholder)
            if component_create_service is not None:
                component_info = component_create_service.create_field(field_dict,
                                                                       editable=self.editable,
                                                                       path=field_path,
                                                                       aggre_func=aggre_func)
                component_info = component_info.to_dict()
                component_data_source = component_info.get("data_source")
                if component_type == ComponentType.IMAGE:
                    count_limit, extension = 99, ["bmp", "jpg", "jpeg", "png", "gif"]
                    preview = {"width": 86, "height": 86}
                    if component_data_source:
                        component_data_source.update({"field_info": {"uuid": field_uuid}})
                elif component_type == ComponentType.FILE:
                    component_data_source.update({"field_info": {"uuid": field_uuid}, "type": FileSourceType.FIELD})
                    chunk_size, file_limit = 10, 99
                    extension = ["png", "jpg", "zip", "txt", "docx", "exe", "xlsx", "pdf"]
                elif component_type in [ComponentType.TEXT, ComponentType.TAG]:
                    filterable = True
            else:
                component_info = dict()
            editable = {'is_monitor': True, 'once': True, 'type': 0, 'uuid': lemon_uuid(), 'value': False}
            cell_settings = {"editable": EditableValue().to_dict()}
            base_input_visible.get("is_visible", {})["uuid"] = lemon_uuid()
            item = ColumnItem(
                title=StringTitleValue(field_display_name), field=field_uuid, dataIndex=lemon_uuid(),
                width=datalist_column_width, input_control=input_control, uuid=lemon_uuid(),
                aggregation={"use_aggregation": False}, format=datalist_column_format,
                style=datalist_column_style, edit_field=edit_field, component=component_info,
                editable=editable, is_preview=True, count_limit=count_limit, extension=extension,
                preview=preview, chunk_size=chunk_size, file_limit=file_limit, cell_settings=cell_settings,
                visible=base_input_visible, filterable=filterable
            )
            column_items.append(item)
        field_name_list = [ModelField.field_type.name, ModelField.is_required.name,
                           ModelField.field_name.name, ModelField.enum_uuid, ModelField.display_name.name]
        for relationship_dict in self.relationship_list:
            count_limit, extension, preview, chunk_size, file_limit = None, None, None, None, None
            filterable = False
            input_control = ComponentType.R_SELECT
            #  在为数据模型生成表单的时候，只为从这个数据模型发出的关联（数据模型位于键尾）生成关联组件
            if relationship_dict.get("is_source") == 1:
                continue
            name_field = relationship_dict.get(ModelBasic.name_field.name)
            if name_field:
                data_name = relationship_dict.get(ModelBasic.data_name.name)
                model_display_name = relationship_dict.get(ModelBasic.display_name.name)
                model_is_many = relationship_dict.get("model_is_many")
                if model_is_many:
                    continue
                relationship_uuid = relationship_dict.get(RelationshipBasic.relationship_uuid.name)
                field_dict = {field_name: relationship_dict.get(field_name) for field_name in field_name_list}
                field_dict[ModelField.field_uuid.name] = name_field
                field_type = field_dict.get(ModelField.field_type.name)
                field_display_name = data_name if data_name else model_display_name
                component_type_list = ComponentType.FIELD_TO_SHOW_COMPONENT.get(field_type)
                component_type = component_type_list[0] if len(component_type_list) >= 1 else None
                component_create_service = self.engine.service.component_create.create_service(component_type)
                placeholder = {}
                if field_type == FieldType.ENUM:
                    placeholder["enum_uuid"] = field_dict.get(ModelField.enum_uuid.name)
                edit_field = FormFieldInfo(
                    uuid=name_field,
                    check={"type": ComponentType.COMPONENT_TO_FIELD.get(input_control)},
                    placeholder=placeholder
                )
                if component_create_service is not None:
                    component_info = component_create_service.create_field(
                        field_dict, editable=self.editable, path=[relationship_uuid])
                    component_info = component_info.to_dict()
                    component_data_source = component_info.get("data_source")
                    if component_type == ComponentType.IMAGE:
                        if component_data_source:
                            component_data_source.update({"field_info": {"uuid": field_uuid}})
                        count_limit, extension = 99, ["bmp", "jpg", "jpeg", "png", "gif"]
                        preview = {"width": 86, "height": 86}
                    elif component_type == ComponentType.FILE:
                        component_data_source.update({"field_info": {"uuid": field_uuid}, "type": FileSourceType.FIELD})
                        chunk_size, file_limit = 10, 99
                        extension = ["png", "jpg", "zip", "txt", "docx", "exe", "xlsx", "pdf"]
                    elif component_type in [ComponentType.TEXT, ComponentType.TAG]:
                        filterable = True
                else:
                    component_info = dict()
                editable = {'is_monitor': True, 'once': True, 'type': 0, 'uuid': lemon_uuid(), 'value': False}
                cell_settings = {"editable": EditableValue().to_dict()}
                base_input_visible.get("is_visible", {})["uuid"] = lemon_uuid()
                item = ColumnItem(
                    title=StringTitleValue(field_display_name), field=name_field, dataIndex=lemon_uuid(),
                    width=datalist_column_width, input_control=input_control, uuid=lemon_uuid(),
                    aggregation={"use_aggregation": False}, format=datalist_column_format,
                    style=datalist_column_style, component=component_info, edit_field=edit_field,
                    editable=editable, is_preview=True, count_limit=count_limit, extension=extension,
                    preview=preview, chunk_size=chunk_size, file_limit=file_limit, cell_settings=cell_settings,
                    visible=base_input_visible, filterable=filterable
                    )
                base_input_visible.get("is_visible", {})["uuid"] = lemon_uuid()
                column_items.append(item)

        for relationship_dict in many_ass_rela_list:
            if relationship_dict.get("relationship_type") == 1:
                continue  # 不生成多端字段
            input_control = ComponentType.R_SELECT
            name_field = relationship_dict.get(ModelBasic.name_field.name)
            if name_field:
                count_limit, extension, preview, chunk_size, file_limit = None, None, None, None, None
                filterable = False
                data_name = relationship_dict.get(ModelBasic.data_name.name)
                frontref = relationship_dict.get(RelationshipBasic.frontref.name)
                target_model = relationship_dict.get(RelationshipBasic.target_model.name)
                source_model = relationship_dict.get(RelationshipBasic.source_model.name)
                model_uuid = relationship_dict.get(ModelBasic.model_uuid.name)
                if model_uuid == target_model:
                    model_display_name = frontref
                elif model_uuid == source_model:
                    continue  # 不生成多端字段
                else:
                    model_display_name = relationship_dict.get(ModelBasic.display_name.name)
                relationship_uuid = relationship_dict.get(RelationshipBasic.relationship_uuid.name)
                field_dict = {field_name: relationship_dict.get(field_name) for field_name in field_name_list}
                field_dict[ModelField.field_uuid.name] = name_field
                field_type = field_dict.get(ModelField.field_type.name)
                field_display_name = data_name if data_name else model_display_name
                component_type_list = ComponentType.FIELD_TO_SHOW_COMPONENT.get(field_type)
                component_type = component_type_list[0] if len(component_type_list) >= 1 else None
                component_create_service = self.engine.service.component_create.create_service(component_type)
                placeholder = {}
                if field_type == FieldType.ENUM:
                    placeholder["enum_uuid"] = field_dict.get(ModelField.enum_uuid.name)
                edit_field = FormFieldInfo(
                    uuid=name_field,
                    check={"type": ComponentType.COMPONENT_TO_FIELD.get(input_control)},
                    placeholder=placeholder
                )
                if component_create_service is not None:
                    component_info = component_create_service.create_field(
                        field_dict, editable=self.editable, path=[relationship_uuid])
                    component_info = component_info.to_dict()
                    component_data_source = component_info.get("data_source")
                    if component_type == ComponentType.IMAGE:
                        if component_data_source:
                            component_data_source.update({"field_info": {"uuid": field_uuid}})
                        count_limit, extension = 99, ["bmp", "jpg", "jpeg", "png", "gif"]
                        preview = {"width": 86, "height": 86}
                    elif component_type == ComponentType.FILE:
                        component_data_source.update({"field_info": {"uuid": field_uuid}, "type": FileSourceType.FIELD})
                        chunk_size, file_limit = 10, 99
                        extension = ["png", "jpg", "zip", "txt", "docx", "exe", "xlsx", "pdf"]
                    elif component_type in [ComponentType.TEXT, ComponentType.TAG]:
                        filterable = True
                else:
                    component_info = dict()
                editable = {'is_monitor': True, 'once': True, 'type': 0, 'uuid': lemon_uuid(), 'value': False}
                cell_settings = {"editable": EditableValue().to_dict()}
                base_input_visible.get("is_visible", {})["uuid"] = lemon_uuid()
                item = ColumnItem(
                    title=StringTitleValue(model_display_name), field=name_field, dataIndex=lemon_uuid(),
                    width=datalist_column_width, input_control=input_control, uuid=lemon_uuid(),
                    aggregation={"use_aggregation": False}, format=datalist_column_format,
                    style=datalist_column_style, component=component_info, edit_field=edit_field,
                    editable=editable, is_preview=True, count_limit=count_limit, extension=extension,
                    preview=preview, chunk_size=chunk_size, file_limit=file_limit, cell_settings=cell_settings,
                    visible=base_input_visible, filterable=filterable
                    )
                column_items.append(item)

        datalist_uuid = lemon_uuid()
        event_action_data = EventActionData(action=EventAction.SUBMIT, component=datalist_uuid)
        event_action_data = {"click": [event_action_data.to_dict()]}

        datalist_data_source = {
            "model": self.model_uuid, "type": DataSourceType.MODEL_WITH_PRE}
        if request_component_type is True:
            datalist_data_source["relsect_popup"] = 1
            # popup_search_bar= self.popup_search_bar
            popup_search_bar = {
                "operation_relation": 0,
                "show_search_button": True, "show_delete_button": True,
                "funcbar_search_button_text": "搜索", "items": []
            }
            form_submit_button_title = "选择"
        else:
            datalist_data_source["relsect_popup"] = 0
            popup_search_bar = None
            form_submit_button_title = "保存"
        button_items = [FormCancelButton(), FormSubmitButton(
            event=event_action_data, title=form_submit_button_title)]
        datalist = Datalist(
            uuid=datalist_uuid, name=self.page_name, class_id=lemon_uuid(),
            controller=datalist_controller, data_source=datalist_data_source,
            columns=column_items, order=[], search_bar={},
            function_bar=datalist_function_bar, pagination=Pagination(is_shown=None, show_empty_lines=False),
            empty_image="", subtable=[], style=datalist_style,
            popup_search_bar=popup_search_bar
        )
        page = Page(
            uuid=lemon_uuid(), name=self.page_name, children=[datalist], open_type=self.open_type,
            page_title=PageTitleValue(self.page_title), buttons=button_items
        )
        return page


class CardlistCreateService(BasePageCreateService):

    async def create_page_document(self, request):
        result = await super().create_page_document(request)
        await FormCreateService.create_item_info(self)
        return result

    def create_component(self, request_component_type=None, many_ass_rela_list=list()) -> dict:
        model_name = self.model.get("model_name", "电子表格")
        cardist_data_source = {
            "model": self.model_uuid, "type": DataSourceType.MODEL_WITH_PRE, "model_name": model_name}
        cardlist_title = {"type": 0, "value": model_name, "value_type": 0}
        cardlist_uuid = lemon_uuid()
        event_action_data = EventActionData(action=EventAction.SUBMIT, component=cardlist_uuid)
        event_action_data = {"click": [event_action_data.to_dict()]}
        button_items = [FormCancelButton(), FormSubmitButton(event=event_action_data)]
        if request_component_type == ComponentType.CARDLIST:
            _page = self.create_select_page()
            return _page
        cardlsit = Cardlist(
            uuid=cardlist_uuid, name=self.page_name, style=cardlist_style, title=cardlist_title,
            visible=cardlist_visible, children=[], card_item=cardlist_card_item, controller=cardlist_controller,
            pagination=Pagination(is_shown=None, show_empty_lines=False), titleStyle=cardlist_titleStyle,
            data_source=cardist_data_source,
            line_layout=cardlist_line_layout, cardItemTitleStyle=cardlist_cardItemTitleStyle,
            class_id=lemon_uuid(), popup_search_bar=cardlist_popup_search_bar
        )
        page = Page(
            uuid=lemon_uuid(), name=self.page_name, children=[cardlsit], open_type=self.open_type,
            page_title=PageTitleValue(self.page_title), buttons=button_items
        )
        return page


class PageCreateService(IDEService):

    def __init__(self, engine):
        super().__init__(engine)
        self.create_class_dict = {
            ComponentType.PAGE: EmptyPageCreateService,
            ComponentType.FORM: FormCreateService,
            ComponentType.DATALIST: DatalistCreateService,
            ComponentType.R_SELECT_POPUP: DatalistCreateService,
            ComponentType.CARDLIST: CardlistCreateService
            # CardlistCreateService
        }

    def create_service(self, component_type) -> BasePageCreateService:
        service_class = self.create_class_dict.get(component_type)
        return service_class(self.engine)
