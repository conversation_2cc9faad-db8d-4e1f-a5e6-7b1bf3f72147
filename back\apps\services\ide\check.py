# -*- coding:utf-8 -*-

import time
import asyncio
import traceback
import ujson
import datetime
import os
from itertools import groupby
from operator import itemgetter
from concurrent.futures import Process<PERSON>oolExecutor

from sanic.response import json

from apps.services.checker.py_module import PyModuleCheckerService
from baseutils.const import Code
from baseutils.log import app_log
from baseutils.celery import celery
from baseutils.celery_task_utils import get_engine_in_worker, push_async_task, TaskResult
from baseutils.utils import LemonContextVar
from baseutils.hack import LemonAsyncQueryWrapper
from celery.result import AsyncResult
from celery import Task
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.entity import (
    CheckMessage, ConstTable, Document as DocumentModel, DocumentReference, EnumTable, 
    ModelField, ToolCategory, Localhistory, DocumentDraft
)
from apps.entity import DocumentContent, RelationshipBasic, <PERSON><PERSON><PERSON> as ModuleModel, User<PERSON><PERSON>, LabelPrint
from apps.entity import ModelBasic, Func, StateMachine, ImageTable, ModelField, Page, Print, Workflow
from apps.utils import (
    list_system_relationship, pre_process_self_associate, process_args, lemon_uuid, gen_json_delta,
    replace_relationship_uuid
)
from apps.utils import LemonDictResponse as LDR

from apps.ide_const import IDECode, Document, EntityType, DocumentType, LemonDesignerErrorCode as LDEC, EventAction

from apps.services import DocumentCheckerService
from apps.services import ModelDocumentCheckerService
from apps.services import StateMachineDocumentCheckerService
from apps.services import FuncDocumentCheckerService
from apps.services import ImageDocumentCheckerService
from apps.services import ConstDocumentCheckerService
from apps.services import JsonDocumentCheckerService
from apps.services import EnumDocumentCheckerService
from apps.services import PageDocumentCheckerService
from apps.services import PrintDocumentCheckerService
from apps.services import LabelPrintDocumentCheckerService
from apps.services import SecurityDocumentCheckerService
from apps.services import RuleChainDocumentCheckerService
from apps.services import WorkflowDocumentCheckerService
from apps.services import ConnectorDocumentCheckerService
from apps.services import ExcelTemplateDocumentCheckerService
from apps.services import ExportTemplateDocumentCheckerService
from apps.services import ApprovalFlowDocumentCheckService
from apps.services import ModuleDeployDocumentCheckService
from apps.services import RestfulDocumentCheckerService
from apps.services import NavigationDocumentCheckerService
from apps.services import APPSecurityDocumentCheckerService
from apps.services import ThemeDocumentCheckerService
from apps.services import ThirdAuthDocumentCheckerService
from apps.services import WatermarkDocumentCheckerService
from apps.services import ModuleThemeDocumentCheckerService
from apps.services import ExpansionpackDocumentCheckerService
from apps.services import AppLayoutDocumentCheckerService
from apps.services.checker.permission_config import PermissionConfigDocumentCheckerService
from apps.services.ide import IDEService
from apps.services.checker import CheckerService
from apps.json_schema.main import Validator
from typing import Any, Dict, List
from loguru import logger


class BaseCheckService(IDEService):

    def __init__(self, engine):
        super().__init__(engine)
        self.is_copy = False
        self.component_copy_dict = dict()
    
    def process_model_list(self, model_list):
        model_uuid_set = set()
        for model in model_list:
            model_uuid = model.get(ModelBasic.model_uuid.name)
            model_uuid_set.add(model_uuid)
        return model_uuid_set
    
    def process_model_name_list(self, model_list, module_uuid=None):
        model_name_set = set()
        for model in model_list:
            model_name = model.get(ModelBasic.model_name.name)
            model_module_uuid = model.get(ModelBasic.module_uuid.name)
            if module_uuid:
                if module_uuid and model_module_uuid == module_uuid:
                    model_name_set.add(model_name)
            else:
                model_name_set.add(model_name)
        return model_name_set
    
    def trans_model_list_to_dict(self, model_list):
        model_dict = {}
        for model in model_list:
            model_uuid = model.get(ModelField.model_uuid.name)
            model_dict[model_uuid] = model
        return model_dict

    def process_field_list(self, field_list):
        field_uuid_set = set()
        for field in field_list:
            field_uuid = field.get(ModelField.field_uuid.name)
            field_uuid_set.add(field_uuid)
        return field_uuid_set

    def process_relationship_list(self, relationship_list):
        relationship_uuid_set = set()
        for r in relationship_list:
            relationship_uuid = r.get(RelationshipBasic.relationship_uuid.name)
            relationship_uuid_set.add(relationship_uuid)
            if r.get("source_model") == r.get("target_model"):
                relationship_uuid_source = replace_relationship_uuid(r, RelationshipBasic, replace=False)
                relationship_uuid_set.add(relationship_uuid_source)
                relationship_uuid_target = replace_relationship_uuid(r, RelationshipBasic, from_source=False)
                relationship_uuid_set.add(relationship_uuid_target)
        return relationship_uuid_set

    def process_other_conflict_document_names(self, model_list, workflow_list, func_list, enum_list, const_list):
        reference_name_dict = dict()

        def add_names_to_dict(obj_list, key):
            for obj in obj_list:
                module_uuid = obj.get("module_uuid")
                name = obj.get(key)
                if module_uuid == self.module_uuid:
                    reference_name_dict.setdefault(module_uuid, set())
                    reference_name_dict[module_uuid].add(name)

        add_names_to_dict(model_list, ModelBasic.model_name.name)
        add_names_to_dict(workflow_list, Workflow.wf_name.name)
        add_names_to_dict(func_list, Func.func_name.name)
        add_names_to_dict(enum_list, EnumTable.enum_name.name)
        add_names_to_dict(const_list, ConstTable.const_name.name)
        return reference_name_dict.get(self.module_uuid, set())

    def trans_field_list_to_dict(self, field_list):
        field_dict = {}
        for field in field_list:
            field_uuid = field.get(ModelField.field_uuid.name)
            field_dict[field_uuid] = field
        return field_dict

    def process_func_list(self, func_list):
        func_uuid_dict = dict()
        for func in func_list:
            func_uuid = func.get(Func.func_uuid.name)
            func_uuid_dict[func_uuid] = func
        return func_uuid_dict
    
    def process_const_list(self, const_list):
        const_uuid_set = set()
        for const in const_list:
            const_uuid = const.get(ConstTable.const_uuid.name)
            const_uuid_set.add(const_uuid)
        return const_uuid_set
    
    def process_enum_list(self, enum_list):
        enum_uuid_set  = set()
        enum_value_uuid_set = set()
        for enum in enum_list:
            enum_uuid = enum.get(EnumTable.enum_uuid.name)
            enum_uuid_set.add(enum_uuid)
            enum_value_list = enum.get(EnumTable.value.name)
            for enum_value in enum_value_list:
                enum_value_uuid = enum_value.get("uuid")
                enum_value_uuid_set.add(enum_value_uuid)
        return enum_uuid_set, enum_value_uuid_set

    def process_image_list(self, image_list):
        image_uuid_set = set()
        for image in image_list:
            image_uuid = image.get(ImageTable.image_uuid.name)
            image_uuid_set.add(image_uuid)
        return image_uuid_set
    
    def process_page_list(self, page_list):
        page_uuid_set = set()
        for page in page_list:
            page_uuid = page.get(Page.page_uuid.name)
            page_uuid_set.add(page_uuid)
        return page_uuid_set

    def checker_service(self) -> DocumentCheckerService:
        raise NotImplementedError()

    async def check_document(self) -> DocumentCheckerService:
        raise NotImplementedError()
    
    def init_service(self, json_data):
        # 检查应用全部文档时使用
        self.app_uuid = str(json_data.get("app_uuid"))
        self.module_uuid = str(json_data.get("module_uuid"))
        self.module_name = str(json_data.get("module_name"))
        self.document_uuid = str(json_data.get("document_uuid"))
        self.document_name = str(json_data.get("document_name"))
        self.document_version = int(json_data.get("document_version"))
        self.check_document_uuid = json_data.get("check_document_uuid")
        self.document_content = json_data.get("document_content")
    
    async def collect_app_data(self, data_list):
        pass
        
    async def collect_app_data_func(self):
        return []
        
    async def get_model_data(self, cache_dict=None):
        class_name = self.__class__.__name__
        data_list = None
        if cache_dict is not None:
            data_list = cache_dict.get(class_name)
        if not data_list:
            data_list = await self.collect_app_data_func()
            if isinstance(cache_dict, dict):
                cache_dict[class_name] = data_list
        await self.collect_app_data(data_list)
        
    def init_app_service(self, *args, **kwargs):
        """app级别初始化"""
        self.app_uuid = str(kwargs.get("app_uuid"))
        self.ext_tenant = kwargs.get("ext_tenant")
        self.new_ext_doc = False
        # self.source_doc_content = None
        
    def init_document_service(self, *args, **kwargs):
        """document级别初始化"""
        self.module_uuid = str(kwargs.get("module_uuid"))
        self.module_name = str(kwargs.get("module_name"))
        self.document_uuid = str(kwargs.get("document_uuid"))
        self.document_name = str(kwargs.get("document_name"))
        self.document_version = int(kwargs.get("document_version"))
        self.check_document_uuid = kwargs.get("check_document_uuid")
        self.document_content = kwargs.get("document_content")
        self.document_number = kwargs.get("document_number")
        self.document_type = kwargs.get("document_type")
    
    def check_message(self):
        document_checker_service = self.checker_service()
        try:
            document_checker_service.check_all()
        except Exception as e:
            app_log.error(e)
        return document_checker_service

    async def check_message_commit(self, checker_service):
        error_list = checker_service.error_list
        error = True if error_list else False
        warning_list = checker_service.warning_list
        warning = True if warning_list else False
        info_list = checker_service.info_list
        info = True if info_list else False
        # 检查应用全部文档时使用
        next_document_version = self.document_version + 1
        document_update_data = {
            DocumentModel.update_timestamp.name: int(time.time()),
            DocumentModel.document_version.name: next_document_version
        }
        # reference_data = {}
        # if isinstance(checker_service, CheckerService):
        #     document_other_info = checker_service.document_other_info
        #     field_dict = document_other_info.get("field_dict", {})
        #     model_dict = document_other_info.get("model_dict", {})
        #     r_dict = document_other_info.get("relationship_dict", {})
        #     func_dict = document_other_info.get("func_dict", {})
        #     enum_dict = document_other_info.get("enum_dict", {})
        #     page_dict = document_other_info.get("page_dict", {})
        #     print_dict = document_other_info.get("print_dict", {})
        #     wf_dict = document_other_info.get("wf_dict", {})
        #     enum_item_dict = document_other_info.get("enum_item_dict", {})
        #     reference_data.update({DocumentReference.field_reference.name: field_dict, 
        #                             DocumentReference.model_reference.name: model_dict,
        #                             DocumentReference.r_reference.name: r_dict,
        #                             DocumentReference.func_reference.name: func_dict,
        #                             DocumentReference.enum_reference.name: enum_dict,
        #                             DocumentReference.page_reference.name: page_dict,
        #                             DocumentReference.print_reference.name: print_dict,
        #                             DocumentReference.wf_reference.name: wf_dict,
        #                             DocumentReference.enum_item_reference.name: enum_item_dict,
        #                             DocumentReference.document_uuid.name: self.document_uuid,
        #                             DocumentReference.app_uuid.name: self.app_uuid
        #                             })
        # app_log.info(f"field_dict_save: {self.document_uuid}, {document_update_data}")
        document_content_create_data = {
            DocumentContent.document_uuid.name: self.document_uuid
        }
        if self.ext_tenant and not self.new_ext_doc:
            if self.source_doc_content is None:
                source_content = {}
            else:
                source_content = self.source_doc_content.document_content
                document_content_create_data.update({DocumentContent.source_uuid.name: self.source_doc_content.document_uuid})
            doc_content = gen_json_delta(source_content, self.document_content)
        else:
            doc_content = checker_service.element
        document_content_create_data.update({
            DocumentContent.document_content.name: doc_content
        })
        document_content_update_data = {
            DocumentContent.document_content.name: doc_content
        }
        check_message_create_data = {
            CheckMessage.document_uuid.name: self.document_uuid,
            CheckMessage.document_version.name: self.document_version,
            CheckMessage.error.name: error, 
            CheckMessage.error_message.name: error_list,
            CheckMessage.warning.name: warning,
            CheckMessage.warning_message.name: warning_list,
            CheckMessage.info.name: info,
            CheckMessage.info_message.name: info_list
        }
        if hasattr(self, "ext_tenant"):
            ext_tenant = getattr(self, "ext_tenant", None)
            if ext_tenant:
                check_message_create_data.update({"ext_tenant": ext_tenant})
        check_message_update_data = {
            CheckMessage.document_version.name: self.document_version,
            CheckMessage.error.name: error, 
            CheckMessage.error_message.name: error_list,
            CheckMessage.warning.name: warning,
            CheckMessage.warning_message.name: warning_list,
            CheckMessage.info.name: info,
            CheckMessage.info_message.name: info_list
        }
        message_func = None
        async with self.engine.db.objs.atomic():
            if checker_service.is_element_changed:
                await self.engine.access.update_document_by_document_uuid(
                    self.document_uuid, **document_update_data)
                if self.document_version == 0:
                    await self.engine.access.create_document_content(**document_content_create_data)
                else:
                    await self.engine.access.update_document_content_by_document_uuid(
                        self.document_uuid, **document_content_update_data)
            if self.check_document_uuid is None:
                message_func = self.engine.access.create_check_message(**check_message_create_data)
            else:
                message_func = self.engine.access.update_check_message_by_document_uuid(
                    self.document_uuid, **check_message_update_data)
            await message_func
            
    async def update_resource_dict_info(self, app_uuid, resource_dict, model, resource_field):
        if not resource_dict:
            return
        query = model.select(model.document_uuid, resource_field).where(resource_field.in_(list(resource_dict.keys())),
                                                                        ((model.app_uuid==app_uuid)|(model.app_uuid==self.engine.config.SYS_APP_UUID)))
        resource_list = await self.engine.access.list_obj(model, query)
        for resource in resource_list:
            resource_dict[resource[resource_field.name]].update({
                "link_document": resource["document_uuid"]
            })

    async def update_documnet_other_info(self, app_uuid, field_dict,
                                         model_dict, r_dict, func_dict,
                                         enum_dict, page_dict, print_dict,
                                         label_print_dict, wf_dict, enum_item_dict):
        await self.update_resource_dict_info(app_uuid, field_dict, ModelField, ModelField.field_uuid)
        await self.update_resource_dict_info(app_uuid, model_dict, ModelBasic, ModelBasic.model_uuid)
        await self.update_resource_dict_info(app_uuid, r_dict, RelationshipBasic, RelationshipBasic.relationship_uuid)
        await self.update_resource_dict_info(app_uuid, func_dict, Func, Func.func_uuid)
        await self.update_resource_dict_info(app_uuid, enum_dict, EnumTable, EnumTable.enum_uuid)
        await self.update_resource_dict_info(app_uuid, enum_item_dict, EnumTable, EnumTable.enum_uuid)
        await self.update_resource_dict_info(app_uuid, print_dict, Print, Print.print_uuid)
        await self.update_resource_dict_info(app_uuid, wf_dict, Workflow, Workflow.wf_uuid)
        await self.update_resource_dict_info(app_uuid, page_dict, Page, Page.page_uuid)
        await self.update_resource_dict_info(app_uuid, label_print_dict, LabelPrint, LabelPrint.label_uuid)

    async def check_commit(self, checker_service, save_document=True, check_message=None, force_update_content=False,
                           check_schema=True):
        # 检查单个文档时，使用
        # app_log.info(f"checker_service error_list: {checker_service.error_list}")
        error_list = checker_service.error_list
        error = True if error_list else False
        warning_list = checker_service.warning_list
        warning = True if warning_list else False
        info_list = checker_service.info_list
        info = True if info_list else False
        if not force_update_content:
            original_document = await self.access.get_document_by_document_uuid(self.document_uuid)
            if original_document and original_document.document_version > self.document_version:
                # 检查引用时，数据是先取出来再塞到队列的，等检查任务执行时，数据可能已经过期
                app_log.error(f"document version error: {self.document_uuid}")
                return
        next_document_version = self.document_version + 1 if save_document else self.document_version
        document_update_data = {
            DocumentModel.update_timestamp.name: int(time.time()),
            DocumentModel.document_version.name: next_document_version
        }
        reference_update_data = {}
        reference_data = {}
        if isinstance(checker_service, CheckerService):
            document_other_info = checker_service.document_other_info
            
            field_dict = document_other_info.get("field_dict", {})
            model_dict = document_other_info.get("model_dict", {})
            r_dict = document_other_info.get("relationship_dict", {})
            func_dict = document_other_info.get("func_dict", {})
            enum_dict = document_other_info.get("enum_dict", {})
            page_dict = document_other_info.get("page_dict", {})
            print_dict = document_other_info.get("print_dict", {})
            label_print_dict = document_other_info.get("label_print_dict", {})
            wf_dict = document_other_info.get("wf_dict", {})
            enum_item_dict = document_other_info.get("enum_item_dict", {})
            reference_update_data.update({DocumentReference.field_reference.name: field_dict, 
                                    DocumentReference.model_reference.name: model_dict,
                                    DocumentReference.r_reference.name: r_dict,
                                    DocumentReference.func_reference.name: func_dict,
                                    DocumentReference.enum_reference.name: enum_dict,
                                    DocumentReference.page_reference.name: page_dict,
                                    DocumentReference.print_reference.name: print_dict,
                                    DocumentReference.label_print_reference.name: label_print_dict,
                                    DocumentReference.wf_reference.name: wf_dict,
                                    DocumentReference.enum_item_reference.name: enum_item_dict,
                                    })
            reference_data.update(reference_update_data)
            reference_data.update({
                DocumentReference.document_uuid.name: self.document_uuid,
                DocumentReference.app_uuid.name: self.app_uuid
            })

            # # ! 临时处理，老数据没有存reference所在的文档uuid
            # if update_old_ref_info:
            #     await self.update_documnet_other_info(self.app_uuid, field_dict,
            #                              model_dict, r_dict, func_dict,
            #                              enum_dict, page_dict, print_dict,
            #                              label_print_dict, wf_dict, enum_item_dict)

        document_content_create_data = {
            DocumentContent.document_uuid.name: self.document_uuid
        }
        if self.ext_tenant and not self.new_ext_doc:
            if self.source_doc_content is None:
                source_content = {}
            else:
                source_content = self.source_doc_content.document_content
                document_content_create_data.update(
                    {DocumentContent.source_uuid.name: self.source_doc_content.document_uuid})
            doc_content = gen_json_delta(source_content, self.document_content)
            
        else:
            doc_content = self.document_content
        document_content_create_data.update({
            DocumentContent.document_content.name: doc_content
        })
        document_content_update_data = {
            DocumentContent.document_content.name: doc_content
        }
        check_message_create_data = {
            CheckMessage.document_uuid.name: self.document_uuid,
            CheckMessage.document_version.name: next_document_version,
            CheckMessage.error.name: error, 
            CheckMessage.error_message.name: error_list,
            CheckMessage.warning.name: warning,
            CheckMessage.warning_message.name: warning_list,
            CheckMessage.info.name: info,
            CheckMessage.info_message.name: info_list
        }
        check_message_update_data = {
            CheckMessage.document_version.name: next_document_version,
            CheckMessage.error.name: error, 
            CheckMessage.error_message.name: error_list,
            CheckMessage.warning.name: warning,
            CheckMessage.warning_message.name: warning_list,
            CheckMessage.info.name: info,
            CheckMessage.info_message.name: info_list
        }
        if check_message is None:
            check_message = await self.engine.access.get_check_message_by_document_uuid(
                self.document_uuid)
        reference_info = await self.engine.access.get_document_reference_by_document_uuid(
            self.document_uuid)
        async with self.engine.db.objs.atomic():
            await checker_service.commit_modify(self.engine)
            await self.engine.access.update_document_by_document_uuid(
                self.document_uuid, **document_update_data)
            # if checker_service.is_element_changed or force_update_content or next_document_version <= 1:
            await self.update_document_content(doc_content)
            if reference_info:
                await self.engine.access.update_reference_by_document_uuid(
                    self.document_uuid, **reference_update_data)
            else:
                await self.engine.access.create_document_reference(**reference_data)

        check_after_commit_func = getattr(checker_service, "check_after_commit", None)
        if callable(check_after_commit_func):
            get_check_after_commit_depend = getattr(
                checker_service, "get_check_after_commit_depend", None)
            if callable(get_check_after_commit_depend):
                checker_service.engine = self.engine
                t= time.time()
                await get_check_after_commit_depend()
                app_log.info(f"prepare depend cost: {time.time() - t}")
            check_after_commit_func()
            self.update_check_message(
                check_message_create_data, check_message_update_data, check_message, 
                checker_service)
        if check_message:
            checker_service.error_changed = (check_message.error_message != error_list) or (
                check_message.warning_message != warning_list) or (
                    check_message.info_message != info_list
            )
            message_func = self.engine.access.update_check_message_by_document_uuid(
                self.document_uuid, **check_message_update_data)
            await message_func
        else:
            message_func = self.engine.access.create_check_message(**check_message_create_data)
            await message_func
        if check_schema:
            # schema暂时只检查了引用，check all document的时候不用check
            await self.json_schema_validate(self.app_uuid, self.document_type, self.document_content,
                                        is_element_changed=checker_service.is_element_changed,
                                        post_validate=force_update_content)

    async def json_schema_validate(self, app_uuid: str, document_type: int, document_content: Any,
                                   is_element_changed=False, post_validate=False):
        start = time.time()
        try:
            validator = Validator(document_type,
                                  self.engine)
            await validator.pre_validate(str(app_uuid))
            for error in validator.validate(document_content):
                pass
            await validator.handle_reference_data(self.document_uuid, validator._validator.references)
            if post_validate or is_element_changed:
                tasks = LemonContextVar.atomic_cache_tasks.get()
                tasks.append(asyncio.ensure_future(validator.post_validate()))
        except Exception:
            logger.exception("**************validator error")
        app_log.info(f"{self.document_uuid} json schema validate cost: {time.time() - start}")

    async def init_check_args(self, json_data, document_type, json_return=True, check_update=False):
        # 处理 参数及必要的数据
        with process_args():
            self.module_uuid = json_data.get("module_uuid")
            self.app_uuid = json_data.get("app_uuid")
            self.document_uuid = str(json_data.get("document_uuid"))
            self.document_version = int(json_data.get("document_version"))
            self.document_content = json_data.get("document_content")
            self.ext_tenant = json_data.get("ext_tenant")
        if json_return:
            if not all([self.document_uuid]):
                return json(LDR(Code.ARGS_ERROR))
            # 防止模型文档被清空
            if document_type == DocumentType.MODEL:
                models = self.document_content.get("models", [])
                if not models:
                    model_list = await self.access.list_model_by_module_uuid(self.module_uuid)
                    if model_list:
                        return json(LDR(IDECode.MODEL_DOCUMENT_CLEAN_ERROR))

    async def backup_async(self, app_uuid, document_uuid, document_content):
        model = Localhistory
        data = {
            model.app_uuid.name: app_uuid,
            model.document_uuid.name: document_uuid,
            model.document_content.name: document_content,
            model.timestamp.name: int(datetime.datetime.now().replace(second=0, microsecond=0).timestamp())
        }
        await self.engine.access.create_localhistory(**data)

    async def save_draft(self, document_uuid, content):
        async with self.engine.db.objs.atomic():
            obj = await self.engine.access.get_obj(DocumentContent, document_uuid=document_uuid)
            if not obj:
                await self.engine.access.create_document_content(**{
                    DocumentContent.document_uuid.name: document_uuid,
                    DocumentContent.document_content.name: content
                })
            return await self.engine.access.update_or_create_document_draft_by_document_uuid(document_uuid, **{
                DocumentDraft.document_content.name: content,
            })

    @celery.task
    def create_content_backup(self, app_uuid, document_uuid, document_content):
        asyncio.get_event_loop().run_until_complete(self.backup_async(app_uuid, document_uuid, document_content))

    async def check(self, json_data, document_type, *, force_run_in_pool=False,
                    with_result=False, backup=True, force_in_current_proc=False, is_import=False, **kwargs):
        assert not (force_in_current_proc and force_run_in_pool)
        res = None
        app_uuid = json_data.get("app_uuid")
        document_uuid = json_data.get("document_uuid")
        document_content = json_data.get("document_content")
        timestamp = json_data.get("timestamp")
        assert app_uuid and document_uuid and document_content, "check args error"
        draft = await self.engine.access.get_obj(DocumentDraft, document_uuid=document_uuid)
        # 当前仅接口会带timestamp，其他地方不会带
        if draft and timestamp and draft.timestamp.timestamp() > timestamp:
            return json(LDR(IDECode.DOCUMENT_VERSION_LOWER))
        await self.save_draft(document_uuid, document_content)
        # 如果在worker里则直接执行
        in_celery = get_engine_in_worker()
        in_async_celery = os.environ.get("LEMON_ASYNC_TASK") == '1'
        if not force_in_current_proc and self.engine.app.config.USE_CELERY and (force_run_in_pool or (not in_celery)):
            check_in_pool_kwargs = dict(ignore_version=True, use_db_data=False)
            check_in_pool_kwargs.update(kwargs)
            res_id = await push_async_task(check_document_content.__module__,
                                           check_document_content.__name__,
                                           app_uuid,
                                           f"doc_check:{document_uuid}",
                                           args=(self, json_data, document_type),
                                           kwargs=check_in_pool_kwargs,
                                           priority=5,
                                           engine=self.engine)
            if (in_celery and not in_async_celery) or is_import:
                res = res_id
            else:
                if with_result and res_id:
                    tr = TaskResult(uid=res_id, engine=self.engine)
                    timeout = 1000
                    while timeout:
                        done = await tr.done()
                        if done:
                            res = tr.result.result
                            break
                        await asyncio.sleep(0.1)
                        timeout -= 1
        else:
            res = await check_document_content(self, json_data, document_type, **kwargs)
        if backup:
            # todo: check成功才backup
            if self.engine.app.config.USE_CELERY:
                print(app_uuid, document_uuid, document_content)
                self.create_content_backup.delay(self, app_uuid, document_uuid, document_content)
            else:
                await self.backup_async(app_uuid, document_uuid, document_content)
        return res

    async def _check_async(self, json_data, document_type, json_return=False,
                           check_updata=False, with_init=True, check_references=True,
                           ignore_version=False, use_db_data=False, force_update_content=True):
        if with_init:
            init_result = await self.init_check_args(
                json_data, document_type, json_return, check_update=check_updata)
            if init_result and json_return:
                return init_result
        if use_db_data:
            document_content = await self.access.get_document_draft_by_document_uuid(self.document_uuid)
            if document_content:
                self.document_content = document_content.document_content
        source_document = await self.access.get_document_by_document_uuid(self.document_uuid)
        if ignore_version and source_document:
            # 上一个用户修改还没commit，下一个用户修改获取到的是旧的version
            self.document_version = source_document.document_version
        self.new_ext_doc = False
        self.document = source_document

        if json_return:
            if not self.document:
                return json(LDR(IDECode.DOCUMENT_NOT_EXISTS))
            if self.document_version <= self.document.document_version:
                return json(LDR(IDECode.DOCUMENT_VERSION_LOWER))

            if self.document.document_type != document_type:
                return json(LDR(IDECode.DOCUMENT_TYPE_INCURRECT))
        self.document_type = document_type
        self.module = await self.engine.access.get_module_by_module_uuid(self.module_uuid)
        if self.module:
            self.app_uuid = self.module.app_uuid
            self.module_name = self.module.module_name
        else:
            self.module_name = None
        if source_document:
            self.document_name = self.document.document_name
            self.document_version = self.document.document_version
            self.document_number = self.document.document_number
        else:
            self.document_name = self.document_content.get("name", "")
            self.document_version = self.document_version
            self.document_number = 0
        app_log.debug(f"module name: {self.module_name}")
        app_log.debug(f"document name: {self.document_name}")

        # 此处调用 文档检查
        document_checker_service = await self.check_document()
        document_checker_service.is_copy = self.is_copy
        # 处理 文档 增删改查
        try:
            document_checker_service.check_all()
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError):
            app_log.error(f"check document error: {traceback.format_exc()}")
        except Exception:
            app_log.error(f"check document error: {traceback.format_exc()}")
            document_checker_service._add_error_list(attr=self.document_name, return_code=LDEC.ERROR)
        if self.is_copy:
            self.update_page_button(document_checker_service)
        await self.check_commit(document_checker_service, force_update_content=force_update_content)

        if self.engine.app.config.USE_CELERY and check_references:
            cache_tasks = LemonContextVar.atomic_cache_tasks.get()
            cache_tasks.append(asyncio.ensure_future(self.check_document_reference_list(self.app_uuid, self.document_uuid)))
        if self.is_copy:
            return json(LDR(data=self.document_content))
        if json_return:
            if check_updata:
                return json(LDR(updata_data=document_checker_service.element.get(
                    "children", list()), data=document_checker_service.error_list))
            else:
                return json(LDR(data=document_checker_service.error_list))
        else:
            return document_checker_service.error_list

    def update_page_button(self, document_checker_service):
        buttons_list = self.document_content.get("buttons", [])
        component_copy_dict = document_checker_service.component_copy_dict
        for button in buttons_list:
            click_list = button.get("events", {}).get("click", [])
            for click in click_list:
                if click.get("action") == EventAction.SUBMIT:
                    component_uuid = click.get("component")
                    new_component_uuid = component_copy_dict.get(component_uuid)
                    if new_component_uuid:
                        click.update({"component": new_component_uuid})

    async def check_document_reference_list(self, app_uuid: str, document_uuid: str):
        reference_list = await self.engine.access.list_document_by_link_uuid(
            document_uuid, as_dict=False)
        if not reference_list:
            return
        to_check_list = set(reference.document_uuid for reference in reference_list)
        # 排除自己引用自己
        to_check_list.discard(document_uuid)
        all_document = await self.engine.access.list_document_content_by_document_uuid_list(list(to_check_list))
        await self.engine.service.document_check.check_all_document2(app_uuid, all_document,
                                                                     check_commit=True,
                                                                     gather=self.engine.app.config.USE_CELERY)

    async def update_document_content(self, doc_content):
        document_content_create_data = {
            DocumentContent.document_uuid.name: self.document_uuid,
            DocumentContent.document_content.name: doc_content
        }
        document_content_update_data = {
            DocumentContent.document_content.name: doc_content
        }
        need_update = True
        if self.document_version == 0:
            obj = await self.engine.access.get_obj(DocumentContent, document_uuid=self.document_uuid)
            if not obj:
                await self.engine.access.create_document_content(**document_content_create_data)
                need_update = False
        if need_update:
            await self.engine.access.update_document_content_by_document_uuid(
                self.document_uuid, **document_content_update_data)

    def update_check_message(self, create_data: dict, update_data: dict, is_update, checker_service):
        error_list = checker_service.error_list
        error = True if error_list else False
        warning_list = checker_service.warning_list
        warning = True if warning_list else False
        info_list = checker_service.info_list
        info = True if info_list else False
        need_update = update_data if is_update else create_data
        need_update.update({
                CheckMessage.error.name: error, 
                CheckMessage.error_message.name: error_list,
                CheckMessage.warning.name: warning,
                CheckMessage.warning_message.name: warning_list,
                CheckMessage.info.name: info,
                CheckMessage.info_message.name: info_list
            })


class ModelCheckService(BaseCheckService):

    def checker_service(self):
        self.app_enum_uuid_set, self.app_enum_value_uuid_set = self.process_enum_list(self.app_enum_list)
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)
        document_checker_service = ModelDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_model_list=self.app_model_list, app_relationship_list=self.app_relationship_list,
            app_field_list=self.app_field_list, app_index_list=self.app_index_list,
            app_enum_uuid_set=self.app_enum_uuid_set, app_enum_value_uuid_set=self.app_enum_value_uuid_set,
            ext_tenant=self.ext_tenant, new_ext_doc=self.new_ext_doc,
            app_func_uuid_dict=self.app_func_uuid_dict, app_enum_list=self.app_enum_list,
            system_relationship_backref=self.system_relationship_backref,
            other_conflict_document_names_set=self.other_conflict_document_names_set
            # graph_depend_model=self.graph_depend_model
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_model_list, app_relationship_list, app_field_list, app_index_list, app_enum_list, app_func_list, \
            system_relationship_backref, module_workflow_list, module_const_list = data_list
        self.app_model_list = list(app_model_list)
        self.app_relationship_list = list(app_relationship_list)
        self.app_field_list = list(app_field_list)
        self.app_index_list = list(app_index_list)
        self.app_enum_list = list(app_enum_list)
        self.app_func_list = list(app_func_list)
        self.system_relationship_backref = system_relationship_backref
        self.other_conflict_document_names_set = self.process_other_conflict_document_names(
            [], module_workflow_list, app_func_list, app_enum_list, module_const_list
        )
        # self.graph_depend_model = depend_models  # TODO 这儿用的是数据库的表, 不符合文档检查的要求, 先这样吧
        # app_log.debug(f"app model list: {self.app_model_list}")

    async def collect_app_data_func(self):
        app_model_list = await self.engine.access.list_model_by_app_uuid(
            self.app_uuid, need_delete=True, with_sys=True, ext_tenant=self.ext_tenant)
        app_relationship_list = await self.engine.access.list_relationship_by_app_uuid(self.app_uuid, need_delete=True)
        app_field_list = await self.engine.access.list_field_by_app_uuid(
            self.app_uuid, need_delete=True, with_sys=True, ext_tenant=self.ext_tenant, need_hide=True)
        app_index_list = await self.engine.access.list_index_by_app_uuid(self.app_uuid, need_delete=True)
        app_enum_list = await self.engine.access.list_enum_by_app_uuid(self.app_uuid, need_delete=False, with_sys=True)
        app_func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, with_sys=True)
        system_relationship = await self.engine.access.list_sys_relationships_by_app_uuid(self.app_uuid)
        module_workflow_list = await self.engine.access.list_workflow_by_module_uuid(self.module_uuid)
        module_const_list = await self.engine.access.list_const_by_module_uuid(self.module_uuid)

        system_relationship_backref = {}
        for relationship in system_relationship:
            model_uuid = relationship.get("target_model")
            relationship_uuid = relationship.get("relationship_uuid")
            relationship_name = relationship.get("relationship_name")
            module_name = relationship.get("module_name")
            backref = relationship.get("backref")
            if backref is not None:
                model_dict = system_relationship_backref.setdefault(model_uuid, {})
                r = "/".join([module_name, "数据模型", relationship_name])
                relationship_dict = {relationship_uuid: r}
                backref_dict = model_dict.setdefault(backref, {})
                backref_dict.update(relationship_dict)

        return app_model_list, app_relationship_list, app_field_list, app_index_list, app_enum_list, app_func_list, \
            system_relationship_backref, module_workflow_list, module_const_list

    async def check_document(self) -> ModelDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class StateMachineCheckService(BaseCheckService):

    def checker_service(self):
        self.app_model_uuid_set = self.process_model_list(self.app_model_list)
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)
        self.app_const_uuid_set = self.process_const_list(self.app_const_list)
        self.app_enum_uuid_set, _ = self.process_enum_list(self.app_enum_list)

        # TODO: app_user_role_uuid_set 要从数据库取出来

        document_checker_service = StateMachineDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_sm_list=self.app_sm_list, app_event_list=self.app_event_list, app_state_list=self.app_state_list,
            app_model_uuid_set=self.app_model_uuid_set, app_func_uuid_dict=self.app_func_uuid_dict, 
            app_page_uuid_set=set(), app_const_uuid_set=self.app_const_uuid_set, 
            app_enum_uuid_set=self.app_enum_uuid_set, app_user_role_uuid_set=set()
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        sm_list, model_list, func_list, event_list, state_list, const_list, enum_list = data_list
        self.app_sm_list = list(sm_list)
        self.app_model_list = list(model_list)
        self.app_func_list = list(func_list)
        self.app_event_list = list(event_list)
        self.app_state_list = list(state_list)
        self.app_const_list = list(const_list)
        self.app_enum_list = list(enum_list)
        app_log.debug(f"app sm list: {self.app_sm_list}")

    async def collect_app_data_func(self):
        model_fields = [ModelBasic.model_uuid]
        func_fields = [Func.func_uuid, Func.arg_list, Func.return_list]
        sm_list = await self.engine.access.list_sm_by_app_uuid(self.app_uuid, need_delete=True)
        model_list = await self.engine.access.list_model_by_app_uuid(self.app_uuid, fields=model_fields)
        func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, fields=func_fields, with_sys=True)
        event_list = await self.engine.access.list_event_by_app_uuid(self.app_uuid, with_sys=True, need_delete=True)
        state_list = await self.engine.access.list_state_by_app_uuid(self.app_uuid, need_delete=True)
        const_list = await self.engine.access.list_const_by_app_uuid(self.app_uuid, with_sys=True)
        enum_list = await self.engine.access.list_enum_by_app_uuid(self.app_uuid, with_sys=True)
        return sm_list, model_list, func_list, event_list, state_list, const_list, enum_list

    async def check_document(self) -> StateMachineDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class PageCheckService(BaseCheckService):

    def checker_service(self):
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)
        document_checker_service = PageDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_page_list=self.app_page_list, app_field_list=self.app_field_list, 
            app_model_list=self.app_model_list, app_relationship_list=self.app_relationship_list, 
            ext_tenant=self.ext_tenant, new_ext_doc=self.new_ext_doc, 
            app_workflow_list=self.app_workflow_list, app_label_print_dict=self.app_label_print_dict, 
            modules_role=self.modules_role,
            is_copy=self.is_copy, app_func_uuid_dict=self.app_func_uuid_dict,
            app_url_dict=self.app_url_dict, app_restful_dict=self.app_restful_dict,
            module_tag_dict=self.module_tag_dict, app_excel_template_dict=self.app_excel_template_dict
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        page_list, field_list, model_list, relationship_list, workflow_list, func_list, modules_role, url_list, \
            restful_list, module_tag_dict, app_excel_template_dict, label_print_dict = data_list
        relationship_list = list(relationship_list)
        r_list = list_system_relationship(model_list, basic_data=True)
        relationship_list.extend(r_list)

        self.app_page_list = list(page_list)
        self.app_field_list = list(field_list)
        self.app_model_list = list(model_list)
        self.app_relationship_list = relationship_list
        self.app_workflow_list = list(workflow_list)
        self.app_func_list = list(func_list)
        self.app_label_print_dict = label_print_dict
        self.modules_role = modules_role
        app_url_dict = {
            url_info.get("custom_path", {}).get("path"): url_info.get("page_uuid")
            for url_info in url_list}
        app_restful_dict = dict()
        for restful_info in restful_list:
            restful_uuid = restful_info.get("restful_uuid")
            version_list = restful_info.get("children")
            auth_type = restful_info.get("auth_type")
            versions = dict()
            app_restful_dict.update({
                restful_uuid: {"auth_type": auth_type, "versions": versions}})
            for version_info in version_list:
                version = version_info.get("version")
                resource_list = version_info.get("children")
                resources = dict()
                versions.update({version: {"resources": resources}})
                for resource_info in resource_list:
                    resource = resource_info.get("resource")
                    request_list = resource_info.get("children")
                    methods = list()
                    resources.update({resource: {"methods": methods}})
                    for request_info in request_list:
                        method = request_info.get("method")
                        methods.append(method)
        self.app_url_dict = app_url_dict
        self.app_restful_dict = app_restful_dict
        self.module_tag_dict = module_tag_dict
        self.app_excel_template_dict = app_excel_template_dict

    async def collect_app_data_func(self):
        func_fields = [Func.func_uuid, Func.arg_list, Func.return_list]
        page_list = await self.engine.access.list_page_by_app_uuid(
            self.app_uuid, need_delete=True, ext_tenant=self.ext_tenant)
        field_list = await self.engine.access.list_field_by_app_uuid(
            self.app_uuid, with_sys=True,
            ext_tenant=self.ext_tenant, need_sys=True)
        model_list = await self.engine.access.list_model_by_app_uuid(
            self.app_uuid, with_sys=True, ext_tenant=self.ext_tenant)
        relationship_list = await self.engine.access.list_relationship_by_app_uuid(
            self.app_uuid, with_sys=True)
        workflow_list = await self.engine.access.list_workflow_by_app_uuid(
            self.app_uuid)
        func_list = await self.engine.access.list_func_by_app_uuid(
            self.app_uuid, fields=func_fields, with_sys=True)
        modules_role_list = await self.engine.access.list_module_role_by_app_uuid(self.app_uuid)
        url_setting_list = await self.engine.access.list_page_url_by_app_uuid(
            self.app_uuid)
        restful_list = await self.engine.access.list_restful_request_by_app_uuid(
            self.app_uuid, basic_auth=False)
        excel_template_list = await self.engine.access.list_document_content_by_app_uuid_document_type(
            app_uuid=self.app_uuid, document_type=DocumentType.EXCEL_TEMPLATE)
        label_print_list = await self.engine.access.list_docuemnt_content_by_app_uuid(self.app_uuid)
        modules_role = dict()
        for r in modules_role_list:
            cur_module_role = modules_role.setdefault(r["module_uuid"], list())
            cur_module_role.append(r)
        app_tag = await self.engine.access.list_tag(self.app_uuid)
        module_tag_dict = dict()
        for tag in app_tag:
            module_uuid = tag.get("module_uuid")
            current_m = module_tag_dict.setdefault(module_uuid, dict())
            module_tag_list = list(filter(lambda x: x.get("module_uuid") == module_uuid, app_tag))
            for tag in module_tag_list:
                current_m.update({tag.get("tag_uuid"): tag})
        # execl导入导出模板
        app_excel_template_dict = dict()
        for excel_template in excel_template_list:
            document_content = excel_template.get("document_content", {})
            documnet_uuid = excel_template.get("document_uuid")
            model = document_content.get("data_source", {}).get("model")
            app_excel_template_dict.update({documnet_uuid: model})

        label_print_dict = dict()
        for label_print in label_print_list:
            label_uuid = label_print.get("label_uuid")
            label_print_dict.update({label_uuid: label_print})

        return page_list, field_list, model_list, relationship_list, workflow_list, func_list, modules_role, \
            url_setting_list, restful_list, module_tag_dict, app_excel_template_dict, label_print_dict

    async def check_document(self) -> PageDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class PrintCheckService(BaseCheckService):

    def checker_service(self):
        self.field_uuid_dict = dict()
        for field in self.app_field_list:
            field_uuid = field.get(ModelField.field_uuid.name)
            self.field_uuid_dict[field_uuid] = field

        document_checker_service = PrintDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_print_list=self.app_print_list, app_field_dict=self.field_uuid_dict, 
            ext_tenant=self.ext_tenant, new_ext_doc=self.new_ext_doc, is_copy=self.is_copy, 
            app_model_list=self.app_model_list, app_relationship_list=self.app_relationship_list
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        print_list, field_list, app_model_list,  app_relationship_list = data_list
        self.app_print_list = list(print_list)
        self.app_field_list = list(field_list)
        self.app_model_list = list(app_model_list)
        self.app_relationship_list = list(app_relationship_list)

    async def collect_app_data_func(self):
        print_list = await self.engine.access.list_print_by_app_uuid(
            self.app_uuid, need_delete=True, ext_tenant=self.ext_tenant)
        field_list = await self.engine.access.list_field_by_app_uuid(self.app_uuid, with_sys=True)
        app_model_list = await self.engine.access.list_model_by_app_uuid(
            self.app_uuid, need_delete=False, with_sys=True, ext_tenant=self.ext_tenant)
        app_relationship_list = await self.engine.access.list_relationship_by_app_uuid(self.app_uuid, need_delete=False)
        return print_list, field_list, app_model_list, app_relationship_list

    async def check_document(self) -> PrintDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class LabelPrintCheckService(BaseCheckService):

    def checker_service(self):
        self.field_uuid_dict = dict()
        for field in self.app_field_list:
            field_uuid = field.get(ModelField.field_uuid.name)
            self.field_uuid_dict[field_uuid] = field

        document_checker_service = LabelPrintDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_label_print_list=self.app_label_print_list, app_field_dict=self.field_uuid_dict, 
            ext_tenant=self.ext_tenant, new_ext_doc=self.new_ext_doc, is_copy=self.is_copy, 
            app_model_list=self.app_model_list, app_relationship_list=self.app_relationship_list
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        label_print_list, field_list, app_model_list,  app_relationship_list = data_list
        self.app_label_print_list = list(label_print_list)
        self.app_field_list = list(field_list)
        self.app_model_list = list(app_model_list)
        self.app_relationship_list = list(app_relationship_list)

    async def collect_app_data_func(self):
        label_print_list = await self.engine.access.list_label_print_by_app_uuid(
            self.app_uuid, need_delete=True, ext_tenant=self.ext_tenant)
        field_list = await self.engine.access.list_field_by_app_uuid(self.app_uuid, with_sys=True, need_sys=True)
        app_model_list = await self.engine.access.list_model_by_app_uuid(
            self.app_uuid, need_delete=False, with_sys=True, ext_tenant=self.ext_tenant)
        app_relationship_list = await self.engine.access.list_relationship_by_app_uuid(self.app_uuid, need_delete=False)
        return label_print_list, field_list, app_model_list, app_relationship_list

    async def check_document(self) -> LabelPrintDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class FuncCheckService(BaseCheckService):

    def checker_service(self):
        self.app_model_uuid_set = self.process_model_list(self.app_model_list)
        self.module_model_name_set = self.process_model_name_list(self.app_model_list, self.module_uuid)
        self.app_image_uuid_set = self.process_image_list(self.app_image_list)
        self.app_enum_uuid_set, _ = self.process_enum_list(self.app_enum_list)

        self.sm_tool_category_uuid_set = set()
        for category in self.category_list:
            category_uuid = category.get(ToolCategory.category_uuid.name)
            self.sm_tool_category_uuid_set.add(category_uuid)

        document_checker_service = FuncDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_func_list=self.app_func_list, app_model_uuid_set=self.app_model_uuid_set, 
            app_enum_uuid_set=self.app_enum_uuid_set, app_icon_uuid_set=set(), 
            app_image_uuid_set=self.app_image_uuid_set, sm_tool_category_uuid_set=self.sm_tool_category_uuid_set,
            module_model_name_set=self.module_model_name_set, 
            is_copy=self.is_copy,
            other_conflict_document_names_set=self.other_conflict_document_names_set
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_func_list, app_model_list, app_image_list, app_enum_list, sys_result, user_result, \
             module_workflow_list, module_const_list = data_list
        self.app_func_list = list(app_func_list)
        self.app_model_list = list(app_model_list)
        self.app_image_list = list(app_image_list)
        self.app_enum_list = list(app_enum_list)
        self.category_list = list()
        self.sys_list, self.user_list = list(sys_result), list(user_result)
        self.category_list.extend(self.sys_list)
        self.category_list.extend(self.user_list)
        self.other_conflict_document_names_set = self.process_other_conflict_document_names(
            app_model_list, module_workflow_list, [], app_enum_list, module_const_list
        )

    async def collect_app_data_func(self):
        model_fields = [ModelBasic.model_uuid, ModelBasic.module_uuid, ModelBasic.model_name]
        image_fields = [ImageTable.image_uuid, ImageTable.image_name]
        entity_type = EntityType.SM
        app_func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, need_delete=True)
        app_model_list = await self.engine.access.list_model_by_app_uuid(self.app_uuid, fields=model_fields)
        app_image_list = await self.engine.access.list_image_by_app_uuid(
            self.app_uuid, fields=image_fields, with_sys=True)
        app_enum_list = await self.engine.access.list_enum_by_app_uuid(self.app_uuid, with_sys=True)
        sys_result = await self.engine.access.list_tool_category_by_sys(entity_type)
        user_result = await self.engine.access.list_tool_category_by_user(self.app_uuid, entity_type)
        module_workflow_list = await self.engine.access.list_workflow_by_module_uuid(self.module_uuid)
        module_const_list = await self.engine.access.list_const_by_module_uuid(self.module_uuid)
        return app_func_list, app_model_list, app_image_list, app_enum_list, sys_result, user_result, \
            module_workflow_list, module_const_list

    async def check_document(self) -> FuncDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ImageCheckService(BaseCheckService):

    def checker_service(self):
        document_checker_service = ImageDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            image_storage_type=self.engine.config.IMAGE_STORAGE_TYPE,
            static_url_prefix=self.engine.config.STATIC_URL_PREFIX,
            app_image_list=self.app_image_list, is_copy=self.is_copy,
            document_number=self.document_number, ignore_name=False
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_image_list = data_list
        self.app_image_list = list(app_image_list)

    async def collect_app_data_func(self):
        app_image_list = await \
            self.engine.access.list_image_by_app_uuid(self.app_uuid, need_delete=True, need_theme=False)
        return app_image_list

    # async def get_model_data(self):
    #     app_image_list = await self.engine.access.list_image_by_app_uuid(self.app_uuid, need_delete=True)
    #     self.app_image_list = list(app_image_list)
    #     app_log.debug(f"app image list: {self.app_image_list}")

    async def check_document(self) -> ImageDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ConstCheckService(BaseCheckService):

    def checker_service(self):
        document_checker_service = ConstDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_const_list=self.app_const_list, is_copy=self.is_copy,
            document_number=self.document_number,
            other_conflict_document_names_set=self.other_conflict_document_names_set
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_const_list, module_model_list, module_workflow_list, module_func_list, module_enum_list = data_list
        self.app_const_list = list(app_const_list)
        other_conflict_document_names_set = self.process_other_conflict_document_names(
            module_model_list, module_workflow_list, module_func_list, module_enum_list, [])
        self.other_conflict_document_names_set = other_conflict_document_names_set
        app_log.debug(f"app const list: {self.app_const_list}")

    async def collect_app_data_func(self):
        app_const_list = await self.engine.access.list_const_by_app_uuid(self.app_uuid, need_delete=True)
        module_model_list = await self.engine.access.list_model_by_module_uuid(
            self.module_uuid, ext_tenant=self.ext_tenant)
        module_func_list = await self.engine.access.list_func_by_module_uuid(self.module_uuid)
        module_workflow_list = await self.engine.access.list_workflow_by_module_uuid(self.module_uuid)
        module_enum_list = await self.engine.access.list_enum_by_module_uuid(self.module_uuid)

        return app_const_list, module_model_list, module_workflow_list, module_func_list, module_enum_list

    # async def get_model_data(self):
    #     app_const_list = await self.engine.access.list_const_by_app_uuid(self.app_uuid, need_delete=True)
    #     self.app_const_list = list(app_const_list)
    #     app_log.debug(f"app const list: {self.app_const_list}")
    
    async def check_document(self) -> ConstDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class JsonCheckService(BaseCheckService):

    def checker_service(self):
        document_checker_service = JsonDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_json_list=self.app_json_list, is_copy=self.is_copy,
            document_number=self.document_number
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_json_list = data_list
        self.app_json_list = list(app_json_list)
        app_log.debug(f"app const list: {self.app_json_list}")

    async def collect_app_data_func(self):
        app_json_list = await self.engine.access.list_json_by_app_uuid(self.app_uuid, need_delete=True)
        return app_json_list

    async def check_document(self) -> JsonDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class EnumCheckService(BaseCheckService):

    def checker_service(self):
        self.app_image_uuid_set = self.process_image_list(self.app_image_list)
        document_checker_service = EnumDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_enum_list=self.app_enum_list, app_icon_uuid_set=set(),
            app_image_uuid_set=self.app_image_uuid_set, is_copy=self.is_copy,
            document_number=self.document_number, app_model_list=self.app_model_list,
            other_conflict_document_names_set=self.other_conflict_document_names_set
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_enum_list, app_image_list, app_model_list, \
            module_model_list, module_func_list, module_workflow_list, module_const_list = data_list
        self.app_enum_list = list(app_enum_list)
        self.app_image_list = list(app_image_list)
        self.app_model_list = list(app_model_list)
        other_conflict_document_names_set = self.process_other_conflict_document_names(
            module_model_list, module_workflow_list, module_func_list, [], module_const_list)
        self.other_conflict_document_names_set = other_conflict_document_names_set
        app_log.debug(f"app enum list: {self.app_enum_list}")

    async def collect_app_data_func(self):
        image_fields = [ImageTable.image_uuid, ImageTable.image_name]
        app_enum_list = await self.engine.access.list_enum_by_app_uuid(self.app_uuid, need_delete=True)
        app_image_list = await self.engine.access.list_image_by_app_uuid(
            self.app_uuid, fields=image_fields, with_sys=True)
        app_model_list = await self.engine.access.list_model_by_app_uuid(
                self.app_uuid, need_delete=False, with_sys=True, ext_tenant=self.ext_tenant)
        module_model_list = await self.engine.access.list_model_by_module_uuid(
            self.module_uuid, ext_tenant=self.ext_tenant)
        module_func_list = await self.engine.access.list_func_by_module_uuid(self.module_uuid)
        module_workflow_list = await self.engine.access.list_workflow_by_module_uuid(self.module_uuid)
        module_const_list = await self.engine.access.list_const_by_module_uuid(self.module_uuid)
        return app_enum_list, app_image_list, app_model_list, \
            module_model_list, module_func_list, module_workflow_list, module_const_list

    async def check_document(self) -> EnumDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class SecurityCheckService(BaseCheckService):

    def checker_service(self):
        document_checker_service = SecurityDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_module_role_list=self.app_module_role_list, module_model_list=self.module_model_list,
            module_page_list=self.module_page_list, module_func_list=self.module_func_list,
            ext_tenant=self.ext_tenant, self_associations=self.self_associations
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_enum_list, app_image_list = data_list
        self.app_enum_list = list(app_enum_list)
        self.app_image_list = list(app_image_list)
        app_log.debug(f"app enum list: {self.app_enum_list}")

    async def collect_app_data_func(self):
        image_fields = [ImageTable.image_uuid, ImageTable.image_name]
        app_enum_list = await self.engine.access.list_enum_by_app_uuid(self.app_uuid, need_delete=True)
        app_image_list = await self.engine.access.list_image_by_app_uuid(
            self.app_uuid, fields=image_fields, with_sys=True)
        return app_enum_list, app_image_list

    async def get_model_data(self, cache_dict=None):
        self.app_module_role_list = list(await self.engine.access.list_module_role_by_app_uuid(
            self.app_uuid, need_delete=True))
        app_log.debug(f"app_module_role_list: {self.app_module_role_list}")
        field_model_fields = [ModelField.field_name, ModelField.field_uuid, ModelField.model_uuid,
                              ModelField.module_uuid]
        module_model_list = await self.engine.access.list_model_by_module_uuid(
            self.module_uuid, ext_tenant=self.ext_tenant)
        module_page_list = await self.engine.access.list_page_by_module_uuid(
            self.module_uuid, ext_tenant=self.ext_tenant)
        module_func_list = await self.engine.access.list_func_by_module_uuid(self.module_uuid)
        model_document = await self.engine.access.list_document_content_by_app_uuid_document_type(
            app_uuid=self.app_uuid, document_type=DocumentType.MODEL)
        field_list = await self.engine.access.list_field_by_module_uuid(
            self.module_uuid, field_model_fields, ext_tenant=self.ext_tenant, need_sys=True)
        r_list = await self.engine.access.list_relationship_by_module_uuid(self.module_uuid)

        self.module_model_list = list(module_model_list)
        self.module_page_list = list(module_page_list)
        self.module_func_list = list(module_func_list)
        app_log.debug(f"module model list: {module_model_list}")
        app_log.debug(f"module page list: {module_page_list}")
        app_log.debug(f"module func list: {module_func_list}")
        model_dict = {}
        for model in module_model_list:
            model_uuid = model["model_uuid"]
            system_relationship_list = list_system_relationship(
                    [model])
            model["relations"] = system_relationship_list
            model["fields"] = []
            model_dict[model_uuid] = model
        for relationship in r_list:
            source_model_uuid = relationship.get(RelationshipBasic.source_model.name)
            target_model_uuid = relationship.get(RelationshipBasic.target_model.name)
            source_model = model_dict.get(source_model_uuid)
            target_model = model_dict.get(target_model_uuid)
            if source_model:
                source_model["relations"].append(relationship)
            if target_model:
                target_model["relations"].append(relationship)
        for field in field_list:
            model_uuid = field.get(ModelField.model_uuid.name)
            filed_model = model_dict.get(model_uuid)
            if filed_model:
                filed_model["fields"].append(field)

        self_associations = pre_process_self_associate(model_document)
        self.self_associations = self_associations

    async def check_document(self) -> DocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class RuleChainCheckService(BaseCheckService):

    def checker_service(self):
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)
        self.app_const_uuid_set = self.process_const_list(self.app_const_list)
        self.app_enum_uuid_set, _ = self.process_enum_list(self.app_enum_list)

        document_checker_service = RuleChainDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_rule_list=self.app_rule_list,
            app_func_uuid_dict=self.app_func_uuid_dict,
            app_page_uuid_set=set(), app_const_uuid_set=self.app_const_uuid_set,
            app_enum_uuid_set=self.app_enum_uuid_set, app_user_role_uuid_set=set(),
            document_list=self.document_list, is_copy=self.is_copy
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        rule_list, func_list, const_list, enum_list, document_list = data_list
        self.app_rule_list = list(rule_list)
        self.app_model_list = list()
        self.app_func_list = list(func_list)
        self.app_const_list = list(const_list)
        self.app_enum_list = list(enum_list)
        self.document_list = list(document_list)
        app_log.debug(f"app rule list: {self.app_rule_list}")

    async def collect_app_data_func(self):
        func_fields = [Func.func_uuid, Func.arg_list, Func.return_list]
        rule_list = await self.engine.access.list_rulechain_by_app_uuid(self.app_uuid, need_delete=True)
        func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, fields=func_fields, with_sys=True)
        const_list = await self.engine.access.list_const_by_app_uuid(self.app_uuid, with_sys=True)
        enum_list = await self.engine.access.list_enum_by_app_uuid(self.app_uuid, with_sys=True)
        document_list = await self.engine.access.list_document_by_app_uuid_document_type(
            self.app_uuid, DocumentType.RULECHAIN)
        return rule_list, func_list, const_list, enum_list, document_list

    async def check_document(self) -> RuleChainDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class WorkflowCheckService(BaseCheckService):

    def checker_service(self):
        self.app_model_uuid_set = self.process_model_list(self.app_model_list)
        self.app_model_dict = {m["model_uuid"]: m for m in self.app_model_list}
        self.app_field_dict = {f["field_uuid"]: f for f in self.app_field_list}
        self.app_field_uuid_set = self.process_field_list(self.app_field_list)
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)
        self.app_const_uuid_set = self.process_const_list(self.app_const_list)
        self.app_enum_uuid_set, _ = self.process_enum_list(self.app_enum_list)
        self.app_relationship_uuid_set = self.process_relationship_list(self.relationship_list)

        document_checker_service = WorkflowDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_workflow_list=self.app_workflow_list, app_model_dict=self.app_model_dict,
            app_field_dict=self.app_field_dict, app_func_uuid_dict=self.app_func_uuid_dict,
            app_page_list=self.app_page_list, app_const_uuid_set=self.app_const_uuid_set,
            app_enum_uuid_set=self.app_enum_uuid_set, app_user_role_uuid_set=self.app_user_role_uuid_set,
            modules_role=self.modules_role, app_relationship_uuid_set=self.app_relationship_uuid_set,
            relationship_list=self.relationship_list, is_copy=self.is_copy,
            module_roles_user_related=self.module_roles_user_related,
            other_conflict_document_names_set=self.other_conflict_document_names_set
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        workflow_list, model_list, field_list, func_list, const_list, enum_list, page_list, role_list, \
            modules_role, relationship_list, module_roles_user_related = data_list
        relationship_list = list(relationship_list)
        r_list = list_system_relationship(model_list, basic_data=True)
        relationship_list.extend(r_list)

        self.app_workflow_list = list(workflow_list)
        self.app_model_list = list(model_list)
        self.app_field_list = list(field_list)
        self.app_func_list = list(func_list)
        self.app_const_list = list(const_list)
        self.app_enum_list = list(enum_list)
        self.app_page_list = list(page_list)
        self.app_user_role_uuid_set = {r["role_uuid"] for r in role_list}
        self.modules_role = modules_role
        self.relationship_list = relationship_list
        self.module_roles_user_related = module_roles_user_related
        self.other_conflict_document_names_set = self.process_other_conflict_document_names(
            model_list, [], func_list, enum_list, const_list
        )
        app_log.debug(f"app_workflow_list: {self.app_workflow_list}")

    async def collect_app_data_func(self):
        model_fields = [ModelBasic.model_uuid, ModelBasic.model_name, ModelBasic.module_uuid]
        func_fields = [Func.func_uuid, Func.func_name, Func.arg_list, Func.return_list, Func.module_uuid]
        workflow_list = await self.engine.access.list_workflow_by_app_uuid(self.app_uuid, need_delete=True)
        model_list = await self.engine.access.list_model_by_app_uuid(self.app_uuid, fields=model_fields)
        field_list = await self.engine.access.list_field_by_app_uuid(self.app_uuid, with_sys=True, need_sys=True)
        func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, fields=func_fields, with_sys=True)
        const_list = await self.engine.access.list_const_by_app_uuid(self.app_uuid, with_sys=True)
        enum_list = await self.engine.access.list_enum_by_app_uuid(self.app_uuid, with_sys=True)
        page_list = await self.engine.access.list_page_by_app_uuid(self.app_uuid, need_delete=True)
        role_list = await self.engine.access.list_user_role_by_app_uuid(self.app_uuid)
        modules_role_list = await self.engine.access.list_module_role_by_app_uuid(self.app_uuid)
        relationship_list = await self.engine.access.list_relationship_by_app_uuid(self.app_uuid, with_sys=True)
        modules_role = dict()
        for r in modules_role_list:
            cur_module_role = modules_role.setdefault(r["module_uuid"], list())
            cur_module_role.append(r)

        module_roles_user_related = dict()
        role_content_list = await self.engine.access.list_app_user_role_content(self.app_uuid, need_delete=True)
        for role_content in role_content_list:
            for module_role in role_content["module_role"]:
                if module_role["uuid"] in module_roles_user_related:
                    module_roles_user_related[module_role["uuid"]].append(role_content["user_role"])
                else:
                    module_roles_user_related[module_role["uuid"]] = [role_content["user_role"]]

        return workflow_list, model_list, field_list, func_list, const_list, enum_list, page_list, role_list, \
            modules_role, relationship_list, module_roles_user_related

    async def check_document(self) -> WorkflowDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ConnectorCheckService(BaseCheckService):

    def checker_service(self):
        self.app_model_uuid_set = self.process_model_list(self.app_model_list)
        self.app_field_uuid_set = self.process_field_list(self.app_field_list)
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)

        document_checker_service = ConnectorDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_connector_list=self.app_connector_list, app_model_uuid_set=self.app_model_uuid_set,
            app_field_uuid_set=self.app_field_uuid_set, app_func_uuid_dict=self.app_func_uuid_dict,
            app_page_list=self.app_page_list,  app_user_role_uuid_set=set(),
            is_copy=self.is_copy
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        connector_list, model_list, field_list, func_list, page_list = data_list
        self.app_connector_list = list(connector_list)
        self.app_model_list = list(model_list)
        self.app_field_list = list(field_list)
        self.app_func_list = list(func_list)
        self.app_page_list = list(page_list)
        app_log.debug(f"app_connector_list: {self.app_connector_list}")

    async def collect_app_data_func(self):
        model_fields = [ModelBasic.model_uuid]
        func_fields = [Func.func_uuid, Func.arg_list, Func.return_list]
        connector_list = await self.engine.access.list_connector_by_app_uuid(self.app_uuid, need_delete=True)
        model_list = await self.engine.access.list_model_by_app_uuid(self.app_uuid, fields=model_fields)
        field_list = await self.engine.access.list_field_by_app_uuid(self.app_uuid, with_sys=True)
        func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, fields=func_fields, with_sys=True)
        page_list = await self.engine.access.list_page_by_app_uuid(self.app_uuid)
        return connector_list, model_list, field_list, func_list, page_list

    async def check_document(self) -> ConnectorDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ExcelTemplateCheckService(BaseCheckService):

    def checker_service(self):
        app_field_dict = self.trans_field_list_to_dict(self.app_field_list)
        app_model_dict = self.trans_model_list_to_dict(self.app_model_list)
        self.app_model_uuid_set = set(app_model_dict.keys())
        self.app_field_uuid_set = set(app_field_dict.keys())
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)

        document_checker_service = ExcelTemplateDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_template_list=self.app_template_list, app_model_uuid_set=self.app_model_uuid_set,
            app_field_uuid_set=self.app_field_uuid_set, app_func_uuid_dict=self.app_func_uuid_dict,
            app_page_list=self.app_page_list,  app_user_role_uuid_set=set(), app_field_dict=app_field_dict,
            app_model_dict=app_model_dict,
            is_copy=self.is_copy
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_template_list, model_list, field_list, func_list, page_list = data_list
        self.app_template_list = list(app_template_list)
        self.app_model_list = list(model_list)
        self.app_field_list = list(field_list)
        self.app_func_list = list(func_list)
        self.app_page_list = list(page_list)

    async def collect_app_data_func(self):
        model_fields = [ModelBasic.model_uuid]
        func_fields = [Func.func_uuid, Func.arg_list, Func.return_list]
        app_template_list = await self.engine.access.list_excel_template_by_app_uuid(self.app_uuid, need_delete=True)
        model_list = await self.engine.access.list_model_by_app_uuid(self.app_uuid, fields=model_fields, with_sys=True)
        field_list = await self.engine.access.list_field_by_app_uuid(self.app_uuid, with_sys=True)
        func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, fields=func_fields, with_sys=True)
        page_list = await self.engine.access.list_page_by_app_uuid(self.app_uuid)
        return app_template_list, model_list, field_list, func_list, page_list

    async def check_document(self) -> ExcelTemplateDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ExportTemplateCheckService(BaseCheckService):

    def checker_service(self):
        app_field_dict = self.trans_field_list_to_dict(self.app_field_list)
        app_model_dict = self.trans_model_list_to_dict(self.app_model_list)
        self.app_model_uuid_set = set(app_model_dict.keys())
        self.app_field_uuid_set = set(app_field_dict.keys())
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)

        document_checker_service = ExportTemplateDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_template_list=self.app_template_list, app_model_uuid_set=self.app_model_uuid_set, 
            app_field_uuid_set=self.app_field_uuid_set, app_func_uuid_dict=self.app_func_uuid_dict, 
            app_page_list=self.app_page_list,  app_user_role_uuid_set=set(), app_field_dict=app_field_dict,
            app_model_dict=app_model_dict,
            is_copy=self.is_copy
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_template_list, model_list,  page_list = data_list
        field_list, func_list = [], []
        self.app_template_list = list(app_template_list)
        self.app_model_list = list(model_list)
        self.app_field_list = list(field_list)
        self.app_func_list = list(func_list)
        self.app_page_list = list(page_list)

    async def collect_app_data_func(self):
        model_fields = [ModelBasic.model_uuid]
        # func_fields = [Func.func_uuid, Func.arg_list, Func.return_list]
        app_template_list = await self.engine.access.list_export_template_by_app_uuid(self.app_uuid, need_delete=True)
        model_list = await self.engine.access.list_model_by_app_uuid(self.app_uuid, fields=model_fields, with_sys=True)
        page_list = await self.engine.access.list_page_by_app_uuid(self.app_uuid)
        return app_template_list, model_list, page_list

    async def check_document(self) -> ExcelTemplateDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ApprovalFlowCheckService(BaseCheckService):

    def checker_service(self):
        self.app_model_uuid_set = self.process_model_list(self.app_model_list)
        self.app_field_uuid_set = self.process_field_list(self.app_field_list)
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)
        self.app_const_uuid_set = self.process_const_list(self.app_const_list)
        self.app_enum_uuid_set, _ = self.process_enum_list(self.app_enum_list)
        self.app_relationship_uuid_set = self.process_relationship_list(self.relationship_list)

        document_checker_service = ApprovalFlowDocumentCheckService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_workflow_list=self.app_workflow_list, app_model_uuid_set=self.app_model_uuid_set,
            app_field_uuid_set=self.app_field_uuid_set, app_func_uuid_dict=self.app_func_uuid_dict,
            app_page_list=self.app_page_list, app_const_uuid_set=self.app_const_uuid_set,
            app_enum_uuid_set=self.app_enum_uuid_set, app_user_role_uuid_set=self.app_user_role_uuid_set,
            modules_role=self.modules_role, app_relationship_uuid_set=self.app_relationship_uuid_set,
            is_copy=self.is_copy
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        workflow_list, model_list, field_list, func_list, const_list, enum_list, page_list, role_list, \
            modules_role, relationship_list = data_list
        self.app_workflow_list = list(workflow_list)
        self.app_model_list = list(model_list)
        self.app_field_list = list(field_list)
        self.app_func_list = list(func_list)
        self.app_const_list = list(const_list)
        self.app_enum_list = list(enum_list)
        self.app_page_list = list(page_list)
        self.app_user_role_uuid_set = {r["role_uuid"] for r in role_list}
        self.modules_role = modules_role
        self.relationship_list = list(relationship_list)

    async def collect_app_data_func(self):
        model_fields = [ModelBasic.model_uuid]
        func_fields = [Func.func_uuid, Func.arg_list, Func.return_list]
        workflow_list = await self.engine.access.list_workflow_by_app_uuid(self.app_uuid, need_delete=True)
        model_list = await self.engine.access.list_model_by_app_uuid(self.app_uuid, fields=model_fields)
        field_list = await self.engine.access.list_field_by_app_uuid(self.app_uuid, with_sys=True, need_sys=True)
        func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, fields=func_fields, with_sys=True)
        const_list = await self.engine.access.list_const_by_app_uuid(self.app_uuid, with_sys=True)
        enum_list = await self.engine.access.list_enum_by_app_uuid(self.app_uuid, with_sys=True)
        page_list = await self.engine.access.list_page_by_app_uuid(self.app_uuid)
        role_list = await self.engine.access.list_user_role_by_app_uuid(self.app_uuid)
        modules_role_list = await self.engine.access.list_module_role_by_app_uuid(self.app_uuid)
        relationship_list = await self.engine.access.list_relationship_by_app_uuid(self.app_uuid, with_sys=True)
        modules_role = dict()
        for r in modules_role_list:
            cur_module_role = modules_role.setdefault(r["module_uuid"], list())
            cur_module_role.append(r)
        return workflow_list, model_list, field_list, func_list, const_list, enum_list, page_list, role_list, modules_role, relationship_list

    async def check_document(self) -> ApprovalFlowDocumentCheckService:
        await self.get_model_data()
        return self.checker_service()


class ModuleDeployCheckService(BaseCheckService):
    
    def checker_service(self):

        document_checker_service = ModuleDeployDocumentCheckService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name, 
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version
        )
        return document_checker_service
    
    async def collect_app_data(self, data_list):
        ...
    
    async def collect_app_data_func(self):
        return None
    
    async def check_document(self) -> ModuleDeployDocumentCheckService:
        await self.get_model_data()
        return self.checker_service()


class RestfulCheckService(BaseCheckService):

    def checker_service(self) -> RestfulDocumentCheckerService:
        self.app_func_uuid_dict = self.process_func_list(self.app_func_list)
        document_checker_service = RestfulDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_func_uuid_dict=self.app_func_uuid_dict,
            module_role_uuid_set=self.modules_role_uuid.get(self.module_uuid, set()),
            app_restful_list=self.app_restful_list
        )
        return document_checker_service
    
    async def collect_app_data(self, data_list):
        restful_list, func_list, modules_role = data_list
        self.app_restful_list = list(restful_list)
        self.app_func_list = list(func_list)
        self.modules_role_uuid = modules_role

    async def collect_app_data_func(self):
        func_fields = [Func.func_uuid, Func.arg_list, Func.return_list]
        restful_list = await self.engine.access.list_restful_by_app_uuid(self.app_uuid, need_delete=True)
        func_list = await self.engine.access.list_func_by_app_uuid(self.app_uuid, fields=func_fields, with_sys=True)
        role_list = await self.engine.access.list_module_role_by_app_uuid(self.app_uuid)
        modules_role = dict()
        for r in role_list:
            cur_module_role = modules_role.setdefault(r["module_uuid"], set())
            cur_module_role.add(r["role_uuid"])
        return restful_list, func_list, modules_role

    async def check_document(self) -> RestfulDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class NavigationCheckService(BaseCheckService):

    def checker_service(self) -> NavigationDocumentCheckerService:
        document_checker_service = NavigationDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            model=None, app_navigation_dict=self.app_navigation_dict,
            app_navigation_item_dict=self.app_navigation_item_dict, user_uuid=self.user_uuid,
            app_tag_dict=self.app_tag_dict
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_info, app_navigation_dict, app_navigation_item_dict, app_tag_dict = data_list
        self.app_navigation_dict = app_navigation_dict
        self.app_navigation_item_dict = app_navigation_item_dict
        self.user_uuid = app_info.user_uuid  # TODO 这里直接用了middle_user
        self.app_tag_dict = app_tag_dict

    async def collect_app_data_func(self):  # TODO 到底需要什么data
        app_info = await self.engine.access.get_app_by_app_uuid(self.app_uuid)
        app_navigation_list = await self.engine.access.list_navigation_by_app_uuid(self.app_uuid, need_delete=True)
        app_navigation_item_list = await self.engine.access.list_navigationitem(self.app_uuid, need_delete=True)
        app_navigation_dict = {x["navigation_uuid"]: x for x in app_navigation_list}
        app_navigation_item_dict = dict()
        for item in app_navigation_item_list:
            navigation_items = app_navigation_item_dict.setdefault(
                item["navigation_uuid"], list())
            navigation_items.append(item)
        app_tag = await self.engine.access.list_tag(self.app_uuid)
        app_tag_dict = {r["tag_uuid"]: r for r in app_tag}
        return app_info, app_navigation_dict, app_navigation_item_dict, app_tag_dict

    async def check_document(self) -> NavigationDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class APPSecurityCheckService(BaseCheckService):

    def checker_service(self) -> APPSecurityDocumentCheckerService:
        document_checker_service = APPSecurityDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            model=UserRole, app_module_map=self.app_module_map,
            user_role_list_exist=self.user_role_list_exist,
            app_user_role_content=self.app_user_role_content,
            app_module_role=self.app_module_role, app_setting=self.app_setting
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_module_map, user_role_list_exist, app_user_role_content, app_module_role, app_setting = data_list
        self.app_module_map = app_module_map
        self.user_role_list_exist = user_role_list_exist
        self.app_user_role_content = app_user_role_content
        self.app_module_role = app_module_role
        self.app_setting = app_setting

    async def collect_app_data_func(self):  # TODO 到底需要什么data
        app_module_list = await self.engine.access.list_module_by_app_uuid(app_uuid=self.app_uuid)
        user_role_list_exist = await self.engine.access.list_user_role_by_app_uuid(
            app_uuid=self.app_uuid, need_delete=True)
        app_user_role_content_list = await self.engine.access.list_app_user_role_content(
            app_uuid=self.app_uuid, need_delete=True)
        app_module_role_list = await self.engine.access.list_module_role_by_app_uuid(app_uuid=self.app_uuid)
        app_setting = await self.engine.access.get_app_setting(self.app_uuid)
        app_log.info(list(app_user_role_content_list))
        app_module_map = {module["module_name"]: module["module_uuid"] for module in app_module_list}
        user_role_list_exist = list(user_role_list_exist)
        app_user_role_content = dict()
        for c in app_user_role_content_list:
            contents = app_user_role_content.setdefault(
                c["user_role"], list())
            contents.append(c)
        app_module_role = dict()
        for m in app_module_role_list:
            contents = app_module_role.setdefault(m["module_uuid"], list())
            contents.append(m)
        return app_module_map, user_role_list_exist, app_user_role_content, app_module_role, app_setting

    async def check_document(self) -> APPSecurityDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ThemeCheckService(BaseCheckService):

    def checker_service(self) -> ThemeDocumentCheckerService:
        document_checker_service = ThemeDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            model=None, app_module_theme_dict=self.app_module_theme_dict,
            app_layout_dict=self.app_layout_dict
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_module_theme_dict, app_layout_dict = data_list
        self.app_module_theme_dict = app_module_theme_dict
        self.app_layout_dict = app_layout_dict

    async def collect_app_data_func(self):
        module_theme_list = list(await self.engine.access.list_module_theme_by_app_uuid_document_type(
            self.app_uuid, document_type=DocumentType.MODULE_THEME, as_dict=True, need_delete=False))
        app_module_theme_dict = {x["document_uuid"]: x for x in module_theme_list}

        app_layout_list = list(await self.engine.access.list_module_theme_by_app_uuid_document_type(
            self.app_uuid, document_type=DocumentType.APP_LAYOUT, as_dict=True, need_delete=False))
        app_layout_dict = {x["document_uuid"]: x for x in app_layout_list}
        return app_module_theme_dict, app_layout_dict

    async def check_document(self) -> ThemeDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ExpansionPackCheckService(BaseCheckService):

    def checker_service(self) -> ExpansionpackDocumentCheckerService:
        document_checker_service = ExpansionpackDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            model=None
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        return []

    async def collect_app_data_func(self):
        return None

    async def check_document(self) -> ExpansionpackDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class ThirdAuthCheckService(BaseCheckService):

    def checker_service(self) -> ThirdAuthDocumentCheckerService:
        document_checker_service = ThirdAuthDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            model=None,
        )
        return document_checker_service

    async def check_document(self) -> ThirdAuthDocumentCheckerService:
        return self.checker_service()

    async def collect_app_data_func(self):
        return None


class WatermarkCheckService(BaseCheckService):

    def checker_service(self) -> WatermarkDocumentCheckerService:
        document_checker_service = WatermarkDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            model=None,
        )
        return document_checker_service

    async def check_document(self) -> WatermarkDocumentCheckerService:
        return self.checker_service()

    async def collect_app_data_func(self):
        return None


class ModuleThemeCheckService(BaseCheckService):

    def checker_service(self) -> ModuleThemeDocumentCheckerService:
        document_checker_service = ModuleThemeDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            app_image_list=self.app_image_list, document_number=self.document_number
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        app_image_list = data_list
        self.app_image_list = list(app_image_list)

    async def collect_app_data_func(self):
        app_image_list = await self.engine.access.list_image_by_app_uuid(self.app_uuid, need_delete=True)
        return app_image_list

    async def check_document(self) -> ModuleThemeDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class PermissionConfigCheckService(BaseCheckService):

    def checker_service(self) -> PermissionConfigDocumentCheckerService:
        document_checker_service = PermissionConfigDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            module_resource_uuid_dict=self.module_resource_uuid_dict,
            module_tag_uuid_dict=self.module_tag_uuid_dict,
            module_resource_tag_dict=self.module_resource_tag_dict
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        module_resource_uuid_dict, module_tag_uuid_dict, module_resource_tag_dict = data_list
        self.module_resource_uuid_dict = module_resource_uuid_dict
        self.module_tag_uuid_dict = module_tag_uuid_dict
        self.module_resource_tag_dict = module_resource_tag_dict

    async def collect_app_data_func(self):
        module_resource_uuid_dict = dict()
        module_tag_uuid_dict = dict()
        module_resource_tag_dict = dict()
        app_resource_list = \
            await self.engine.access.list_resource_by_app_uuid(self.app_uuid, need_delete=True)
        for resource in app_resource_list:
            module_uuid = resource.get("module_uuid")
            resource_uuid = resource.get("resource_uuid")
            module_resource_uuid_dict.setdefault(module_uuid, dict())
            module_resource_uuid_dict.get(module_uuid, {}).update({resource_uuid: resource})

            tag_list = await self.engine.access.list_tag_by_resource_uuid(resource_uuid, need_delete=True)
            tag_uuid_list = list(tag.get("tag_uuid") for tag in tag_list)
            module_tag_uuid_dict.setdefault(module_uuid, list())
            module_tag_uuid_dict.get(module_uuid, []).extend(tag_uuid_list)

            module_resource_tag_dict.setdefault(resource_uuid, list())
            module_resource_tag_dict.get(resource_uuid, []).extend(tag_uuid_list)

        return module_resource_uuid_dict, module_tag_uuid_dict, module_resource_tag_dict

    async def check_document(self) -> PermissionConfigDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()


class AppLayoutCheckService(BaseCheckService):

    def checker_service(self) -> AppLayoutDocumentCheckerService:
        document_checker_service = AppLayoutDocumentCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        return []

    async def collect_app_data_func(self):
        return None

    async def check_document(self) -> AppLayoutDocumentCheckerService:
        await self.get_model_data()
        return self.checker_service()

class PyModuleCheckService(BaseCheckService):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def checker_service(self) -> CheckerService:
        document_checker_service = PyModuleCheckerService(
            app_uuid=self.app_uuid, module_uuid=self.module_uuid, module_name=self.module_name,
            document_uuid=self.document_uuid, document_name=self.document_name,
            element=self.document_content, document_version=self.document_version,
            module_funcs=self.module_funcs
        )
        return document_checker_service

    async def collect_app_data(self, data_list):
        # self.module_funcs = list(await self.engine.access.list_func_by_module_uuid(
        #     self.module_uuid,
        #     [Func.func_uuid, Func.module_uuid, Func.is_delete, Func.func_name,
        #      Func.document_uuid, Func.from_py_module, Func.py_module_line_no],
        #     need_delete=True
        # ))
        pass

    async def collect_app_data_func(self):
        return None


    async def check_document(self) -> CheckerService:
        await self.get_model_data()
        return self.checker_service()

class DocumentCheckService(IDEService):

    def __init__(self, engine):
        super().__init__(engine)
        self.document_class_dict = {
            Document.TYPE.MODEL: ModelCheckService,
            Document.TYPE.SM: StateMachineCheckService,
            Document.TYPE.FUNC: FuncCheckService,
            Document.TYPE.IMAGE: ImageCheckService,
            Document.TYPE.CONST: ConstCheckService,
            Document.TYPE.JSON: JsonCheckService,
            Document.TYPE.ENUM: EnumCheckService,
            Document.TYPE.PAGE: PageCheckService,
            Document.TYPE.SECURITY: SecurityCheckService,
            Document.TYPE.RULECHAIN: RuleChainCheckService,
            Document.TYPE.WORKFLOW: WorkflowCheckService,
            Document.TYPE.PRINT: PrintCheckService,
            Document.TYPE.LABEL_PRINT: LabelPrintCheckService,
            Document.TYPE.CONNECTOR: ConnectorCheckService,
            Document.TYPE.EXCEL_TEMPLATE: ExcelTemplateCheckService, 
            Document.TYPE.APPROVALFLOW: ApprovalFlowCheckService, 
            Document.TYPE.MODULE_DEPLOY: ModuleDeployCheckService,
            Document.TYPE.RESTFUL: RestfulCheckService, 
            Document.TYPE.NAVIGATION: NavigationCheckService, 
            Document.TYPE.APP_SECURITY: APPSecurityCheckService, 
            Document.TYPE.THEME: ThemeCheckService,
            Document.TYPE.DEPLOY_CONFIG: ThirdAuthCheckService,
            Document.TYPE.WATERMARK: WatermarkCheckService,
            Document.TYPE.EXPORT_TEMPLATE: ExportTemplateCheckService,
            Document.TYPE.MODULE_THEME: ModuleThemeCheckService,
            Document.TYPE.PERMISSION_CONFIG: PermissionConfigCheckService,
            Document.TYPE.EXPANSION_PACK: ExpansionPackCheckService,
            Document.TYPE.APP_LAYOUT: AppLayoutCheckService,
            Document.TYPE.PY_MODULE: PyModuleCheckService
        }

    def create_service(self, document_type) -> BaseCheckService:
        service_class = self.document_class_dict.get(document_type)
        if service_class:
            return service_class(self.engine)
        return None

    async def check_all_document(self, app_uuid, all_document, ext_tenant=None):
        model_list = await self.engine.access.list_model_by_app_uuid(app_uuid)
        func_list = await self.engine.access.list_func_by_app_uuid(app_uuid)
        page_list = await self.engine.access.list_page_by_app_uuid(app_uuid)
        relationship_list = await self.engine.access.list_relationship_by_app_uuid(app_uuid)
        field_list = await self.engine.access.list_field_by_app_uuid(app_uuid)
        role_list = await self.engine.access.list_module_role_by_app_uuid(app_uuid)

        module_page_dict, module_func_dict = dict(), dict()
        module_model_dict, model_field_dict = dict(), dict()
        source_relationship_dict, target_relationship_dict = dict(), dict()

        for model_uuid, field_group in groupby(list(field_list), itemgetter(ModelBasic.model_uuid.name)):
            model_field_dict[model_uuid] = list(field_group)

        for source_model_uuid, r_group in groupby(
                list(relationship_list), itemgetter(RelationshipBasic.source_model.name)):
            source_relationship_dict[source_model_uuid] = list(r_group)

        for target_model_uuid, r_group in groupby(
                list(relationship_list), itemgetter(RelationshipBasic.target_model.name)):
            target_relationship_dict[target_model_uuid] = list(r_group)

        for module_uuid, page_group in groupby(list(page_list), itemgetter(ModuleModel.module_uuid.name)):
            module_page_dict[module_uuid] = list(page_group)

        for module_uuid, func_group in groupby(list(func_list), itemgetter(ModuleModel.module_uuid.name)):
            module_func_dict[module_uuid] = list(func_group)

        for module_uuid, model_group in groupby(list(model_list), itemgetter(ModuleModel.module_uuid.name)):
            module_model_dict[module_uuid] = list(model_group)
            for model in model_group:
                model_uuid = model.get(ModelBasic.model_uuid.name)
                field_list = model_field_dict.get(model_uuid, list())
                source_list = source_relationship_dict.get(model_uuid, list())
                target_list = target_relationship_dict.get(model_uuid, list())
                target_list.extend(source_list)
                model["relations"] = target_list
                model["fields"] = field_list

        document_type_data_dict = {}
        key_list = [
            "app_sm_list", "app_model_list", "app_event_list", "app_state_list", "app_func_list", "app_page_list",
            "app_image_list", "app_const_list", "app_enum_list", "app_relationship_list", "app_json_list",
            "app_field_list", "app_index_list", "category_list", "app_workflow_list", "app_rule_list",
            "app_print_list"
        ]
        for document_dict in all_document:
            # app_log.info(f"_____________{document_dict}")
            document_type = document_dict.get(DocumentModel.document_type.name)
            document_check_service = self.engine.service.document_check.create_service(document_type)
            if document_check_service:
                document_check_service.init_service(document_dict)            
                setattr(document_check_service, "ext_tenant", ext_tenant)
                setattr(document_check_service, "new_ext_doc", False)
                setattr(document_check_service, "source_doc_content", None)
                if document_type == DocumentType.SECURITY:
                    module_uuid = document_dict.get(DocumentModel.module_uuid.name)
                    app_module_role_list = list(role_list)
                    module_model_list = module_model_dict.get(module_uuid, list())
                    module_page_list = module_page_dict.get(module_uuid, list())
                    module_func_list = module_func_dict.get(module_uuid, list())
                    setattr(document_check_service, "app_module_role_list", app_module_role_list)
                    setattr(document_check_service, "module_model_list", module_model_list)
                    setattr(document_check_service, "module_page_list", module_page_list)
                    setattr(document_check_service, "module_func_list", module_func_list)
                else:
                    document_type_data = document_type_data_dict.get(document_type)
                    if isinstance(document_type_data, dict):
                        for key, value in document_type_data.items():
                            setattr(document_check_service, key, value)
                    else:
                        await document_check_service.get_model_data()
                        document_type_data = dict()
                        for key in key_list:
                            value = getattr(document_check_service, key, None)
                            if value is not None:
                                document_type_data.update({key: value})
                        document_type_data_dict.update({document_type: document_type_data})
                checker_service = document_check_service.check_message()
                await document_check_service.check_message_commit(checker_service)

    async def check_all_document2(self, app_uuid, all_document, check_commit=False, ext_tenant=None, gather=True, priority='normal'):
        modified_doc = []
        func_list = []
        doc_check_order = [DocumentType.CONST, DocumentType.JSON, DocumentType.IMAGE, DocumentType.ENUM, DocumentType.FUNC, DocumentType.MODEL, DocumentType.PAGE]
        all_document = list(all_document)
        all_document.sort(key=lambda x: doc_check_order.index(x.get('document_type')) if x.get("document_type") in doc_check_order else 100)
        # with ProcessPoolExecutor() as pool:
        for document_type, document_group in groupby(
            all_document, key=itemgetter("document_type")):
            document_group = list(document_group)
            if document_type == DocumentType.MODEL:
                modified = await self.check_document_by_group(
                    app_uuid, ext_tenant, document_type, document_group, check_commit=check_commit, gather=gather, priority=priority)
                modified_doc.extend(modified)
            else:
                func_list.append(self.check_document_by_group(
                    app_uuid, ext_tenant, document_type, document_group, check_commit=check_commit, gather=gather, priority=priority))
        # asyncio.gather 可能会导致线程卡死 Thread 0x7f9f6cf4a700 (LWP 28773) "python" futex_abstimed_wait_cancelable (private=0, abstime=0x0, clockid=0, expected=0, futex_word=0x2422420)
        if gather:
            await asyncio.gather(*func_list)
        else:
            for func in func_list:
                m = await func
                modified_doc.extend(m)
        # modified = await asyncio.gather(*func_list)
        # for m in modified_doc:
        #     modified_doc.extend(m)
        return modified_doc

    async def check_document_by_group(self, app_uuid, ext_tenant, document_type, document_group, pool=None, check_commit=False, gather=False, priority='normal'):
        check_service_uninit = self.engine.service.document_check.create_service(document_type)
        cache_dict = {}
        func_list = []
        modified_doc = []
        if check_service_uninit:
            # 初始化类型字段
            check_service_uninit.init_app_service(app_uuid=app_uuid, ext_tenant=ext_tenant)
            # SECURITY 文档需按模块进行检查,不能按类型统一获取基础数据
            t1 = time.time()
            if document_type not in [
                DocumentType.SECURITY, DocumentType.MODEL, DocumentType.FUNC, DocumentType.WORKFLOW,
                    DocumentType.CONST, DocumentType.ENUM]:
                await check_service_uninit.get_model_data(cache_dict)
            for document in document_group:
                check_func = self.check_document_by_cache(
                    cache_dict, app_uuid, ext_tenant, document, pool, check_commit, priority=priority,
                    gather=gather)
                func_list.append(check_func)
            if gather:
                try:
                    await asyncio.gather(*func_list)
                except (BaseException, Exception):
                    app_log.info(traceback.format_exc())
            else:
                for func in func_list:
                    try:
                        modified = await func
                        if modified:
                            modified_doc.append(modified)
                    except (BaseException, Exception):
                        app_log.info(traceback.format_exc())

            app_log.info(f"{type(check_service_uninit)} check_const: {time.time()-t1}")
        return modified_doc

    async def check_document_by_cache(
        self, cache_dict, app_uuid, ext_tenant,  document, pool=None, check_commit=False, priority='normal',
        gather=False
    ):
        if self.engine.app.config.USE_CELERY and gather:
            for class_name, data_list in cache_dict.items():
                if data_list:
                    data_list = list(data_list)
                    for idx, data in enumerate(data_list):
                        if isinstance(data, LemonAsyncQueryWrapper):
                            data_list[idx] = list(data)
                    cache_dict[class_name] = data_list
            # start_time = time.time()
            # queue = "doc_save_check" if is_link_check else None

            priority = 5 if priority == 'high' else 10 if priority == 'normal' else 20
            await push_async_task(check_document_by_cache.__module__,
                                  check_document_by_cache.__name__,
                                  app_uuid,
                                  f"doc_check:{document.get('document_uuid')}",
                                  args=(self.engine, cache_dict, app_uuid, ext_tenant, document, pool, 
                                        check_commit, False),
                                  priority=priority,
                                  engine=self.engine)
        else:
            return await check_document_by_cache(engine=self.engine,
                                             cache_dict=cache_dict,
                                             app_uuid=app_uuid,
                                             ext_tenant=ext_tenant,
                                             document=document,
                                             pool=pool,
                                             check_commit=check_commit)


async def check_document_by_cache(engine, cache_dict, app_uuid, ext_tenant,  document, pool=None, check_commit=False, check_link=True):
    document_type = document.get("document_type")
    check_service_uninit = engine.service.document_check.create_service(document_type)
    document_content = document.get("document_content")
    check_service_uninit.init_app_service(app_uuid=app_uuid, ext_tenant=ext_tenant)
    async with engine.db.objs.atomic():
        if document_content:
            app_log.info(f"start_check: {document.get('document_uuid')}")
            # 初始化检查service
            check_service_uninit.init_document_service(**document)
            if document_type in [Document.TYPE.SECURITY]:
                await check_service_uninit.get_model_data()
            else:
                await check_service_uninit.get_model_data(cache_dict)   
                            
            checker_service = check_service_uninit.checker_service()
            # 进行检查并提交
            if pool:
                # 使用进程池进行文档检查时, checker_service的报错信息不会更新，只能通过返回结果来更新
                result = await engine.app.loop.run_in_executor(
                    pool, checker_service.check_all)
                result = ujson.loads(result)
                for key, value in result.items():
                    setattr(checker_service, key, value)
            else:
                result = checker_service.check_all()
            app_log.info(f"end_check: {document.get('document_uuid')}")
            if check_commit or document_type == DocumentType.MODEL:
                await check_service_uninit.check_commit(checker_service, check_schema=check_link)
            else:
                await check_service_uninit.check_message_commit(checker_service)    
            app_log.info(f"end_commit: {document.get('document_uuid')}")
            res = {
                "error_changed": getattr(checker_service, "error_changed", False)
            }
            if checker_service.is_element_changed:
                res.update({"document_content": checker_service.element,
                        "document_uuid": str(checker_service.document_uuid)})
            return res


async def check_document_content(check_service: BaseCheckService, *arg, **kwargs):
    code_error = None
    res = None
    try:
        res = await check_service._check_async(*arg, **kwargs)
    except Exception:
        code_error = traceback.format_exc()
        app_log.error(code_error)
    finally:
        doc_check_down_topic = f"doc_check_down:{check_service.app_uuid}:{'root'}"
        await check_service.engine.pubsub.publish_json(doc_check_down_topic, {
                            "check_status": "error" if code_error else "success",
                            "document_type": check_service.document_type,
                            "document_uuid": check_service.document_uuid,
                            "error": code_error})
        return res
