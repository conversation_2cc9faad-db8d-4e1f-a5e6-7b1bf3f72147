# -*- coding:utf-8 -*-

import time
import async<PERSON>
from itertools import groupby
from operator import itemgetter

from sanic.request import Request
from sanic.response import json
from peewee import Case, fn

from baseutils.log import app_log
from apps.entity import (
    ModelBasic, Func, StateMachine, Event, ModelField,
    RelationshipBasic as Relation, ImageTable, ConstTable, JsonTable,
    EnumTable, Page, RuleChain, UserRole, Workflow, RestfulTable
)
from apps.entity import Module as ModuleModel
from apps.entity import Document as DocumentModel
from apps.entity import ToolCategory as ToolCategoryModel
from apps.utils import process_args, check_lemon_name, lemon_uuid
from apps.utils import LemonDictResponse as LDR
from baseutils.const import Code, SystemField
from apps.ide_const import (
    Document, DocumentType, IDECode, EntityType, Module
)
from apps.services.mall.const import ReleaseType
from apps.services.ide import IDEService
from apps.base_utils import decode_sys_field_uuid
import copy

class QueryService(IDEService):

    async def list_obj_groupby_module(
            self, app_uuid, module_result, obj_result,
            need_dir_name=False, dir_view=False, need_module=False):
        dir_name_dict = dict()
        dir_dict = dict()
        if need_dir_name:
            d_model = DocumentModel
            fields = [
                d_model.document_uuid, d_model.document_name,
                d_model.document_puuid, d_model.module_uuid,
                d_model.document_type
            ]
            app_dir_list = await self.access.list_document_by_app_uuid_document_type(
                app_uuid=app_uuid, document_type=DocumentType.DIR,
                fields=fields)
            for app_dir in app_dir_list:
                dir_uuid = app_dir.get(d_model.document_uuid.name)
                dir_name = app_dir.get(d_model.document_name.name)
                dir_puuid = app_dir.get(d_model.document_puuid.name) or app_dir.get(d_model.module_uuid.name)
                dir_name_dict[dir_uuid] = dir_name
                dir_child = dir_dict.setdefault(dir_puuid, list())
                dir_child.append(app_dir)

        module_list, obj_list = list(module_result), list(obj_result)
        module_dict = dict()
        for module in module_list:
            module_uuid = module.get(ModuleModel.module_uuid.name)
            module_dict[module_uuid] = module
        data_module_list = []
        data = dict(
            app_uuid=app_uuid, children=data_module_list, dir_dict=dir_dict)
        obj_list.sort(key=itemgetter(ModuleModel.module_uuid.name))
        module_dict_bak = copy.deepcopy(module_dict)
        if need_module:
            for module_info in module_list:
                module_uuid = module_info.get(ModuleModel.module_uuid.name)
                module_type = module_info.get(ModuleModel.module_type.name)
                module_name = module_info.get(ModuleModel.module_name.name)
                module_id = module_info.get(ModuleModel.id.name)
                data_model_list = []
                data_module_dict = {
                    ModuleModel.module_uuid.name: module_uuid,
                    ModuleModel.module_type.name: module_type,
                    ModuleModel.module_name.name: module_name,
                    ModuleModel.id.name: module_id,
                    "children": data_model_list
                }
                data_module_list.append(data_module_dict)
                for obj_module_uuid, group in groupby(
                        obj_list, itemgetter(ModuleModel.module_uuid.name)):
                    if obj_module_uuid == module_uuid:
                        for model in group:
                            document_pname = ""
                            document_puuid = model.get(DocumentModel.document_puuid.name)
                            if need_dir_name:
                                if document_puuid == "":
                                    document_pname = "<根目录>"
                                else:
                                    document_pname = dir_name_dict.get(document_puuid, "")
                            model.update({"document_pname": document_pname})
                            data_model_list.append(model)
        else:
            for module_uuid, group in groupby(
                    obj_list, itemgetter(ModuleModel.module_uuid.name)):
                if module_dict_bak.get(module_uuid):
                    module_dict_bak.pop(module_uuid)
                module = module_dict.get(module_uuid)
                app_log.info(f"module_uuid: {module_uuid}")
                module_type = module.get(ModuleModel.module_type.name)
                module_name = module.get(ModuleModel.module_name.name)
                module_id = module.get(ModuleModel.id.name)
                data_model_list = []
                data_module_dict = {
                    ModuleModel.module_uuid.name: module_uuid,
                    ModuleModel.module_type.name: module_type,
                    ModuleModel.module_name.name: module_name,
                    ModuleModel.id.name: module_id,
                    "children": data_model_list
                }
                data_module_list.append(data_module_dict)
                for model in group:
                    document_pname = ""
                    document_puuid = model.get(DocumentModel.document_puuid.name)
                    if need_dir_name:
                        if document_puuid == "":
                            document_pname = "<根目录>"
                        else:
                            document_pname = dir_name_dict.get(document_puuid, "")
                    model.update({"document_pname": document_pname})
                    data_model_list.append(model)
        if dir_view:
            self.group_page_with_parent(data, need_module)
        return json(LDR(data=data))

    async def get_entity_name(self, entity_uuid_list, entity_type):
        model_dict = {
            EntityType.MODEL: ModelBasic,
            EntityType.SM: StateMachine,
            EntityType.FUNC: Func,
            EntityType.IMAGE: ImageTable,
            EntityType.CONST: ConstTable,
            EntityType.JSON: JsonTable,
            EntityType.ENUM: EnumTable,
            EntityType.FIELD: ModelField,
            EntityType.RELATIONSHIP: Relation,
            EntityType.EVENT: Event,
            EntityType.RESTFUL: RestfulTable
        }
        field_dict = {
            EntityType.MODEL: [ModelBasic.model_uuid, ModelBasic.model_name],
            EntityType.SM: [StateMachine.sm_uuid, StateMachine.sm_name],
            EntityType.FUNC: [Func.func_uuid, Func.func_name],
            EntityType.IMAGE: [ImageTable.image_uuid, ImageTable.image_name],
            EntityType.CONST: [ConstTable.const_uuid, ConstTable.const_name],
            EntityType.JSON: [JsonTable.json_uuid, JsonTable.json_name],
            EntityType.ENUM: [EnumTable.enum_uuid, EnumTable.enum_name],
            EntityType.FIELD: [
                ModelField.field_uuid, ModelField.field_name,
                ModelField.field_type, ModelField.length,
                ModelField.aggre_field, ModelField.calculate_field,
                ModelField.hierarchy_field, ModelField.model_uuid,
                ModelField.enum_uuid, ModelField.display_name,
                ModelField.is_serial
            ],
            EntityType.RELATIONSHIP: [
                Relation.relationship_uuid, Relation.relationship_name],
            EntityType.EVENT: [Event.event_uuid, Event.event_name],
            EntityType.RESTFUL: [
                RestfulTable.restful_uuid, RestfulTable.restful_name]
        }
        model = model_dict.get(entity_type)
        fields = field_dict.get(entity_type)
        if not model or not fields:
            return json(LDR(IDECode.ENTITY_NOT_EXISTS))

        pk_field, name_field = fields[0], fields[1]
        query = model.select(*fields).where(pk_field.in_(entity_uuid_list))
        data = await self.engine.access.list_obj(model, query)

        entity_data = dict()
        # 转换数据格式
        for d in data:
            if not isinstance(d, dict):
                continue
            pk_data, name_data = d.get(pk_field.name), d.get(name_field.name)
            e_data = {"uuid": pk_data, "name": name_data}
            # 字段数据 需要 字段类型显示
            if entity_type == EntityType.FIELD:
                type_data = d.get(ModelField.field_type.name)
                e_data.update({
                    "type": type_data,
                    "model_uuid": d.get(ModelField.model_uuid.name),
                    "aggre_field": d.get(ModelField.aggre_field.name),
                    "hierarchy_field": d.get(ModelField.hierarchy_field.name),
                    "enum_uuid": d.get(ModelField.enum_uuid.name),
                    "display_name": d.get(ModelField.display_name.name),
                    "is_serial": d.get(ModelField.is_serial.name)
                })
            entity_data.update({pk_data: e_data})
        if entity_type == EntityType.FIELD:
            for field_uuid in entity_uuid_list:
                # 系统字段未写入数据库 需特殊处理
                if "*" in field_uuid:
                    model_uuid, field_name = decode_sys_field_uuid(field_uuid)
                    field_info = SystemField.FIELD_DICT.get(field_name)
                    field_type = field_info.get("field_type")
                    display_name = field_info.get("display_name")
                    e_data = {
                        "type": field_type, "uuid": field_uuid,
                        "model_uuid": model_uuid, "name": field_name,
                        "display_name": display_name, "enum_uuid": None,
                        "aggre_field": "", "hierarchy_field": "",
                        "is_serial": "", "is_system": True
                    }
                    entity_data.update({field_uuid: e_data})
        return json(LDR(data=entity_data))

    # 按照模块列出数据模型
    async def list_model_groupby_module(self, request: Request, with_sys=False):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            ext_tenant = request.json.get("ext_tenant")
            mark_self_relationship = request.json.get("mark_self_relationship")
        model = ModelBasic
        fields = (model.module_uuid, model.model_uuid, model.model_name, model.display_name, model.is_temp, model.ext_tenant.is_null(False).alias("is_extension"))
        if mark_self_relationship:
            r_model = Relation
            contain_relationship = Case(
                None, 
                [(r_model.source_model==r_model.target_model, True), 
                 (r_model.source_model!=r_model.target_model, False)], 
                False
            )
            fields = fields+(fn.SUM(contain_relationship).alias("contain_self_relationship"),)
            list_model_func = self.engine.access.list_model_by_app_uuid(
                app_uuid, fields=fields, 
                as_dict=True, with_sys=with_sys, ext_tenant=ext_tenant, 
                mark_self_relationship=mark_self_relationship
                )
        else:
            list_model_func = self.engine.access.list_model_by_app_uuid(
                app_uuid, fields=fields, as_dict=True, with_sys=with_sys, ext_tenant=ext_tenant, 
                )
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=with_sys)
        obj_result = await list_model_func
        return await self.list_obj_groupby_module(app_uuid, module_result, obj_result)

    # 按照模块列出云函数
    async def list_func_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        model = Func
        d_model = DocumentModel
        fields = (model.module_uuid, model.func_uuid, model.func_name, model.icon, d_model.document_puuid)
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_func_by_app_uuid(
            app_uuid, fields=fields, need_sort=True, as_dict=True, with_sys=True)
        return await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)

    # 按照模块列出excel文档
    async def list_excel_doc_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            model_uuid = str(request.json.get("model_uuid"))
        model = DocumentModel
        fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.is_delete,
                model.document_name,
                model.document_puuid,
                model.document_type
            )
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_template_doc_by_app_uuid(
            app_uuid, fields=fields, as_dict=True, with_sys=True, model_uuid=model_uuid)
        return await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)
    
    # 按照模块列出导出文档
    async def list_export_template_doc_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            model_uuid = str(request.json.get("model_uuid"))
        model = DocumentModel
        fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.is_delete,
                model.document_name,
                model.document_puuid,
                model.document_type
            )
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_export_template_doc_by_app_uuid(
            app_uuid, fields=fields, as_dict=True, with_sys=True, model_uuid=model_uuid)
        return await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)

    # 按照模块列出状态机
    async def list_sm_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        model = StateMachine
        fields = (model.module_uuid, model.sm_uuid, model.sm_name, model.display_name)
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_sm_by_app_uuid(app_uuid, with_sys=True, fields=fields, as_dict=True)
        return await self.list_obj_groupby_module(app_uuid, module_result, obj_result)

    async def list_rc_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        model = RuleChain
        fields = (model.module_uuid, model.rule_uuid, model.rule_name)
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_rulechain_by_app_uuid(
            app_uuid, with_sys=True, fields=fields, as_dict=True)
        return await self.list_obj_groupby_module(app_uuid, module_result, obj_result)

    # 按照模块列出事件
    async def list_event_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        model = Event
        sm_model = StateMachine
        fields = (model.module_uuid, model.sm_uuid, sm_model.sm_name, model.event_uuid, model.event_name)
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_event_join_sm_by_app_uuid(
                app_uuid, with_sys=True, fields=fields, as_dict=True)
        return await self.list_obj_groupby_module(app_uuid, module_result, obj_result)

    # 按照模块列出常量
    async def list_const_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        model = ConstTable
        fields = (model.module_uuid, model.const_uuid, model.const_name, model.const_type, model.value)
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_const_by_app_uuid(app_uuid, with_sys=True, fields=fields, as_dict=True)
        return await self.list_obj_groupby_module(app_uuid, module_result, obj_result)

    # 按照模块列出枚举
    async def list_enum_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        model = EnumTable
        d_model = DocumentModel
        fields = (model.module_uuid, model.enum_uuid, model.enum_name,
                  model.value.alias("children"), d_model.document_puuid,
                  d_model.document_uuid,
                  d_model.document_name)
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_enum_by_app_uuid(app_uuid, fields=fields, with_sys=True, as_dict=True)
        return await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, dir_view=True, need_dir_name=True)

    # 按照模块列出图片
    async def list_image_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_image_group_by_document(
                app_uuid, with_sys=True, as_dict=True, need_theme=False)
        return await self.list_obj_groupby_module(app_uuid, module_result, obj_result)

    # 按照模块列出页面
    async def list_page_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            open_type = request.json.get("open_type", list())
            form_count = request.json.get("form_count", None)
            ext_tenant = request.json.get("ext_tenant")
            if open_type is not None:
                open_type = list(open_type)
        model = Page
        d_model = DocumentModel
        fields = (
            model.module_uuid, d_model.document_name.alias(model.page_name.name), 
            model.page_uuid, model.open_type, model.form_count, model.container_model, 
            d_model.document_puuid, model.ext_tenant.is_null(False).alias("is_extension"),
            d_model.id
        )
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_page_by_app_uuid_open_type(
                app_uuid, open_type=open_type, form_count=form_count, fields=fields,
                as_dict=True, ext_tenant=ext_tenant)
        app_log.info(module_result)
        result = await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)
        return result

    # 按照模块列出工作流
    async def list_workflow_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            ext_tenant = request.json.get("ext_tenant")
        model = Workflow
        d_model = DocumentModel
        fields = (
            model.module_uuid, d_model.document_name.alias(model.wf_name.name), model.wf_uuid,
            d_model.document_puuid, d_model.id
        )
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_workflow_join_document_by_app_uuid(
                app_uuid, fields=fields, as_dict=True, ext_tenant=ext_tenant)
        result = await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)
        return result

    # 按照模块列出审批流
    async def list_approvalflow_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            ext_tenant = request.json.get("ext_tenant")
        model = Workflow
        d_model = DocumentModel
        fields = (
            model.module_uuid, d_model.document_name.alias(model.wf_name.name), model.wf_uuid,
            d_model.document_puuid, d_model.id
        )
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_approvalflow_join_document_by_app_uuid(
                app_uuid, fields=fields, as_dict=True, ext_tenant=ext_tenant)
        result = await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)
        return result

    # 按照模块列出打印模板
    async def list_print_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            form_count = request.json.get("form_count", None)
            model_uuid = request.json.get("model_uuid", None)
            ext_tenant = request.json.get("ext_tenant")
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_print_join_document_by_app_uuid(
                app_uuid, form_count=form_count, model_uuid=model_uuid,
                as_dict=True, ext_tenant=ext_tenant)
        return await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)

    # 按照模块列出标签打印
    async def list_label_print_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            ext_tenant = request.json.get("ext_tenant")
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_label_print_join_document_by_app_uuid(
                app_uuid, as_dict=True, ext_tenant=ext_tenant)
        return await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)

    # 按照模块列出RESTful接口
    async def list_restful_groupby_module(self, request: Request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            basic_auth = request.json.get("basic_auth")
            # ext_tenant = request.json.get("ext_tenant")
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_restful_request_by_app_uuid(
                app_uuid, basic_auth=basic_auth, as_dict=True)
        result = await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)
        return result

    # 按照模块列出相应类型的文档
    async def list_document_groupby_module(self, request, with_policy=False):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            document_type = int(request.json.get("document_type"))
            ext_tenant = request.json.get("ext_tenant")
        if not app_uuid or document_type not in Document.TYPE.FILE:
            return json(LDR(Code.ARGS_ERROR))

        model = DocumentModel
        module_model = ModuleModel
        fields = (
            model.module_uuid, model.document_uuid, model.document_name, model.document_type, 
            model.document_path, model.update_timestamp, model.ext_tenant.is_null(False).alias("is_extension")
        )
        dir_fields = (
            model.id, model.document_name
        )
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid)
        obj_result = await self.engine.access.list_document_by_app_uuid_document_type(
                app_uuid, document_type, fields=fields, as_dict=True, ext_tenant=ext_tenant, with_policy=with_policy)
        dir_result = await self.engine.access.list_document_by_app_uuid_document_type(
                app_uuid, Document.TYPE.DIR, fields=dir_fields, as_dict=True, with_policy=with_policy)
        module_list, obj_list, dir_list = list(module_result), list(obj_result), list(dir_result)

        module_dict, dir_dict = dict(), dict()
        for module in module_list:
            module_uuid = module.get(module_model.module_uuid.name)
            module_dict[module_uuid] = module
        for d in dir_list:
            d_id = d.get("id")
            d_name = d.get(model.document_name.name)
            dir_dict[d_id] = d_name

        data_module_list = []
        data = dict(app_uuid=app_uuid, children=data_module_list)
        for module_uuid, group in groupby(obj_list, itemgetter(module_model.module_uuid.name)):
            module = module_dict.get(module_uuid)
            module_type = module.get(module_model.module_type.name)
            module_name = module.get(module_model.module_name.name)
            data_model_list = []
            data_module_dict = {
                module_model.module_uuid.name: module_uuid,
                module_model.module_type.name: module_type,
                module_model.module_name.name: module_name,
                "children": data_model_list
            }
            data_module_list.append(data_module_dict)
            for m in group:
                document_path = m.get(model.document_path.name)
                document_id_split = document_path.split("/")
                document_path_list = list()
                for d_id in document_id_split:
                    if d_id != "":
                        d_path = dir_dict.get(int(d_id))
                        document_path_list.append(d_path)
                new_document_path = "/" + "/".join(document_path_list)
                m.update({model.document_path.name: new_document_path})
                data_model_list.append(m)
        data_module_list.sort(
            key=itemgetter(module_model.module_type.name, module_model.module_name.name),
            reverse=True)
        return json(LDR(data=data))

    # 按照模块列出相应类型的文档
    async def list_tool_category(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            entity_type = int(request.json.get("entity_type"))
        if not app_uuid or entity_type not in EntityType.CATEGORY_ENTITY:
            return json(LDR(Code.ARGS_ERROR))

        model = ToolCategoryModel
        sys_result = await self.engine.access.list_tool_category_by_sys(entity_type)
        user_result = await self.engine.access.list_tool_category_by_user(app_uuid, entity_type)
        sys_list, user_list = list(sys_result), list(user_result)
        user_list.extend(sys_list)
        data = dict(app_uuid=app_uuid, children=user_list)
        user_list.sort(key=itemgetter(model.category_type.name))
        return json(LDR(data=data))

    # 按照工具分类列出工具
    async def list_tool_groupby_category(self, request):
        with process_args():
            app_uuid = str(request.json.get("app_uuid"))
            entity_type = int(request.json.get("entity_type"))
        if not app_uuid or entity_type not in EntityType.CATEGORY_ENTITY:
            return json(LDR(Code.ARGS_ERROR))

        if entity_type in [EntityType.APP_LAYOUT]:
            user_tool_list = list(await self.engine.access.list_user_tool_join_category(app_uuid))
        else:
            sys_tool_result = await self.engine.access.list_sys_tool_join_category(entity_type)
            user_tool_result = await self.engine.access.list_user_tool_join_category(app_uuid, entity_type)
            sys_tool_list = list(sys_tool_result)
            user_tool_list = list(user_tool_result)
            app_log.info(user_tool_list)
            user_tool_list.extend(sys_tool_list)

        c_model = ToolCategoryModel
        data_list = list()
        data = dict(app_uuid=app_uuid, children=data_list)
        for c_tuple, group in groupby(
            user_tool_list, itemgetter(
                c_model.category_uuid.name, c_model.category_type.name, 
                c_model.category_name.name, c_model.category_sort.name)):
            c_uuid, c_type, c_name, c_sort = c_tuple
            tool_list = list()
            data_dict = {
                c_model.category_uuid.name: c_uuid,
                c_model.category_type.name: c_type,
                c_model.category_name.name: c_name,
                c_model.category_sort.name: c_sort, 
                "children": tool_list
            }
            for tool in group:
                tool_id = tool.get("tool_uuid")
                if tool_id:
                    tool_list.append(tool)
            data_list.append(data_dict)
        data_list.sort(key=itemgetter(c_model.category_type.name, c_model.category_sort.name))
        return json(LDR(data=data))
    
    async def check_create_module(self, app_uuid, module_name):
        if not all([app_uuid, module_name]):
            return Code.ARGS_ERROR
        module_name = check_lemon_name(module_name, chinese=True)
        if not module_name:
            return IDECode.MODULE_NAME_FAILED
        module = await self.engine.access.get_module_by_app_uuid_module_name(
            app_uuid, module_name)
        if module:
            return IDECode.MODULE_NAME_EXISTS
        return False

    async def create_module(
        self, app_uuid, module_name, user_uuid, extend_from=None, extension_type=None, 
        create_document=True, module_uuid=None, module_description=""):
        create_module_invalid = await self.check_create_module(app_uuid, module_name)
        if create_module_invalid:
            return json(LDR(create_module_invalid))
        module_dict = await self.create_module_with_document(
            app_uuid, module_name, user_uuid, extend_from, extension_type, create_document, 
            module_uuid=module_uuid, module_description=module_description)
        if not create_document:
            return module_dict
        return json(LDR(data=module_dict))
    
    async def create_module_with_document(
        self, app_uuid, module_name, user_uuid, extend_from, extension_type, create_document=True, 
        module_uuid=None, module_description=""):
        module_uuid = module_uuid or lemon_uuid()
        module_dict = dict(
            app_uuid=app_uuid,
            module_uuid=module_uuid,
            module_name=module_name,
            module_type=Module.TYPE.OWN, 
            extend_from=extend_from, 
            extension_type=extension_type,
            module_description=module_description,
        )
        user_role_list = await self.engine.access.list_user_role_by_app_uuid(app_uuid=app_uuid, 
                                                                             fields=(UserRole.role_uuid,))
        user_role_list = list(user_role_list)
        await self.engine.access.create_module(user_uuid, **module_dict)
        await self.engine.access.add_module_relationship_on_user_role_content(
            app_uuid, module_uuid, user_role_list)
        if not create_document:
            # 不写入模块安全和模型文档
            return module_dict
        security = await self.engine.access.get_document_by_module_uuid_document_type(module_uuid, Document.TYPE.SECURITY)
        model_document = await self.engine.access.get_document_by_module_uuid_document_type(module_uuid, Document.TYPE.MODEL)
        module_deploy = await self.engine.access.get_document_by_module_uuid_document_type(module_uuid, Document.TYPE.MODULE_DEPLOY)
        permission_config = await self.engine.access.get_document_by_module_uuid_document_type(
            module_uuid, Document.TYPE.PERMISSION_CONFIG)
        module_delopy_config = await self.engine.access.get_document_by_module_uuid_document_type(
            module_uuid, Document.TYPE.EXPANSION_PACK)

        async def _create_document(
            document_name, document_type, document_uuid=None, document_version=0):
            now_timestamp = int(time.time())
            document_uuid = document_uuid or lemon_uuid()
            document_dict = dict(
                app_uuid=app_uuid,
                module_uuid=module_uuid,
                document_uuid=document_uuid,
                document_name=document_name,
                document_type=document_type,
                document_version=document_version, 
                document_path="/",
                document_puuid="",
                create_timestamp=now_timestamp,
                update_timestamp=now_timestamp
            )
            await self.engine.access.create_document(user_uuid, **document_dict)
        if not security:
            await _create_document("模块安全", Document.TYPE.SECURITY)
        if not model_document:
            await _create_document("数据模型", Document.TYPE.MODEL)
        if not module_deploy:
            document_uuid = lemon_uuid()
            await _create_document(
                "模块配置", Document.TYPE.MODULE_DEPLOY, document_uuid=document_uuid, 
                document_version=1)
            # publisher = await gen_app_publisher(self.engine, app_uuid)
            content = {
                "show_name": module_name, 
                # "publisher_name": publisher_name,  # 肯定是应用所有者对应的发行商, 不再保存
                "release_type": ReleaseType.TEMPLATE, 
                "included_module": "", 
                "previous_version": "", 
                "current_version": "1.0", 
                "extension_uuid": module_uuid
            }
            data = {
                "document_uuid": document_uuid, 
                "document_content": content
            }
            await self.engine.access.create_document_content(**data)
        if not permission_config:
            document_uuid = lemon_uuid()
            await _create_document("权限资源", Document.TYPE.PERMISSION_CONFIG, document_uuid)
        if not module_delopy_config:
            document_uuid = lemon_uuid()
            document_content = dict(
                document_uuid=document_uuid,
                document_content={"extension_pack": {"packages": [], "sysPackages": {}}},
            )
            await _create_document("扩展包管理", Document.TYPE.EXPANSION_PACK, document_uuid=document_uuid, document_version=1)
            await self.engine.access.create_document_content(**document_content)
        return module_dict

    def group_page_with_parent(self, app_info, need_module=False):
        module_list = app_info.get("children")
        dir_dict = app_info.get("dir_dict")
        d_model = DocumentModel
        for module_info in module_list:
            parent_dict = {}
            children_list = module_info.get("children")
            for child in children_list:
                document_puuid = child.get(d_model.document_puuid.name) or child.get(d_model.module_uuid.name)
                parent_child = parent_dict.setdefault(document_puuid, list())
                parent_child.append(child)
            module_info[d_model.document_uuid.name] = module_info.get(d_model.module_uuid.name)
            if need_module:
                self.get_dir_all(module_info, dir_dict, parent_dict)
            else:
                self.get_dir_page(module_info, dir_dict, parent_dict)
        app_info.pop("dir_dict", None)

    def get_dir_all(self, dir_info, dir_dict, parent_dict):
        d_model = DocumentModel
        dir_uuid = dir_info.get(d_model.document_uuid.name)
        child_page = parent_dict.get(dir_uuid, [])
        child_dir = dir_dict.get(dir_uuid, [])
        for c_dir_info in child_dir:
            self.get_dir_all(c_dir_info, dir_dict, parent_dict)
        child_dir_list = [document_i.get("document_uuid") for document_i in child_dir]
        for child_page_i in child_page:
            if child_page_i.get("document_uuid") not in child_dir_list:
                child_dir.append(child_page_i)
        dir_info["children"] = child_dir

    def get_dir_page(self, dir_info, dir_dict, parent_dict):
        d_model = DocumentModel
        dir_uuid = dir_info.get(d_model.document_uuid.name)
        child_page = parent_dict.get(dir_uuid, [])
        child_dir = dir_dict.get(dir_uuid, [])
        for c_dir_info in child_dir:
            self.get_dir_page(c_dir_info, dir_dict, parent_dict)
        child_dir = filter(lambda c_dir: c_dir.get("children"), child_dir)
        child_page.extend(child_dir)
        dir_info["children"] = child_page

    # 按照模块列出某类型文档
    async def list_document_groupby_module_by_document_type(self, app_uuid, document_type, alias_name=None):
        module_result = await self.engine.access.list_module_by_app_uuid(app_uuid, with_sys=True)
        obj_result = await self.engine.access.list_module_theme_by_app_uuid_document_type(
            app_uuid, document_type=document_type, as_dict=True, need_delete=False)
        if alias_name:
            for obj in obj_result:
                obj[f'{alias_name}_uuid'] = obj.get("document_uuid")
                obj[f'{alias_name}_name'] = obj.get("document_name")
        result = await self.list_obj_groupby_module(
            app_uuid, module_result, obj_result, need_dir_name=True, dir_view=True)
        return result

    # 按照模块列出模块目录
    async def list_module_dir_groupby_module(self, request: Request, app_uuid=None):
        with process_args():
            app_uuid = app_uuid or str(request.json.get("app_uuid"))
            module_uuid = request.json.get("module_uuid")

        if module_uuid:
            cur_module_info: ModuleModel = await self.engine.access.get_module_by_module_uuid(module_uuid)
            extend_from = cur_module_info.extend_from
            module_uuid = module_uuid if extend_from else None  # 如果是扩展模块，只列出当前模块的目录

        module_result = await self.engine.access.list_module_by_app_uuid(
            app_uuid, with_sys=True, module_uuid=module_uuid)
        app_dir_list = await self.access.list_document_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=DocumentType.DIR, module_uuid=module_uuid)
        # app_log.info(f"module_result: {len(list(module_result))}")
        # app_log.info(f"app_dir_list: {len(list(app_dir_list))}")
        result = await self.list_obj_groupby_module(
            app_uuid, module_result, app_dir_list, need_dir_name=True, dir_view=True, need_module=True)
        return result
