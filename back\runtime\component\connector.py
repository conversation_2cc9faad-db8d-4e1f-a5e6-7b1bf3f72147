import asyncio
import uuid
import os
import traceback
import ujson
import zlib
import hashlib
import base64
import time
import copy
from functools import partial

from baseutils.log import app_log
from baseutils.utils import LemonContextVar
from apps.base_entity import CurrentUserModel
from asyncio_mqtt import Client as MqttClient
from aiokafka.structs import TopicPartition
from collections import defaultdict, OrderedDict
from runtime.engine import engine
from runtime.utils import LemonMessage
from runtime.core.connector import ClientConnector
from runtime.core.value_editor import LemonValueEditor
from runtime.core.utils import (
    lemon_field, lemon_model, lemon_model_node, build_join,
    build_join_info, lemon_association, build_topic
)
from runtime.utils import (
    GlobalVars
)
from apps.ext import KafkaConsumer
from apps.ide_const import DictWrapper
from peewee import SQL, RawQuery, fn, SchemaManager, ModelIndex, Field
from tests.utils import UUID
# from jsonpath import parse
from apps.peewee_ext import <PERSON>F<PERSON>ign<PERSON>eyField
from runtime.core.func_run import Func<PERSON>un
from runtime.component.data_adapter import Rule<PERSON>hain

class DcuConnector:
    def __init__(self, app_uuid, connector_define, app_config, tenants_config, loop=None) -> None:
        self._loop = loop
        self.app_env_name = os.environ.get("APP_ENV_NAME", "").rstrip(";")
        self.app_uuid = app_uuid
        self.connector_define = connector_define
        self.app_config = app_config
        self.tenant_uuid = None # 租户id需要写道环境变量里
        self.tenants_config = {self.tenant_uuid: tenants_config.pop(self.tenant_uuid, {})} if self.tenant_uuid else tenants_config   # 解析前： 租户配置，解析后：租户完整配置
        self.connector_config_app = {}   # 解析后的连接器app配置

        self.uuid = connector_define.get('ct_uuid', '')
        self.sid = str(uuid.uuid5(uuid.UUID(self.app_uuid), "".join([self.tenant_uuid or "", self.uuid]))).replace("-", "")
        app_log.info(self.sid)
        self.client_connector = ClientConnector(self.sid)
        self.client_connector.on_result = self.on_result

        self.sub_topics = list()
        self.callback_topics = list()

        self.wait_tasks = list()

    def __hash__(self):
        return hash(f"connector {self.sid}")

    def __repr__(self) -> str:
        return f"lemon_connector_{self.uuid}_{self.connector_define.get('ct_name', '')}"

    def get_editor_value(self, value_define, app_config, tenant_config):
        editable = value_define.get("editable", 0)
        if editable == 1:
            value_editor_define = app_config
        elif editable == 2:
            value_editor_define = tenant_config
        else:
            value_editor_define = {}

        return LemonValueEditor(**value_editor_define).value if value_editor_define else value_define["default"]

    async def on_result(self, msg):
        app_log.debug(msg)

    async def _init_agent_config(self, tenant_uuid, config):
        key = f"connector_config_cache{self.app_env_name}:" + "_".join([self.app_uuid, self.uuid])
        await engine.redis.hset(key, tenant_uuid, ujson.dumps(config))
        event_args = {"agent_config": config}
        await self.send_connector_msg(tenant_uuid, "command_reply", event_args)

    async def send_connector_msg(self, tenant_uuid, msg_type, msg):
        reply_topic = "/".join(["", "dcu", msg_type, self.app_uuid, tenant_uuid, self.uuid])
        async with MqttClient('tlemon.lemonstudio.tech') as client:
            await client.publish(reply_topic, ujson.dumps(msg), qos=1)

    async def create_connector(self):
        try:
            await self.analyze_config()
            await self.init_consumer()
        except:
            app_log.error(traceback.format_exc())

    async def analyze_config(self):
        # 先只支持source_model和source_model 1:* 模型下的字段，一层关联
        self.tenants_config.update({"*": {}})
        app_data_point = self.connector_define.get('data', {})
        for tenant_uuid, tenant_config in self.tenants_config.items():
            app_basic_config = self.app_config.get("basic_config", {})
            tenant_basic_config = tenant_config.get('basic_config', {})
            basic_config = {}
            for basic_ in self.connector_define.get("basic_config", []):
                key = basic_["key"]
                value = self.get_editor_value(basic_, app_basic_config.get(key, {}), tenant_basic_config.get(key, {}))
                basic_config.update({key: value})
            
            connector_tenant_config = dict(ct_uuid=self.uuid, ct_type="dcu", basic_config=basic_config, data=app_data_point, version=1)
            self.tenants_config.update({tenant_uuid: connector_tenant_config})
            await self._init_agent_config(tenant_uuid, connector_tenant_config)
        
        self.data_point_cfg = {} 
        self.dp_models = OrderedDict()
        self.source_model = lemon_model(self.connector_define.get("source_model"))
        self.dp_models[self.source_model._meta.table_name + "_"] = set()
        self.parse_data_struct(None, "$", self.data_point_cfg, app_data_point)
        app_log.info(self.dp_models)
        app_log.info(self.data_point_cfg)


    def parse_data_struct(self, key, path, dp_path_dict, data_struct):
        dp_type = data_struct["type"]
        bindto = data_struct.get("bindto")
        app_log.info((key, path, dp_path_dict, data_struct))
        if bindto:
            dp_column = lemon_field(bindto)
            if dp_column:
                dp_model_name = dp_column.model._meta.table_name + "_" + ",".join(data_struct.get("path", []))
                if dp_model_name not in self.dp_models:
                    self.dp_models[dp_model_name] = set()
                self.dp_models[dp_model_name].add(bindto)
                dp_path_dict[bindto] = {
                    "key": key,
                    "expr": parse(path),
                    "column": dp_column,
                    "type": dp_type
                }
        if dp_type == "object":
            for dp_key, dp_define in data_struct.get("properties", {}).items():
                self.parse_data_struct(dp_key, f"{path}.{dp_key}", dp_path_dict, dp_define)
        elif dp_type == "array":
            self.parse_data_struct(key, f"{path}[*]", dp_path_dict, data_struct["items"])


    async def start(self):
        app_log.debug(f"start {self}")
        await self.create_connector()

    async def init_consumer(self):
        config = DictWrapper(
            loop=self._loop,
            KAFKA_GROUP="lemon-connector-dcu",
            KAFKA_HOSTS=engine.app.config.KAFKA_HOSTS,
            KAFKA_CLIENT_ID=self.sid,
            KAFKA_SUBSCRIBE=f"lemon-bridge-dcu-{self.app_uuid}-.*"  # 先假定租户共用，不单独部署
        )
        self.consumer = KafkaConsumer()
        await self.consumer.init_app(config=config)
        app_log.info(f"subscribe: {config.KAFKA_SUBSCRIBE}")
        self.consumer.subscribe(pattern=config.KAFKA_SUBSCRIBE)
        self._loop.create_task(self.handle_consumer())

    async def commit_kafka(self, topic, partition, offset):
        app_log.debug(f"commit {(topic, partition, offset)}")
        tp = TopicPartition(topic, partition)
        await self.consumer.commit({tp: offset + 1})
   
    async def handle_consumer(self):

        async for msg in self.consumer:
            app_log.info(("msg: ", type(msg), msg.topic, msg.partition, msg.offset, msg.key, msg.timestamp))
            app_log.debug(f"{msg.value}"[:200])
            tp = (msg.topic, msg.partition, msg.offset)
            # tp = TopicPartition(msg.topic, msg.partition)
            # await self.consumer.commit({tp: msg.offset + 1})
            # continue
            try:
                topic_split = msg.topic.split("-")  # lemon-connector-{device_type}-{app_uuid}-{tenant_uuid}
                device_type, app_uuid, tenant_uuid = topic_split[2:5]
                topic_type = msg.value.get("topic_type")
                connector_uuid = msg.value.get("device_id")
                payload = msg.value.get("payload")
                payload = ujson.loads(payload)
                data = payload.get("data")
                
                current_user = await CurrentUserModel.from_dict({"tenant_uuid": tenant_uuid})
                LemonContextVar.current_user.set(current_user)

                if device_type == "dcu" and topic_type == "post" and data:
                    source_model_pk = None

                    for model_name, model_info in self.dp_models.items():
                        table_name, path_str = model_name.split("_")
                        path = path_str.split(",")
                        is_source = False
                        model_data = []
                        model = lemon_model(table_name)
                        path_field = model._meta.columns.get(path[0])
                        if table_name == self.source_model._meta.table_name and not path_field:
                            is_source = True
                        
                        for bindto in model_info:
                            dp_info = self.data_point_cfg[bindto]
                            dp_expr = dp_info['expr']
                            dp_column = dp_info['column']
                            dp_type = dp_info['type']
                            match = dp_expr.find(data)
                            if len(match) == 0:
                                continue
                            for idx, m in enumerate(match):
                                if idx == len(model_data):
                                    model_data.append({})
                                model_data[idx].update({dp_column.name: m})
                        if path_field:
                            for row_data in model_data:
                                row_data.update({path_field.name: source_model_pk})
                        if model_data:
                            pk = await self.insert_data(model, model_data, is_many = not is_source)
                            if is_source:
                                source_model_pk = pk
            except:
                app_log.error(traceback.format_exc())
            await self.commit_kafka(*tp)

    async def get_uniques(self, model):
        with engine.userdb.real_database.allow_sync():
            await asyncio.get_event_loop().run_in_executor(None, partial(engine.userdb.real_database.connection().ping, True))
            existing_indexes = await asyncio.get_event_loop().run_in_executor(None, partial(engine.userdb.real_database.get_indexes, model._meta.table_name, schema='public'))
        app_log.info(f"existing indexes: {existing_indexes}")
        unique_columns = set()
        for index in existing_indexes:
            if index.unique:
                for column in index.columns:
                    unique_columns.add(column)
        return list(unique_columns)

    async def insert_data(self, model, data, is_many=True):
        uniques = await self.get_uniques(model)
        # 冲突时值需要更新的字段
        preserve = {"_modified_time": model._modified_time}   # 糙快猛，有系统字段更新时间更新， insert才会返回pk
        for name in data[0].keys():
            field = model._meta.fields.get(name)
            if field.column_name not in uniques:
                preserve.update({field.column_name: field})
        action = 'UPDATE' if preserve else 'IGNORE'  
        if is_many:
            query = model.insert_many(data).on_conflict(action, preserve=list(preserve.values()))
        else:
            query = model.insert(data[0]).on_conflict(action, preserve=list(preserve.values()))
            # 因为有系统字段更新时间更新，所以这里conflict的时候会update并返回pk
        query._run_model_event = True
        return await engine.userdb.objs.execute(query, direct_insert_serial=True)

class OldEntityConnector:
    def __init__(self, app_uuid, connector_define, app_config, tenants_config, loop=None) -> None:
        self._loop = loop
        self.app_env_name = os.environ.get("APP_ENV_NAME", "").rstrip(";")
        self.app_uuid = app_uuid
        self.connector_define = connector_define
        self.app_config = app_config
        self.tenant_uuid = None # 租户id需要写道环境变量里
        self.tenants_config = {self.tenant_uuid: tenants_config.pop(self.tenant_uuid, {})} if self.tenant_uuid else tenants_config   # 解析前： 租户配置，解析后：租户完整配置
        self.connector_config_app = {}   # 解析后的连接器app配置

        self.uuid = connector_define.get('uuid', '')
        self.sid = str(uuid.uuid5(uuid.UUID(self.app_uuid), "".join([self.tenant_uuid or "", self.uuid]))).replace("-", "")
        self.client_connector = ClientConnector(self.sid)
        self.client_connector.on_result = self.on_result

        self.sub_topics = list()
        self.callback_topics = list()
        self.sync_out_model_map = defaultdict(dict)     # 假定租户不能修改绑定的模型
        self.sync_in_model_map = defaultdict(dict)

        self.wait_tasks = list()

    def __hash__(self):
        return hash(f"connector {self.sid}")

    def __repr__(self) -> str:
        return f"lemon_connector_{self.uuid}_{self.connector_define.get('name', '')}"

    def get_editor_value(self, value_define, app_config, tenant_config):
        editable = value_define.get("editable", 0)
        if editable == 1:
            value_editor_define = app_config
        elif editable == 2:
            value_editor_define = tenant_config
        else:
            value_editor_define = {}

        return LemonValueEditor(**value_editor_define).value if value_editor_define else value_define["default"]

    async def on_result(self, msg):
        app_log.debug(msg)

    async def create_connector(self):
        #先调用调用系统init，然后connector自定义的init
        from tests.document.rc_connector import rc_connector
        self.rc_instance = RuleChain(rule_dict=rc_connector.to_dict(), connector=self.client_connector)
        await self.rc_instance.start()
        app_log.info(f"connector machine_id: {(self.rc_instance.lsm.machine_id)}, connector: {self.sid}")

        try:
            await self.analyze_config()
            await self._init_ct_event()
            self._init_event_actor()
            await self._init_sys_event()
            await self._init_user_func()
            await self.create_model_change_timer()
        except:
            app_log.error(traceback.format_exc())

    async def analyze_config(self):
        # todo: 优化
        self.tenants_config.update({"*": {}})
        for tenant_uuid, tenant_config in self.tenants_config.items():
            app_basic_config = tenant_config.get("basic_config", {})
            tenant_basic_config = tenant_config.get('basic_config', {})
            basic_config = {}
            for basic_ in self.connector_define.get("basic_config", []):
                key = basic_["key"]
                value = self.get_editor_value(basic_, app_basic_config.get(key, {}), tenant_basic_config.get(key, {}))
                basic_config.update({key: value})

            app_action = tenant_config.get("action", {})
            tenant_action = tenant_config.get('actoin', {})
            ct_action_config = []
            for action_ in self.connector_define.get("action", []):
                action_id = action_.get("id")
                action_app_config = app_action.get(action_id, {})
                action_tenant_config = tenant_action.get(action_id, {})
                action_config = {
                    "id": action_id
                }
                
                action_type = action_.get("action_type")
                action_config.update({"action_type": action_type})
                if action_type == 2:  # sync
                    for key in ["full_sync_interval", "delta_sync_interval"]:
                        value_define = action_.get(key)
                        if value_define:
                            action_config.update({
                                key:
                                self.get_editor_value(value_define, action_app_config.get(key, {}),
                                                        action_tenant_config.get(key, {}))
                            })
                    action_config.update({"incoming": action_.get("incoming"),
                        "outward": action_.get("outward")})
                    tables_config = []
                    tables_app_config = action_app_config.get("tables", {})
                    tables_tenant_config = action_tenant_config.get("tables", {})
                    for table_define in action_.get("tables", []):

                        table_id = table_define.get("id")
                        table_dependences = {}
                        table_app_config = tables_app_config.get(table_id, {})
                        table_tenant_config = tables_tenant_config.get(table_id, {})
                        direction = table_define.get("direction", 0)
                        table_config = {"id": table_id, "direction": direction, "dependences": table_dependences}
                        table_config.update({"conversion": table_app_config.get("conversion", {})})
                        table_config.update({"bindlist": table_app_config.get("bindlist", [])})
                        
                        table_config.update({"constrain": table_define.get("constrain", {})})
                        table_bind_to = table_app_config.get("bindto")
                        table_config.update({"bindto": table_bind_to})
                        
                        # source_model = engine.uuids.model_uuid_map.get(table_app_config.get("data_source"))
                        table_config.update({"data_source": table_app_config.get("data_source", {})})
                        
                        model = engine.uuids.model_uuid_map.get(table_bind_to)
                        app_log.debug(f"{table_id} bind model {model}, {table_bind_to}")
                        if direction == 1:  # lemon to 外部
                            self.sync_out_model_map[action_id].update({(table_id, model): table_config})
                        else:
                            self.sync_in_model_map[action_id].update({table_id: model})
                        config_define_keys = ["table_name", "sync_type", "trigger"]     # ? trigger在调用的时候计算？
                        for key in config_define_keys:
                            value_define = table_define.get(key)
                            if value_define:
                                table_config.update({
                                    key:
                                    self.get_editor_value(value_define, table_app_config.get(key, {}),
                                                        table_tenant_config.get(key, {}))
                                })
                        fields_config = []
                        fields_app_config = table_app_config.get("fields", {})
                        fields_tenant_config = table_tenant_config.get("fields", {})
                        

                        for idx, field_define in enumerate(table_define.get("fields", [])):
                            field_id = str(field_define.get('id', idx))
                            field_type = field_define.get("type")
                            field_config = {"id": field_id}
                            if field_type is not None:
                                field_config.update({"type": field_type})
                            foreignkey = field_define.get("foreignkey", [])
                            if foreignkey:
                                dependent_table_id = foreignkey[0]
                                table_dependences[field_id] = dependent_table_id
                            field_config.update({"foreignkey": foreignkey})
                            field_config.update({"condition_foreignkey": field_define.get("condition_foreignkey", {})})
                            field_config.update({"constrain": field_define.get("constrain")})
                            
                            field_app_config = fields_app_config.get(field_id, {})
                            field_tenant_config = fields_tenant_config.get(field_id, {})
                            field_config.update({'bindto': field_app_config.get("bindto")})
                            field_config.update({'path': field_app_config.get("path", [])})
                            field_config.update({'rel_bindto': field_app_config.get("rel_bindto")})
                            field_config.update({'bindlist': field_app_config.get('bindlist')})
                            field_config.update({"conversion": field_app_config.get("conversion")})
                            field_config.update({"default": field_app_config.get("default")})
                            field_name_value = self.get_editor_value(field_define.get("field_name", {}),
                                                                     field_app_config.get("field_name", {}),
                                                                     field_tenant_config.get("field_name", {}))
                            field_config.update({"field_name": field_name_value})

                            

                            fields_config.append(field_config)
                        table_config.update({"fields": fields_config})

                        tables_config.append(table_config)

                    joins_config = []
                    joins_app_config = action_app_config.get("joins", {})
                    joins_tenant_config = action_tenant_config.get("joins", {})
                    for join_define in action_.get("joins", []):
                        join_id = join_define.get("id")
                        join_app_config = joins_app_config.get(join_id, {})
                        join_tenant_config = joins_tenant_config.get(join_id, {})
                        join_config = {"id": table_id}
                        join_config.update(join_define)
                        join_bind = join_app_config.get("bindto")
                        join_config.update({"bindto": join_bind})
                        join_config.update({"query_action": join_define.get("query_action", 0)})
                        fields_config = []
                        fields_app_config = join_app_config.get("fields", {})
                        fields_tenant_config = join_tenant_config.get("fields", {})
                        for idx, field_define in enumerate(join_define.get("fields", [])):
                            field_id = str(field_define.get("id", idx))
                            field_config = {"id": field_id}
                            field_config.update(field_define)
                            field_app_config = fields_app_config.get(field_id, {})
                            field_config.update({'bindto': field_app_config.get("bindto")})
                            field_config.update({"conversion": field_app_config.get("conversion")})
                            field_config.update({'bindlist': field_app_config.get('bindlist')})
                            field_config.update({'rel_bindto': field_app_config.get("rel_bindto")})
                            field_config.update({'default': field_app_config.get("default")})
                            field_alias = field_define.get("alias")
                            if field_alias is None:
                                field_name = None
                                field_tid = field_define.get("table_id")
                                field_fid = field_define.get("field_id")
                                for table_define in tables_config:
                                    if str(table_define.get("id")) == str(field_tid):
                                        for idx, field_define in enumerate(table_define.get("fields", [])):
                                            if str(field_define.get("id", idx)) == str(field_fid):
                                                field_name = field_define.get("field_name")
                                if not field_name:
                                    continue
                            else:
                                field_name = field_alias
                            field_config.update({"field_name": field_name})
                            fields_config.append(field_config)
                        join_config.update({"fields": fields_config})
                        joins_config.append(join_config)

                    action_config.update({"joins": joins_config})
                    action_config.update({"tables": tables_config})
                ct_action_config.append(action_config)
            connector_tenant_config = dict(ct_uuid=self.uuid, basic_config=basic_config, action=ct_action_config, version='-1')
            self.tenants_config.update({tenant_uuid: connector_tenant_config})
            await self._init_agent_config(tenant_uuid, connector_tenant_config)
        self.connector_config_app = self.tenants_config["*"]

    async def create_model_change_timer(self):
        from runtime.core.timer import LemonTimer, IntervalType, ValueEditorType
        actions = { action["id"]: action for action in self.connector_config_app["action"]}
        app_log.debug(self.sync_out_model_map)
        for action_id, models_map in self.sync_out_model_map.items():
            action = actions[action_id]
            
            full_interval = action["full_sync_interval"]
            delta_interval = action["delta_sync_interval"]
            # full_timer = LemonTimer(**dict(interval_type=IntervalType.MINUTE, interval_value=full_interval,
            #         uuid=lemon_uuid(), name="full table sync", type=TimerType.ONCE, start_timestamp={
            #                     "type": ValueEditorType.EXPR, "value_type": VariableType.INTEGER, 
            #                     "expr": "time.time()"
            #                 }))
            # delta_timer = LemonTimer(**dict(interval_type=IntervalType.SECOND, interval_value=delta_interval,
            #         uuid=lemon_uuid(), name="delta table sync", type=TimerType.ONCE, start_timestamp={
            #                     "type": ValueEditorType.EXPR, "value_type": VariableType.INTEGER, 
            #                     "expr": "time.time()"
            #                 }))
            app_log.info(models_map)
            if not models_map:
                continue
            sorted_models = self.topo_sort_models(models_map)
            app_log.info(sorted_models)
            if not sorted_models:
                continue
            self._loop.create_task(self.handle_model_sync_out(action_id, sorted_models, full_interval * 60, True))
            self._loop.create_task(self.handle_model_sync_out(action_id, sorted_models, delta_interval, False))
            # full_timer.add_on_timer(partial(self.handle_model_sync_out, action_id, sorted_models, full_timer, True))
            # delta_timer.add_on_timer(partial(self.handle_model_sync_out, action_id, sorted_models, delta_timer, False))
            # full_timer.start()
            # delta_timer.start()
            # app_log.info((full_timer, delta_timer))

    def topo_sort_models(self, model_map):
        degrees = defaultdict(int)
        num = 0
        model_dependent_map = {}
        model_id_map = {}
        for u, table_config in model_map.items():
            if not table_config.get("bindto"):
                continue
            num += 1
            table_id, model = u
            degrees[table_id] += 1
            model_dependent_map.update({table_id: table_config})
            model_id_map.update({table_id: model})
            table_dependences = table_config["dependences"]
            for _, dt_id in table_dependences.items():
                if dt_id == table_id:
                    degrees[dt_id] += 1
            
        
        Q = [u for u in degrees if degrees[u] == 1]
        app_log.info((Q,model_dependent_map))
        seq = OrderedDict()
        while Q:
            u = Q.pop()
            model = model_id_map[u]
            table_config = model_dependent_map[u]
            seq[model] = table_config
            table_dependences = table_config["dependences"]
            for _, table_id in table_dependences.items():
                degrees[table_id] -= 1
                if degrees[table_id] == 0:
                    Q.append(table_id)
        app_log.info(seq)
        return seq if len(seq) == num else OrderedDict()

    async def send_event(self, event_uuid, event_args):
        event_topic = self.rc_instance.lsm.build_event_topic(self.app_env_name, event_uuid)
        event_msg = LemonMessage(data={"event_args": event_args}, topic=event_topic)
        app_log.info(f"send event: event_topic {event_topic}")
        # await self.client_connector.publish(event_topic, event_msg)
        asyncio.create_task(self.client_connector.call(event_msg))

    def slice_pack(self, data: str):
        zdata = zlib.compress(data.encode())
        data_len = len(zdata)
        per_packet_len = 1024 * 100
        total_packets = int(data_len / per_packet_len) + 1 if (data_len % per_packet_len) > 0 else int(data_len /
                                                                                                       per_packet_len)
        data_md5 = hashlib.md5(zdata).hexdigest()
        app_log.debug((data_md5, total_packets, data_len))
        pos = 0
        current = 0
        while pos < data_len:
            packet_len = per_packet_len if data_len - pos > per_packet_len else data_len - pos
            pack_data = zdata[pos:(pos + packet_len)]
            pack = {
                "pos": pos,
                "data": base64.b64encode(pack_data).decode(),
                "total": total_packets,
                "compress": 1,
                "full_md5": data_md5
            }
            yield (current, ujson.dumps(pack))
            current += 1
            pos += packet_len

    async def send_connector_msg(self, tenant_uuid, msg_type, msg):
        # topic = self.rc_instance.lsm.build_topic_func(self.rc_instance.lsm.machine_id, "", connector_uuid)
        # topic = f"lemon-connector-outward-developer-{self.app_uuid}-{tenant_uuid}-{self.uuid}"
        # msg = LemonMessage(data=msg, topic=topic)
        # app_log.debug(msg)
        reply_topic = "/".join(["", "dta", msg_type, self.app_uuid + self.app_env_name, tenant_uuid, self.uuid])
        async with MqttClient(engine.app.config.MQTT_HOST) as client:
            await client.publish(reply_topic, ujson.dumps(msg), qos=1)

    async def send_connector_action_data(self, tenant_uuid, msg_type, msg, action_id, *, action_type=None, table_data_type=None):
        topic_keyword = ["", "dta", msg_type, self.app_uuid + self.app_env_name, tenant_uuid, self.uuid, action_id]
        if action_type:
            topic_keyword.append(action_type)
        if table_data_type:
            topic_keyword.append(table_data_type)
        reply_topic = "/".join(topic_keyword)
        async with MqttClient(engine.app.config.MQTT_HOST) as client:
            for num, pack in self.slice_pack(ujson.dumps(msg)):
                app_log.debug((reply_topic, pack))
                await client.publish(reply_topic, pack, qos=1)

    async def send_commit_msg(self, commit_info):
        commit_topic = commit_info.get("topic")
        commit_id = commit_info.get("commit_id")
        msg = LemonMessage(commit_id=commit_id)
        app_log.debug(f"commit msg: {msg}, topic: {commit_topic}")
        await self.client_connector.publish(commit_topic, msg)

    async def _init_agent_config(self, tenant_uuid, config):
        key = f"connector_config_cache{self.app_env_name}:" + "_".join([self.app_uuid, self.uuid])
        await engine.redis.hset(key, tenant_uuid, ujson.dumps(config))
        event_args = {"agent_config": config}
        await self.send_connector_msg(tenant_uuid, "command_reply", event_args)

    async def _init_sys_event(self):
        event_uuid = UUID.rc_connector.event_init
        event_args = {}
        app_log.info(f"connector init")
        await self.send_event(event_uuid, event_args=event_args)

    async def _init_ct_event(self):
        if self.tenant_uuid:
            actions = self.tenants_config[self.tenant_uuid]["action"]
        else:
            actions = self.connector_config_app["action"]
        topic_map = {}
        for action in actions:
            action_type = action.get("action_type", 0)
            action_id = action.get("id")
            if action_type in [1, 2]:
                
                sub_uuid = '-'.join([self.uuid, action_id])
                topic = self.rc_instance.lsm.build_event_topic(self.app_env_name, sub_uuid)
                self.sub_topics.append(topic)
                topic_map.update({sub_uuid: [topic]})
            
        app_log.debug(f"connector subtopics: {self.sub_topics}")
        redis_key = ":".join([f"connector_event_map{self.app_env_name}", self.app_uuid])
        tenant_uuid = self.tenant_uuid or "*"
        await engine.redis.hset(redis_key, tenant_uuid, ujson.dumps(topic_map))

    def _init_event_actor(self):
        topics = list()
        topics.extend(self.sub_topics)

        topics = set(topics)
        for topic in topics:
            app_log.debug(f"add topic: {topic}")
            self.add_callback(topic, self.inbounds)

    async def _init_user_func(self):
        func_uuid = self.connector_config_app.get("init_func")
        if func_uuid:
            arg_dict = {}
            return await GlobalVars.FuncRun.run_func(func_uuid, lsm=self.rc_instance.lsm, **arg_dict)

    def add_callback(self, topic, func):
        self.client_connector.add_callback(topic, func)
        self.callback_topics.append(topic)

    async def inbounds(self, msg: LemonMessage):
        # 订阅消息入口
        app_log.info(f"topic: {msg.topic}")
        app_log.info(f"data: {msg}")
        response_info = msg.response_info
        await self.client_connector.publish(response_info["topic"], {"topic": msg.topic, "commit_id": response_info["commit_id"]})
        event_type = msg.event_type
        if event_type != "action":
            return
        if msg.action_type == "table":
            await self.handle_sync_message(msg)

    async def get_alive_tenants(self):
        # return ["fb12334b150a576ca776bdefa17198fd"]
        key = "_".join(["alive_dta_tenants", self.app_uuid + self.app_env_name, self.uuid])
        timestamp = int(time.time()*100)
        await engine.redis.zremrangebyscore(key, max=timestamp-100*60*30)   #超过30分钟未发ping的移除
        return await engine.redis.zrange(key)   

    async def handle_model_sync_out(self, action_id, models, interval, full=True):
        # todo: if other sync_out in processing, stop current
        from runtime.core.data_preprocess import AsyncDataPreprocess
        from runtime.core.utils import build_relation_query
        from runtime.modelapi.entity_utils import join_user_table
        from baseutils.const import SystemField
        from runtime.utils import build_model_field_path
        app_log.debug(f"handle model sync out {'full' if full else 'delta'}")
        tenants = await self.get_alive_tenants()
        for tenant_uuid in tenants:
            current_user = await CurrentUserModel.from_dict({"tenant_uuid": tenant_uuid})
            LemonContextVar.current_user.set(current_user)
            self.rc_instance.lsm.lemon.system.current_user = current_user
            redis_key = "_".join(["sync_out_cache_info" + self.app_env_name + ":", self.app_uuid, tenant_uuid, self.uuid]) 
            out_data = []
            pk_cache_map = {}
            for model, table_config in models.items():
                try:
                    data_source = table_config.get("data_source", {})
                    source_model = engine.uuids.model_uuid_map.get(data_source.get("model"))
                    app_log.info(f"source_model: {source_model}")
                    if source_model:
                        model = source_model
                    if not model:
                        continue
                    start_node = lemon_model_node(model._meta.table_name)
                    preprocessing_dict = data_source.get("preprocessing", {})
                    if preprocessing_dict:
                        data_query = await AsyncDataPreprocess(model, preprocessing_dict)()
                    else:
                        data_query = model.select()
                    table_id = table_config["id"]
                    sync_type = table_config["sync_type"]
                    # where_expr = table_config["trigger"]
                    redis_field = "_".join([action_id, table_id])
                    # if isinstance(where_expr, str):
                    #     where_expr = SQL(where_expr)
                    relation_column_dict = {}
                    alias_columns = []
                    all_models = {}
                    returning = []

                    for field in table_config["fields"]:
                        bindto = field.get("bindto", "")
                        path = field.get("path", [])
                        column_model = engine.uuids.field_uuid_map.get(bindto)
                        if not column_model:
                            continue
                        column = lemon_field(bindto)
                        if not column:
                            continue
                        field_name = field.get("field_name")
                        if column_model == model :      # and not path
                            column_alias = column.alias(field_name)
                            alias_columns.append(column_alias)
                            returning.append(column_alias)
                        else:
                            column_model_uuid = column_model._meta.table_name
                            path_model_uuid = build_model_field_path(column_model_uuid, path)
                            self_referential = False if column_model == model else False    # 自关联导出有bug，不管
                            model_column_info = relation_column_dict.setdefault(
                                path_model_uuid, {
                                    "path": [], "columns": [], "model_uuid": column_model_uuid, 
                                    "self_referential": self_referential, "field_uuid_dict": {}})
                            model_column_info["model_uuid"] = column_model_uuid
                            model_columns = model_column_info.get("columns")
                            if column.column_name != bindto:
                                model_column_info["field_uuid_dict"].update({column.column_name: bindto})
                            if not model_columns:
                                model_column_info["path"] = path
                            column_alias = column.alias(field_name)
                            model_columns.append(column_alias)
                            returning.append(column_alias)
                            model_columns = list(set(model_columns))
                            model_column_info["columns"] = model_columns
                    relation_query_dict = build_relation_query(model, relation_column_dict, auto_alias=False)

                    # 构造 data_query
                    start_node = lemon_model_node(model._meta.table_name)
                    # 防止多次join,以后传了path可以去掉
                    query_joins = data_query._joins
                    all_join_model = set()
                    for left_model, join_models in query_joins.items():
                        all_join_model.add(left_model)
                        for join_tuple in join_models:
                            all_join_model.add(join_tuple[0])
                    app_log.info(f"all_join_model: {all_join_model}, all_model: {all_models}")
                    for m, path in all_models.items():
                        if m != model and m not in all_join_model:
                            if path:
                                data_query = build_join(start_node, end_node=None, path=path, query=data_query, rename_model=True)
                            else:
                                this_node = lemon_model_node(m._meta.table_name)
                                data_query = build_join(start_node, this_node, query=data_query)
                    app_log.info(f"data_query: {data_query}")
                    app_log.info(f"query joins: {data_query._joins}")
                    alias_columns = list(set(alias_columns))
                    data_query = data_query.select(*alias_columns)
                    # if where_expr:
                    #     data_query = data_query.where(where_expr)
                    if sync_type in [0, 2] and full == False:
                        # 增量
                        pk = await engine.redis.hget(redis_key, redis_field)
                        # app_log.info(f"pk {pk}, {type(pk)}")
                        if pk is None:
                            pk = 0
                        pk = int(pk)
                        pk_query = data_query.clone() 
                        pk_query = pk_query.select(fn.MAX(model._meta.primary_key).alias("max_pk"))
                        # if where_expr:
                        #     pk_query = pk_query.where(where_expr)
                        app_log.info(f"pk_query: {pk_query}")
                        max_pk = await engine.userdb.objs.scalar(pk_query)
                        app_log.info(f"maxpk: {max_pk}, pk: {pk}")
                        if not max_pk:
                            max_pk = 0
                        max_pk = int(max_pk)
                        pk_cache_map.update({redis_field: max_pk}) 
                        if max_pk <= pk:
                            continue
                          
                        data_query = data_query.where(model._meta.primary_key>pk)
                    elif sync_type in [0, 1] and full == True:
                        # 全量
                        pass
                    else:
                        continue

                    app_log.info(f"data_query: {data_query}")
                    data_list = await engine.userdb.objs.execute(data_query.dicts())

                    source_in_page, pk_list, pk_index_dict = [], [], {}

                    for index, data_dict in enumerate(data_list, start=0):
                        # app_log.debug(f"data_dict: {data_dict}")
                        pk = data_dict.get(model.id.name)
                        pk_list.append(pk)
                        pk_index_dict[pk] = index
                        source_line = dict()
                        source_line.update(data_dict)
                        app_log.info(f"source_line: {source_line}")
                        source_in_page.append(source_line)

                    # 关联模型的查询
                    for model_uuid, query_info in relation_query_dict.items():
                        query = query_info.get("query")
                        is_many = query_info.get("is_many")
                        query = query.where(model.id.in_(pk_list)).dicts()
                        app_log.info(f"relation_query: {query}")
                        data_list = await engine.userdb.objs.execute(query)
                        model_pk_data_dict = {}
                        for data_dict in data_list:
                            # app_log.debug(f"data_dict: {data_dict}")
                            pk = data_dict.get(model.id.name)
                            # 确定关联的数据，对应的是哪个 pk
                            pk_data_dict = model_pk_data_dict.setdefault(pk, {})
                            if is_many:
                                for data_uuid, data in data_dict.items():
                                    if data_uuid != "id":
                                        pk_data_list = pk_data_dict.setdefault(
                                            data_uuid, [])
                                        pk_data_list.append(data)
                            else:
                                pk_data_dict.update(data_dict)
                        # app_log.info(f"pk_data_dict: {pk_data_dict}")
                        for pk, pk_data_dict in model_pk_data_dict.items():
                            # 获取 pk 在 list_in_page 的索引，确认行；
                            # index 就是列的索引
                            pk_datalist_index = pk_index_dict.get(pk, None)
                            if pk_datalist_index is not None:
                                source_in_page[pk_datalist_index].update(pk_data_dict)
                    

                    app_log.debug(f"out source_in_page: {source_in_page and source_in_page[0]}")
                    new_data = []
                    for d in source_in_page:
                        # 只传有bindto的
                        row = {}
                        for f in returning:
                            new_name = f._alias
                            v = d.get(new_name, None)
                            row.update({new_name: v})
                        new_data.append(row)
                    if new_data:
                        out_data.append({table_id: new_data})
                    app_log.info(f"out data: {out_data}")
                except:
                    app_log.error(traceback.format_exc())
            # app_log.info(f"pk_map: {pk_cache_map}")
            if out_data:
                # app_log.info(out_data)
                await self.send_connector_action_data(tenant_uuid, "action_out", out_data, 
                        action_id=action_id, action_type="table", table_data_type="full" if full else "delta")
            app_log.info(f"pk_map: {pk_cache_map}")
            await asyncio.gather(*[engine.redis.hset(redis_key, key, pk_cache_map[key]) for key in pk_cache_map])
        await asyncio.sleep(interval)
        self._loop.create_task(self.handle_model_sync_out(action_id, models, interval, full))

    async def create_uniques(self, model, unique_columns):
        not_existing_uniques = []
        with engine.userdb.real_database.allow_sync():
            await asyncio.get_event_loop().run_in_executor(None, partial(engine.userdb.real_database.connection().ping, True))
            existing_indexes = await asyncio.get_event_loop().run_in_executor(None, partial(engine.userdb.real_database.get_indexes, model._meta.table_name, schema='public'))
        app_log.info(f"existing indexes: {existing_indexes}")
        for idx, uniques in enumerate(unique_columns):
            unique_column_name_set = set([u.column_name for u in uniques])
            found = False
            for index in existing_indexes:
                if index.unique and set(index.columns) == unique_column_name_set:
                    found = True
                    break
            if not found: 
                not_existing_uniques.append(uniques)
        app_log.info(not_existing_uniques)
        for u in not_existing_uniques:
            try:
                manager = SchemaManager(model)
                u = u + [model.tenant_uuid]
                ctx = manager._create_index(ModelIndex(model, u, unique=True))
                query = RawQuery(sql=''.join(ctx._sql), params=ctx._values, _database=engine.userdb.database)
                app_log.info(query)
                await engine.userdb.objs.execute(query)
            except:
                app_log.error(traceback.format_exc())
    
    async def handle_sync_message(self, msg):
        tenant_uuid = msg.tenant_uuid
        _start = time.time()
        current_user = await CurrentUserModel.from_dict({"tenant_uuid": tenant_uuid})
        LemonContextVar.current_user.set(current_user)
        action_id = msg.action_id
        table_data_type = msg.table_data_type
        if tenant_uuid in self.tenants_config:
            connector_configs = self.tenants_config[tenant_uuid]
        else:
            connector_configs = self.connector_config_app
        actions = connector_configs["action"]
        action = {}
        for action_ in actions:
            if action_["id"] == action_id:
                action = action_
                break
        if not action:
            return

        
        joins = action.get("joins", [])
        join_configs = {join.get("id"): copy.deepcopy(join) for join in joins}
        app_log.info(f'join_configs: {join_configs}')
        tables = action.get("tables", [])
        table_configs = {table.get("id"): copy.deepcopy(table) for table in tables}

        
        model_id_map = self.sync_in_model_map[action_id]

        field_cache_pk = defaultdict(dict)

        for tid in table_configs:
            table_config = table_configs[tid]
            m_uuid = table_config.get("bindto")
            model = engine.uuids.model_uuid_map.get(m_uuid)
            app_log.info(f"model: {model}, {m_uuid}")
            if not model:
                continue
            table_config.update({"model": model})
            table_fields = table_config.get("fields", [])
            app_log.info(table_fields)
            table_field_names = {}
            table_field_ids = {}
            unique_columns = []
            row_bindlist = table_config.get("bindlist", [])
            for bind in row_bindlist:
                f_uuid = bind.get('bindto')
                column = lemon_field(f_uuid)
                if not column:
                    continue
                bind.update({"column": column})
                rel_f_uuid = bind.get('rel_bindto')
                if rel_f_uuid is None:
                    continue
                rel_model = engine.uuids.field_uuid_map.get(rel_f_uuid)
                if rel_model:
                    rel_column = lemon_field(rel_f_uuid)
                    bind.update({"rel_column": rel_column})
            for idx, field_config in enumerate(table_fields):
                f_id = str(field_config.get("id", idx))
                table_field_ids.update({f_id: field_config})
                f_name = field_config.get("field_name")
                table_field_names.update({f_name: field_config})
                bindlist = field_config.get("bindlist", [])
                if bindlist:
                    for bind in bindlist:
                        f_uuid = bind.get('bindto')
                        column = lemon_field(f_uuid)
                        if not column:
                            continue
                        bind.update({"column": column})
                        rel_f_uuid = bind.get('rel_bindto')
                        if rel_f_uuid is None:
                            continue
                        rel_column = lemon_field(rel_f_uuid)
                        bind.update({"rel_column": rel_column})
                
                f_uuid = field_config.get("bindto")
                column = lemon_field(f_uuid)
                if not column:
                    continue
                field_config.update({"column": column})

                rel_f_uuid = field_config.get('rel_bindto')
                rel_model = engine.uuids.field_uuid_map.get(rel_f_uuid)
                if rel_model:
                    rel_column = lemon_field(rel_f_uuid)
                    field_config.update({"rel_column": rel_column})
                
                constrain_define = field_config.get("constrain", [])
                if "pk" in constrain_define:
                    model._extern_pk_column = column
                    app_log.info(f"extern_pk_column: {column}")
                    unique_columns.append([column])
                elif "unique" in constrain_define:
                    model._extern_unique_column = column
                    unique_columns.append([column])
             
            constrain_fields = table_config.get("constrain", {})
            unique_field_ids = constrain_fields.get("unique", [])
            unique_fields = [table_field_ids.get(fid, {}) for fid in unique_field_ids]
            if unique_fields:
                unique_columns.append([f["column"] for f in unique_fields])
            table_config.update({"unique_columns": unique_columns})
            table_config.update({"field_names": table_field_names})
            table_config.update({"field_ids": table_field_ids})
            model._unique_columns = unique_columns

            #! 强制添加唯一约束
            # await self.create_uniques(model, unique_columns)

        app_log.info(f"table_configs: {table_configs}")  

        for jid in join_configs:
            join_config = join_configs[jid]
            bindto = join_config.get("bindto")
            j_bind_model = engine.uuids.model_uuid_map.get(bindto)
            # app_log.info((bindto, j_bind_model))
            if not j_bind_model:
                continue
            j_field_names = {}
            j_field_ids = {}
            j_unique_columns = []
            for idx, field_config in enumerate(join_config.get("fields", [])):
                fid = str(field_config.get("id"))
                table_id = field_config.get("table_id")
                field_id = field_config.get("field_id")
                j_field_names.update({field_config.get("field_name"): field_config})
                j_field_ids.update({fid: field_config})

                bindlist = field_config.get("bindlist", [])
                if bindlist:
                    for bind in bindlist:
                        f_uuid = bind.get('bindto')
                        column = lemon_field(f_uuid)
                        if not column:
                            continue
                        bind.update({"column": column})
                        rel_f_uuid = bind.get('rel_bindto')
                        if rel_f_uuid is None:
                            continue
                        
                        rel_model = engine.uuids.field_uuid_map.get(rel_f_uuid)
                        rel_column = lemon_field(rel_f_uuid)
                        bind.update({"rel_column": rel_column})
                
                f_bind = field_config.get("bindto")
                f_column = lemon_field(f_bind)
                
                field_config.update({"column": f_column})
                rel_f_uuid = field_config.get('rel_bindto')
                rel_model = engine.uuids.field_uuid_map.get(rel_f_uuid)
                if rel_model:
                    rel_column = lemon_field(rel_f_uuid)
                    field_config.update({"rel_column": rel_column})
                
            join_config.update({"field_ids": j_field_ids})
            join_config.update({"field_names": j_field_names})
            unique_define = join_config.get("constrain", {}).get("unique", [])
            unique_column = [j_field_ids.get(fid, {}).get("column") for fid in unique_define]
            j_unique_columns.append(unique_column)
            if getattr(j_bind_model, "_unique_columns", None) is not None:
                j_bind_model._unique_columns.extend(j_unique_columns)
            else:
                j_bind_model._unique_columns = j_unique_columns

            #! 强制添加唯一约束
            # await self.create_uniques(j_bind_model, j_unique_columns)

        app_log.info(f"join_configs: {join_configs}")

        cid = msg.data_id
        cache_num = await engine.redis.zcard(cid)
        if table_data_type in ["full", "join"]:
            start = cache_num - 1
        else:
            start = 0
        for current_index in range(start, cache_num):
            app_log.info((current_index, cache_num))
            zdata_list = await engine.redis.zrange(cid, start=current_index, stop=-1, encoding=None)

            for val in zdata_list:
                tables_data = ujson.loads(zlib.decompress(val)).get("data", [])
                if tables_data:
                    try:
                        for data in tables_data:
                            if not data:
                                continue
                            # table_tasks = set()
                            t_id = list(data.keys())[0]
                            t_data = list(data.values())[0]
                            app_log.info(f"tid: {t_id}, tdata: {t_data and t_data[0]}")
                            row_len = len(t_data[0]) if t_data else 0
                            if table_data_type == "join":
                                table_config = join_configs.get(t_id, {})                          
                            else:
                                table_config = table_configs.get(t_id, {})

                            # if table_config.get('sync_type') == 3:
                            #     continue
                            lemon_model_uuid = table_config.get("bindto")
                            lemon_model = engine.uuids.model_uuid_map.get(lemon_model_uuid)
                            app_log.info(lemon_model)
                            if not lemon_model:
                                continue
                            new_table_data = []
                            query_action = table_config.get("query_action", 0)

                            row_conversion = table_config.get("conversion", {})
                            row_bindlist = table_config.get("bindlist", [])
                            conversion_type = row_conversion.get("type", 0)
                            if conversion_type == 0:
                                editor_define = row_conversion.get("editor", {})
                                editor = LemonValueEditor(**editor_define)
                                editor.set_globals(self.rc_instance.lsm)
                            else:
                                func_uuid = row_conversion.get("func")

                            for idx, row in enumerate(t_data):
                                if len(row) != row_len:
                                    continue
                                self.rc_instance.lsm.lemon.system.extern_data = row
                                self.rc_instance.lsm.lemon.system.current_user = current_user   
                                new_row = {}
                                if row_conversion and row_bindlist:
                                    if conversion_type == 0:
                                        value_list = editor.value
                                    else:
                                        value_list = await FuncRun.run_func(func_uuid, row, lsm=self.rc_instance.lsm)
                                    for vidx, v in enumerate(value_list):
                                        b_column = row_bindlist[vidx].get("column")
                                        rel_column = row_bindlist[vidx].get("rel_column")
                                        if not b_column:
                                            continue
                                        path = row_bindlist[vidx].get("path", None)
                                        new_value = await self.get_new_column_value(b_column, rel_column, path, v, None, field_cache_pk)
                                        new_row.update({b_column.name: new_value})
                                else:
                                    for key, original_value in row.items():
                                        field_config = table_config.get("field_names").get(key, {})
                                        column = field_config.get("column")
                                        bindlist = field_config.get("bindlist", [])
                                        # app_log.info((column, original_value))
                                        # app_log.info(column is None)
                                        if column is None and not bindlist:
                                            continue
                                    
                                        conversion = field_config.get("conversion", {})
                                        if conversion:
                                            value_editor = LemonValueEditor(**conversion)
                                            value_editor.set_globals(self.rc_instance.lsm)
                                            value = value_editor.value
                                        else:
                                            value = original_value

                                        foreignkey_define = field_config.get("foreignkey")
                                        if not foreignkey_define:
                                            foreignkey_define_model = None
                                        else:
                                            rel_model_id = foreignkey_define[0]
                                            foreignkey_define_model = model_id_map.get(rel_model_id)
                                        if isinstance(value, list) and bindlist:
                                            for vidx, v in enumerate(value):
                                                b_column = bindlist[vidx].get("column")
                                                rel_column = bindlist[vidx].get("rel_column")
                                                path = bindlist[vidx].get("path", None)
                                                if b_column is None:
                                                    continue
                                                new_value = await self.get_new_column_value(b_column, rel_column, path, foreignkey_define_model, field_cache_pk)
                                                new_row.update({b_column.name: new_value})
                                        else:
                                            f_column = field_config.get("rel_column")
                                            path = field_config.get("path", None)
                                            new_value = await self.get_new_column_value(column, f_column, path, value, foreignkey_define_model, field_cache_pk)
                                            new_row.update({column.name: new_value})
                                        
                                   
                                # app_log.info(new_row)
                                new_table_data.append(new_row)
                                
                                if idx and idx % 10240 == 0:
                                    await self.insert_data(lemon_model, new_table_data, query_action, table_config)
                                    new_table_data = []    
                                
                            await self.insert_data(lemon_model, new_table_data, query_action, table_config)                          
                                    
                    except:
                        app_log.error(traceback.format_exc())
            
        await engine.redis.delete(cid)
        await self.send_commit_msg(msg.commit_info)
        app_log.debug(f"cost time:***************************** {time.time() - _start} ")

    async def get_new_column_value(self, b_column, rel_column, path, value, foreign_define_model, field_cache_pk):
        current_user = LemonContextVar.current_user.get()
        if current_user is not None:
            tenant_uuid = current_user.tenant_uuid
        else:
            tenant_uuid = None
        if value is not None and isinstance(b_column, LemonForeignKeyField) and rel_column and not foreign_define_model:
            path_str = ujson.dumps(path)
            rel_column_name = rel_column.model._meta.table_name + "*" + rel_column.column_name
            if path_str  not in field_cache_pk[rel_column_name]:
                field_cache_pk[rel_column_name][path_str] = dict()
            
            path_value_map = field_cache_pk[rel_column_name][path_str]
            # app_log.info(path_value_map)
            if value in path_value_map:
                pk_value = path_value_map[value]
                app_log.info(pk_value)    
            else:
                
                try:
                    if path:
                        pk_query = build_join(lemon_model_node(b_column.rel_model._meta.table_name), end_node=None, path=path)
                    else:
                        pk_query = b_column.rel_model
                    
                    app_log.info(pk_query)
                    pk_query = pk_query.select(b_column.rel_model._meta.primary_key).where(rel_column==value, b_column.rel_model.tenant_uuid==tenant_uuid)
                    app_log.info(pk_query)
                    pk_obj = await engine.userdb.objs.get(pk_query)
                    # app_log.info((column, rel_column,pk_obj))
                except:
                    app_log.error(traceback.format_exc())
                    pk_value = None
                else:
                    pk_value = pk_obj._pk
                path_value_map[value] = pk_value
            return pk_value

        elif foreign_define_model and value is not None:     # 是外键，并且外键值不为空
            rel_model = foreign_define_model
            pk_value = None
            if rel_model:
                extern_unique_column = getattr(rel_model, "_extern_pk_column", None)
                if extern_unique_column is None:
                    extern_unique_column = getattr(rel_model, "_extern_unique_column", None)
                extern_unique_column_name = extern_unique_column.model._meta.table_name + "*" + extern_unique_column.column_name
                if "direct"  not in field_cache_pk[extern_unique_column_name]:
                    field_cache_pk[extern_unique_column_name]["direct"] = dict()
                path_value_map = field_cache_pk[extern_unique_column_name]["direct"]
                if value in path_value_map:
                    pk_value = path_value_map[value]
                else:
                    try:
                        # todo: 所有外键一起取
                        pk_query = rel_model.select(rel_model._meta.primary_key).where(extern_unique_column==value, rel_model.tenant_uuid==tenant_uuid)
                        pk_obj = await engine.userdb.objs.get(pk_query)
                    except:
                        pk_value = None
                    else:
                        pk_value = pk_obj._pk
                    path_value_map[value] = pk_value
            return pk_value
            
        else:
            return value

    async def insert_data(self, model, data, query_action, table_config=None):  
        if not data:
            return    
        if query_action == 0:
            query = model.insert_many(data)

            # 冲突时值需要更新的字段
            preserve = {}
            for name in data[0].keys():
                field = model._meta.fields.get(name)
                preserve.update({field.column_name: field})
            
            preserve.pop(model._meta.primary_key.name, None)
            # 唯一字段保持不变
            unique_columns = table_config.get("unique_columns", []) if table_config else model._unique_columns
            for uniques in unique_columns:
                for u in uniques:
                    if not isinstance(u, Field):
                        continue
                    preserve.pop(u.column_name, None)

            # preserve.pop("externpk", None)
            if preserve:
                query = query.on_conflict(
                    preserve=list(preserve.values())
                )
            else:
                query = query.on_conflict('IGNORE')
            return await engine.userdb.objs.execute(query, direct_insert_serial=True)
        else:
            for d in data:
                where_condition = []
                columns = list(set(column for columns in model._unique_columns for column in columns))
                
                for column in columns:
                    if getattr(column, 'calculate_field', False):
                        name = 'id'
                        column = column.model._meta.primary_key
                    else:
                        name = column.name
                    v = d.pop(name, None)
                    # if v is None:
                    #     continue
                    where_condition.append(column==v)
                if where_condition:
                    query = model.update(**d).where(*where_condition)
                    app_log.info(query)
                    await engine.userdb.objs.execute(query, direct_insert_serial=True) 

        #     # delete_query = lemon_model.delete().where(lemon_model._outward_unique.not_in(out_ward_pk_values))
        #     # await engine.userdb.objs.execute(delete_query)
        #     await engine.userdb.objs.execute(query)
     

    async def start(self):
        app_log.debug(f"start {self}")
        await self.create_connector()
        # asyncio.run(self.create_connector())
        # self._loop.run(self.create_connector())


class LemonConnector:
    def __init__(self, app_uuid, connector_define, tenants_config, loop=None) -> None:
        self.instance = None
        if connector_define.get("version") == "-1":
            self.instance = OldEntityConnector(app_uuid, connector_define, {}, tenants_config, loop)
        else:
            self.instance = DcuConnector(app_uuid, connector_define, {}, tenants_config, loop)
        self.uuid = self.instance.uuid

    async def start(self):
        await self.instance.start()
