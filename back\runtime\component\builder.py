# -*- coding:utf-8 -*-

import abc
from typing import List
import weakref

from apps.ide_const import ComponentType

from runtime.component.base import (
    ComponentABC, BaseComponent, RootComponent, BaseContainerComponent,
    BaseDataComponent, BaseExtendComponent, BaseControlComponent,
    BaseInputComponent, BasePresentComponent, BaseButtonComponent,
    BaseNavigationComponent
)
from runtime.component.chart_container import Base<PERSON><PERSON>
from runtime.component.container import BaseDataContainerComponent, CardlistSubTable
from runtime.component.container import (
    RSelectContainer, RSelectPopupContainer, RTileContainer, DatagridSubTable,
    SubTable, RTableContainer, SelectRTableContainer, DeleteRTableContainer,
    RFormContainer, DatagridRTableContainer, TreelistRTableContainer, TreelistSubTable,
    TransferContainer, Datalist, Cardlist, Datagrid, Treelist
)
from runtime.component.base import CONTAINER_COMPONENT_CLASSES
from runtime.component.input import INPUT_COMPONENT_CLASSES
from runtime.component.present import PRESENT_COMPONENT_CLASSES
from runtime.component.button import BUTTON_COMPONENT_CLASSES
from runtime.component.navigation import NAVIGATION_COMPONENT_CLASSES
from runtime.component.extend import EXTEND_COMPONENT_CLASSES
from runtime.component.chart_container import CHART_COMPONENT_CLASSES
from runtime.component.container import DATA_CONTAINER_COMPONENT_CLASSESS
from runtime.component.react import ReactComponent
from runtime.component.page import Page
from runtime.core.connector import ClientConnector

DATA_CONTAINER_COMPONENT_CLASSESS += [ReactComponent]
BASE_CONTROL_COMPONENT_CLASS = (
    CONTAINER_COMPONENT_CLASSES + INPUT_COMPONENT_CLASSES +
    PRESENT_COMPONENT_CLASSES + BUTTON_COMPONENT_CLASSES +
    NAVIGATION_COMPONENT_CLASSES
)


class ComponentBuilderABC(abc.ABC):

    _component_class: ComponentABC = ComponentABC

    def __init__(self):
        super().__init__()

    def __repr__(self):
        return "<%s@%s>" % (type(self).__name__, id(self))

    def reset(self) -> None:
        self._component: ComponentABC = self.component_class()

    @property
    def component_class(self) -> ComponentABC:
        return self._component_class

    @component_class.setter
    def component_class(self, value: ComponentABC) -> None:
        self._component_class = value

    @property
    def _component(self):
        if self._component_ref_ is None:
            return None
        return self._component_ref_()

    @_component.setter
    def _component(self, value):
        self._component_: ComponentABC = value
        self._component_ref_ = None if value is None else weakref.ref(value)

    @property
    def component(self):
        _component_ = self._component_
        self._component_ = None
        return _component_

    def build_connector(self, connector: ClientConnector) -> None:
        self._component.init_connector(connector)

    def build_component_dict(self, component_dict, data_convert=True) -> None:
        self._component.init_component_dict(component_dict, data_convert)

    def build_component_attrs(self, **kwargs) -> None:
        for key, value in kwargs.items():
            setattr(self._component, key, value)

    def build_base_reuse_attrs(self) -> None:
        self._component.find_decorator_func()

    def build_reuse_attrs(self, *args, **kwargs) -> None:
        pass

    def build_initialize_reuse_attrs(self) -> None:
        pass

    def copy_reuse_attrs(self, component) -> None:
        self._component.copy_reuse_attrs(component)

    def copy_initialize_reuse_attrs(self, component) -> None:
        self._component.copy_initialize_reuse_attrs(component)


class RootComponentBuilder(ComponentBuilderABC):

    _component_class: RootComponent = RootComponent

    def reset(self) -> None:
        self._component: RootComponent = self.component_class()


class BaseComponentBuilder(ComponentBuilderABC):

    _component_class: BaseComponent = BaseComponent

    def reset(self) -> None:
        self._component: BaseComponent = self.component_class()

    def build_parent(
            self, parent: BaseComponent):
        self._component.set_parent(parent)
        self._component.parent_event = parent.parent_event

    def build_in_page_component(self) -> None:
        self._component.page.update_component_dict(self._component)


class BaseDataComponentBuilder(BaseComponentBuilder):

    _component_class: BaseDataComponent = BaseDataComponent

    def reset(self) -> None:
        self._component: BaseDataComponent = self.component_class()

    def build_p_context(self, p_context) -> None:
        self._component.init_p_context(p_context)

    def build_root_form_data(
            self, root_form_uuid=None, form_nested=False, depth=0,
            memory_storage=False) -> None:
        self._component.init_root_form_data(
            root_form_uuid, form_nested, depth, memory_storage)

    def build_machine_id(self, nested=False, machine_id=None) -> None:
        self._component.init_machine_id(nested, machine_id)

    def build_reuse_attrs(self, model_uuid=None) -> None:
        self._component.init_model_data(model_uuid)

    def build_initialize_reuse_attrs(self) -> None:
        self._component.find_all_columns()


class BaseControlComponentBuilder(BaseComponentBuilder):

    _component_class: BaseControlComponent = BaseControlComponent

    def reset(self) -> None:
        self._component: BaseControlComponent = self.component_class()

    def build_parent(
            self, parent: BaseComponent):
        super().build_parent(parent)

    def build_memory_storage(self, memory_storage):
        self._component._memory_storage = memory_storage

    def build_data_model(self, data_model):
        self._component.init_data_model(data_model)


class BaseContainerComponentBuilder(BaseControlComponentBuilder):

    _component_class: BaseContainerComponent = BaseContainerComponent

    def reset(self) -> None:
        self._component: BaseContainerComponent = self.component_class()


class BaseInputComponentBuilder(BaseControlComponentBuilder):

    _component_class: BaseInputComponent = BaseInputComponent

    def reset(self) -> None:
        self._component: BaseInputComponent = self.component_class()


class BasePresentComponentBuilder(BaseControlComponentBuilder):

    _component_class: BasePresentComponent = BasePresentComponent

    def reset(self) -> None:
        self._component: BasePresentComponent = self.component_class()


class BaseButtonComponentBuilder(BaseControlComponentBuilder):

    _component_class: BaseButtonComponent = BaseButtonComponent

    def reset(self) -> None:
        self._component: BaseButtonComponent = self.component_class()


class BaseNavigationComponentBuilder(BaseControlComponentBuilder):

    _component_class: BaseNavigationComponent = BaseNavigationComponent

    def reset(self) -> None:
        self._component: BaseNavigationComponent = self.component_class()


class BaseExtendComponentBuilder(BaseDataComponentBuilder):

    _component_class: BaseExtendComponent = BaseExtendComponent

    def reset(self) -> None:
        self._component: BaseExtendComponent = self.component_class()


class BaseChartBuilder(BaseDataComponentBuilder):

    _component_class: BaseChart = BaseChart

    def reset(self) -> None:
        self._component: BaseChart = self.component_class()


class BaseDataContainerComponentBuilder(BaseDataComponentBuilder):

    _component_class: BaseDataContainerComponent = BaseDataContainerComponent

    def reset(self) -> None:
        self._component: BaseDataContainerComponent = self.component_class()


class RFormContainerBuilder(BaseDataContainerComponentBuilder):

    _component_class: BaseDataContainerComponent = BaseDataContainerComponent

    def reset(self) -> None:
        self._component: BaseDataContainerComponent = self.component_class()


class PageBuilder(BaseComponentBuilder):

    _component_class: Page = Page

    def reset(self) -> None:
        self._component: Page = self.component_class()

    def build_nested_data(self, nested=False) -> None:
        self._component.init_nested_data(nested)

    def build_p_context(self, p_context) -> None:
        self._component.init_p_context(p_context)

    def build_data_context(self, data_context) -> None:
        self._component.init_data_context(data_context)

    def build_root_form_data(
            self, root_form_uuid=None, form_nested=False, depth=0,
            memory_storage=False) -> None:
        self._component.init_root_form_data(
            root_form_uuid, form_nested, depth, memory_storage)

    def build_reuse_attrs(self, *args, **kwargs) -> None:
        self.build_base_reuse_attrs()


def add_builder(builder: ComponentBuilderABC, *component_classes):
    def _add_builder(func):
        func.builder = builder
        func.component_classes = component_classes
        return func
    return _add_builder


class ComponentCreator(object):

    def __init__(self):
        self.component_class_dict = dict()
        self.builder_dict = dict()
        self.creator_dict = dict()
        self.relation_component_class_dict = {
            ComponentType.FORM: RFormContainer,
            ComponentType.R_SELECT: RSelectContainer,
            ComponentType.TRANSFER: TransferContainer,
            ComponentType.R_SELECT_POPUP: RSelectPopupContainer,
            ComponentType.R_TILE: RTileContainer,
            ComponentType.SUB_TABLE: SubTable,
            ComponentType.CARDLIST_SUB_TABLE: CardlistSubTable,
            ComponentType.DATAGRID_SUB_TABLE: DatagridSubTable,
            ComponentType.TREELIST_SUB_TABLE: TreelistSubTable,
            ComponentType.ADD_TABLE: SelectRTableContainer,
            ComponentType.DELETE_TABLE: DeleteRTableContainer,
            ComponentType.VIEW_TABLE: RTableContainer,
            ComponentType.DATAGRID_TABLE: DatagridRTableContainer,
            ComponentType.TREELIST_TABLE: TreelistRTableContainer,
            ComponentType.DATALIST: Datalist,
            ComponentType.CARDLIST: Cardlist,
            ComponentType.DATAGRID: Datagrid,
            ComponentType.TREELIST: Treelist
        }
        self.relation_container_builder = BaseDataContainerComponentBuilder()
        self.batch_create = False
        self.find_builder()

    def find_builder(self):
        for name in dir(self):
            if name.startswith("_"):
                continue
            obj = getattr(self, name, None)
            if not callable(obj):
                continue
            builder: ComponentBuilderABC = getattr(obj, "builder", None)
            if not builder:
                continue
            component_classes: List[ComponentABC] = getattr(
                obj, "component_classes", [])
            if not component_classes:
                continue
            for component_class in component_classes:
                component_type = component_class.component_type
                if not isinstance(component_type, int):
                    continue

                builder_type = component_type
                # INPUT 组件中 RForm DatalistTable CardlistTable
                # 它们的 component_type 与 Form Datalist Cardlist
                # 重复了，需要先替换
                if isinstance(builder, BaseControlComponentBuilder):
                    if component_type in ComponentType.BUILDER_REPLACE_TYPE:
                        builder_type = ComponentType.BUILDER_REPLACE_TYPE.get(
                            component_type)
                self.builder_dict.update({builder_type: builder})
                self.creator_dict.update({builder_type: obj})
                self.component_class_dict.update(
                    {builder_type: component_class})

    def get_builder_info(self, component_type, control=False):
        builder_type = component_type
        if control is True:
            if component_type in ComponentType.BUILDER_REPLACE_TYPE:
                builder_type = ComponentType.BUILDER_REPLACE_TYPE.get(
                    component_type)
        builder: ComponentBuilderABC = self.builder_dict.get(builder_type)
        creator = self.creator_dict.get(builder_type)
        component_class = self.component_class_dict.get(builder_type)
        # app_log.info(f"{builder}, {creator}, {component_class}")
        return builder, creator, component_class

    def do_create_component(
            self, builder: ComponentBuilderABC, creator, component_class,
            *args, **kwargs):
        if component_class:
            builder.component_class = component_class
            builder.reset()
            return creator(builder, *args, **kwargs)
        return None

    def create_component(self, component_type, *args, control=False, **kwargs):
        self.batch_create = False
        builder, creator, component_class = self.get_builder_info(
            component_type, control)
        component = self.do_create_component(
            builder, creator, component_class, *args, **kwargs)
        component.initialize()
        builder.build_initialize_reuse_attrs()
        return component

    def batch_create_component(self, component_type, control=False, component_args=None):
        component_list = []
        builder, creator, component_class = self.get_builder_info(
            component_type, control)
        for index, c_args in enumerate(component_args):
            # 第一个组件正常创建
            self.batch_create = False if index == 0 else True
            args, kwargs = c_args
            component = self.do_create_component(
                builder, creator, component_class, *args, **kwargs)
            if self.batch_create:
                builder.copy_reuse_attrs(component_list[-1])
            component.initialize()
            if self.batch_create:
                builder.copy_initialize_reuse_attrs(component_list[-1])
            component_list.append(component)
        return component_list

    def create_relation_container_component(
            self, component_type, component_dict=None,
            parent=None, p_context=None, model_uuid=None,
            nested=False, machine_id=None,
            root_form_uuid=None, form_nested=False,
            depth=0, memory_storage=False, data_convert=False, **kwargs
            ) -> BaseDataContainerComponent:
        self.batch_create = False
        component_class = self.relation_component_class_dict.get(component_type)
        builder = self.relation_container_builder
        builder.component_class = component_class
        builder.reset()
        self._builder_create_base_data_component(
            builder, component_dict, parent,
            p_context, model_uuid, nested, machine_id,
            root_form_uuid, form_nested, depth,
            memory_storage, data_convert, **kwargs)
        component = builder.component
        component.initialize()
        return component

    @add_builder(RootComponentBuilder(), *[RootComponent])
    def create_root_component(
            self, builder: RootComponentBuilder, component_dict=None,
            connector=None, data_convert=False, **kwargs) -> RootComponent:
        builder.build_component_dict(component_dict, data_convert)
        builder.build_connector(connector)
        return builder.component

    @add_builder(PageBuilder(), *[Page])
    def create_page(
            self, builder: PageBuilder, component_dict=None,
            parent=None, p_context=None, data_context=None, nested=False,
            root_form_uuid=None, form_nested=False,
            depth=0, memory_storage=False, data_convert=False, **kwargs) -> Page:
        builder.build_parent(parent)
        builder.build_component_dict(component_dict, data_convert)
        builder.build_connector(parent.connector)
        builder.build_nested_data(nested)
        builder.build_root_form_data(
            root_form_uuid, form_nested, depth, memory_storage)
        builder.build_p_context(p_context)
        builder.build_data_context(data_context)
        if not self.batch_create:
            builder.build_reuse_attrs()
        return builder.component

    @add_builder(BaseControlComponentBuilder(), *BASE_CONTROL_COMPONENT_CLASS)
    def create_base_control_component(
            self, builder: BaseControlComponentBuilder, component_dict=None,
            parent=None, memory_storage=False, data_model=None,
            data_convert=False, **kwargs
            ) -> BaseControlComponent:
        self._builder_create_base_control_component(
            builder, component_dict, parent, memory_storage,
            data_model, data_convert, **kwargs)
        return builder.component

    # @add_builder(BaseContainerComponentBuilder(), *CONTAINER_COMPONENT_CLASSES)
    # def create_base_container_component(
    #     self, builder: BaseContainerComponentBuilder, component_dict=None,
    #     parent=None, data_model=None, data_convert=False
    #     ) -> BaseContainerComponent:
    #     self._builder_create_base_control_component(
    #         builder, component_dict, parent, data_model, data_convert)
    #     return builder.component

    # @add_builder(BaseInputComponentBuilder(), *INPUT_COMPONENT_CLASSES)
    # def create_base_input_component(
    #     self, builder: BaseInputComponentBuilder, component_dict=None,
    #     parent=None, data_model=None, data_convert=False
    #     ) -> BaseInputComponent:
    #     self._builder_create_base_control_component(
    #         builder, component_dict, parent, data_model, data_convert)
    #     return builder.component

    # @add_builder(BasePresentComponentBuilder(), *PRESENT_COMPONENT_CLASSES)
    # def create_base_present_component(
    #     self, builder: BasePresentComponentBuilder, component_dict=None,
    #     parent=None, data_model=None, data_convert=False
    #     ) -> BasePresentComponent:
    #     self._builder_create_base_control_component(
    #         builder, component_dict, parent, data_model, data_convert)
    #     return builder.component

    # @add_builder(BaseButtonComponentBuilder(), *BUTTON_COMPONENT_CLASSES)
    # def create_base_button_component(
    #     self, builder: BaseButtonComponentBuilder, component_dict=None,
    #     parent=None, data_model=None, data_convert=False
    #     ) -> BaseButtonComponent:
    #     self._builder_create_base_control_component(
    #         builder, component_dict, parent, data_model, data_convert)
    #     return builder.component

    @add_builder(BaseExtendComponentBuilder(), *EXTEND_COMPONENT_CLASSES)
    def create_base_extend_component(
            self, builder: BaseExtendComponentBuilder, component_dict=None,
            parent=None, p_context=None, model_uuid=None,
            nested=False, machine_id=None, root_form_uuid=None,
            form_nested=False, depth=0, memory_storage=False,
            data_convert=False, **kwargs
            ) -> BaseExtendComponent:
        self._builder_create_base_data_component(
            builder, component_dict, parent,
            p_context, model_uuid, nested, machine_id,
            root_form_uuid, form_nested, depth,
            memory_storage, data_convert, **kwargs)
        return builder.component

    @add_builder(BaseChartBuilder(), *CHART_COMPONENT_CLASSES)
    def create_base_chart_component(
            self, builder: BaseChartBuilder, component_dict=None,
            parent=None, p_context=None, model_uuid=None,
            nested=False, machine_id=None,
            root_form_uuid=None, form_nested=False,
            depth=0, memory_storage=False, data_convert=False, **kwargs) -> BaseChart:
        self._builder_create_base_data_component(
            builder, component_dict, parent,
            p_context, model_uuid, nested, machine_id,
            root_form_uuid, form_nested, depth,
            memory_storage, data_convert, **kwargs)
        return builder.component

    @add_builder(
        BaseDataContainerComponentBuilder(), *DATA_CONTAINER_COMPONENT_CLASSESS)
    def create_base_data_container_component(
            self, builder: BaseDataContainerComponentBuilder,
            component_dict=None, parent=None, p_context=None, model_uuid=None,
            nested=False, machine_id=None,
            root_form_uuid=None, form_nested=False,
            depth=0, memory_storage=False, data_convert=True, **kwargs
            ) -> BaseDataContainerComponent:
        self._builder_create_base_data_component(
            builder, component_dict, parent,
            p_context, model_uuid, nested, machine_id,
            root_form_uuid, form_nested, depth,
            memory_storage, data_convert, **kwargs)
        return builder.component

    def _builder_create_base_component(
            self, builder: BaseComponentBuilder, component_dict=None,
            parent=None, data_convert=True, **kwargs) -> BaseComponent:
        builder.build_parent(parent)
        builder.build_connector(parent.connector)
        builder.build_component_dict(component_dict, data_convert)
        builder.build_in_page_component()
        builder.build_component_attrs(**kwargs)
        if not self.batch_create:
            builder.build_base_reuse_attrs()

    def _builder_create_base_data_component(
            self, builder: BaseDataComponentBuilder, component_dict=None,
            parent=None, p_context=None, model_uuid=None,
            nested=False, machine_id=None, root_form_uuid=None,
            form_nested=False, depth=0, memory_storage=False,
            data_convert=True, **kwargs
            ) -> BaseDataComponent:
        self._builder_create_base_component(
            builder, component_dict, parent, data_convert, **kwargs)
        builder.build_p_context(p_context)
        builder.build_root_form_data(
            root_form_uuid, form_nested, depth, memory_storage)
        builder.build_machine_id(nested, machine_id)
        if not self.batch_create:
            builder.build_reuse_attrs(model_uuid)

    def _builder_create_base_control_component(
            self, builder: BaseControlComponentBuilder, component_dict=None,
            parent=None, memory_storage=False, data_model=None,
            data_convert=False, **kwargs
            ) -> BaseControlComponent:
        self._builder_create_base_component(
            builder, component_dict, parent, data_convert, **kwargs)
        builder.build_memory_storage(memory_storage)
        builder.build_data_model(data_model)
