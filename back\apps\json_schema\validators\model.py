from apps.json_schema.validators.base import BaseValidator
from apps.json_schema.refresolver import RefResolver
from apps.json_schema.context import AppCtx, AppModel
from apps.json_schema.schemas.model import model_schema
from apps.utils import PageFinder
from apps.ide_const import Document
from apps.json_schema.utils import id_of
from loguru import logger


class ModelValidator(BaseValidator):
    def __init__(self, app_ctx, version: str = '1.0') -> None:
        super().__init__(app_ctx, version)
        self.document_type = Document.TYPE.MODEL
        self.schema = model_schema
        self.resolver = RefResolver(base_uri=id_of(model_schema), referrer=model_schema)
        self.model_in_page = {}
