#!/bin/bash

#当本地已有数据时执行,需要将原目录的 mysql 和 redis目录复制过来
sudo systemctl start docker || true
source ./config.sh
docker network create --subnet=$subnet --gateway=$gateway --opt "com.docker.network.bridge.name"="lemon-bridge" lemon-bridge || true
DIR_BIN="$(pwd )"
DIR="$( cd "$( dirname "$0"  )" && pwd  )"
DIR_DEPLOY="$(dirname "$DIR" )"
cd $DIR_DEPLOY
docker-compose pull
docker-compose -f init-compose.yml pull
docker-compose -f init-compose.yml -p $app_env up -d
sleep 20
cd $DIR_BIN
source ./app_patch.sh
cd $DIR_DEPLOY
docker exec -it mysql-$app_env /local_deploy/bin/migrate_old_data.sh
docker exec -it mysql-$app_env /local_deploy/bin/update_sys_table.sh
docker exec -it mysql-$app_env /local_deploy/bin/load_runtime_data.sh

# 仅1.27第一次update需要打开注释
# docker exec -it mysql-$app_env /local_deploy/bin/tenant_table_dump.sh
# docker exec -it web-$app_env /root/local_deploy/process_tenant_table.sh
# docker exec -it mysql-$app_env /local_deploy/bin/tenant_table_load.sh

docker exec -it web-$app_env /root/local_deploy/new_version.sh
docker exec -it mysql-$app_env /local_deploy/bin/load_view.sh
docker-compose -f init-compose.yml -p $app_env down
