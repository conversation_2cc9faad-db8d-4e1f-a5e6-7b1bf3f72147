from contextvars import ContextVar
from types import SimpleNamespace
from typing import Optional, Any
from apps.entity import (
    ModelBasic, RelationshipBasic, ModelField, Func,
    ConstTable, EnumTable, Page as PageTable, Workflow,
    ImageTable, LabelPrint as LabelPrintTable, Print as PrintTable,
    Module, ModuleRole
)
import abc
import copy
from apps.utils import (
    replace_relationship_uuid, RelationshipFinder, PageFinder
)
from baseutils.const import SystemTable, SystemField
from collections import defaultdict


class JsonElement:
    def __init__(self, uuid: str,
                 name: str,
                 type: str,
                 element: dict) -> None:
        self.uuid = uuid
        self.name = name
        self.type = type
        self.attr_names = SimpleNamespace()
        self.element = element
        self.current_attr = ""

    @classmethod
    def from_json(cls, json_data: dict):
        return cls(
            json_data.get("uuid", ""),
            json_data.get("name", ""),
            json_data.get("type", ""),
            json_data,
        )

    def set_attr_name(self, attr: str, name: str):
        setattr(self.attr_names, attr, name)


class KeyWordCtx(SimpleNamespace):
    __required_attrs = {"parent"}

    def __init__(self, **kwargs):
        # if not self.__required_attrs.issubset(kwargs.keys()):
        #     raise ValueError(f"missing required attrs: {self.__required_attrs}")
        super().__init__(**kwargs)


class SchemaCtx(SimpleNamespace):
    pass


class AppCtx(SimpleNamespace):
    pass


class ModuleCtx(SimpleNamespace):
    pass


class DocCtx:
    def __init__(self) -> None:
        self.doc_type = None
        self._origin_entity = SimpleNamespace()
        self._current_entity = SimpleNamespace()
        self.entity_add_dict = defaultdict(list)
        self.entity_update_dict = defaultdict(list)
        self.entity_remove_dict = defaultdict(list)
        self.reference_list = []
        self.current_path = []
        self.current_parent = None
        self.current_relative_path = None
        self.current_schema = None

    @property
    def original_entity(self):
        return self._origin_entity

    @property
    def current_entity(self):
        return self._current_entity

    def to_add_entity(self, entity_cls, entity_data):
        self.entity_add_dict[entity_cls].append(entity_data)

    def to_update_entity(self, entity_cls, entity_data):
        self.entity_update_dict[entity_cls].append(entity_data)

    def to_remove_entity(self, entity_cls, entity_data):
        self.entity_remove_dict[entity_cls].append(entity_data)

    def get_original_entity_attr(self, attr_name: str, default=None):
        return getattr(self.original_entity, attr_name, default)

    def get_current_entity_attr(self, attr_name: str, default=None):
        return getattr(self.current_entity, attr_name, default)

    def to_add_reference(self, reference_data):
        self.reference_list.append(reference_data)


class Resource:
    _resource_id_name = None
    _resource_name_name = None

    def __init__(self, app_uuid: str):
        self.app_uuid = app_uuid
        self.uuid_to_resource_dict = {}
        self.need_update = False
        self.resource_id_name = self._resource_id_name
        self.resource_name_name = self._resource_name_name

    def get_resource(self, resource_uuid: str, resource_attr: Optional[str] = None,
                     resource_attr_item_value: Optional[str] = None) -> Any:
        """
        resource_uuid: 资源id
        resource_attr: 资源属性, 如enum里value属性存的enum_item
        resource_attr_item_value: 资源属性值，如enum里value属性存的enum_item， {"item_name": {...}}
        """
        if resource_uuid in self.uuid_to_resource_dict:
            resource_instance = self.uuid_to_resource_dict[resource_uuid]
            if resource_attr is None:
                return resource_instance
            if isinstance(resource_instance, dict):
                return resource_instance[resource_attr][resource_attr_item_value]
            try:
                return getattr(getattr(resource_instance, resource_attr, None), resource_attr_item_value)
            except Exception:
                return None
        return None

    def _update_data(self, resource_list: Any):
        for resource_info in resource_list:
            resource_uuid = resource_info.get(self.resource_id_name)
            self.uuid_to_resource_dict[resource_uuid] = resource_info

    def update_data(self, *args):
        self._update_data(*args)
        self.need_update = False

    def get_resource_by_name(self, resource_name: str, attr_name: Optional[str] = None, module_uuid=None) -> Any:
        # todo: 优化查找速度, 查找属性
        for resource_uuid, resource_info in self.uuid_to_resource_dict.items():
            if resource_info.get(self.resource_name_name) == resource_name:
                if module_uuid:
                    if resource_info.get("module_uuid") != module_uuid:
                        continue
                return resource_info
        return None


class AppModel(Resource):
    _resource_id_name = ModelBasic.model_uuid.name
    _resource_name_name = ModelBasic.model_name.name

    def _update_data(self, app_model_list):
        for model_info in app_model_list:
            model_uuid = model_info.get(ModelBasic.model_uuid.name)
            self.uuid_to_resource_dict[model_uuid] = model_info


class AppModelRelationship(Resource):
    _resource_id_name = RelationshipBasic.relationship_uuid.name
    _resource_name_name = RelationshipBasic.relationship_name.name
    
    def _update_data(self,
                    app_model_list,
                    app_model_relationship_list,
                    app_field_list,
                    ):
        self.relationship_finder = RelationshipFinder(
            model_list=app_model_list,
            relationship_list=app_model_relationship_list,
            field_list=app_field_list)
        for relationship_dict in app_model_relationship_list:
            r_uuid = relationship_dict.get("relationship_uuid")
            r_type = relationship_dict.get("relationship_type")
            self.uuid_to_resource_dict[r_uuid] = relationship_dict
            source_model = relationship_dict.get("source_model")
            target_model = relationship_dict.get("target_model")
            if target_model in SystemTable.UUIDS:
                continue
            if source_model == target_model:
                self.process_self_relationship(relationship_dict)
                continue
            for r_model_uuid in [target_model, source_model]:
                r_model = self.relationship_finder.model_uuid_map.get(
                    r_model_uuid)
                if r_model:
                    # 关联下拉框的 placeholder 需要 data_name
                    relationship_dict.update({
                        ModelBasic.data_name.name: r_model.display_name})
                    
    def process_self_relationship(self, relationship_dict):
        from_source = copy.copy(relationship_dict)
        from_target = copy.copy(relationship_dict)
        new_uuid = replace_relationship_uuid(from_source, RelationshipBasic)
        self.uuid_to_resource_dict[new_uuid] = from_source
        new_uuid = replace_relationship_uuid(from_target, RelationshipBasic, from_source=False)
        self.uuid_to_resource_dict[new_uuid] = from_target


class AppModelField(Resource):
    _resource_id_name = ModelField.field_uuid.name
    _resource_name_name = ModelField.field_name.name

    def _update_data(self, app_field_list, app_model_list):
        for field_info in app_field_list:
            field_uuid = field_info.get(ModelField.field_uuid.name)
            self.uuid_to_resource_dict[field_uuid] = field_info
        for model_info in app_model_list:
            model_uuid = model_info.get(ModelBasic.model_uuid.name)
            info = {}
            info.update(model_info)
            for sys_field in SystemField.ALL_FIELD:
                self.uuid_to_resource_dict[f"{model_uuid}*{sys_field['field_name']}"] = {
                    ModelField.field_uuid.name: f"{model_uuid}*{sys_field['field_name']}",
                    ModelField.field_name.name: sys_field['display_name'],
                }


class AppFunc(Resource):
    _resource_id_name = Func.func_uuid.name
    _resource_name_name = Func.func_name.name


class AppConst(Resource):
    _resource_id_name = ConstTable.const_uuid.name
    _resource_name_name = ConstTable.const_name.name


class AppEnum(Resource):
    _resource_id_name = EnumTable.enum_uuid.name
    _resource_name_name = EnumTable.enum_name.name

    def _update_data(self, app_enum_list):
        for enum in app_enum_list:
            enum_uuid = enum.get(EnumTable.enum_uuid.name)
            enum_item_dict = {}
            for v in enum["value"]:
                enum_item_dict[v["name"]] = v
            enum["value"] = enum_item_dict
            self.uuid_to_resource_dict[enum_uuid] = enum


class AppPage(Resource):
    _resource_id_name = PageTable.page_uuid.name
    _resource_name_name = PageTable.page_name.name


class AppWorkflow(Resource):
    _resource_id_name = Workflow.wf_uuid.name
    _resource_name_name = Workflow.wf_name.name


class AppImage(Resource):
    _resource_id_name = ImageTable.image_uuid.name
    _resource_name_name = ImageTable.image_name.name


class AppLabelPrint(Resource):
    _resource_id_name = LabelPrintTable.label_uuid.name
    _resource_name_name = LabelPrintTable.label_name.name


class AppPrint(Resource):
    _resource_id_name = PrintTable.print_uuid.name
    _resource_name_name = PrintTable.print_name.name


class AppModule(Resource):
    _resource_id_name = Module.module_uuid.name
    _resource_name_name = Module.module_name.name


class AppModuleRole(Resource):
    _resource_id_name = ModuleRole.role_uuid.name
    _resource_name_name = ModuleRole.role_name.name