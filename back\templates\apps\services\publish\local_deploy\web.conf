map $http_user_agent $app_host {
default         "web.runtime.lemonstudio.tech"; # 默认认为是非移动设备
~*mobile|android|webos|iphone|ipad|blackberry|kindle|iemobile|opera\  "mobile.runtime.lemonstudio.tech";
}
server {
        {% if ssl %}
        listen 8888 ssl;
        ssl_certificate {{ssl_certificate_nginx}}; # 证书文件
        ssl_certificate_key {{ssl_certificate_key_nginx}}; # 私钥文件
        ssl_session_timeout 5m; # 会话缓存过期时间
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # 开启 SSL 支持
        ssl_prefer_server_ciphers on; # 设置协商加密算法时，优先使用服务端的加密套件
        {% else %}
        listen 8888 ;
        {% endif %}
        # Add index.php to the list if you are using PHP
        index index.html index.htm;
        gzip  on; #开启gzip
        gzip_vary on;
        gzip_min_length 1k; #不压缩临界值,大于1k的才压缩,一般不用改
        gzip_buffers 4 16k;
        gzip_comp_level 6; #压缩级别,数字越大压缩的越好
        gzip_types  text/plain application/javascript application/json  application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png image/x-icon;
        server_name 127.0.0.1;
        location /runtime/api {
            rewrite ^/runtime/(.*)$ /$1 break;
            proxy_pass http://web-{{app_env}}:6500;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        }
        location /custom/ {
                 root /data0/nfs/www/html;
                 add_header Access-Control-Allow-Origin *;
                 add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        }
        location /mobile {
                rewrite ^/mobile/(.*)$ /$1 break;
                proxy_set_header Host "mobile.runtime.lemonstudio.tech";
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
                proxy_set_header  X-Real-IP        $remote_addr;
                proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_set_header X-NginX-Proxy true;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "Upgrade";
                proxy_connect_timeout {{proxy_connect_timeout}};
                proxy_send_timeout {{proxy_send_timeout}};
                proxy_read_timeout {{proxy_read_timeout}};
                set $realuri $uri;
                proxy_set_header X-Real-URI $realuri;
                send_timeout  {{send_timeout}};
                proxy_pass http://web-{{app_env}}:7000;  
        }
        location /app {
                rewrite ^/app/(.*)$ /$1 break;
                proxy_set_header Host "app.runtime.lemonstudio.tech";
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
                proxy_set_header  X-Real-IP        $remote_addr;
                proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_set_header X-NginX-Proxy true;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "Upgrade";
                proxy_connect_timeout {{proxy_connect_timeout}};
                proxy_send_timeout {{proxy_send_timeout}};
                proxy_read_timeout {{proxy_read_timeout}};
                set $realuri $uri;
                proxy_set_header X-Real-URI $realuri;
                send_timeout  {{send_timeout}};
                proxy_pass http://web-{{app_env}}:7000;  
        }
        location /desktop {
                rewrite ^/desktop/(.*)$ /$1 break;
                proxy_set_header Host "pc.runtime.lemonstudio.tech";
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
                proxy_set_header  X-Real-IP        $remote_addr;
                proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_set_header X-NginX-Proxy true;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "Upgrade";
                set $realuri $uri;
                proxy_set_header X-Real-URI $realuri;
                proxy_connect_timeout {{proxy_connect_timeout}};
                proxy_send_timeout {{proxy_send_timeout}};
                proxy_read_timeout {{proxy_read_timeout}};
                send_timeout  {{send_timeout}};
                proxy_pass http://web-{{app_env}}:7000;  
        }
        location /{{app_env}} {
                # First attempt to serve request as file, then
                # as directory, then fall back to displaying a 404.
                proxy_set_header Host $app_host;
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
                proxy_set_header  X-Real-IP        $remote_addr;
                proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_set_header X-NginX-Proxy true;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "Upgrade";
                set $realuri $uri;
                proxy_set_header X-Real-URI $realuri;
                proxy_connect_timeout {{proxy_connect_timeout}};
                proxy_send_timeout {{proxy_send_timeout}};
                proxy_read_timeout {{proxy_read_timeout}};
                send_timeout  {{send_timeout}};
                proxy_pass http://web-{{app_env}}:7000;

        }
}
