# -*- coding:utf-8 -*-

import time
import asyncio
from json import dumps as json_dumps
import os
import traceback
from aiohttp import ClientSession
import copy
import importlib
import xml.etree.ElementTree as ET
from typing import Optional
from memory_profiler import profile

import sanic.websocket
import ujson
from sanic import Blueprint
from sanic.request import Request
from sanic.response import json, redirect
from sanic_openapi import doc
from sanic.views import HTTPMethodView as OriginHTTPMethodView

from baseutils.clients.ucenter_client import UCenterClient
from baseutils.log import app_log
from baseutils.utils import LemonContextVar, make_tenant_sys_table_copy, FuncRunError
from apps.utils import (
    lemon_uuid, process_args, LemonDictResponse as LDR, list_system_relationship,
    new_relationship_uuid, gen_model_resource_info,
    process_tag_grouby_resource_uuid, process_resource_tree
)
from baseutils.const import DOC, Code, WorkflowType, TagPermissionAction, CustomLoginPath
from apps.entity import Page as <PERSON>Mode<PERSON>, Workflow, Model<PERSON>asic, <PERSON><PERSON>ield, RelationshipBasic, IconFont
from apps.ide_const import (DeviceType, DocumentType, HandlerType, NodeStatus, NodeSystemActionType, 
        NodeTaskStatus, NodeType, SearchSaveActionKey)
from apps.exceptions import LemonRuntimeError, LemonPermissionError
from apps.local_ext_config import WeChartConfig
from apps.base_utils import get_real_app_uuid
from apps.watermark.utils import WatermarkType
from runtime.security import permission_map
from runtime.engine import engine
from apps.user_entity import NodeInstance, NodeTask, WorkflowInstance
from apps.entity import User
from runtime.modelapi.entity_utils import CommitQuery, RollbackQuery
from apps.user_entity import DocumentContent
from runtime.const import API, RedisChannel, Comment_Label
from runtime.utils import (
    LemonHTTPMethodView as HTTPMethodView, handle_tenant_message, get_ws_app_uuid, get_app_tenant_uuid, NumpyEncoder,
    LemonRedis, send_break_ws_message, RPCMessage, calc_action, get_app_uuid, get_app_env, handle_schedule_message)
from runtime.api.utils import WSClient, Client, gen_client
from runtime.api.helper import GetComponentVariablesHelper, RuntimeOssHelper
from runtime.component.data_adapter import WorkflowAdapter
from runtime.api.wf_helper import handle_workflow_instance, submit_workflow, handle_workflow, handle_assign_workflow
from runtime.api.wf_utils import (
    handle_wf_message, get_all_wf_instance_uuid, get_all_active_node_tasks, get_mainflow_status)
from runtime.api.wf_service import (
    list_task_action, get_task_process, list_parallel_node_related_workflow, get_subflow_detail)
from runtime.core.func_run import FuncRun
from runtime.core.namespace import LemonWrapper
from apps.ucenter_entity import DepartmentMember
from runtime.api.utils import check_url_request_bak
from runtime.runtime_entity import LocalSystemLog
from runtime.uc.utils import on_role_member_change


API_NAME_VERSION = "_".join([API.NAME, API.V1])
url_prefix = "/api/" + "/".join([API.NAME, API.V1])
bp = Blueprint(API_NAME_VERSION, url_prefix=url_prefix)


async def handle_message(channel, message):
    app_log.info(f'Received message {message} from channel {channel}')
    channel: str = channel.decode()
    message = ujson.loads(message)
    if channel.endswith((RedisChannel.EMAIL, RedisChannel.WECOM)):
        await handle_wf_message(channel, message)
    elif channel.endswith(RedisChannel.TENANT):
        await handle_tenant_message(message, engine)
    elif channel.endswith(RedisChannel.APP):
        # TODO 通过装饰器将函数存在一个全局字典, 直接调用, 不用手动添加到这个all_func_dict中, 便于管理
        all_func_dict = {
            "send_break_ws_message": send_break_ws_message,
            "on_role_member_change": on_role_member_change
        }
        func_name = message.get("func")
        is_sync_func = message.get("sync", False)
        args = message.get("args", tuple())
        kwargs = message.get("kwargs", {})
        if func := all_func_dict.get(func_name):
            if is_sync_func:
                func(*args, **kwargs)
            else:
                await func(*args, **kwargs)
    elif channel.endswith(RedisChannel.LOCALLOG):
        query = LocalSystemLog.insert(**message)
        await engine.userdb.objs.execute(query, system_log=True)
    elif channel.endswith(RedisChannel.SCHEDULE):
        await handle_schedule_message(channel, message)
    elif channel.endswith(RedisChannel.CANCEL):
        await handle_schedule_message(channel, message, cancel=True)


async def handle_stream_message(channel, message):
    app_log.info(f'Received message {message} from channel {channel}')
    if channel.endswith(RedisChannel.LOCALLOG):
        query = LocalSystemLog.insert(**message)
        try:
            await engine.userdb.objs.execute(query, system_log=True)
        except:
            app_log.info("handle_stream_message")


class GetServerTime(HTTPMethodView):

    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取服务器时间")
    async def post(self, request):
        data = time.time()
        return json(LDR(data=data))


class GetPagePermission(HTTPMethodView):

    class GetPagePermissionObj():
        page_uuid = doc.String("page_uuid")

    @doc.consumes(GetPagePermissionObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面权限信息")
    async def post(self, request):

        with process_args():
            page_uuid = str(request.json.get("page_uuid"))
            _, tenant_uuid = get_app_tenant_uuid(request)
        result = await self.get_page_permission_info(page_uuid, tenant_uuid)
        return json(result)

    @doc.consumes(GetPagePermissionObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面权限信息")
    async def get(self, request):

        with process_args():
            page_uuid = str(request.args.get("page_uuid"))
            _, tenant_uuid = get_app_tenant_uuid(request)
        result = await self.get_page_permission_info(page_uuid, tenant_uuid)
        headers = {"Cache-Control": "public, max-age=31536000"}
        return json(result, headers=headers)

    async def get_page_permission_info(self, page_uuid, tenant_uuid):

        page_document = await engine.access.select_page_content_by_page_uuid(
            page_uuid, ext_tenant=tenant_uuid)
        if not page_document:
            return LDR(Code.PAGE_NOT_FOUND)
        page_dict = page_document.get(DocumentContent.document_content.name)
        permission_config = page_dict.get("permission_config", dict())
        tags = permission_config.get("tag")
        if permission_config.get("enable") and engine.pm.permission_check:
            if not calc_action(engine.system.current_user, tags, TagPermissionAction.VISIBLE):
                return LDR(Code.PERMISSION_REQUIRED)
        return LDR(Code.OK)


class GetPage(HTTPMethodView):

    class GetPageObj():
        page_uuid = doc.String("page_uuid")
    
    @doc.consumes(GetPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面Json")
    async def post(self, request):

        with process_args():
            page_uuid = str(request.json.get("page_uuid"))
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")

        data = await self.get_page_info(tenant_uuid, app_uuid, page_uuid, user_uuid)
        return json(data)

    async def get_page_info(self, tenant_uuid, app_uuid, page_uuid, user_uuid):
        try:
            engine.pm.predicate_page(page_uuid)
        except LemonPermissionError as e:
            app_log.error(e)
            return LDR(Code.PERMISSION_REQUIRED)

        page_document = await engine.access.select_page_content_by_page_uuid(
            page_uuid, ext_tenant=tenant_uuid)
        if not page_document:
            return LDR(Code.PAGE_NOT_FOUND)
        page_dict = page_document.get(DocumentContent.document_content.name)
        permission_config = page_dict.get("permission_config", dict())
        tags = permission_config.get("tag")
        if permission_config.get("enable") and engine.pm.permission_check:
            if not calc_action(engine.system.current_user, tags, TagPermissionAction.VISIBLE):
                return LDR(Code.PERMISSION_REQUIRED)
        page_dlist_option = await self.get_page_dlist_option(
            tenant_uuid, app_uuid, user_uuid, page_uuid)
        page_form_stage = await self.get_page_form_stage(
            tenant_uuid, app_uuid, user_uuid, page_uuid)
        page_dict = page_document.get(DocumentContent.document_content.name)
        page_dict.update({
            "page_dlist_option": page_dlist_option,
            "page_form_stage": page_form_stage
        })
        return LDR(data=page_dict)

    async def get_page_dlist_option(self, tenant_uuid, app_uuid, user_uuid, page_uuid):
        """获取页面上所有数据列表/树形列表/电子表格的表头操作记忆"""
        key_reg = "_".join([tenant_uuid, app_uuid, user_uuid, page_uuid])
        page_dlist_option = dict()

        data = await engine.redis.hgetall_datalist_option(key_reg)
        for full_uuid, value in data.items():
            # 看起来这样的写法是认为最后一个下划线之后的uuid是container_uuid, 但可能不是32位
            last_letter_position = full_uuid.rfind("_")
            container_uuid = full_uuid[last_letter_position + 1:]
            page_dlist_option.update({
                container_uuid: value.get(SearchSaveActionKey.PROPERTY)
            })
        return page_dlist_option

    async def get_page_form_stage(self, tenant_uuid, app_uuid, user_uuid, page_uuid):
        """获取页面上所有表单的暂存操作的记忆"""
        key = "_".join([app_uuid, tenant_uuid, user_uuid, page_uuid])
        form_stage_data = await engine.redis.hgetall_form_stage(key)
        return form_stage_data

    @doc.consumes(GetPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面Json")
    async def get(self, request):

        with process_args():
            page_uuid = str(request.args.get("page_uuid"))
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")

        data = await self.get_page_info(tenant_uuid, app_uuid, page_uuid, user_uuid)
        headers = {"Cache-Control": "public, max-age=600"}
        return json(data, headers=headers)


class GetComponentVariables(HTTPMethodView):

    class ListAPPPageObj():
        sid = doc.String("websocket sid")
        component_uuid = doc.String("组件 UUID")
        variables = doc.List("组件变量 UUID")
    
    @doc.consumes(ListAPPPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取组件变量值")
    async def post(self, request):

        with process_args():
            sid = str(request.json.get("sid"))
            component_uuid = str(request.json.get("component_uuid"))
            variables = request.json.get("variables", list())
        
        helper = GetComponentVariablesHelper(sid, component_uuid, variables)
        data = await helper.get_component_variable()
        if data is None:
            app_log.error(f"variables: {variables}")
            # return json(LDR(Code.COMPONENT_VARIABLE_FAILED))
            return json(LDR(data={}))
        return json(LDR(data=data), dumps=json_dumps, cls=NumpyEncoder)


class HandleAssignWorkflow(HTTPMethodView):
    class HandleAssignWorkflowObj:
        assign_type = doc.String("发起指派的类型")
        assign_info = doc.String("指派user的处理人")
        user_uuids = doc.Integer("需要被处理task的user uuids")

    @doc.consumes(HandleAssignWorkflowObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("跳转工作流状态")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            assign_type = request.json.get("assign_type")
            assign_info = request.json.get("assign_info")
            user_uuids = request.json.get("user_uuids")

        return await handle_assign_workflow(tenant_uuid, user_uuids, assign_type, assign_info)

    
class StartWorkflow(HTTPMethodView):

    class StartWorkflowObj():
        wf_uuid = doc.String("工作流UUID")
        form_pk = doc.String("表单提交后返回的pk （若页面有表单； 可选参数）")
    
    @doc.consumes(StartWorkflowObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("开始一个工作流")    
    async def post(self, request):
        with process_args():
            form_pk = request.json.get("form_pk")
            wf_uuid = request.json.get("wf_uuid")
            device_type = request.json.get("device_type")
            user_uuid = request.ctx.session.get("user_uuid")
            wf_creator = request.json.get("wf_creator")
            user_uuid = wf_creator or user_uuid
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            action_data = request.json.get("action_data", dict())
            sid = request.json.get("sid")
            condition = request.ctx.condition

        return await submit_workflow(
            app_uuid, tenant_uuid, wf_uuid, form_pk, device_type, user_uuid, sid, condition=condition,
            action_data=action_data)


class BatchStartWorkflow(StartWorkflow):

    class BatchStartWorkflowObj():
        wf_uuid = doc.String("工作流UUID")
        form_pk = doc.String("表单提交后返回的pk （若页面有表单； 可选参数）")
    
    @doc.consumes(BatchStartWorkflowObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("批量开始工作流")
    async def post(self, request):
        with process_args():
            submit_info = request.json.get("submit_info", list())
        
        # TODO 暂时不使用
        for info in submit_info:
            form_pk = info["form_pk"]
            action_data = info.get("action_data", dict())
            request.json.update({
                "form_pk": form_pk, 
                "action_data": action_data
            })
            result = await super().post(request)
            app_log.info(123123)


class HandleWorkflow(HTTPMethodView):

    class HandleWorkflowObj():
        task_uuid = doc.String("任务UUID")
        action_uuid = doc.String("动作UUID")
        action_data = doc.String(
            '动作所需的数据， 转交： {"assign_user": "user_uuid"}  \
                回退： {"return_to": "node_uuid"}'
        )
        device_type = doc.Integer("设备类型，  0 pc  1 mobile  2 pad")
        form_pk = doc.String("表单提交后返回的pk （若页面有表单； 可选参数）")

    @doc.consumes(
        HandleWorkflowObj, content_type=DOC.JSON_TYPE, location="body",
        required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("处理一个工作流")
    async def post(self, request):
        with process_args():
            task_uuid = request.json.get("task_uuid", list())
            action_uuid = request.json.get("action_uuid")
            action_data = request.json.get("action_data", dict())
            comment = request.json.get("comment", "")
            device_type = request.json.get("device_type")
            user_uuid = request.ctx.session.get("user_uuid")
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            sys_admin_handle = request.json.get("sys_admin_handle", False)
            sid = request.json.get("sid")
            countersign_reason = request.json.get("countersign_reason", "")
            condition = request.ctx.condition

        return await handle_workflow(
            app_uuid, tenant_uuid, task_uuid, action_uuid, device_type, user_uuid, sid=sid,
            condition=condition, action_data=action_data, sys_admin_handle=sys_admin_handle, comment=comment,
            countersign_reason=countersign_reason)


class ListTaskAction(HTTPMethodView):

    class ListTaskActionObj():
        task_uuid = doc.String("任务UUID")
    
    @doc.consumes(ListTaskActionObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出节点动作")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            task_uuid = str(request.json.get("task_uuid"))
            ignore_handler = request.json.get("ignore_handler", False)
            
        actions = await list_task_action(
            user_uuid, task_uuid=task_uuid, ignore_handler=ignore_handler, filter_display=True)
        return json(LDR(data=actions))



class ListTaskHistory(HTTPMethodView):

    class ListTaskHistoryObj():
        wf_instance_uuid = doc.String("工作流实例UUID")
    
    @doc.consumes(ListTaskHistoryObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出工作流回退节点")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            wf_instance_uuid = str(request.json.get("wf_instance_uuid"))
            task_uuid = request.json.get("task_uuid")

        w_model = make_tenant_sys_table_copy(WorkflowInstance, tenant_uuid)
        wf_fields = (w_model.wf_instance_uuid, w_model.wf_dict)
        if task_uuid:
            node_task = await engine.user_access.get_node_task(task_uuid=task_uuid)
            wf_instance_uuid = node_task.wf_instance_uuid
        
        async with engine.userdb.objs.atomic():
            node_instance_result = await engine.user_access.list_node_instance_history_by_wf_instance_uuid(
                wf_instance_uuid, skip_type=[
                    NodeType.COPY, NodeType.PARALLEL, NodeType.AUTO, NodeType.SUBFLOW, NodeType.COUNTERSIGN,
                    NodeType.END],
                skip_auto_processed=True)
            wf_instance_data = await engine.user_access.select_wf_instance_by_wf_instance_uuid(
                wf_instance_uuid, fields=wf_fields)

        wf_dict = wf_instance_data.get(WorkflowInstance.wf_dict.name)
        workflow_adapter = WorkflowAdapter(workflow_dict=wf_dict)
        node_history_list, all_handlers = list(), list()
        for node_instance_dict in node_instance_result:
            node_uuid = node_instance_dict.get(NodeInstance.node_uuid.name)
            node_name = node_instance_dict.get(NodeInstance.node_name.name)
            node_instance_uuid = node_instance_dict.get(NodeInstance.node_instance_uuid.name)
            action_result = node_instance_dict.get(NodeInstance.action_result.name)
            handlers = node_instance_dict.get(NodeInstance.handlers.name, list())
            outer_wf_instance_uuid = node_instance_dict.get(NodeInstance.outer_wf_instance_uuid.name)
            node_type = node_instance_dict.get(NodeInstance.node_type.name)
            if node_type == NodeType.START and outer_wf_instance_uuid:
                # 分支中的开始节点不展示回退
                continue
            if handlers:
                all_handlers.extend(handlers)
            all_action_dict = dict()
            node_dict = workflow_adapter.all_node_dict.get(node_uuid, dict())
            node_data = node_dict.get("data", dict())
            node_actions = node_data.get("actions", dict())
            custom_actions = node_actions.get("custom", list())
            all_action_dict.update({
                NodeSystemActionType.ACCEPT: NodeSystemActionType.ACCEPT_NAME,
                NodeSystemActionType.REJECT: NodeSystemActionType.REJECT_NAME,
                NodeSystemActionType.ASSIGN: NodeSystemActionType.ASSIGN_NAME,
                NodeSystemActionType.RETURN: NodeSystemActionType.RETURN_NAME, 
                NodeSystemActionType.REVOKE: NodeSystemActionType.REVOKE_NAME
            })
            for action_dict in custom_actions:
                action_uuid = action_dict.get("uuid")
                action_name = action_dict.get("name")
                all_action_dict.update({action_uuid: action_name})
            action_result_name = all_action_dict.get(action_result)
            node_history_list.append({
                "node_uuid": node_uuid, "node_name": node_name, "node_instance_uuid": node_instance_uuid,
                "handlers": handlers, "handlers_name": list(), 
                "action_result": action_result, "action_result_name": action_result_name})
        all_handlers = list(set(all_handlers))
        
        async with engine.db.objs.atomic():
            user_result = await engine.user_access.list_tenant_user_by_user_uuid_list(all_handlers, tenant_uuid)
        all_user_dict = dict()
        for user_dict in user_result:
            user_uuid = user_dict.get(User.user_uuid.name)
            full_name = user_dict.get(User.full_name.name)
            all_user_dict.update({user_uuid: full_name})
        for node_history_dict in node_history_list:
            handlers = node_history_dict.get("handlers", list())
            handlers_name = node_history_dict.get("handlers_name", list())
            for handler in handlers:
                handler_name = all_user_dict.get(handler)
                handlers_name.append(handler_name)
        return json(LDR(data=node_history_list))


class ListUserTask(HTTPMethodView):
    class ListUserTaskObj:
        status = doc.Integer("处理状态； 0 未处理  1 已处理")
        timestamp = doc.Integer("时间戳（可选； 默认 None）")
        pagination = doc.Integer("页码")
        page_size = doc.Integer("每页显示条数 （可选； 默认 10）")
        order_by = doc.Integer("排序字段（可选； 0 发起时间  1 转交时间  2 完成时间， 默认 0 ）")
        direction = doc.Integer("排序方式（可选； 0 升序  1 降序， 默认 1）")
    
    @doc.consumes(ListUserTaskObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出用户的任务")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            status = request.json.get("status", 0)
            timestamp = request.json.get("timestamp")
            pagination = request.json.get("pagination", 1)
            page_size = request.json.get("page_size", 10)
            order_by = request.json.get("order_by", 0)
            direction = request.json.get("direction", 1)
            node_type = request.json.get("node_type")
            if status not in [NodeTaskStatus.DOING, NodeTaskStatus.DONE]:
                return json(LDR(Code.ARGS_ERROR))
            if order_by not in [0, 1, 2]:
                return json(LDR(Code.ARGS_ERROR))
            if node_type not in [NodeType.COPY, None]:
                return json(LDR(Code.ARGS_ERROR))

        t_model = make_tenant_sys_table_copy(NodeTask, tenant_uuid)
        w_model = make_tenant_sys_table_copy(WorkflowInstance, tenant_uuid)
        time_field, filter_expr, order_by_field = t_model.create_time, None, w_model.create_time
        if status == NodeTaskStatus.DONE:
            time_field = t_model.handle_time
        if isinstance(timestamp, (int, float)):
            filter_expr = [time_field >= timestamp]
        if status == NodeTaskStatus.DONE and node_type != NodeType.COPY:
            if not filter_expr:
                filter_expr = [t_model.node_type != NodeType.COPY]
            else:
                filter_expr.append(t_model.node_type != NodeType.COPY)
        if order_by == 1:
            order_by_field = t_model.create_time
        elif order_by == 2:
            order_by_field = t_model.handle_time
        if direction == 1:
            order_by_expr = order_by_field.desc()
        else:
            order_by_expr = order_by_field
        async with engine.db.objs.atomic():
            count, result = await engine.user_access.list_node_task_by_handler_status(
                tenant_uuid, app_uuid, handlers=user_uuid, status=status, filter_expr=filter_expr, 
                pagination=pagination, page_size=page_size, order_by_expr=order_by_expr, 
                node_type=node_type)
        
        data = list(result)
        return json(LDR(data={"count": count, "data": data}))


class ListSubordinateTask(HTTPMethodView):
    class ListSubordinateTaskObj:
        timestamp = doc.Integer("时间戳（可选； 默认 None）")
        pagination = doc.Integer("页码")
        page_size = doc.Integer("每页显示条数 （可选； 默认 10）")
        order_by = doc.Integer("排序字段（可选； 0 发起时间  1 转交时间， 默认 0 ）")
        direction = doc.Integer("排序方式（可选； 0 升序  1 降序， 默认 1）")
    
    @doc.consumes(ListSubordinateTaskObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出下级的任务")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            timestamp = request.json.get("timestamp")
            pagination = request.json.get("pagination", 1)
            page_size = request.json.get("page_size", 10)
            order_by = request.json.get("order_by", 0)
            direction = request.json.get("direction", 1)
            if order_by not in [0, 1]:
                return json(LDR(Code.ARGS_ERROR))

        users = []
        users_result = await engine.user_access.list_department_member_by_user(
            user_uuid, tenant_uuid, is_admin=2)
        for user_dict in users_result:
            t_user_uuid = user_dict.get(DepartmentMember.user_uuid.name)
            if t_user_uuid != user_uuid:
                users.append(t_user_uuid)
        if not users:
            return json(LDR(data={"count": 0, "data": []}))

        t_model = make_tenant_sys_table_copy(NodeTask, tenant_uuid)
        w_model = make_tenant_sys_table_copy(WorkflowInstance, tenant_uuid)
        time_field, filter_expr, order_by_field = t_model.create_time, None, w_model.create_time
        if isinstance(timestamp, int):
            filter_expr = [time_field >= timestamp]
        if order_by == 1:
            order_by_field = t_model.create_time
        if direction == 1:
            order_by_expr = order_by_field.desc()
        else:
            order_by_expr = order_by_field
        async with engine.userdb.objs.atomic():
            count, result = await engine.user_access.list_node_task_by_handler_status(
                tenant_uuid, app_uuid, handlers=users, status=NodeTaskStatus.DOING, filter_expr=filter_expr, 
                pagination=pagination, page_size=page_size, order_by_expr=order_by_expr)
        data = list(result)
        return json(LDR(data={"count": count, "data": data}))


class ListUserStartTask(HTTPMethodView):
    class ListUserStartTaskObj:
        timestamp = doc.Integer("时间戳（可选； 默认 None）")
        pagination = doc.Integer("页码")
        page_size = doc.Integer("每页显示条数 （可选； 默认 10）")
        order_by = doc.Integer("排序字段（可选； 0 发起时间  2 完成时间， 默认 0 ）")
        direction = doc.Integer("排序方式（可选； 0 升序  1 降序， 默认 1）")
    
    @doc.consumes(ListUserStartTaskObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出发起的任务")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            timestamp = request.json.get("timestamp")
            pagination = request.json.get("pagination", 1)
            page_size = request.json.get("page_size", 10)
            order_by = request.json.get("order_by", 0)
            direction = request.json.get("direction", 1)
            if order_by not in [0, 2]:
                return json(LDR(Code.ARGS_ERROR))
        
        w_model = make_tenant_sys_table_copy(WorkflowInstance, tenant_uuid)
        time_field, filter_expr, order_by_field = w_model.create_time, None, w_model.create_time
        if isinstance(timestamp, int):
            filter_expr = (time_field >= timestamp)
        if order_by == 2:
            order_by_field = w_model.end_time
        if direction == 1:
            order_by_expr = order_by_field.desc()
        else:
            order_by_expr = order_by_field
        async with engine.userdb.objs.atomic():
            count, wf_result = await engine.user_access.list_wf_instance_by_creator(
                tenant_uuid, app_uuid, creator=user_uuid, filter_expr=filter_expr, 
                pagination=pagination, page_size=page_size, order_by_expr=order_by_expr)
        
        wf_data = list(wf_result)
        all_handlers = list()
        for wf_data_dict in wf_data:
            handlers = wf_data_dict.get(NodeInstance.handlers.name, list())
            if handlers:
                all_handlers.extend(handlers)
        all_handlers = list(set(all_handlers))
        app_log.info(f"all_handlers: {all_handlers}")
        
        async with engine.db.objs.atomic():
            user_result = await engine.user_access.list_user_by_user_uuid_list(all_handlers)
        all_user_dict = dict()
        for user_dict in user_result:
            t_user_uuid = user_dict.get(User.user_uuid.name)
            full_name = user_dict.get(User.full_name.name)
            all_user_dict.update({t_user_uuid: full_name})
        for wf_data_dict in wf_data:
            handlers_name = list()
            handlers = wf_data_dict.get(NodeInstance.handlers.name, list())
            if handlers:
                for handler in handlers:
                    handler_name = all_user_dict.get(handler)
                    handlers_name.append(handler_name)
            wf_data_dict.update({"handlers_name": handlers_name})
        return json(LDR(data={"count": count, "data": wf_data}))


class RemindUserTask(HTTPMethodView):
    class RemindUserTaskObj:
        wf_instance_uuid = doc.String("工作流实例UUID")
    
    @doc.consumes(RemindUserTaskObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("催办任务")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            wf_instance_uuid= request.json.get("wf_instance_uuid")

        async with engine.userdb.objs.atomic():
            update_count = await engine.user_access.remind_node_task_by_wf_instance_uuid_creator(
                wf_instance_uuid, user_uuid)
        return json(LDR(data=update_count))


class GetTaskPage(HTTPMethodView):
    class GetTaskPageObj:
        wf_instance_uuid = doc.String("工作流实例UUID")
        device_type = doc.String("设备类型 （PC 0  MOBILE 1  PAD 2")
    
    @doc.consumes(GetTaskPageObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取工作流页面信息")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            wf_instance_uuid= request.json.get("wf_instance_uuid")
            device_type = request.json.get("device_type")
            node_uuid = request.json.get("node_uuid")
            from_tab = request.json.get("from_tab")

        async with engine.userdb.objs.atomic():
            wf_data = await engine.user_access.select_wf_instance_join_node_instance_by_wf_instance_uuid(wf_instance_uuid)
        version_dict = wf_data.get(WorkflowInstance.wf_dict.name)
        node_uuid = node_uuid or wf_data.get(NodeInstance.node_uuid.name)
        workflow_adapter = WorkflowAdapter(workflow_dict=version_dict)
        node_type = workflow_adapter.all_node_dict.get(node_uuid, dict()).get("type")
        app_log.info(f"node_uuid: {node_uuid}, node_type: {node_type}")
        app_log.info(f"version_dict: {version_dict}")
        page_uuid = None
        if node_type == NodeType.START or from_tab == "user_start":
            page_uuid = workflow_adapter.get_start_form_page_uuid(device_type)
        else:
            page_uuid = workflow_adapter.get_node_form_page_uuid(device_type, node_uuid)

        async with engine.db.objs.atomic():
            fields = [PageModel.page_uuid, PageModel.page_name, PageModel.open_type,]
            page_document = await engine.access.select_page_by_page_uuid(page_uuid, fields)

        if not page_document:
            return json(LDR(Code.PAGE_NOT_FOUND))

        status = wf_data.get(WorkflowInstance.status.name)
        mainflow = wf_data.get(WorkflowInstance.mainflow.name)
        if mainflow:
            status = await get_mainflow_status(wf_data, status)

        page_document.update({
            "status": status
        })

        return json(LDR(data=page_document))


class GetTaskInfo(HTTPMethodView):
    class GetTaskInfoObj:
        wf_instance_uuid = doc.String("工作流实例UUID")
    
    @doc.consumes(GetTaskInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取工作流（任务）处理进程")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            wf_instance_uuid = request.json.get("wf_instance_uuid")
            task_uuid = request.json.get("task_uuid")
            processed = request.json.get("processed", False)

        wf_process, approvalflow_opinion, approvalflow_opinion_required, design_return, cur_task_handler = await \
            get_task_process(tenant_uuid, app_uuid, user_uuid, wf_instance_uuid=wf_instance_uuid, task_uuid=task_uuid,
                             processed=processed)
        data = {
            "wf_process": wf_process,
            "wf_instance_uuid": wf_instance_uuid,
            "approvalflow_opinion": approvalflow_opinion,
            "approvalflow_opinion_required": approvalflow_opinion_required,
            "design_return": design_return,
            "cur_task_handler": cur_task_handler
        }
        # 需审批意见时返回对应标签
        if approvalflow_opinion is True:
            key = "_".join([app_uuid, tenant_uuid, user_uuid])
            comment_label = await engine.redis.hget_workflow_label(key, "comment_label")
            if comment_label is None:
                comment_label = Comment_Label.ALL_DEFAULT_LABEL
            data.update({"comment_label": comment_label})
        return json(LDR(data=data))


class GetWorkflowBaseInfo(HTTPMethodView):
    class GetWorkflowBaseInfoObj:
        task_uuid = doc.String("节点任务UUID")

    @doc.consumes(GetWorkflowBaseInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取工作流基础信息")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            task_uuid = request.json.get("task_uuid", "")
            wf_instance_uuid = request.json.get("wf_instance_uuid", "")

        if task_uuid:
            node_task_obj = await engine.user_access.get_node_task(task_uuid=task_uuid)
            if node_task_obj.outmost_wf_instance_uuid:
                wf_instance_uuid = node_task_obj.outmost_wf_instance_uuid
            else:
                wf_instance_uuid = node_task_obj.wf_instance_uuid
            node_uuid = node_task_obj.node_uuid
        elif wf_instance_uuid:
            start_node_task = await engine.user_access.select_start_node_task_by_wf_instance_uuid(wf_instance_uuid)
            if start_node_task.get("outmost_wf_instance_uuid", ""):
                wf_instance_uuid = start_node_task.get("outmost_wf_instance_uuid")
            node_uuid = start_node_task.get("node_uuid")
            task_uuid = start_node_task.get("task_uuid")
        else:
            node_uuid = ""

        data = {
            "wf_instance_uuid": wf_instance_uuid,
            "node_uuid": node_uuid,
            "task_uuid": task_uuid
        }
        return json(LDR(data=data))


class ListParallelDetail(HTTPMethodView):
    
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            node_instance_uuid = request.json.get("node_instance_uuid")
            
        if not all([app_uuid, tenant_uuid, user_uuid, node_instance_uuid]):
            return json(LDR(Code.ARGS_ERROR))

        result = await list_parallel_node_related_workflow(
            tenant_uuid, app_uuid, user_uuid, node_instance_uuid)
        return json(LDR(data=result))


class ListSubflowDetail(HTTPMethodView):

    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            node_instance_uuid = request.json.get("node_instance_uuid")

        if not all([app_uuid, tenant_uuid, user_uuid, node_instance_uuid]):
            return json(LDR(Code.ARGS_ERROR))

        result = await get_subflow_detail(tenant_uuid, app_uuid, user_uuid, node_instance_uuid)
        return json(LDR(data=result))


class CountUnreadTask(HTTPMethodView):
    
    # @doc.consumes(CountUnreadTaskObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取未读任务数量")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
        
        async with engine.userdb.objs.atomic():
            count = await engine.user_access.count_unread_copy_node_task_by_handler(
                tenant_uuid, app_uuid, handlers=user_uuid)
        return json(LDR(data=count))


class CountUnHandlerTask(HTTPMethodView):
    
    # @doc.consumes(CountUnreadTaskObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取未处理任务数量")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
        
        async with engine.userdb.objs.atomic():
            count = await engine.user_access.count_unhandler_node_task_by_handler(
                tenant_uuid, app_uuid, handlers=user_uuid)
        return json(LDR(data=count))


class GetFrontConst(HTTPMethodView):
    
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取前端所需常量配置")
    async def post(self, request):
        from runtime.front_const import const_list, enum_list
        data = {"const_list": const_list, "enum_list": enum_list} 
        return json(LDR(data=data))

class GetPermissionDeploy(HTTPMethodView):
    
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用权限相关的配置信息")
    async def post(self, request):
        permission_map = importlib.import_module(
            ".".join(["resources", os.environ.get("APP_NAME", "").lower(), "permission_map"]))
        result = {
            "module_role_depend_map": permission_map.module_role_depend_map, 
            "permission_check": permission_map.permission_check
        }
        return json(LDR(data=result))

class GetPageDatalistLastOption(HTTPMethodView):
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取页面数据列表上次搜索/筛选/分组/排序/显示列选项")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            page_uuid = request.json.get("page_uuid")

        key_reg = "_".join([tenant_uuid, app_uuid, user_uuid, page_uuid])
        data = await engine.redis.hgetall_datalist_option(key_reg)
        return json(LDR(data=data))


class SetDatalistColumnOption(HTTPMethodView):
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("保存显示列选项")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            page_uuid = request.json.get("page_uuid")
            datalist_uuid = request.json.get("datalist_uuid")
            column_info = request.json.get("column_info", {})
            ext_tenant = request.json.get("ext_tenant")

        key = "_".join([tenant_uuid, app_uuid, user_uuid, page_uuid, datalist_uuid])
        await engine.redis.hset_datalist_option(key, "column_info", column_info)
        await engine.redis.hset_datalist_option(f"default_{key}", "column_info", column_info)
        return json(LDR(Code.OK))


class SetPropertyOption(HTTPMethodView):
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("保存显示列选项")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            page_uuid = request.json.get("page_uuid")
            parent_component_uuid = request.json.get("parent_component_uuid")
            component_uuid = request.json.get("component_uuid")
            property = request.json.get("property", {})

        if parent_component_uuid:
            key = "_".join([tenant_uuid, app_uuid, user_uuid, page_uuid, parent_component_uuid, component_uuid])
        else:
            key = "_".join([tenant_uuid, app_uuid, user_uuid, page_uuid, component_uuid])
        await engine.redis.hset_datalist_option(key, "property", property)
        return json(LDR(Code.OK))


class SetFormStageOption(HTTPMethodView):
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("保存显示列选项")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            page_uuid = request.json.get("page_uuid")
            form_uuid = request.json.get("form_uuid")
            stage = request.json.get("stage", 0)
        
        key = "_".join([app_uuid, tenant_uuid, user_uuid, page_uuid])
        await engine.redis.hset_form_stage(key, form_uuid, stage)
        return json(LDR(Code.OK))


class GetAPPTheme(HTTPMethodView):
    class GetAPPThemeObj:
        app_uuid = doc.String("应用UUID")

    @doc.consumes(GetAPPThemeObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取应用主题配置")
    async def post(self, request):
        with process_args():
            app_uuid, _ = get_app_tenant_uuid(request)
            app_uuid = get_real_app_uuid(app_uuid)
        theme = await engine.access.list_document_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=DocumentType.THEME, as_dict=False)
        theme = list(theme)[0]
        content = await engine.access.get_document_content_by_document_uuid(theme.document_uuid)
        if content is None:
            data = dict()
        else:
            document_content = content.document_content
            data = {"logo": document_content.get("logo")}
            if document_content.get("theme_uuid"):
                content = await engine.access.get_document_content_by_document_uuid(
                    document_content.get("theme_uuid"))
                document_content = content.document_content
                theme_variables = document_content.get("theme_variables")
                component_tokens = document_content.get("component_tokens")
                data.update({
                    "theme_variables": theme_variables,
                    "component_tokens": component_tokens
                })
            else:
                data.update({
                    "theme_variables": document_content
                })
        return json(LDR(data=data))


class ListExcelTemplate(HTTPMethodView):
    
    class ListExcelTemplateObj:
        document_uuid = doc.String("文档uuid")

    @doc.consumes(ListExcelTemplateObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取excel导入导出模板")
    async def post(self, request):
        with process_args():
            app_uuid, _ = get_app_tenant_uuid(request)
            app_uuid = get_real_app_uuid(app_uuid)
            document_uuid = request.json.get("document_uuid")
        template_list = await engine.access.list_excel_template_by_document_uuid(document_uuid, app_uuid)
        return json(LDR(data=list(template_list)))


class CheckUrlAccess(HTTPMethodView):

    class ListExcelTemplateObj:
        app_uuid = doc.String("应用uuid")

    @doc.consumes(ListExcelTemplateObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("检测url是否可访问")
    async def post(self, request):
        with process_args():
            app_uuid, _ = get_app_tenant_uuid(request)
            app_uuid = get_real_app_uuid(app_uuid)
        flag = await check_url_request_bak(app_uuid)
        return json(LDR(data={"Flag": flag}))


class ListExportTemplate(HTTPMethodView):
    
    class ListExportTemplateObj:
        document_uuid = doc.String("文档uuid")

    @doc.consumes(ListExportTemplateObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取导出模板")
    async def post(self, request):
        with process_args():
            app_uuid, _ = get_app_tenant_uuid(request)
            app_uuid = get_real_app_uuid(app_uuid)
            document_uuid = request.json.get("document_uuid")
        template_list = await engine.access.list_export_template_by_document_uuid(document_uuid, app_uuid)

        return json(LDR(data=list(template_list)))


class CheckAppAlive(HTTPMethodView):

    class CheckAppAliveObj:
        app_uuid = doc.String("应用uuid")

    @doc.consumes(CheckAppAliveObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("检测app是否可用")
    async def post(self, request):
        start_time = time.time()
        with process_args():
            app_uuid, _ = get_app_tenant_uuid(request)
            app_uuid = get_real_app_uuid(app_uuid)
        # 检查数据库是否能访问
        result = await engine.access.check_page_table_available(app_uuid)
        end_time = time.time()
        app_log.info(f"API Cost: check_app_alive.json {(end_time - start_time) * 1000}ms")
        return json(LDR(data=list()))
    
    async def get(self, request):
        result = await engine.access.check_page_table_available("")
        return json(LDR(data=list()))
    
class ListSystemLog(HTTPMethodView):
    
    class ListSystemLogObj:
        condition_list = doc.List("条件列表")
        page = doc.Integer("当前页")
        size = doc.Integer("没用数据条数")
        log_type = doc.Integer("日志类型")
        sort_type = doc.List("排序字段列表")
        tenant_uuid = doc.String("租户ID")

    @doc.consumes(ListSystemLogObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取系统日志")
    async def post(self, request):
        result = await engine.elastic_client.list_system_log(request.json,  request.ctx.session)
        return json(LDR(data=result))
    
class ThirdPartyAuth(HTTPMethodView):
    
    class ThirdPartyAuthObj:
        app_uuid = doc.String("应用uuid")
        account = doc.String("手机号码或用户名")
        code = doc.String("验证码 或 密码")

    @doc.consumes(ThirdPartyAuthObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取第三方校验码")
    async def post(self, request):
        with process_args():
            app_uuid, _ = get_app_tenant_uuid(request)
            app_uuid = get_real_app_uuid(app_uuid)
        auth_code = ""
        if engine.app.config.LOCAL_DEPLOY:
            # 检查数据库是否能访问
            local_config = await engine.access.get_app_local_config(app_uuid)
            if local_config:
                auth_func = local_config.get("auth_func")
                account = request.json.get("account")
                code = request.json.get("code")
                stdout, result = await FuncRun.run_func(
                    auth_func, account, code, need_stdout=True, is_system=True)
                app_log.info([stdout, result])
                if isinstance(result, FuncRunError) or result is False:
                    auth_code = False
                else:
                    app_log.info([stdout, result])
                    auth_code = lemon_uuid()
                    await engine.redis.set_third_auth_code(auth_code, account)
        return json(LDR(data={"auth_code": auth_code}))
    

class ThirdPartyAuthInfo(HTTPMethodView):
    
    class ThirdPartyAuthInfoObj:
        app_uuid = doc.String("应用uuid")

    @doc.consumes(ThirdPartyAuthInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取第三方校验配置")
    async def post(self, request):
        with process_args():
            app_uuid, _ = get_app_tenant_uuid(request)
            app_uuid = get_real_app_uuid(app_uuid)
        auth_info = {"status": 0}
        if engine.app.config.LOCAL_DEPLOY:
            local_config = await engine.access.get_app_local_config(app_uuid)
            if local_config:
                auth_info = local_config
        auth_info.update({"is_local_env": engine.app.config.LOCAL_DEPLOY})
        return json(LDR(data=auth_info))


@bp.websocket("/ws")
# @profile(stream=open("/tmp/lemon_ws.log", "w+", buffering=1))
async def lemon_ws(request, ws: sanic.websocket.WebSocketConnection):
    args = request.args
    sid = args.get("sid", None)
    token_list = args.get("token", [])
    tenant_uuid_list = args.get("tenant_uuid", [])
    app_uuid_list = args.get("app_uuid", [])
    device_type = args.get("device_type", DeviceType.PC)
    session_id_list = args.get("SessionId", [])
    preview = args.get("preview", False)
    preview = preview == '1' or preview == 'on'
    if not permission_map.anonymous and (not token_list or not tenant_uuid_list):
        return json(LDR(Code.WEBSOCKET_ARGS_FAILED))

    if app_uuid_list:
        app_uuid = app_uuid_list
    else:
        app_uuid = get_ws_app_uuid(request)
    app_log.info(f"app_uuid: {app_uuid}")

    token, tenant_uuid, session_id = token_list, tenant_uuid_list, session_id_list
    try:
        device_type = int(device_type)
    except TypeError as e:
        app_log.error(e)
        app_log.error(traceback.format_exc())
        device_type = DeviceType.PC
    app_log.info(f"sid: {sid}, tenant_uuid: {tenant_uuid}, app_uuid: {app_uuid}, device_type: {device_type} {session_id = }"
                 f" {preview = }")
    # app_log.info(f"enabled: {gc.isenabled()}")
    # gc.set_debug(gc.DEBUG_COLLECTABLE | gc.DEBUG_UNCOLLECTABLE | gc.DEBUG_LEAK)
    client : WSClient = engine.clients.get(sid)
    app_log.info(f"client: {client}")
    if client and client.token != token:
        client.connection_close()
        client.close()
        client = None
    # BUG 不同租户不能公用 client 数据查出来的有问题
    if client and client.tenant_uuid == tenant_uuid:
        client.connection_close()
        client.init_params(**{
            "token": token, "tenant_uuid": tenant_uuid, 
            "app_uuid": app_uuid, "device_type": device_type, 
            "ws": ws, "request": request,
            "session_id": session_id,
            "preview": preview})
    else:
        sid = None
        client = WSClient(request, ws, None, token, tenant_uuid, app_uuid, device_type, session_id=session_id,
                          preview=preview)
        engine.clients.add_client(client)
    await client.keep_alive()
    client.clean()
    # _unreachable = gc.collect()
    # app_log.info(f"unreachable object num: {_unreachable}")
    # for gar in gc.garbage:
    #     app_log.info(f"gar: {gar}")
    #     if gar:
    #         refers = gc.get_referrers(gar)
    #         for ref in refers:
    #             app_log.info(f"ref gar: {ref}")
    #         app_log.info(f"sys.getrefcount(gar): {sys.getrefcount(gar)}")
    app_log.info(f"ws closed")


class HandleWorkflowInstance(HTTPMethodView):
    class HandleWorkflowInstanceObj:
        organization_id = doc.String("企业uuid")
        wf_instance_uuid = doc.String("工作流实例UUID")
        handle_type = doc.Integer("操作类型: 0: 恢复  1 完成  2 终止  3 异常  4 暂停")

    @doc.consumes(HandleWorkflowInstanceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("跳转工作流状态")
    async def post(self, request):

        with process_args():
            user_uuid = request.ctx.session.get("user_uuid")
            tenant_uuid = request.json.get("organization_id")
            wf_instance_uuid = str(request.json.get("wf_instance_uuid"))
            handle_type = int(request.json.get("handle_type", 0))

        return await handle_workflow_instance(user_uuid, tenant_uuid, wf_instance_uuid, handle_type)


class GetCountersignBaseInfo(HTTPMethodView):
    class GetCountersignBaseInfoObj:
        task_uuid = doc.String("节点任务UUID")

    @doc.consumes(GetCountersignBaseInfoObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("获取加签任务的基本信息")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            task_uuid = request.json.get("task_uuid", "")

        data = dict()
        if task_uuid:
            node_task_obj = await engine.user_access.get_node_task(task_uuid=task_uuid)
            countersign_node_instance_uuid = node_task_obj.node_instance_uuid
            pnode_task = await engine.user_access.list_node_task_by_countersign_node_instance_uuid(
                [countersign_node_instance_uuid])
            if pnode_task:
                user_info = await engine.user_access.get_tenant_user_by_user_uuid(
                    tenant_uuid, pnode_task[0].get("handler"))
                full_name = user_info.get("full_name")
                data.update({
                    "countersign_handler": full_name,
                    "countersign_reason": pnode_task[0].get("countersign_reason")
                })
        return json(LDR(data=data))


class ListUserTaskByWfInstance(HTTPMethodView):
    class ListUserTaskByWfInstanceObj:
        wf_instace_uuid = doc.String("工作流实例UUID")

    @doc.consumes(ListUserTaskByWfInstanceObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION), 
        location="header", required=True)
    @doc.description("列出工作流实例用户待处理所有任务")
    @doc.tag(url_prefix)
    @doc.summary("列出工作流实例用户待处理所有任务")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            wf_instance_uuid = request.json.get("wf_instance_uuid")

        if not wf_instance_uuid:
            raise ValueError("wf_instance_uuid is required")

        all_wf_instance_uuid = await get_all_wf_instance_uuid(wf_instance_uuid, tenant_uuid)
        all_active_node_tasks = await get_all_active_node_tasks(all_wf_instance_uuid, user_uuid)

        # 如果当前没有正在进行中的任务则返回开始节点，前端显示task_uuid显示进程
        if not all_active_node_tasks:
            start_node_task = await engine.user_access.select_start_node_task_by_wf_instance_uuid(wf_instance_uuid)
            all_active_node_tasks.append(start_node_task)

        data = {
            "all_tasks": all_active_node_tasks
        }
        return json(LDR(data=data))


APP_RESOURCE_INFO = {}


class GetAPPResourceInfo(HTTPMethodView):

    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("列出应用资源数据")
    @doc.tag(url_prefix)
    @doc.summary("列出应用资源数据")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
        if not APP_RESOURCE_INFO:
            model_list = await engine.access.list_model_by_app_uuid(app_uuid, with_sys=True)
            field_list = await engine.access.list_field_by_app_uuid(app_uuid, with_sys=True, need_sys=True)
            relationship_list = await engine.access.list_relationship_by_app_uuid(app_uuid, with_sys=True)
            APP_RESOURCE_INFO.update(gen_model_resource_info(
                model_list, field_list, relationship_list,
                ModelBasic, ModelField, RelationshipBasic))
        return json(LDR(data=APP_RESOURCE_INFO))


class UpdateWorkflowLabel(HTTPMethodView):
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("更新工作流相关标签")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            user_uuid = request.ctx.session.get("user_uuid")
            comment_label = request.json.get("comment_label")

        key = "_".join([app_uuid, tenant_uuid, user_uuid])
        comment_label_list = await engine.redis.hget_workflow_label(key, "comment_label")
        if isinstance(comment_label, str):
            # 保存为标签
            if len(comment_label) > 500:
                return json(LDR(Code.WORKFLOW_LABEl_LENGTH_ERR))
            else:
                if comment_label_list is None:
                    comment_label_list = Comment_Label.ALL_DEFAULT_LABEL
                comment_label_list.append({"uuid": lemon_uuid(), "name": comment_label})
        elif isinstance(comment_label, list):
            # 删除标签，前端会传删除之后的标签
            comment_label_list = comment_label
        if len(comment_label_list) > 10:
            return json(LDR(Code.WORKFLOW_LABEl_COUNT_ERR))
        await engine.redis.hset_workflow_label(key, "comment_label", comment_label_list)
        return json(LDR(data={"comment_label": comment_label_list}))


class UpdateRolePermission(HTTPMethodView):

    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("设置角色权限")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            role_id = request.json.get("role_id", 0)
            tag_id = request.json.get("tag_id", [])
        redis = LemonRedis()
        exist_role_tags = await engine.user_access.list_permission_config(role_id=role_id)
        exist_role_tags_id = {t["to_tag"] for t in exist_role_tags}

        need_add = set(tag_id) - exist_role_tags_id
        need_del = exist_role_tags_id - set(tag_id)
        if need_add:
            await engine.user_access.add_permission_config(role_id, need_add)
        if need_del:
            await engine.user_access.del_permission_config(role_id, need_del)

        # 断开受影响用户的ws, 多进程情况下, 每个进行都要进行同样的处理
        if need_add or need_del:
            user_role = await engine.access.get_user_role(id=role_id)
            channel = "server:" + app_uuid + ":" + RedisChannel.APP
            data = RPCMessage("send_break_ws_message", args=(user_role.role_uuid,))
            await engine.redis.redis.publish(channel, data.to_json())
        return json(LDR())


class ListRolePermission(HTTPMethodView):

    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("列出角色权限")
    async def post(self, request):
        with process_args():
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            role_uuid = request.json.get("role_uuid", "")
            role_id = request.json.get("role_id", 0)

        async def process_all_tags(all_tags, role_selected_tags):
            for resource in all_tags:
                for tag in resource.get("children", []):
                    tag.update({
                        "selected": tag["id"] in role_selected_tags
                    })

        if role_uuid:
            user_role = await engine.access.get_user_role(role_uuid=role_uuid)
        elif role_id:
            user_role = await engine.access.get_user_role(id=role_id)
        else:
            raise

        role_permission = await engine.user_access.list_permission_config(role_id=user_role.id)
        role_selected_tags = [t["to_tag"] for t in role_permission]
        module_resources = await engine.access.list_all_resource_by_app_uuid_module_uuid(app_uuid)
        module_tags = await engine.access.list_module_resource_tag(app_uuid)
        tag_obj_list, resource_uuid_dict = process_tag_grouby_resource_uuid(module_tags)
        await process_all_tags(tag_obj_list, role_selected_tags)
        all_resource_tags = process_resource_tree(module_resources, resource_uuid_dict)
        result = {
            "id": user_role.id,
            "role_uuid": user_role.role_uuid,
            "role_name": user_role.role_name,
            "tags": all_resource_tags
        }
        return json(LDR(data=result))


class GatherIconFont(HTTPMethodView):

    class SyncIconObj():
        app_uuid = doc.String("应用UUID")

    @doc.consumes(SyncIconObj, content_type=DOC.JSON_TYPE, location="body", required=True)
    @doc.consumes(
        doc.String(name="Authorization", description=DOC.AUTH_DESCRIPTION),
        location="header", required=True)
    @doc.description("")
    @doc.tag(url_prefix)
    @doc.summary("搜集应用所有图标")
    async def post(self, request):
        with process_args():
            app_uuid = request.json.get("app_uuid")
        custom_icon_list = await engine.access.list_iconfont(app_uuid)
        system_icon_list = await engine.access.list_iconfont(app_uuid="system")
        all_icon = [custom_icon_list, system_icon_list]
        tree = ET.fromstring('<svg></svg>')
        for icon_list in all_icon:
            for icon in icon_list:
                content = icon.get(IconFont.content.name)
                icon_element = ET.fromstring(content)
                tree.append(icon_element)
        result = ET.tostring(tree)
        return json(LDR(data=result))


class GetCustomLoginPage(HTTPMethodView):

    async def get(self, request: Request):
        with process_args():
            device_type = request.args.get("device_type")
            if not device_type:
                raise ValueError("device_type is required")
            app_uuid, tenant_uuid = get_app_tenant_uuid(request)
            tenant_uuid = os.getenv("TENANT_UUID", "") or tenant_uuid

        custom_login_path = CustomLoginPath.from_device_type_str(device_type)
        if not custom_login_path:
            return json(LDR(data=None))

        login_pages = await engine.access.list_page_by_url_app_uuid(app_uuid, [custom_login_path.value])
        if not login_pages:
            return json(LDR(data=None))

        if not permission_map.anonymous:
            return json(LDR(Code.ANONYMOUS_NOT_OPEN))

        if not tenant_uuid:
            tenant_uuids = await UCenterClient().list_tenant_uuid(app_uuid)
            if not tenant_uuids:
                return json(LDR(return_code=Code.NO_TENANT_AVAILABLE))
            tenant_uuid = tenant_uuids[0]

        page = login_pages[0]
        return json(LDR(data={
            "page_uuid": page.get("page_uuid"),
            "page_url": f"/{get_app_env()}/{tenant_uuid}/container/{custom_login_path.value}",
            "page_name": page.get("page_name")
        }))


class BindWxOpenid(HTTPMethodView):

    async def get(self, request: Request):
        with process_args():
            app_uuid = get_app_uuid(request)
            code = request.args.get("code")
            app_id = request.args.get("appid")
            tenant_uuid = request.args.get("tenant_uuid")

        _, component_token = await engine.wx_api._get_component_token()
        if not component_token:
            raise
        get_openid_url = f"https://api.weixin.qq.com/sns/oauth2/component/access_token?appid={app_id}&code={code}&grant_type=authorization_code&component_appid={WeChartConfig.COMPONENT_APPID}&component_access_token={component_token}"
        async with ClientSession(trust_env=True) as session:
            request = await session.post(get_openid_url, json={})
            result_text = await request.text()
            result_json = ujson.loads(result_text)
            openid = result_json.get("openid")
        headers = {
            "code": code
        }
        cur_user = LemonContextVar.current_user.get()
        # url_prefix = engine.config.APP_LOCALTION + "/" + tenant_uuid
        var_app_env_name = os.environ.get("APP_ENV_NAME", "").rstrip(";")
        url_prefix = "/".join([
            engine.config.M_APP_DOMAIN, app_uuid + var_app_env_name, tenant_uuid
        ])
        url = f"{url_prefix}/container/wxbind?openid={openid}&is_custom_path=true"

        # 前端说无法把连接中的参数提取出来供表单用, 这儿先存到redis, 后续云函数中使用
        key = cur_user.uuid + "_" + "wx_service:" + app_uuid
        await engine.redis.setex(key, 5*60, openid)
        return redirect(url, headers=headers)


bp.add_route(GetServerTime.as_view(), "/get_server_time.json")
bp.add_route(GetComponentVariables.as_view(), "/get_component_variable.json")
bp.add_route(GetPage.as_view(), "/get_page.json")
bp.add_route(StartWorkflow.as_view(), "/start_workflow.json")
bp.add_route(BatchStartWorkflow.as_view(), "/batch_start_workflow.json")
bp.add_route(HandleWorkflow.as_view(), "/handle_workflow.json")
bp.add_route(ListTaskAction.as_view(), "/list_task_action.json")
bp.add_route(ListTaskHistory.as_view(), "/list_task_history.json")
bp.add_route(ListUserTask.as_view(), "/list_user_task.json")
bp.add_route(ListSubordinateTask.as_view(), "/list_subordinate_task.json")
bp.add_route(ListUserStartTask.as_view(), "/list_user_start_task.json")
bp.add_route(RemindUserTask.as_view(), "/remind_user_task.json")
bp.add_route(GetTaskPage.as_view(), "/get_task_page.json")
bp.add_route(GetTaskInfo.as_view(), "/get_task_info.json")
bp.add_route(CountUnreadTask.as_view(), "/count_unread_task.json")
bp.add_route(CountUnHandlerTask.as_view(), "/count_unhandler_task.json")
bp.add_route(GetFrontConst.as_view(), "/get_front_const.json")
bp.add_route(GetAPPTheme.as_view(), "/get_app_theme.json")
bp.add_route(GetPageDatalistLastOption.as_view(), "/get_page_datalist_last_option.json")
bp.add_route(SetDatalistColumnOption.as_view(), "/set_datalist_column_option.json")
bp.add_route(SetPropertyOption.as_view(), "/set_property_option.json")
bp.add_route(SetFormStageOption.as_view(), "/set_form_stage_option.json")
bp.add_route(ListExcelTemplate.as_view(), "/list_excel_template.json")
bp.add_route(CheckUrlAccess.as_view(), "/check_url_access.json")
bp.add_route(ListExportTemplate.as_view(), "/list_export_template.json")
bp.add_route(CheckAppAlive.as_view(), "/check_app_alive.json")
bp.add_route(ListSystemLog.as_view(), "/list_system_log.json")
bp.add_route(ThirdPartyAuth.as_view(), "/third_party_auth.json")
bp.add_route(ThirdPartyAuthInfo.as_view(), "/third_party_auth_info.json")
bp.add_route(GetPermissionDeploy.as_view(), "/get_permission_config.json")
bp.add_route(ListParallelDetail.as_view(), "/list_parallel_detail.json")
bp.add_route(ListSubflowDetail.as_view(), "/list_subflow_detail.json")
bp.add_route(GetWorkflowBaseInfo.as_view(), "/get_workflow_base_info.json")
bp.add_route(HandleWorkflowInstance.as_view(), "/handle_workflow_instance.json")
bp.add_route(HandleAssignWorkflow.as_view(), "/handle_assign_workflow.json")
bp.add_route(GetCountersignBaseInfo.as_view(), "/get_countersign_base_info.json")
bp.add_route(ListUserTaskByWfInstance.as_view(), "/list_user_task_by_wf_instance.json")
bp.add_route(GetAPPResourceInfo.as_view(), "/get_app_resource_info.json")
bp.add_route(UpdateWorkflowLabel.as_view(), "/update_workflow_label.json")
bp.add_route(UpdateRolePermission.as_view(), "/update_role_permission.json")
bp.add_route(ListRolePermission.as_view(), "/list_role_permission.json")
bp.add_route(GatherIconFont.as_view(), "/gather_iconfont.json")
bp.add_route(GetPagePermission.as_view(), "/get_page_permission.json")
bp.add_route(GetCustomLoginPage.as_view(), "/get_custom_login_page.json")
bp.add_route(BindWxOpenid.as_view(), "/bind_wx_openid.json")
