# -*- coding:utf-8 -*-

# from baseutils.log import app_log
from apps.entity import RestfulTable
from apps.exceptions import (
    CheckNameError, CheckUUIDError, CheckUUIDUniqueError
)
from apps.ide_const import LemonDesignerErrorCode as LDEC
from apps.ide_const import Restful, RestfulAuthorizationType
from apps.services import CheckerService, DocumentCheckerService
from apps.services.checker import checker


METHODS = ["GET", "PUT", "POST", "PATCH", "DELETE"]


class AuthCheckerService(CheckerService):
    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str,
            document_uuid: str, document_name: str, element: dict,
            app_func_uuid_dict, module_role_uuid_set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid,
            document_name, element, *args, **kwargs)
        self.module_role_uuid_set = module_role_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict

    def initialize(self):
        super().initialize()
        self.username = self.element.get('username')
        self.password = self.element.get('password')
        self.access = self.element.get('access', dict())

    @checker.run
    def check_access(self):
        access_type = self.access.get("type", 0)
        if access_type == 0:
            if self.access.get("role") not in self.module_role_uuid_set:
                attr = Restful.ATTR.ACCESS_ROLE
                return_code = LDEC.RESTFUL_ACCESS_ROLE_ERROR
                return_code.message = return_code.message.format(
                    name=self.document_name)
                self._add_error_list(attr=attr, return_code=return_code)
        elif access_type == 1:
            if self.access.get("func") not in self.app_func_uuid_dict:
                attr = Restful.ATTR.ACCESS_FUNC
                return_code = LDEC.RESTFUL_ACCESS_FUNC_ERROR
                return_code.message = return_code.message.format(
                    name=self.document_name)
                self._add_error_list(attr=attr, return_code=return_code)


class VersionCheckerService(CheckerService):
    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str,
            document_uuid: str, document_name: str, element: dict,
            app_func_uuid_dict, versions_restful_uri_set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid,
            document_name, element, *args, **kwargs)
        self.app_func_uuid_dict = app_func_uuid_dict
        self.versions_restful_uri_set = versions_restful_uri_set
        self.version_requests = list()

    def initialize(self):
        super().initialize()
        self.version = self.element.get('version')
        self.resources = self.element.get('resources', list())

    def check_requests(self, requests, resource_requests):
        exist_method = set()
        attr = Restful.ATTR.REQUEST
        for request in requests:
            method = request.get('method')
            if method not in METHODS:
                return_code = LDEC.RESTFUL_REQUEST_METHOD_ERROR
                return_code.message = return_code.message.format(
                    name=self.document_name)
                self._add_error_list(attr=attr, return_code=return_code)
            if method in exist_method:
                return_code = LDEC.RESTFUL_REQUEST_METHOD_EXIST
                return_code.message = return_code.message.format(
                    name=self.document_name)
                self._add_error_list(attr=attr, return_code=return_code)
            if method in METHODS:
                resource_requests.append({"method": method})
            exist_method.add(method)
            func = request.get('func')
            if func not in self.app_func_uuid_dict:
                return_code = LDEC.RESTFUL_REQUEST_FUNC_ERROR
                return_code.message = return_code.message.format(
                    name=self.document_name)
                self._add_error_list(attr=attr, return_code=return_code)
            else:
                func_dict = self.app_func_uuid_dict[func]
                self.update_cloud_func_reference(func, self.document_name, self.document_uuid, attr)
                if len(func_dict["arg_list"]) < 1:
                    return_code = LDEC.RESTFUL_REQUEST_FUNC_ARG_ERROR
                    return_code.message = return_code.message.format(
                        name=self.document_name)
                    self._add_error_list(attr=attr, return_code=return_code)
                if len(func_dict["return_list"]) != 1:
                    return_code = LDEC.RESTFUL_REQUEST_FUNC_RETURN_ERROR
                    return_code.message = return_code.message.format(
                        name=self.document_name)
                    self._add_error_list(attr=attr, return_code=return_code)

    @checker.run
    def check_resources(self):
        for resource in self.resources:
            uri = resource.get('resource')
            resource_requests = []
            version_resource = {"resource": uri, "children": resource_requests}
            if not uri.startswith("/"):
                attr = Restful.ATTR.REQUEST
                return_code = LDEC.RESTFUL_RESOUCE_START_ERROR
                return_code.message = return_code.message.format(
                    name=self.document_name, version=self.version)
                self._add_error_list(attr=attr, return_code=return_code)
            self.versions_restful_uri_set.add(uri)
            requests = resource.get('requests', list())
            self.check_requests(requests, resource_requests)
            self.version_requests.append(version_resource)


class RestfulCheckerService(CheckerService):

    attr_class = Restful.ATTR
    uuid_error = LDEC.RESTFUL_UUID_UNIQUE_ERROR
    uuid_unique_error = LDEC.RESTFUL_UUID_UNIQUE_ERROR
    name_error = LDEC.RESTFUL_NAME_FAILED
    name_unique_error = LDEC.RESTFUL_NAME_NOT_UNIQUE

    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str,
            document_uuid: str, document_name: str, element: dict,
            module_role_uuid_set, app_func_uuid_dict,
            app_restful_uri_set, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, *args, **kwargs)
        self.module_role_uuid_set = module_role_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_restful_uri_set = app_restful_uri_set
        self.versions_restful_uri_set = set()
        self.requests = list()

    def initialize(self):
        super().initialize()
        self.authorization = self.element.get("authorization", 0)
        self.anonymous_role = self.element.get("anonymous_role")
        self.basic_auth = self.element.get("basic_auth", list())
        self.versions = self.element.get("versions", list())
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []

    def build_insert_query_data(self):
        return {
            RestfulTable.app_uuid.name: self.app_uuid,
            RestfulTable.module_uuid.name: self.module_uuid,
            RestfulTable.document_uuid.name: self.document_uuid,
            RestfulTable.restful_uuid.name: self.element_uuid,
            RestfulTable.restful_name.name: self.element_name,
            RestfulTable.auth_type.name: self.authorization,
            RestfulTable.restful_uri_list.name: list(
                self.versions_restful_uri_set),
            RestfulTable.requests.name: self.requests,
        }

    def build_update_query_data(self):
        return {
            RestfulTable.restful_name.name: self.element_name,
            RestfulTable.auth_type.name: self.authorization,
            RestfulTable.restful_uri_list.name: list(
                self.versions_restful_uri_set),
            RestfulTable.requests.name: self.requests,
            RestfulTable.is_delete.name: False
        }

    def build_update_query(self):
        query_data = self.build_update_query_data()
        return RestfulTable.update(**query_data).where(
            RestfulTable.restful_uuid == self.element_uuid)

    @staticmethod
    def build_delete_query(restful_uuid):
        return RestfulTable.update(**{
                RestfulTable.is_delete.name: True
            }).where(RestfulTable.restful_uuid == restful_uuid)

    def check_modify(self, document_restful_uuid_set):
        if self.element_uuid in document_restful_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

    @checker.run
    def check_restful(self):
        # basic auth使用柠檬的用户名和密码，不用检查
        # if self.authorization == RestfulAuthorizationType.BASIC:
        #     self.check_auth()
        if self.authorization == RestfulAuthorizationType.ANONYMOUS:
            self.check_role()
        if isinstance(self.versions, list):
            for version in self.versions:
                version_checker_service = VersionCheckerService(
                    app_uuid=self.app_uuid, module_uuid=self.module_uuid,
                    module_name=self.module_name,
                    document_uuid=self.document_uuid,
                    document_name=self.document_name, element=version,
                    app_func_uuid_dict=self.app_func_uuid_dict,
                    versions_restful_uri_set=self.versions_restful_uri_set
                )
                version_checker_service.check_all()
                self.update_any_list(version_checker_service)
                self.requests.append({
                    "version": version_checker_service.version,
                    "children": version_checker_service.version_requests})
        else:
            attr = Restful.ATTR.CONTENT
            return_code = LDEC.RESTFUL_CONTENT_ERROR
            return_code.message = return_code.message.format(
                name=self.document_name)
            self._add_error_list(attr=attr, return_code=return_code)
        # 如果当前文档里的uri和其他文档里的uri有重复
        intersection = self.versions_restful_uri_set & self.app_restful_uri_set
        if intersection:
            attr = Restful.ATTR.REQUEST
            return_code = LDEC.RESTFUL_RESOUCE_EXIST
            return_code.message = return_code.message.format(
                name=self.document_name,
                resources=",".join(list(intersection)))
            self._add_error_list(attr=attr, return_code=return_code)

    def check_auth(self):
        if isinstance(self.basic_auth, list):
            for auth in self.basic_auth:
                auth_checker_service = AuthCheckerService(
                    app_uuid=self.app_uuid, module_uuid=self.module_uuid,
                    module_name=self.module_name,
                    document_uuid=self.document_uuid,
                    document_name=self.document_name, element=auth,
                    app_func_uuid_dict=self.app_func_uuid_dict,
                    module_role_uuid_set=self.module_role_uuid_set
                )
                auth_checker_service.check_all()
                self.update_any_list(auth_checker_service)
        else:
            attr = Restful.ATTR.CONTENT
            return_code = LDEC.RESTFUL_CONTENT_ERROR
            return_code.message = return_code.message.format(
                name=self.document_name)
            self._add_error_list(attr=attr, return_code=return_code)

    def check_role(self):
        if self.anonymous_role not in self.module_role_uuid_set:
            attr = Restful.ATTR.ANONYMOUS_ROLE
            return_code = LDEC.RESTFUL_ANONYMOUS_ROLE_ERROR
            return_code.message = return_code.message.format(
                name=self.document_name)
            self._add_error_list(attr=attr, return_code=return_code)


class RestfulDocumentCheckerService(DocumentCheckerService):

    def __init__(
            self, app_uuid: str, module_uuid: str, module_name: str,
            document_uuid: str, document_name: str, element: dict,
            document_version: int, app_func_uuid_dict: dict,
            module_role_uuid_set: set, app_restful_list: list,
            *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name,
            element, RestfulTable, *args, **kwargs)
        self.document_version = document_version
        self.app_func_uuid_dict = app_func_uuid_dict
        self.module_role_uuid_set = module_role_uuid_set
        self.app_restful_uuid_set = set()
        self.app_restful_uri_set = set()
        self.document_restful_uuid_set = set()

        for restful_dict in app_restful_list:
            restful_uuid = restful_dict.get("restful_uuid", "")
            # restful_module_uuid = restful_dict.get("module_uuid", "")
            restful_document_uuid = restful_dict.get("document_uuid", "")
            restful_uri_set = set(restful_dict.get("restful_uri_list", list()))

            # 找到原文档中所有的 RESTful ，为了新增、更新、删除文档的 RESTful
            if restful_document_uuid == self.document_uuid:
                self.document_restful_uuid_set.add(restful_uuid)
            else:
                # 排除当前文档所有的 restful_uuid ，获取应用的所有 restful_uuid
                if not restful_dict.get("is_delete"):
                    self.app_restful_uuid_set.add(restful_uuid)
                    self.app_restful_uri_set = (
                        self.app_restful_uri_set | restful_uri_set)

    @checker.run
    def check_restful(self):
        restful_checker_service = RestfulCheckerService(
            self.app_uuid, self.module_uuid, self.module_name,
            self.document_uuid, self.document_name, self.element,
            app_func_uuid_dict=self.app_func_uuid_dict,
            module_role_uuid_set=self.module_role_uuid_set,
            app_restful_uri_set=self.app_restful_uri_set, is_copy=self.is_copy)
        restful_checker_service.document_other_info = self.document_other_info
        restful_uuid = restful_checker_service.element_uuid
        try:
            restful_checker_service.check_uuid()
            restful_checker_service.check_uuid_unique(
                self.app_restful_uuid_set)
            restful_checker_service.check_all()
        except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
            self.update_any_list(restful_checker_service)
            raise e
        else:
            self.update_any_list(restful_checker_service)

        # 找到新增 或 更新的 连接器
        restful_checker_service.check_modify(self.document_restful_uuid_set)
        if restful_checker_service.insert_query_list:
            self.document_insert_list.extend(
                restful_checker_service.insert_query_list)
        if restful_checker_service.update_query_list:
            self.document_update_list.extend(
                restful_checker_service.update_query_list)

        # 找出删除的 连接器 ，将其 is_delete 置为 True
        delete_restful_uuid_set = self.document_restful_uuid_set - set(
            [restful_uuid])
        for this_restful_uuid in delete_restful_uuid_set:
            query = restful_checker_service.build_delete_query(
                this_restful_uuid)
            self.document_delete_list.append(query)
