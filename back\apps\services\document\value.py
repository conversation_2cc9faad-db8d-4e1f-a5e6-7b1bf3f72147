# -*- coding:utf-8 -*-

from apps.utils import <PERSON><PERSON>, lemon_uuid
from apps.ide_const import ValueEditorType


class ValueEditor(Json):

    def __init__(self, editor_type, value_type=None, once=False, is_monitor=False, *args, **kwargs):
        self.uuid = lemon_uuid()
        self.type = editor_type
        self.value_type = value_type
        self.once = once
        self.is_monitor = is_monitor
        super().__init__(*args, **kwargs)
    

class StringValue(ValueEditor):

    def __init__(self, value_type, value, *args, **kwargs):
        editor_type = ValueEditorType.STRING
        super().__init__(editor_type, value_type, *args, **kwargs)
        self.value = value


class ConstValue(ValueEditor):

    def __init__(self, value_type, module_uuid, const_uuid, *args, **kwargs):
        editor_type = ValueEditorType.CONST
        super().__init__(editor_type, value_type, *args, **kwargs)
        self.module = module_uuid
        self.const = const_uuid


class VariableValue(ValueEditor):

    def __init__(self, value_type, variable_level, variable_uuid, field_name=None, *args, **kwargs):
        editor_type = ValueEditorType.VARIABLE
        super().__init__(editor_type, value_type, *args, **kwargs)
        self.variable_level = variable_level
        self.variable_uuid = variable_uuid
        self.field_name = field_name


class FieldValue(ValueEditor):

    def __init__(self, value_type, field, path=None, aggre_func=0, *args, **kwargs):
        editor_type = ValueEditorType.FIELD
        super().__init__(editor_type, value_type, *args, **kwargs)
        self.field = field
        self.path = [] if path is None else path
        self.aggre_func = aggre_func


class ExprValue(ValueEditor):

    def __init__(self, value_type, expr, *args, **kwargs):
        editor_type = ValueEditorType.EXPR
        super().__init__(editor_type, value_type, *args, **kwargs)
        self.expr = expr
        