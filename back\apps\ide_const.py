# -*- coding: utf-8 -*-

import copy
import time
import functools
from enum import Enum as DEnum
from keyword import kwlist

from baseutils.const import BaseOperatorType
from pydantic import BaseModel, Field as BaseField
from typing import List, Optional

from baseutils.const import Code, ReturnCode

"""
*** 注意 *** 这个文件只能引入 baseutils 下的包 和 baseutils.const
"""


class DictWrapper(dict):

    def __init__(self, **kwargs):
        self.update(kwargs)
        self.initialize()

    def initialize(self):
        pass

    def __getattr__(self, key):
        if key in self:
            return self.get(key)
        return super().__getattr__(key)
    
    def __setattr__(self, name, value) -> None:
        self.update({name: value})
        return super().__setattr__(name, value)

    def delete(self, key):
        if key in self:
            del self[key]

    def clone(self):
        obj = self.__class__.__new__(self.__class__)
        obj.update(self)
        return obj


class API(object):

    NAME = "ide"
    V1   = "v1"


class IDEViewDoc(object):

    ENTITY_TYPE     = """1：数据模型 2：状态机 3：云函数 4：页面 5：图片 6：常量 7：JSON 8：枚举 15：连接器"""
    ENTITY_TYPE_ALL = """1：数据模型 2：状态机 3：云函数 4：页面 5：图片 6：常量 7：JSON 8：枚举 
    100：模型字段 101：模型关联 200：状态机事件 15：连接器"""


class EntityType(object):

    DIR       = 0  # 文件夹
    MODEL     = 1  # 文档
    SM        = 2  # 状态机
    FUNC      = 3  # 云函数
    PAGE      = 4  # 页面
    IMAGE     = 5  # 图片
    CONST     = 6  # 常量
    JSON      = 7  # JSON
    ENUM      = 8  # 枚举
    SECURITY  = 9  # 权限,模块安全
    RULECHAIN = 10 # 规则引擎
    WORKFLOW  = 11 # 工作流
    PRINT     = 12 # 打印模板
    THEME     = 13 # 应用主题
    CONNECTOR = 15 # 连接器定义
    CONNECTOR_CONFIG = 14  # 连接器配置
    EXCEL_TEMPLATE = 16  # excel导入模板
    APPROVALFLOW = 17  # 审批流
    MODULE_DEPLOY = 18
    APP_DEPLOY = 19
    OBJECT_MAPPING = 20
    API_CONFIG = 21
    RESTFUL = 22
    NAVIGATION = 23  # 导航
    APP_SECURITY = 24  # 应用安全
    DEPLOY_CONFIG = 25  # 部署配置
    WATERMARK = 26  # 水印设置
    EXPORT_TEMPLATE = 27 #导出模板
    MODULE_THEME = 28 # 模块主题
    LABEL_PRINT = 29 # 标签打印
    PERMISSION_CONFIG = 30  # 权限配置
    EXPANSION_PACK = 31  # 扩展包
    APP_LAYOUT = 32  # 应用布局
    PY_MODULE = 33

    FIELD = 100  # 模型字段
    RELATIONSHIP = 101  # 模型关联
    EVENT = 200  # 状态机事件

    CATEGORY_ENTITY = [MODEL, SM, FUNC, PAGE, IMAGE, CONST, JSON, ENUM, SECURITY, RULECHAIN,
                       WORKFLOW, PRINT, THEME, CONNECTOR, CONNECTOR_CONFIG, APPROVALFLOW,
                       MODULE_DEPLOY, APP_DEPLOY, NAVIGATION, APP_SECURITY, MODULE_THEME, LABEL_PRINT,
                       APP_LAYOUT]
    ALL = [DIR, MODEL, SM, FUNC, PAGE, IMAGE, CONST, JSON, ENUM, SECURITY, RULECHAIN,
           WORKFLOW, PRINT, THEME, CONNECTOR, CONNECTOR_CONFIG, APPROVALFLOW,
           MODULE_DEPLOY, APP_DEPLOY, NAVIGATION, APP_SECURITY, MODULE_THEME, LABEL_PRINT,
           APP_LAYOUT]


class DragType(object):

    MODEL   = "model"
    STATE   = "state"
    TRIGGER = "trigger"
    ACTION  = "action"
    PRINT_CONTAINER = "print_container"
    PRINT_NODE = "print_node"

    ALL = [MODEL, STATE, TRIGGER, ACTION, PRINT_CONTAINER, PRINT_NODE]


class MoveType(object):

    DRAG = 0
    CLICK = 1

    ALL = [CLICK, DRAG]


class ToolType(object):

    SYS  = 0
    USER = 1

    ALL = [SYS, USER]


class ToolClass(object):  # 工具种类，包括云函数、页面组件...等等

    MODEL   = 0  # 数据模型
    STATE   = 1  # 状态
    TRIGGER = 2  # 触发器
    ACTION  = 3  # 动作
    PAGE    = 4  # 页面（待定）
    PRINT   = 5  # 打印

    ALL = [MODEL, STATE, ACTION, PAGE, PRINT]


class TypeSystem(object):

    AUTO   = 0  # 自动
    OBJECT = 1  # 对象
    LIST   = 2  # 列表
    ENUM   = 3  # 枚举

    STRING   = 10  # 字符
    INTEGER  = 11  # 整数
    DECIMAL  = 12  # 小数
    BOOLEAN  = 13  # 布尔值
    DATETIME = 14  # 时间日期
    DATE     = 15  # 日期
    TIME     = 16  # 时间
    PHONE    = 17  # 电话号码
    CURRENCY = 18  # 货币
    BYTES    = 19  # 二进制
    FILE     = 20  # 文件
    FIXSTRING = 21
    JSON = 22
    IMAGE = 23  # 图片
    DATETIME_CYCLE = 24  # 日期循环
    TEXT = 25  # 多行文本
    MEDIUMTEXT = 26  # 中长度文本

    RELATION = 30  # 关联

    NOT_SUPPORT = 100


class ModuleType(object):

    SYS = 0  # 系统模块
    IMP = 1  # 导入模块
    OWN = 2  # 自定义模块
    ALL = [SYS, IMP, OWN]


class Module(object):

    TYPE = ModuleType  # 模块类型


class DocumentType(object):

    DIR       = EntityType.DIR  # 文件夹
    MODEL     = EntityType.MODEL  # 文档
    SM        = EntityType.SM  # 状态机
    FUNC      = EntityType.FUNC  # 云函数
    PAGE      = EntityType.PAGE  # 页面
    IMAGE     = EntityType.IMAGE  # 图片
    CONST     = EntityType.CONST  # 常量
    JSON      = EntityType.JSON  # JSON
    ENUM      = EntityType.ENUM  # 枚举
    SECURITY  = EntityType.SECURITY  # 权限
    RULECHAIN = EntityType.RULECHAIN  # 规则引擎
    WORKFLOW  = EntityType.WORKFLOW  # 工作流
    PRINT     = EntityType.PRINT  # 打印模板
    LABEL_PRINT = EntityType.LABEL_PRINT #标签打印
    THEME     = EntityType.THEME # 应用主题
    CONNECTOR = EntityType.CONNECTOR # 连接器
    EXCEL_TEMPLATE = EntityType.EXCEL_TEMPLATE # excel导入模板
    APPROVALFLOW = EntityType.APPROVALFLOW
    MODULE_DEPLOY = EntityType.MODULE_DEPLOY
    APP_DEPLOY = EntityType.APP_DEPLOY
    OBJECT_MAPPING = EntityType.OBJECT_MAPPING
    API_CONFIG = EntityType.API_CONFIG
    RESTFUL = EntityType.RESTFUL
    NAVIGATION = EntityType.NAVIGATION
    APP_SECURITY = EntityType.APP_SECURITY
    DEPLOY_CONFIG = EntityType.DEPLOY_CONFIG
    WATERMARK = EntityType.WATERMARK
    EXPORT_TEMPLATE = EntityType.EXPORT_TEMPLATE
    MODULE_THEME = EntityType.MODULE_THEME  # 模块主题
    PERMISSION_CONFIG = EntityType.PERMISSION_CONFIG
    EXPANSION_PACK = EntityType.EXPANSION_PACK  # 扩展包管理
    APP_LAYOUT = EntityType.APP_LAYOUT  # 应用布局
    PY_MODULE = EntityType.PY_MODULE

    FILE = [
        MODEL, SM, FUNC, PAGE, IMAGE, CONST, JSON, ENUM, SECURITY, RULECHAIN,
        WORKFLOW, PRINT, LABEL_PRINT, THEME, CONNECTOR, APPROVALFLOW, MODULE_DEPLOY,
        APP_DEPLOY, OBJECT_MAPPING, API_CONFIG, RESTFUL, NAVIGATION,
        APP_SECURITY, DEPLOY_CONFIG, WATERMARK, MODULE_THEME, APP_LAYOUT
    ]
    SYSTEM_TYPE = [SECURITY, MODEL, MODULE_DEPLOY, PERMISSION_CONFIG, EXPANSION_PACK]
    ALL = [
        DIR, MODEL, SM, FUNC, PAGE, IMAGE, CONST, JSON, ENUM, SECURITY,
        RULECHAIN, WORKFLOW, PRINT, LABEL_PRINT, THEME, CONNECTOR, APPROVALFLOW,
        EXCEL_TEMPLATE, MODULE_DEPLOY, APP_DEPLOY, OBJECT_MAPPING, API_CONFIG,
        RESTFUL, NAVIGATION, APP_SECURITY, DEPLOY_CONFIG, WATERMARK, EXPORT_TEMPLATE, MODULE_THEME,
        APP_LAYOUT, PY_MODULE
    ]


class DocumentName:

    DIR = "目录"
    MODEL = "数据模型"
    SM = "状态机"
    FUNC = "云函数"
    PAGE = "页面"
    IMAGE = "图片表"
    CONST = "常量"
    JSON = "JSON"
    ENUM = "枚举"
    SECURITY = "模块安全"
    RULECHAIN = "规则引擎"
    WORKFLOW = "工作流"
    PRINT = "打印模板"
    THEME = "主题配置"
    CONNECTOR = "连接器"
    EXCEL_TEMPLATE = "Excel导入导出"
    APPROVALFLOW = "审批流"
    MODULE_DEPLOY = "模块配置"
    APP_DEPLOY = "应用配置"
    OBJECT_MAPPING = "对象映射"
    API_CONFIG = "API配置"
    RESTFUL = "服务器API"
    NAVIGATION = "导航"
    APP_SECURITY = "应用安全"
    DEPLOY_CONFIG = "部署配置"
    WATERMARK = "水印设置"
    EXPORT_TEMPLATE = "导入导出模板"
    MODULE_THEME = "主题"
    LABEL_PRINT = "标签打印"
    APP_LAYOUT = "应用布局"

    # TODO 有需要按type搜索的，记得加进来
    NAME_TO_TYPE = {
        MODEL: DocumentType.MODEL,
        FUNC: DocumentType.FUNC,
        PAGE: DocumentType.PAGE,
        IMAGE: DocumentType.IMAGE,
        CONST: DocumentType.CONST,
        JSON: DocumentType.JSON,
        ENUM: DocumentType.ENUM,
        SECURITY: DocumentType.SECURITY,
        RULECHAIN: DocumentType.RULECHAIN,
        WORKFLOW: DocumentType.WORKFLOW,
        PRINT: DocumentType.PRINT,
        CONNECTOR: DocumentType.CONNECTOR,
        EXCEL_TEMPLATE: DocumentType.EXCEL_TEMPLATE,
        APPROVALFLOW: DocumentType.APPROVALFLOW,
        MODULE_DEPLOY: DocumentType.MODULE_DEPLOY,
        RESTFUL: DocumentType.RESTFUL,
        EXPORT_TEMPLATE: DocumentType.EXPORT_TEMPLATE,
        MODULE_THEME: DocumentType.MODULE_THEME,
        LABEL_PRINT: DocumentType.LABEL_PRINT,
        APP_LAYOUT: DocumentType.APP_LAYOUT
    }


class Document(object):

    TYPE = DocumentType  # 文档类型


class ElementAttr(object):

    UUID = "元素UUID"
    NAME = "元素NAME"


class Element(object):

    ATTR = ElementAttr


class ModelAttr(object):

    UUID             = "模型UUID"
    NAME             = "模型名称"
    DISPLAY_NAME     = "模型展示名称"
    NAME_FIELD       = "数据名称字段"
    DATA_FIELD       = "数据标题字段"
    SORT_FIELD       = "排序字段"
    CHECK_FIELD      = "有效性字段"
    USER_MODEL       = "所属用户关联"
    DEPARTMENT_MODEL = "所属部门关联"
    EVENT            = "模型事件"


class ModelType(object):

    MEMORY  = 0
    STORAGE = 1
    ALL     = [MEMORY, STORAGE]


class ModelDocument(object):

    TYPE = ModelType
    ATTR = ModelAttr


class FieldType(object):

    ENUM = TypeSystem.ENUM
    STRING = TypeSystem.STRING
    INTEGER = TypeSystem.INTEGER
    BOOLEAN = TypeSystem.BOOLEAN
    DECIMAL = TypeSystem.DECIMAL
    DATETIME = TypeSystem.DATETIME
    DATE = TypeSystem.DATE
    TIME = TypeSystem.TIME
    PHONE = TypeSystem.PHONE
    BINARY = TypeSystem.BYTES
    CURRENCY = TypeSystem.CURRENCY
    FILE = TypeSystem.FILE
    FIXSTRING = TypeSystem.FIXSTRING
    JSON = TypeSystem.JSON
    IMAGE = TypeSystem.IMAGE
    RELATION = TypeSystem.RELATION
    DATETIME_CYCLE = TypeSystem.DATETIME_CYCLE
    TEXT = TypeSystem.TEXT
    MEDIUMTEXT = TypeSystem.MEDIUMTEXT

    NOT_SUPPORT = TypeSystem.NOT_SUPPORT
    OBSOLETE = {DATETIME_CYCLE: "日期循环"}
    ALL = [
        ENUM, STRING, INTEGER, BOOLEAN, DECIMAL, DATETIME, FILE, IMAGE,
        RELATION, DATETIME_CYCLE, JSON
    ]


class FieldAttr(object):

    UUID = "字段UUID"
    NAME = "字段名称"
    DISPLAY_NAME = "字段展示名称"
    TYPE = "字段类型"
    ENUM = "枚举字段"
    SERIAL = "流水号"
    DECIMALS = "小数位"
    CHECKFUN = "验证方法"


class Field(object):

    TYPE = FieldType
    ATTR = FieldAttr


class IndexAttr(object):

    UUID         = "索引UUID"
    NAME         = "索引名称"
    DISPLAY_NAME = "索引展示名称"
    IS_UNIQUE    = "索引是否唯一"
    FIELDS       = "索引字段"


class Index(object):

    ATTR = IndexAttr


class RelationshipAttr(object):

    SOURCE_MODEL           = "起始模型"
    TARGET_MODEL           = "目标模型"
    UUID                   = "关联UUID"
    NAME                   = "关联名称"
    DISPLAY_NAME           = "展示名称"
    MODEL                  = "关联模型"
    SOURCE_MODEL_ON_DELETE = SOURCE_MODEL + "删除行为"
    TYPE                   = "映射关系"
    TARGET_MODEL_ON_DELETE = TARGET_MODEL + "删除行为"
    SOURCE_TARGET          = "关联方向"
    FRONTREF               = SOURCE_MODEL + "引用名称"
    BACKREF                = TARGET_MODEL + "引用名称"


class RelationshipDeleteType(object):

    NO_ACTION   = 0  # 不做操作
    CASCADE     = 1  # 级联删除
    RESTRICT    = 2  # 删除报错
    NOT_SUPPORT = 100
    ALL         = [NO_ACTION, CASCADE, RESTRICT]


class RelationshipType(object):

    ONE_TO_MANY  = 0
    MANY_TO_MANY = 1
    ONE_TO_ONE   = 2
    NOT_SUPPORT  = 100
    ALL          = [ONE_TO_MANY, MANY_TO_MANY, ONE_TO_ONE]


class Relationship(object):

    TYPE        = RelationshipType
    ATTR        = RelationshipAttr
    DELETE_TYPE = RelationshipDeleteType


class FuncAttr(object):

    UUID               = "云函数UUID"
    NAME               = "云函数名称"
    ICON_TYPE          = "云函数图标类型"
    ICON               = "云函数图标"
    ARG_UUID           = "云函数参数UUID"
    ARG_NAME           = "云函数参数名称"
    ARG_TYPE           = "云函数参数类型"
    ARG_MODEL          = "云函数参数数据模型"
    ARG_ENUM           = "云函数参数枚举类型"
    ARG_DEFAULT        = "云函数参数默认值"
    RETURN_UUID        = "云函数返回值UUID"
    RETURN_NAME        = "云函数返回值名称"
    RETURN_TYPE        = "云函数返回值类型"
    INSTALL_SM_TOOLBOX = "云函数安装状态机工具箱"
    SM_TOOL_NAME       = "状态机工具箱动作标题"
    SM_TOOL_CATEGORY   = "状态机工具箱动作分类"
    FUNC_ARGS          = "云函数参数"
    FUNC_RETURN        = "云函数返回值"


class Func(object):

    ATTR = FuncAttr


class ConnectorAttr(object):
    UUID               = "连接器UUID"
    NAME               = "连接器名称"


class Connector(object):
    ATTR = ConnectorAttr


class RestfulAttr(object):

    UUID = "服务器API UUID"
    NAME = "服务器API名称"
    CONTENT = "服务器API内容"
    ANONYMOUS_ROLE = "服务器API匿名角色"
    ACCESS_ROLE = "服务器API授权角色"
    ACCESS_FUNC = "服务器API授权云函数"
    REQUEST = "服务器API请求"


class Restful(object):

    ATTR = RestfulAttr


class RestfulAuthorizationType():

    LEMON = 0
    BASIC = 1
    ANONYMOUS = 2


class ConditionAttr(object):

    UUID     = "条件UUID"
    NAME     = "条件名称"
    TO_STATE = "目标状态"
    DEFAULT  = "默认条件"
    EXPR     = "条件表达式"
    CONDITION_TYPE     = "条件类型"


class Condition(object):

    ATTR = ConditionAttr


class TransitionAttr(object):

    UUID              = "转换UUID"
    NAME              = "转换名称"
    TO_SELF           = "自转换"
    RUN_DELAY         = "延迟转换"
    DELAY_TIME        = "延迟转换时间"
    CONDITION_UUID    = "条件UUID"
    CONDITION_NAME    = "条件名称"
    CONDITION_TYPE    = "条件类型"
    CONDITION_DEFAULT = "默认条件"
    TO_STATE          = "目标状态"


class Transition(object):

    ATTR = TransitionAttr


class ActionFuncType(object):

    NORMAL = 0  # 普通云函数
    METHOD = 1  # 组件实例方法


class ActionSyncType(object):

    AFTER_ACTION = 0
    AFTER_STATE  = 1
    NONE         = 2
    NOT_SUPPORT  = 100

    ALL = [AFTER_ACTION, AFTER_STATE, NONE]


class ActionAttr(object):

    UUID                  = "动作UUID"
    NAME                  = "动作名称"
    TYPE                  = "动作类型"
    AUTO                  = "自动动作"
    FUNC                  = "动作云函数"
    RUN_ASYNC             = "动作异步执行"
    SYNC_TYPE             = "动作异步同步点"
    RUN_DELAY             = "动作延迟执行"
    DELAY_TIME            = "动作延迟执行时间"
    DATA_BIND_ARG         = "数据绑定参数"
    DATA_BIND_ARG_TYPE    = "数据绑定参数类型"
    DATA_BIND_RETURN      = "数据绑定返回值"
    DATA_BIND_RETURN_TYPE = "数据绑定返回值类型"
    DATA_BIND_VARIABLE    = "数据绑定变量"
    LOOP_VARIABLE         = "For循环变量"
    LOOP_LIST             = "For循环列表"
    CONTROL_FLOW          = "If控制流"


class ActionType(object):

    FUNC        = 0  # 普通云函数
    FOR         = 1  # for循环
    IF          = 2  # if 分支
    BREAK       = 3  # break
    CONTINUE    = 4  # continue
    EXCEPTION   = 5  # 异常处理
    NOT_SUPPORT = 100

    ALL = [FUNC, FOR, IF, BREAK, CONTINUE, EXCEPTION]


class Action(object):

    TYPE      = ActionType
    ATTR      = ActionAttr
    SYNC_TYPE = ActionSyncType


class TriggerAttr(object):

    UUID                = "触发器UUID"
    NAME                = "触发器名称"
    TYPE                = "触发器类型"
    EVENT               = "触发器事件"
    EVENT_ARG           = "触发器事件参数"
    EVENT_BIND_VARIABLE = "触发器事件关联变量"
    TIMER               = "触发器定时器"


class TriggerType(object):

    AUTO        = 0
    EVENT       = 1
    TIMER       = 2
    NOT_SUPPORT = 100

    ALL = [AUTO, EVENT, TIMER]


class Trigger(object):

    TYPE = TriggerType
    ATTR = TriggerAttr


class IntervalType(object):

    SECOND      = 0
    MINUTE      = 1
    HOUR        = 2
    DAY         = 3
    WEEK        = 4
    TWO_WEEK    = 5
    MONTH       = 6
    YEAR        = 7
    NOT_SUPPORT = 100

    REGULAR = [SECOND, MINUTE, HOUR, DAY, WEEK, TWO_WEEK]
    ALL     = [SECOND, MINUTE, HOUR, DAY, WEEK, TWO_WEEK, MONTH, YEAR]


class PeriodType(object):

    DAY         = 0
    WEEK        = 1
    MONTH       = 2
    NOT_SUPPORT = 100

    ALL = [DAY, WEEK, MONTH]


class TimerAttr(object):

    UUID            = "定时器UUID"
    NAME            = "定时器名称"
    TYPE            = "定时器类型"
    START_TIMESTAMP = "定时器开始时间"
    INTERVAL_TYPE   = "间隔类型"
    INTERVAL_VALUE  = "间隔值"
    PERIOD_TYPE     = "周期重复类型"
    PERIOD_VALUE    = "周期重复值"
    EVENT_LIST      = "事件列表"


class TimerType(object):

    ONCE        = 0
    INTERVAL    = 1
    PERIOD      = 2
    NOT_SUPPORT = 100
    ALL         = [ONCE, INTERVAL, PERIOD]


class Timer(object):

    TYPE          = TimerType
    INTERVAL_TYPE = IntervalType
    PERIOD_TYPE   = PeriodType
    ATTR          = TimerAttr


class VariableLevel(object):

    SYSTEM = 0
    SM     = 1
    GLOBAL = 2
    LOCAL  = 3
    FLOW = 4

    NOT_SUPPORT = 100


class VariableAttr(object):

    UUID    = "变量UUID"
    NAME    = "变量名称"
    TYPE    = "变量类型"
    DEFAULT = "变量初始值"
    MODEL   = "变量模型"


class VariableAttrStateLocal(object):

    UUID    = "状态局部变量UUID"
    NAME    = "状态局部变量名称"
    TYPE    = "状态局部变量类型"
    DEFAULT = "状态局部变量初始值"
    MODEL   = "状态局部变量模型"


class VariableType(object):

    AUTO   = TypeSystem.AUTO
    OBJECT = TypeSystem.OBJECT
    LIST   = TypeSystem.LIST
    ENUM   = TypeSystem.ENUM

    STRING   = TypeSystem.STRING
    INTEGER  = TypeSystem.INTEGER
    DECIMAL  = TypeSystem.DECIMAL
    BOOLEAN  = TypeSystem.BOOLEAN
    DATETIME = TypeSystem.DATETIME
    BYTES    = TypeSystem.BYTES

    NOT_SUPPORT = 100

    WITH_MODEL = [AUTO, OBJECT, LIST]
    ALL        = [AUTO, OBJECT, LIST, ENUM, STRING, INTEGER, DECIMAL, BOOLEAN, DATETIME, BYTES]


class Variable(object):

    TYPE = VariableType
    ATTR = VariableAttr


class VariableStateLocal(object):

    ATTR = VariableAttrStateLocal


class EventAttr(object):

    UUID        = "事件UUID"
    NAME        = "事件名称"
    TYPE        = "事件类型"
    ARG_NAME    = "事件参数名称"
    ARG_TYPE    = "事件参数类型"
    ARG_DEFAULT = "事件参数默认值"
    ARG_MODEL   = "事件参数模型"
    ARG_ENUM    = "时间参数枚举"


class Event(object):

    ATTR = EventAttr


class HandlerType(object):

    ROLE        = 0  # 按角色
    VALUE       = 1  # 值编辑器
    MANAGER     = 2  # 上层主管
    CREATOR     = 3  # 发起人
    EXTRA_ASSIGN = 4  # 运行时指定
    NOT_SUPPORT = 100

    ALL = [ROLE, VALUE, MANAGER, CREATOR, EXTRA_ASSIGN]


class HandleType(object):

    ORDER       = 0  # 依次处理
    EACH        = 1  # 同时处理
    PREEMPTION  = 2  # 抢单
    NOT_SUPPORT = 100

    ALL = [ORDER, EACH, PREEMPTION]


class HandleCondition(object):

    COUNT       = 0
    RATE        = 1
    NOT_SUPPORT = 1001

    ALL = [COUNT, RATE]


class StateType(object):

    AUTO        = 0
    MANUAL      = 1
    CHILD       = 2
    START       = 3
    END         = 4
    NOT_SUPPORT = 100

    ALL = [AUTO, MANUAL, CHILD, START, END]


class StateAttr(object):

    UUID             = "状态UUID"
    NAME             = "状态名称"
    TYPE             = "状态类型"
    HANDLER_TYPE     = "处理人类型"
    HANDLER_VALUE    = "处理人"
    HANDLE_TYPE      = "多处理人规则"
    HANDLE_CONDITION = "多处理人跳转条件"
    HANDLE_VALUE     = "多处理人跳转条件值"
    PAGE             = "表单页面"
    LOCAL_VARIABLE   = "局部变量"
    CHILD_SM         = "子状态机"
    RECOVERY         = "子状态恢复上次退出状态"


class State(object):

    TYPE             = StateType
    ATTR             = StateAttr
    HANDLER_TYPE     = HandlerType
    HANDLE_TYPE      = HandleType
    HANDLE_CONDITION = HandleCondition


class StateMachineAttr(object):

    UUID           = "状态机UUID"
    NAME           = "状态机名称"
    MULTI_INSTANCE = "多实例运行"
    STRICT_MODE    = "严格模式"
    START_STATE    = "开始状态"


class StateMachine(object):

    ATTR = StateMachineAttr


class RuleChainAttr(object):

    UUID = "规则引擎UUID"
    NAME = "规则引擎名称"


class RuleChain(object):

    ATTR = RuleChainAttr


class NodeLineAttr(object):

    UUID = "线UUID"
    NAME = "线名称"
    DEFAULT = "线默认条件"
    ACTION = "人工节点动作"
    TO_NODE = "跳转节点"


class NodeLine(object):

    ATTR = NodeLineAttr


class NodeTimeoutType(object):

    MESSAGE = 0
    TO_NODE = 1
    ASSIGN = 2

    ALL = [MESSAGE, TO_NODE, ASSIGN]


class NodeTimeoutAttr(object):

    UUID = "超时规则UUID"
    NAME = "超时规则名称"
    TYPE = "超时动作"
    TIMEOUT = "超时设定"
    TIMEOUT_DAY = "超时设定（天）"
    TIMEOUT_HOUR = "超时设定（小时）"
    URGING = "催办间隔"
    URGING_DAY = "催办间隔（天）"
    URGING_HOUR = "催办间隔（小时）"
    URGING_LIMIT = "催办次数上限"
    TIMEOUT_TO_NODE = "超时跳转节点"


class NodeTimeout(object):

    TYPE = NodeTimeoutType
    ATTR = NodeTimeoutAttr


class NodeAssignType(object):

    NORMAL = 0  # 正常转交
    DISABLE = 1  # 停用转交
    DELETE = 2  # 删除转交


class NodeSystemActionRule(object):

    ALL_ACCEPT = 0
    ONE_ACCEPT = 1
    ONE_REJECT = 2

    ALL = [ALL_ACCEPT, ONE_ACCEPT, ONE_REJECT]


class NodeSystemActionType(object):

    SUBMIT = "870c49aa617011eb858184c5a603df26"
    RESTART = "a3a23300bcb545b084d3633b4477b3c5"
    CANCEL = "333b7c2e29c153f89e7c5860e433f19b"
    ACCEPT = "bb48ab4c4f4a11eba91984c5a603df26"
    REJECT = "bb7fa3224f4a11eba91984c5a603df26"
    ASSIGN = "51bf460a5be211eb858184c5a603df26"
    RETURN = "60cff8245be211eb858184c5a603df26"
    INVALID = "cc9cb3b74028584ca434e6e4880f94d0"
    REVOKE = "e7361d765c1c59bea405cc4915de7dab"
    WITHDRAW = "5d490f1cea46588888c3e22bae7982da"  # 与REVOKE功能一致, 是否应使用同一UUID
    COUNTERSIGN = "457b573612f1547abbdfc6e85a074a7f"
    HANDLE_SIGNATURE = "7d13eaba600a4e65a6b740ae4a302625"
    FORM_BUTTON = 'd68e5a0d10945e02aae3c975668d4918'   # 工作流按钮布局表单按钮

    SUBMIT_NAME = "发起"
    RESTART_NAME = "再次发起"
    CANCEL_NAME = "取消"  # 在开始节点取消发起
    ACCEPT_NAME = "同意"
    REJECT_NAME = "拒绝"
    ASSIGN_NAME = "转交"
    RETURN_NAME = "回退"
    INVALID_NAME = "作废"
    REVOKE_NAME = "撤审"
    WITHDRAW_NAME = "撤回"
    COUNTERSIGN_NAME = "加签"
    HANDLE_SIGNATURE_NAME = "处理加签"
    FORM_BUTTON_NAME = '表单按钮'

    ALL = [ACCEPT, REJECT]

    ALL_EVENT = {
        SUBMIT: SUBMIT_NAME,
        RESTART: RESTART_NAME,
        ACCEPT: ACCEPT_NAME,
        REJECT: REJECT_NAME,
        ASSIGN: ASSIGN_NAME,
        RETURN: RETURN_NAME,
        CANCEL: CANCEL_NAME,
        INVALID: INVALID_NAME,
        REVOKE: REVOKE_NAME,
        WITHDRAW: WITHDRAW_NAME,
        COUNTERSIGN: COUNTERSIGN_NAME,
        HANDLE_SIGNATURE: HANDLE_SIGNATURE_NAME
    }
    ALL_EVENT_NAME = {value: key for key, value in ALL_EVENT.items()}
    # ACCEPT/REJECT uuid固定,但不再认为是系统action
    ALL_SYSTEM_EVENT = copy.deepcopy(ALL_EVENT)
    ALL_SYSTEM_EVENT.pop(ACCEPT)
    ALL_SYSTEM_EVENT.pop(REJECT)
    ALL_SYSTEM_EVENT.pop(ASSIGN)
    ALL_SYSTEM_EVENT.pop(HANDLE_SIGNATURE)

    ALL_ATTR_EVENT = [ASSIGN, RETURN, WITHDRAW, REVOKE, INVALID]


class NodeCustomActionRuleType(object):

    COUNT = 0
    RATE = 1
    ALL = [COUNT, RATE]


class NodeCustomActionAttr(object):

    UUID = "处理动作UUID"
    NAME = "处理动作名称"
    RULE_TYPE = "处理动作跳转类型"
    RULE_VALUE = "处理动作跳转值"


class NodeCustomAction(object):

    ATTR = NodeCustomActionAttr
    RULE_TYPE = NodeCustomActionRuleType


class NodeSystemAction(object):

    ATTR = NodeCustomActionAttr  # 目前看起来可以使用自定义动作attr
    TYPE = NodeSystemActionType


class NodeMessageType(object):

    ENTRY_NODE  = 0
    EXIT_NODE   = 1
    CREATE_TASK = 2
    CLOSE_TASK  = 3

    ALL = [ENTRY_NODE, EXIT_NODE, CREATE_TASK, CLOSE_TASK]


class NodeMessageAttr(object):

    UUID = "消息发送UUID"
    NAME = "消息发送名称"
    TYPE = "消息发送时机类型"


class NodeMessage(object):

    TYPE = NodeMessageType
    ATTR = NodeMessageAttr


class NodeRejectAction:

    TO_END = 0  # 前往结束
    TO_LAST = 1  # 类似撤审, 回到上一个
    FOLLOW_LINE = 2  # 按线规则


class AutoProcessRule:

    PROCESSED = 0
    PROCESSED_LAST = 1
    CLOSED = 2


class ParallelEndRule:

    FINISH_ALL = 0
    FINISH_PART = 1


class NodeType(object):

    ORIGIN = -1
    START  = 0
    END    = 1
    MANUAL = 2
    AUTO   = 3
    PARALLEL = 4
    COPY = 5
    SUBFLOW = 6
    COUNTERSIGN = 7

    ALL = [START, END, MANUAL, AUTO, PARALLEL, COPY, SUBFLOW, COUNTERSIGN]


class NodeAttr(object):

    UUID = "节点UUID"
    NAME = "节点名称"
    TYPE = "节点类型"
    LINE = "节点线"
    FUNC = "自动节点云函数"
    ROLE = "自动节点模块角色"
    MESSAGE = "消息发送"
    HANDLER = "节点处理人"
    TO_PAGE = "人工节点处理页面"
    TO_COPY_PAGE = "抄送节点抄送页面"
    PC_TO_PAGE = "人工节点 PC 处理页面"
    MOBILE_TO_PAGE = "人工节点 手机 处理页面"
    PAD_TO_PAGE = "人工节点 PAD 处理页面"
    TOTAL_TO_PAGE = "处理页面"
    PC_TO_COPY_PAGE = "抄送节点 PC 抄送页面"
    MOBILE_TO_COPY_PAGE = "抄送节点 手机 抄送页面"
    PAD_TO_COPY_PAGE = "抄送节点 PAD 抄送页面"
    TOTAL_TO_COPY_PAGE = "抄送页面"
    ACTION = "人工节点处理动作"
    EVENT = "节点事件"
    BRANCH = "并行节点分支"
    TIMER = "开始节点定时"
    SUBFLOW = "子流程设置"


class NodeTaskStatus(object):

    DOING = 0  # 未处理
    DONE = 1  # 已处理


class NodeTaskResult(object):

    DOING = 0  # 未处理
    SUCCESS = 1  # 已XXX（根据动作名称决定）
    FAILED = 2  # 失败


class NodeStatus(object):

    DOING = 0  # 未处理
    DONE = 1  # 已处理
    STOPPED = 2  # 已终止
    ERROR = 3  # 异常
    PAUSED = 4  # 已暂停
    COUNTERSIGN = 5  # 加签


class Node(object):

    TYPE = NodeType
    ATTR = NodeAttr
    STATUS = NodeStatus
    TASK_STATUS = NodeTaskStatus
    TASK_RESULT = NodeTaskResult


class WorkflowAttr(object):

    UUID = "工作流UUID"
    NAME = "工作流名称"
    VERSION = "工作流版本"
    NODE = "工作流节点"
    START_NODE = "工作流开始节点"
    END_NODE = "工作流结束节点"
    TOTAL_FROM_PAGE = "工作流发起页面"
    PC_FROM_PAGE = "工作流 PC 发起页面"
    MOBILE_FROM_PAGE = "工作流 手机 发起页面"
    PAD_FROM_PAGE = "工作流 PAD 发起页面"
    TOTAL_TO_PAGE = "工作流处理页面"
    PC_TO_PAGE = "工作流 PC 处理页面"
    MOBILE_TO_PAGE = "工作流 手机 处理页面"
    PAD_TO_PAGE = "工作流 PAD 处理页面"


class WorkflowStatus(object):

    EXEC = 0  # 运行
    DONE = 1  # 完成
    STOPPED = 2  # 终止
    ERROR = 3  # 异常
    PAUSED = 4  # 暂停
    CANCEL = 5  # 用户主动取消

    NOT_EXEC = [DONE, STOPPED, ERROR, PAUSED, CANCEL]
    IRR_STATUS = [DONE, STOPPED, ERROR]   # 不可逆的状态


class Workflow(object):

    ATTR = WorkflowAttr
    STATUS = WorkflowStatus


class WorkflowHandleType(object):
    EXEC = 0  # 恢复
    DONE = 1  # 完成
    STOPPED = 2  # 终止
    ERROR = 3  # 异常
    PAUSED = 4  # 暂停

    ALL_HANDLE_TYPE = [EXEC, DONE, STOPPED, ERROR, PAUSED]
    HANDLE_TYPE_DICT = {
        "恢复": 0,
        "完成": 1,
        "终止": 2,
        "异常": 3,
        "暂停": 4
    }
    HANDLE_NAME_DICT = {value: key for key, value in HANDLE_TYPE_DICT.items()}

   
class ApprovalFlowAttr:
    UUID = "审批流UUID"
    NAME = "审批流名称"
    VERSION = "审批流版本"
    START_NODE = "审批流开始节点"
    END_NODE = "审批流结束节点"
    PC_FROM_PAGE = "审批流 PC 发起页面"
    MOBILE_FROM_PAGE = "审批流 手机 发起页面"
    PAD_FROM_PAGE = "审批流 PAD 发起页面"
    PC_TO_PAGE = "审批流 PC 处理页面"
    MOBILE_TO_PAGE = "审批流 手机 处理页面"
    PAD_TO_PAGE = "审批流 PAD 处理页面"


class ApprovalFlowStatus(WorkflowStatus):
    ...


class ApprovalFlow(object):

    ATTR = ApprovalFlowAttr
    STATUS = ApprovalFlowStatus


class WxSourceType():
    
    APPLET = 3
    H5 = 4

    SOURCE_TYPE = {
        3: "applet",
        4: "h5"
    }

    GET_TARGET_STR = {
        3: "h5",
        4: "applet"
    }


class DeviceType(object):

    PC = 0
    MOBILE = 1
    PAD = 2

    DEVICE_TYPE = {
        0: "pc",
        1: "mobile",
        2: "pad"
    }

    DEVICE_TYPE_TO_PLATFORM = {
        PC: 0,  # PC 实际上是1, 目前处理上PC完全使用了响应式导航
        MOBILE: 3,
        PAD: 2
    }


class ValueEditorType(object):

    STRING   = 0
    CONST    = 1
    VARIABLE = 2
    EXPR     = 3
    FIELD    = 4
    ENUM = 5

    TO_DICT = {
        STRING  : "文字常量",
        CONST   : "符号常量",
        VARIABLE: "变量",
        EXPR    : "表达式",
        FIELD   : "字段",
        ENUM: "枚举"
    }

    NOT_SUPPORT = 100

    EXPR_NESTED = [CONST, VARIABLE, FIELD, ENUM]
    ALL = [STRING, CONST, VARIABLE, EXPR, FIELD, ENUM]


class ValueEditor(object):

    TYPE = ValueEditorType


class IconType(object):

    SYSTEM = 0  # 系统图标
    ICON   = 1  # 字体图标
    IMAGE  = 2  # 图片表

    NOT_SUPPORT = 100

    ALL = [SYSTEM, ICON, IMAGE]


class Icon(object):

    TYPE = IconType


class ImageAttr(object):

    UUID   = "图片UUID"
    NAME   = "图片名称"
    TYPE   = "图片类型"
    SIZE   = "图片尺寸"
    WIDTH  = "图片宽度"
    HEIGHT = "图片高度"
    MD5    = "图片哈希"
    EVENT = "图片事件"


class AuditStatusType(object):

    # 0: 审核成功, 1: 审核被拒绝, 2: 审核中, 3: 已撤回, 4: 审核延后
    SUCCESS = 0
    REJECT = 1
    AUDITING = 2
    WITHDRAW = 3
    POSTPONE = 4

    NOTUPDATE = [SUCCESS, REJECT, WITHDRAW]
    NEEDUPDATE = [AUDITING, POSTPONE]

class WxMediaType(object):
    
    MP4 = 0

    PNG = 1
    JPEG = 2
    JPG = 3
    GIF = 4

    video_type = ["mp4"]
    image_type = ["png", "jpeg", "jpg", "gif"]
    
    max_video_size = 10 * 1024 * 1024
    max_image_size = 2 * 1024 * 1024


class VideoType(object):

    AVI = 6
    QUICKTIME = 7
    MP4 = 8
    MPEG = 9
    WMV = 10
    FLV = 11
    MKV = 12
    RMVB = 13
    _3GP = 14
    WEBM = 15
    RM = 16
    SWF = 17

    DICT = {
        6: ".avi",
        7: ".mov",
        8: ".mp4",
        9: ".mpg",
        10: ".wmv",
        11: ".flv",
        12: ".mkv",
        13: ".rmvb",
        14: ".3gp",
        15: "webm",
        16: ".rm",
        17: ".swf"
    }

    ALL = [AVI, QUICKTIME, MP4, MPEG, WMV, FLV, MKV, RMVB, _3GP, WEBM, RM, SWF]


class ImageType(object):

    JPG = 1
    PNG = 2
    BMP = 3
    GIF = 4
    SVG = 5

    NOT_SUPPORT = 100

    DICT = {
        1: ".jpg",
        2: ".png",
        3: ".bmp",
        4: ".gif",
        5: ".svg"
    }
    TYPE_TO_DICT = {
        "JPEG": JPG,
        "PNG" : PNG,
        "BMP" : BMP,
        "GIF" : GIF,
        "SVG" : SVG
    }

    DICT_TO_TYPE = dict(zip(TYPE_TO_DICT.values(), TYPE_TO_DICT.keys()))

    ALL = [JPG, PNG, BMP, GIF, SVG]


class ImageStorageType(object):

    FILE = 0
    OSS  = 1

    ALL = [FILE, OSS]


class Image(object):

    TYPE         = ImageType
    ATTR         = ImageAttr
    STORAGE_TYPE = ImageStorageType


class ImageShowAttr(object):
    UUID  = "图片展示UUID"
    NAME  = "图片展示名称"
    EVENT = "图片展示事件"
    BADGE = "徽标数"


class ImageShow(object):

    ATTR = ImageShowAttr


class WatermarkAttr(object):
    IMAGEWATERMARK = "图片水印"


class WatermarkType(object):
    CHARACTER = 1
    IMAGE = 2


class Watermark(object):
    TYPE = WatermarkType
    ATTR = WatermarkAttr


class ConstAttr(object):

    UUID  = "常量UUID"
    NAME  = "常量名称"
    TYPE  = "常量类型"
    VALUE = "常量值"


class ConstType(object):

    STRING      = TypeSystem.STRING
    INTEGER     = TypeSystem.INTEGER
    DECIMAL     = TypeSystem.DECIMAL
    BOOLEAN     = TypeSystem.BOOLEAN
    NOT_SUPPORT = TypeSystem.NOT_SUPPORT

    TO_PYTHON_CLASS = {
        STRING : str,
        INTEGER: int,
        DECIMAL: float,
        BOOLEAN: bool
    }

    ALL = [STRING, INTEGER, DECIMAL, BOOLEAN]


class JsonAttr(object):

    UUID  = "JSON UUID"
    NAME  = "JSON 名称"
    VALUE = "JSON 值"


class Const(object):

    TYPE = ConstType
    ATTR = ConstAttr


class Json(object):

    ATTR = JsonAttr


class EnumAttr(object):

    UUID           = "枚举UUID"
    NAME           = "枚举名称"
    VALUE          = "枚举值"
    ITEM_UUID      = "枚举项UUID"
    ITEM_NAME      = "枚举项名称"
    ITEM_ICON_TYPE = "枚举项图标类型"
    ITEM_ICON      = "枚举项图标"


class Enum(object):

    ATTR = EnumAttr


class visual_Attr(object):
    name = ""
    #堆积条形
    UUID           = name + "UUID"
    NAME           = name + "名称"
    DATASOURCE     = name + "数据源"

    AXLE_VALUE     = name + "轴"
    LEGENT_VALUE   = name + "图例"
    VALUE          = name + "值"

    SHARED_AXIS     = name + "共享轴"
    COLUMN_SERIES   = name + "列图例"
    COLUMN_VALUES   = name + "列值"
    LINE_VALUES     = name + "行值"

    SEARCH_TERM     = name + "搜索项"


class visual(object):

    ATTR = visual_Attr


class TemplateAttr(object):

    UUID           = "模板UUID"
    NAME           = "模板名称"
    COLUMN         = "列"
    TEMPLATE       = "模板"
    DATASOURCE     = "数据源"


class Template(object):

    ATTR = TemplateAttr


class ExportTemplateAttr(object):

    UUID           = "模板UUID"
    NAME           = "模板名称"
    TEMPLATE       = "模板"
    DATASOURCE     = "数据源"

class ExportTemplateClass(object):

    ATTR = ExportTemplateAttr


class SecurityAttr(object):
    UUID = "模块安全UUID"
    NAME = "模块安全名称"
    MODEL = "数据模型"
    PAGE = "页面"
    FUNC = "云函数"
    FIELDS = "字段"
    PERMISSION = "权限"

class Security(object):
    ATTR = SecurityAttr

class InputAttr(object):

    UUID = "输入框控件UUID"
    NAME = "输入框控件名称"
    TYPE = "输入框所选字段类型"
    SETTING = "输入框设置"


class TextareaAttr(object):

    UUID = "多行文本框控件UUID"
    NAME = "多行文本框控件名称"
    TYPE = "多行文本框所选字段类型"


class RadioAttr(object):

    UUID = "单选框控件UUID"
    NAME = "单选框控件名称"
    TYPE = "单选框所选字段类型"


class CheckboxAttr(object):

    UUID = "复选框控件UUID"
    NAME = "复选框控件名称"
    TYPE = "复选框所选字段类型"

class DatetimeCycleAttr(object):

    UUID = "时间循环控件UUID"
    NAME = "时间循环控件名称"
    TYPE = "时间循环所选字段类型"


class SelectAttr(object):

    UUID = "下拉框控件UUID"
    NAME = "下拉框控件名称"
    TYPE = "下拉框所选字段类型"
    CANDIDATES = "下拉框候选项设置"
    DATASOURCE = "数据源"


class DatetimeAttr(object):

    UUID = "日期时间下拉框控件UUID"
    NAME = "日期时间下拉框控件名称"
    TYPE = "日期时间下拉框所选字段类型"


class RSelectAttr(object):

    UUID = "关联下拉框控件UUID"
    NAME = "关联下拉框控件名称"
    TYPE = "关联下拉框所选字段"
    VIEW_PAGE = "关联下拉框查看页面"
    NEW_PAGE = "关联下拉框新建页面"
    CANDIDATES = "关联下拉框候选项设置"
    DATASOURCE = "数据源"
    

class TransferAttr(object):
    
    UUID = "穿梭框控件UUID"
    NAME = "穿梭框控件名称"
    CANDIDATES = "关联下拉框候选项设置"
    DATASOURCE = "数据源"
    DISPLAY_COLUMN = "左侧显示字段"
    SERACH_COLUMN = "搜索字段"


class RSelectPopupAttr(object):

    UUID = "关联弹窗控件UUID"
    NAME = "关联弹窗控件名称"
    TYPE = "关联弹窗所选字段"
    VIEW_PAGE = "关联弹窗查看页面"
    MODAL_PAGE = "关联弹窗弹窗页面"
    POP_FIELD = "关联弹窗字段显示"
    CANDIDATE_PREPROCESSING = "关联弹窗候选项预处理"


class RTile(object):

    UUID = "关联平铺控件UUID"
    NAME = "关联平铺控件名称"
    TYPE = "关联平铺所选字段"
    CANDIDATE_PREPROCESSING = "关联平铺候选项预处理"


class RTreeAttr(object):

    UUID = "关联树形框控件UUID"
    NAME = "关联树形框控件名称"
    TYPE = "关联树形框所选字段"
    DATA_SOURCE = "关联树形框数据源"


class RCascadeAttr(object):

    UUID = "关联级联框控件UUID"
    NAME = "关联级联框控件名称"
    TYPE = "关联级联框所选字段"


class RSelectTableAttr(object):

    UUID = "关联选择子表控件UUID"
    NAME = "关联选择子表控件名称"


class RCreateTableAttr(object):

    UUID = "关联填写子表控件UUID"
    NAME = "关联填写子表控件名称"


class SliderAttr(object):

    UUID = "滑动输入控件UUID"
    NAME = "滑动输入控件名称"
    TYPE = "滑动输入控件所选字段类型"
    DEFAULT_NAME = "滑动输入控件默认值"


class SwitchAttr(object):

    UUID = "开关控件UUID"
    NAME = "开关控件名称"
    TYPE = "开关控件所选字段类型"


class UploadFileAttr(object):

    UUID = "上传文件控件UUID"
    NAME = "上传文件控件名称"
    TYPE = "上传文件控件所选字段类型"


class UploadImageAttr(object):

    UUID = "上传图片控件UUID"
    NAME = "上传图片控件名称"
    TYPE = "上传图片控件所选字段类型"


class FormAttr(object):

    UUID = "表单UUID"
    NAME = "表单名称"
    EVENT = "事件"
    DATASOURCE = "数据源"
    CONVENTION = "常规"
    


class DatalistAttr(object):

    UUID = "数据列表UUID"
    NAME = "数据列表名称"
    SUBTABLE = "子表"
    COLUMN = "列"
    FUNCTION_BAR = "按钮区"  # 仅更改显示名称
    DATASOURCE = "数据源"
    ROW_SETTINGS = "行"
    SELECT_PAGE = "数据列表选择页面"
    EVENT = "事件"
    PAGINATION = "分页设置"
    MULTICOLUMN_AGGRE = "多列合并汇总"


class TreeAttr(object):
    UUID = "树形组件UUID"
    NAME = "树形组件名称"
    DATASOURCE = "数据源"
    COMMON = "常规"
    OPERATE = "操作"
    EVENT = "事件"


class TreelistAttr(DatalistAttr):
    UUID = "树形列表UUID"
    NAME = "树形列表名称"
    COLUMN = "列"
    PAGINATION = "分页设置"


class ButtonAttr(object):
    UUID = "按钮UUID"
    NAME = "按钮名称"
    EVENT = "按钮事件"
    COMMON = "常规"
    SEARCH_ITEM = "指定搜索项搜索"
    GENERAL_SEARCH = "普通搜索"
    FREE_SEARCH = "自由检索"
    DATASOURCE = "数据源"
    BADGE = "徽标数"


class ButtonStyle(object):
    NORMAL_DISPLAY = "常态显示"
    DYNAMIC_DEFINITION = "动态定义"


class ButtonIcon(object):
    BUTTON_ICON = "图标显示"


class CardlistAttr(object):

    UUID = "卡片列表UUID"
    NAME = "卡片列表名称"
    COLUMN = "列"
    FUNCTION_BAR = "按钮区"
    DATASOURCE = "数据源"
    EVENT = "卡片列表事件"
    SELECT_PAGE = "卡片列表选择页面"
    PAGINATION = "分页设置"
    DRAG = "拖动"


class CardlistStyle(object):
    DYNAMIC_DEFINITION = "动态定义"


class DatagridAttr(object):

    UUID = "电子表格UUID"
    NAME = "电子表格名称"
    COLUMN = "列"
    FUNCTION_BAR = "按钮区"  # 仅更改显示名称
    DATASOURCE = "数据源"
    SEARCH_BAR = "筛选区"
    SEARCH_ITEM = "筛选项"

class SplitPageAttr(object):
    
    UUID = "分割UUID"
    NAME = "分割名称"

class GridAttr(object):

    UUID = "栅格UUID"
    NAME = "栅格名称"


class GridRowAttr(object):

    UUID = "栅格行UUID"
    NAME = "栅格行名称"


class GridColAttr(object):

    UUID = "栅格列UUID"
    NAME = "栅格列名称"


class PageAttr(object):

    UUID = "页面UUID"
    NAME = "页面名称"
    TITLE = "页面标题"
    INPUT_CONTROL = "表单输入组件"
    SCAN_CONTROL = "页面扫码组件"
    REFRESH = "页面刷新设置"
    CUSTOM_PATH = "自定义路径"
    EVENT = "页面事件"


class BaseInputAttr(object):

    UUID = "输入控件UUID"
    NAME = "输入控件名称"
    TYPE = "输入控件所选字段类型"
    DATASOURCE = "数据源"
    EVENT = "输入控件事件"


class PrintButtonAttr(object):

    UUID = "打印按钮UUID"
    NAME = "打印控件名称"
    DATASOUCE = "数据绑定"


class ChartAttr(object):

    UUID = "可视化组件UUID"
    NAME = "可视化组件名称"
    AXIS = "轴"
    LEGEND = "图例"
    TOOLTIPS = "工具提示"
    SHARED_AXIS = "共享轴"
    COLUMN_SERIES = "列序列"
    LINE_VALUES = "行值"
    COLUMN_VALUES = "列值"
    DATASOURCE = "数据源"
    DETAILS = "详细信息"
    VALUES = "值"


class NavigationAttr(object):

    ICON = "导航图标"
    ITEM_NAME = "导航名称"
    BADGE = "徽标数"


class DropdownMenuAttr(object):
    UUID = "下拉菜单UUID"
    NAME = "下拉菜单名称"
    DISPLAY = "显示"

class MenuAttr(object):
    UUID = "菜单UUID"
    NAME = "菜单名称"
    EVENT = "事件"
    BASIC_CONF = "基础配置"


class UniverAttr(object):
    UUID = "EXCEL表格 UUID"
    NAME = "EXCEL表格名称"
    DATAVARIABLES = "数据变量"


class Univer(object):
    ATTR = UniverAttr


class BaseInput(object):

    ATTR = BaseInputAttr


class Input(object):

    ATTR = InputAttr


class Textarea(object):

    ATTR = TextareaAttr


class Radio(object):

    ATTR = RadioAttr


class Checkbox(object):

    ATTR = CheckboxAttr


class Select(object):

    ATTR = SelectAttr


class Transfer(object):
    
    ATTR = TransferAttr
    
    
class Datetime(object):

    ATTR = DatetimeAttr


class RSelect(object):

    ATTR = RSelectAttr


class RSelectPopup(object):

    ATTR = RSelectPopupAttr


class RTile(object):
    
    ATTR = RTile


class RTree(object):

    ATTR = RTreeAttr


class RCascade(object):

    ATTR = RCascadeAttr


class RSelectTable(object):

    ATTR = RSelectTableAttr


class RCreateTable(object):

    ATTR = RCreateTableAttr


class Slider(object):

    ATTR = SliderAttr


class Switch(object):

    ATTR = SwitchAttr


class UploadFile(object):

    ATTR = UploadFileAttr


class UploadImage(object):

    ATTR = UploadImageAttr


class Chart(object):

    ATTR = ChartAttr


class ColorAttr(object):

    UUID = "色板控件UUID"
    NAME = "色板控件名称"
    TYPE = "色板控件所选字段类型"


class CustomComponentAttr:

    UUID = "自定义组件UUID"
    NAME = "自定义组件名称"
    TYPE = "自定义组件所选字段类型"


class TabsAttr(object):

    UUID = "页签组UUID"
    NAME = "页签组名称"
    BADGE = "徽标数"


class TabAttr(object):
    
    UUID = "单个页签UUID"
    NAME = "单个页签名称"
    BADGE = "徽标数"


class CollapseAttr(object):

    UUID = "折叠面板UUID"
    NAME = "折叠面板名称"


class PanelAttr(object):
    UUID = "单个面板UUID"
    NAME = "单个面板名称"
    BADGE = "徽标数"

class SplitPageAttr(object):

    UUID = "分割组件UUID"
    NAME = "分割组件名称"


class SplitAttr(object):
    UUID = "单个分割组件UUID"
    NAME = "单个分割组件名称"
    BADGE = "徽标数"


class ContainerAttr(object):

    UUID = "容器UUID"
    NAME = "容器名称"
    EVENT = "容器事件"


class ContainerStyle(object):
    DYNAMIC_DEFINITION = "动态定义"


class CalendarAttr(object):

    UUID = "日期容器UUID"
    NAME = "日期容器名称"
    DATE_FIELD = "日期字段"


class Color(object):

    ATTR = ColorAttr


class CustomComponent:

    ATTR = CustomComponentAttr


class Calendar(object):

    ATTR = CalendarAttr


class Form(object):

    ATTR = FormAttr


class Tree(object):
    ATTR = TreeAttr


class Treelist(object):
    ATTR = TreelistAttr


class PrintButton(object):
    ATTR = PrintButtonAttr


class Datalist(object):

    ATTR = DatalistAttr


class Button(object):
    ATTR = ButtonAttr
    STYLE = ButtonStyle
    ICON = ButtonIcon


class Cardlist(object):

    ATTR = CardlistAttr
    STYLE = CardlistStyle


class Datagrid(object):

    ATTR = DatagridAttr


class Container(object):

    ATTR = ContainerAttr
    STYLE = ContainerStyle


class Tabs(object):

    ATTR = TabsAttr


class Tab(object):

    ATTR = TabAttr


class Collapse(object):

    ATTR = CollapseAttr


class Panel(object):
    ATTR = PanelAttr


class SplitPage(object):

    ATTR = SplitPageAttr


class Split(object):
    ATTR = SplitAttr


class Grid(object):

    ATTR = GridAttr


class GridRow(object):

    ATTR = GridRowAttr


class GridCol(object):

    ATTR = GridColAttr


class Page(object):

    ATTR = PageAttr


class Navigation(object):

    ATTR = NavigationAttr


class DropdownMenu(object):

    ATTR = DropdownMenuAttr


class Menu(object):
    ATTR = MenuAttr


class PrintImageAttr(object):

    UUID = "打印图片UUID"
    NAME = "打印图片名称"
    DATASOURCE = "数据源"


class PrintTextAttr(object):

    UUID = "打印文本UUID"
    NAME = "打印文本名称"
    DATASOURCE = "数据源"


class PrintDatalistAttr(object):

    UUID = "打印数据列表UUID"
    NAME = "打印数据列表名称"
    COLUMN = "打印数据列表列"
    DATASOURCE = "数据源"


class PrintCardlistAttr(object):

    UUID = "打印卡片列表UUID"
    NAME = "打印卡片列表名称"
    DATASOURCE = "数据源"


class PrintFormAttr(object):

    UUID = "打印表单UUID"
    NAME = "打印表单名称"
    DATASOURCE = "数据源"


class PrintTableAttr(object):

    UUID = "打印表格UUID"
    NAME = "打印表格名称"
    DATASOURCE = "数据源"


class PrintAttr(object):

    UUID = "打印模板UUID"
    NAME = "打印模板名称"
    SIZE = "打印纸张尺寸"
    PAGE_WIDTH = "打印模板页面宽度"
    DATASOURCE = "数据源"
    HIGHT = "图片高度"
    WIDTH = "格子宽度"


class LabelPrintAttr(object):
    
    UUID = "标签打印UUID"
    NAME = "标签打印名称"
    DATASOURCE = "数据源"
    SIZE = "尺寸"
    UNIVERSAL = "通用"
    

class LabelPrintRectBoxAttr(object):
    UUID = "标签打印矩形框UUID"
    NAME = "标签打印矩形框名称"


class LabelPrintStraightLineAttr(object):
    UUID = "标签打印直线UUID"
    NAME = "标签打印直线名称"


class LabelPrintTextAttr(object):
    UUID = "标签打印文字UUID"
    NAME = "标签打印文字名称"
    DATASOURCE = "数据源"


class LabelPrintMultilineTextAttr(object):
    UUID = "标签打印多行文字UUID"
    NAME = "标签打印多行文字名称"
    DATASOURCE = "数据源"


class LabelPrintBarCodeAttr(object):
    UUID = "标签打印条形码UUID"
    NAME = "标签打印条形码名称"
    DATASOURCE = "数据源"


class LabelPrintQRCodeAttr(object):
    UUID = "标签打印二维码UUID"
    NAME = "标签打印二维码名称"
    DATASOURCE = "数据源"


class LabelPrintImageAttr(object):
    UUID = "标签打印图片UUID"
    NAME = "标签打印图片名称"
    DATASOURCE = "数据源"


class TextAttr(object):
    UUID = "文本UUID"
    NAME = "文本名称"
    DATASOURCE = "数据源"
    TITLE = "标题"
    BADGE = "徽标数"


class TextStyle(object):
    DYNAMIC_DEFINITION = "动态定义"


class ScanCountAttr(object):
    UUID = "扫码计数UUID"
    NAME = "扫码计数名称"
    DATASOURCE = "数据源"


class ScanAttr(object):
    UUID = "扫码UUID"
    NAME = "扫码名称"
    DATASOURCE = "数据源"
    EVENT = "扫码事件"

class ReactAttr:
    UUID = "云函数组件UUID"
    NAME = "云函数组件名称"
    DATASOURCE = "数据源"

class TimeLineAttr(object):
    UUID = "时间线UUID"
    NAME = "时间线名称"
    DATASOURCE = "数据源"
    DATASOURCE_FIELD = {
    "time_name": "时间段名称",
    "time_start": "时间段开始时间",
    "time_end": "时间段结束时间",
    "target_name": "主指标片段名称",
    "target_color": "主指标片段颜色",
    "target_start": "主指标片段开始时间",
    "target_end": "主指标片段结束时间",
    "target_branch": "主指标片段是否分行显示",
    "sub_index_name": "副指标片段名称",
    "sub_index_name": "副指标片段颜色",
    "sub_index_start": "副指标片段开始时间",
    "sub_index_end": "副指标片段结束时间"
    }


class PresentImageAttr(object):
    UUID = "图片UUID"
    NAME = "图片名称"
    DATASOURCE = "数据源"


class PresentTagAttr(object):
    UUID = "标签UUID"
    NAME = "标签名称"
    DATASOURCE = "数据源"


class PresentTagStyle(object):
    DYNAMIC_DEFINITION = "动态定义"


class PresentCircleProgressAttr(object):
    UUID = "环形进度条UUID"
    NAME = "环形进度条名称"
    DATASOURCE = "数据源"
    DATASOURCE_FIELD = {
        # "compltevalueEdit": "当前应完成值",  # 改为选填了
        "currentvalueEdit": "当前值",
        "valueEdit": "目标值"
    }


class PresentProgressAttr(object):
    UUID = "条形进度条UUID"
    NAME = "条形进度条名称"
    DATASOURCE = "数据源"
    DATASOURCE_FIELD = {
        # "compltevalueEdit": "当前应完成值",  # 改为选填了
        "currentvalueEdit": "当前值",
        "valueEdit": "目标值"
    }


class PresentFileAttr(object):
    UUID = "文件UUID"
    NAME = "文件名称"
    FIELD_INFO = "数据源"


class ModuleThemeAttr(object):

    UUID = "模块主题UUID"
    NAME = "模块主题名称"


class ThemeAttr(object):

    THEME_SELECTED = "主题选择"
    APPLAYOUT_SELECTED = "应用布局选择"


class ExtPackAttr(object):

    EXPANSION_PACKAGE = "扩展包管理"

class PrintImage(object):

    ATTR = PrintImageAttr


class LabelPrint(object):
    ATTR = LabelPrintAttr


class LabelPrintRectBox(object):
    ATTR = LabelPrintRectBoxAttr


class LabelPrintStraightLine(object):
    ATTR = LabelPrintStraightLineAttr


class LabelPrintText(object):
    ATTR = LabelPrintTextAttr


class LabelPrintMultiLine(object):
    ATTR = LabelPrintMultilineTextAttr


class LabelPrintBarCode(object):
    ATTR = LabelPrintBarCodeAttr


class LabelPrintQRCode(object):
    ATTR = LabelPrintQRCodeAttr


class LabelPrintImage(object):
    ATTR = LabelPrintImageAttr


class PrintText(object):

    ATTR = PrintTextAttr


class PrintDatalist(object):

    ATTR = PrintDatalistAttr


class PrintCardlist(object):

    ATTR = PrintCardlistAttr


class PrintForm(object):

    ATTR = PrintFormAttr


class PrintTable(object):

    ATTR = PrintTableAttr


class Print(object):

    ATTR = PrintAttr


class ModuleTheme(object):

    ATTR = ModuleThemeAttr


class Theme(object):

    ATTR = ThemeAttr


class ExtPack(object):

    ATTR = ExtPackAttr


class ImageSourceType(object):

    IMAGE = 0  # 图片表
    FIELD = 1  # 字段
    CODE  = 2  # 条码
    LINK  = 3  # 外部链接
    

class FileSourceType(object):
    
    FIELD = 1  # 字段
    LINK  = 3  # 外部链接


class DataSourceType(object):

    MODEL_WITH_PRE = 0
    MODEL_WITH_EDITOR = 1
    ASSOCIATION = 2  # 关联数据库存储
    ASSOCIATION_MEMORY = 4  # 关联内存存储
    MODEL_WITH_FUNC = 5

    FORM_WITH_CONTEXT = 0
    FORM_WITH_EDITOR = 1
    FORM_ASSOCIATION = 2
    FORM_LINKAGE = 3
    FORM_ASSOCIATION_MEMORY = 4
    FORM_WITH_FUNC = 5

    RTREE_HIERARCHY = 0
    RTREE_SELF_REFERENTIAL = 1

    TREE_WITH_PRE = 0
    TREE_WITH_FUNC = 5
    
    SELECT_FIELD = 0
    SELECT_FUNC = 1

    # 数据源图片类型
    class Image:
        IMAGE_TABLE = 0
        IMAGE_FIELD = 1
        BARCODE = 2

class PageOpenType(object):
    POPUP = 0
    JUMP = 1
    DRAWER = 2

class PurposeType(object):

    ADD = 0
    DELETE = 1
    VIEW = 2

class AddMethodType(object):
    HAND_INPUT = 0
    SELECT = 1
    SCAN = 2

class LinkageType(object):

    DATALIST_WITH_FORM = 0  # 数据列表 和 表单联动
    SEARCH_BAR_WITH_BI = 1
    ITEM_FILTER_WITH_BI     = 2  # 过滤器 和 BI组件
    DATALIST_WITH_TIMELINE = 3
    FORM_WITH_TIMELINE = 4
    CARDLIST_WITH_TIMELINE = 5


class EventResponseMethod(object):

    NONE = 0
    ACTION = 1
    FUNC = 2


class EventAction(object):

    OPEN = 0
    CLOSE = 1
    LINK = 2
    NEW = 3
    SAVE = 4
    CANCEL = 5
    DELETE = 6
    SUBMIT = 7
    MESSAGE_BOX = 8
    START_WF = 9
    RESET = 10
    FORM_PRINT = 11  # 打印表单
    EXPORT = 12
    IMPORT = 13
    LABEL_PRINT = 14


class NewButtonEditType(object):

    NEW_PAGE = 0
    ADD_HEAD = 1
    ADD_TAIL = 2


class ButtonClickEventType(object):

    OPEN_PAGE  = 0
    CLOSE_PAGE = 1
    CALL_FUNC  = 2
    SEND_EVENT = 3


class SearchType(object):

    INVISIBLE = 0
    READONLY  = 1
    EDITABLE  = 2


class OperatorType(BaseOperatorType):

    SELECT_TIME = -1


class DirectionType(object):

    ESC  = 0
    DESC = 1


class AggregationFunc(object):

    SUM   = 0
    AVG   = 1
    MIN   = 2
    MAX   = 3
    COUNT = 4
    FIRST = 5
    FUNC  = 6

    ALL = [SUM, AVG, MIN, MAX, COUNT, FIRST, FUNC]


class AggreFunc:

    CONCAT = 0
    COUNT  = 1
    SUM    = 2
    AVG    = 3
    MAX    = 4
    MIN    = 5
    FUNC   = 6
    FIRST  = 7
    SUPPORT_TYPE = [CONCAT, COUNT, SUM, AVG, MAX, MIN]

class BaseComponentType(object):

    CONTAINER  = 0
    INPUT      = 1
    PRESENT    = 2
    BUTTON     = 3
    EXTEND     = 4
    VISUAL     = 5
    NAVIGATION = 6


class ComponentType(object):

    ROOT = -10
    PAGE = -1
    GRID = 0
    GRID_ROW = 1
    GRID_COL = 2
    SPLIT_PAGE = 117  # 分割组件
    SPLIT = 118  # 分割组件的 单个容器
    DATALIST = 3
    FORM = 4
    INPUT = 5  # 输入框
    TEXTAREA = 6  # 多行输入框
    RADIO = 7  # 单选框
    CHECKBOX = 8  # 复选框
    SELECT = 9  # 下拉框
    R_SELECT = 10  # 关联下拉框
    R_SELECT_POPUP = 11  # 关联弹窗
    R_SELECT_TABLE = 12  # 关联选择子表
    R_CREATE_TABLE = 13  # 关联填写子表
    DATETIME = 14  # 日期时间选择框
    SWITCH = 15  # 开关
    SLIDER = 16  # 滑动输入
    UPLOAD_FILE = 17
    UPLOAD_IMAGE = 18
    R_CASCADE = 110  # 关联级联下拉框
    R_TREE = 111  # 关联树形下拉框
    COLOR = 112  # 色板
    DATETIME_CYCLE = 113  # 日期循环组件
    FORM_SELECTOR = 114  # 子表单选择器
    R_TILE = 115  # 关联平铺
    TRANSFER = 116  # 穿梭框

    # 数据展示组件
    TEXT = 20
    IMAGE = 21
    TIMEAXIS = 22
    TIMELINE = 23
    TAG = 24
    LINE_BAR = 25
    RING_BAR = 26
    FILE = 27

    STACKED_BAR = 30  # 堆积条形
    STACKED_COLUMN = 31  # 堆积柱形
    CLUSTERED_BAR = 32  # 簇状条形
    CLUSTERED_COLUMN = 33  # 簇状柱形
    PER_STACKED_BAR = 34  # 百分比堆积条形
    PER_STACKED_COLUMN = 35  # 百分比堆积柱形
    LINE = 36  # 折线
    LINE_STACKED_COLUMN = 37  # 折线+堆积柱形
    LINE_CLUSTERED_COLUMN = 38  # 折线+簇状柱形
    PIE = 39  # 饼图
    DONUT = 40  # 环形
    TREEMAP = 41  # 树形
    GAUGE = 42  # 仪表
    CARD = 43  # 卡片
    TABLE = 44  # 表
    SUMMARY_TABLE = 45  # 汇总表
    ITEM_FILTER = 46  # 项目过滤器
    RANGE_FILTER = 47
    SEARCH_BAR = 48  # 搜索栏
    CUSTOM_REPORT = 49  # 表格报表

    NORMAL_BUTTON = 50
    SEARCH_BUTTON = 51
    NEW_BUTTON = 52
    DELETE_BUTTON = 53
    ADD_BUTTON = 54
    REMOVE_BUTTON = 55
    EDIT_BUTTON = 56
    IMPORT_BUTTON = 57
    EXPORT_BUTTON = 58
    SHOW_BUTTON = 59
    CANCEL_BUTTON = 60
    SUBMIT_BUTTON = 61
    GROUPBY_BUTTON = 62
    EXTERNAL_BUTTON = 63  # 外部填写

    CARDLIST = 70
    TABS = 71
    TAB = 711
    CONTAINER = 72
    CALENDAR = 73
    COLLAPSE = 74
    PANEL = 75  # 折叠面板的 单个面板
    TREE = 76
    TREELIST = 77
    DATAGRID = 79
    COLUMN = 304

    DROPDOWN_MENU = 81  # 下拉菜单
    MENU = 82  # 下拉菜单的菜单
    EXECL_TABLE = 88  # excel表格组件

    # STACKED_BAR_CHART = 200
    GANTT = 100  # 甘特图
    PRINT = 101  # 打印
    SCAN = 102   # 扫码
    PLANEPOSITION = 103  # 平面定位
    SCAN_COUNT = 104  # 扫码计数校验
    REACT = 105     # 云函数组件

    CUSTOM_INPUT = 200  # 自定义表单输入组件
    CUSTOM_PRESENT = 201  # 自定义数据展示组件
    CUSTOM_EXTEND = 202  # 自定义扩展组件

    SPECIAL_BUTTON = [
        SEARCH_BUTTON, NEW_BUTTON, DELETE_BUTTON, ADD_BUTTON, REMOVE_BUTTON,
        EDIT_BUTTON, IMPORT_BUTTON, EXPORT_BUTTON, SHOW_BUTTON, CANCEL_BUTTON,
        SUBMIT_BUTTON, GROUPBY_BUTTON, EXTERNAL_BUTTON
    ]
    ALL_BUTTON = [NORMAL_BUTTON]
    ALL_BUTTON.extend(SPECIAL_BUTTON)
    INPUT_CONTROL_LIST = [
        INPUT, TEXTAREA, RADIO, CHECKBOX, SELECT, DATETIME, SWITCH, SLIDER,
        UPLOAD_FILE, UPLOAD_IMAGE, COLOR, DATETIME_CYCLE, FORM_SELECTOR,
        CUSTOM_INPUT
    ]
    DATA_VISUALIZE_COMPONENT = [
        STACKED_BAR, STACKED_COLUMN, CLUSTERED_BAR, CLUSTERED_COLUMN,
        PER_STACKED_BAR, PER_STACKED_COLUMN, LINE, LINE_STACKED_COLUMN,
        LINE_CLUSTERED_COLUMN, PIE, DONUT, TREEMAP, GAUGE, CARD, TABLE,
        SUMMARY_TABLE, ITEM_FILTER, RANGE_FILTER, SEARCH_BAR
    ]
    RELATION_CONTROL_LIST = [
        R_SELECT, R_SELECT_POPUP, R_TILE, R_SELECT_TABLE, R_CREATE_TABLE,
        R_CASCADE, R_TREE, TRANSFER
    ]
    R_SELECT_CONTROL_LIST = [
        R_SELECT, R_SELECT_POPUP, R_TILE, R_CASCADE, R_TREE, TRANSFER
    ]
    R_SELECT_TABLE_CONTROL_LIST = [R_SELECT_TABLE, R_CREATE_TABLE]
    CONTAINER_LIST = [DATALIST, CARDLIST, DATAGRID, FORM, TREELIST]
    ALL_LAYOUT_CONTAINER_LIST = [GRID, GRID_ROW, GRID_COL, TABS, TAB, CONTAINER, COLLAPSE, SPLIT_PAGE, PANEL]
    CHART_LIST = range(30, 49)
    ALL_INPUT_CONTROL_LIST = copy.deepcopy(INPUT_CONTROL_LIST)
    ALL_INPUT_CONTROL_LIST.extend(RELATION_CONTROL_LIST)  # 输入组件列表
    ALL_PRESENT_CONTROL_LIST = [
        TEXT, IMAGE, TAG, LINE_BAR, RING_BAR, FILE, SCAN_COUNT, CUSTOM_PRESENT
    ]
    COLUMN_CONTROL_LIST = INPUT_CONTROL_LIST + ALL_PRESENT_CONTROL_LIST
    BASE_DATA_CONTAINER_LIST = CONTAINER_LIST + [TREE, CALENDAR, R_SELECT, TRANSFER, R_SELECT_POPUP, R_TILE]
    FIELD_TO_COMPONENT = {
        FieldType.ENUM: [SELECT, RADIO],
        FieldType.STRING: [INPUT, TEXTAREA],
        FieldType.INTEGER: [INPUT, SLIDER],
        FieldType.DECIMAL: [INPUT, SLIDER],
        FieldType.DATETIME: [DATETIME],
        FieldType.BOOLEAN: [RADIO, SELECT, CHECKBOX, SWITCH],
        FieldType.FILE: [UPLOAD_FILE],
        FieldType.IMAGE: [UPLOAD_IMAGE],
        FieldType.RELATION: [
            R_SELECT, R_SELECT_POPUP, R_TILE, R_SELECT_TABLE, R_CREATE_TABLE
        ],
        FieldType.DATETIME_CYCLE: [DATETIME_CYCLE],
        FieldType.JSON: []
    }
    FIELD_TO_DATALSIT_COMPONENT = {
        FieldType.ENUM: [SELECT, RADIO],
        FieldType.STRING: [INPUT, TEXTAREA],
        FieldType.INTEGER: [INPUT, SLIDER],
        FieldType.DECIMAL: [INPUT, SLIDER],
        FieldType.DATETIME: [DATETIME],
        FieldType.BOOLEAN: [CHECKBOX, SELECT, CHECKBOX, SWITCH],
        FieldType.FILE: [UPLOAD_FILE],
        FieldType.IMAGE: [UPLOAD_IMAGE],
        FieldType.RELATION: [R_SELECT, R_SELECT_POPUP, R_TILE, R_SELECT_TABLE, R_CREATE_TABLE],
        FieldType.DATETIME_CYCLE: [DATETIME_CYCLE],
        FieldType.JSON: []
    }
    FIELD_TO_SHOW_COMPONENT = {
        FieldType.ENUM: [TEXT, TAG],
        FieldType.STRING: [TEXT, TAG],
        FieldType.INTEGER: [TEXT, TAG, SCAN_COUNT, RING_BAR, LINE_BAR],
        FieldType.DECIMAL: [TEXT, TAG, RING_BAR, LINE_BAR],
        FieldType.DATETIME: [TEXT, TAG],
        FieldType.BOOLEAN: [TEXT, TAG],
        FieldType.FILE: [FILE],
        FieldType.IMAGE: [IMAGE],
        FieldType.RELATION: [TEXT, TAG],
        FieldType.DATETIME_CYCLE: [TEXT],
        FieldType.JSON: [TEXT, TAG]
    }
    AGG_FUNC_TO_FIELD = {
        AggreFunc.CONCAT: [
            FieldType.STRING, FieldType.INTEGER, FieldType.DECIMAL, FieldType.ENUM,
            FieldType.BOOLEAN, FieldType.DATETIME
        ],
        AggreFunc.SUM: [FieldType.INTEGER, FieldType.DECIMAL],
        AggreFunc.AVG: [FieldType.INTEGER, FieldType.DECIMAL]
    }
    COMPONENT_TO_FIELD = {
        INPUT: [FieldType.STRING, FieldType.INTEGER, FieldType.DECIMAL],
        TEXTAREA: [FieldType.STRING],
        RADIO: [FieldType.ENUM, FieldType.BOOLEAN],
        CHECKBOX: [FieldType.BOOLEAN],
        SELECT: [FieldType.ENUM, FieldType.BOOLEAN],
        R_SELECT: [FieldType.RELATION],
        R_SELECT_POPUP: [FieldType.RELATION],
        R_TILE: [FieldType.RELATION],
        R_SELECT_TABLE: [FieldType.RELATION],
        R_CREATE_TABLE: [FieldType.RELATION],
        DATETIME: [FieldType.DATETIME],
        SWITCH: [FieldType.BOOLEAN],
        SLIDER: [FieldType.INTEGER, FieldType.DECIMAL],
        UPLOAD_FILE: [FieldType.FILE],
        UPLOAD_IMAGE: [FieldType.IMAGE],
        COLOR: [FieldType.STRING],
        DATETIME_CYCLE: [FieldType.DATETIME_CYCLE],
        CUSTOM_INPUT: FieldType.ALL
    }

    # 下面这些是 builder 使用的
    SUB_TABLE = "sub_table"
    CARDLIST_SUB_TABLE = "cardlist_sub_table"
    DATAGRID_SUB_TABLE = "datagrid_sub_table"
    TREELIST_SUB_TABLE = "treelist_sub_table"
    ADD_TABLE = "add_table"
    DELETE_TABLE = "delete_table"
    VIEW_TABLE = "view_table"
    R_FORM = "r_form"
    DATALIST_TABLE = "datalist_table"
    CARDLIST_TABLE = "cardlist_table"
    DATAGRID_TABLE = "datagrid_table"
    TREELIST_TABLE = "treelist_table"
    BUILDER_REPLACE_TYPE = {
        FORM: R_FORM,
        DATALIST: DATALIST_TABLE,
        CARDLIST: CARDLIST_TABLE,
        DATAGRID: DATAGRID_TABLE,
        TREELIST: TREELIST_TABLE
    }


class SelectType(object):
    FIELD = 0
    CLOUD_FUNCTION = 1
    
    SELECT = {
        FIELD: [FieldType.ENUM, FieldType.BOOLEAN],
        CLOUD_FUNCTION: [FieldType.INTEGER, FieldType.DECIMAL, FieldType.BOOLEAN, 
                         FieldType.STRING,FieldType.DATETIME, FieldType.ENUM]
    }
    
    ALL = {
        ComponentType.SELECT: SELECT
    }


class PrintType(object):

    FORM = 0  # 表单
    CARDLIST = 1  # 卡片列表
    DATALIST = 2  # 数据列表

    TABLE = 10  # 表格
    BREAK = 11  # 分页符

    VISUAL = 20  # 可视化组件
    IMAGE = 21  # 图片
    TEXT = 22  # 文本

class LabelPrintType(object):
    
    RECT_BOX = 1 # 矩形框
    STRAIGHT_LINE = 2 # 直线
    TEXT = 3 # 文字
    MULTILINE_TEXT = 4 # 多行文字
    BAR_CODE = 5 # 条形码
    QR_CODE = 6 # 二维码
    IMAGE = 7 # 图片
    
    IMAGE_TYPE = [BAR_CODE, QR_CODE, IMAGE]
    TEXT_TYPE = [TEXT, MULTILINE_TEXT]


class LabelPrintImageType(object):
    PICTURE_TABEL = 0 # 图片表
    IMAGE_FIELD = 1 # 图片字段
    BAR_CODE = 2 # 条码


class SerialRule:

    PREFIX_SERIAL = 0
    PREFIX_DATE_SERIAL = 1

    BUILT_IN = {
        PREFIX_SERIAL: "前缀+流水号",
        PREFIX_DATE_SERIAL: "前缀+日期+流水号"
    }
    ALL = [PREFIX_SERIAL, PREFIX_DATE_SERIAL]


class SerialGenType(object):

    PRE = 0
    POST = 1

    ALL = [PRE, POST]


class PageToolName(object):

    GRID = "栅格"
    GRID_ROW = "栅格行"
    GRID_COL = "栅格列"
    DATALIST = "数据列表"
    DATAGRID = "电子表格"
    TREE = "树形组件"
    TREELIST = "树形列表"
    FORM = "表单"
    CARDLIST = "卡片列表"
    INPUT = "输入框"
    TEXTAREA = "多行输入框"
    RADIO = "单选框组"
    CHECKBOX = "复选框"
    SELECT = "下拉框"
    R_SELECT = "关联下拉框"
    R_SELECT_POPUP = "关联弹窗"
    R_TILE = "关联平铺"
    R_SELECT_TABLE = "关联选择子表"
    R_CREATE_TABLE = "关联填写子表"
    DATETIME = "日期时间选择框"
    DATETIME_CYCLE = "日期循环组件"
    SWITCH = "开关"
    SLIDER = "滑动输入"
    UPLOAD_FILE = "上传文件"
    UPLOAD_IMAGE = "上传图片"
    R_CASCADE = "关联级联下拉框"  # 关联级联下拉框
    R_TREE = "关联树形下拉框"  # 关联树形下拉框
    COLOR = "色板"
    TEXT = "文本"
    IMAGE = "图片"
    TIMEAXIS = "时间轴"
    TIMELINE = "时间线"
    TAG = "标签"
    LINE_BAR = "条形进度条"
    RING_BAR = "环形进度条"
    FILE = "文件"
    PRINT = "打印"
    CUSTOM_INPUT = "表单输入组件"
    CUSTOM_PRESENT = "数据展示组件"
    CUSTOM_EXTEND = "扩展组件"

    NORMAL_BUTTON = "普通按钮"
    SUBMIT_BUTTON = "保存按钮"
    CANCEL_BUTTON = "取消按钮"
    SEARCH_BUTTON = "搜索按钮"
    NEW_BUTTON = "新建按钮"
    DELETE_BUTTON = "删除按钮"
    ADD_BUTTON = "添加按钮"
    REMOVE_BUTTON = "移除按钮"
    EDIT_BUTTON = "编辑按钮"
    IMPORT_BUTTON = "导入按钮"
    EXPORT_BUTTON = "导出按钮"
    SHOW_BUTTON = "显示列按钮"
    GROUPBY_BUTTON = "分组按钮"
    EXTERNAL_BUTTON = "外部填写按钮"

    DROPDOWN_MENU = "下拉菜单"
    MENU = "菜单"

    EXECL_TABLE = "excel表格"

    CONTAINER = "容器"
    SCAN_COUNT = "扫码计数校验"
    FORM_SELECTOR = "子表单选择器"
    REACT = "云函数组件"

    STACKED_BAR_CHART = "堆积条形图"
    TRANSFER = "穿梭框"
    SPLIT_PAGE = "分割组件"

class ConstantCode(object):
    MONITOR_TYPE_NEED_FOCUS = 0
    MONITOR_TYPE_NOT_NEED_FOCUS = 1


class ModelSYSTool(object):

    MODEL = {
        "tool_name": "数据模型", "icon": "", "tool_class": ToolClass.MODEL, "sort": 1,
        "content"  : {"drag_type": DragType.MODEL}
    }

    ALL = [MODEL]


class SMGeneralSYSTool(object):

    AUTO = {
        "tool_name": "自动状态", "icon": "iconauto", "tool_class": ToolClass.STATE, "sort": 1,
        "content"  : {"drag_type": DragType.STATE, "state_type": StateType.AUTO}
    }
    MANUAL = {
        "tool_name": "人工状态", "icon": "iconartificial", "tool_class": ToolClass.STATE, "sort": 2,
        "content"  : {"drag_type": DragType.STATE, "state_type": StateType.MANUAL}
    }
    END = {
        "tool_name": "结束状态", "icon": "iconend", "tool_class": ToolClass.STATE, "sort": 3,
        "content"  : {"drag_type": DragType.STATE, "state_type": StateType.END}
    }
    CHILD = {
        "tool_name": "子状态", "icon": "iconchild", "tool_class": ToolClass.STATE, "sort": 4,
        "content"  : {"drag_type": DragType.STATE, "state_type": StateType.CHILD}
    }
    TRIGGER = {
        "tool_name": "触发器", "icon": "icontrigger", "tool_class": ToolClass.TRIGGER, "sort": 5,
        "content"  : {"drag_type": DragType.TRIGGER}
    }

    ALL = [AUTO, MANUAL, END, CHILD, TRIGGER]


class SMControlFlowSYSTool(object):

    FOR = {
        "tool_name": "for", "icon": "iconexprfor", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FOR}
    }
    IF = {
        "tool_name": "if", "icon": "iconexprif", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.IF}
    }
    EXCEPTION = {
        "tool_name": "异常处理", "icon": "iconexception", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.EXCEPTION}
    }
    BREAK = {
        "tool_name": "break", "icon": "iconbreak", "tool_class": ToolClass.ACTION, "sort": 4,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.BREAK}
    }
    CONTINUE = {
        "tool_name": "continue", "icon": "iconcontinue", "tool_class": ToolClass.ACTION, "sort": 5,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.CONTINUE}
    }

    ALL = [FOR, IF, EXCEPTION, BREAK, CONTINUE]


class SMObjectActionSYSTool(object):

    MODIFY = {
        "tool_name": "修改对象", "icon": "iconaction-edit", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    DELETE = {
        "tool_name": "删除对象", "icon": "icondelete", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SELECT = {
        "tool_name": "查询对象", "icon": "iconobtain", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SAVE = {
        "tool_name": "保存对象", "icon": "iconsave", "tool_class": ToolClass.ACTION, "sort": 4,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [MODIFY, DELETE, SELECT, SAVE]


class SMListActionSYSTool(object):

    MODIFY = {
        "tool_name": "修改列表", "icon": "iconedit-list", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    CALC = {
        "tool_name": "运算列表", "icon": "iconcalculate-list", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    AGG = {
        "tool_name": "聚合列表", "icon": "iconaggregation-list", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [MODIFY, CALC, AGG]


class SMVariableActionSYSTool(object):

    MODIFY = {
        "tool_name": "修改变量", "icon": "iconedit-variable", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [MODIFY]


class SMFrontActionSYSTool(object):

    CLOSE_PAGE = {
        "tool_name": "关闭页面", "icon": "iconclose-page", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    OPEN_PAGE = {
        "tool_name": "打开页面", "icon": "iconopen-page", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SHOW_MSG = {
        "tool_name": "显示消息", "icon": "iconmessage", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [CLOSE_PAGE, OPEN_PAGE, SHOW_MSG]


class SMInternetActionSYSTool(object):

    HTTP = {
        "tool_name": "HTTP请求", "icon": "iconhttp", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    WECHAT = {
        "tool_name": "微信", "icon": "iconwechat", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    DINGTALK = {
        "tool_name": "钉钉", "icon": "iconding", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    EMAIL = {
        "tool_name": "邮件", "icon": "iconemail", "tool_class": ToolClass.ACTION, "sort": 4,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SMS = {
        "tool_name": "短信", "icon": "iconshortmessage", "tool_class": ToolClass.ACTION, "sort": 5,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [HTTP, WECHAT, DINGTALK, EMAIL, SMS]


class SMSystemActionSYSTool(object):

    FUNC = {
        "tool_name": "云函数", "icon": "iconcloudfunc-action", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SEND_EVENT = {
        "tool_name": "发送事件", "icon": "iconsend", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    CREATE_SM = {
        "tool_name": "创建状态机", "icon": "iconcreate-statemachine", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    DELETE_SM = {
        "tool_name": "删除状态机", "icon": "icondelete-statemachine", "tool_class": ToolClass.ACTION, "sort": 4,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    COMMIT = {
        "tool_name": "提交事务", "icon": "iconsubmit-transaction", "tool_class": ToolClass.ACTION, "sort": 5,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    ROOLBACK = {
        "tool_name": "回滚事务", "icon": "iconrollback", "tool_class": ToolClass.ACTION, "sort": 6,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    LOG = {
        "tool_name": "写日志", "icon": "iconlog", "tool_class": ToolClass.ACTION, "sort": 7,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [FUNC, SEND_EVENT, CREATE_SM, DELETE_SM, COMMIT, ROOLBACK, LOG]


class RulechainControlFlowSYSTool(object):

    FOR = {
        "tool_name": "for", "icon": "iconexprfor", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FOR}
    }
    IF = {
        "tool_name": "if", "icon": "iconexprif", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.IF}
    }
    EXCEPTION = {
        "tool_name": "异常处理", "icon": "iconexception", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.EXCEPTION}
    }
    BREAK = {
        "tool_name": "break", "icon": "iconbreak", "tool_class": ToolClass.ACTION, "sort": 4,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.BREAK}
    }
    CONTINUE = {
        "tool_name": "continue", "icon": "iconcontinue", "tool_class": ToolClass.ACTION, "sort": 5,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.CONTINUE}
    }

    ALL = [FOR, IF, EXCEPTION, BREAK, CONTINUE]


class RulechainObjectActionSYSTool(object):

    MODIFY = {
        "tool_name": "修改对象", "icon": "iconaction-edit", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    DELETE = {
        "tool_name": "删除对象", "icon": "icondelete", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SELECT = {
        "tool_name": "查询对象", "icon": "iconobtain", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SAVE = {
        "tool_name": "保存对象", "icon": "iconsave", "tool_class": ToolClass.ACTION, "sort": 4,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [MODIFY, DELETE, SELECT, SAVE]


class RulechainListActionSYSTool(object):

    MODIFY = {
        "tool_name": "修改列表", "icon": "iconedit-list", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    CALC = {
        "tool_name": "运算列表", "icon": "iconcalculate-list", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    AGG = {
        "tool_name": "聚合列表", "icon": "iconaggregation-list", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [MODIFY, CALC, AGG]


class RulechainVariableActionSYSTool(object):

    MODIFY = {
        "tool_name": "修改变量", "icon": "iconedit-variable", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [MODIFY]


class RulechainFrontActionSYSTool(object):

    CLOSE_PAGE = {
        "tool_name": "关闭页面", "icon": "iconclose-page", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    OPEN_PAGE = {
        "tool_name": "打开页面", "icon": "iconopen-page", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SHOW_MSG = {
        "tool_name": "显示消息", "icon": "iconmessage", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [CLOSE_PAGE, OPEN_PAGE, SHOW_MSG]


class RulechainInternetActionSYSTool(object):

    HTTP = {
        "tool_name": "HTTP请求", "icon": "iconhttp", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    WECHAT = {
        "tool_name": "微信", "icon": "iconwechat", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    DINGTALK = {
        "tool_name": "钉钉", "icon": "iconding", "tool_class": ToolClass.ACTION, "sort": 3,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    EMAIL = {
        "tool_name": "邮件", "icon": "iconemail", "tool_class": ToolClass.ACTION, "sort": 4,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SMS = {
        "tool_name": "短信", "icon": "iconshortmessage", "tool_class": ToolClass.ACTION, "sort": 5,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [HTTP, WECHAT, DINGTALK, EMAIL, SMS]


class RulechainSystemActionSYSTool(object):

    FUNC = {
        "tool_name": "云函数", "icon": "iconcloudfunc-action", "tool_class": ToolClass.ACTION, "sort": 1,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    SEND_EVENT = {
        "tool_name": "发送事件", "icon": "iconsend", "tool_class": ToolClass.ACTION, "sort": 2,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    COMMIT = {
        "tool_name": "提交事务", "icon": "iconsubmit-transaction", "tool_class": ToolClass.ACTION, "sort": 5,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    ROOLBACK = {
        "tool_name": "回滚事务", "icon": "iconrollback", "tool_class": ToolClass.ACTION, "sort": 6,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }
    LOG = {
        "tool_name": "写日志", "icon": "iconlog", "tool_class": ToolClass.ACTION, "sort": 7,
        "content"  : {"drag_type": DragType.ACTION, "action_type": Action.TYPE.FUNC}
    }

    ALL = [FUNC, SEND_EVENT, COMMIT, ROOLBACK, LOG]


class PageContainerSYSTool(object):

    GRID = {
        "tool_name" : PageToolName.GRID,           "icon"   : "icongrid", "sort": 1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.GRID}
    }
    TABS = {
        "tool_name" : "页签组",         "icon"   : "icontabs", "sort": 2,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TABS}
    }
    CONTAINER = {
        "tool_name" : "容器",         "icon"   : "iconcontainer", "sort": 3,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CONTAINER}
    }
    COLLAPSE = {
        "tool_name" : "折叠面板",         "icon"   : "iconcollapse", "sort": 4,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.COLLAPSE}
    }
    SPLIT_PAGE = {
        "tool_name": "分割组件",         "icon"   : "iconfenlanzujian", "sort": 5,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SPLIT_PAGE}
    }
    ALL = [GRID, TABS, CONTAINER, COLLAPSE, SPLIT_PAGE]


class PageDataContainerSYSTool(object):

    DATALIST = {
        "tool_name" : PageToolName.DATALIST,         "icon"   : "icondatalist", "sort": 1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.DATALIST}
    }
    DATAGRID = {
        "tool_name" : PageToolName.DATAGRID,         "icon"   : "icondatagrid", "sort": 2,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.DATAGRID}
    }
    CARDLIST = {
        "tool_name" : PageToolName.CARDLIST,         "icon"   : "iconcardlist", "sort": 3,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CARDLIST}
    }
    FORM = {
        "tool_name" : PageToolName.FORM,           "icon"   : "iconform", "sort": 4,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.FORM}
    }
    CALENDAR = {
        "tool_name" : "日期容器",         "icon"   : "iconcalendar", "sort": 5,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CALENDAR}
    }
    TREE = {
        "tool_name": PageToolName.TREE,
        "icon": "icontree",
        "sort": 6,
        "tool_class": ToolClass.PAGE,
        "content": {
            "component_type": ComponentType.TREE
        }
    }
    TREELIST = {
        "tool_name": PageToolName.TREELIST,
        "icon": "icontreelist",
        "sort": 7,
        "tool_class": ToolClass.PAGE,
        "content": {
            "component_type": ComponentType.TREELIST
        }
    }
    EXECLTABLE = {
        "tool_name": PageToolName.EXECL_TABLE,
        "icon": "iconexcelbiaoge",
        "sort": 8,
        "tool_class": ToolClass.PAGE,
        "content": {
            "component_type": ComponentType.EXECL_TABLE
        }
    }

    ALL = [DATALIST, DATAGRID, CARDLIST, FORM, CALENDAR, TREE, TREELIST, EXECLTABLE]


class PageFormInputSYSTool(object):

    INPUT = {
        "tool_name" : PageToolName.INPUT,          "icon"   : "icontext-old", "sort": 1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.INPUT}
    }
    TEXTAREA = {
        "tool_name" : PageToolName.TEXTAREA,        "icon"   : "icontextarea", "sort": 2,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TEXTAREA}
    }
    RADIO = {
        "tool_name" : PageToolName.RADIO,          "icon"   : "iconradio", "sort": 3,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.RADIO}
    }
    CHECKBOX = {
        "tool_name" : PageToolName.CHECKBOX,          "icon"   : "iconcheckbox", "sort": 4,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CHECKBOX}
    }
    SELECT = {
        "tool_name" : PageToolName.SELECT,          "icon"   : "iconselect", "sort": 5,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SELECT}
    }
    R_SELECT = {
        "tool_name" : PageToolName.R_SELECT,        "icon"   : "iconguanlianxialakuang", "sort": 6,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.R_SELECT}
    }
    R_SELECT_POPUP = {
        "tool_name" : PageToolName.R_SELECT_POPUP,         "icon"   : "iconguanliandanchuang", "sort": 7,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.R_SELECT_POPUP}
    }
    R_TILE = {
        "tool_name" : PageToolName.R_TILE,         "icon"   : "iconguanlianpingpu", "sort": 7.1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.R_TILE},
        "is_delete": 1
    }
    R_SELECT_TABLE = {
        "tool_name" : PageToolName.R_SELECT_TABLE,       "icon"   : "iconrelationship", "sort": 8,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.R_SELECT_TABLE}
    }
    # 任务468去除关联填写子表
    R_CREATE_TABLE = {
        "tool_name" : PageToolName.R_CREATE_TABLE,       "icon"   : "iconrelationship", "sort": 9,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.R_CREATE_TABLE}
    }
    R_CASCADE = {
        "tool_name" : PageToolName.R_CASCADE,       "icon"   : "iconcascader", "sort": 10,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.R_CASCADE}
    }
    R_TREE = {
        "tool_name" : PageToolName.R_TREE,       "icon"   : "icontree-select", "sort": 11,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.R_TREE}
    }
    DATETIME = {
        "tool_name" : PageToolName.DATETIME,      "icon"   : "icondatetime", "sort": 12,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.DATETIME}
    }
    SWITCH = {
        "tool_name" : PageToolName.SWITCH,           "icon"   : "iconswitch", "sort": 13,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SWITCH}
    }
    SLIDER = {
        "tool_name" : PageToolName.SLIDER,         "icon"   : "iconslider", "sort": 14,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SLIDER}
    }
    UPLOAD_FILE = {
        "tool_name" : PageToolName.UPLOAD_FILE,         "icon"   : "iconupload-file", "sort": 15,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.UPLOAD_FILE}
    }
    UPLOAD_IMAGE = {
        "tool_name" : PageToolName.UPLOAD_IMAGE,         "icon"   : "iconupload-picture", "sort": 16,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.UPLOAD_IMAGE}
    }
    COLOR = {
        "tool_name" : PageToolName.COLOR,         "icon"   : "iconupload-color", "sort": 17,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.COLOR}
    }
    FORM_SELECTOR = {
        "tool_name" : "子表单选择器",         "icon"   : "iconform-selector", "sort": 19,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.FORM_SELECTOR}
    }
    TRANSFER = {
        "tool_name": PageToolName.TRANSFER,        "icon": "iconchuansuokuang", "sort": 20,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TRANSFER}
    }

    ALL = [
        INPUT, TEXTAREA, RADIO, CHECKBOX, SELECT, R_SELECT, R_SELECT_POPUP, R_TILE,
        R_CASCADE, R_TREE, DATETIME, SWITCH, SLIDER, UPLOAD_FILE, UPLOAD_IMAGE,
        COLOR, FORM_SELECTOR, TRANSFER
    ]


class PageButtonSYSTool(object):

    NORMAL = {
        "tool_name" : PageToolName.NORMAL_BUTTON,          "icon"   : "icontext", "sort": 1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.NORMAL_BUTTON}
    }
    SUBMIT_BUTTON = {
        "tool_name" : PageToolName.SUBMIT_BUTTON,          "icon"   : "iconbaocunanniu", "sort": 2,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SUBMIT_BUTTON}
    }
    CANCEL_BUTTON = {
        "tool_name" : PageToolName.CANCEL_BUTTON,          "icon"   : "iconquxiaoanniu", "sort": 3,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CANCEL_BUTTON}
    }
    SEARCH_BUTTON = {
        "tool_name" : PageToolName.SEARCH_BUTTON,          "icon"   : "iconsearch2", "sort": 4,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SEARCH_BUTTON}
    }
    NEW_BUTTON = {
        "tool_name" : PageToolName.NEW_BUTTON,          "icon"   : "iconicon2-14", "sort": 5,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.NEW_BUTTON}
    }
    DELETE_BUTTON = {
        "tool_name" : PageToolName.DELETE_BUTTON,          "icon"   : "iconicon2-15", "sort": 6,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.DELETE_BUTTON}
    }
    ADD_BUTTON = {
        "tool_name" : PageToolName.ADD_BUTTON,          "icon"   : "icontianjiaanniu", "sort": 7,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.ADD_BUTTON}
    }
    REMOVE_BUTTON = {
        "tool_name" : PageToolName.REMOVE_BUTTON,          "icon"   : "iconyichuanniu", "sort": 8,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.REMOVE_BUTTON}
    }
    EDIT_BUTTON = {
        "tool_name" : PageToolName.EDIT_BUTTON,          "icon"   : "iconicon2-16", "sort": 9,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.EDIT_BUTTON}
    }
    IMPORT_BUTTON = {
        "tool_name" : PageToolName.IMPORT_BUTTON,          "icon"   : "iconicon2-17", "sort": 10,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.IMPORT_BUTTON}
    }
    EXPORT_BUTTON = {
        "tool_name" : PageToolName.EXPORT_BUTTON,          "icon"   : "iconicon2-18", "sort": 11,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.EXPORT_BUTTON}
    }
    SHOW_BUTTON = {
        "tool_name" : PageToolName.SHOW_BUTTON,          "icon"   : "iconshowhiden", "sort": 12,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SHOW_BUTTON}
    }
    GROUPBY_BUTTON = {
        "tool_name" : PageToolName.GROUPBY_BUTTON,          "icon"   : "icongroupby", "sort": 13,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.GROUPBY_BUTTON}
    }
    EXTERNAL_BUTTON = {
        "tool_name" : PageToolName.EXTERNAL_BUTTON,          "icon"   : "iconexternalfilling", "sort": 14,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.EXTERNAL_BUTTON}
    }
    ALL = [NORMAL, SUBMIT_BUTTON, CANCEL_BUTTON, SEARCH_BUTTON, NEW_BUTTON, DELETE_BUTTON, ADD_BUTTON, REMOVE_BUTTON,
           EDIT_BUTTON, IMPORT_BUTTON, EXPORT_BUTTON, SHOW_BUTTON, GROUPBY_BUTTON, EXTERNAL_BUTTON]


class PageVisualSYSTool(object):

    STACKED_BAR = {
        "tool_name": "堆积条形", "icon": "iconstacked-bar", "sort": 1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.STACKED_BAR}
    }
    STACKED_COLUMN = {
        "tool_name": "堆积柱形", "icon": "iconstacked-column", "sort": 2,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.STACKED_COLUMN}
    }
    CLUSTERED_BAR = {
        "tool_name": "簇状条形", "icon": "iconclustered-bar", "sort": 3,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CLUSTERED_BAR}
    }
    CLUSTERED_COLUMN = {
        "tool_name": "簇状柱形", "icon": "iconclustered-column", "sort": 4,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CLUSTERED_COLUMN}
    }
    PER_STACKED_BAR = {
        "tool_name": "百分比堆积条形", "icon": "iconpre-stacked-bar", "sort": 5,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.PER_STACKED_BAR}
    }
    PER_STACKED_COLUMN = {
        "tool_name": "百分比堆积柱形", "icon": "iconpre-stacked-column", "sort": 6,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.PER_STACKED_COLUMN}
    }
    LINE = {
        "tool_name": "折线", "icon": "iconline", "sort": 7,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.LINE}
    }
    LINE_STACKED_COLUMN = {
        "tool_name": "折线+堆积柱形", "icon": "iconline-stacked-column", "sort": 8,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.LINE_STACKED_COLUMN}
    }
    LINE_CLUSTERED_COLUMN = {
        "tool_name": "折线+簇状柱形", "icon": "iconline-clustered-column", "sort": 9,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.LINE_CLUSTERED_COLUMN}
    }
    PIE = {
        "tool_name": "饼图", "icon": "iconpie", "sort": 10,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.PIE}
    }
    DONUT = {
        "tool_name": "环形", "icon": "icondonut", "sort": 11,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.DONUT}
    }
    TREEMAP = {
        "tool_name": "树形", "icon": "icontreemap", "sort": 12,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TREEMAP}
    }
    GAUGE = {
        "tool_name": "仪表", "icon": "icongauge", "sort": 13,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.GAUGE}
    }
    CARD = {
        "tool_name": "卡片", "icon": "iconcard", "sort": 14,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CARD}
    }
    TABLE = {
        "tool_name": "表", "icon": "icontable", "sort": 15,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TABLE}
    }
    SUMMARY_TABLE = {
        "tool_name": "汇总表", "icon": "iconsummary-table", "sort": 16,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SUMMARY_TABLE}
    }
    ITEM_FILTER = {
        "tool_name": "项目过滤器", "icon": "iconitem-filter", "sort": 17,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.ITEM_FILTER}
    }
    SEARCH_BAR = {
        "tool_name": "搜索栏", "icon": "iconsearch2", "sort": 19,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SEARCH_BAR}
    }
    CUSTOM_REPORT = {
        "tool_name": "表格报表", "icon": "iconbaobiao", "sort": 20,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.CUSTOM_REPORT}
    }

    ALL = [
        STACKED_BAR, STACKED_COLUMN, CLUSTERED_BAR, CLUSTERED_COLUMN, PER_STACKED_BAR, PER_STACKED_COLUMN,
        LINE, LINE_STACKED_COLUMN, LINE_CLUSTERED_COLUMN, PIE, DONUT, TREEMAP, GAUGE, CARD, TABLE,
        SUMMARY_TABLE, ITEM_FILTER, SEARCH_BAR, CUSTOM_REPORT
    ]


class PagePresentSYSTool(object):

    TEXT = {
        "tool_name": "文本", "icon": "icontext-old", "sort": 1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TEXT}
    }
    IMAGE = {
        "tool_name": "图片", "icon": "iconimage", "sort": 2,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.IMAGE}
    }
    FILE = {
        "tool_name": "文件", "icon": "iconfile", "sort": 3,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.FILE}
    }
    TIMEAXIS = {
        "tool_name": "时间轴", "icon": "icontimeaxis", "sort": 4,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TIMEAXIS}
    }
    TIMELINE = {
        "tool_name": "时间线", "icon": "icontimeline", "sort": 5,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TIMELINE}
    }
    TAG = {
        "tool_name": "标签", "icon": "icontag", "sort": 6,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.TAG}
    }
    LINE_BAR = {
        "tool_name": "条形进度条", "icon": "iconline-bar", "sort": 7,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.LINE_BAR}
    }
    RING_BAR = {
        "tool_name": "环形进度条", "icon": "iconring-bar", "sort": 8,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.RING_BAR}
    }

    ALL = [TEXT, IMAGE, FILE, TIMEAXIS, TIMELINE, TAG, LINE_BAR, RING_BAR]


class PageNavigationSYSTool(object):
    DROPDOWN_MENU = {
        "tool_name": PageToolName.DROPDOWN_MENU, "icon": "iconxialacaidan", "sort": 1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.DROPDOWN_MENU}
    }

    ALL = [DROPDOWN_MENU]


class PageExtendSYSTool(object):

    GANTT = {
        "tool_name": "甘特图", "icon": "icongantt", "sort": 1,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.GANTT},
        "is_delete": 1
    }
    PRINT = {
        "tool_name": "打印", "icon": "iconprint", "sort": 2,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.PRINT},
        "is_delete": 0
    }
    SCAN = {
        "tool_name": "扫码", "icon": "iconscan", "sort": 3,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.SCAN}
    }
    PLANEPOSITION = {
        "tool_name": "平面定位", "icon": "iconplaneposition", "sort": 4,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.PLANEPOSITION},
        "is_delete": 1
    }
    REACT = {
        "tool_name": "云函数组件", "icon": "iconcloudfunc-action", "sort": 5,
        "tool_class": ToolClass.PAGE, "content": {"component_type": ComponentType.REACT}
    }
    ALL = [GANTT, PRINT, SCAN, PLANEPOSITION, REACT]


class PrintContainerSYSTool(object):

    FORM = {
        "tool_name" : "表单",           "icon"   : "iconform", "sort": 1,
        "tool_class": ToolClass.PRINT, "content": {"template_type": PrintType.FORM, "drag_type": DragType.PRINT_CONTAINER}
    }
    CARDLIST = {
        "tool_name" : "卡片列表",         "icon"   : "iconcardlist", "sort": 2,
        "tool_class": ToolClass.PRINT, "content": {"template_type": PrintType.CARDLIST, "drag_type": DragType.PRINT_CONTAINER}
    }
    DATALIST = {
        "tool_name" : "数据列表",         "icon"   : "icondatalist", "sort": 3,
        "tool_class": ToolClass.PRINT, "content": {"template_type": PrintType.DATALIST, "drag_type": DragType.PRINT_CONTAINER}
    }

    ALL = [FORM, CARDLIST, DATALIST]


class PrintLayoutSYSTool(object):

    TABLE = {
        "tool_name" : "表格",           "icon"   : "icontable", "sort": 1,
        "tool_class": ToolClass.PRINT, "content": {"template_type": PrintType.TABLE, "drag_type": DragType.PRINT_CONTAINER}
    }
    BREAK = {
        "tool_name" : "分页符",         "icon"   : "iconpagebreaks", "sort": 2,
        "tool_class": ToolClass.PRINT, "content": {"template_type": PrintType.BREAK, "drag_type": DragType.PRINT_CONTAINER}
    }

    ALL = [TABLE, BREAK]


class PrintDataSYSTool(object):

    VISUAL = {
        "tool_name" : "可视化组件",         "icon"   : "iconvisual", "sort": 1,
        "tool_class": ToolClass.PRINT, "content": {"template_type": PrintType.VISUAL, "drag_type": DragType.PRINT_NODE}
    }
    IMAGE = {
        "tool_name" : "图片",            "icon"   : "iconimage", "sort": 2,
        "tool_class": ToolClass.PRINT, "content": {"template_type": PrintType.IMAGE, "drag_type": DragType.PRINT_NODE}
    }
    TEXT = {
        "tool_name" : "文本",            "icon"   : "icontext-old", "sort": 3,
        "tool_class": ToolClass.PRINT, "content": {"template_type": PrintType.TEXT, "drag_type": DragType.PRINT_NODE}
    }

    ALL = [VISUAL, IMAGE, TEXT]

class LabelPrintAssemblySYSTool(object):
    RECT_BOX = {
        "tool_name" : "矩形框",         "icon"   : "iconjuxingkuang", "sort": 1,
        "tool_class": ToolClass.PRINT, "content": {"label_type": LabelPrintType.RECT_BOX, "drag_type": DragType.PRINT_NODE}
    }
    
    STRAIGHT_LINE = {
        "tool_name" : "直线",           "icon"   : "iconzhixian", "sort": 2,
        "tool_class": ToolClass.PRINT, "content": {"label_type": LabelPrintType.STRAIGHT_LINE, "drag_type": DragType.PRINT_NODE}
    }
    
    TEXT = {
        "tool_name" : "文字",           "icon"   : "iconwenzi", "sort": 3,
        "tool_class": ToolClass.PRINT, "content": {"label_type": LabelPrintType.TEXT, "drag_type": DragType.PRINT_NODE}
    }
    
    MULTILINE_TEXT = {
        "tool_name" : "多行文字",           "icon"   : "iconduohangwenzi", "sort": 4,
        "tool_class": ToolClass.PRINT, "content": {"label_type": LabelPrintType.MULTILINE_TEXT, "drag_type": DragType.PRINT_NODE}
    }
    
    BAR_CODE = {
        "tool_name" : "条形码",           "icon"   : "icontiaoxingma", "sort": 5,
        "tool_class": ToolClass.PRINT, "content": {"label_type": LabelPrintType.BAR_CODE, "drag_type": DragType.PRINT_NODE}
    }
    
    QR_CODE = {
        "tool_name" : "二维码",           "icon"   : "iconerweima", "sort": 6,
        "tool_class": ToolClass.PRINT, "content": {"label_type": LabelPrintType.QR_CODE, "drag_type": DragType.PRINT_NODE}
    }
    
    IMAGE = {
        "tool_name" : "图片",           "icon"   : "icontupian", "sort": 7,
        "tool_class": ToolClass.PRINT, "content": {"label_type": LabelPrintType.IMAGE, "drag_type": DragType.PRINT_NODE}
    }
    
    ALL = [RECT_BOX, STRAIGHT_LINE, TEXT, MULTILINE_TEXT, BAR_CODE, QR_CODE, IMAGE]

class SMSYSTool(object):

    DEFAULT         = list()
    GENERAL         = SMGeneralSYSTool.ALL
    CONTROL_FLOW    = SMControlFlowSYSTool.ALL
    OBJECT_ACTION   = SMObjectActionSYSTool.ALL
    LIST_ACTION     = SMListActionSYSTool.ALL
    VARIABLE_ACTION = SMVariableActionSYSTool.ALL
    FRONT_ACTION    = SMFrontActionSYSTool.ALL
    INTERNET_ACTION = SMInternetActionSYSTool.ALL
    SYSTEM_ACTION   = SMSystemActionSYSTool.ALL


class RulechainSYSTool(object):

    CONTROL_FLOW    = RulechainControlFlowSYSTool.ALL
    OBJECT_ACTION   = RulechainObjectActionSYSTool.ALL
    LIST_ACTION     = RulechainListActionSYSTool.ALL
    VARIABLE_ACTION = RulechainVariableActionSYSTool.ALL
    FRONT_ACTION    = RulechainFrontActionSYSTool.ALL
    INTERNET_ACTION = RulechainInternetActionSYSTool.ALL
    SYSTEM_ACTION   = RulechainSystemActionSYSTool.ALL


class PageSYSTool(object):

    DEFAULT        = list()
    CONTAINER      = PageContainerSYSTool.ALL
    DATA_CONTAINER = PageDataContainerSYSTool.ALL
    FORM_INPUT     = PageFormInputSYSTool.ALL
    BUTTON         = PageButtonSYSTool.ALL
    VISUAL         = PageVisualSYSTool.ALL
    PRESENT        = PagePresentSYSTool.ALL
    NAVIGATION     = PageNavigationSYSTool.ALL
    EXTEND         = PageExtendSYSTool.ALL


class PrintSYSTool(object):

    DEFAULT = list()
    CONTAINER = PrintContainerSYSTool.ALL
    LAYOUT = PrintLayoutSYSTool.ALL
    DATA = PrintDataSYSTool.ALL

class LabelPrintSYSTool(object):
    DEFAULT = list()
    ASSEMBLY = LabelPrintAssemblySYSTool.ALL
    

class ToolCategoryType(object):

    SYS  = 0
    USER = 1

    ALL = [SYS, USER]


class SaveType(object):
    NO_SUPPORT = 0
    TRANSITION_SUPPORT = 1
    LOCAL_SUPPORT = 2


class ToolCategory(object):

    MODEL = [{
        "category_name": "通用",
        "category_type": ToolCategoryType.SYS,
        "entity_type"  : EntityType.MODEL,
        "tools"        : ModelSYSTool.ALL
    }]

    SM = [
        {
            "category_name": "通用",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.GENERAL
        },
        {
            "category_name": "控制流动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.CONTROL_FLOW
        },
        {
            "category_name": "对象变量动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.OBJECT_ACTION
        },
        {
            "category_name": "列表变量动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.LIST_ACTION
        },
        {
            "category_name": "值变量动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.VARIABLE_ACTION
        },
        {
            "category_name": "前端动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.FRONT_ACTION
        },
        {
            "category_name": "Internet动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.INTERNET_ACTION
        },
        {
            "category_name": "系统动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.SYSTEM_ACTION
        },
        {
            "category_name": "其他动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.SM,
            "tools"        : SMSYSTool.DEFAULT
        },
    ]

    PAGE = [
        {
            "category_name": "布局组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PAGE,
            "tools"        : PageSYSTool.CONTAINER
        },
        {
            "category_name": "数据容器",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PAGE,
            "tools"        : PageSYSTool.DATA_CONTAINER
        },
        {
            "category_name": "表单输入组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PAGE,
            "tools"        : PageSYSTool.FORM_INPUT
        },
        {
            "category_name": "按钮",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PAGE,
            "tools"        : PageSYSTool.BUTTON
        },
        {
            "category_name": "数据可视化组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PAGE,
            "tools"        : PageSYSTool.VISUAL
        },
        {
            "category_name": "数据展示组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PAGE,
            "tools"        : PageSYSTool.PRESENT
        },
        {
            "category_name": "导航",
            "category_type": ToolCategoryType.SYS,
            "entity_type": EntityType.PAGE,
            "tools": PageSYSTool.NAVIGATION
        },
        {
            "category_name": "自定义组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PAGE,
            "tools"        : PageSYSTool.EXTEND
        },
        {
            "category_uuid": "7183e6bdf1955d739313ff2979b9254d",
            "category_name": "导入组件",
            "category_type": ToolCategoryType.USER,
            "entity_type"  : EntityType.PAGE,
            "tools"        : list()
        }
    ]

    RULECHAIN = [
        {
            "category_name": "控制流动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.RULECHAIN,
            "tools"        : RulechainSYSTool.CONTROL_FLOW
        },
        {
            "category_name": "对象变量动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.RULECHAIN,
            "tools"        : RulechainSYSTool.OBJECT_ACTION
        },
        {
            "category_name": "列表变量动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.RULECHAIN,
            "tools"        : RulechainSYSTool.LIST_ACTION
        },
        {
            "category_name": "值变量动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.RULECHAIN,
            "tools"        : RulechainSYSTool.VARIABLE_ACTION
        },
        {
            "category_name": "前端动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.RULECHAIN,
            "tools"        : RulechainSYSTool.FRONT_ACTION
        },
        {
            "category_name": "Internet动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.RULECHAIN,
            "tools"        : RulechainSYSTool.INTERNET_ACTION
        },
        {
            "category_name": "系统动作",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.RULECHAIN,
            "tools"        : RulechainSYSTool.SYSTEM_ACTION
        },
    ]

    PRINT = [
        {
            "category_name": "容器组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PRINT,
            "tools"        : PrintSYSTool.CONTAINER
        },
        {
            "category_name": "布局组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PRINT,
            "tools"        : PrintSYSTool.LAYOUT
        },
        {
            "category_name": "数据组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.PRINT,
            "tools"        : PrintSYSTool.DATA
        },
    ]

    LABEL_PRINT = [
        {
            "category_name": "组件",
            "category_type": ToolCategoryType.SYS,
            "entity_type"  : EntityType.LABEL_PRINT,
            "tools"        : LabelPrintSYSTool.ASSEMBLY
        }
    ]

    ALL = [MODEL, SM, PAGE, RULECHAIN, PRINT, LABEL_PRINT]


class IDECode(Code):

    TITLE_NAME          = "{name}必须以字母、下划线开始，可以包含数字、字母、下划线，不能包含减号'-'，并且长度不能超过40"
    TITLE_DOCUMENT_NAME = "{name}可用中文、字母、数字及 -_.,@#%^+= 特殊符号，但不能以 . 开头，并且长度不能超过40"
    TITLE_CHINESE_NAME  = "{name}必须以中文、字母、下划线开始，可以包含数字、中文、字母、下划线，不能包含减号'-'，并且长度不能超过40"
    COMPONENT_FIELD_NAME = "{name}{field_name}未选择字段"
    KEY_WORDS_NAME = "不能使用关键字{}命名"
    NULL_CONDITION = "请先选择<{model_name}>/<{name}>"
    P_NULL_CONDITION = "该组件的约束条件没有被满足"

    COPY_DOCUMENTS_NEED_TEN_SECONDS       = ReturnCode(1099, "复制新复制的文档需要间隔10s")
    APP_NOT_EXISTS                        = ReturnCode(1100, "应用不存在")
    APP_EXISTS                            = ReturnCode(1101, "应用已存在")
    MODULE_NOT_EXISTS                     = ReturnCode(1102, "模块不存在")
    MODULE_EXISTS                         = ReturnCode(1103, "模块已存在")
    DOCUMENT_NOT_EXISTS                   = ReturnCode(1104, "文档不存在")
    DOCUMENT_SOURCE_NOT_EXISTS            = ReturnCode(11041, "源文档不存在")
    DOCUMENT_EXISTS                       = ReturnCode(1105, "文档已存在")
    PARENT_DOCUMENT_NOT_EXISTS            = ReturnCode(1106, "父目录不存在")
    PARENT_DOCUMENT_NOT_DIR               = ReturnCode(1107, "父目录必须为目录类型")
    DOCUMENT_TYPE_NOT_EXISTS              = ReturnCode(1108, "文档类型不存在")
    DOCUMENT_PATH_MAX_DEPTH               = ReturnCode(1109, "最大目录深度为8层")
    DOCUMENT_DIR_NOT_CLEAN                = ReturnCode(1110, "文件夹不为空")
    DOCUMENT_NAME_SAME                    = ReturnCode(1111, "文档名未修改")
    DOCUMENT_NAME_EXISTS                  = ReturnCode(1112, "模块下文档名称已存在")
    DOCUMENT_NAME_FAILED                  = ReturnCode(1113, TITLE_DOCUMENT_NAME.format(name="文件名称"))
    MODULE_NAME_FAILED                    = ReturnCode(1114, TITLE_CHINESE_NAME.format(name="模块名称"))
    APP_NAME_FAILED                       = ReturnCode(1115, TITLE_CHINESE_NAME.format(name="应用名称"))
    DOCUMENT_VERSION_LOWER                = ReturnCode(1116, "文档版本低于当前已存在的版本")
    DOCUMENT_TYPE_INCURRECT               = ReturnCode(1117, "文档类型不匹配")
    TOOL_CATEGORY_EXISTS                  = ReturnCode(1118, "工具箱分类已存在")
    TOOL_CATEGORY_NOT_EXISTS              = ReturnCode(1119, "工具箱分类不存在")
    TOOL_EXISTS                           = ReturnCode(1120, "工具箱分类中已存在改工具名称")
    ENTITY_NOT_EXISTS                     = ReturnCode(1121, "实体类型不存在")
    MODEL_NOT_EXISTS                      = ReturnCode(1122, "数据模型不存在", "数据源")
    PAGE_NOT_EXISTS                       = ReturnCode(1123, "页面不存在")
    P_PAGE_NOT_EXISTS                     = ReturnCode(1124, "父页面不存在")
    DOCUMENT_TYPE_NOT_CREATABLE           = ReturnCode(1125, "不允许手动创建的文档类型")
    DOCUMENT_TYPE_NOT_ALLOW_DEL           = ReturnCode(1126, "不允许删除的文档类型")
    USER_ROLE_NOT_EXISTS                  = ReturnCode(1126, "用户角色不存在")
    MODULE_NAME_EXISTS                    = ReturnCode(1127, "模块名称已存在")
    PAGE_FORM_COUNT_NEED_ONE              = ReturnCode(1128, "页面有且只需有一个表单组件")
    PAGE_FORM_MODEL_NOT_FOUND             = ReturnCode(1129, "页面表单模型不存在")
    WORKFLOW_NOT_EXISTS                   = ReturnCode(1130, "工作流不存在")
    MODULE_NOT_CLEAN                      = ReturnCode(1131, "模块不为空")
    KEY_WORDS_ERROR                       = ReturnCode(1132, "")
    SYS_MODULE_DOCUMENT_FORBID_MOVE       = ReturnCode(1133, "系统模块下文档不可移动")
    SYS_FIELD_UNEDITABLE                  = ReturnCode(1134, "系统字段不支持编辑")
    TOO_MANY_OPTIONS                      = ReturnCode(1135, "数据未全部加载，请使用搜索方式")
    MODULE_NAME_FORBID_PYTHON_KEYWORDLIST = ReturnCode(1136, "模块名称不允许为python关键字")
    FUNC_NAME_FORBIT_PYTHON_KEYWORD       = ReturnCode(1137, "云函数名称不允许为python关键字")
    FUNC_NAME_FAILED                      = ReturnCode(1139, TITLE_CHINESE_NAME.format(name="云函数名称"))
    MODEL_DOCUMENT_CLEAN_ERROR            = ReturnCode(1140, "数据模型正在被清空！！！")
    LIMIT_CONDITION_IS_NULL               = ReturnCode(1141, "请选择约束条件")
    APP_NAME_ENDS_ERROR                   = ReturnCode(1142, "为避免与默认示例应用混淆, 应用名请勿包含< 示例应用 >")
    FIELD_IS_REQUIRED_NO_DEFAULT_VALUE    = ReturnCode(1143, "字段必填但无默认值")
    DELETE_MODULE_ERROR                   = ReturnCode(1144, "删除模块出错")
    GIT_PROJECT_NOT_FOUND                 = ReturnCode(1146, "项目版本控制未开启")
    USER_GIT_SPACE_NOT_INIT               = ReturnCode(1147, "用户项目版本控制未初始化")
    TEAM_COMBINER_PERMISSION_REPEAT       = ReturnCode(1148, "用户已拥有应用权限, 添加失败")
    USER_NOT_TEAMPLAYER                   = ReturnCode(1149, "添加了非团队成员的用户, 添加失败")
    MODULE_NAME_FORBID_SYS_MODULE         = ReturnCode(1150, "模块名称不允许为{name}")
    EXTEND_MODULE_FORBID_MOVE_OUT         = ReturnCode(1151, "导入模块中的文档不允许移出")
    PY_MODULE_NAME_FAILED                 = ReturnCode(1152, TITLE_CHINESE_NAME.format(name="Python模块名称"))
    PY_MODULE_NAME_FORBID_PYTHON_KEYWORD       = ReturnCode(1137, "Python模块名称不允许为python关键字")

    CONTENT_NOT_EXISTS                    = ReturnCode(1199, "content不存在")

    EXTENSION_DEPLOY_LOSS                 = ReturnCode(1300, "缺少模块配置系统文档,模块不完整或已删除")
    EXTENSION_ALREADY_ON_SHELFED          = ReturnCode(1301, "不支持扩展重复上架")
    EXTENSION_ONLY_TEMPLATE               = ReturnCode(1302, "仅支持发布为模板")
    EXTENSION_NOT_FOUND                   = ReturnCode(1303, "商城中没有找到目标扩展")
    INVALID_ACTION                        = ReturnCode(1304, "非法操作")
    PUBLIC_EXTENSION_NOT_ALLOW_DEL        = ReturnCode(1305, "公开状态的扩展不支持删除")
    EXTENSION_ALREADY_IMPORTED            = ReturnCode(1306, "不支持重复安装同一扩展")
    USER_NOT_PUBLISHER                    = ReturnCode(1307, "用户不是发行商")
    APP_NAME_ACCOUNT_ERROR                = ReturnCode(1308, "应用名或账号错误")
    ONLY_PUBLISHER_DO_PUBLIC              = ReturnCode(1309, "仅支持发行商进行上架操作")
    REPEAT_PUBLISHER_PUSH_TO_SAME_USER    = ReturnCode(1310, "该用户存在尚未处理的该扩展的导入请求")
    EXTENSION_RELEASE_TYPE_INVALID        = ReturnCode(1311, "扩展类型不合法")
    EXTENSION_MODULE_NOT_FOUND            = ReturnCode(1312, "扩展对应模块不存在")
    EXTENSION_VERSION_NOT_SET             = ReturnCode(1313, "当前版本未设置")
    EXTENSION_PRIVATE_NOT_ALLOW_DELETE    = ReturnCode(1314, "不支持删除公开到商城的扩展")
    UI_COMPONENT_VERSION_NOT_EXIST        = ReturnCode(1315, "ui组件版本不存在")
    ALREADY_EXIST_SAME_NAME_UI_COMPONENT  = ReturnCode(1316, "已安装重名自定义组件")
    TEMPLATE_MODULE_ALREADY_EXISTS        = ReturnCode(1317, "模块已存在应用中,需要删除后重新导入")

    ACTION_INVALID = ReturnCode(1351, "标签操作配置不合法")
    WX_APPID_EXIST    = ReturnCode(1414, "该微信或小程序已绑定其他应用，请先在《{app_name}》解除绑定")
    APP_WX_NOT_BIND_EXIST    = ReturnCode(1415, "该应用未绑定微信或小程序")
    PERSIONAL_APPLET_NOT_SUPPORT = ReturnCode(1416, "暂不支持个人版小程序")
    INVALID_LOCALHISTORY_VERSION = ReturnCode(3300, "本地历史版本失效")
    APP_NOT_COMMIT = ReturnCode(3301, "应用未发布小程序")
    EXTENSION_PACKAGE_PIP_ERROR = ReturnCode(3333, "包或版本与环境不兼容")
    NOT_SUPPORT_ON_IMAGES_TYPE = ReturnCode(1590, "不支持该图片格式")





class LemonDesignerErrorCode(IDECode):

    SOURCE_MODEL = RelationshipAttr.SOURCE_MODEL
    TARGET_MODEL = RelationshipAttr.TARGET_MODEL

    ELEMENT_UUID_ERROR = ReturnCode(1499, "元素< {name} >UUID错误")
    ELEMENT_UUID_UNIQUE_ERROR = ReturnCode(1498, "元素< {name} UUID重复>")
    ELEMENT_NAME_ERROR = ReturnCode(1497, "元素< {name} >名称错误")
    ELEMENT_NAME_UNIQUE_ERROR = ReturnCode(1496, "元素< {name} >名称重复")
    ELEMENT_NAME_NOT_FOUND = ReturnCode(1497, "< {name} >元素未找到")

    MODEL_DEFAULT_ERROR            = ReturnCode(1599, "模型校验错误")
    MODEL_UUID_ERROR               = ReturnCode(1598, "模型< {name} >UUID错误", "通用")
    MODEL_UUID_UNIQUE_ERROR        = ReturnCode(1597, "模型< {name} >UUID重复", "通用")
    RELATIONSHIP_UUID_ERROR        = ReturnCode(1596, "关联< {name} >UUID错误", "通用")
    RELATIONSHIP_UUID_UNIQUE_ERROR = ReturnCode(1595, "关联< {name} >UUID重复", "通用")
    FIELD_UUID_ERROR               = ReturnCode(1594, "字段< {name} >UUID错误", "通用")
    FIELD_UUID_UNIQUE_ERROR        = ReturnCode(1593, "字段< {name} >UUID重复", "通用")
    INDEX_UUID_ERROR               = ReturnCode(1592, "索引< {name} >UUID错误", "通用")
    INDEX_UUID_UNIQUE_ERROR        = ReturnCode(1591, "索引< {name} >UUID重复", "通用")

    MODEL_NAME_FAILED                   = ReturnCode(1500, IDECode.TITLE_CHINESE_NAME.format(name="模型名称"))
    MODEL_NAME_NOT_UNIQUE               = ReturnCode(1501, "模型名称需在< 模块 >中唯一", "通用")
    MODEL_UUID_FAILED                   = ReturnCode(1502, "模型UUID格式错误", "通用")
    MODEL_UUID_NOT_UNIQUE               = ReturnCode(1503, "模型UUID需在< 模块 >中唯一", "通用")
    MODEL_NAME_FIELD_NOT_EXISTS         = ReturnCode(1504, "所选数据名称字段不存在", "通用")
    MODEL_SORT_FIELD_NOT_EXISTS         = ReturnCode(1505, "所选排序字段不存在", "通用")
    MODEL_CHECK_FIELD_NOT_EXISTS        = ReturnCode(1506, "所选有效性字段不存在", "通用")
    MODEL_USER_MODEL_NOT_EXISTS         = ReturnCode(1507, "所属用户关联不存在", "通用")
    MODEL_DEPARTMENT_MODEL_NOT_EXISTS   = ReturnCode(1508, "所属部门关联不存在", "通用")
    MODEL_DISPLAY_NAME_FAILED           = ReturnCode(1509, IDECode.TITLE_CHINESE_NAME.format(name="模型展示名称"), "通用")
    MODEL_NAME_FIELD_IS_AGGRE_FIELD     = ReturnCode(1510, "所选数据名称字段不能是聚合字段", "通用")
    MODEL_NAME_FIELD_IS_HIERARCHY_FIELD = ReturnCode(1511, "所选数据名称字段不能是层次字段", "通用")
    MODEL_NAME_FIELD_NOT_FUNC           = ReturnCode(1512, "字段< {field_name} >中,计算字段没有绑定云函数", "通用")
    MODEL_FIELD_NOT_EXISTS              = ReturnCode(1513, "字段未绑定或字段不存在", "数据源")
    MODEL_FIELD_VALUE_EDITOR_NONE       = ReturnCode(1514, "字段< {field_name} >中,计算字段值编辑器未设置", "通用")
    MODEL_NAME_FIELD_FUNC_NOT_EXISTS    = ReturnCode(1515, "字段< {field_name} >中,计算字段绑定云函数被删除", "通用")
    MODEL_NAME_REPEAT_SYS_MODEL = ReturnCode(1516, "模型名称与系统模块模型重名", "通用")
    CALCULATE_FIELD_VALUE_EDITOR_ERROR = ReturnCode(1517, "计算字段<{field_name}>不能引用自己本身", "通用")


    FIELD_NAME_FAILED            = ReturnCode(1520, IDECode.TITLE_CHINESE_NAME.format(name="字段名称"), "通用")
    FIELD_NAME_NOT_UNIQUE        = ReturnCode(1521, "字段名称需在< 模型 >中唯一", "通用")
    FIELD_UUID_FAILED            = ReturnCode(1522, "字段UUID格式错误", "通用")
    FIELD_UUID_NOT_UNIQUE        = ReturnCode(1523, "字段UUID需在< 模型 >中唯一")
    FIELD_TYPE_NOT_SUPPORT       = ReturnCode(1524, "字段类型不支持", "通用")
    FIELD_TYPE_OBSOLETE          = ReturnCode(1552, "<{0}>字段类型已弃用，请删除", "通用")
    FIELD_ENUM_NOE_EXISTS        = ReturnCode(1525, "枚举字段未选择枚举类型", "通用")
    FIELD_DISPLAY_NAME_FAILED    = ReturnCode(1526, IDECode.TITLE_CHINESE_NAME.format(name="字段名称"), "通用")
    FIELD_DEFAULT_VALUE_ERROR    = ReturnCode(1527, "字段默认值类型错误", "表单相关")
    FIELD_DEFAULT_VALUE_ENUM_NOT_EXISTS = ReturnCode(1552, "字段的默认值不存在", "通用")
    FIELD_SERIAL_TYPE_ERROR      = ReturnCode(1528, "仅文本字段可设置为流水号", "通用")
    FIELD_SERIAL_RULE_ERROR      = ReturnCode(1529, "流水号规则设置有误", "通用")
    FIELD_SERIAL_PREFIX_ERROR    = ReturnCode(1530, "流水号前缀仅支持英文,中文及数字,且长度不得超过30位", "通用")
    SERIAL_CALCULATE_ERROR       = ReturnCode(1531, "流水号不可设置为计算字段", "通用")
    FIELD_SERIAL_NUM_LEN_ERROR   = ReturnCode(1532, "流水号位数设置有误", "通用")
    DECIMAL_LENGTH_ERROR         = ReturnCode(1533, "小数字段总长度位数需不小于小数部分位数", "通用")
    FIELD_SERIAL_GEN_TYPE_ERROR  = ReturnCode(1534, "流水号生成方式设置有误", "通用")
    FIELD_SERIAL_START_NUM_ERROR = ReturnCode(1535, "流水号起始值设置有误", "通用")
    DECIMALS_NONE_ERROR          = ReturnCode(1536, "小数位为0", "通用")
    FILED_NAME_ERROR             = ReturnCode(1537, "{field_name}为系统字段不能作为字段名称", "通用")
    FIELD_DESCRIPTION_ERROR      = ReturnCode(1538, "数据模型新增的字段描述带“%”", "通用")
    CHECK_FUNCTION_NOT_FIELD_IN_FIELD = ReturnCode(1539, "{name} 字段验证方法未填写表达式", "通用")
    SYSTEM_BACKREF_FILED_NAME_ERROR = ReturnCode(1540, "<{field_name}>与<{relation_name}>在系统表中引用使用的字段名重复", "通用")

    INDEX_NAME_FAILED        = ReturnCode(1540, IDECode.TITLE_NAME.format(name="索引名称"), "通用")
    INDEX_NAME_NOT_UNIQUE    = ReturnCode(1541, "索引名称需在< 模型 >中唯一", "通用")
    INDEX_UUID_FAILED        = ReturnCode(1542, "索引UUID格式错误", "通用")
    INDEX_UUID_NOT_UNIQUE    = ReturnCode(1543, "索引UUID需在< 模型> 中唯一", "通用")
    INDEX_FIELD_NOT_EXISTS   = ReturnCode(1544, "索引所选字段不存在", "通用")
    INDEX_FIELD_REPETITION   = ReturnCode(1545, "索引所选字段重复", "通用")
    INDEX_FIELD_ORDER_UNIQUE = ReturnCode(1546, "索引所选字段及顺序需在模型中唯一", "通用")
    INDEX_TYPE_NOT_SUPPORT   = ReturnCode(1547, "索引映射关系不支持", "通用")
    INDEX_FIELDS_IS_NULL     = ReturnCode(1548, "索引所选字段为空", "通用")
    INDEX_FIELD_IS_AGGRE_FIELD     = ReturnCode(1549, "索引所选字段不能是聚合字段", "通用")
    INDEX_FIELD_IS_HIERARCHY_FIELD = ReturnCode(1550, "索引所选字段不能是层次字段", "通用")
    INDEX_FIELD_IS_CALCULATE_FIELD = ReturnCode(1551, "索引所选字段不能是非生成列的计算字段", "通用")

    RELATIONSHIP_NAME_FAILED              = ReturnCode(1561, IDECode.TITLE_CHINESE_NAME.format(name="关联名称"), "通用")
    RELATIONSHIP_NAME_NOT_UNIQUE          = ReturnCode(1562, "关联名称需在< 应用 >中唯一", "通用")
    RELATIONSHIP_UUID_FAILED              = ReturnCode(1563, "关联UUID格式错误", "通用")
    RELATIONSHIP_UUID_NOT_UNIQUE          = ReturnCode(1564, "关联UUID需在< 应用 >中唯一", "通用")
    RELATIONSHIP_SOURCE_MODEL_NOT_EXISTS  = ReturnCode(1565, SOURCE_MODEL + "不存在", "通用")
    RELATIONSHIP_TARGET_MODEL_NOT_EXISTS  = ReturnCode(1566, TARGET_MODEL + "不存在", "通用")
    RELATIONSHIP_TYPE_NOT_SUPPORT         = ReturnCode(1567, "关联类型不支持", "通用")
    RELATIONSHIP_SOURCE_ON_DELETE         = ReturnCode(1568, SOURCE_MODEL + "删除行为不支持", "删除行为")
    RELATIONSHIP_TARGET_ON_DELETE         = ReturnCode(1569, TARGET_MODEL + "删除行为不支持", "删除行为")
    RELATIONSHIP_TARGET_MODEL_NOT_STORAGE = ReturnCode(1570, "永久存储模型不能指向非永久存储模型", "通用")
    RELATIONSHIP_EXISTS                   = ReturnCode(1571, "与模型的关联关系已存在")
    RELATIONSHIP_FRONTREF_FAILED          = ReturnCode(1572, IDECode.TITLE_CHINESE_NAME.format(name=SOURCE_MODEL+"引用名称"), "通用")
    RELATIONSHIP_BACKREF_FAILED           = ReturnCode(1573, IDECode.TITLE_CHINESE_NAME.format(name=TARGET_MODEL+"引用名称"), "通用")
    RELATIONSHIP_FRONTREF_NOT_UNIQUE      = ReturnCode(1574, "<{0}>关联中的引用名称<{1}>与模型<{2}>的字段名重复，需保持唯一性", "通用")
    RELATIONSHIP_BACKREF_NOT_UNIQUE       = ReturnCode(1575, "<{0}>关联中的引用名称<{1}>与模型<{2}>的字段名重复，需保持唯一性", "通用")
    RELATIONSHIP_DISPLAY_NAME_FAILED      = ReturnCode(1576, IDECode.TITLE_CHINESE_NAME.format(name="关联展示名称"), "通用")
    RELATIONSHIP_SELF_REFERENTIAL_FAILED  = ReturnCode(1578, "自关联不支持多对多")
    RELATIONSHIP_SYSTEM_TABLE_IS_SOURCE   = ReturnCode(1579, "<{name}>不能作为外键端")
    RELATIONSHIP_SYSTEM_TABLE_MANY_TO_MANY = ReturnCode(1580, "系统表不能多对多关联")

    SM_DEFAULT_ERROR             = ReturnCode(1799, "状态机校验错误")
    SM_UUID_ERROR                = ReturnCode(1798, "状态机< {name} >UUID错误")
    SM_UUID_UNIQUE_ERROR         = ReturnCode(1797, "状态机< {name} >UUID重复")

    STATE_UUID_ERROR             = ReturnCode(1796, "状态< {name} >UUID错误")
    STATE_UUID_UNIQUE_ERROR      = ReturnCode(1795, "状态< {name} >UUID重复")
    EVENT_UUID_ERROR             = ReturnCode(1794, "事件< {name} >UUID错误")
    EVENT_UUID_UNIQUE_ERROR      = ReturnCode(1793, "事件< {name} >UUID重复")
    VARIABLE_UUID_ERROR          = ReturnCode(1792, "变量< {name} >UUID错误")
    VARIABLE_UUID_UNIQUE_ERROR   = ReturnCode(1791, "变量< {name} >UUID重复")
    TIMER_UUID_ERROR             = ReturnCode(1790, "定时器< {name} >UUID错误")
    TIMER_UUID_UNIQUE_ERROR      = ReturnCode(1789, "定时器< {name} >UUID重复")
    TRIGGER_UUID_ERROR           = ReturnCode(1792, "触发器< {name} >UUID错误")
    TRIGGER_UUID_UNIQUE_ERROR    = ReturnCode(1791, "触发器< {name} >UUID重复")
    ACTION_UUID_ERROR            = ReturnCode(1790, "动作< {name} >UUID错误")
    ACTION_UUID_UNIQUE_ERROR     = ReturnCode(1789, "动作< {name} >UUID重复")
    TRANSITION_UUID_ERROR        = ReturnCode(1788, "转换< {name} >UUID错误")
    TRANSITION_UUID_UNIQUE_ERROR = ReturnCode(1787, "转换< {name} >UUID重复")
    CONDITION_UUID_ERROR         = ReturnCode(1786, "条件< {name} >UUID错误")
    CONDITION_UUID_UNIQUE_ERROR  = ReturnCode(1785, "条件< {name} >UUID重复")
    CONDITION_TYPE_ERROR         = ReturnCode(1784, "条件< {name} >错误")
    RC_DEFAULT_ERROR             = ReturnCode(1783, "规则引擎校验错误")
    RC_UUID_ERROR                = ReturnCode(1782, "规则引擎< {name} >UUID错误")
    RC_UUID_UNIQUE_ERROR         = ReturnCode(1781, "规则引擎< {name} >UUID重复")
    WF_DEFAULT_ERROR             = ReturnCode(1780, "工作流校验错误")
    WF_UUID_ERROR                = ReturnCode(1779, "工作流< {name} >UUID错误")
    WF_UUID_UNIQUE_ERROR         = ReturnCode(1778, "工作流< {name} >UUID重复")
    NODE_UUID_ERROR              = ReturnCode(1777, "节点< {name} >UUID错误")
    NODE_UUID_UNIQUE_ERROR       = ReturnCode(1776, "节点< {name} >UUID重复")
    WF_MESSAGE_UUID_ERROR        = ReturnCode(1775, "节点消息发送< {name} >UUID错误")
    WF_MESSAGE_UUID_UNIQUE_ERROR = ReturnCode(1774, "节点消息发送< {name} >UUID重复")
    WF_TIMEOUT_UUID_ERROR        = ReturnCode(1773, "人工节点超时规则< {name} >UUID错误")
    WF_TIMEOUT_UUID_UNIQUE_ERROR = ReturnCode(1772, "人工节点超时规则< {name} >UUID重复")
    WF_LINE_UUID_ERROR           = ReturnCode(1771, "节点线< {name} >UUID错误")
    WF_LINE_UUID_UNIQUE_ERROR    = ReturnCode(1770, "节点线< {name} >UUID重复")
    AF_UUID_ERROR                = ReturnCode(1769, "审批流< {name} >UUID错误")
    AF_UUID_UNIQUE_ERROR         = ReturnCode(1768, "审批流< {name} >UUID重复")

    SM_NAME_FAILED               = ReturnCode(1600, IDECode.TITLE_NAME.format(name="状态机名称"), "通用")
    SM_NAME_NOT_UNIQUE           = ReturnCode(1601, "状态机名称需在< 模块 >中唯一")
    SM_MULTI_INSTANCE_INCORRECT  = ReturnCode(1602, "状态机多实例设置错误")
    SM_STRICT_MODE_INCORRECT     = ReturnCode(1603, "状态机严格模式设置错误")
    SM_START_STATE_MUST_ONLY_ONE = ReturnCode(1604, "状态机开始状态有且只能有一个")

    STATE_NAME_FAILED                         = ReturnCode(1620, IDECode.TITLE_CHINESE_NAME.format(name="状态名称"))
    STATE_NAME_NOT_UNIQUE                     = ReturnCode(1621, "状态名称需在< 状态机 >中唯一")
    STATE_TYPE_NOT_SUPPORT                    = ReturnCode(1622, "状态类型不支持")
    STATE_MANUAL_HANDLER_LIST_IS_NULL         = ReturnCode(1623, "人工状态处理人不存在", "设置")
    STATE_MANUAL_HANDLER_TYPE_NOT_SUPPORT     = ReturnCode(1624, "人工状态处理人类型不支持", "设置")
    STATE_MANUAL_HANDLER_ROLE_NOT_EXISTS      = ReturnCode(1625, "人工状态处理人角色不存在", "设置")
    STATE_MANUAL_HANDLE_TYPE_NOT_SUPPORT      = ReturnCode(1626, "人工状态多处理人规则不支持", "设置")
    STATE_MANUAL_HANDLE_CONDITION_NOT_SUPPORT = ReturnCode(1627, "人工状态多处理人跳转条件不支持", "设置")
    STATE_MANUAL_HANDLE_VALUE_INCURRECT       = ReturnCode(1628, "人工状态多处理人跳转条件值不合理", "设置")
    STATE_MANUAL_PAGE_NOT_EXISTS              = ReturnCode(1629, "人工状态表单页面不存在", "设置")
    STATE_START_NAME_MODIFY_FAILED            = ReturnCode(1630, "开始状态名称< 开始 >不允许修改")
    STATE_END_NAME_MODIFY_FAILED              = ReturnCode(1631, "结束状态名称< 结束 >不允许修改")
    STATE_CHILD_RECOVERY_INCURRECT            = ReturnCode(1632, "子状态恢复退出状态设置不合理")
    STATE_CHILD_STATE_MACHINE_INCURRECT       = ReturnCode(1633, "子状态机格式错误")
    STATE_CHILD_STATE_MACHINE_NOT_EXISTS      = ReturnCode(1634, "子状态机在应用中不存在")

    EVENT_NAME_FAILED           = ReturnCode(1640, IDECode.TITLE_NAME.format(name="事件名称"), "事件")
    EVENT_NAME_NOT_UNIQUE       = ReturnCode(1641, "事件名称需在< 状态机 >中唯一", "事件")
    EVENT_TYPE_NOT_SUPPORT      = ReturnCode(1648, "事件类型不支持", "事件")
    EVENT_ARG_NAME_FAILED       = ReturnCode(1642, IDECode.TITLE_NAME.format(name="事件参数名称"), "事件")
    EVENT_ARG_NAME_NOT_UNIQUE   = ReturnCode(1643, "事件参数名称需在< 参数列表 >中唯一", "事件")
    EVENT_ARG_TYPE_NOT_SUPPORT  = ReturnCode(1644, "事件参数类型不支持", "事件")
    EVENT_ARG_DEFAULT_INCURRECT = ReturnCode(1645, "事件参数默认值不合理", "事件")
    EVENT_ARG_MODEL_NOT_EXISTS  = ReturnCode(1646, "事件参数所选数据模型不存在", "事件")
    EVENT_ARG_ENUM_NOT_EXISTS   = ReturnCode(1647, "事件参数所选枚举类型不存在", "事件")

    VARIABLE_NAME_FAILED      = ReturnCode(1660, IDECode.TITLE_CHINESE_NAME.format(name="变量名称"))
    VARIABLE_NAME_NOT_UNIQUE  = ReturnCode(1661, "变量名称需在< 状态机 >中唯一")
    VARIABLE_TYPE_NOT_SUPPORT = ReturnCode(1662, "变量类型不支持")
    VARIABLE_MODEL_NOT_EXISTS = ReturnCode(1663, "变量所选数据模型不存在")
    VARIABLE_ENUM_NOT_EXISTS  = ReturnCode(1664, "变量所选枚举类型不存在")

    TIMER_NAME_FAILED                     = ReturnCode(1680, IDECode.TITLE_NAME.format(name="定时器名称"))
    TIMER_NAME_NOT_UNIQUE                 = ReturnCode(1681, "定时器名称需在< 状态机 >中唯一")
    TIMER_TYPE_NOT_SUPPORT                = ReturnCode(1682, "定时器类型不支持")
    TIMER_START_TIMESTAMP_NOT_SUPPORT     = ReturnCode(1683, "定时器开始时间未选择")
    TIMER_START_TIMESTAMP_VALUE_INCURRECT = ReturnCode(1684, "定时器开始时间设置不合理")
    TIMER_INTERVAL_TYPE_NOT_SUPPORT       = ReturnCode(1684, "间隔性定时器未选择间隔类型")
    TIMER_INTERVAL_VALUE_INCURRECT        = ReturnCode(1685, "间隔性定时器未选择间隔")
    TIMER_PERIOD_TYPE_NOT_SUPPORT         = ReturnCode(1686, "周期性定时器重复类型未选择")
    TIMER_PERIOD_VALUE_INCURRECT          = ReturnCode(1687, "周期性定时器未选择重复类型对应的值")
    TIMER_PERIOD_VALUE_WEEK_INCURRECT     = ReturnCode(1688, "周期性定时器< 周 >重复值不合理")
    TIMER_PERIOD_VALUE_MONTH_INCURRECT    = ReturnCode(1689, "周期性定时器< 月 >重复值不合理")
    TIMER_EVENT_UUID_NOT_EXISTS           = ReturnCode(1690, "定时器绑定事件< {name} >不存在")

    TRIGGER_NAME_FAILED                    = ReturnCode(1700, IDECode.TITLE_NAME.format(name="触发器名称"))
    TRIGGER_NAME_NOT_UNIQUE                = ReturnCode(1701, "触发器名称需在< 状态 >中唯一")
    TRIGGER_TYPE_NOT_SUPPORT               = ReturnCode(1702, "触发器类型不支持")
    TRIGGER_EVENT_NOT_EXISTS               = ReturnCode(1703, "触发器所选事件不存在")
    TRIGGER_EVENT_ARG_NOT_EXISTS           = ReturnCode(1704, "触发器事件参数< {name} >不存在")
    TRIGGER_EVENT_BIND_VARIABLE_NOT_EXISTS = ReturnCode(1705, "触发器事件参数绑定变量不存在")
    TRIGGER_TIMER_NOT_EXISTS               = ReturnCode(1706, "触发器所选定时器不存在")

    ACTION_NAME_FAILED                       = ReturnCode(1720, IDECode.TITLE_NAME.format(name="动作名称"))
    ACTION_AUTO_INCURRECT                    = ReturnCode(1721, "自动动作设定不正确")
    ACTION_FUNC_NOT_EXISTS                   = ReturnCode(1722, "动作所选云函数不存在", "事件")
    ACTION_TYPE_NOT_SUPPORT                  = ReturnCode(1723, "动作类型不支持")
    ACTION_RUN_ASYNC_INCURRECT               = ReturnCode(1724, "动作异步执行设定不正确")
    ACTION_SYNC_TYPE_NOT_SUPPORT             = ReturnCode(1725, "异步同步点不支持")
    ACTION_RUN_DELAY_INCURRECT               = ReturnCode(1726, "动作延迟执行设定不正确")
    ACTION_DELAY_TIME_INCURRECT              = ReturnCode(1727, "动作延迟执行时间设定不合理")
    ACTION_DATA_BIND_ARG_INCURRECT           = ReturnCode(1728, "动作数据绑定参数< {name} >与云函数参数无法对应")
    ACTION_DATA_BIND_ARG_TYPE_NOT_SUPPORT    = ReturnCode(1729, "动作数据绑定参数< {name}>类型不支持")
    ACTION_DATA_BIND_RETURN_INCURRECT        = ReturnCode(1730, "动作数据绑定返回值< {name} >与云函数返回值无法对应")
    ACTION_DATA_BIND_RETURN_TYPE_NOT_SUPPORT = ReturnCode(1731, "动作数据绑定返回值< {name} >类型不支持")
    ACTION_DATA_BIND_VARIABLE_NOT_EXISTS     = ReturnCode(1732, "动作数据绑定返回值< {name} >所关联变量不存在")
    ACTION_FOR_LOOP_VARIABLE_NOT_EXISTS      = ReturnCode(1733, "For动作所选循环变量不存在")
    ACTION_IF_CONTROL_FLOW_FAILED            = ReturnCode(1735, "If动作< {control} >控制语法错误")
    ACTION_IF_CONTROL_FLOW_IF_LOST           = ReturnCode(1736, "If动作缺少if控制语法")

    TRANSITION_NAME_FAILED                       = ReturnCode(1740, IDECode.TITLE_NAME.format(name="转换名称"))
    TRANSITION_NAME_NOT_UNIQUE                   = ReturnCode(1741, "转换名称需在< 状态 >中唯一")
    TRANSITION_TO_SELF_INCURRECT                 = ReturnCode(1742, "自转换设置错误")
    TRANSITION_SKIP_ENTRY_EXIT_INCURRECT         = ReturnCode(1743, "自转换跳过进入退出动作设置错误")
    TRANSITION_RUN_DELAY_INCURRECT               = ReturnCode(1744, "转换延迟执行设定不正确")
    TRANSITION_DELAY_TIME_INCURRECT              = ReturnCode(1745, "转换延迟时间设定不合理")
    TRANSITION_CONDITION_NAME_FAILED             = ReturnCode(1746, IDECode.TITLE_NAME.format(name="转换条件名称"))
    TRANSITION_CONDITION_NAME_NOT_UNIQUE         = ReturnCode(1747, "转换条件名称需在< 转换 >中唯一")
    TRANSITION_CONDITION_TYPE_NOT_SUPPORT        = ReturnCode(1748, "转换条件类型不支持")
    TRANSITION_DEFAULT_CONDITION_LARGER_THEN_ONE = ReturnCode(1749, "转换默认条件大于一个")
    TRANSITION_TO_STATE_NOT_EXISTS               = ReturnCode(1750, "转换< {name} >所选目标状态不存在")
    TRANSITION_CONDITION_TO_STATE_NOT_EXISTS     = ReturnCode(1751, "转换条件< {name} - {c_name} >所选目标状态不存在")

    FUNC_DEFAULT_ERROR            = ReturnCode(1899, "云函数校验错误")
    FUNC_UUID_ERROR               = ReturnCode(1898, "云函数< {name} >UUID错误")
    FUNC_UUID_UNIQUE_ERROR        = ReturnCode(1897, "云函数< {name} >UUID重复")
    FUNC_ARG_UUID_ERROR           = ReturnCode(1896, "云函数参数< {name} >UUID错误")
    FUNC_ARG_UUID_UNIQUE_ERROR    = ReturnCode(1895, "云函数参数< {name} >UUID重复")
    FUNC_RETURN_UUID_ERROR        = ReturnCode(1894, "云函数返回值< {name} >UUID错误")
    FUNC_RETURN_UUID_UNIQUE_ERROR = ReturnCode(1893, "云函数返回值< {name} >UUID重复")

    VALUE_EDITER_TYPE_NOT_SUPPORT  = ReturnCode(1800, "值编辑器类型不支持")
    VALUE_TYPE_NOT_SUPPORT         = ReturnCode(1801, "值类型不支持")
    VALUE_AND_VALUE_TYPE_NOT_EQUAL = ReturnCode(1802, "值与值类型不匹配")
    PLACEHOLDER_NOT_EXIST          = ReturnCode(1803, "占位符序号:{name} 不存在", "设置")

    FUNC_ARGS_NAME_ERROR            = ReturnCode(1818, "云函数参数< {name} >名称重复", "参数列表")
    FUNC_RETURN_NAME_ERROR            = ReturnCode(1819, "云函数返回值< {name} >名称重复", "返回值列表")
    FUNC_NAME_FAILED                 = ReturnCode(1820, IDECode.TITLE_CHINESE_NAME.format(name="云函数名称"), "通用", stop_publish=True)
    FUNC_NAME_NOT_UNIQUE             = ReturnCode(1821, "云函数名称需在< 模块 >中唯一", "通用")
    FUNC_ICON_TYPE_NOT_SUPPORT       = ReturnCode(1822, "云函数图标类型不存在", "通用")
    FUNC_ICON_NOT_EXISTS             = ReturnCode(1823, "云函数图标不存在", "通用")
    FUNC_INSTALL_SM_TOOBOX_INCURRECT = ReturnCode(1824, "云函数安装状态机工具箱设定不合理", "动作")
    FUNC_SM_TOOL_NAME_FAILED         = ReturnCode(1825, "云函数状态机动作标题", "动作")
    FUNC_SM_TOOL_CATEGORY_FAILED     = ReturnCode(1826, IDECode.TITLE_NAME.format(name="状态机动作分类名称"), "动作")
    FUNC_SM_TOOL_CATEGORY_NOT_EXISTS = ReturnCode(1827, "云函数状态机动作分类", "动作")
    FUNC_ARG_NAME_FAILED             = ReturnCode(1828, IDECode.TITLE_NAME.format(name="云函数参数名称"), "参数列表")
    FUNC_ARG_NAME_NOT_UNIQUE         = ReturnCode(1829, "云函数参数名称需在< 参数列表 >中唯一", "参数列表")
    FUNC_ARG_TYPE_NOT_SUPPORT        = ReturnCode(1830, "云函数参数类型不支持", "参数列表")
    FUNC_ARG_MODEL_NOT_EXISTS        = ReturnCode(1831, "云函数所选数据模型不存在")
    FUNC_ARG_ENUM_NOT_EXISTS         = ReturnCode(1832, "云函数所选枚举类型不存在", "参数列表")
    FUNC_RETURN_NAME_FAILED          = ReturnCode(1833, IDECode.TITLE_NAME.format(name="云函数返回值名称"), "返回值列表")
    FUNC_RETURN_NAME_NOT_UNIQUE      = ReturnCode(1834, "云函数返回值名称需在< 返回值列表 >中唯一", "返回值列表")
    FUNC_RETURN_TYPE_NOT_SUUPORT     = ReturnCode(1835, "云函数返回值类型不支持", "返回值列表")
    FUNC_ARBITRARY_ARGS_NOT_SUPPORT  = ReturnCode(1836, "云函数仅支持确定数量的参数，暂不支持*args, **kwargs等参数形式", "")

    EVENT_MAX_COUNT_ERR     = ReturnCode(1850, "事件数量超出最大限制 <{count}>", "事件")
    EVENT_ORDER_ERR         = ReturnCode(1851, "同响应方式内,云函数应先于动作类顺序", "事件")
    EVENT_NO_WORKFLOW_ERR   = ReturnCode(1852, "所选流程文档不存在", "事件")
    PAGE_PROPS_ERROR        = ReturnCode(1853, "事件缺少页面参数", "事件")
    EVENT_START_WF_NEED_WF_UUID = ReturnCode(1854, "需选择需要发起的流程", "事件")
    PAGE_MODEL_DIFFERENT_WF_FROM_MODEL = ReturnCode(1855, "模型与工作流的< {name} >端发起页面不一致", "事件")
    PAGE_MODEL_DIFFERENT_AF_TO_MODEL = ReturnCode(1856, "模型与审批流的< {name} >端处理页面不一致", "事件")

    IMAGE_DEFAULT_ERROR     = ReturnCode(1999, "图片表校验错误")
    IMAGE_UUID_ERROR        = ReturnCode(1998, "图片< {name} >UUID错误")
    IMAGE_UUID_UNIQUE_ERROR = ReturnCode(1997, "图片< {name} >UUID重复")
    CONST_DEFAULT_ERROR     = ReturnCode(1996, "常量表校验错误")
    CONST_UUID_ERROR        = ReturnCode(1995, "常量< {name} >UUID错误")
    CONST_UUID_UNIQUE_ERROR = ReturnCode(1994, "常量< {name} >UUID重复")
    JSON_DEFAULT_ERROR      = ReturnCode(1993, "JSON表校验错误")
    JSON_UUID_ERROR         = ReturnCode(1992, "JSON< {name} >UUID错误")
    JSON_UUID_UNIQUE_ERROR  = ReturnCode(1991, "JSON< {name} >UUID重复")
    ENUM_DEFAULT_ERROR      = ReturnCode(1990, "枚举表校验错误")
    ENUM_UUID_ERROR         = ReturnCode(1989, "枚举< {name} >UUID错误")
    ENUM_UUID_UNIQUE_ERROR  = ReturnCode(1988, "枚举< {name} >UUID重复")
    ENUM_ITEM_UUID_ERROR         = ReturnCode(1988, "枚举< {name} >UUID错误")
    ENUM_ITEM_UUID_UNIQUE_ERROR  = ReturnCode(1987, "枚举< {name} >UUID重复")

    IMAGE_NAME_FAILED      = ReturnCode(1900, IDECode.TITLE_CHINESE_NAME.format(name="图片名称"), "通用")
    IMAGE_NAME_NOT_UNIQUE  = ReturnCode(1901, "图片名称< {name} >需在< 模块 >中唯一", "通用")
    IMAGE_TYPE_NOT_SUPPORT = ReturnCode(1902, "图片< {name} >类型不支持", "通用")
    IMAGE_MD5_INCURRECT    = ReturnCode(1903, "图片< {name} >哈希值校验失败", "通用")
    IMAGE_MD5_DIFF         = ReturnCode(1904, "图片< {name} >哈希值与数据库中不一致，请确认是否已修改", "通用")

    CONST_NAME_FAILED      = ReturnCode(1920, IDECode.TITLE_CHINESE_NAME.format(name="常量名称"))
    CONST_NAME_NOT_UNIQUE  = ReturnCode(1921, "常量名称< {name} >需在< 模块 >中唯一")
    CONST_TYPE_NOT_SUPPORT = ReturnCode(1922, "常量< {name} >类型不支持")
    CONST_VALUE_INCURRECT  = ReturnCode(1923, "常量< {name} >值错误")

    JSON_NAME_FAILED     = ReturnCode(1940, IDECode.TITLE_NAME.format(name="JSON名称"))
    JSON_NAME_NOT_UNIQUE = ReturnCode(1941, "JSON名称< {name} >需在< 模块 >中唯一")
    JSON_VALUE_INCURRECT = ReturnCode(1942, "JSON< {name} >值错误")

    ENUM_NAME_FAILED                = ReturnCode(1960, IDECode.TITLE_CHINESE_NAME.format(name="枚举名称"))
    ENUM_NAME_NOT_UNIQUE            = ReturnCode(1961, "枚举名称< {name} >需在< 模块 >中唯一")
    ENUM_VALUE_INCURRECT            = ReturnCode(1962, "枚举< {name} >值错误")
    ENUM_ITEM_NAME_FAILED           = ReturnCode(1963, IDECode.TITLE_CHINESE_NAME.format(name="枚举项名称"))
    ENUM_ITEM_NAME_NOT_UNIQUE       = ReturnCode(1964, "枚举项名称< {name} >需在< 枚举 >中唯一")
    ENUM_ITEM_ICON_TYPE_NOT_SUPPORT = ReturnCode(1965, "枚举项< {name} >图标类型不支持")
    ENUM_ITEM_ICON_NOT_EXISTS       = ReturnCode(1966, "枚举项< {name} >图标不存在")
    ENUM_ITEM_COLOR_INCURRECT       = ReturnCode(1967, "枚举项< {name} >颜色错误")
    ENUM_ITEM_VALUE_ERROR           = ReturnCode(1968, "枚举项< {name} >值错误")

    # TODO
    PAGE_DEFAULT_ERROR               = ReturnCode(2999, "页面校验错误")
    INPUT_UUID_ERROR                 = ReturnCode(2998, "输入框< {name} >UUID错误")
    INPUT_UUID_UNIQUE_ERROR          = ReturnCode(2997, "输入框< {name} >UUID重复")
    TEXTAREA_UUID_ERROR              = ReturnCode(2996, "多行文本框< {name} >UUID错误")
    TEXTAREA_UUID_UNIQUE_ERROR       = ReturnCode(2995, "多行文本框< {name} >UUID重复")
    RADIO_UUID_ERROR                 = ReturnCode(2994, "单选框< {name} >UUID错误")
    RADIO_UUID_UNIQUE_ERROR          = ReturnCode(2993, "单选框< {name} >UUID重复")
    CHECKBOX_UUID_ERROR              = ReturnCode(2992, "单选框< {name} >UUID错误")
    CHEBKBOX_UUID_UNIQUE_ERROR       = ReturnCode(2991, "单选框< {name} >UUID重复")
    SELECT_UUID_ERROR                = ReturnCode(2990, "单选框< {name} >UUID错误")
    SELECT_UUID_UNIQUE_ERROR         = ReturnCode(2989, "单选框< {name} >UUID重复")
    DATETIME_UUID_ERROR              = ReturnCode(2988, "单选框< {name} >UUID错误")
    DATETIME_UUID_UNIQUE_ERROR       = ReturnCode(2987, "单选框< {name} >UUID重复")
    R_SELECT_UUID_ERROR              = ReturnCode(2986, "关联下拉框< {name} >UUID错误")
    R_SELECT_UUID_UNIQUE_ERROR       = ReturnCode(2985, "关联下拉框< {name} >UUID重复")
    R_SELECT_POPUP_UUID_ERROR        = ReturnCode(2984, "关联弹窗< {name} >UUID错误")
    R_SELECT_POPUP_UUID_UNIQUE_ERROR = ReturnCode(2983, "关联弹窗< {name} >UUID重复")
    R_SELECT_TABLE_UUID_ERROR        = ReturnCode(2982, "关联选择子表< {name} >UUID错误")
    R_SELECT_TABLE_UUID_UNIQUE_ERROR = ReturnCode(2981, "关联选择子表< {name} >UUID重复")
    R_CREATE_TABLE_UUID_ERROR        = ReturnCode(2980, "关联填写子表< {name} >UUID错误")
    R_CREATE_TABLE_UUID_UNIQUE_ERROR = ReturnCode(2979, "关联填写子表< {name} >UUID重复")
    SLIDER_UUID_ERROR                = ReturnCode(2978, "滑动输入< {name} >UUID重复")
    SLIDER_UUID_UNIQUE_ERROR         = ReturnCode(2977, "滑动输入< {name} >UUID重复")
    SWITCH_UUID_ERROR                = ReturnCode(2976, "开关< {name} >UUID重复")
    SWITCH_UUID_UNIQUE_ERROR         = ReturnCode(2975, "开关< {name} >UUID重复")
    UPLOAD_FILE_UUID_ERROR           = ReturnCode(2974, "上传文件< {name} >UUID重复")
    UPLOAD_FILE_UUID_UNIQUE_ERROR    = ReturnCode(2973, "上传文件< {name} >UUID重复")
    UPLOAD_IMAGE_UUID_ERROR          = ReturnCode(2972, "上传图片< {name} >UUID重复")
    UPLOAD_IMAGE_UUID_UNIQUE_ERROR   = ReturnCode(2971, "上传图片< {name} >UUID重复")
    FORM_UUID_ERROR                  = ReturnCode(2970, "表单< {name} >UUID错误")
    FORM_UUID_UNIQUE_ERROR           = ReturnCode(2969, "表单< {name} >UUID重复")
    DATALIST_UUID_ERROR              = ReturnCode(2968, "数据列表< {name} >UUID错误")
    DATALIST_UUID_UNIQUE_ERROR       = ReturnCode(2967, "数据列表< {name} >UUID重复")
    GRIDROW_UUID_ERROR               = ReturnCode(2966, "栅格行< {name} >UUID错误")
    GRIDROW_UUID_UNIQUE_ERROR        = ReturnCode(2965, "栅格行< {name} >UUID重复")
    GRIDCOL_UUID_ERROR               = ReturnCode(2964, "栅格列< {name} >UUID错误")
    GRIDCOL_UUID_UNIQUE_ERROR        = ReturnCode(2963, "栅格列< {name} >UUID重复")
    GRID_UUID_ERROR                  = ReturnCode(2962, "栅格< {name} >UUID错误")
    GRID_UUID_UNIQUE_ERROR           = ReturnCode(2961, "栅格< {name} >UUID重复")
    
    SPLIT_PAGE_UUID_ERROR            = ReturnCode(2897, "分割< {name} >UUID错误")
    SPLIT_PAGE_UNIQUE_ERROR          = ReturnCode(2896, "分割< {name} >UUID重复")

    PAGE_UUID_ERROR                  = ReturnCode(2960, "页面< {name} >UUID错误")
    PAGE_UUID_UNIQUE_ERROR           = ReturnCode(2959, "页面< {name} >UUID重复")
    COLOR_UUID_ERROR                 = ReturnCode(2958, "色板< {name} >UUID错误")
    COLOR_UUID_UNIQUE_ERROR          = ReturnCode(2957, "色板< {name} >UUID重复")
    PRINT_UUID_ERROR                 = ReturnCode(2956, "打印模板< {name} >UUID错误")
    PRINT_UUID_UNIQUE_ERROR          = ReturnCode(2955, "打印模板< {name} >UUID重复")
    PRINT_TABLE_UUID_ERROR           = ReturnCode(2954, "打印表格< {name} >UUID错误")
    PRINT_TABLE_UUID_UNIQUE_ERROR    = ReturnCode(2953, "打印表格< {name} >UUID重复")
    PRINT_FORM_UUID_ERROR            = ReturnCode(2952, "打印表单< {name} >UUID错误")
    PRINT_FORM_UUID_UNIQUE_ERROR     = ReturnCode(2951, "打印表单< {name} >UUID重复")
    PRINT_CARDLIST_UUID_ERROR        = ReturnCode(2950, "打印卡片列表< {name} >UUID错误")
    PRINT_CARDLIST_UUID_UNIQUE_ERROR = ReturnCode(2949, "打印卡片列表< {name} >UUID重复")
    PRINT_DATALIST_UUID_ERROR        = ReturnCode(2948, "打印数据列表< {name} >UUID错误")
    PRINT_DATALIST_UUID_UNIQUE_ERROR = ReturnCode(2947, "打印数据列表< {name} >UUID重复")
    PRINT_TEXT_UUID_ERROR            = ReturnCode(2946, "打印文本< {name} >UUID重复")
    PRINT_TEXT_UUID_UNIQUE_ERROR     = ReturnCode(2945, "打印文本< {name} >UUID重复")
    PRINT_IMAGE_UUID_ERROR           = ReturnCode(2944, "打印图片< {name} >UUID重复")
    PRINT_IMAGE_UUID_UNIQUE_ERROR    = ReturnCode(2943, "打印图片< {name} >UUID重复")
    TABS_UUID_ERROR                  = ReturnCode(2942, "页签组< {name} >UUID错误")
    TABS_UUID_UNIQUE_ERROR           = ReturnCode(2941, "页签组< {name} >UUID重复")
    TAB_UUID_ERROR                   = ReturnCode(2893, "单个页签< {name} >UUID错误")
    TAB_UUID_UNIQUE_ERROR            = ReturnCode(2892, "单个页签< {name} >UUID重复")
    CONTAINER_UUID_ERROR             = ReturnCode(2940, "容器< {name} >UUID错误")
    CONTAINER_UUID_UNIQUE_ERROR      = ReturnCode(2939, "容器< {name} >UUID重复")
    CALENDAR_UUID_ERROR              = ReturnCode(2938, "日期容器< {name} >UUID错误")
    CALENDAR_UUID_UNIQUE_ERROR       = ReturnCode(2937, "日期容器< {name} >UUID重复")
    R_TREE_UUID_ERROR                = ReturnCode(2936, "关联树形< {name} >UUID错误")
    R_TREE_UUID_UNIQUE_ERROR         = ReturnCode(2935, "关联树形< {name} >UUID重复")
    R_CASCADE_UUID_ERROR             = ReturnCode(2934, "关联级联< {name} >UUID错误")
    R_CASCADE_UUID_UNIQUE_ERROR      = ReturnCode(2933, "关联级联< {name} >UUID重复")
    CARDLIST_UUID_ERROR              = ReturnCode(2932, "卡片列表< {name} >UUID错误")
    CARDLIST_UUID_UNIQUE_ERROR       = ReturnCode(2932, "卡片列表< {name} >UUID重复")
    BUTTON_UUID_ERROR                = ReturnCode(2931, "按钮< {name} >UUID错误")
    BUTTON_UUID_UNIQUE_ERROR         = ReturnCode(2930, "按钮< {name} >UUID重复")
    IMAGE_SHOW_UUID_ERROR            = ReturnCode(2929, "图片展示< {name} >UUID错误")
    IMAGE_SHOW_UUID_UNIQUE_ERROR     = ReturnCode(2928, "图片展示< {name} >UUID重复")
    COLLAPSE_UUID_ERROR              = ReturnCode(2927, "折叠面板< {name} >UUID错误")
    COLLAPSE_UUID_UNIQUE_ERROR       = ReturnCode(2926, "折叠面板< {name} >UUID重复")
    PANEL_UUID_ERROR                 = ReturnCode(2895, "面板< {name} >UUID错误")
    PANEL_UUID_UNIQUE_ERROR          = ReturnCode(2894, "面板< {name} >UUID重复")
    TREE_UUID_ERROR                  = ReturnCode(2925, "树< {name} >UUID错误")
    TREE_UUID_UNIQUE_ERROR           = ReturnCode(2924, "树< {name} >UUID重复")
    TREELIST_UUID_ERROR              = ReturnCode(2923, "电子表格< {name} >UUID错误")
    TREELIST_UUID_UNIQUE_ERROR       = ReturnCode(2922, "电子表格< {name} >UUID重复")
    DATAGRID_UUID_ERROR              = ReturnCode(2921, "电子表格< {name} >UUID错误")
    DATAGRID_UUID_UNIQUE_ERROR       = ReturnCode(2920, "电子表格< {name} >UUID重复")
    R_TILE_UUID_ERROR                = ReturnCode(2919, "关联平铺< {name} >UUID错误")
    R_TILE_UUID_UNIQUE_ERROR         = ReturnCode(2918, "关联平铺< {name} >UUID重复")
    CUSTOM_UUID_ERROR = ReturnCode(2917, "自定义组件< {name} >UUID错误")
    CUSTOM_UUID_UNIQUE_ERROR = ReturnCode(2916, "自定义组件< {name} >UUID重复")
    TRANSFER_UUID_ERROR              = ReturnCode(2915, "关联下拉框< {name} >UUID错误")
    TRANSFER_UUID_UNIQUE_ERROR       = ReturnCode(2914, "关联下拉框< {name} >UUID重复")
    TRANSFER_NAME_FAILED             = ReturnCode(2913, IDECode.TITLE_NAME.format(name="穿梭框控件名称"), "通用")
    TRANSFER_NAME_NOT_UNIQUE         = ReturnCode(2912, "穿梭框控件名称< {name} >需在< 模块 >中唯一", "通用")

    LABEL_PRINT_UUID_ERROR                       = ReturnCode(2915, "标签打印< {name} >UUID错误")
    LABEL_PRINT_UUID_UNIQUE_ERROR                = ReturnCode(2914, "标签打印< {name} >UUID重复")
    LABEL_PRINT_RECT_BOX_UUID_ERROR              = ReturnCode(2913, "标签打印矩形框< {name} >UUID错误")
    LABEL_PRINT_RECT_BOX_UUID_UNIQUE_ERROR       = ReturnCode(2912, "标签打印矩形框< {name} >UUID重复")
    LABEL_PRINT_STRAIGHT_LINE_UUID_ERROR         = ReturnCode(2911, "标签打印直线< {name} >UUID错误")
    LABEL_PRINT_STRAIGHT_LINE_UUID_UNIQUE_ERROR  = ReturnCode(2910, "标签打印直线< {name} >UUID重复")
    LABEL_PRINT_TEXT_UUID_ERROR                  = ReturnCode(2909, "标签打印文字< {name} >UUID错误")
    LABEL_PRINT_TEXT_UUID_UNIQUE_ERROR           = ReturnCode(2908, "标签打印文字< {name} >UUID重复")
    LABEL_PRINT_MULTILINE_TEXT_UUID_ERROR        = ReturnCode(2907, "标签打印多行文字< {name} >UUID错误")
    LABEL_PRINT_MULTILINE_TEXT_UUID_UNIQUE_ERROR = ReturnCode(2906, "标签打印多行文字< {name} >UUID错误")
    LABEL_PRINT_BAR_CODE_UUID_ERROR              = ReturnCode(2905, "标签打印条形码< {name} >UUID错误")
    LABEL_PRINT_BAR_CODE_UUID_UNIQUE_ERROR       = ReturnCode(2904, "标签打印条形码< {name} >UUID错误")
    LABEL_PRINT_QR_CODE_UUID_ERROR               = ReturnCode(2903, "标签打印二维码< {name} >UUID错误")
    LABEL_PRINT_QR_CODE_UUID_UNIQUE_ERROR        = ReturnCode(2902, "标签打印二维码< {name} >UUID错误")
    LABEL_PRINT_IMAGE_UUID_ERROR                 = ReturnCode(2901, "标签打印图片< {name} >UUID错误")
    LABEL_PRINT_IMAGE_UUID_UNIQUE_ERROR          = ReturnCode(2900, "标签打印图片< {name} >UUID错误")
    LABEL_PRINT_NAME_FAILED                      = ReturnCode(2899, IDECode.TITLE_NAME.format(name="标签打印名称"), "通用")
    LABEL_PRINT_NAME_NOT_UNIQUE                  = ReturnCode(2898, "标签打印名称< {name} >需在< 模块 >中唯一", "通用")

    INPUT_NAME_FAILED     = ReturnCode(2000, IDECode.TITLE_NAME.format(name="输入框控件名称"), "通用")
    INPUT_NAME_NOT_UNIQUE = ReturnCode(2001, "输入框控件名称< {name} >需在< 模块 >中唯一", "通用")
    INPUT_NAME_NUMVER_ATTRIBUTE_SETTING_ERROR = ReturnCode(2002, "所设置的最大值小于最小值", "常规")

    TEXTAREA_NAME_FAILED     = ReturnCode(2005, IDECode.TITLE_NAME.format(name="多行文本控件名称"), "通用")
    TEXTAREA_NAME_NOT_UNIQUE = ReturnCode(2006, "多行文本控件名称< {name} >需在< 模块 >中唯一", "通用")

    RADIO_NAME_FAILED     = ReturnCode(2010, IDECode.TITLE_NAME.format(name="单选框控件名称"), "通用")
    RADIO_NAME_NOT_UNIQUE = ReturnCode(2011, "单选框控件名称< {name} >需在< 模块 >中唯一", "通用")

    CHECKBOX_NAME_FAILED     = ReturnCode(2015, IDECode.TITLE_NAME.format(name="复选框控件名称"), "通用")
    CHECKBOX_NAME_NOT_UNIQUE = ReturnCode(2016, "复选框控件名称< {name} >需在< 模块 >中唯一", "通用")

    SELECT_NAME_FAILED     = ReturnCode(2020, IDECode.TITLE_NAME.format(name="下拉框控件名称"), "通用")
    SELECT_NAME_NOT_UNIQUE = ReturnCode(2021, "下拉框控件名称< {name} >需在< 模块 >中唯一", "通用")
    SELECT_PREPROCESSING_DEIT_NOT_EXISTS = ReturnCode(2022, "候选项预处理值编辑器未绑定", "通用")
    DATETIME_NAME_FAILED     = ReturnCode(2025, IDECode.TITLE_NAME.format(name="日期时间下拉框控件名称"), "通用")
    DATETIME_NAME_NOT_UNIQUE = ReturnCode(2026, "日期时间下拉框控件名称< {name} >需在< 模块 >中唯一", "通用")

    R_SELECT_NAME_FAILED     = ReturnCode(2027, IDECode.TITLE_NAME.format(name="关联下拉框控件名称"), "通用")
    R_SELECT_NAME_NOT_UNIQUE = ReturnCode(2028, "关联下拉框控件名称< {name} >需在< 模块 >中唯一", "通用")
    R_SELECT_NEW_PAGE_LOST = ReturnCode(2029, "未选择新建页面", "常规")
    R_SELECT_NEW_PAGE_IS_DELETE = ReturnCode(2030, "新建页面已被删除", "常规")
    R_SELECT_VIEW_PAGE_LOST = ReturnCode(2031, "未选择查看页面", "常规")
    R_SELECT_VIEW_PAGE_IS_DELETE = ReturnCode(2032, "查看页面已被删除", "常规")
    R_SELECT_POPUP_PATH_NOT_SELECT = ReturnCode(2033, "关联弹窗数据源未选择字段", "数据源")
    R_SELECT_POPUP_NAME_FAILED     = ReturnCode(2034, IDECode.TITLE_NAME.format(name="关联弹窗控件名称"), "通用")
    R_SELECT_POPUP_NAME_NOT_UNIQUE = ReturnCode(2035, "关联弹窗控件名称< {name} >需在< 模块 >中唯一", "通用")
    R_SELECT_POPUP_VIEW_PAGE_LOST = ReturnCode(2036, "未选择查看页面", "常规")
    R_SELECT_POPUP_VIEW_PAGE_IS_DELETE = ReturnCode(2037, "查看页面已被删除", "常规")
    R_SELECT_POPUP_MODAL_PAGE_LOST = ReturnCode(2038, "未选择弹窗页面", "常规")
    R_SELECT_POPUP_FIELD_NOT_SELECT = ReturnCode(2039 ,"未选择弹窗字段", "常规")
    R_SELECT_POPUP_MODAL_PAGE_IS_DELETE = ReturnCode(2043, "弹窗页面已被删除", "常规")
    
    R_SELECT_TABLE_NAME_FAILED     = ReturnCode(2040, IDECode.TITLE_NAME.format(name="关联选择子表控件名称"), "通用")
    R_SELECT_TABLE_NAME_NOT_UNIQUE = ReturnCode(2041, "关联选择子表控件名称< {name} >需在< 模块 >中唯一", "通用")
    R_SELECT_CANDIDATES_FUNC_ARG_ERROR = ReturnCode(2042, "所选云函数应包含2个参数", "数据源")

    R_CREATE_TABLE_NAME_FAILED     = ReturnCode(2045, IDECode.TITLE_NAME.format(name="关联填写子表控件名称"), "通用")
    R_CREATE_TABLE_NAME_NOT_UNIQUE = ReturnCode(2046, "关联填写子表控件名称< {name} >需在< 模块 >中唯一", "通用")
    
    SLIDER_NAME_FAILED     = ReturnCode(2050, IDECode.TITLE_NAME.format(name="滑动输入控件名称"), "通用")
    SLIDER_NAME_NOT_UNIQUE = ReturnCode(2051, "滑动输入控件名称< {name} >需在< 模块 >中唯一", "通用")

    SLIDER_NOT_DEFAULT_VALUE = ReturnCode(2052, "滑动输入控件< {name} >无默认值", "通用")
    SLIDER_NOT_DEFAULT_VALUE_TOO_LARGE = ReturnCode(2053, "滑动输入控件< {name} >默认值大于最大值", "通用")
    SLIDER_NOT_DEFAULT_VALUE_TOO_SMALL = ReturnCode(2054, "滑动输入控件< {name} >默认值小于最小值", "通用")

    SWITCH_NAME_FAILED     = ReturnCode(2055, IDECode.TITLE_NAME.format(name="开关控件名称"), "通用")
    SWITCH_NAME_NOT_UNIQUE = ReturnCode(2056, "开关控件名称< {name} >需在< 模块 >中唯一", "通用")

    UPLOAD_FILE_NAME_FAILED     = ReturnCode(2060, IDECode.TITLE_NAME.format(name="上传文件控件名称"), "通用")
    UPLOAD_FILE_NAME_NOT_UNIQUE = ReturnCode(2061, "上传文件控件名称< {name} >需在< 模块 >中唯一", "通用")

    UPLOAD_IMAGE_NAME_FAILED     = ReturnCode(2065, IDECode.TITLE_NAME.format(name="上传图片控件名称"), "通用")
    UPLOAD_IMAGE_NAME_NOT_UNIQUE = ReturnCode(2066, "上传图片控件名称< {name} >需在< 模块 >中唯一", "通用")

    COLOR_NAME_FAILED     = ReturnCode(2070, IDECode.TITLE_NAME.format(name="色板控件名称"), "通用")
    COLOR_NAME_NOT_UNIQUE = ReturnCode(2071, "色板控件名称< {name} >需在< 模块 >中唯一", "通用")

    CALENDAR_NAME_FAIELD           = ReturnCode(2075, IDECode.TITLE_NAME.format(name="日期容器组件名称"), "通用")
    CALENDAR_NAME_NOT_UNIQUE       = ReturnCode(2076, "日期容器组件名称< {name} >需在< 模块 >中唯一", "通用")
    CALENDAR_DATE_FIELD_NOT_SET    = ReturnCode(2077, "日期字段未设置", "数据源")
    CALENDAR_DATE_FIELD_TYPE_ERROR = ReturnCode(2078, "日期字段需选择{name}类型", "数据源")
    CALENDAR_DATE_FIELD_NOT_EXISTS = ReturnCode(2079, "所选日期字段不存在", "数据源")

    R_TREE_NAME_FAILED        = ReturnCode(2080, IDECode.TITLE_NAME.format(name="关联树形控件名称"), "通用")
    R_TREE_NAME_NOT_UNIQUE    = ReturnCode(2081, "关联树形控件名称< {name} >需在< 模块 >中唯一", "通用")
    R_TREE_RELATION_NOT_FOUND = ReturnCode(2082, "所选自关联不存在", "数据源")

    R_CASCADE_NAME_FAILED     = ReturnCode(2085, IDECode.TITLE_NAME.format(name="关联级联控件名称"), "通用")
    R_CASCADE_NAME_NOT_UNIQUE = ReturnCode(2086, "关联级联控件名称< {name} >需在< 模块 >中唯一", "通用")

    FORM_IS_PAGINATED_PAGE_FORM_COUNT_ERROR = ReturnCode(2099, "开启表单翻页功能的页面,最外层只能有一个表单", "常规")
    FORM_NAME_FAILED     = ReturnCode(2100, IDECode.TITLE_NAME.format(name="表单名称"), "通用")
    FORM_NAME_NOT_UNIQUE = ReturnCode(2101, "表单名称< {name} >需在< 模块 >中唯一", "通用")

    R_TILE_NAME_FAILED     = ReturnCode(2102, IDECode.TITLE_NAME.format(name="关联平铺控件名称"), "通用")
    R_TILE_NAME_NOT_UNIQUE = ReturnCode(2103, "关联平铺控件名称< {name} >需在< 模块 >中唯一", "通用")

    CUSTOM_NAME_FAILED = ReturnCode(2106, IDECode.TITLE_NAME.format(name="自定义组件"), "通用")
    CUSTOM_NAME_NOT_UNIQUE = ReturnCode(2107, "自定义组件< {name} >需在< 模块 >中唯一", "通用")

    DATALIST_NAME_FAILED                       = ReturnCode(2110, IDECode.TITLE_NAME.format(name="数据列表名称"), "通用")
    DATALIST_NAME_NOT_UNIQUE                   = ReturnCode(2111, "数据列表名称< {name} >需在< 模块 >中唯一", "名称")
    DATALIST_SUBTABLE_ERROR                    = ReturnCode(2112, "子表< {name} >不支持功能栏", "数据源")
    DATALIST_COLUMN_INPUT_CONTROL_ERROR        = ReturnCode(2113, "列编辑字段类型与输入控件不匹配", "常规")
    
    DATALIST_COLUMN_IMAGE_EDIT_ERROR     = ReturnCode(2109, "列的图片组件仅图片字段支持编辑", "常规")
    DATALIST_COLUMN_FIELD_EDIT_ERROR     = ReturnCode(2108, "列的文件组件仅文件字段支持编辑", "常规")
    DATALIST_COLUMN_USLOAD_EXTENSION_ERROR = ReturnCode(2105, "列的上传组件的扩展名不能为空", "常规")

    DATALIST_COLUMN_NO_EDITABLE_COLUMN         = ReturnCode(2114, "未选择编辑字段", "常规")
    # DATALIST_NEW_BUTTON_PAGE_NOT_EXISTS        = ReturnCode(2115, "新建按钮页面不存在", "设置")
    DATALIST_NEW_BUTTON_PAGE_NOT_EXISTS        = ReturnCode(2115, "新建按钮选择的页面已被删除", "设置")
    DATALIST_NEW_BUTTON_PAGE_FORM_COUNT_ERROR  = ReturnCode(2116, "新建页面只能有1个最外层表单", "设置")
    DATALIST_NEW_BUTTON_PAGE_FORM_MODEL_ERROR  = ReturnCode(2117, "新建按钮页面的表单模型与数据容器模型不一致", "设置")
    DATALIST_EDIT_BUTTON_PAGE_NOT_EXISTS       = ReturnCode(2118, "编辑按钮页面不存在", "设置")
    DATALIST_EDIT_BUTTON_PAGE_FORM_COUNT_ERROR = ReturnCode(2119, "编辑页面只能有1个最外层表单", "设置")
    DATALIST_EDIT_BUTTON_PAGE_FORM_MODEL_ERROR = ReturnCode(2120, "编辑按钮页面的表单模型与数据列表模型不一致", "设置")
    DATALIST_NEW_BUTTON_CANNOT_NEW_INLINE      = ReturnCode(2121, "所有列不可编辑时，无法首行或末行新建", "设置")
    DATALIST_COLUMN_INPUT_CONTROL_IS_RADIO     = ReturnCode(2122, "列的输入控件不能为单选框", "常规")
    DATALIST_NEW_BUTTON_PAGE_NOT_SELECT        = ReturnCode(2123, "新建按钮未选择页面", "设置")
    DATALIST_EDIT_BUTTON_PAGE_NOT_SELECT       = ReturnCode(2124, "编辑按钮未选择页面", "设置")
    DATALIST_SUBTABLE_DATASOURCE_ERROR         = ReturnCode(2125, "子表< {name} >数据源类型只能选择'关联'", "数据源")
    DATALIST_DATASOURCE_ERROR                  = ReturnCode(2126, "< {name} >数据源类型不可以选择'关联'", "数据源")
    DATALIST_PREPROCESS_GROUP_BY_UNSELECT      = ReturnCode(2127, "数据源预处理未添加分组结果条件筛选", "数据源")
    DATALIST_COMPONENT_TYPE_ERROR              = ReturnCode(2130, "列组件类型与字段类型不匹配", "数据源")
    DATALIST_CALC_INLINE_EDIT_ERROR            = ReturnCode(2131, "计算字段不允许行内编辑", "数据源")
    DATALIST_REL_INLINE_EDIT_ERROR             = ReturnCode(2132, "关联表字段不允许使用非关联输入组件进行行内编辑", "数据源")
    DATASOURCE_DOESNOT_EXIST                   = ReturnCode(2215, "{name}数据模型未绑定", "数据源")
    DATASOURCE_RELATION_NOT_EXIST              = ReturnCode(2216, "{name}关联未绑定", "数据源")
    DATASOURCE_COMPONENT_NOT_EXIST             = ReturnCode(2217, "{name}组件未选择", "数据源")
    DATALIST_SEARCH_ITEM_FIELD_TYPE_ERROR      = ReturnCode(2218, "搜索项字段类型不能为图片,文件或日期循环", "常规")
    DATALIST_SEARCH_ITEM_FIELD_TYPE_ERROR_CALC = ReturnCode(2219, "搜索项字段不能为计算字段", "常规")
    DATASOURCE_PREPROCESSING_PATH_NOT_FOUND    = ReturnCode(2220, "{name}关联路径约束所选路径不存在", "数据源")
    SUBTABLE_PATH_NOT_FOUND                    = ReturnCode(2221, "子表所选路径不存在", "数据源")
    DATALIST_EXTERNAL_BUTTON_PC_PAGE_NOT_EXISTS= ReturnCode(2222, "外部填写按钮PC版绑定页面不存在", "设置")
    DATALIST_EXTERNAL_BUTTON_PAGE_NOT_EXISTS   = ReturnCode(2223, "外部填写按钮手机版绑定页面不存在", "设置")
    DATALIST_EXTERNAL_BUTTON_ROLE_NOT_BIND     = ReturnCode(2224, "外部填写按钮未绑定角色", "设置")
    DATALIST_COLUMN_EDITABLE_IS_FIELD_EDITOR   = ReturnCode(2225, "未在数据容器列的可编辑，值编辑器中不能选择字段", "常规")
    DATALIST_COLUMN_TITLE_IS_FIELD_EDITOR      = ReturnCode(2225, "未在数据容器列的标题，值编辑器中不能选择字段", "常规")
    FUNC_NOT_EXIST                             = ReturnCode(2227, "未选择云函数", "事件")
    FUNC_IS_DELETED                            = ReturnCode(2228, "所选择的云函数已被删除", "{position}")
    PAGE_REFRESH_GAP_NOT_SET                   = ReturnCode(2229, "页面表单刷新频率未设置", "刷新设置")
    LABEL_PRINT_MODEL_DIFFERENT                = ReturnCode(2230, "与标签打印模型不一致", "事件")
    DATALIST_INLINE_EDIT_PAGE_NOT_EXISTS       = ReturnCode(2118, "行编辑页面不存在", "行")
    DATALIST_INLINE_EDIT_PAGE_FORM_COUNT_ERROR = ReturnCode(2119, "行编辑页面只能有1个最外层表单", "行")
    DATALIST_INLINE_EDIT_PAGE_FORM_MODEL_ERROR = ReturnCode(2120, "行编辑页面的表单模型与列表/表格模型不一致", "行")
    DATALIST_INLINE_EDIT_PAGE_NOT_SELECT       = ReturnCode(2124, "未选择行编辑页面", "行")
    DATALIST_EDIT_BUTTON_CANNOT_EDIT_INLINE    = ReturnCode(2121, "所有列不可编辑时，无法行内编辑", "设置")
    CONTAINER_DATASOURCE_NOT_MULTIPLE          = ReturnCode(2133, "同一父组件下的子组件选择关联<{association_name}>相同时，数据源类型不能同时存在数据库存储和内存存储", "数据源")
    DATALIST_SELECT_PAGE_NOT_SELECT            = ReturnCode(2134, "数据列表选择页面未选择", "数据源")
    CARDIST_SELECT_PAGE_NOT_SELECT             = ReturnCode(2135, "卡片列表选择页面未选择", "数据源")
    R_SELECT_POPUP_SELECT_PAGE_NOT_SELECT      = ReturnCode(2136, "关联弹窗选择页面未选择", "常规")
    SELECT_VIEW_PAGE_IS_DELETE                  = ReturnCode(2137, "查看页面已被删除", "常规")
    DATALIST_CARDLIST_VIEW_PAGE_IS_DELETE                  = ReturnCode(2140, "查看页面已被删除", "数据源")
    DATALIST_SELECT_PAGE_MODEL_ERROR           = ReturnCode(2138, "选择页面的数据列表模型与数据容器模型不一致", "数据源")
    CARDLIST_SELECT_PAGE_MODEL_ERROR           = ReturnCode(2139, "选择页面的卡片列表模型与数据容器模型不一致", "数据源")
    DATALIST_COLUMN_R_SELECT_NOT_CREATE_PAGE    = ReturnCode(2141, "列的关联下拉框组件未选择新建页面", "常规")
    DATALIST_COLUMN_R_SELECT_PAGE_FORM_MODEL_ERROR   = ReturnCode(2142, "列上的数据容器模型与新建页面表单模型不一致 ", "常规")
    VIEW_PAGE_CANNOT_JUMP          = ReturnCode(2143, "选择页面不支持跳转打开", "数据源")
    DATALIST_IMPORT_BUTTON_MODEL_ERROR  = ReturnCode(2144, "导入按钮导入文档的模型与数据容器模型不一致", "导入文档")
    DATALIST_EXPORT_BUTTON_MODEL_ERROR  = ReturnCode(2145, "导出按钮导出文档的模型与数据容器模型不一致", "导出文档")
    PAGINATION_PAGE_SIZE_TOO_MANY = ReturnCode(2146, "列表<{name}>分页候选项不能大于10", "分页")
    PAGINATION_PAGE_SIZE_TOO_LARGE = ReturnCode(2147, "列表<{name}>每页显示数量不能大于1000", "分页")
    DYNAMIC_DATA_FIELD_NOT_CHOOSE = ReturnCode(2148, "动态列数据字段未选择", "类型")
    DYNAMIC_DATA_SOURCE_FIELD_NOT_CHOOSE = ReturnCode(2149, "动态列数据源字段未选择", "类型")
    DYNAMIC_TITLE_FIELD_NOT_CHOOSE = ReturnCode(3401, "动态列标题字段未选择", "类型")
    DYNAMIC_MODEL_DIFFERENT = ReturnCode(3402, "动态列标题字段与数据源字段模型不一致", "类型")
    DYNAMIC_DATA_SOURCE_INVALID = ReturnCode(3403, "动态列指定方式不合法", "类型")
    DYNAMIC_DATA_SOURCE_FIELD_INVALID = ReturnCode(3404, "动态列数据源字段不支持非生成列的计算字段", "类型")
    DYNAMIC_MAPPING_KEY_NOT_CHOOSE = ReturnCode(3405, "动态列映射键未设置", "类型")
    DYNAMIC_FIELD_SAME = ReturnCode(3406, "动态列数据源字段与标题字段不能相同", "类型")

    CARDLIST_NAME_FAILED = ReturnCode(2128, IDECode.TITLE_NAME.format(name="卡片列表名称"), "通用")
    CARDLIST_NAME_NOT_UNIQUE = ReturnCode(2129, IDECode.TITLE_NAME.format(name="卡片列表名称"), "通用")
    FORM_IN_CARDLIST_NOT_ALLOW = ReturnCode(2130, "卡片列表中不支持嵌入表单", "通用")

    GRIDROW_NAME_FAILED     = ReturnCode(2150, IDECode.TITLE_NAME.format(name="栅格行名称"), "通用")
    GRIDROW_NAME_NOT_UNIQUE = ReturnCode(2151, "栅格行名称< {name} >需在< 模块 >中唯一", "通用")

    GRIDCOL_NAME_FAILED     = ReturnCode(2155, IDECode.TITLE_NAME.format(name="栅格列名称"), "通用")
    GRIDCOL_NAME_NOT_UNIQUE = ReturnCode(2156, "栅格列名称< {name} >需在< 模块 >中唯一", "通用")

    GRID_NAME_FAILED     = ReturnCode(2160, IDECode.TITLE_NAME.format(name="栅格名称"), "通用")
    GRID_NAME_NOT_UNIQUE = ReturnCode(2161, "栅格名称< {name} >需在< 模块 >中唯一", "通用")
    SPLIT_PAGE_NAME_FAILED     = ReturnCode(2162, IDECode.TITLE_NAME.format(name="分割组件名称"), "通用")
    SPLIT_PAGE_NAME_NOT_UNIQUE = ReturnCode(2163, "分割组件名称< {name} >需在< 模块 >中唯一", "通用")

    TABS_NAME_FAILED     = ReturnCode(2165, IDECode.TITLE_NAME.format(name="页签组名称"), "通用")
    TABS_NAME_NOT_UNIQUE = ReturnCode(2166, "页签组名称< {name} >需在< 模块 >中唯一", "通用")
    TAB_NAME_FAILED     = ReturnCode(2167, IDECode.TITLE_NAME.format(name="单个页签名称"), "通用")
    TAB_NAME_NOT_UNIQUE = ReturnCode(2168, "单个页签名称< {name} >需在< 模块 >中唯一", "通用")

    CONTAINER_NAME_FAILED     = ReturnCode(2170, IDECode.TITLE_NAME.format(name="容器名称"), "通用")
    CONTAINER_NAME_NOT_UNIQUE = ReturnCode(2171, "容器名称< {name} >需在< 模块 >中唯一", "通用")

    COLLAPSE_NAME_FAILED     = ReturnCode(2175, IDECode.TITLE_NAME.format(name="折叠面板名称"), "通用")
    COLLAPSE_NAME_NOT_UNIQUE = ReturnCode(2176, "折叠面板名称< {name} >需在< 模块 >中唯一", "通用")
    PANEL_NAME_FAILED        = ReturnCode(2177, IDECode.TITLE_NAME.format(name="面板名称"), "通用")
    PANEL_NAME_NOT_UNIQUE    = ReturnCode(2178, "面板名称< {name} >需在< 模块 >中唯一", "通用")

    PAGE_NAME_FAILED       = ReturnCode(2180, IDECode.TITLE_NAME.format(name="页面名称"), "通用")
    PAGE_NAME_NOT_UNIQUE   = ReturnCode(2181, "页面名称< {name} >需在< 模块 >中唯一", "通用")
    PAGE_HAS_INPUT_CONTROL = ReturnCode(2182, "表单输入组件需在数据容器下", "数据源")
    PAGE_URL_NOT_UNIQUE    = ReturnCode(2183, "页面自定义路径重复", "自定义路径")
    PAGE_URL_IS_NULL       = ReturnCode(2184, "页面自定义路径为空", "自定义路径")
    PAGE_URL_INVALID       = ReturnCode(2185, "页面自定义路径不合法", "自定义路径")

    CUSTOM_RESTFUL_API_ERROR = ReturnCode(4000, "RESTful API 未选择")
    CUSTOM_RESTFUL_API_VERSION_NOT_FOUND = ReturnCode(4001, "RESTful API 版本不存在")
    CUSTOM_RESTFUL_API_RESOURCE_FOUND = ReturnCode(4002, "RESTful API 资源不存在")
    CUSTOM_RESTFUL_API_METHOD_FOUND = ReturnCode(4003, "RESTful API 请求方式不存在")
    CUSTOM_RESTFUL_API_AUTH_ERROR = ReturnCode(4004, "RESTful API 认证类型不能选择 基本认证")
    CUSTOM_RESTFUL_PAGE_ERROR = ReturnCode(4000, "页面未选择")
    CUSTOM_RESTFUL_PAGE_NOT_FOUND = ReturnCode(4000, "页面不存在")

    MODULE_ROLE_UUID_ERROR        = ReturnCode(2200, "模块角色< {name} >UUID错误")
    MODULE_ROLE_UUID_UNIQUE_ERROR = ReturnCode(2201, "模块角色< {name} >UUID重复")
    MODULE_ROLE_NAME_ERROR        = ReturnCode(2202, IDECode.TITLE_CHINESE_NAME.format(name="模块角色名称"), "通用")
    MODULE_ROLE_NAME_UNIQUE_ERROR = ReturnCode(2203, "模块角色名称< {name} >需在< 模块安全 >中唯一", "通用")
    MODULE_ROLE_NAME_BANNED = ReturnCode(2204, "模块角色名称禁用< {name} >", "通用")

    MODEL_NOT_EXISTS_IN_THIS_MODULE   = ReturnCode(2231, "数据模型< {name} >不存在")
    PAGE_NOT_EXISTS_IN_THIS_MODULE    = ReturnCode(2232, "页面< {name} >不存在")
    FUNC_NOT_EXISTS_IN_THIS_MODULE    = ReturnCode(2233, "云函数< {name} >不存在")
    FIELD_NOT_EXISTS_IN_THIS_MODEL    = ReturnCode(2234, "字段< {name} >不存在")
    FIELD_PERMISSION_FORMAT_ERROR     = ReturnCode(2235, "字段< {name} >权限格式错误")
    RELATION_NOT_EXISTS_IN_THIS_MODEL = ReturnCode(2236, "关联< {name} >不存在")
    RELATION_PERMISSION_FORMAT_ERROR  = ReturnCode(2237, "关联< {name} >权限格式错误")

    PERMISSION_FORMAT_ERROR = ReturnCode(2250, "权限格式错误")
    PERMISSION_UPDATE_NO_VIEW = ReturnCode(2251, "字段与关联权限设置中,勾选编辑权限时需同时勾选查看权限")

    RC_NAME_FAILED               = ReturnCode(2300, IDECode.TITLE_NAME.format(name="规则引擎名称"))
    RC_NAME_NOT_UNIQUE           = ReturnCode(2301, "规则引擎名称需在< 模块 >中唯一")
    RC_SYSTEM_SERVICE_INCORRECT  = ReturnCode(2302, "规则引擎系统服务设置错误")

    WF_FROM_PAGE_SYS_MODEL       = ReturnCode(2310, "工作流发起页面表单数据模型不能为系统表")
    WF_TO_PAGE_SYS_MODEL         = ReturnCode(2311, "工作流处理页面表单数据模型不能为系统表")
    WF_NAME_FAILED               = ReturnCode(2400, IDECode.TITLE_NAME.format(name="工作流名称"), "通用")
    WF_NAME_NOT_UNIQUE           = ReturnCode(2401, "工作流名称需在< 模块 >中唯一", "通用")
    WF_ONLINE_VERSION_ERROR      = ReturnCode(2402, "版本设置错误", "版本")
    WF_VERSION_NOT_UNIQUE        = ReturnCode(2403, "版本需在< 工作流 >中唯一", "版本")
    WF_FROM_PAGE_NOT_FOUND       = ReturnCode(2404, "< 发起页面 >不存在", "设置")
    WF_TO_PAGE_NOT_FOUND         = ReturnCode(2405, "< 处理页面 >不存在", "设置")
    WF_START_NODE_INCURRECT      = ReturnCode(2406, "有且只能有一个开始节点")
    WF_END_NODE_INCURRECT        = ReturnCode(2407, "有且只能有一个结束节点")
    WF_ONLINE_VERSION_THAN_ONE   = ReturnCode(2408, "只能设置一个上线版本", "版本")
    WF_FROM_PAGE_NEED_ONE_FORM   = ReturnCode(2409, "< 发起页面 >有且只能有一个最外层表单组件", "设置")
    WF_TO_PAGE_NEED_ONE_FORM     = ReturnCode(2410, "< 处理页面 >有且只能有一个最外层表单组件", "设置")
    WF_FROM_PAGE_FORM_MODEL_DIFF = ReturnCode(2411, "不同设备的< 发起页面 >，需保证表单的数据模型一致", "设置")
    WF_TO_PAGE_FORM_MODEL_DIFF   = ReturnCode(2412, "不同设备的< 处理页面 >，需保证表单的数据模型一致", "设置")
    AF_NAME_FAILED               = ReturnCode(2413, IDECode.TITLE_NAME.format(name="审批流名称"), "通用")
    AF_NAME_NOT_UNIQUE           = ReturnCode(2414, "审批流名称需在< 模块 >中唯一", "通用")
    PARALLEL_NAME_FAILED         = ReturnCode(2415, IDECode.TITLE_CHINESE_NAME.format(name="分支名称"), "通用")
    PARALLEL_NAME_NOT_UNIQUE     = ReturnCode(2416, "分支名称需在< 模块 >中唯一", "通用")
    WF_TO_COPY_PAGE_FORM_MODEL_DIFF   = ReturnCode(2417, "不同设备的< 抄送页面 >，需保证表单的数据模型一致", "设置")
    WF_TO_COPY_PAGE_NEED_ONE_FORM     = ReturnCode(2418, "< 抄送页面 >有且只能有一个最外层表单组件", "设置")
    WF_VARIABLE_NAME_NOT_UNIQUE       = ReturnCode(2419, "工作流变量名称需在工作流中唯一", "设置")

    NODE_NAME_FAILED                     = ReturnCode(2420, IDECode.TITLE_CHINESE_NAME.format(name="节点名称"), "通用")
    NODE_NAME_NOT_UNIQUE                 = ReturnCode(2421, "节点名称需在< 工作流 >中唯一", "通用")
    START_NODE_LINE_INCURRECT            = ReturnCode(2422, "开始节点线有且只能有一条")
    END_NODE_LINE_INCURRECT              = ReturnCode(2423, "结束节点无法绘线")
    AUTO_NODE_FUNC_NOT_FOUND             = ReturnCode(2424, "动作所选云函数不存在", "设置")
    AUTO_NODE_FUNC_ARG_LARGE             = ReturnCode(2425, "动作参数列表长度大于云函数参数列表", "设置")
    AUTO_NODE_FUNC_RETURN_LARGE          = ReturnCode(2426, "动作返回列表长度大于云函数返回列表", "设置")
    AUTO_NODE_FUNC_RESULT_VARIABLE_ERROR = ReturnCode(2427, "云函数结果变量值编辑器所选变量，不在工作流变量列表中", "设置")
    CONDITION_NAME_NOT_UNIQUE            = ReturnCode(2428, "节点下转换条件名称不能重复", "通用")
    NODE_NAME_NOT_UNIQUE_IN_AF           = ReturnCode(2429, "节点名称需在< 审批流 >中唯一", "通用")
    TIMER_START_TIMESTAMP_NOT_EXIST      = ReturnCode(2430, "定时设置开始时间未设置", "定时设置")
    TIMER_NEXT_NODE_PARALLEL             = ReturnCode(2431, "定时工作流发起后,不支持自动流转到并行节点<{node_name}>", "定时设置")
    TIMER_NEXT_NODE_INVALID              = ReturnCode(2432, "定时工作流发起后,自动流转到的抄送节点{node_name1}或人工节点{node_name2}的操作人不能为运行时指定", "定时设置")
    TIMER_RETURN_NODE_START              = ReturnCode(2433, "定时工作流发起后,不支持设计时回退到开始节点<{node_name}>", "回退设置")
    TENANT_START_FIELD_NOT_CHOOSE        = ReturnCode(2434, "租户开启定时器字段未选择", "定时设置")
    TENANT_START_FIELD_NOT_EXIST         = ReturnCode(2435, "租户开启定时器字段不存在", "定时设置")
    TENANT_START_FIELD_TYPE_ERROR        = ReturnCode(2436, "租户开启定时器字段类型需要是布尔类型", "定时设置")

    NODE_MESSAGE_NAME_FAILED     = ReturnCode(2440, IDECode.TITLE_CHINESE_NAME.format(name="节点消息发送名称"), "消息发送")
    NODE_MESSAGE_NAME_NOT_UNIQUE = ReturnCode(2441, "节点消息发送名称需在< 节点 >中唯一", "消息发送")
    MESSAGE_TYPE_NOT_SUPPORT     = ReturnCode(2442, "节点消息发送< {name} >的发送时机类型不支持", "消息发送")
    NODE_MESSAGE_MODEL_NOT_CHOOSE     = ReturnCode(2443, "<{name}>数据模型未选择", "消息发送")
    NODE_MESSAGE_MODEL_NOT_EXIST     = ReturnCode(2444, "<{name}>数据模型不存在", "消息发送")
    NODE_MESSAGE_FIELD_NOT_CHOOSE     = ReturnCode(2445, "<{name}>字段未选择", "消息发送")
    NODE_MESSAGE_FIELD_NOT_EXIST     = ReturnCode(2446, "<{name}>字段不存在", "消息发送")
    NODE_MESSAGE_FIELD_TYPE_ERROR     = ReturnCode(2447, "<{name}>字段类型不正确", "消息发送")
    NODE_MESSAGE_CONTENT_NOT_EXIST     = ReturnCode(2448, "消息内容未填写", "消息发送")
    NODE_MESSAGE_FIELD_NOT_RELATION     = ReturnCode(2449, "<{name}>关联不存在或关联的映射关系已改变", "消息发送")
    NODE_SUBFLOW_NOT_CHOOSE = ReturnCode(2450, "子流程未选择", "子流程设置")
    NODE_SUBFLOW_NOT_EXIST = ReturnCode(2451, "子流程不存在", "子流程设置")
    NDOE_SUBFLOW_PK_NOT_SET = ReturnCode(2452, "子流程主键未设置", "子流程设置")

    NODE_HANDLER_ROLE_NOT_SELECTED = ReturnCode(2458, "处理人所选角色<{name}>未被应用安全中的角色选择", "设置")
    NODE_HANDLER_EXPR_NOT_EXIST = ReturnCode(2459, "处理人值编辑器表达式未配置", "设置")
    NODE_HANDLER_TYPE_NOT_SUPPORT        = ReturnCode(2460, "处理人类型不支持", "设置")
    NODE_HANDLER_ROLE_NOT_FOUND          = ReturnCode(2461, "处理人所选角色组不存在", "设置")
    NODE_HANDLER_MANAGER_ERROR           = ReturnCode(2462, "处理人主管级别须大于等于 1", "设置")
    NODE_HANDLE_TYPE_NOT_SUPPORT         = ReturnCode(2463, "处理人规则不支持", "设置")
    NODE_TO_PAGE_NOT_FOUND               = ReturnCode(2464, "处理页面不存在", "设置")
    NODE_ACTION_DEFAULT_RULE_NOT_SUPPORT = ReturnCode(2465, "处理规则不支持", "处理动作")
    NODE_ACTION_ACCEPT_UUID_ERROR        = ReturnCode(2466, "同意动作设置错误", "处理动作")
    NODE_ACTION_REJECT_UUID_ERROR        = ReturnCode(2467, "拒绝动作设置错误", "处理动作")
    NODE_ACTION_RULE_TYPE_NOT_SUPPORT    = ReturnCode(2468, "处理动作< {name} >跳转类型不支持", "处理动作")
    NODE_ACTION_RULE_VALUE_ERROR         = ReturnCode(2469, "处理动作< {name} >跳转值设置错误", "处理动作")
    NODE_HANDLER_NOT_EXISTS              = ReturnCode(2470, "处理人为空", "设置")
    NODE_ACTIONS_IS_NULL                 = ReturnCode(2471, "必须指定一个系统或自定义动作", "处理动作")
    NODE_HANDLER_NO_LIMIT_ROLES          = ReturnCode(2472, "限制指定角色不支持为空", "设置")
    NODE_AUTO_PROCESS_TYPE_INVALID       = ReturnCode(2473, "自动处理规则不合法", "设置")
    NODE_AUTO_PROCESS_CONFLICT_COMMENT   = ReturnCode(2474, "自动处理开启时不支持填写审批意见", "设置")
    NODE_RUNTIME_HANDLER_CONFLICT_LAST_NODE   = ReturnCode(2475, "运行时指定处理人时, 前置节点<{name}>不可以为支持自动通过的节点", "设置")
    RETURN_TO_NODE_NOT_EXIST   = ReturnCode(2476, "回退后返回节点不存在", "回退设置")
    RETURN_TO_NODE_NOT_SET   = ReturnCode(2477, "回退后返回节点未选择", "设置")
    TIMER_WORKFLOW_HANDLER_CREATOR_ONLY = ReturnCode(2478, "定时发起的工作流, 节点处理人不能仅包含发起人相关", "设置")
    AUTO_NODE_ROLE_NOT_IN_APP_ROLE = ReturnCode(2479, "模块角色未被应用安全中的角色选择", "设置")

    NODE_TIMEOUT_NAME_FAILED       = ReturnCode(2480, IDECode.TITLE_CHINESE_NAME.format(name="超时规则名称"), "超时规则")
    NODE_TIMEOUT_NAME_NOT_UNIQUE   = ReturnCode(2481, "超时规则名称需在< 节点 >中唯一", "超时规则")
    NODE_TIMEOUT_TYPE_NOT_SUPPORT  = ReturnCode(2482, "超时规则指定动作不支持", "超时规则")
    NODE_TIMEOUT_DAYS_LESS         = ReturnCode(2483, "超时时间设定（天）需大于等于 0", "超时规则")
    NODE_TIMEOUT_HOURS_LESS        = ReturnCode(2484, "超时时间设定（小时）需大于等于 0", "超时规则")
    NODE_TIMEOUT_HOURS_LARGE       = ReturnCode(2485, "超时时间设定（小时）需小于 24", "超时规则")
    NODE_TIMEOUT_TIME_ERROR        = ReturnCode(2486, "超时时间设定时长至少为一小时", "超时规则")
    NODE_URGING_DAYS_LESS          = ReturnCode(2487, "催办时间设定（天）需大于等于 0", "超时规则")
    NODE_URGING_HOURS_LESS         = ReturnCode(2488, "催办时间设定（小时）需大于等于 0", "超时规则")
    NODE_URGING_HOURS_LARGE        = ReturnCode(2489, "催办时间设定（小时）需小于 24", "超时规则")
    NODE_URGING_TIME_ERROR         = ReturnCode(2490, "催办时间设定周期间隔至少为一小时", "超时规则")
    NODE_URGING_LIMIT_ERROR        = ReturnCode(2491, "催办次数上限需大于等于 1", "超时规则")
    NODE_TIMEOUT_TO_NODE_NOT_FOUND = ReturnCode(2492, "超时跳转节点不存在", "超时规则")

    NODE_LINE_NAME_FAILED       = ReturnCode(2500, IDECode.TITLE_CHINESE_NAME.format(name="线名称"), "通用")
    NODE_LINE_NAME_NOT_UNIQUE   = ReturnCode(2501, "线名称需在< 节点 >中唯一", "通用")
    NODE_LINE_DEFAULT_ERROR     = ReturnCode(2502, "节点的所有线至多有一个默认条件", "设置")
    NODE_LINE_ACTION_NOT_FOUND  = ReturnCode(2503, "线所选的节点动作不存在", "设置")
    NODE_LINE_TO_NODE_NOT_FOUND = ReturnCode(2504, "线所连接的节点不存在", "设置")
    NODE_LINE_NOT_ALLOW_REJECT  = ReturnCode(2505, "节点上不允许额外的拒绝路线", "设置")
    NODE_LOOP                   = ReturnCode(2506, "工作流自动流转的节点成环", "设置")

    NODE_EVENT_NO_FUNC = ReturnCode(2520, "节点事件云函数未选择", "事件")
    AUTO_NODE_ROLE_NOT_SELECT = ReturnCode(2521, "自动节点使用模块角色权限未选择", "设置")
    AUTO_NODE_ROLE_NOT_FOUND = ReturnCode(2522, "自动节点使用模块角色权限存在已被删除的角色", "设置")
    NODE_TO_PAGE_FORM_MODEL_DIFF = ReturnCode(2524, "节点设备<处理页面>需保证与工作流<处理页面>表单的数据模型一致", "设置")
    NODE_TO_PAGE_SYS_MODEL = ReturnCode(2525, "节点处理页面表单数据模型不能为系统表", "设置")
    NODE_EVENT_ARG_NUMBER_ERROR = ReturnCode(2526, "节点事件的参数数量只能为2个", "{position}")

    PARALLEL_NODE_NO_BRANCH = ReturnCode(2540, "并行节点中需至少包含一个分支", "通用")
    PARALLEL_BRANCH_FINISH_AMOUNT_ZERO = ReturnCode(2541, "并行节点分支完成数不能为0", "设置")
    PARALLEL_BRANCH_FINISH_AMOUNT_TOO_MUCH = ReturnCode(2542, "并行节点分支完成数不能多于分支总数", "设置")
    PARALLEL_BRANCH_END_RULE_INVALID = ReturnCode(2543, "并行节点结束规则不合法", "设置")
    BUTTON_ICON_NOT_SET = ReturnCode(2544, "<{button_name}>按钮图标未设置", "通用")
    WF_FROM_PAGE_IS_DELETE = ReturnCode(2545, "工作流所选发起页面被删除", "设置")
    WF_TO_PAGE_IS_DELETE = ReturnCode(2546, "<{name}>工作流所选处理页面被删除", "设置")
    NODE_TO_PAGE_IS_DELETE = ReturnCode(2547, "<{name}>节点所选处理页面被删除", "设置")

    INPUT_FIELD_NOT_FOUND              = ReturnCode(2601, "输入控件未选择字段", "数据源")
    INPUT_FIELD_TYPE_ERROR             = ReturnCode(2602, "输入控件类型与字段类型不匹配", "数据源")
    INPUT_FIELD_AND_MODEL_NOT_EQUAL    = ReturnCode(2603, "输入控件所选字段的模型与容器模型不一致", "数据源")
    INPUT_FIELD_NOT_EXISTS             = ReturnCode(2604, "输入控件所选字段不存在", "数据源")
    RELATION_FIELD_NOT_FOUND           = ReturnCode(2605, "关联控件未选择字段", "数据源")
    RELATION_FIELD_AND_MODEL_EQUAL     = ReturnCode(2606, "关联控件所选字段的模型与容器模型一致", "数据源")
    RELATION_FIELD_NOT_EXISTS          = ReturnCode(2607, "关联控件所选字段不存在", "数据源")
    RELATION_PATH_NOT_FOUND            = ReturnCode(2608, "关联路径不存在", "数据源")
    RELATION_PATH_TOO_LARGE            = ReturnCode(2609, "需选择与父容器模型直接关联的模型", "数据源")
    RELATION_FIELD_NOT_HIERARCHY_FIELD = ReturnCode(2610, "关联字段不能是层级字段", "数据源")
    RELATION_ASSOCIATION_NOT_EXISTS    = ReturnCode(2611, "关联子表未选择关联模型", "数据源")
    FIELD_AGG_FUNC_ERR                 = ReturnCode(2612, "字段类型与聚合函数不匹配", "数据源")
    CALCULATE_FIELD_EDITABLE_FALSE     = ReturnCode(2613, "计算字段不允许设置为可编辑类型", "可编辑")
    CALCULATE_FIELD_NOT_SUPPORT        = ReturnCode(2614, "该组件不支持非生成列的计算字段", "数据源")
    FILTER_SORT_ONLY_NORMAL_FIELD      = ReturnCode(2615, "表头排序筛选操作不支持图片,文件,日期循环类型或非生成列的计算字段", "常规")
    FILTER_SORT_ONLY_FIELD_VALUE_EDITOR = ReturnCode(2625, "标签和按钮表头排序筛选操作仅支持文本为值编辑器的字段", "常规")
    FILTER_SORT_TOO_MANY_BUTTON        = ReturnCode(2626, "表头排序筛选操作不支持一列多按钮")
    FILTER_SORT_NOT_ASSOCIATION_MEMORY = ReturnCode(2627, "内存存储不支持表头排序筛选")
    GROUPBY_ONLY_NORMAL_FIELD          = ReturnCode(2616, "分组操作不支持图片,文件,日期循环类型或非生成列的计算字段", "分组")
    SEARCH_ITEM_ONLY_NORMAL_FIELD      = ReturnCode(2617, "搜索项不支持图片、文件及非生成列的计算字段", "常规")
    SEARCH_DATA_SOURCE_TYPE_NOT_FOUND  = ReturnCode(2618, "搜索项数据源类型不支持", "常规")
    SEARCH_SELF_REFERENTIAL_PATH_ERROR = ReturnCode(2619, "搜索项树形下拉框自关联路径不存在", "常规")
    OPERATOR_TYPE_NOT_ALLOWED          = ReturnCode(2620, "操作符选择与搜索项控件不匹配", "常规")
    PAGE_MONITOR_REPEAT                = ReturnCode(2621, "单个页面最多允许一个页面监听组件", "数据源")
    MULT_FIELD_REQUIRE_AGGRE_FUNC      = ReturnCode(2622, "多端字段需要聚合函数", "数据源")
    SEARCH_FIELD_NOT_EXISTS            = ReturnCode(2623, "{name}——未选择字段或所选字段不存在", "普通搜索")

    COMPONENT_REMOVED_PLEASE_dELETE    = ReturnCode(2624, "该组件已移除，请删除", "常规")

    PRINT_NAME_FAILED                 = ReturnCode(2700, IDECode.TITLE_NAME.format(name="打印模板名称"), "通用")
    PRINT_NAME_NOT_UNIQUE             = ReturnCode(2701, "打印模板名称< {name} >需在< 模块 >中唯一", "通用")
    PRINT_SIZE_ERROR                  = ReturnCode(2702, "打印纸张尺寸必填且宽高不能为0", "打印设置")
    PRINT_PAGE_WIDTH_LESS_THAN_MARGIN = ReturnCode(2703, "打印模板页面宽度小于左边距+右边距", "打印设置")
    PRINT_PAGE_WIDTH_ERROR            = ReturnCode(2704, "打印模板页面宽度设置错误", "打印设置")
    PRINT_PAGE_MARGINS_ERROR          = ReturnCode(2705, "打印模板页边距只能为不小于0的整数", "打印设置")

    PRINT_IMAGE_HIGTH_ERROR            = ReturnCode(2706, "打印模板图片高度超出设置", "打印设置")
    PRINT_IMAGE_WIDTH_ERROR            = ReturnCode(2707, "打印模板图片宽度超出设置", "打印设置")
    PRINT_HIGTH_ERROR            = ReturnCode(2708, "打印模板< {name} >高度超出设置", "打印设置")
    PRINT_WIDTH_ERROR            = ReturnCode(2709, "打印模板< {name} >宽度超出设置", "打印设置")


    PRINT_TABLE_NAME_FAILED = ReturnCode(2720, IDECode.TITLE_NAME.format(name="打印表格名称"), "通用")
    PRINT_TABLE_NAME_NOT_UNIQUE = ReturnCode(2721, "打印表格名称< {name} >需在< 打印模板 >中唯一", "通用")

    PRINT_FORM_FAILED_DOESNOT_EXIST = ReturnCode(2722, "打印模板< {name}>值编辑器绑定字段不存在", "数据源")
    PRINT_FORM_FAILED_UNBOUND = ReturnCode(2723, "打印模板< {name}>值编辑器未绑定", "数据源")
    PRINT_FORM_RELATION_PATH_NOT_FOUND = ReturnCode(2724, "打印模板< {name}>值编辑器绑定关联关系不存在", "数据源")
    PLACEHOLDER_SEQUENCE_NUMBER_DOESNOT_EXIST = ReturnCode(2725, "打印模板< {name}>表达式中引用序号多于占位符序号", "数据源")
    PRINT_DATASOURCE_DOESNOT_EXIST = ReturnCode(2726, "打印模板< {name}>模型未绑定", "数据源")

    PRINT_BUTTON_DATASOURCE_DOESNOT_EXIST = ReturnCode(2727, "打印模板< {name}>模型未绑定", "数据绑定")
    PRINT_BUTTON_DOCUMENT_NOT_EXISTS = ReturnCode(2728, "打印模板< {name}>绑定文档不存在", "数据绑定")

    PRINT_FORM_NAME_FAILED = ReturnCode(2740, IDECode.TITLE_NAME.format(name="打印表单名称"), "通用")
    PRINT_FORM_NAME_NOT_UNIQUE = ReturnCode(2741, "打印表单名称< {name} >需在< 打印模板 >中唯一", "通用")
    FORM_INPUT_COL_ERROR = ReturnCode(2742, "表单或表单输入组件的宽度超出限制", "输入布局")

    PRINT_CARDLIST_NAME_FAILED = ReturnCode(2760, IDECode.TITLE_NAME.format(name="打印数据列表名称"), "通用")
    PRINT_CARDLIST_NAME_NOT_UNIQUE = ReturnCode(2761, "打印数据列表名称< {name} >需在< 打印模板 >中唯一", "通用")

    PRINT_DATALIST_NAME_FAILED = ReturnCode(2780, IDECode.TITLE_NAME.format(name="打印卡片列表名称"), "通用")
    PRINT_DATALIST_NAME_NOT_UNIQUE = ReturnCode(2781, "打印卡片列表名称< {name} >需在< 打印模板 >中唯一", "通用")

    PRINT_TEXT_NAME_FAILED = ReturnCode(2800, IDECode.TITLE_NAME.format(name="打印文本名称"), "通用")
    PRINT_TEXT_NAME_NOT_UNIQUE = ReturnCode(2801, "打印文本名称< {name} >需在< 打印模板 >中唯一", "通用")

    PRINT_IMAGE_NAME_FAILED = ReturnCode(2820, IDECode.TITLE_NAME.format(name="打印图片名称"), "通用")
    PRINT_IMAGE_NAME_NOT_UNIQUE = ReturnCode(2821, "打印图片名称< {name} >需在< 打印模板 >中唯一", "通用")

    MULTI_SCAN_NO_FOCUSFAILED = ReturnCode(2831, "存在多个扫描组件时,至少需存在一个< 需要获取焦点 >", "设置")

    VALUE_FIELD_NOT_EXIST = ReturnCode(2849, "值编辑器占位符的值所选字段不存在", "数据源")
    VALUE_EDITOR_FIELD_NOT_EXIST = ReturnCode(2850, "值编辑器未配置", "数据源")
    IMAGE_NOT_EXIST = ReturnCode(2853, "图片值编辑器未选择", "数据源")
    CODE_NOT_EXIST = ReturnCode(2854, "图片数据源编辑器未选择", "数据源")
    FILE_FIELD_NOT_EXIST = ReturnCode(2855, "文件字段未绑定", "数据源")
    TIMELINE_FIELD_NOT_EXIST = ReturnCode(2852, "{name}{field_name}未选择字段", "数据源")
    PROGRESS_FIELD_NOT_EXIST = ReturnCode(2856, "{name}{field_name}值编辑器未填写", "数据源")
    IMAGE_TYPE_ERROR = ReturnCode(2857, "图片所选字段类型不匹配", "数据源")
    FILE_TYPE_ERROR = ReturnCode(2858, "文件所选字段类型不匹配", "数据源")
    SCAN_FIELD_REPEAT = ReturnCode(2859, "页面扫码计数写回字段重复", "数据源")
    SCAN_COUNT_FIELD_NOT_EXIST = ReturnCode(2860, "扫码计数组件未选择字段", "数据源")
    SCAN_FIELD_NOT_EXIST = ReturnCode(2861, "扫码组件未选择字段", "数据源")

    PUBLIST_ERROR_ENUM_NAME_ERROR = ReturnCode(1960, "枚举名称出错")

    TREE_MODEL_NOT_SELF_ASSOCIATION = ReturnCode(2857, "数据模型未包含自关联", "数据源")
    TREE_ASSOCIATION_OPERATE_ERROR = ReturnCode(2858, "名称为关联表字段, 禁用操作选项", "操作")
    SELF_ASSOCIATION_NOT_SELECT = ReturnCode(2859, "父节点关联未选择", "数据源")
    TREE_NAME_FIELD_NOT_SELECT = ReturnCode(2860, "{name}名称字段未选择", "数据源")
    TREE_NAME_FIELD_IS_MANY = ReturnCode(2861, "名称字段关联禁止穿过多端", "数据源")
    TREE_NEW_PAGE_NOT_SELECT = ReturnCode(2862, "树形组件新建页面未选择", "操作")
    TREE_EDIT_PAGE_NOT_SELECT = ReturnCode(2863, "树形组件编辑页面未选择", "操作")
    TREE_NEW_PAGE_IS_DELETE = ReturnCode(2864, "树形组件新建页面被删除", "操作")
    TREE_EDIT_PAGE_IS_DELETE = ReturnCode(2865, "树形组件编辑页面被删除", "操作")
    TREE_NEW_PAGE_FORM_MODEL_ERROR  = ReturnCode(2866, "树形组件新建页面的表单模型与数据容器模型不一致", "操作")
    TREE_EDIT_PAGE_FORM_MODEL_ERROR  = ReturnCode(2867, "树形组件编辑页面的表单模型与数据容器模型不一致", "操作")

    CONNECTOR_NAME_FAILED            = ReturnCode(3000, IDECode.TITLE_NAME.format(name="连接器名称"), "通用")
    CONNECTOR_NAME_NOT_UNIQUE        = ReturnCode(3001, "连接器名称< {name} >需在< 模块 >中唯一", "通用")
    CONNECTOR_UUID_UNIQUE_ERROR      = ReturnCode(3002, "连接器< {name} >UUID重复")
    EXCEL_TEMPLATE_NAME_FAILED       = ReturnCode(3003, IDECode.TITLE_NAME.format(name="导入模板名称"), "通用")
    EXCEL_TEMPLATE_NAME_NOT_UNIQUE   = ReturnCode(3004, "导入模板名称< {name} >需在< 页面 >中唯一", "通用")
    EXCEL_TEMPLATE_UUID_UNIQUE_ERROR = ReturnCode(3005, "导入模板< {name} >UUID重复")
    EXCEL_COLUMN_INDEX_ERROR         = ReturnCode(3006, "Excel字段列标有误")
    EXCEL_FIELD_NOT_EXIST            = ReturnCode(3007, "Excel列字段不存在")
    EXCEL_RELATIONSHIP_NOT_EXIST     = ReturnCode(3008, "Excel列关联表字段未选择路径")
    EXCEL_UNIQUE_COLUMN_ERROR        = ReturnCode(3009, "Excel同一模型上只支持设置一个唯一字段")
    EXCEL_FILE_FIELD_ERROR           = ReturnCode(3010, "Excel导入导出不支持文件字段")
    EXCEL_COLUMN_INDEX_REPEAT        = ReturnCode(3011, "Excel字段列标重复")
    RESTFUL_NAME_FAILED              = ReturnCode(3012, IDECode.TITLE_NAME.format(name="服务器API名称"), "通用")
    RESTFUL_NAME_NOT_UNIQUE          = ReturnCode(3013, "服务器API名称< {name} >需在< 模块 >中唯一", "通用")
    RESTFUL_UUID_UNIQUE_ERROR        = ReturnCode(3014, "服务器API< {name} >UUID重复")
    RESTFUL_CONTENT_ERROR            = ReturnCode(3015, "服务器API< {name} >内容格式异常")
    RESTFUL_ANONYMOUS_ROLE_ERROR     = ReturnCode(3016, "服务器API< {name} >匿名角色不存在")
    RESTFUL_ACCESS_ROLE_ERROR        = ReturnCode(3016, "服务器API< {name} >权限角色未选择")
    RESTFUL_ACCESS_FUNC_ERROR        = ReturnCode(3017, "服务器API< {name} >权限云函数未选择")
    RESTFUL_REQUEST_ERROR            = ReturnCode(3018, "服务器API< {name} >请求配置错误")
    RESTFUL_RESOUCE_EMPTY            = ReturnCode(3025, "服务器API< {name} >资源不能为空")
    RESTFUL_RESOUCE_START_ERROR      = ReturnCode(3026, "服务器API< {name} >/< {version} >资源必须以'/'开头")
    RESTFUL_RESOUCE_EXIST            = ReturnCode(3027, "服务器API< {name} >资源< {resources} >已存在")
    RESTFUL_REQUEST_FUNC_ERROR       = ReturnCode(3019, "服务器API< {name} >请求云函数未选择")
    RESTFUL_REQUEST_FUNC_ARG_ERROR       = ReturnCode(3020, "服务器API< {name} >请求云函数参数数量不匹配")
    RESTFUL_REQUEST_FUNC_RETURN_ERROR       = ReturnCode(3021, "服务器API< {name} >请求云函数返回值数量不匹配")
    RESTFUL_REQUEST_METHOD_ERROR     = ReturnCode(3022, "服务器API< {name} >请求方式错误")
    RESTFUL_REQUEST_METHOD_EXIST     = ReturnCode(3023, "服务器API< {name} >请求方式已存在")

    EXPORT_TEMPLATE_NAME_FAILED       = ReturnCode(3050, IDECode.TITLE_CHINESE_NAME.format(name="导出模板名称"), "通用")
    EXPORT_TEMPLATE_NAME_NOT_UNIQUE   = ReturnCode(3051, "导出模板名称< {name} >需在< 页面 >中唯一", "通用")
    EXPORT_TEMPLATE_UUID_UNIQUE_ERROR = ReturnCode(3052, "导出模板< {name} >UUID重复")
    EXCEL_IMPORT_METHOD_REPEAT        = ReturnCode(3053, "同一模型里不能同时有“新增或更新”和“仅查询”两种方式")

    DATAGRID_NAME_FAILED     = ReturnCode(3100, IDECode.TITLE_NAME.format(name="电子表格名称"), "通用")
    DATAGRID_NAME_NOT_UNIQUE = ReturnCode(3101, "电子表格名称< {name} >需在< 模块 >中唯一", "名称")
    SCAN_FIELD_NOT_FOUND              = ReturnCode(3201, "扫码组件未选择字段", "数据源")
    SCAN_MODEL_NOT_FOUND              = ReturnCode(3202, "扫码组件未选择模型", "数据源")
    SCAN_FIELD_AND_MODEL_NOT_EQUAL    = ReturnCode(3203, "扫码组件所选字段的模型与容器模型不一致", "数据源")
    SCAN_FIELD_NOT_EXISTS             = ReturnCode(3204, "扫码组件所选字段不存在", "数据源")
    SCAN_MODEL_NOT_EXISTS             = ReturnCode(3204, "扫码组件所选模型不存在", "数据源")


    VISUALIZATION_UUID_ERROR                         = ReturnCode(3500, "< {name} >UUID错误")
    VISUALIZATION_UUID_UNIQUE_ERROR                  = ReturnCode(3501, "< {name} >UUID重复")
    VISUALIZATION_AXLE_DOESNOT_EXIST                 = ReturnCode(3502, "{name}——轴未绑定", "数据源")
    VISUALIZATION_AXLE_FIELD_DOESNOT_EXIST           = ReturnCode(3503, "{name}——轴绑定的字段不存在", "数据源")
    VISUALIZATION_LEGENT_FIELD_DOESNOT_EXIST         = ReturnCode(3504, "{name}——图例字段未绑定", "数据源")
    VISUALIZATION_LEGENT_FIELD_UNBOUND               = ReturnCode(3505, "{name}——图例绑定的字段不存在", "数据源")
    VISUALIZATION_VALUE_UNBOUND                      = ReturnCode(3506, "{name}——值未绑定", "数据源")
    VISUALIZATION_VALUE_FIELD_DOESNOT_EXIST         = ReturnCode(3507, "{name}——值绑定的字段不存在", "数据源")
    VISUALIZATION_SHARED_AXIS_DOESNOT_EXIST          = ReturnCode(3508, "{name}——共享轴未绑定", "数据源")
    VISUALIZATION_SHARED_AXIS_FIELD_DOESNOT_EXIST    = ReturnCode(3509, "{name}——共享轴绑定的字段不存在", "数据源")
    VISUALIZATION_COLUMN_SERIES_FIELD_DOESNOT_EXIST  = ReturnCode(3510, "{name}——列图例绑定字段不存在", "数据源")
    VISUALIZATION_COLUMN_VALUES_DOESNOT_EXIST        = ReturnCode(3511, "{name}——列值未绑定", "数据源")
    VISUALIZATION_COLUMN_FIELD_DOESNOT_EXIST         = ReturnCode(3512, "{name}——列值绑定字段不存在", "数据源")
    VISUALIZATION_LINE_VALUES_DOESNOT_EXIST          = ReturnCode(3513, "{name}——行值未绑定", "数据源")
    VISUALIZATION_LINE_FIELD_DOESNOT_EXIST           = ReturnCode(3514, "{name}——行值绑定字段不存在", "数据源")
    VISUALIZATION_MAX_VALUES_DOESNOT_EXIST           = ReturnCode(3515, "{name}——最大值字段不存在", "数据源")
    VISUALIZATION_MIN_VALUES_DOESNOT_EXIST           = ReturnCode(3516, "{name}——最小值字段不存在", "数据源")
    VISUALIZATION_TARGET_VALUES_DOESNOT_EXIST        = ReturnCode(3517, "{name}——目标值字段不存在", "数据源")
    VISUALIZATION_MAX_VALUES_UNBOUND                 = ReturnCode(3518, "{name}——最大值未绑定", "数据源")
    VISUALIZATION_MIN_VALUES_UNBOUND                 = ReturnCode(3519, "{name}——最小值未绑定", "数据源")
    VISUALIZATION_TARGET_VALUES_UNBOUND              = ReturnCode(3520, "{name}——目标值未绑定", "数据源")
    VISUALIZATION_FIELD_DOESNOT_EXIST                = ReturnCode(3521, "{name}——未选择字段", "数据源")
    VISUALIZATION_FIELD_VALUES_DOESNOT_EXIST         = ReturnCode(3522, "{name}——选择字段不存在", "数据源")
    VISUALIZATION_SEARCH_TERM_FIELDS_UNBOUND         = ReturnCode(3523, "{name}——搜索项字段未绑定", "数据源")
    VISUALIZATION_SEARCH_TERM_FIELDS_DOESNOT_EXIST   = ReturnCode(3524, "{name}——搜索项字段不存在", "数据源")
    VISUALIZATION_SEARCH_RELATIONSHIP_DOESNOT_EXIST  = ReturnCode(3525, "{name}——搜索项字段关联关系不存在", "数据源")
    VISUALIZATION_Y_DOESNOT_EXIST                    = ReturnCode(3526, "{name}——Y 轴或辅助 Y 轴至少设置一个", "数据源")
    VISUALIZATION_AXIS_Y_DOESNOT_EXIST               = ReturnCode(3527, "{name}——Y 轴绑定字段不存在", "数据源")
    VISUALIZATION_SECONDARY_Y_DOESNOT_EXIST          = ReturnCode(3528, "{name}——辅助 Y 轴绑定字段不存在", "数据源")
    VISUALIZATION_VALUE_NAME_TOO_LONG                = ReturnCode(3529, "{name}——值显示名称长度不能超过 20 字", "数据源")
    VISUALIZATION_AXIS_Y_NAME_TOO_LONG          = ReturnCode(3531, "{name}——Y 轴显示名称长度不能超过 20 字", "数据源")
    VISUALIZATION_SECONDARY_Y_NAME_TOO_LONG          = ReturnCode(3531, "{name}——辅助 Y 轴显示名称长度不能超过 20 字", "数据源")
    # VISUALIZATION_FIELD_AND_MODEL_NOT_EQUAL    = ReturnCode(3550, "{name}组件所选字段的模型与容器模型不一致", "数据源")


    MODULE_THEME_SELECTED_DOESNOT_EXIST   = ReturnCode(3600, "所选主题已被删除")
    APP_LAYOUT_SELECTED_DOESNOT_EXIST   = ReturnCode(3601, "所选应用布局已被删除")

    WATERMARK_IMAGE_NOT_SET = ReturnCode(4100, "水印图片未设置")

    NAVIGATION_ICON_NOT_SET = ReturnCode(4200, "菜单<{0}>的图标未设置")
    NAVIGATIONITEM_ICON_NOT_SET = ReturnCode(4201, "导航项<{0}>的图标未设置")
    NAVIGATION_ITEM_NAME_ERROR = ReturnCode(4202, "导航项名称<{0}>长度不能超过40")


    REFERENCE_NOT_FOUND = ReturnCode(5404, "引用不存在")
    PYTHON_CODE_FORMAT_ERROR = ReturnCode(5405, "Python代码格式错误")
    
    TRANSFER_RIGHT_COLUMN_NOT_EXIST = ReturnCode(6000, "未选择右侧显示字段")

    PERMISSION_CONFIG_NOT_CHOOSE_TAG = ReturnCode(7000, "<{element_name}>权限资源未选择标签", "权限资源标签")
    PERMISSION_CONFIG_TAG_NOT_EXISTS = ReturnCode(7001, "<{element_name}>权限资源所选标签不存在", "权限资源标签")
    PERMISSION_CONFIG_ACTION_NOT_EXISTS = ReturnCode(7002, "<{element_name}>所选操作不存在", "权限资源标签")

    AGGRE_MULTICOLUMN_SAME_INDEX_ERROR = ReturnCode(8000, "汇总{name}：从第几列到第几列，数字不能相同", "多列合并汇总")
    AGGRE_MULTICOLUMN_ILLEGAL_ERROR = ReturnCode(8001, "汇总{name}：从第几列到第几列，右边数字必须大于左边数字", "多列合并汇总")
    AGGRE_MULTICOLUMN_MIN_INDEX_ERROR = ReturnCode(8002, "汇总{name}：从第几列到第几列，数字必须大于等于1", "多列合并汇总")
    AGGRE_MULTICOLUMN_MAX_INDEX_ERROR = ReturnCode(8003, "汇总{name}：数字必须不大于当前已有列数", "多列合并汇总")
    AGGRE_MULTICOLUMN_REPETITION_ERROR = ReturnCode(8004, "不能重复设置同一列汇总, 汇总{name1} 和 汇总{name2}", "多列合并汇总")
    AGGRE_MULTICOLUMN_COLUMN_NOT_EXIST = ReturnCode(8005, "汇总{name}：请选择用于计算的列", "多列合并汇总")
    AGGRE_MULTICOLUMN_FUNC_UNBOUND = ReturnCode(8006, "汇总{name}：请选择汇总函数", "多列合并汇总")
    AGGRE_MULTICOLUMN_PARAM_UNBOUND = ReturnCode(8007, "汇总{name}：云函数参数未配置", "多列合并汇总")
    AGGRE_MULTICOLUMN_PARAM_NOT_EXIST = ReturnCode(8008, "汇总{name}：云函数参数不存在", "多列合并汇总")
    AGGRE_COLUMN_PARAM_UNBOUND = ReturnCode(8009, "列汇总云函数参数未配置", "汇总")
    AGGRE_COLUMN_PARAM_NOT_EXIST = ReturnCode(8010, "列汇总云函数参数不存在", "汇总")
    AGGRE_COLUMN_FUNC_UNBOUND = ReturnCode(8011, "列汇总请选择汇总函数", "汇总")
    DATASET_DATASOURCE_DOESNOT_EXIST = ReturnCode(8012, "{name}数据集数据模型未绑定", "数据源")

    FIELD_AND_MODEL_NOT_EQUAL = ReturnCode(8020, "{name}所选字段的模型与容器模型不一致", "数据源")

    SORT_FIELD_NOT_SELECT = ReturnCode(8030, "排序字段未选择", "行")
    SORT_FIELD_NOT_EXISTS = ReturnCode(8031, "排序字段不存在", "行")
    SORT_FIELD_NOT_INTEGER_DECIMAL = ReturnCode(8032, "排序字段需是整型或小数类型", "行")

    DROPDOWN_MENU_UUID_ERROR        = ReturnCode(8040, "下拉菜单< {name} >UUID错误")
    DROPDOWN_MENU_UUID_UNIQUE_ERROR = ReturnCode(8041, "下拉菜单< {name} >UUID重复")
    DROPDOWN_MENU_NAME_FAILED       = ReturnCode(8042, IDECode.TITLE_NAME.format(name="下拉菜单名称"), "通用")
    DROPDOWN_MENU_NAME_NOT_UNIQUE   = ReturnCode(8043, "下拉菜单名称< {name} >需在< 模块 >中唯一", "通用")
    MENU_UUID_ERROR                 = ReturnCode(8044, "菜单< {name} >UUID错误")
    MENU_UUID_UNIQUE_ERROR          = ReturnCode(8045, "菜单< {name} >UUID重复")
    MENU_NAME_FAILED                = ReturnCode(8046, IDECode.TITLE_NAME.format(name="菜单名称"), "通用")
    MENU_NAME_NOT_UNIQUE            = ReturnCode(8047, "菜单名称< {name} >需在< 模块 >中唯一", "通用")


    UNIVER_VARIABLES_NAME_NOT_UNIQUE = ReturnCode(8088, "< {name} >变量名重复", "数据变量")

class PythonKeyWords(object):
    KWDICT = {name: name for name in kwlist}

class LemonKeyWords(object):
    KWLIST = ["image_table"]
    KWDICT = {name: name for name in KWLIST}

class PreprocessType(object):
    Normal = 0    # 数据库取数据
    PathLimit = 1  # 关联路径约束
    CloudFunction = 3  # 云函数 
    # 2不填写

class MysqlErrorDesc:
    code2desc = {
        1062: "<{}>数据违反数据模型字段唯一性约束",
        1427: "数据模型小数字段总长度设置应大于等于小数位长度设置"
    }


class FormEventType:

    INIT = "init"
    SAVE = "save"
    DELETE = "delete"
    CANCEL = "cancel"
    LOAD = "load"

    ALL = [INIT, SAVE, DELETE, CANCEL, LOAD]


class PageEventType:

    CREATE = "create"  # 创建
    DESTROY = "destroy"  # 销毁
    LOAD = "load"

    ALL = [CREATE, DESTROY, LOAD]


"""前端属性中英文对照"""
class AttiChn2Eng:
    # TODO 不考虑页面名称修改
    # 单纯修改名称可能性不大, 更大可能是修改结构, 目前无法应对这种情况
    AttiDic = {
    "常规": 'common',
    "参数列表": 'parameter_list',
    "返回值列表": 'return_list',
    "动作": 'action',
    "数据源": 'data_source',
    "事件": 'event',
    "约束设置": 'limit_setting',
    "样式": 'style',
    "可见性": 'visible',
    "设计区": 'design_area',
    "规则引擎": 'rule_engine',
    "标题": 'title',
    "列顺序与宽度": 'column_order_width',
    "排序字段": 'order_field',
    "行": 'row',
    "子表": 'sub_form',
    "分页": 'pagination',
    "分组": 'group',
    "汇总": 'gather',
    "设置": 'settings',
    "搜索设置": 'serarch_setting',
    "可编辑": 'editable',
    "图例": 'legend',
    "Y轴": 'y_axis',
    "X轴": 'x_axis',
    "数据颜色": 'data_color',
    "数据标签": 'data_label',
    "背景": 'background',
    "边框": 'frame',
    "横纵比": 'aspect_ratio',
    "工具提示": 'tool_point',
    "筛选": 'chosen',
    "通用": 'universal',
    "类别标签": 'category_label',
    "形状":'shape',
    "详细信息标签": 'details_tab',
    "选择器标头":'selector_header',
    "选择方式": 'options',
    "项目": 'project',
    "数值输入": 'numerical_input',
    "滑块": 'slider',
    "添加": 'add',
    "数据绑定": 'data_binding',
    "扫码解析": 'scan_code_analysis',
    "尺寸":'size',
    "列标题": 'col_title',
    "行标题": 'row_title',
    "值":'value',
    "行小计": 'row_subtotal',
    "列小计": 'col_subtotal',
    "总计": 'total',
    "字段格式设置": 'field_formatting',
    "条件格式": 'conditional_formatting',
    "文字格式": 'text_format',
    "卡片数": 'number_of_cards',
    "列宽度": 'col_width',
    "布局风格": 'layout_style',
    "打印设置": 'print_settings',
    "执行行为": 'executive_behavior',
    "消息发送": 'message_sending',
    "转换条件顺序": 'transition_condition_sequence',
    "表单组件设置": 'form_component_settings',
    "处理动作": 'processing_action',
    "超时规则": 'timeout_rule',
    "版本": 'version',
    "输入设置": "input_setting"
  }

class InputAdapter:
    tj_scale = [
        {
            "name": "串口名称",
            "key": "serial_ports",
            "editable": 1,   # 0：不可编辑，1：设计者可编辑， 2：拓展租户可以编辑
            "default": "com1",
            "value_type": 10
        },
        {
            "name": "读取单位",
            "key": "unit",
            "editable": 1,
            "default": "g",
            "value_type": 10
        }
    ]

    adapters = {
        "f3e7be2989b0573dbb7da3af59c11189": tj_scale
    }


class InputType:
    keyboard = 0
    external_adapter = 1


class TreeLoadType:

    WHOLE = 0  # 全部加载
    ONE_FLOOR = 1  # 单层加载


class ShowLevelType:  # 自关联展示的数据的显示

    ALL = 0  # 显示所有层级
    LAST = 1  # 仅显示最后一级
    NOT_FIRST = 2  # 不显示第一级（仅一级时无效）


class PermissionDeploy:

    NoPermissionFieldShow = {
        FieldType.IMAGE: [
            {
                "uid": "1",
                "url": "design/icon/permission_error.svg",
                "name": "permission_error.svg",
                "status": "done"
            }
        ],
        -1: "**"
    }


class ReturnHandleType:
    RUNTIME = 0
    DESIGN = 1


class ValueEditorVariable(DictWrapper):

    def __init__(self, uuid, name, expr=""):
        if not expr:
            expr = ".".join(["lemon", "system", uuid])
        super().__init__(**{"uuid": uuid, "name": name, "expr": expr})


class ValueEditorVariables:

    CURRENT_USER = ValueEditorVariable("user", "当前用户", "lemon.system.current_user")
    CURRENT_DEP = ValueEditorVariable("current_dep", "当前主部门")
    ROLES = ValueEditorVariable("roles", "角色列表")
    CONTEXT = ValueEditorVariable("context", "上下文对象")
    SQCODE = ValueEditorVariable("sqcode", "扫码结果")
    OBJ = ValueEditorVariable("obj", "模型对象")
    OBJS = ValueEditorVariable("objs", "已选中模型对象")
    PARENT_OBJ = ValueEditorVariable("parent_obj", "父模型对象")
    CONTEXT_TRIGGER_OBJ = ValueEditorVariable("context_trigger_obj", "上下文触发模型对象")
    APP_LOCATION = ValueEditorVariable("app_location", "应用根URL")
    CURRENT_ENV = ValueEditorVariable("current_env", "应用执行环境")

    ALL = [
        CURRENT_USER, CURRENT_DEP, ROLES, CONTEXT, SQCODE, OBJ, OBJS,
        PARENT_OBJ, CONTEXT_TRIGGER_OBJ, APP_LOCATION, CURRENT_ENV
    ]
    UUID_TO_EXPR = {v.uuid: v.expr for v in [
        OBJ, OBJS, PARENT_OBJ, CONTEXT_TRIGGER_OBJ,
        CURRENT_USER, CURRENT_DEP, CURRENT_ENV, APP_LOCATION
    ]}


class ValueEditorLanguage:
    PYTHON = 0
    MYSQL = 1


class DocUpdateType(int, DEnum):
    INSERT = 0
    UPDATE = 1
    DELETE = 2


class DocUpdateMessage(BaseModel):
    msg_type: DocUpdateType
    timestamp: int = BaseField(default_factory=functools.partial(int, time.time()))
    doc_type: int
    doc_uuid: str
    obj_uuid: str   # func uuid / page uuid
    module_name: str
    content: str

class DeltaItemChangeType(DEnum):
    ADD = "add"
    REMOVE = "remove"
    UPDATE = "update"

class DeltaItemType(DEnum):
    MODEL = "model"
    FIELD = "field"
    RELATIONSHIP = "relationship"


class DocItemDelta(BaseModel):
    delta_type: DeltaItemChangeType
    item_uuid: str
    item_type: DeltaItemType
    references: list

class DocDeltaMessage(BaseModel):
    timestamp: int = BaseField(default_factory=functools.partial(int, time.time()))
    doc_type: int
    doc_uuid: str
    user_uuid: Optional[str] = None
    delta_items: List[DocItemDelta]


class ExcelExportType(object):
    export_filter = 0
    export_all = 1
    export_selected = 2


class SearchSaveActionKey():

    SORT = "sort"
    SEARCH = "search"
    SEARCH_TYPE = "search_type"
    FILTER = "filter"
    DROPDOWN = "dropdown"
    GROUPBY = "groupby"
    PAGINATION = "pagination"
    PROPERTY = "property"
    ITEM = "condition_list"
    GENERAL = "general_condition_list"
    FREE = "free_condition_list"
    RELATION = "operation_relation"

    ALL = [
        SORT, SEARCH, SEARCH_TYPE, FILTER, DROPDOWN, GROUPBY, PAGINATION,
        PROPERTY, ITEM, GENERAL, FREE, RELATION
    ]


class ButtonSearchType():

    ITEM: int = 0
    GENERAL: int = 1
    FREE: int = 2

    TYPE_TO_KEY = {
        ITEM: SearchSaveActionKey.ITEM,
        GENERAL: SearchSaveActionKey.GENERAL,
        FREE: SearchSaveActionKey.FREE,
    }

class TreelistExpandType(object):

    COLLAPSE_ALL = 0
    EXPAND_ALL = 1
    FIRST_LEVEL = 2


class LocalSystemLogType(object):
    
    TOTAL = 500


class OpenPageType(object):

    JUMP = 1
    POP_UP = 0
    DRAWER = 2

class HierarchyType(object):
    
    YEAR = 0
    QUARTER = 1
    MONTH = 2
    WEEK = 3
    DAY = 4
    WEEKDAY = 5
    HOUR = 6
    MINUTE = 7
    SECOND = 8

class HierarchyFieldType(object):
    
    NOT_HIERARCHY = 0  # 非层次字段
    HIERARCHY_FIELD = 1  # 普通层次字段
    DATE_HIERARCHY_FIELD = 2  # 日期层次字段

class SelectTimeType(object):
    
    LASTDAY = 0
    LASTWEEK = 1
    PASTMONTH = 2
    PASTSIXMONTHS = 3
    PASTYEAR = 4
    TODAY = 5
    THISWEEK = 6
    THISMONTH = 7
    THISYEAR = 8


class DateFilterType(object):

    MANUAL_SELECT = 0
    SPECIFY_TIME = 1


class CardlistType(object):

    NORMAL = 0
    SIMPLE = 1


class ButtonShowType(object):
    only_text = 0
    text_icon = 1
    only_icon = 2


class DatalistColumnType:
    NORMAL = 0
    DYNAMIC = 1


class DynamicColumnType:
    DEFAULT = 1
    EXAMPLE = 2


class DynamicSourceType:
    JSON = 0
    SUBTABLE = 1


class InputSetting:

    # 适配器
    ADAPTER = "electronic_scales"  # 电子秤

    # read_method 输入方式
    KEYBOARD_INPUT = 0  # 键盘输入
    EXTERNAL_DATA_ADAPTER = 1  # 外部数据适配器

    # type 读取方式
    AUTO_READ = 0  # 自动读取
    MANUAL_INPUT = 1  # 手动输入
    
    # read_interval 读取间隔
    READ_INTERVAL = 1
    FORM_INPUTSETTING = {
        "type": AUTO_READ, "adapter": ADAPTER, "read_method": KEYBOARD_INPUT, "read_interval": READ_INTERVAL}
    DATALIST_INPUTSETTING = {
        "type": MANUAL_INPUT, "adapter": ADAPTER, "read_method": KEYBOARD_INPUT, "read_interval": READ_INTERVAL}


class ImportStatus:

    DOING = 0
    SUCCEED = 1
    FAILED = 2


class AggregationType:
    SINGLE_COLUMN = 0
    MULTIPLE_COLUMN = 1
    
    
class ReportTableAttr:
    DATASET = "数据集"


class NavPlatformType:
    reactive = 0
    pc = 1
    pad = 2
    mobile = 3


class AddButtonEditType:
    # 新建按钮 -> 新建时
    open_new_page = 0  # 打开新页面
    add_to_beginning = 1  # 首行添加
    add_to_end = 2  # 末行添加


class UniverFuncType:

    LEMON_FILL = "LEMON_FILL"
    LEMON_GET = "LEMON_GET"
    LEMON_EXCEL_CELL = "LEMON_EXCEL_CELL"
    RETURNFAIL = {"message": 'fail'}
    RETURNOK = {"message": "OK"}
    TEMPERROR = {"error": "模板配置错误"}
    CHANGEERROR = {"error": "模板修改失败"}


class DropdownStyle:
    TEXT_STYLE = 0  # 文字样式
    BUTTON_STYLE = 1  # 按钮样式
    ONLY_DISPLAY_ICON = 2  # 仅显示图标


class DlistOptionType:

    SORT_FILTER = "sort_filter"
    SEARCH_BUTTON_FILTER = "search_button_filter"

    ALL = [SORT_FILTER, SEARCH_BUTTON_FILTER]
