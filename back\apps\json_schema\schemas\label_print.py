from apps.json_schema.schemas.utils import gen_const_condition_schema

text = {
    "type": "object",
    "properties": {
        "data_source": {
            "$ref": "mem://common/value_editor_all"
        }
    }
}

pic = {
    "type": "object",
    "properties": {
        "data_source": {
            "type": "object",
            "properties": {
                "image": {
                    "type": "object",
                    "properties": {
                        "uuid": {
                            "$ref": "mem://common/image_ref"
                        }
                    }
                }
            }
        }
    }
}

qr_code = {
    "type": "object",
    "properties": {
        "data_source": {
            "type": "object",
            "properties": {
                "code_data": {
                    "$ref": "mem://common/value_editor_all"
                }
            }
        }
    }
}


label_print = {
    "is_element": True,
    "attr_name": "标签打印",
    "type": "object",
    "properties": {
        "data_source": {
            "type": "object",
            "properties": {
                "model": {
                    "$ref": "mem://common/model_ref"
                }
            }
        },
        "label_context": {
            "type": "array",
            "items": {
                "allOf": [
                    gen_const_condition_schema(3, "mem://label_print/text", const_key="label_type"),  # 文字
                    gen_const_condition_schema(4, "mem://label_print/text", const_key="label_type"),  # 多行文本
                    gen_const_condition_schema(5, "mem://label_print/qr_code", const_key="label_type"),
                    gen_const_condition_schema(6, "mem://label_print/qr_code", const_key="label_type"),
                    gen_const_condition_schema(7, "mem://label_print/pic", const_key="label_type"),
                ]
            }
        }
    }
}