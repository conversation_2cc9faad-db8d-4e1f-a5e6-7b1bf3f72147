# -*- coding:utf-8 -*-

import io
import uj<PERSON>
import datetime
import asyncio
import traceback
from typing import List, Callable, Optional
from functools import partial, wraps
from contextlib import redirect_stdout
import time
import weakref
from RestrictedPython import RestrictingNodeTransformer

from baseutils.log import app_log
from baseutils.utils import LemonContextVar, CVE, FuncRunError
from apps.utils import lemon_uuid
from apps.exceptions import FuncFieldPermissionError, BackGroundError
from apps.ide_const import (
    ComponentType,
    ValueEditorType,
    ValueEditorVariables,
)
from apps.ide_const import DictWrapper
from runtime.core.py_module import PyModuleFunc
from runtime.engine import engine
from baseutils.const import PermissionAction as Action, ValueEditorMeaning
from runtime.core.utils import loop, LemonGlobals
from runtime.core.value_editor import LemonValueEditor, BaseValueEditor
from runtime.core.utils import compile_restricted_async_function as _craf_
from RestrictedPython import compile_restricted_function as _crf_
from runtime.log import runtime_log
from runtime.metrics.business_metrics import FuncInstrumentContext
from runtime.core.py_module import PyModuleFunc

from tests.document.func import (
    print_action,
    send_email,
    faker,
    modify_variable,
    model_event,
)

all_public_wrapper_func = []
all_func = {
    print_action.uuid: print_action.to_dict(),
    send_email.uuid: send_email.to_dict(),
    faker.uuid: faker.to_dict(),
    modify_variable.uuid: modify_variable.to_dict(),
    model_event.uuid: model_event.to_dict(),
}


class LemonRestrictingNodeTransformer(RestrictingNodeTransformer):

    def visit_AsyncFunctionDef(self, node):
        """Deny async functions."""
        print("in async func def")
        super().visit_FunctionDef(node)
        print("out async func def")

    def visit_Await(self, node):
        """Deny async functionality."""
        self.node_contents_visit(node)

    def visit_AsyncFor(self, node):
        """Deny async functionality."""
        self.node_contents_visit(node)

    def visit_AsyncWith(self, node):
        """Deny async functionality."""
        self.node_contents_visit(node)


class LemonSMValueEditor(object):
    """
    状态机 和 容器 都会实例此类，区别在于：
    1. 状态机上运行的实例，用来计算状态机内部数据，不会将数据更新到状态机属性上
    2. 容器上运行的实例，需要绑定到属性数据，以返回前端
    为何要实例两次：
    因为提供给用户使用的 lemon.utils.lemon_editor 默认 *应该* 是不进行数据更新的
    """

    _value_editor_class_ = LemonValueEditor
    type_error_message = "表达式值编辑器返回的结果类型错误"

    def __init__(self, component=None):
        self._lsm = None
        self._component = None
        self.component = component
        self.value_editor_dict = {}

    @property
    def lsm(self):
        if self.component:
            return self.component.lsm
        return None if self._lsm is None else self._lsm()

    @lsm.setter
    def lsm(self, value):
        self._lsm = value if value is None else weakref.ref(value)

    @property
    def component(self):
        if self._component is None:
            return None
        else:
            return self._component()

    @component.setter
    def component(self, value):
        self._component = None if value is None else weakref.ref(value)

    def replace_value_editor_value(self, value, to_str=True):
        if to_str:
            if isinstance(value, (datetime.datetime, datetime.date)):
                value = value.strftime("%Y/%m/%d %H:%M:%S")
                return value
            # elif isinstance(value, bool):
            #     return "是" if value else "否"
            elif isinstance(value, list):
                # app_log.info(f"source_value: {value}")
                new_value_list = []
                for v in value:
                    if isinstance(v, (datetime.datetime, datetime.date)):
                        new_value_list.append(v.strftime("%Y/%m/%d %H:%M:%S"))
                    # elif isinstance(value, bool):
                    #     return "是" if value else "否"
                    else:
                        try:
                            ujson.dumps(v)
                        except Exception:
                            new_value_list.append(self.type_error_message)
                        else:
                            new_value_list.append(v)
                return new_value_list
            else:
                try:
                    ujson.dumps(value)
                except Exception:
                    # TODO: 可能跟用户，实际需要返回的数据类型不符合
                    return self.type_error_message
                else:
                    return value
        return value

    def init(self, editor_dict) -> BaseValueEditor:
        value_editor = None
        if isinstance(editor_dict, dict) and editor_dict:
            editor_uuid = editor_dict.get("uuid")
            value_editor = self.value_editor_dict.get(editor_uuid)
            if value_editor is None:
                value_editor = self._value_editor_class_(reuse=False, **editor_dict)
                self.value_editor_dict.update({editor_uuid: value_editor})
            else:
                value_editor.clear()
        return value_editor

    async def batch_calc_value(self, value_editor, nested_forms):
        for form in nested_forms:
            value_editor.set_globals(form.lsm)

    def calc_extend_value(self, value, extend_value, extend_op):
        if extend_op == 0:  # or
            value = value or extend_value
            # value_editor.value = value
        elif extend_op == 1:  # and
            if extend_value is not None:
                value = True if (value and extend_value) else False
            # value_editor.value = value
        return value

    def batch_calc_extend_value(self, batch_values, extend_value, extend_op):
        if extend_value is not None:
            batch_values = [self.calc_extend_value(v, extend_value, extend_op) for v in batch_values]
        return batch_values

    async def batch_get_value(
            self, value_editor, nested_forms, default=None,
            extend_value=None, extend_op=None, to_str=True):
        if value_editor is None:
            if default is None:
                return [None] * len(nested_forms)
            return [default] * len(nested_forms)

        if default is None:
            if value_editor.type in [ValueEditorType.EXPR, ValueEditorType.FIELD]:
                if value_editor.type == ValueEditorType.EXPR:
                    batch_values = await value_editor.batch_calc_value_thread(nested_forms)
                else:
                    batch_values = await value_editor.calc_value(nested_forms)
                batch_values = [self.replace_value_editor_value(v, to_str) for v in batch_values]
                batch_values = self.batch_calc_extend_value(batch_values, extend_value, extend_op)
                value = batch_values[-1]
                value_editor.value = value
                return batch_values
            elif value_editor.type == ValueEditorType.VARIABLE:
                batch_values = value_editor.calc_value(nested_forms)
                batch_values = self.batch_calc_extend_value(batch_values, extend_value, extend_op)
                # value = batch_values[-1]
                # value_editor.value = value
                return batch_values
            else:
                # 在为每行表单计算数据
                value = value_editor.value
                batch_values = [value] * len(nested_forms)
                batch_values = self.batch_calc_extend_value(batch_values, extend_value, extend_op)
                value = batch_values[-1]
                return batch_values
        else:
            value = self.replace_value_editor_value(default, to_str)
            value_editor.value = value
            return [value] * len(nested_forms) if nested_forms else value

    async def single_get_value(
            self, value_editor, default, extend_value, extend_op,
            to_str, **kv_globals):
        if value_editor is None:
            if default is None:
                return None
            return default

        if default is None:
            if self.lsm:
                value_editor.set_globals(self.lsm)
            if value_editor.type in [ValueEditorType.EXPR, ValueEditorType.FIELD]:
                value_editor.update_globals(**kv_globals)
                if value_editor.type == ValueEditorType.EXPR:
                    await value_editor.calc_value()
                    value = await value_editor.value_async
                else:
                    value = await value_editor.calc_value()
                value = self.replace_value_editor_value(value, to_str)
                value = self.calc_extend_value(value, extend_value, extend_op)
                value_editor.value = value
                return value
            else:
                value = self.get_value_from_editor(value_editor)
                value = self.calc_extend_value(value, extend_value, extend_op)
                value_editor.value = value
                return value
        else:
            value = self.replace_value_editor_value(default, to_str)
            value_editor.value = value
            return value

    async def get_value(
        self,
        value_editor,
        default=None,
        extend_value=None,
        extend_op=None,
        to_str=True,
        **kv_globals,
    ):
        nested_forms = LemonContextVar.nested_forms.get()
        if nested_forms:
            return await self.batch_get_value(
                value_editor, nested_forms, default, extend_value, extend_op, to_str)
        else:
            return await self.single_get_value(
                value_editor, default, extend_value, extend_op, to_str, **kv_globals)

    def get_value_from_editor(self, value_editor):
        value = value_editor.value
        if value_editor.type == ValueEditorType.VARIABLE:
            if value_editor.variable_uuid == ValueEditorVariables.OBJ.uuid:
                # 值编辑器选择变量且选择模型对象
                component = self.lsm.component
                if component.component_type == ComponentType.DATALIST:
                    parent = component.parent
                    if parent and parent.component_type == ComponentType.FORM:
                        # self.lsm 是关联子表的状态机
                        value = parent.model_obj
        return value

    async def __call__(
        self,
        editor_dict,
        default=None,
        extend_value=None,
        extend_op=None,
        to_str=True,
        **kv_globals,
    ):
        # app_log.debug(f"editor_dict: {editor_dict}")
        value_editor = self.init(editor_dict)
        return await self.get_value(
            value_editor,
            default,
            extend_value,
            extend_op,
            to_str,
            **kv_globals,
        )


lemon_sm_value_editor = LemonSMValueEditor()


class FuncArg(object):

    def __init__(self, arg_dict):
        self.name = arg_dict.get("name")
        self.type = arg_dict.get("type")
        self.default = arg_dict.get("default")
        self.model = arg_dict.get("model")


class FuncReturn(object):

    def __init__(self, return_dict):
        self.name = return_dict.get("name")
        self.type = return_dict.get("type")


class AsyncFunc(object):

    def __init__(self, func_dict, compiled, errors=""):
        self.uuid = func_dict.get("uuid")
        self.name = func_dict.get("name")
        self.is_async = func_dict.get("is_async", False)
        self.is_system = func_dict.get("is_system", False)
        self.arg_list = func_dict.get("arg_list", list())
        self.return_list = func_dict.get("return_list", list())
        self.compiled = compiled
        self.errors = errors
        self.func_arg_list: List[FuncArg] = list()
        self.func_return_list: List[FuncReturn] = list()
        self.globals = LemonGlobals()
        self.stdout = io.StringIO()
        self.init_arg_list()
        self.init_return_list()
        self.init_globals()

    def __repr__(self):
        return "<%s('%s')@%s>" % (type(self).__name__, self.name, id(self))

    def compiled_function(self, *args, **arg_value_dict):
        if not self.is_system:
            engine.pm.predicate(self.uuid, Action.EXECUTE)
        _locals = dict()
        arg_default_dict = dict()
        lsm = LemonContextVar.current_lsm.get()
        lemon_sm_value_editor.lsm = lsm
        for func_arg in self.func_arg_list:
            if func_arg.default and isinstance(func_arg.default, dict):
                value_editor = lemon_sm_value_editor.init(func_arg.default)
                arg_value = value_editor.value
                arg_default_dict.update({func_arg.name: arg_value})
            else:
                arg_default_dict.update({func_arg.name: None})
        for index, arg in enumerate(args):
            func_arg = self.func_arg_list[index]
            arg_default_dict.update({func_arg.name: arg})
        for arg_name, arg_value in arg_value_dict.items():
            if arg_name in arg_default_dict:
                arg_default_dict.update({arg_name: arg_value})
        self.globals.update(arg_default_dict)
        func_name = self.name
        if self.compiled is None:
            raise RuntimeError(self.errors)
        if isinstance(self.compiled, PyModuleFunc):
            return partial(self.compiled.with_globals(self.globals), **arg_default_dict)
        exec(self.compiled.code, self.globals, _locals)
        compiled_function = _locals.get(func_name)
        return compiled_function

    async def __call__(self, *args, **arg_value_dict):
        compiled_function = self.compiled_function(*args, **arg_value_dict)
        return await compiled_function()

    def init_arg_list(self):
        for arg_dict in self.arg_list:
            func_arg = FuncArg(arg_dict)
            self.func_arg_list.append(func_arg)

    def init_return_list(self):
        for return_dict in self.return_list:
            func_return = FuncReturn(return_dict)
            self.func_return_list.append(func_return)

    def init_globals(self):
        self.globals.update({"asyncio": asyncio, "print": print})

    def update_globals(self, global_dict):
        self.globals.update(global_dict)


class Func(AsyncFunc):

    def __call__(self, *args, **arg_value_dict):
        compiled_function = self.compiled_function(*args, **arg_value_dict)
        return compiled_function()


class ErrorFunc(AsyncFunc):

    def __call__(self, *args, **arg_value_dict):
        raise RuntimeError(self.compiled.errors)


class FuncResultWrapper(DictWrapper):

    def __init__(self, result, return_list):
        self._result = result
        self._return_list = return_list
        self.update(self._result)

    def as_list(self):
        data_list = []
        for func_return in self._return_list:
            name = func_return.name
            data = self._result.get(name)
            data_list.append(data)
        return data_list


class FuncWrapper(object):

    def __init__(self, func_dict, module_dict, pre_exe_func: Optional[Callable] = None):
        self.compiled_error = False
        self.func_dict = func_dict
        self.func_name = self.func_dict.get("name")
        self.is_async = self.func_dict.get("is_async", False)
        self.is_system = self.func_dict.get("is_system", False)
        self.func_str = self.func_dict.get("func", "")
        self.func_uuid = self.func_dict.get("func_uuid") or self.func_dict.get("uuid")
        self.return_list = func_dict.get("return_list", list())
        self.init_func_str()
        self.errors = ""
        self.module_uuid = module_dict.get("module_uuid")
        self.module_name = module_dict.get("module_name")
        self.compiled = pre_exe_func or self.func_compile()
        self.func = self.new_func()

    def __repr__(self):
        return "<%s('%s')@('%s')%s>" % (
            type(self).__name__,
            self.func_name,
            self.is_async,
            id(self),
        )

    def func_compile(self):
        try:
            filename = "_".join([self.module_name, self.func_name])
            func_name = self.func_name
            crf = _craf_ if self.is_async else _crf_
            compiled = crf("", self.func_str, func_name, filename=filename, policy=None)
            app_log.debug(f"compiled: {func_name}")
            if compiled.errors:
                app_log.info(f"func: {func_name}, compiled errors: {compiled.errors}")
            if compiled.errors:
                self.compiled_error = True
        except Exception:
            self.errors = traceback.format_exc()
            app_log.info(f"func: {self.func_name}, exception errors: {self.errors}")
            compiled = None
        return compiled

    def init_func_str(self):
        """
        # 根据云函数的返回值，生成 return
        """
        func_str_list = list()
        for return_dict in self.return_list:
            key = return_dict.get("name")
            func_str = f'"{key}": {key}'
            func_str_list.append(func_str)
        _func_str = ", ".join(func_str_list)
        _func_str = "return {" + _func_str + "}"
        self.func_str = self.func_str + "\n" + _func_str

    def new_func(self) -> AsyncFunc:
        if self.compiled_error:
            func_class = ErrorFunc
        else:
            func_class = AsyncFunc if self.is_async else Func
        func = func_class(self.func_dict, self.compiled, self.errors)
        return func

    def run_func(self, func, *args, **kwargs):
        with FuncInstrumentContext(self.func_name, app_log):
            stdout = None
            if isinstance(func, partial):
                stdout = func.func.stdout
            elif isinstance(func, AsyncFunc):
                stdout = func.stdout
            if stdout is not None:
                with redirect_stdout(stdout):
                    return func(*args, **kwargs)
            else:
                return func(*args, **kwargs)

    @staticmethod
    def update_globals(func: AsyncFunc, lsm=None):
        # app_log.info(f"current_lsm: {LemonContextVar.current_lsm.get()}")
        lsm = lsm or LemonContextVar.current_lsm.get()
        app_log.info(f"func: {func}, lsm: {lsm}")
        from runtime.core.namespace import LemonWrapper

        if lsm is None:
            lemon = LemonContextVar.current_lemon.get() or LemonWrapper()
            func.update_globals({"lemon": lemon})
        else:
            func.update_globals({"lemon": lsm.lemon, "ctx": lsm.lemon.ctx})
            # app_log.info(f"m_id: {lsm.machine_id}")
            # app_log.info(f"current_user: {lsm.lemon.system.current_user}")
            # 这里是为了让 模型的查询能够 拿到当前用户是谁 自动加上 user_uuid 和 tenant_id 的筛选
            LemonContextVar.current_user.set(lsm.lemon.system.current_user)

        # 这里是将 系统内置的 部分云函数 加到 当前云函数的 globals 里 已达到调用这些内置云函数 的目的
        # 因为在系统内置的云函数里，有调用其他系统内置云函数的需要
        # 用户自己写的云函数 则不需要再 加入到 globals 里，会使用 lemon 关键字 到 namespace 里查询
        for public_wrapper_func in all_public_wrapper_func:
            public_func_new = public_wrapper_func.new_func()
            public_func_new.globals = func.globals
            func.update_globals({public_func_new.name: public_func_new})

    def handle_on_exception(decorated_func):

        @wraps(decorated_func)
        async def wrapper(func_obj, *args, **kwargs):
            try:
                func = func_obj.new_func()
                return await decorated_func(func_obj, *args, _func=func, **kwargs)
            except FuncFieldPermissionError as e:
                stdout = func.stdout.getvalue()
                format_error = traceback.format_exc()
                func_message = f"出错的云函数: {func.name}"
                app_log.error(format_error)
                func_error = FuncRunError(e.args[0], func.name)
                return "\n".join([func_message, stdout]) + format_error, func_error
            except BackGroundError as e:
                stdout = func.stdout.getvalue()
                format_error = traceback.format_exc()
                func_message = f"出错的云函数: {func.name}"
                app_log.error(format_error)
                # background_error 前端不弹窗，但系统日志需要显示
                func_error = FuncRunError(
                    "\n".join([func_message, stdout]) + format_error, func.name, rollback=e.rollback)
                return "", func_error
            except Exception:
                stdout = func.stdout.getvalue()
                format_error = traceback.format_exc()
                func_message = f"出错的云函数: {func.name}"
                app_log.error(format_error)
                try:
                    format_error = format_error.replace(func.uuid, func.name)
                except Exception:
                    format_error = format_error
                func_error = FuncRunError(format_error, func.name)
                return "\n".join([func_message, stdout, format_error]), func_error

        return wrapper

    @handle_on_exception
    async def call_async(self, *args, lsm=None, globals=None, _func=None, **kwargs):
        func = _func or self.new_func()
        self.update_globals(func, lsm=lsm)
        if globals:
            func.update_globals(globals)
        if func.is_async:
            result = await self.run_func(func, *args, **kwargs)
        else:
            loop = asyncio.get_running_loop()
            run_p_func = partial(self.run_func, func, *args, **kwargs)
            result = await loop.run_in_executor(CVE, run_p_func)
        stdout = func.stdout.getvalue()
        return stdout, result

    def batch_run_func(self, func, *args, **kwargs):
        with FuncInstrumentContext(self.func_name):
            stdout = None
            if isinstance(func, partial):
                stdout = func.func.stdout
            elif isinstance(func, AsyncFunc):
                stdout = func.stdout
            batch_args = kwargs.get("batch_args", [])
            return_list = []
            for args in batch_args:
                if stdout is not None:
                    with redirect_stdout(stdout):
                        return_list.append(func(*args, **kwargs))
                else:
                    return_list.append(func(*args, **kwargs))
            return return_list

    @handle_on_exception
    async def batch_call_async(self, *args, lsm=None, globals=None, _func=None, **kwargs):
        func = _func or self.new_func()
        self.update_globals(func, lsm=lsm)
        if globals:
            func.update_globals(globals)
        if func.is_async:
            result = await self.batch_run_func(func, *args, **kwargs)
        else:
            loop = asyncio.get_running_loop()
            run_p_func = partial(self.batch_run_func, func, *args, **kwargs)
            result = await loop.run_in_executor(CVE, run_p_func)
        stdout = func.stdout.getvalue()
        return stdout, result

    def __call__(self, *args, lsm=None, need_stdout=False, **kwargs):
        # 这里应该只可能被，用户写的云函数里，又调用了它写的云函数
        # 所以需要将异常 raise 出去，因为用户可能自己写了 except 捕获异常
        func = self.new_func()
        try:
            # if is_main_thread():
            #     raise Exception("请使用 call_async 方法")
            self.update_globals(func, lsm=lsm)
            if func.is_async:
                raise Exception("无法执行异步函数")
            else:
                start_time = time.time()
                result = self.run_func(func, *args, **kwargs)
                func_out = func.stdout.getvalue()
                # 如果func_out为空直接print会产生换行符，会导致产生空弹窗
                if func_out:
                    stack = traceback.format_stack()
                    # 第一级云函数需要直接记录到系统日志里
                    if len(stack) >= 2 and (
                        "module" in stack[-2] or "jinja" in stack[-2]
                    ):
                        func_log = runtime_log.func_log(
                            self, func_out, result, start_time
                        )
                        future = asyncio.run_coroutine_threadsafe(func_log, engine.loop)
                        file_obj = future.result()
                    elif func_out:
                        # 如果func_out有内容需要传递到上一级云函数的输出，这样系统日志才会有记录
                        print(func_out)
            stdout = func.stdout.getvalue()
            if result is False:
                func_message = f"出错的云函数: {func.name}"
                stdout = "\n".join([func_message, stdout])
            if need_stdout:
                return stdout, result
            return result
        except BackGroundError as e:
            stdout = func.stdout.getvalue()
            func_message = f"出错的云函数: {func.name}"
            format_error = traceback.format_exc()
            raise BackGroundError("\n".join([func_message, stdout]), rollback=e.rollback)
        except FuncFieldPermissionError as e:
            stdout = func.stdout.getvalue()
            if stdout:
                print(stdout)
            try:
                func_log = runtime_log.func_log(self, stdout, result, start_time)
                future = asyncio.run_coroutine_threadsafe(func_log, engine.loop)
                future.result()
            except:
                pass
            format_error = traceback.format_exc()
            app_log.error(format_error)
            func_message = f"出错的云函数: {func.name}"
            raise FuncFieldPermissionError("\n".join([func_message, stdout]))
        except Exception as e:
            stdout = func.stdout.getvalue()
            if stdout:
                print(stdout)
            format_error = traceback.format_exc()
            app_log.error(format_error)
            func_message = f"出错的云函数: {func.name}"
            raise type(e)("\n".join([func_message, stdout]))


async def main(func_str):
    func_str = """time.sleep(1)\nz = x + y\na = x - y\nb = x * y"""
    _globals = LemonGlobals()
    _globals.update({"asyncio": asyncio, "x": 10, "y": 1})
    _locals = {"x": 1, "y": 1}

    # 生成
    compiled = _craf_("", func_str, "lemon_func", policy=None)
    app_log.info(f"code obj: {compiled.code}")

    # 用来判断编译错误（编译都是通过的，无错误）
    app_log.info(f"compile errors: {compiled.errors}")

    # compiled.code.co_names 正常时为 ("lemon_func",) 有问题时为 ()
    app_log.info(f"code co_names: {compiled.code.co_names}")

    # 执行，并从 _locals 中拿到函数
    exec(compiled.code, _globals, _locals)

    """
    # 问题：如果compile的时候，参数 policy=RestrictingNodeTransformer
    # 则无法从 _locals 中拿到 lemon_func
    """
    app_log.info(f"locals: {_locals}")
    compiled_function = _locals["lemon_func"]
    app_log.info(f"func: {compiled_function}")

    # 运行，拿到函数返回值
    result = await compiled_function()
    app_log.info(f"result: {result}")


if __name__ == "__main__":
    func_str = """time.sleep(1)\nz = x + y\ntype = x - y\nc = x * y"""
    return_list = [{"name": "z", "type": 1}, {"name": "type", "type": 2}]
    func_dict = {
        "uuid": lemon_uuid(),
        "name": "test_func",
        "func": func_str,
        "return_list": return_list,
    }
    func = Func(func_dict)
    loop.create_task(main(func.func_str))
    loop.run_forever()
