# -*- coding:utf-8 -*-

from apps.services.document.value import Expr<PERSON>alue, StringValue
from apps.ide_const import (
    PrintType, ValueEditorType, VariableType
)
from apps.utils import <PERSON><PERSON>, lemon_uuid
from tests.utils import UUID


text_style = {
    "horizontal_align": "center",
    "vertical_align": "center",
    "font_size": 14,
    "font_weight": "normal",
    "color": "",
    "bg_color": ""
}


class PageTitleValue(StringValue):

    def __init__(self, value):
        value_type = ValueEditorType.STRING
        super().__init__(value_type, value)


class Print(Json):

    def __init__(
        self, uuid, orientation="portrait", page_width=1000, format="a4", size=None, margin=None, 
        children=None, *args, **kwargs):
        self.uuid = uuid
        self.orientation = orientation
        self.page_width = page_width
        self.format = format
        self.size = [100, 100] if size is None else size
        self.margin = [10, 10, 10, 10] if margin is None else margin
        self.children = list() if children is None else children
        super().__init__(*args, **kwargs)


class Table(Json):

    def __init__(
        self, uuid, name, row_num=5, col_num=5, merges=None, borders=None, children=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = PrintType.TABLE
        self.row_num = row_num
        self.col_num = col_num
        self.merges = list() if merges is None else merges
        self.borders = list() if borders is None else borders
        self.children = list() if children is None else children
        super().__init__(*args, **kwargs)


class Form(Json):

    def __init__(
        self, uuid, name, data_source=None, row_num=5, col_num=5, children=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = PrintType.FORM
        self.row_num = row_num
        self.col_num = col_num
        self.data_source = dict() if data_source is None else data_source
        self.children = list() if children is None else children
        super().__init__(*args, **kwargs)


class Cardlist(Json):

    def __init__(
        self, uuid, name, data_source=None, children=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = PrintType.CARDLIST
        self.data_source = dict() if data_source is None else data_source
        self.children = list() if children is None else children
        super().__init__(*args, **kwargs)


class Datalist(Json):

    def __init__(
        self, uuid, name, data_source=None, min_row_height=30, width_unit=0, bordered=False, align="center", 
        font_size=10, font_weight="normal", columns=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = PrintType.DATALIST
        self.data_source = dict() if data_source is None else data_source
        self.min_row_height = min_row_height
        self.width_unit = width_unit
        self.bordered = bordered
        self.align = align
        self.font_size = font_size
        self.font_weight = font_weight
        self.columns = list() if columns is None else columns
        super().__init__(*args, **kwargs)


class ColumnItem(Json):

    def __init__(
        self, uuid, name, title="column", data_source=None, width=None, aggregation=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.title = title
        self.data_source = dict() if data_source is None else data_source
        self.width = dict() if width is None else width
        self.aggregation = dict() if aggregation is None else aggregation
        super().__init__(*args, **kwargs)


class Text(Json):

    def __init__(self, uuid, name, data_source=None, coordinate=None, style=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = PrintType.TEXT
        self.data_source = dict() if data_source is None else data_source
        self.coordinate = [0, 0, 0, 0] if coordinate is None else coordinate
        self.style = text_style if style is None else style
        super().__init__(*args, **kwargs)


class Image(Json):

    def __init__(self, uuid, name, data_source=None, coordinate=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = PrintType.IMAGE
        self.data_source = dict() if data_source is None else data_source
        self.coordinate = [0, 0, 0, 0] if coordinate is None else coordinate
        super().__init__(*args, **kwargs)


class Visual(Json):

    def __init__(self, uuid, name, coordinate=None, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.type = PrintType.VISUAL
        self.coordinate = [0, 0, 0, 0] if coordinate is None else coordinate
        super().__init__(*args, **kwargs)
