# -*- coding:utf-8 -*-

import asyncio
import time
import traceback
import hashlib

import peewee
import peewee_async
import ujson
import copy
import os
import contextlib
import jsonpatch
import zlib
import base64
from typing import List, Optional, Type, TypeVar, Union, Iterable, Dict
from functools import reduce
from itertools import groupby
import operator
from peewee import ModelSelect, ModelUpdate, Query, ModelDelete, Field, Expression
from peewee import JOIN, Value, Case, fn, SQL, NodeList, Query, Model as PwModel
import aioredlock
from packaging import version

from baseutils.log import app_log
from baseutils.utils import LemonContextVar
from apps.ext import DB
from baseutils.const import (
    Action, ImportResult, ImportType, Log, SystemField, ResourceType, SystemTable, SYSConfig)
from apps.ide_const import EntityType, RestfulAuthorizationType
from apps.config import Config, DevelopmentConfig
from apps.utils import (
    lemon_uuid, patch_json, patch_document_content, check_document_name, split_binary_string,
    replace_relationship_uuid, list_system_relationship, document_name_to_document_type
)
from apps.exceptions import LemonDesignError
from apps.entity import (
    APPSetting, AppConfig, BaseModel, CheckMessage, Combine, ConnectorTenantConfig, DocumentOrder, Extension, ExtensionDetail,
    ModuleRole, Navigation, NavigationItem, TemplateTable, UserRole, UserRoleContent, Workflow, DocumentReference,
    Connector, Publisher, AppletCodeManager, Resource, Tag, IconFont, AppGroup, PublishGroup
)
from apps.entity import (
    Policy, APP, Module, Document, DocumentContent, Page, Print, APPPublish, LabelPrint,
    ModelBasic, RelationshipBasic, StateMachine, Func as DesignFunc, Event, State, ModelIndex, ModelField,
    ToolCategory, SYSTool, UserTool, ImageTable, ConstTable, JsonTable, EnumTable,
    Client, RuntimeAccount, RuleChain, AppManager, ExtPolicy,
    AppPublishMessage, AuthorizedWX, ImportRecord, RestfulTable, APPBranch, SubmitAuditWX,
    SandboxUser, Sandbox, Localhistory, ExportTemplate, DocumentLinks, LocalhistoryPatch,
    Resource, Tag, DocumentDraft, Packages
)
from apps.base_entity import User
from apps.runtime_entity import AppEditor, Tenant, GitAccessManagement, Team, Teamplayer
from apps.ide_const import DocumentType, NodeStatus, RelationshipType, ToolCategoryType, IDECode
from apps.base_utils import (
    check_document_func_name, get_sys_field_uuid, is_model_version_control,
    get_current_workspace, GlobalVars
)

MT = TypeVar("MT", bound=PwModel)
MAX_LOG_SIZE = Log.MAX_LOG_SIZE

Func = DesignFunc
app_name = os.environ.get("APP_NAME")
lemon_env = os.environ.get("LEMON_ENV")
if app_name and lemon_env != DevelopmentConfig.ENV and "sandbox" not in os.environ.get("REAL_ENV_NAME", ''):  # 认为开发环境不使用运行时数据库
    import resources  # 避免循环引入
    import importlib
    sys_module = importlib.import_module(
        "." + app_name.lower() + ".系统模块.lemon_model", resources.__name__)
    UserRole = sys_module.lemon_userrole
    ExportTemplate = sys_module.lemon_exporttemplate
    ModelBasic = sys_module.lemon_modelbasic
    Func = sys_module.lemon_func
    Resource = sys_module.lemon_resource
    Tag = sys_module.lemon_tag
    Navigation = sys_module.lemon_navigation
    NavigationItem = sys_module.lemon_navigationitem


def func_model_condition_func() -> Union[DesignFunc, Func]:
    current_user = LemonContextVar.current_user.get(None)
    if current_user and current_user.use_design:
        return DesignFunc
    return Func


class BaseDBAccess(object):

    """
    # get_obj 默认返回 object
    # select_obj_by_query 默认返回 dict
    # list_obj 默认返回 list(dict1, dict2)
    """

    def __init__(self, db: DB, config: Config = None):
        self.db = db
        self.config = config

    @contextlib.asynccontextmanager
    async def with_workspace(self, workspace=None):
        current_workspace = get_current_workspace()
        LemonContextVar.current_workspace.set(workspace)
        try:
            yield
        finally:
            LemonContextVar.current_workspace.set(current_workspace)

    async def create_obj(self, obj: Type[MT], **data) -> MT:
        obj_name = obj._meta.table_name
        app_log.info(f"Create instance: {obj_name}")
        # app_log.debug(f"Create data: {data}")
        start_time = time.time()
        instance = await self.db.objs.create(obj, **data)
        end_time = time.time()
        app_log.debug(f"Create instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        return instance

    async def insert_obj(self, obj: BaseModel, data, on_conflict_replace=False) -> BaseModel:
        obj_name = obj._meta.table_name
        app_log.info(f"Insert instance: {obj_name}")
        app_log.debug(f"Insert data: {data}")
        start_time = time.time()
        if on_conflict_replace:
            query = obj.replace(**data)
        else:
            query = obj.insert(**data)
        result = await self.db.objs.create(query)
        end_time = time.time()
        app_log.debug(f"Insert instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        return result

    async def insert_many_obj(self, obj: BaseModel, data_list: list, on_conflict_replace=False):
        obj_name = obj._meta.table_name
        app_log.info(f"Insert many instance: {obj_name}")
        start_time = time.time()
        # app_log.debug(f"Insert many data: {data_list}")
        if on_conflict_replace:
            query = obj.replace_many(data_list)
        else:
            query = obj.insert_many(data_list)
        result = await self.db.objs.execute(query)
        end_time = time.time()
        app_log.debug(f"Insert many instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        return result

    async def get_obj(
        self, obj: Type[MT], need_delete: bool = False, **data) -> Optional[MT]:
        try:
            obj_name = obj._meta.table_name
            app_log.info(f"Get instance: {obj_name}")
            # 默认 不查询 标记为 删除 的数据
            if not need_delete:
                # 判断 obj 是否有 is_delete 字段
                if hasattr(obj, "is_delete"):
                    data.update({"is_delete": False})
            app_log.debug(f"Get data: {data}")
            start_time = time.time()
            instance = await self.db.objs.get(obj, **data)
            end_time = time.time()
            app_log.info(f"Get instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        except Exception:
            instance = None
            app_log.error(f"Get instance not exists: {obj_name}")
        return instance

    async def select_obj_by_query(
        self, obj: Type[MT], query: ModelSelect,
        need_delete: bool = False, as_dict: bool = True) -> Optional[MT]:
        try:
            obj_name = obj._meta.table_name
            app_log.info(f"Get instance: {obj_name}")
            # 默认 不查询 标记为 删除 的数据
            if not need_delete:
                # 判断 obj 是否有 is_delete 字段
                if hasattr(obj, "is_delete"):
                    app_log.debug(f"Get query add is_delete==False")
                    query = query.where(obj.is_delete==False)
            if as_dict:
                query = query.dicts()
            app_log.debug(f"Get query sql: {query.sql()}"[:MAX_LOG_SIZE])
            start_time = time.time()
            instance = await self.db.objs.get(query)
            end_time = time.time()
            app_log.debug(f"Get instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        except Exception:
            instance = None
            app_log.error(traceback.format_exc())
        return instance

    async def list_obj(
        self, obj: Type[BaseModel], query: ModelSelect,
        as_dict: bool = True, need_delete: bool = False,
        raise_exception=True) -> list:
        try:
            obj_name = obj._meta.table_name
            app_log.info(f"List instance list: {obj_name}")
            # 默认 不查询 标记为 删除 的数据
            if not need_delete:
                # 判断 obj 是否有 is_delete 字段
                if hasattr(obj, "is_delete"):
                    app_log.debug(f"List query add is_delete==False")
                    query = query.where(obj.is_delete==False)
            if as_dict:
                query = query.dicts()
            app_log.debug(f"List query sql: {query}"[:MAX_LOG_SIZE])
            start_time = time.time()
            result = await self.db.objs.execute(query)
            end_time = time.time()
            app_log.debug(f"List instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        except Exception as e:
            app_log.error(traceback.format_exc())
            if raise_exception:
                raise e
            else:
                result = []
        return result

    async def count_obj(
        self, obj: BaseModel, query: ModelSelect,
        need_delete: bool = False) -> int:
        try:
            obj_name = obj._meta.table_name
            app_log.info(f"Count instance list: {obj_name}")
            # 默认 不查询 标记为 删除 的数据
            if not need_delete:
                # 判断 obj 是否有 is_delete 字段
                if hasattr(obj, "is_delete"):
                    app_log.debug(f"Count query add is_delete==False")
                    query = query.where(obj.is_delete==False)
            app_log.debug(f"Count query sql: {query.sql()}"[:MAX_LOG_SIZE])
            start_time = time.time()
            result = await self.db.objs.count(query)
            end_time = time.time()
            app_log.debug(f"Count instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        except Exception:
            result = 0
            app_log.error(traceback.format_exc())
        return result

    async def list_obj_with_pagination_by_query(self, obj, count_query, list_query, pagination, page_size, order_by_expr_list=None):
        if isinstance(order_by_expr_list, list):
            for expr in order_by_expr_list:
                list_query = list_query.order_by(expr)
        elif order_by_expr_list is not None:
            list_query = list_query.order_by(order_by_expr_list)
        count = await self.count_obj(obj, count_query)
        if isinstance(pagination, int):
            list_query = list_query.paginate(pagination, page_size)
        data = await self.list_obj(obj, list_query)
        return count, data

    async def update_obj_by_query(
        self, obj: Type[MT], query: ModelUpdate, need_delete: bool = False) -> int:
        try:
            obj_name = obj._meta.table_name
            app_log.info(f"Update instance: {obj_name}")
            # 默认 不更新 标记为 删除 的数据
            if not need_delete:
                # 判断 obj 是否有 is_delete 字段
                if hasattr(obj, "is_delete"):
                    app_log.debug(f"Update query add is_delete==False")
                    query = query.where(obj.is_delete==False)
            app_log.debug(f"Update sql: {query}"[:MAX_LOG_SIZE])
            start_time = time.time()
            count = await self.db.objs.execute(query)
            end_time = time.time()
            app_log.debug(f"Update instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        except Exception:
            count = 0
            app_log.error(traceback.format_exc())
        return count

    async def insert_on_conflict(self, obj: BaseModel, data_info, preserve, update):
        query = obj.insert(data_info)
        query = query.on_conflict(
                preserve=preserve,
                update=update
            )
        result = await self.db.objs.execute(query)
        return result

    async def delete_obj_by_query(
        self, obj: BaseModel, query: ModelDelete):
        try:
            obj_name = obj._meta.table_name
            app_log.info(f"delete model: {obj_name}")
            app_log.debug(f"Delete query sql: {query}"[:MAX_LOG_SIZE])
            start_time = time.time()
            result = await self.db.objs.execute(query)
            end_time = time.time()
            app_log.debug(f"Delete instance {obj_name} cost: {(end_time - start_time) * 1000}ms")
        except Exception:
            result = 0
            app_log.error(traceback.format_exc())
        return result


class DBAccess(BaseDBAccess):

    def build_query_with_sys(self, query, model, expressions=None):
        current_workspace = get_current_workspace()
        if is_model_version_control(model):
            # if current_workspace:
            #     user_uuid = current_workspace.user_uuid
            #     branch_uuid = current_workspace.branch_uuid
            # else:
            #     user_uuid, branch_uuid = "", ""
            expressions = expressions or []
            # if user_uuid and branch_uuid:
            #     expressions.extend([
            #         model.user_uuid==user_uuid, model.branch_uuid==branch_uuid])
            # else:
            #     expressions.extend([
            #         model.user_uuid=="", model.branch_uuid==""])
            expressions = reduce(operator.and_, expressions)
            query = query.where(
                expressions | (model.app_uuid==self.config.SYS_APP_UUID))
            # query.with_sys = True
        return query


    async def list_obj(self, obj: Type[MT], query: ModelSelect, as_dict: bool=True, need_delete: bool=False, ext_tenant: str = None ) -> List[MT]:
        return await super().list_obj(obj, query, as_dict=as_dict, need_delete=need_delete)

    async def select_policy(self, group_id: str, resource_id: str, action:int, app_uuid: str) -> Policy:
        model = Policy
        combine_model = Combine

        query = model.select().join(combine_model, join_type=JOIN.LEFT_OUTER,
                                        on=(model.app_uuid==combine_model.app_uuid)
                                ).where(
                                    model.resource_id==resource_id,
                                    model.action>=action,
                                    model.app_uuid==app_uuid,
                                    combine_model.user_uuid==group_id
                                )
        policy = await self.select_obj_by_query(model, query)

        if not policy and action == Action.SELECT:
            query = model.select().join(combine_model, join_type=JOIN.LEFT_OUTER,
                                            on=(model.app_uuid==combine_model.app_uuid)
                                    ).where(
                                        model.resource_id==resource_id,
                                        model.action>=action,
                                        model.app_uuid==app_uuid,
                                        combine_model.user_uuid==self.config.SYS_USER_UUID
                                    )
            policy = await self.select_obj_by_query(model, query)
        return policy

    async def create_policy(self, group_id, resource_id: str, action: int = Action.SELECT,
                            app_uuid: str=None, resource_type: int=None) -> Policy:
        model = Policy
        data = {
            model.group_id.name: group_id,
            model.resource_id.name: resource_id,
            model.action.name: action,
            model.app_uuid.name: app_uuid,
            model.type.name: resource_type
        }
        return await self.create_obj(model, **data)

    async def list_combine_by_user_uuid(self, user_uuid, app_uuid=None, is_owner=None,
                                        need_delete=False, team_uuid=None, team_must_null=False):
        model = Combine
        query = model.select()
        if isinstance(user_uuid, str):
            query = query.where(model.user_uuid==user_uuid)
        elif isinstance(user_uuid, list):
            query = query.where(model.user_uuid.in_(user_uuid))
        else:
            return []
        if app_uuid is not None:
            query = query.where(model.app_uuid==app_uuid)
        if is_owner is not None:
            query = query.where(model.is_owner==is_owner)
        if team_uuid is not None:
            query = query.where(model.team_uuid==team_uuid)
        else:
            if team_must_null:
                query = query.where(model.team_uuid.is_null())
        app_log.info(query)
        return await self.list_obj(model, query, need_delete=need_delete)

    async def list_combine_by_app_uuid(self, app_uuid, is_owner=None, team_uuid=None):
        model = Combine
        s_model = Sandbox
        u_model = SandboxUser
        sandbox_name = fn.GROUP_CONCAT(NodeList((s_model.sandbox_name, SQL("SEPARATOR ','")))
                                       ).alias("sandbox_name")
        sandbox_uuid = fn.GROUP_CONCAT(NodeList((s_model.sandbox_uuid, SQL("SEPARATOR ','")))
                                       ).alias("sandbox_uuid")
        fields = (
            Combine.app_uuid,
            Combine.user_uuid,
            Combine.is_owner,
            Combine.visible,
            Combine.publish_permission,
            Combine.team_uuid,
            Combine.permission,
            sandbox_name,
            sandbox_uuid
        )
        query = model.select(*fields).join(
                u_model, join_type=JOIN.LEFT_OUTER, on=(u_model.user_uuid==model.user_uuid)
            ).join(
                s_model,join_type=JOIN.LEFT_OUTER,
                on=((u_model.sandbox_uuid==s_model.sandbox_uuid) & (s_model.app_uuid==model.app_uuid))).where(
            model.app_uuid==app_uuid).group_by(model.user_uuid)
        if is_owner is not None:
            query = query.where(model.is_owner==is_owner)
        if team_uuid is not None:
            query = query.where(model.team_uuid==team_uuid)
        return await self.list_obj(model, query)

    async def list_combine_by_app_uuid_join_user(
        self, app_uuid, fields=None, is_owner=None, hide_mobile=False):
        model = Combine
        u_model = User
        if fields is None:
            fields = (
                model.user_uuid,
                model.is_owner,
                u_model.user_name,
                u_model.full_name,
                fn.insert(u_model.mobile_phone, 4, 4, "****").alias(u_model.mobile_phone.name) \
                    if hide_mobile else u_model.mobile_phone
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid).join(
            u_model, on=(model.user_uuid==u_model.user_uuid))
        if is_owner is not None:
            query = query.where(model.is_owner==is_owner)
        return await self.list_obj(model, query)

    async def bulk_update_combine(self, combine_infos: list, need_delete=False, **update_data):
        # 接口手动调用且不频繁
        for c in combine_infos:
            await self.update_combine(app_uuid=c.get("app_uuid"), user_uuid=c.get("user_uuid"),
                                      need_delete=need_delete, **update_data)

    async def update_combine(self, app_uuid, user_uuid, need_delete=False, **data):
        model = Combine
        query = model.update(**data).where(model.app_uuid==app_uuid, model.user_uuid==user_uuid)
        return await self.update_obj_by_query(model, query, need_delete=need_delete)

    async def create_app(self, user_uuid: str, **data) -> APP:
        model = APP
        data.update({"user_uuid": user_uuid,
                     "middle_user": user_uuid,
                     "create_time": time.time()})
        app_uuid = data.get("app_uuid")
        action = Action.DELETE
        combine_data = {
            Combine.is_owner.name: True,
            Combine.app_uuid.name: app_uuid,
            Combine.user_uuid.name: user_uuid
        }
        async with self.db.objs.atomic():
            app = await self.create_obj(model, **data)
            await self.create_policy(
                group_id=user_uuid,
                resource_id=app_uuid,
                action=action,
                app_uuid=app_uuid,
                resource_type=ResourceType.APP
            )
            await self.create_obj(Combine, **combine_data)
            return app

    async def get_app(self, **data) -> APP:
        model = APP
        return await self.get_obj(model, **data)

    async def get_app_by_app_uuid(self, app_uuid: str) -> APP:
        model = APP
        data = {
            model.app_uuid.name: app_uuid
        }
        return await self.get_app(**data)

    async def get_app_by_app_uuid_join_combine(self, app_uuid: str, user_uuid) -> APP:
        model = APP
        c_model = Combine
        fields = model._meta.sorted_fields[::]
        fields.extend([
            c_model.user_uuid,
            c_model.permission,
            c_model.publish_permission,
            c_model.team_uuid])
        query = model.select(*fields).join(c_model, on=(model.app_uuid==c_model.app_uuid)).where(
            model.app_uuid==app_uuid, c_model.user_uuid==user_uuid
        )
        return await self.select_obj_by_query(model, query, as_dict=True)

    async def is_app_admin(self, app_uuid, user_uuid):
        model = AppManager
        query = model.select().where(model.app_uuid==app_uuid, model.manager_uuid==user_uuid, model.is_delete==False)
        result = await self.db.objs.count(query)
        return result > 0

    async def is_app_admin_or_combiner(self, app_uuid, user_uuid):
        model = AppManager
        query = model.select().where(model.app_uuid==app_uuid, model.manager_uuid==user_uuid, model.is_delete==False)
        result = await self.db.objs.count(query)

        c_model = Combine
        c_query = c_model.select(1).where(c_model.app_uuid==app_uuid, c_model.user_uuid==user_uuid, c_model.is_delete==False)
        c_result = await self.db.objs.count(c_query)
        return result + c_result > 0

    async def list_app_by_user_uuid(self, user_uuid: str, order_by_query=None, time_limit=None) -> list:
        # 展示user所有者apps
        model = APP
        query = model.select(
            model.id,
            model.app_uuid,
            model.app_name,
            model.app_description,
            model.app_color,
            model.app_icon,
            model.status,
            model.create_time
            ).where(model.user_uuid==user_uuid)
        if order_by_query:
            query = query.order_by(order_by_query)
        if time_limit is not None:
            earliest_delete_time = time.time() - time_limit
            query = query.where(model.delete_time > earliest_delete_time)
        return await self.list_obj(model, query)

    async def list_app_by_app_uuid_list(self, app_uuid_list: list, order_by_query=None, time_limit=None,
                                        as_dict=True, count_combine=False):
        model = APP
        wx_model = AuthorizedWX
        case = Case(
            None,
            [(wx_model.app_uuid.is_null(), False)],
            True
        )
        query = model.select(
            model.id,
            model.app_uuid,
            model.app_name,
            model.user_uuid,
            model.app_description,
            model.app_color,
            model.app_icon,
            model.status,
            model.create_time,
            case.alias("bind_wx")
            ).join(wx_model, join_type=JOIN.LEFT_OUTER,
                    on=((model.app_uuid==wx_model.app_uuid) & (wx_model.is_delete==False))
                   ).where(model.app_uuid.in_(app_uuid_list))
        if order_by_query:
            query = query.order_by(order_by_query)
        if time_limit is not None:
            earliest_delete_time = time.time() - time_limit
            query = query.where(model.delete_time > earliest_delete_time)
        app_list = await self.list_obj(model, query, as_dict=as_dict)
        if count_combine:  # 查询协作者人数
            c_model = Combine
            c_query = c_model.select(
                c_model.app_uuid,
                fn.COUNT(Combine.user_uuid).alias("combine_count"),
                c_model.team_uuid
            ).where(c_model.is_owner == False).group_by(c_model.app_uuid)
            combine_list = await self.list_obj(c_model, c_query)
            combine_dic = {x.get("app_uuid"): x for x in combine_list}
            if as_dict:
                [x.update(
                    {
                        "combine_count": combine_dic.get(x.get("app_uuid"), dict()).get("combine_count", 0),
                        "team_uuid": combine_dic.get(x.get("app_uuid"), dict()).get("team_uuid")
                        }
                    ) for x in app_list]
            else:
                # TODO obj格式
                ...
        return app_list

    async def list_app_with_editor(self, user_uuid, hide_when_design=False):
        """
        hide_when_design: 仅查询设计时可见的
        """
        model = APP
        e_model = AppEditor
        m_model = AppManager
        fields = [model.id,
            model.app_uuid,
            model.app_name,
            model.app_description,
            model.app_color,
            model.app_icon,
            model.status,
            model.create_time]
        m_fields = fields + [SQL("1 as is_owner")]
        m_query = m_model.select(*m_fields).join(model, on=(model.app_uuid==m_model.app_uuid)
        ).where(m_model.manager_uuid==user_uuid, m_model.is_delete==False, model.is_delete==False)
        e_fields = fields + [SQL("0 as is_owner")]
        e_query = e_model.select(*e_fields).join(model, on=(model.app_uuid==e_model.app_uuid)
        ).where(e_model.user_uuid==user_uuid, e_model.is_delete==False, model.is_delete==False)
        if hide_when_design:
            m_query = m_query.join(Combine, on=(Combine.app_uuid==model.app_uuid)).where(Combine.visible==True, Combine.user_uuid==user_uuid, Combine.is_delete==False, Combine.team_uuid.is_null())
            e_query = e_query.join(Combine, on=(Combine.app_uuid==model.app_uuid)).where(Combine.visible==True, Combine.user_uuid==user_uuid, Combine.is_delete==False, Combine.team_uuid.is_null())
        query = m_query | e_query
        query = query.select_from(
            query.c.id,
            query.c.app_uuid,
            query.c.app_name,
            query.c.app_description,
            query.c.app_color,
            query.c.app_icon,
            query.c.is_owner,
            query.c.create_time
        ).group_by(query.c.app_uuid).order_by(query.c.is_owner.desc(), query.c.create_time.desc(),
                                              query.c.id.desc())

        return await self.list_obj(model, query, need_delete=True)  # 该query不需要添加where is_delete=0，用need_delete=True来表示不用修改query

    async def list_app_by_combine_user(
        self, user_uuid_list, is_owner=None, hide_when_design=False, team_uuid=None):
        model = Combine
        app_model = APP
        fields = [app_model.id,
            app_model.app_uuid,
            app_model.app_name,
            app_model.app_description,
            app_model.app_color,
            app_model.app_icon,
            app_model.status,
            app_model.create_time]
        query = app_model.select(*fields).join(model, on=(model.app_uuid==app_model.app_uuid)).where(
            model.user_uuid.in_(user_uuid_list) & (model.is_delete == 0)).order_by(app_model.create_time.desc(), app_model.id.desc())
        if is_owner is not None:
            query = query.where(model.is_owner==is_owner)
        if hide_when_design:
            query = query.where(model.visible==True)
        # if team_uuid is not None:
        query = query.where(model.team_uuid==team_uuid)
        return await self.list_obj(app_model, query, need_delete=False)

    async def list_app_ext_tenants(self, app_uuid, fields: list=None, need_delete: bool=False):
        model = AppEditor
        if not fields:
            fields = [
                model.tenant_uuid,
                model.app_uuid
            ]

        query = model.select(*fields).where(model.app_uuid == app_uuid)
        return await self.list_obj(model, query, need_delete=need_delete)

    async def list_app_policy_by_user(self, app_uuid, user_uuid):
        e_model = AppEditor
        t_model = Tenant
        fields = (t_model.tenant_uuid, t_model.tenant_name)
        query = e_model.select(*fields).join(t_model, on=(e_model.tenant_uuid==t_model.tenant_uuid)
        ).where(e_model.app_uuid==app_uuid, e_model.user_uuid==user_uuid, t_model.is_delete==False, e_model.is_delete==False)
        return await self.list_obj(e_model, query)

    async def list_app_policy(self, app_uuid):
        model = Policy
        query = model.select().where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query)

    async def is_app_editor(self, app_uuid, user_uuid, ext_tenant):
        e_model = AppEditor
        t_model = Tenant
        fields = (t_model.tenant_uuid, t_model.tenant_name)
        query = e_model.select(*fields).join(t_model, on=(e_model.tenant_uuid==t_model.tenant_uuid)
        ).where(e_model.app_uuid==app_uuid, e_model.user_uuid==user_uuid, e_model.tenant_uuid==ext_tenant, t_model.is_delete==False, e_model.is_delete==False)
        return await self.select_obj_by_query(e_model, query)

    async def get_app_setting(self, app_uuid: str) -> APPSetting:
        model = APPSetting
        data = {
            model.app_uuid.name: app_uuid,
        }
        return await self.get_obj(model, **data)

    async def create_app_setting(self, app_uuid: str) -> APPSetting:
        model = APPSetting
        data = {
            model.app_uuid.name: app_uuid,
            model.permission_check.name: False,
            model.anonymous.name: False
        }
        app_setting = await self.get_obj(model, need_delete=True, **data)
        if app_setting:
            data.update({
                model.is_delete.name: False
            })
            query = model.update(**data).where(model.app_uuid==app_uuid)
            await self.update_obj_by_query(model, query, need_delete=True)
            return app_setting
        else:
            return await self.create_obj(model, **data)

    async def update_app_setting(self, app_uuid: str, **data):
        model = APPSetting
        query = model.update(**data).where(model.app_uuid==app_uuid)
        return await self.update_obj_by_query(model, query)

    async def update_app(self, app_uuid: str, **data):
        model = APP
        query = model.update(**data).where(model.app_uuid==app_uuid)
        return await self.update_obj_by_query(model, query)

    async def update_app_revision(self, app_uuid: str, app_revision: str, env: str, data=dict()):
        if env == "test":
            data.update({APP.app_revision_test.name: app_revision})
        elif not env:
            data.update({APP.app_revision.name: app_revision})
        return await self.update_app(app_uuid, **data)

    async def select_app_revision(self, app_uuid: str, env: str):
        model = APP
        fields = [model.app_uuid, model.app_revision, model.app_revision_test]
        query = model.select(*fields).where(model.app_uuid == app_uuid)
        return await self.list_obj(model, query, need_delete=True)

    async def create_module(self, user_uuid: str, **data) -> Module:
        model = Module
        module_uuid = data.get("module_uuid")
        action = Action.DELETE  # 此处需要用用户身份权限来确认
        async with self.db.objs.atomic():
            module = await self.create_obj(model, **data)
            await self.create_policy(
                group_id=user_uuid,
                resource_id=module_uuid,
                action=action,
                app_uuid=data.get("app_uuid"),
                resource_type=ResourceType.MODULE
            )
            return module

    async def delete_all_by_module_uuid(self, module_uuid: str):
        table_list = [
            Document, RelationshipBasic, UserRoleContent, ModelIndex, ModelField, ModelBasic, ModuleRole,
            EnumTable, Event, RuleChain
            ]
        result_list = []
        for model in table_list:
            delete_column = model.is_delete.name
            update_info = {delete_column: True}
            query = model.update(**update_info).where(
                model.module_uuid==module_uuid
            )
            result = await self.update_obj_by_query(model, query)
            result_list.append(result)

    async def delete_module_by_module_uuid(self, module_uuid):
        async with self.db.objs.atomic():
            await self.delete_document(module_uuid=module_uuid)

    async def delete_document_by_document_uuid(
            self, document_uuid, document_type):
        base_delete = [Document]
        document_type_to_model = {
            DocumentType.PAGE: [Page],
            DocumentType.ENUM: [EnumTable],
            DocumentType.JSON: [JsonTable],
            DocumentType.IMAGE: [ImageTable],
            DocumentType.CONST: [ConstTable],
            DocumentType.FUNC: [Func],
            DocumentType.WORKFLOW: [Workflow],
            DocumentType.CONNECTOR: [Connector],
            DocumentType.EXCEL_TEMPLATE: [TemplateTable],
            DocumentType.EXPORT_TEMPLATE: [ExportTemplate],
            DocumentType.DIR: [],
            DocumentType.APPROVALFLOW: [Workflow],
            DocumentType.RULECHAIN: [RuleChain],
            DocumentType.PRINT: [Print],
            DocumentType.LABEL_PRINT: [LabelPrint],
            DocumentType.RESTFUL: [RestfulTable],
            DocumentType.MODULE_THEME: [],
            DocumentType.PERMISSION_CONFIG: [Resource, Tag],
            DocumentType.APP_LAYOUT: [],
            DocumentType.PY_MODULE: []
        }
        if document_type not in document_type_to_model:
            raise LemonDesignError(IDECode.DOCUMENT_TYPE_NOT_ALLOW_DEL)
        delete_models = document_type_to_model.get(document_type, [])
        delete_models.extend(base_delete)
        async with self.db.objs.atomic():
            for model in delete_models:
                where_expr = (model.document_uuid == document_uuid)
                await self.delete_document(model=model, where_expr=where_expr)

    async def delete_document(self, *, model=None, where_expr=None, module_uuid=None):
        async def _fake_delete_model(m):
            query = m.update(**{m.is_delete.name: True}).where(where_expr)
            app_log.info(query)
            await self.update_obj_by_query(m, query)
        async def _true_delete_model(m):
            query = m.delete().where(where_expr)
            await self.delete_obj_by_query(m, query)

        fake_delete_list = [
            Document, RelationshipBasic, UserRoleContent, ModelIndex,
            ModelField, ModelBasic, ModuleRole, EnumTable, Event, RuleChain,
            Module, Page, JsonTable, ImageTable, ConstTable, Func, Workflow,
            TemplateTable, Print, LabelPrint, RestfulTable, Resource, Tag]  # TODO 改成真删除
        true_delete_list = [Connector, ExportTemplate, TemplateTable]
        async with self.db.objs.atomic():
            if model:
                if model in fake_delete_list:
                    await _fake_delete_model(model)
                elif model in true_delete_list:
                    await _true_delete_model(model)
            else:
                # 全删
                if not module_uuid:
                    raise LemonDesignError(IDECode.DELETE_MODULE_ERROR)
                for model in fake_delete_list:
                    where_expr = (model.module_uuid==module_uuid)
                    await _fake_delete_model(model)
                for model in true_delete_list:
                    where_expr = (model.module_uuid==module_uuid)
                    await _true_delete_model(model)

    async def update_module_by_module_uuid(self, module_uuid: str, need_delete=False, **data) -> int:
        model = Module
        query = model.update(**data).where(
            model.module_uuid==module_uuid
        )
        return await self.update_obj_by_query(model, query, need_delete=need_delete)

    async def get_module(self, **data):
        model = Module
        return await self.get_obj(model, **data)

    async def get_module_by_module_uuid(self, module_uuid: str) -> Module:
        model = Module
        data = {
            model.module_uuid.name: module_uuid
        }
        return await self.get_module(**data)

    async def get_module_by_app_uuid_module_name(self, app_uuid: str, module_name: str) -> Module:
        model = Module
        data = {
            model.app_uuid.name: app_uuid,
            model.module_name.name: module_name
        }
        return await self.get_module(**data)

    async def get_module_by_app_module_uuid(self, app_uuid: str, module_uuid: str, need_delete: bool = False) -> Module:
        model = Module
        data = {
            model.app_uuid.name: app_uuid,
            model.module_uuid.name: module_uuid
        }
        return await self.get_module(need_delete=need_delete, **data)

    async def list_module_by_app_uuid(
            self, app_uuid: str, as_dict: bool = True, with_sys=False, with_policy=False, ext_tenant=None,
            need_delete=False, module_uuid=None) -> list:
        model = Module
        p_model = ExtPolicy

        is_sys_module = model.app_uuid == self.config.SYS_APP_UUID
        is_sys_module = is_sys_module.alias("is_sys_module")
        fields = [
            model.id,
            model.module_uuid,
            model.module_name,
            model.module_type,
            is_sys_module,
            model.extend_from,
            model.extension_type,
            model.module_description,
            Extension.publisher_name]
        if need_delete:
            fields.append(model.is_delete)
        if with_policy:
            new_fields = list(fields) + [fn.IFNULL(p_model.visible, False).alias("visible"), p_model.additional]
            query = model.select(*new_fields).join(
                p_model, join_type=JOIN.LEFT_OUTER, on=(model.module_uuid == p_model.resource_id)
                ).where((p_model.is_delete == False) | (p_model.is_delete.is_null(True)))
        else:
            # if ext_tenant:
            #     new_fields = list(fields) + [p_model.additional]
            #     query = model.select(*new_fields).join(
            #         p_model, join_type=JOIN.LEFT_OUTER, on=(model.module_uuid == p_model.resource_id)
            #     ).where(p_model.is_delete == False, p_model.visible == True)
            # else:
            query = model.select(*fields)
        query = query.join(
            Extension, join_type=JOIN.LEFT_OUTER, on=(model.extend_from == Extension.id)
            )
        if with_sys:
            expressions = [model.app_uuid == app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid == app_uuid)
        if module_uuid:
            query = query.where(model.module_uuid == module_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_app_imported_module(self, app_uuid: str, extend_from=None, as_dict: bool=True) -> list:
        model = Module
        fields = (
            model.id,
            model.module_uuid,
            model.module_name,
            model.module_type,
            model.extend_from
        )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        if extend_from is None:
            query = query.where(model.extend_from.is_null(False))
        else:
            query = query.where(model.extend_from==extend_from)

        return await self.list_obj(model, query, as_dict=as_dict)

    async def list_document_by_module_uuid(
        self, module_uuid: str, fields: list = None, first_level=False,
        as_dict: bool = True, need_delete: bool = False, order_by_expr=None,
        app_uuid=None) -> list:
        model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.document_puuid,
                model.document_name,
                model.document_type,
                model.document_path,
                model.document_number,
                model.document_version,
                model.update_timestamp,
                model.order,
                model.id
            )
        query = model.select(*fields).where(
            model.module_uuid==module_uuid)
        if app_uuid:
            query= query.where(model.app_uuid==app_uuid)
        if first_level:
            query = query.where(model.document_path=="/")
        if order_by_expr:
            query = query.order_by(order_by_expr)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_document_by_module_document_name(self, app_uuid, module_document_names, as_dict=True):
        current_workspace = get_current_workspace()
        branch_uuid = current_workspace.branch_uuid if current_workspace else ""
        model = Document
        m_model = Module
        module_document_field = fn.CONCAT_WS("|", m_model.module_name, model.document_name)
        fields = (
            module_document_field.alias("module_document"),
            model.document_uuid,
            model.document_name,
            model.module_uuid,
            m_model.module_name,
            model.document_type,
            model.document_version,
            model.document_path,
            model.document_puuid
        )
        query = model.select(*fields).join(m_model, on=((model.module_uuid==m_model.module_uuid)&(model.branch_uuid==m_model.branch_uuid)&(model.user_uuid==m_model.user_uuid)),
            join_type=JOIN.LEFT_OUTER).where(module_document_field.in_(module_document_names),
                model.app_uuid==app_uuid, ((m_model.branch_uuid==branch_uuid)|(model.module_uuid=="")))
        query = query.order_by(model.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=True)

    async def list_document_content_by_module_uuid(
        self, module_uuid: str, fields: list = None, first_level=False,
        document_puuid=None, app_uuid=None, as_dict: bool = True, need_delete: bool = False) -> list:
        model = Document
        c_model = DocumentContent
        if fields is None:
            fields = (
                model.document_uuid,
                c_model.document_content,
                c_model.source_uuid
            )
        query = model.select(*fields).join(
            c_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==c_model.document_uuid)
            ).where(
                model.module_uuid==module_uuid)
        if first_level:
            query = query.where(model.document_path=="/")
        if document_puuid is not None:
            query = query.where(model.document_puuid==document_puuid)
        if app_uuid is not None:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_module_by_app_uuid_module_type(
        self, app_uuid: str, module_type: int, as_dict: bool = True,
        need_all_fields=False) -> list:
        model = Module
        if not need_all_fields:
            fields = (
                model.module_uuid,
                model.module_name,
                model.module_type
            )
        else:
            fields = model._meta.sorted_fields
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        if module_type is not None:
            query = query.where(module_type==module_type)
        return await self.list_obj(model, query, as_dict=as_dict)

    async def create_document(
            self, user_or_uuid: str, ext_tenant: str = None, name_exist_handle="rename", **data) -> Document:

        async def lock_with_document_name(d_name, auto_unlock=True):
            app_log.info(f"a: {app_uuid}, m: {module_uuid}, n: {d_name}")
            resource_id = hashlib.md5(
                "_".join([app_uuid, module_uuid, d_name]).encode()).hexdigest()
            app_log.info(f"resource_id: {resource_id}")
            try:
                lock: aioredlock.Lock = await GlobalVars.engine.resource_lock.lock(
                    resource_id, lock_timeout=1)
                app_log.info(f"lock: {lock}")
                if LemonContextVar.atomic_resource_locks.get() is None:
                    LemonContextVar.atomic_resource_locks.set([])
                atomic_resource_locks = LemonContextVar.atomic_resource_locks.get()
                atomic_resource_locks.append((lock, auto_unlock))
                return lock
            except (BaseException, Exception):
                app_log.info(traceback.format_exc())
                return None

        model = Document
        document_uuid = data.get("document_uuid")
        group_id = user_or_uuid

        # 判断模块父目录下文档能否重名
        app_uuid = data.get("app_uuid")
        module_uuid = data.get("module_uuid")
        document_puuid = data.get("document_puuid")
        document_name = data.get("document_name")
        document_rname = document_name
        document_type = data.get("document_type")
        if not app_uuid:
            raise LemonDesignError(IDECode.ARGS_ERROR)
        # 云函数重名判断特殊，不能带有一些特殊符号
        if document_type == DocumentType.FUNC:
            document_name = check_document_func_name(document_name)
            if not document_name:
                raise LemonDesignError(IDECode.FUNC_NAME_FAILED)
        elif document_type == DocumentType.PY_MODULE:
            document_name = check_document_func_name(document_name)
            if not document_name:
                raise LemonDesignError(IDECode.PY_MODULE_NAME_FAILED)
        else:
            document_name = check_document_name(document_name)
            if not document_name:
                raise LemonDesignError(IDECode.DOCUMENT_NAME_FAILED)
        document_number = 0
        if document_type == DocumentType.DIR:
            name_query_data = {
                Document.app_uuid.name: app_uuid,
                Document.document_name.name: document_name,
                Document.document_puuid.name: document_puuid
            }
        else:
            name_query_data = {
                Document.document_name.name: document_name,
                Document.app_uuid.name: app_uuid,
            }

        # 并发新建文件时，存在创建出多个同名文件的问题
        document_lock: aioredlock.Lock = await lock_with_document_name(document_name)
        if document_lock is None:
            raise LemonDesignError(IDECode.COPY_DOCUMENTS_NEED_TEN_SECONDS)

        name_exists_document = await self.select_document_by_module_uuid(
                module_uuid, **name_query_data)
        name_pk = name_exists_document.get("id")
        if name_pk is not None and name_exist_handle:
            if name_exist_handle == "stop":  # 对存在重名文件的不同处理
                raise LemonDesignError(IDECode.DOCUMENT_NAME_EXISTS)
            if document_type == DocumentType.DIR:
                r_name_query_data = {
                    Document.app_uuid.name: app_uuid,
                    Document.document_rname.name: document_name,
                    Document.document_puuid.name: document_puuid}
            else:
                r_name_query_data = {Document.document_rname.name: document_name}
            r_name_exists_document = await self.select_document_by_module_uuid(
                module_uuid, **r_name_query_data)
            r_name_pk = r_name_exists_document.get("id")
            if r_name_pk is None:
                document_name = document_name + "_1"
                document_number = 1
            else:
                # min_number = r_name_exists_document.get("min_number")
                max_number = r_name_exists_document.get("max_number")
                document_number = max_number + 1
                if document_number > 0:
                    document_name = document_name + "_" + str(document_number)
            # 新的名字，依然需要判断是否已重名
            # 并且新的名字，需要保持10秒钟（基本等于接口超时时间）内不会被自动 unlock
            # 因为两个事务先后执行时，获取到的新名字会是一样的
            # 这样当第一个事务 unlock 新名字的锁，第二个事务恰好获取到锁的话
            # 依然会有重名的可能，这种情况只能让 新名字的锁 保持一个接口的超时时间了
            document_lock: aioredlock.Lock = await lock_with_document_name(
                document_name, auto_unlock=False)
            if document_lock is None:
                raise LemonDesignError(IDECode.DOCUMENT_NAME_EXISTS)

        order = data.get("order")
        if order is None:
            scope_last_order = await self.get_scope_last_document_order(
                module_uuid, document_puuid)
            order = int(scope_last_order) + 1.0  # 减少小数部分
        data.update({
            model.document_name.name: document_name,
            model.document_rname.name: document_rname,
            model.document_number.name: document_number,
            model.order.name: order
        })
        app_log.info(f"data: {data}")

        action = Action.DELETE  # 此处需要用用户身份权限来确认
        async with self.db.objs.atomic():
            document = await self.create_obj(model, **data)
            await self.create_policy(
                group_id=group_id,
                resource_id=document_uuid,
                action=action,
                app_uuid=app_uuid,
                resource_type=ResourceType.DOCUMENT
            )
            return document

    async def update_document_by_document_uuid(self, document_uuid: str, ext_tenant: str = None, need_delete=False, **data) -> int:
        model = Document
        query = model.update(**data).where(
            model.document_uuid==document_uuid
        )
        return await self.update_obj_by_query(model, query, need_delete=need_delete)

    async def create_document_by_document_type(self, **data):
        model = Document
        return await self.create_obj(model, **data)

    async def update_model_by_document_uuid(self, document_uuid: str, model_type, ext_tenant: str = None, need_delete=False, **data) -> int:
        model_list = self.convert_document_type_to_model_list(model_type)
        app_log.info(f"update_model_by_document_uuid:{model_list}")
        count = 0
        for model in model_list:
            query = model.update(**data).where(
                model.document_uuid==document_uuid
            )
            count += await self.update_obj_by_query(model, query, need_delete=need_delete)
        return count

    async def update_all_document_inside_dir(self, target_module_uuid, all_document_dict, need_delete=False):
        data = {Document.module_uuid.name: target_module_uuid}
        model = Document
        count = 0
        for document_type, doucument_dict in all_document_dict.items():
            document_uuid_list = [document_uuid for document_uuid in doucument_dict.keys()]
            query = model.update(**data).where(
                        model.document_uuid.in_(document_uuid_list)
                    )
            count = await self.update_obj_by_query(model, query, need_delete=need_delete)
            other_model_list = self.convert_document_type_to_model_list(document_type)
            if other_model_list:
                for other_model in other_model_list:
                    other_query = other_model.update(**data).where(
                            other_model.document_uuid.in_(document_uuid_list)
                        )
                    count += await self.update_obj_by_query(other_model, other_query, need_delete=need_delete)
        return count

    def convert_document_type_to_model_list(self, document_type):
        need_update_model_dict = {
            EntityType.PAGE: [Page], EntityType.FUNC: [Func], EntityType.PRINT: [Print],
            EntityType.IMAGE: [ImageTable], EntityType.WORKFLOW: [Workflow],
            EntityType.LABEL_PRINT: [LabelPrint], EntityType.RESTFUL: [RestfulTable],
            EntityType.ENUM: [EnumTable], EntityType.APPROVALFLOW: [Workflow]
            }
        model = need_update_model_dict.get(document_type)
        return model

    async def update_document_by_module_uuid(self, module_uuid: str,  **data) -> int:
        model = Document
        query = model.update(**data).where(
            model.module_uuid==module_uuid
        )
        return await self.update_obj_by_query(model, query)

    async def update_document_by_document_path(self, document_path: str, **data) -> int:
        model = Document
        query = model.update(**data).where(
            model.document_path.startswith(document_path)
        )
        return await self.update_obj_by_query(model, query)

    # async def remove_document_order_by_father_uuid(self, )

    async def get_document_order_by_father_uuid(self, **data):
        model = DocumentOrder
        return await self.get_obj(model, **data)

    async def update_document_order_by_father_uuid(self, father_uuid, **data):
        model = DocumentOrder
        query = model.update(**data).where(
            model.father_uuid == father_uuid
        )
        return await self.update_obj_by_query(model, query)

    async def create_document_order(self, **data):
        model = DocumentOrder
        return await self.create_obj(model, **data)

    async def get_document(self, ext_conditions=None, need_delete=False, **data):
        model = Document
        model_ext = Document.alias("ext_doc")
        ext_tenant = data.pop("ext_tenant", None)
        query = model.select()
        if not need_delete:
            # 判断 obj 是否有 is_delete 字段
            if hasattr(model, "is_delete"):
                app_log.debug(f"List query add is_delete==False")
                query = query.where(model.is_delete==False)
        conditions = [(getattr(model, k) == v)
                                   for k, v in data.items()]
        if ext_conditions:
            conditions.extend(ext_conditions)
        query = query.where(*conditions)
        # if ext_tenant:
        #     ext_data = copy.copy(data)
        #     if model.document_uuid.name in data:
        #         doc_uuid = ext_data.pop(model.document_uuid.name)
        #         ext_data.update({model.source.name: doc_uuid})
        #     query_ext = model_ext.select().join(model, on=(model.document_uuid==model_ext.source))
        #     is_delete = ext_data.pop(model.is_delete.name, False)
        #     ext_data.update({model_ext.is_delete.name: is_delete})
        #     conditions = [(getattr(model_ext, k) == v)
        #                             for k, v in ext_data.items()]
        #     query_ext = query_ext.where(*conditions)
        #     # query_ext._returning.append(model_ext.source.alias(model_ext.document_uuid.name))
        #     query =  query_ext | query
        #     query = query.order_by(query_ext.c.source.desc())
        query = query.limit(1)

        app_log.info(f"get_document_query: {query}")
        try:
            result = await self.db.objs.execute(query)
            return list(result)[0]
        except IndexError:
            return None
        # return await self.get_obj(model, **data)

    async def get_document_status_by_document_uuid(self, document_uuid: str) -> Document:
        model = DocumentContent
        d_model = DocumentDraft
        content_field = fn.COALESCE(fn.IF(model.timestamp > d_model.timestamp, None, "unchecked"), None)
        query = model.select(content_field.alias('status'),
                             model.id,
                             model.document_uuid,
                             model.user_uuid,
                             model.branch_uuid).join(d_model, on=(model.document_uuid == d_model.document_uuid),
                                                     join_type=JOIN.LEFT_OUTER).where(
                                                     model.document_uuid == document_uuid)
        return await self.select_obj_by_query(model, query, as_dict=True)

    async def get_document_by_document_uuid(self, document_uuid: str, ext_tenant: str = None, need_delete=False) -> Document:
        model = Document
        data = {
            model.document_uuid.name: document_uuid,
            model.ext_tenant.name: ext_tenant
        }
        return await self.get_document(need_delete=need_delete, **data)

    async def get_document_by_document_puuid(self, document_puuid: str) -> Document:
        model = Document
        data = {
            model.document_puuid.name: document_puuid
        }
        return await self.get_document(**data)

    async def get_document_by_module_uuid_document_name(self, module_uuid: str, document_name: str) -> Document:
        model = Document
        data = {
            model.module_uuid.name: module_uuid,
            model.document_name.name: document_name
        }
        return await self.get_document(**data)

    async def get_document_by_app_uuid_document_name(self, app_uuid: str, document_name: str) -> Document:
        model = Document
        data = {
            model.app_uuid.name: app_uuid,
            model.document_name.name: document_name
        }
        return await self.get_document(**data)

    async def get_document_by_module_uuid_document_type(
        self, module_uuid, document_type, need_delete=False) -> Document:
        model = Document
        data = {
            model.module_uuid.name: module_uuid,
            model.document_type.name: document_type
        }
        return await self.get_document(need_delete=need_delete, **data)

    async def get_common_document_by_module_uuid(self, module_uuid) -> Document:
        model = Document
        data = {
            model.module_uuid.name: module_uuid,
            "ext_conditions": [(model.document_type.not_in(DocumentType.SYSTEM_TYPE))]
        }
        return await self.get_document(**data)


    async def select_document_by_module_uuid(
        self, module_uuid: str, **query_data: dict,) -> Document:
        model = Document
        fields = (
            model,
            fn.MIN(model.document_number).alias("min_number"),
            fn.MAX(model.document_number).alias("max_number"))
        query = model.select(*fields).where(model.module_uuid==module_uuid)
        for query_key, query_value in query_data.items():
            app_log.info(f"query_key: {query_key}, query_value: {query_value}")
            query_column = getattr(model, query_key)
            if query_column:
                query = query.where(query_column==query_value)
        return await self.select_obj_by_query(model, query)

    async def get_document_by_document_puuid_name(self, document_puuid: str, document_name: str) -> Document:
        model = Document
        data = {
            model.document_puuid.name: document_puuid,
            model.document_name.name: document_name
        }
        return await self.get_document(**data)

    async def select_document_ext_by_document_uuid(self, source_uuid, ext_tenant):
        model = Document
        query = model.select().where(model.source==source_uuid, model.ext_tenant==ext_tenant)
        return await self.select_obj_by_query(model, query, as_dict=False)

    async def list_document_by_module_document_puuid(
        self, module_uuid: str, document_puuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None,
        with_policy: bool = False, ext_conditions: list = list(), order_by_expr=None, limit: Optional[int] = None) -> list:
        model = Document
        p_model = ExtPolicy
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.document_name,
                model.document_type,
                model.document_path,
                model.document_number,
                model.document_version,
                model.update_timestamp,
                model.order,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        if with_policy:
            new_fields = list(fields) + [fn.IFNULL(p_model.visible, False).alias("visible"), p_model.additional]
            query = model.select(*new_fields).join(p_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==p_model.resource_id)
            ).where((p_model.is_delete==False)|(p_model.is_delete.is_null(True)))
        else:
            # if ext_tenant:
            #     model_alias = Document.alias("doc_ext")
            #     new_fields = (
            #         model.app_uuid,
            #         model.module_uuid,
            #         model.document_uuid,
            #         model.document_name,
            #         model.document_type,
            #         model.document_path,
            #         model.document_number,
            #         model_alias.document_version,
            #         model_alias.update_timestamp,
            #         model.ext_tenant.is_null(False).alias("is_extension"),
            #         p_model.additional
            #         )
            #     query = model.select(*new_fields).join(model_alias, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==model_alias.source)
            #     ).join(p_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==p_model.resource_id)
            #     ).where(p_model.is_delete==False, p_model.visible==True, ((model_alias.ext_tenant==ext_tenant)|(model_alias.ext_tenant.is_null(True))))
            # else:
            query = model.select(*fields)
        if ext_conditions:
            query = query.where(*ext_conditions)
        query = query.where(model.module_uuid==module_uuid)
        if document_puuid is not None:
            query = query.where(model.document_puuid==document_puuid)
        if order_by_expr is not None:
            query = query.order_by(order_by_expr)
        if limit:
            query = query.limit(1)
        # query = query.order_by(SQL('{}=9 desc'.format(model.document_type.column_name)), model.document_type, model.create_timestamp )
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_page_by_module_document_puuid(self, module_uuid, document_uuid, ext_tenant):
        ext_conditions = [Document.document_type.in_([DocumentType.PAGE, DocumentType.DIR])]
        return await self.list_document_by_module_document_puuid(module_uuid, document_uuid, ext_tenant, ext_conditions=ext_conditions)

    async def update_page_by_document_uuid(self, document_uuid: str, **data) -> int:
        model = Page
        query = model.update(**data).where(
            model.document_uuid==document_uuid
        )
        return await self.update_obj_by_query(model, query)

    async def list_css_style_by_app_uuid(self, app_uuid):
        model = DocumentContent
        page_model = Page
        page_style_field = fn.JSON_EXTRACT(
            model.document_content, '$.pageStyles')
        fields = (
            page_style_field.alias("pageStyles"),
            model.document_uuid,
            page_model.page_uuid
        )
        query = model.select(*fields).join(
            page_model, join_type=JOIN.LEFT_OUTER,
            on=(model.document_uuid == page_model.document_uuid)
        ).where(
            page_model.app_uuid == app_uuid, page_style_field.is_null(False), page_model.is_delete==False)
        return await self.list_obj(model, query)

    async def list_page_url_by_app_uuid(self, app_uuid):
        model = DocumentContent
        page_model = Page
        module_model = Module
        document_model = Document
        page_url_field = fn.JSON_EXTRACT(model.document_content, '$.custom_path')
        fields = (
            page_url_field.alias("custom_path"),
            model.document_uuid,
            page_model.page_uuid,
            page_model.page_name,
            module_model.module_name,
            document_model.document_name
        )
        query = model.select(*fields).join(
            page_model, join_type=JOIN.LEFT_OUTER,
            on=(model.document_uuid == page_model.document_uuid)
        ).join(
            module_model, join_type=JOIN.LEFT_OUTER,
            on=(page_model.module_uuid == module_model.module_uuid)
        ).join(
            document_model, join_type=JOIN.LEFT_OUTER,
            on=(model.document_uuid == document_model.document_uuid)
        ).where(
            page_model.app_uuid == app_uuid, page_url_field.is_null(False), page_model.is_delete==False)
        return await self.list_obj(model, query)

    async def list_page_by_url_app_uuid(self, app_uuid: str, url_list: List[str]):
        model = DocumentContent
        page_model = Page
        page_url_field = fn.JSON_EXTRACT(model.document_content, '$.custom_path.path')
        enable_custom_path = fn.JSON_EXTRACT(model.document_content, '$.custom_path.show')
        fields = (
            page_url_field.alias("custom_path"),
            enable_custom_path.alias("custom_path_enabled"),
            model.document_uuid,
            page_model.page_uuid,
            page_model.page_name,
        )
        query = model.select(*fields).join(
            page_model,
            on=(model.document_uuid == page_model.document_uuid)
        ).where(
            page_model.app_uuid == app_uuid, page_model.is_delete == False,
            page_url_field.in_(url_list), enable_custom_path == peewee.SQL("true")
        )
        return await self.list_obj(model, query)

    async def list_page_with_url_by_app_uuid(self, app_uuid):
        model = DocumentContent
        page_model = Page
        module_model = Module
        document_model = Document
        page_url_field = fn.JSON_EXTRACT(model.document_content, '$.custom_path')
        fields = (
            page_url_field.alias("custom_path"),
            model.document_uuid,
            page_model.page_uuid,
            page_model.page_name,
            module_model.module_name,
            document_model.document_name
        )
        query = model.select(*fields).join(
            page_model, join_type=JOIN.LEFT_OUTER,
            on=(model.document_uuid == page_model.document_uuid)
        ).join(
            module_model, join_type=JOIN.LEFT_OUTER,
            on=(page_model.module_uuid == module_model.module_uuid)
        ).join(
            document_model, join_type=JOIN.LEFT_OUTER,
            on=(model.document_uuid == document_model.document_uuid)
        ).where(
            page_model.app_uuid == app_uuid, page_model.is_delete==False)
        return await self.list_obj(model, query)

    async def select_document_by_module_uuid_document_path_document_name(
            self, module_uuid, document_path, document_name, need_delete=False, as_dict=False
        ):
        model = Document
        fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.document_name,
                model.document_type,
                model.document_path,
                model.document_number,
                model.document_version,
                model.update_timestamp
            )
        query = model.select(*fields).where(
            model.module_uuid==module_uuid,
            model.document_path==document_path,
            model.document_name==document_name
        )
        return await self.select_obj_by_query(model, query, need_delete=need_delete, as_dict=as_dict)

    async def list_document_by_module_uuid_document_path(
        self, module_uuid: str, document_path: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.document_name,
                model.document_type,
                model.document_path,
                model.document_number,
                model.document_version,
                model.update_timestamp
            )
        query = model.select(*fields).where(
            model.module_uuid==module_uuid,
            model.document_path==document_path)
        query = query.order_by(model.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_document_by_app_uuid_document_type(
        self, app_uuid: str, document_type: int, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None,
        with_policy: bool = False, order_by_expr=None, module_uuid=None) -> list:
        model = Document
        p_model = ExtPolicy
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.document_name,
                model.document_type,
                model.document_path,
                model.document_number,
                model.document_version,
                model.update_timestamp,
                model.order,
                model.document_puuid
            )
        if with_policy:
            new_fields = list(fields) + [fn.IFNULL(p_model.visible, False).alias("visible"), p_model.additional]
            query = model.select(*new_fields).join(p_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==p_model.resource_id)
            ).where((p_model.is_delete==False)|(p_model.is_delete.is_null(True)), model.app_uuid==app_uuid)
        else:
            query = model.select(*fields).where(
                model.app_uuid==app_uuid)
        if document_type is not None:
            if isinstance(document_type, int):
                query = query.where(model.document_type==document_type)
            elif isinstance(document_type, list):
                query = query.where(model.document_type.in_(document_type))
        if order_by_expr is not None:
            if isinstance(order_by_expr, (list, tuple)):
                query = query.order_by(*order_by_expr)
            else:
                query = query.order_by(order_by_expr)
        if module_uuid:
            query = query.where(model.module_uuid==module_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_document_by_app_uuid(self, app_uuid, as_dict=True):
        document_model = Document
        module_model = Module
        fields = (
            document_model.document_uuid,
            document_model.document_name,
            document_model.id,
            document_model.app_uuid,
            document_model.module_uuid,
            document_model.document_type,
            document_model.document_path,
            document_model.order,
            module_model.id.alias('module_id')
        )
        query = document_model.select(*fields) \
                    .where(document_model.app_uuid==app_uuid, document_model.is_delete==False) \
                    .join_from(document_model, module_model, on=document_model.module_uuid==module_model.module_uuid)
        app_log.info(query)
        return await self.list_obj(document_model, query, as_dict=as_dict)

    async def list_document_content_by_app_uuid_document_type(
        self, app_uuid: str, document_type: int, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, only_content=False, additional_conditions: Optional[List[Expression]] = None) -> list:
        model = Document
        c_model = DocumentContent
        if only_content:
            join_type = JOIN.INNER
        else:
            join_type = JOIN.LEFT_OUTER
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.document_name,
                model.document_type,
                model.document_path,
                model.document_number,
                model.document_version,
                model.update_timestamp,
                fn.IFNULL(c_model.document_content, "{}").alias("document_content")
            )
        query = model.select(*fields).join_from(
            model, c_model, join_type=join_type,
            on=(model.document_uuid==c_model.document_uuid)).where(
            model.app_uuid==app_uuid)
        if additional_conditions:
            query = query.where(*additional_conditions)
        if document_type is not None:
            if isinstance(document_type, int):
                document_type = [document_type]
            query = query.where(model.document_type.in_(document_type))
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_document_content_by_document_uuid_list(self, document_uuid_list, as_dict=True):
        model = Document
        m_model = Module
        d_model = DocumentContent
        c_model = CheckMessage
        fields = (
            model.app_uuid,
            model.module_uuid,
            m_model.module_name,
            model.document_uuid,
            model.document_name,
            model.document_type,
            model.document_number,
            model.document_version,
            d_model.document_content,
            c_model.document_uuid.alias("check_document_uuid"),
        )
        query = model.select(*fields).join_from(
            model, m_model, join_type=JOIN.LEFT_OUTER,
            on=(model.module_uuid == m_model.module_uuid)).join_from(
            model, d_model, on=(model.document_uuid == d_model.document_uuid)
        ).join_from(
                model, c_model, join_type=JOIN.LEFT_OUTER,
                on=(model.document_uuid == c_model.document_uuid)).where(
            model.document_uuid.in_(document_uuid_list)
        )
        return await self.list_obj(model, query, as_dict=as_dict)

    async def list_document_by_module_uuid_join_check_message(
        self, app_uuid: str, module_uuid: str = None, ignore_version: bool = False, fields: list = None,
            as_dict: bool = True, need_delete: bool = False, ext_tenant=None, with_sys=False) -> list:
        model = Document
        m_model = Module
        c_model = CheckMessage
        d_model = DocumentContent
        dd_model = DocumentDraft

        content_field = fn.COALESCE(fn.IF(d_model.timestamp > dd_model.timestamp, d_model.document_content, dd_model.document_content),
                                    d_model.document_content).python_value(d_model.document_content.python_value)
        timestamp_field = fn.COALESCE(dd_model.timestamp, d_model.timestamp)
        timestamp_str = fn.UNIX_TIMESTAMP(timestamp_field).alias('timestamp')


        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                m_model.module_name,
                model.document_uuid,
                model.document_name,
                model.document_type,
                model.document_number,
                model.document_version,
                c_model.document_uuid.alias("check_document_uuid"),
            )

        fields = list(fields) + [content_field.alias('document_content'), timestamp_str]
        query = model.select(*fields).join_from(
            model, m_model, join_type=JOIN.LEFT_OUTER,
            on=(model.module_uuid == m_model.module_uuid)).join_from(
                model, c_model, join_type=JOIN.LEFT_OUTER,
                on=(model.document_uuid == c_model.document_uuid))

        query = query.join_from(
                model, d_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid == d_model.document_uuid)
                ).join_from(model, dd_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid == dd_model.document_uuid))
        query = query.where(model.document_type != DocumentType.DIR)
        if module_uuid is not None:
            query = query.where(model.module_uuid == module_uuid, model.app_uuid == app_uuid)
        else:
            if with_sys:
                expression = [model.app_uuid == app_uuid]
                query = self.build_query_with_sys(query, model, expression)
            else:
                query = query.where(model.app_uuid == app_uuid)

        if not ignore_version:
            query = query.where(model.document_version > c_model.document_version)
        query = query.group_by(model.document_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def count_document_by_document_puuid(self, document_puuid: str) -> int:
        model = Document
        query = model.select(
            model.document_uuid
        ).where(model.document_puuid==document_puuid)
        return await self.count_obj(model, query)

    async def count_page_by_document_puuid(self, module_uuid: str, document_puuid: str) -> int:
        model = Document
        query = model.select(
            model.document_uuid
        ).where(model.document_puuid == document_puuid, model.module_uuid == module_uuid,
                model.document_type == DocumentType.PAGE)
        return await self.count_obj(model, query)

    async def get_check_message(self, **data):
        model = CheckMessage
        return await self.get_obj(model, **data)

    async def get_check_message_by_document_uuid(self, document_uuid: str) -> Document:
        model = CheckMessage
        data = {
            model.document_uuid.name: document_uuid
        }
        return await self.get_check_message(**data)

    async def create_check_message(self, **data):
        model = CheckMessage
        check_message = await self.create_obj(model, **data)
        return check_message

    async def list_check_message_by_app_uuid(
        self, app_uuid: str, document_uuid: str=None, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant=None, any_problem=None, any_problem_message=None, model: CheckMessage=None) -> list:
        # model = CheckMessage
        d_model = Document
        if fields is None:
            fields = (
                d_model.app_uuid,
                d_model.module_uuid,
                d_model.document_uuid,
                d_model.document_type,
                any_problem_message
            )
        query = model.select(*fields).join_from(
            model, d_model, join_type=JOIN.LEFT_OUTER,
            on=(model.document_uuid==d_model.document_uuid))
        query = query.where(d_model.is_delete==False)
        if document_uuid:
            query = query.where(d_model.document_uuid==document_uuid)
        else:
            query = query.where(d_model.app_uuid==app_uuid)
        query = query.where(
            (any_problem==True)
        ).order_by(d_model.module_uuid, d_model.document_type)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def show_workflow_list(
        self, document_uuid, as_dict=True, need_delete=True
    ):
        model = Workflow
        query = model.select().where(
            (model.pc_from == document_uuid) |
            (model.mobile_from == document_uuid) |
            (model.pad_from == document_uuid)
        )
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def update_check_message_by_document_uuid(self, document_uuid: str, **data) -> int:
        model = CheckMessage
        query = model.update(**data).where(
            model.document_uuid==document_uuid
        )
        return await self.update_obj_by_query(model, query)

    async def get_model(self, **data):
        model = ModelBasic
        return await self.get_obj(model, **data)

    async def get_model_by_module_uuid_model_name(self, module_uuid: str, model_name: str) -> ModelBasic:
        model = ModelBasic
        data = {
            model.module_uuid.name: module_uuid,
            model.model_name.name: model_name
        }
        return await self.get_model(**data)

    async def select_model_by_model_uuid(self, model_uuid: str, ext_tenant=None) -> Func:
        model = ModelBasic
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.model_uuid,
            model.model_name,
            model.display_name,
            model.description,
            model.name_field,
            model.data_name,
            model.is_temp,
            model.ext_tenant.is_null(False).alias("is_extension"),
            model.is_import,
            model.import_binding
        )
        query = model.select(*fields).where(model.model_uuid==model_uuid)
        # if ext_tenant:
        #     query = query.where(model.ext_tenant==ext_tenant)
        return await self.select_obj_by_query(model, query)

    async def select_field_by_field_uuid(self, field_uuid: str, ext_tenant=None) -> Func:
        model = ModelField
        fields = (
            model.app_uuid,
            model.module_uuid,
            model.document_uuid,
            model.model_uuid,
            model.field_name,
            model.display_name,
            model.description,
        )
        query = model.select(*fields).where(model.field_uuid.startswith(field_uuid))
        # if ext_tenant:
        #     query = query.where(model.ext_tenant==ext_tenant)
        return await self.select_obj_by_query(model, query)

    async def list_model_by_app_uuid(
        self, app_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, with_sys: bool = False,
        ext_tenant:str = None, mark_self_relationship=False) -> list:
        model = ModelBasic
        if fields is None:
            fields = (
                model.id,
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.model_uuid,
                model.model_uuid.alias("origin_model_uuid"),
                model.model_name,
                model.display_name,
                model.name_field,
                model.data_name,
                model.is_temp,
                model.fields,
                model.ext_tenant.is_null(False).alias("is_extension"),
                model.is_import,
                model.import_binding,
                model.is_delete,
                model.select_enabled,
            )
        elif fields == "ALL":
            fields = model._meta.sorted_fields[:]
            fields.append(model.model_uuid.alias("origin_model_uuid"))
        query = model.select(*fields)
        if mark_self_relationship:
            r_model = RelationshipBasic
            query = (query.join(
                r_model, join_type=JOIN.LEFT_OUTER, on=((model.model_uuid==r_model.source_model) &
                                                        (r_model.is_delete==0)))
                     .group_by(model.model_uuid))
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        app_log.info(f"query: {query}")
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_model_has_ext_by_module_uuid(self, module_uuid, as_dict=True, ext_tenant=None):
        model = ModelBasic
        f_model = ModelField
        fields = (
            model.app_uuid,
            model.model_uuid,
            model.module_uuid,
            model.model_name,
            model.name_field,
            model.display_name,
            model.ext_tenant.is_null(False).alias("is_extension")
        )
        query = model.select(*fields).join(f_model, join_type=JOIN.LEFT_OUTER, on=(model.model_uuid==f_model.model_uuid)
        ).where(model.module_uuid==module_uuid, (f_model.ext_tenant==ext_tenant))
        return await self.list_obj(model, query, as_dict=as_dict, ext_tenant=ext_tenant)

    async def list_model_by_module_uuid(
            self, module_uuid: str, as_dict: bool = True, ext_tenant=None, prefix: str = ""
    ) -> list:
        model = ModelBasic
        query = model.select().where(model.module_uuid==module_uuid)
        if prefix:
            query = query.where(model.model_name.startswith(prefix))
        return await self.list_obj(model, query, as_dict=as_dict, ext_tenant=ext_tenant)

    async def list_relationship_by_app_uuid(
        self, app_uuid: str, as_dict: bool = True, need_delete: bool = False, with_sys=False) -> list:
        model = RelationshipBasic
        query = model.select()
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_relationship_by_module_uuid(
        self, module_uuid: str, as_dict: bool = True, need_delete: bool = False) -> list:
        model = RelationshipBasic
        query = model.select().where(model.module_uuid==module_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_relationship_by_source_model(
            self, model_uuid: str, show_many: bool = True, as_dict: bool = True,
            need_delete: bool = False, find_name_field=False,
            rename_self_joins=False, need_sys=True, model_dict=None) -> list:
        """
        rename_self_joins 为True, 则将 自关联的 relationship_uuid 替换掉
        因为 自关联的 relationship_uuid 将作为 运行时选择关联的 path
        自关联的 model 和 path 是完全相同的, 无法区分关联方向, 1 -> * 还是 * -> 1
        关联方向 直接影响 获取数据(是否需要 group_concat) 和 保存数据(更新哪些行的外键) 时的行为
        """
        # ONE_TO_MANY  = 0    MANY_TO_MANY = 1    ONE_TO_ONE   = 2
        model = RelationshipBasic
        m_model = ModelBasic
        f_model = ModelField
        # is_many , 当前数据模型 可不可以多选 对端的数据模型的数据
        case = Case(
            None,  # 这个 None 可替换成下面的 model.relationship_type 
            [(model.relationship_type == 0, False), (model.relationship_type == 1, True)],
            False
        )

        # model_is_many , 当前的数据模型 是不是关联的 多端
        m_case = Case(
            None,
            [(model.relationship_type == 0, False), (model.relationship_type == 1, False)],
            False
        )

        # is_source 指的是查询到的这个关联模型，是否为外键模型
        # is_many 指的是查询到的这个关联模型，是否是一个多端模型，例如 1 -> * ； * -> *
        default_fields = [
            model.relationship_uuid,
            model.relationship_name,
            model.display_name.alias("relationship_display_name"),
            model.relationship_type,
            model.frontref,
            model.backref,
            model.extra,
            model.target_model,
            model.source_model,
            model.is_system,
            m_model.model_uuid,
            m_model.model_name,
            m_model.display_name,
            m_model.name_field,
            m_model.data_name
        ]
        fields = []
        fields.extend(default_fields)
        fields.extend([
            Value(False).alias("is_source"),
            case.alias("is_many"),
            m_case.alias("model_is_many")  # 当前数据模型是不是多端
        ])
        if find_name_field:
            fields.extend([
                f_model.field_type, f_model.is_required, f_model.field_name, f_model.enum_uuid, f_model.display_name,
                f_model.calculate_field, f_model.field_uuid, f_model.calculate_type, f_model.is_serial])
        query = model.select(*fields).join(
            m_model, on=(model.target_model == m_model.model_uuid)).where(
            model.source_model == model_uuid, m_model.is_delete == False)
        if model_uuid in SystemTable.UUIDS:
            query = query.where(model.target_model.in_(SystemTable.UUIDS))
        if find_name_field:
            query = query.join_from(m_model, f_model, on=(
                m_model.name_field == f_model.field_uuid))
        if not show_many:
            query = query.where(model.relationship_type.in_(
                [RelationshipType.ONE_TO_MANY, RelationshipType.ONE_TO_ONE]))
        relationship_list = await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)
        relationship_list = list(relationship_list)
        if rename_self_joins:
            for relationship_dict in relationship_list:
                replace_relationship_uuid(relationship_dict, model, keep=True)
            # 遍历 relationship_list 更换为 自关联的 relationship_uuid + 0 的 md5 值
            # 需要保证 发布的时候, md5 能够对应哦
        if need_sys:
            if not model_dict:
                model_dict = await self.select_model_by_model_uuid(model_uuid)
            if model_dict:
                system_relationship_list = list_system_relationship(
                    [model_dict])
                relationship_list.extend(system_relationship_list)
        return relationship_list

    async def list_relationship_by_target_model(
        self, model_uuid: str, show_many: bool = True, as_dict: bool = True,
        need_delete: bool = False, find_name_field=False,
        rename_self_joins=False, need_sys=False, models=None,
        app_uuid=None) -> list:
        """
        rename_self_joins 为True, 则将 自关联的 relationship_uuid 替换掉
        因为 自关联的 relationship_uuid 将作为 运行时选择关联的 path
        自关联的 model 和 path 是完全相同的, 无法区分关联方向, 1 -> * 还是 * -> 1
        关联方向 直接影响 获取数据(是否需要 group_concat) 和 保存数据(更新哪些行的外键) 时的行为
        """
        # ONE_TO_MANY  = 0    MANY_TO_MANY = 1    ONE_TO_ONE   = 2
        model = RelationshipBasic
        m_model = ModelBasic
        f_model = ModelField

        # 当前数据模型 可不可以多选 对端的数据模型的数据
        case = Case(
            None,
            [(model.relationship_type == 0, True), (model.relationship_type == 1, True)],
            False
        )

        # 当前的数据模型 是不是关联的 多端
        m_case = Case(
            None,
            [(model.relationship_type == 0, True), (model.relationship_type == 1, True)],
            False
        )

        # is_source 指的是查询到的这个关联模型，是否为外键模型
        # is_many 指的是查询到的这个关联模型，是否是一个多端模型，例如 1 -> * ； * -> *
        fields = [
            model.relationship_uuid,
            model.relationship_name,
            model.display_name.alias("relationship_display_name"),
            model.relationship_type,
            model.frontref,
            model.backref,
            model.extra,
            model.target_model,
            model.source_model,
            model.is_system,
            m_model.model_uuid,
            m_model.model_name,
            m_model.display_name,
            m_model.name_field,
            m_model.data_name,
            Value(True).alias("is_source"),
            case.alias("is_many"),
            m_case.alias("model_is_many")  # 当前数据模型是不是多端
        ]
        if find_name_field:
            fields.extend([f_model.field_type, f_model.is_required, f_model.field_name, f_model.enum_uuid, f_model.display_name,
                f_model.calculate_field, f_model.field_uuid, f_model.calculate_type])
        query = model.select(*fields).join(
            m_model, on=(model.source_model==m_model.model_uuid)).where(
                model.target_model==model_uuid, m_model.is_delete==False)
        if model_uuid in SystemTable.ALL_UUIDS:
            if app_uuid:
                query = query.where((model.app_uuid==app_uuid) | (model.app_uuid==self.config.SYS_APP_UUID))
        if find_name_field:
            query = query.join_from(m_model, f_model, on=(
                m_model.name_field == f_model.field_uuid))
        if not show_many:
            query = query.where(model.relationship_type==RelationshipType.ONE_TO_ONE)
        relationship_list = await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)
        if need_sys:
            system_relationship_list = list_system_relationship(
                models, reversed=True)
            relationship_list = list(relationship_list)
            relationship_list.extend(system_relationship_list)
        if rename_self_joins:
            for relationship_dict in relationship_list:
                replace_relationship_uuid(
                    relationship_dict, model, from_source=False, keep=True)
        return relationship_list

    async def list_relationship(
        self, model_uuid: str, show_many: bool = True, as_dict: bool = True,
        need_delete: bool = False, find_name_field=False, allow_self_associate=False):
        # ONE_TO_MANY  = 0    MANY_TO_MANY = 1    ONE_TO_ONE   = 2
        model = RelationshipBasic
        m_model = ModelBasic
        f_model = ModelField

        # 当前数据模型 可不可以多选 对端的数据模型的数据
        case = Case(
            None,
            [(model.relationship_type == 0, True), (model.relationship_type == 1, True)],
            False
        )

        # 当前的数据模型 是不是关联的 多端
        m_case = Case(
            None,
            [(model.relationship_type == 0, True), (model.relationship_type == 1, True)],
            False
        )

        # is_source 指的是查询到的这个关联模型，是否为外键模型
        # is_many 指的是查询到的这个关联模型，是否是一个多端模型，例如 1 -> * ； * -> *
        fields = [
            model.relationship_uuid,
            model.relationship_name,
            model.display_name.alias("relationship_display_name"),
            model.relationship_type,
            model.frontref,
            model.backref,
            model.extra,
            model.target_model,
            model.source_model,
            model.is_system,
            m_model.model_uuid,
            m_model.model_name,
            m_model.display_name,
            m_model.name_field,
            m_model.data_name,
            Value(True).alias("is_source"),
            case.alias("is_many"),
            m_case.alias("model_is_many")  # 当前数据模型是不是多端
        ]
        if find_name_field:
            fields.extend([f_model.field_type, f_model.is_required, f_model.field_name, f_model.enum_uuid, f_model.display_name,
                f_model.calculate_field, f_model.field_uuid, f_model.calculate_type])
        query = model.select(*fields)
        if model_uuid in SystemTable.UUIDS:
            query = query.where(
                model.source_model.in_(SystemTable.UUIDS),
                model.target_model.in_(SystemTable.UUIDS))
        if not allow_self_associate:
            query = query.join(
            m_model, on=(
                    ((model.source_model==m_model.model_uuid) & (m_model.model_uuid!=model_uuid)) |
                    ((model.target_model==m_model.model_uuid) & (m_model.model_uuid!=model_uuid)))
                )
            query = query.where(
                ((model.target_model==model_uuid) & (model.source_model!=model_uuid)) |
                ((model.source_model==model_uuid) & (model.target_model!=model_uuid)), m_model.is_delete==False)
        else:
            query = query.join(
                m_model, on=(
                    ((model.source_model==m_model.model_uuid) & (m_model.model_uuid==model_uuid)) |
                    ((model.target_model==m_model.model_uuid) & (m_model.model_uuid==model_uuid)))
                )
            query = query.where(
                ((model.target_model==model_uuid) & (model.source_model==model_uuid)) |
                ((model.source_model==model_uuid) & (model.target_model==model_uuid)), m_model.is_delete==False)
        if find_name_field:
            query = query.join_from(m_model, f_model, on=(
                m_model.name_field == f_model.field_uuid))
        # if not show_many:
        #     query = query.where(model.relationship_type==RelationshipType.ONE_TO_ONE)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_field_and_sys_field(
            self, model, query, app_uuid: str = None, module_uuid: str = None, model_uuid: str = None,
            fields: list = None, need_sys: bool = False, need_relationship_field: bool = False,
            as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None):
        field_list = await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)
        sys_field_list = await self.list_sys_field(
            app_uuid=app_uuid, module_uuid=module_uuid, model_uuid=model_uuid,
            fields=fields, ext_tenant=ext_tenant, need_sys=need_sys,
            need_relationship_field=need_relationship_field)
        if field_list:
            field_list = list(field_list)
            field_list.extend(sys_field_list)
        return field_list, sys_field_list

    async def select_model_by_relationship_list_is_source(
            self, relationship_list: list, is_source: bool) -> dict:
        if not relationship_list:
            return list()
        model = RelationshipBasic
        m_model = ModelBasic
        fields = (
            model.relationship_name,
            model.relationship_uuid,
            m_model.app_uuid,
            m_model.module_uuid,
            m_model.document_uuid,
            m_model.model_uuid,
            m_model.model_name,
            m_model.display_name,
            m_model.is_temp
        )
        if is_source:
            query = model.select(*fields).join(
                m_model, on=(model.source_model==m_model.model_uuid))
        else:
            query = model.select(*fields).join(
                m_model, on=(model.target_model==m_model.model_uuid))
        if len(relationship_list) == 1:
            query = query.where(model.relationship_uuid==relationship_list[0])
        else:
            query = query.where(model.relationship_uuid.in_(relationship_list))
        return await self.list_obj(model, query)

    async def list_app_field_for_publish(
            self, app_uuid: str, fields: list = None,
            as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None, need_sys=False) -> list:
        model = ModelField
        if fields is None:
            fields = (
                    ModelField.decimals, ModelField.default, ModelField.length,
                    ModelField.enum_uuid, ModelField.field_name, ModelField.field_type,
                    ModelField.field_uuid, ModelField.display_name, ModelField.description,
                    ModelField.is_required, ModelField.is_visible,
                    ModelField.is_serial, ModelField.serial_prefix,
                    ModelField.serial_prefix_info,
                    ModelField.serial_num_length, ModelField.serial_rule,
                    ModelField.serial_gen_type, ModelField.serial_start_num,
                    ModelField.calculate_field, ModelField.calculate_function,
                    ModelField.calculate_type, ModelField.calculate_func,
                    ModelField.check_function, ModelField.check_error_message,
                    ModelField.aggre_field, ModelField.aggre_func,
                    ModelField.hierarchy_field, ModelField.hierarchy_list,
                    ModelField.is_unique, ModelField.ext_tenant.is_null(False).alias("is_extension"),
                    ModelField.model_uuid, ModelField.generated_function
                    )
        query = model.select(*fields).where(
            (model.app_uuid == app_uuid) | (model.app_uuid == self.config.SYS_APP_UUID)).order_by(model.sort)
        field_list, sys_field_list = await self.list_field_and_sys_field(
            model, query, app_uuid=app_uuid, fields=fields, need_sys=need_sys, need_relationship_field=True,
            as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)
        return field_list

    async def list_field_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None, need_sys=False,
        need_order=False, need_hide=False) -> list:
        model = ModelField
        m_model = ModelBasic
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.model_uuid,
                model.field_uuid,
                model.field_name,
                model.display_name,
                model.field_type,
                model.is_required,
                model.enum_uuid,
                model.calculate_field,
                model.is_serial,
                model.aggre_field,
                model.hierarchy_field,
                model.length,
                model.default,
                model.ext_tenant.is_null(False).alias("is_extension"),
                model.calculate_type
            )
        query = m_model.select(*fields).join(
            model, on=(m_model.model_uuid==model.model_uuid)).order_by(model.sort)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        if need_delete is False:
            query = query.where(m_model.is_delete==False)
        if not need_hide:
            query = query.where(model.hide!=True)
        if need_order:
            query = query.order_by(model.sort)
        # 如果field_list不存在说明权限校验失败
        field_list, sys_field_list = await self.list_field_and_sys_field(
            model, query, app_uuid=app_uuid, fields=fields, need_sys=need_sys,
            as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)
        return field_list

    async def list_field_by_module_uuid(
        self, module_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None, need_sys=False,
        need_order=False) -> list:
        model = ModelField
        m_model = ModelBasic
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.model_uuid,
                model.field_uuid,
                model.field_name,
                model.display_name,
                model.field_type,
                model.is_required,
                model.enum_uuid,
                model.calculate_field,
                model.calculate_type,
                model.is_serial,
                model.aggre_field,
                model.hierarchy_field,
                model.length,
                model.default,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        query = m_model.select(*fields).join(
            model, on=(m_model.model_uuid == model.model_uuid)).order_by(model.sort)
        query = query.where(model.module_uuid == module_uuid)
        if need_delete is False:
            query = query.where(m_model.is_delete==False)
        if ext_tenant is None:
            query = query.where(m_model.ext_tenant==None)
        if need_order:
            query = query.order_by(model.sort)
        query = query.where(model.hide!=True)
        # 如果field_list不存在说明权限校验失败
        field_list, sys_field_list = await self.list_field_and_sys_field(
            model, query, module_uuid=module_uuid, fields=fields, need_sys=need_sys,
            as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)
        return field_list

    async def list_model_basic(self, app_uuid):
        model = ModelBasic
        query = model.select().where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query)

    async def list_model_field(self, app_uuid):
        model = ModelField
        query = model.select().where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query)

    async def list_relationship_basic(self, app_uuid):
        model = RelationshipBasic
        query = model.select().where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query)

    async def list_sys_relationships_by_app_uuid(self, app_uuid):
        model = RelationshipBasic
        m_model = Module
        field = (
            model.target_model,
            model.relationship_uuid,
            model.backref,
            model.relationship_name,
            m_model.module_name
        )
        query = model.select(*field).join(
            m_model, on=(model.module_uuid == m_model.module_uuid)).where(
            model.app_uuid == app_uuid, model.system_relationship == True)
        return await self.list_obj(model, query)

    '''
    @description: 获取系统字段,app_uuid 和model_uuid 只需要传一个就行
    @param n*o self
    @param n*o app_uuid
    @param n*o model_uuid
    @return n*o
    @author: lv.jimin
    '''
    async def list_sys_field(
        self, app_uuid=None, model_uuid=None, fields=None,
        ext_tenant = None, need_sys=True, need_relationship_field=False, module_uuid=None):
        if not need_sys:
            return list()
        if model_uuid is not None:
            model_info = await self.select_model_by_model_uuid(model_uuid, ext_tenant=ext_tenant)
            if not model_info:
                app_log.info(f"model_not_exist: {model_uuid}")
            model_list = [model_info]
        elif module_uuid is not None:
            model_list = await self.list_model_by_module_uuid(module_uuid)
        else:
            model_list = await self.list_model_by_app_uuid(app_uuid, ext_tenant=ext_tenant, with_sys=True)
        sys_field_list = SystemField.ALL_FIELD
        field_list = list()
        new_fields = []
        for field in fields:
            if not hasattr(field, "_alias"):
                new_fields.append(field)
        for model_info in model_list:
            if not model_info:
                continue
            app_uuid = model_info.get(ModelBasic.app_uuid.name)
            module_uuid = model_info.get(ModelBasic.module_uuid.name)
            document_uuid = model_info.get(ModelBasic.document_uuid.name)
            model_uuid = model_info.get(ModelBasic.model_uuid.name)
            is_import = model_info.get(ModelBasic.is_import.name)
            copy_sys_field_list = sys_field_list[::]
            # need_relationship_field 这个判断，只有发布取系统字段和关联时会用到
            if need_relationship_field:
                copy_sys_field_list.extend(SystemField.RELATIONSHIP_FIELD)
            if is_import:
                app_log.info(f"{model_uuid} model_is_import")
                import_binding = model_info.get(ModelBasic.import_binding.name, {}) or {}
                pk_bind = import_binding.get("pk_bind", 10)
                if pk_bind == 11:
                    pk_bind = 10
                extern_pk_field = copy.copy(SystemField.EXTERN_PK)
                extern_pk_field["field_type"] = pk_bind
                copy_sys_field_list.append(extern_pk_field)
            field_info = {ModelField.app_uuid.name: app_uuid,
                          ModelField.module_uuid.name: module_uuid,
                          ModelField.document_uuid.name: document_uuid,
                          ModelField.module_uuid.name: module_uuid,
                          ModelField.model_uuid.name: model_uuid,
                          ModelField.hierarchy_list.name: []}
            for default_info in copy_sys_field_list:
                field_name = default_info.get("field_name")
                # 所有者 和 修改人 不展示在系统字段里
                # 但发布写模型文件的时候，还是要把这个字段生成出来
                if field_name in SystemField.FAKE_RELATIONSHIP_FIELD:
                    if not need_relationship_field:
                        continue
                field_uuid = get_sys_field_uuid(model_uuid, field_name)
                sys_field_info = {}
                sys_field_info.update(field_info)
                sys_field_info.update(default_info)
                sys_field_info["field_uuid"] = field_uuid
                new_sys_field_info = {field.name: sys_field_info.get(field.name, field.default) for field in new_fields}
                new_sys_field_info.update({"is_system": True})
                if field_name in SystemField.RELATIONSHIP_FIELD_NAMES:
                    # 所有者 和 修改人 从系统字段 变成 系统关联
                    new_sys_field_info.update({"is_relationship": True})
                field_list.append(new_sys_field_info)

        return field_list

    async def list_index_by_app_uuid(
        self, app_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = ModelIndex
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.model_uuid,
                model.index_uuid,
                model.index_name,
                model.fields,
                model.is_unique
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_index_by_model_uuid(
        self, model_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = ModelIndex
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.model_uuid,
                model.index_uuid,
                model.index_name,
                model.fields,
                model.is_unique
            )
        query = model.select(*fields).where(model.model_uuid==model_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_field_by_model_uuid(
        self, model_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None,
        need_sys = False) -> list:
        model = ModelField
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.model_uuid,
                model.field_uuid,
                model.field_name,
                model.display_name,
                model.field_type,
                model.length,
                model.hierarchy_field,
                model.calculate_field,
                model.calculate_type,
                model.aggre_field,
                model.is_required,
                model.is_serial,
                model.enum_uuid
            )
        query = model.select(*fields).where(model.model_uuid==model_uuid).order_by(model.sort)
        query = query.where(model.hide!=True)
        field_list, sys_field_list = await self.list_field_and_sys_field(
            model, query, model_uuid=model_uuid, fields=fields, need_sys=need_sys,
            as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)
        if not field_list:
            field_list = sys_field_list
        return field_list

    async def list_ext_field_by_model_uuid(self, model_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = ModelField
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.model_uuid,
                model.field_uuid,
                model.field_name,
                model.display_name,
                model.field_type,
                model.hierarchy_field,
                model.is_required
            )
        query = model.select(*fields).where(
            model.model_uuid==model_uuid, model.ext_tenant==ext_tenant).order_by(model.sort)
        return await super().list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_field_by_model_uuid_field_type(
        self, model_uuid: str, field_type: int=None, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant=None,
        need_sys=False) -> list:
        model = ModelField
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.model_uuid,
                model.field_uuid,
                model.field_name,
                model.display_name,
                model.field_type,
                model.length,
                model.enum_uuid,
                model.calculate_field,
                model.aggre_field,
                model.hierarchy_field,
                model.hierarchy_list,
                model.aggre_func,
                model.is_required,
                model.is_serial,
                model.is_unique,
                model.ext_tenant.is_null(False).alias("is_extension"),
                model.calculate_type
            )
        query = model.select(*fields).where(model.model_uuid==model_uuid).order_by(model.sort)
        query = query.where(model.hide!=True)
        if isinstance(field_type, list) and field_type:
            query = query.where(model.field_type.in_(field_type))
        app_log.info(query)
        field_list, sys_field_list = await self.list_field_and_sys_field(
            model, query, model_uuid=model_uuid, fields=fields, need_sys=need_sys,
            as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)
        if not field_list:
            field_list = sys_field_list
        return field_list

    async def create_document_content(self, **data):
        model = DocumentContent
        d_model = DocumentDraft
        draft_data = {
            d_model.document_uuid.name: data.get(model.document_uuid.name),
            d_model.document_content.name: data.get(model.document_content.name),
        }
        async with self.db.objs.atomic():
            try:
                await self.create_obj(d_model, **draft_data)
            except Exception:
                # draft有可能先被创建出来
                pass
            document_content = await self.create_obj(model, **data)
        return document_content

    async def get_document_content(self, **data):
        model = DocumentContent
        # 运行时没有document_draft表，临时处理
        if os.environ.get("APP_NAME"):
            return await self.get_obj(model, **data)
        d_model = DocumentDraft
        document_uuid = data.get(model.document_uuid.name)
        content_field = fn.COALESCE(fn.IF(model.timestamp > d_model.timestamp, model.document_content, d_model.document_content),
                                    model.document_content).python_value(model.document_content.python_value)
        timestamp_field = fn.COALESCE(d_model.timestamp, model.timestamp)
        timestamp_str = fn.UNIX_TIMESTAMP(timestamp_field).alias('timestamp')
        query = model.select(content_field.alias('document_content'),
                             model.id,
                             model.document_uuid,
                             model.user_uuid,
                             timestamp_str,
                             model.branch_uuid).join(d_model, on=(model.document_uuid==d_model.document_uuid),
                                                     join_type=JOIN.LEFT_OUTER).where(model.document_uuid==document_uuid)
        result = await self.select_obj_by_query(model, query, as_dict=False)

        # 这是拓展编辑的代码，不需要了
        # if current:
        #     if current.source_uuid:
        #         source_doc = await self.get_document_content(**{model.document_uuid.name: current.source_uuid})
        #         if source_doc:
        #             current.document_content = patch_json(source_doc.document_content, current.document_content)
        return result

    async def update_or_create_document_draft_by_document_uuid(self, document_uuid: str, **data):
        model = DocumentDraft
        query = model.update(**data).where(
            model.document_uuid==document_uuid
        )
        res = await self.update_obj_by_query(model, query)
        if res == 0:
            obj = await self.get_obj(model, document_uuid=document_uuid)
            if not obj:
                data[model.document_uuid.name] = document_uuid
                return await self.create_obj(model, **data)
        return res

    async def get_document_draft_by_document_uuid(self, document_uuid: str) -> DocumentDraft:
        model = DocumentDraft
        data = {
            model.document_uuid.name: document_uuid
        }
        return await self.get_obj(model, **data)

    async def get_document_content_by_document_uuid(self, document_uuid: str) -> DocumentContent:
        model = DocumentContent
        data = {
            model.document_uuid.name: document_uuid
        }
        return await self.get_document_content(**data)

    async def update_document_content_by_document_uuid(self, document_uuid: str, **data) -> int:
        model = DocumentContent
        query = model.update(**data).where(
            model.document_uuid==document_uuid
        )
        return await self.update_obj_by_query(model, query)

    async def create_sm(self, **data):
        model = StateMachine
        state_machine = await self.create_obj(model, **data)
        return state_machine

    async def get_sm(self, **data) -> Func:
        model = StateMachine
        return await self.get_obj(model, **data)

    async def update_or_create_packages(self, package_version: str, package_name: str, **data):
        model = Packages
        query = model.update(**data).where(
            (model.package_version == package_version) & (model.package_name == package_name))
        res = await self.update_obj_by_query(model, query)
        if res == 0:
            obj = await self.get_obj(model, package_version=package_version, package_name=package_name)
            if not obj:
                data[model.package_name.name] = package_name
                data[model.package_version.name] = package_version
                return await self.create_obj(model, **data)
        return res

    async def update_packages(self, data_list):
        preserve = [Packages.package_name, Packages.package_version, Packages.subpackage_info]
        query = Packages.insert_many(data_list).on_conflict(preserve=preserve)
        await self.db.objs.execute(query)

    async def get_packages_info_by_name_version(self, name: str, version: str) -> Func:
        model = Packages
        query = model.select().where((model.package_version == version) & (model.package_name == name))
        return await self.list_obj(model, query)

    async def select_sm_by_sm_uuid(self, sm_uuid: str, fields: list = None) -> Func:
        model = StateMachine
        if fields is None:
            fields = (
                model.sm_uuid,
                model.sm_name,
                model.display_name,
                model.description,
                model.multi_instance,
                model.strict_mode
            )
        query = model.select(*fields).where(model.sm_uuid==sm_uuid)
        return await self.select_obj_by_query(model, query)

    async def select_sm_content_by_sm_uuid(self, sm_uuid: str) -> dict:
        model = StateMachine
        d_model = DocumentContent
        query = model.select(
            model.sm_uuid,
            model.sm_name,
            d_model.document_content
        ).join(d_model, on=(model.document_uuid==d_model.document_uuid)).where(
            model.sm_uuid==sm_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_sm_variable_by_sm_uuid(self, sm_uuid: str, fields: list = None) -> Func:
        model = StateMachine
        if fields is None:
            fields = (
                model.sm_uuid,
                model.sm_name,
                model.display_name,
                model.variable_list.alias("children")
            )
        query = model.select(*fields).where(model.sm_uuid==sm_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_sm_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = StateMachine
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.sm_uuid,
                model.sm_name,
                model.multi_instance,
                model.strict_mode,
                model.states
            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def select_rulechain_content_by_uuid(self, rule_uuid: str):
        model = RuleChain
        d_model = DocumentContent
        query = model.select(
            model.rule_uuid,
            model.rule_name,
            d_model.document_content
        ).join(d_model, on=(model.document_uuid==d_model.document_uuid)).where(
            model.rule_uuid==rule_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_rulechain_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = RuleChain
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.rule_uuid,
                model.rule_name,
                model.system_service,
                model.variable_list
            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_sys_rulechain_by_app_uuid(
            self, app_uuid: str, fields: list = None, with_sys=False,
            as_dict: bool = True, need_delete: bool = False, ignore_env=False
        ):
        model = RuleChain
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.rule_uuid,
                model.rule_name,
                model.system_service,
                model.variable_list
            )
        query = model.select(*fields).where(model.system_service==True)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        if ignore_env:
            query = query.where(model.branch_uuid=="", model.user_uuid=="")
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)


    async def list_rc_variable_by_rc_uuid(
            self, rule_uuid: str, fields: list = None
        ):
        model = RuleChain
        if fields is None:
            fields = (
                model.rule_uuid,
                model.rule_name,
                model.variable_list.alias("children")
            )
        query = model.select(*fields).where(model.rule_uuid==rule_uuid)
        return await self.select_obj_by_query(model, query)

    # async def get_app_connector_config(self, app_uuid: str):
    #     model = Document
    #     data = {
    #         model.app_uuid.name: app_uuid,
    #         model.document_type.name: EntityType.CONNECTOR_CONFIG
    #     }
    #     return await self.get_obj(model, **data)

    # async def get_app_tenant_connector_config(self, app_uuid: str, tenant_uuid: str):
    #     model = ConnectorTenantConfig
    #     data = {
    #         model.app_uuid.name: app_uuid,
    #         model.tenant_uuid.name: tenant_uuid
    #     }
    #     return await self.get_obj(model, **data)
    async def get_connector_content_by_connector_uuid(self, connector_uuid: str):
        model = Connector
        p_model = DocumentContent
        fields = [
            model.connector_uuid,
            model.connector_type,
            p_model.document_content,
            p_model.document_uuid
        ]
        query = model.select(*fields).join(p_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==p_model.document_uuid)).where(model.connector_uuid==connector_uuid)
        return await self.select_obj_by_query(model, query, as_dict=True)

    async def list_connector_tenant_config(self, app_uuid: str, as_dict=True):
        model = Document
        p_model = ConnectorTenantConfig
        query = model.select(*[model.id, model.document_uuid, p_model.connector_uuid,
            p_model.app_uuid, p_model.tenant_uuid]).join(p_model, on=(Document.document_uuid==p_model.document_uuid)).where(model.app_uuid==app_uuid, model.document_type==EntityType.CONNECTOR_CONFIG)
        query = query.order_by(model.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict)

    async def create_func(self, **data):
        model = Func
        func = await self.create_obj(model, **data)
        return func

    async def get_func(self, **data) -> Func:
        model = func_model_condition_func()
        return await self.get_obj(model, **data)

    async def select_func_by_func_uuid(self, func_uuid: str) -> Func:
        model = func_model_condition_func()
        fields = (
            model.func_uuid,
            model.func_name,
            model.description,
            model.func,
            model.icon,
            model.arg_list,
            model.return_list,
            model.front_module
        )
        query = model.select(*fields).where(model.func_uuid==func_uuid)
        return await self.select_obj_by_query(model, query)

    async def select_func_content_by_func_uuid(self, func_uuid: str) -> Page:
        model = func_model_condition_func()
        d_model = DocumentContent
        m_model = Module
        query = model.select(
            model.func_uuid,
            model.func_name,
            model.from_py_module,
            m_model.module_uuid,
            m_model.module_name,
            d_model.document_content
        ).join(d_model, on=(model.document_uuid == d_model.document_uuid)).join_from(
            model, m_model, on=(model.module_uuid == m_model.module_uuid)).where(
            model.func_uuid == func_uuid)
        return await self.select_obj_by_query(model, query)

    async def select_func_content_by_module_name_func_name(self, app_uuid: str, module_name: str, func_name: str):
        model = func_model_condition_func()
        d_model = DocumentContent
        m_model = Module
        query = model.select(
            model.func_uuid,
            model.func_name,
            model.arg_list,
            model.return_list,
            m_model.module_uuid,
            m_model.module_name,
            d_model.document_content,
            model.from_py_module
        ).join(d_model, on=(model.document_uuid == d_model.document_uuid)).join_from(
            model, m_model, on=(model.module_uuid == m_model.module_uuid)).where(
            (m_model.module_name == module_name) & (model.func_name == func_name) & (m_model.app_uuid == app_uuid))
        return await self.select_obj_by_query(model, query)

    async def get_module_info_by_func_uuid(self, func_uuid: str) -> Page:
        model = func_model_condition_func()
        modulle_model = Module
        query = model.select(
            model.module_uuid,
            model.func_name,
            modulle_model.module_name
        ).join(modulle_model, on=(model.module_uuid==modulle_model.module_uuid)).where(
            model.func_uuid==func_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_func_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False, need_sort=False,
        as_dict: bool = True, need_delete: bool = False) -> List[Func]:
        model = func_model_condition_func()
        document = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.func_uuid,
                model.func_name,
                model.arg_list,
                model.return_list,
                model.icon_type,
                model.icon,
                model.install_sm_toolbox,
                model.sm_tool_name,
                model.sm_tool_category,
                model.is_delete
            )
        query = model.select(*fields).join(document, join_type=JOIN.LEFT_OUTER, on=(document.document_uuid==model.document_uuid))
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        if not need_delete:  # 删除标记仅在document上
            query = query.where(document.is_delete == False)
        if need_sort:
            query = query.order_by(document.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_func_by_module_uuid(
        self, module_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, prefix: str = "") -> List[Func]:
        model = func_model_condition_func()
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.func_uuid,
                model.func_name,
                model.icon_type,
                model.icon,
                model.install_sm_toolbox,
                model.sm_tool_name,
                model.sm_tool_category
            )
        query = model.select(*fields).where(model.module_uuid==module_uuid)
        if prefix:
            query = query.where(model.func_name.startswith(prefix))
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def create_image(self, **data):
        model = ImageTable
        image = await self.create_obj(model, **data)
        return image

    async def get_image(self, **data):
        model = ImageTable
        return await self.get_obj(model, **data)

    async def get_image_by_image_uuid(self, image_uuid: str) -> ImageTable:
        model = ImageTable
        data = {
            model.image_uuid.name: image_uuid
        }
        return await self.get_image(**data)

    async def update_image_by_image_uuid(self, image_uuid: str, **data) -> int:
        model = ImageTable
        query = model.update(**data).where(
            model.image_uuid==image_uuid
        )
        return await self.update_obj_by_query(model, query)

    async def list_image_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
            as_dict: bool = True, need_delete: bool = False, need_theme: bool = True) -> list:
        model = ImageTable
        d_model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.image_uuid,
                model.image_name,
                model.image_type,
                model.width,
                model.height,
                model.url,
                model.is_delete
            )
        query = model.select(*fields)
        if need_theme is False:
            query = query.join(
                d_model, join_type=JOIN.LEFT_OUTER, on=(d_model.document_uuid == model.document_uuid))
            query = query.where(d_model.document_type != DocumentType.MODULE_THEME)
        if with_sys:
            expressions = [model.app_uuid == app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid == app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_image_group_by_document(
        self, app_uuid: str, fields: list = None, with_sys=False,
            as_dict: bool = True, need_delete: bool = False, need_theme: bool = True) -> list:
        model = ImageTable
        d_model = Document
        image_info_field = fn.JSON_OBJECT(model.image_uuid.name, model.image_uuid,
                                          model.image_name.name, model.image_name,
                                          model.image_type.name, model.image_type,
                                          model.url.name,  model.url,
                                          model.width.name, model.width,
                                          model.height.name, model.height)
        if fields is None:
            fields = (
                model.module_uuid,
                model.document_uuid,
                d_model.document_name,
                fn.JSON_ARRAYAGG(image_info_field).python_value(ujson.loads).alias("children")
            )
        query = model.select(*fields)
        query = query.join(
                d_model, join_type=JOIN.LEFT_OUTER, on=(d_model.document_uuid == model.document_uuid))
        if need_theme is False:
            query = query.where(d_model.document_type != DocumentType.MODULE_THEME)
        if with_sys:
            expressions = [model.app_uuid == app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid == app_uuid)
        query = query.group_by(model.document_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_image_table_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
            as_dict: bool = True, need_delete: bool = False, need_theme: bool = True) -> list:
        model = ImageTable
        d_model = Document
        m_model = Module
        image_detail = fn.JSON_OBJECT("image_name", model.image_name, "url", model.url,
                                      "document_name", d_model.document_name)
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                m_model.module_name,
                fn.JSON_ARRAYAGG(image_detail).python_value(ujson.loads).alias("image_list"),
                model.is_delete
            )
        query = model.select(*fields).join(
            d_model, join_type=JOIN.LEFT_OUTER, on=(d_model.document_uuid == model.document_uuid)).join(
            m_model, join_type=JOIN.LEFT_OUTER, on=(model.module_uuid == m_model.module_uuid))
        query = query.group_by(model.module_uuid)
        query = query.where(d_model.document_type == DocumentType.IMAGE)
        query = query.where(model.app_uuid == app_uuid)
        app_log.info(query)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def get_const(self, **data):
        model = ConstTable
        return await self.get_obj(model, **data)

    async def get_const_by_const_uuid(self, const_uuid: str) -> ConstTable:
        model = ConstTable
        data = {
            model.const_uuid.name: const_uuid
        }
        return await self.get_const(**data)

    async def list_const_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = ConstTable
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.const_uuid,
                model.const_name,
                model.const_type,
                model.value
            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_const_by_module_uuid(
            self, module_uuid: str, as_dict: bool = True, ext_tenant=None, prefix: str = ""
    ) -> list:
        model = ConstTable
        query = model.select().where(model.module_uuid == module_uuid)
        if prefix:
            query = query.where(model.const_name.startswith(prefix))
        return await self.list_obj(model, query, as_dict=as_dict, ext_tenant=ext_tenant)

    async def list_json_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = JsonTable
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.json_uuid,
                model.json_name,
                model.is_delete
            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_enum_by_module_uuid(self, module_uuid: str, prefix: str = "", as_dict: bool = True):
        query = EnumTable.select().where(EnumTable.module_uuid == module_uuid)
        if prefix:
            query = query.where(EnumTable.enum_name.startswith(prefix))
        res = await self.list_obj(EnumTable, query, as_dict=as_dict)
        print(res, type(res))
        return res

    async def list_enum_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = EnumTable
        document = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.enum_uuid,
                model.enum_name,
                model.is_delete,
                model.value
            )
        query = model.select(*fields).join(document, join_type=JOIN.LEFT_OUTER, on=(document.document_uuid==model.document_uuid))
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        query = query.order_by(document.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def delete_enum_by_document_uuid(self, document_uuid):
        model = EnumTable
        query = model.update({model.is_delete.column_name: 1}).where(model.document_uuid==document_uuid)
        return await self.update_obj_by_query(model, query)

    async def delete_json_by_document_uuid(self, document_uuid):
        model = JsonTable
        query = model.update({model.is_delete.column_name: 1}).where(model.document_uuid==document_uuid)
        return await self.update_obj_by_query(model, query)

    async def delete_image_by_document_uuid(self, document_uuid):
        model = ImageTable
        query = model.update({model.is_delete.column_name: 1}).where(model.document_uuid==document_uuid)
        return await self.update_obj_by_query(model, query)

    async def delete_const_by_document_uuid(self, document_uuid):
        model = ConstTable
        query = model.update({model.is_delete.column_name: 1}).where(model.document_uuid==document_uuid)
        return await self.update_obj_by_query(model, query)

    async def delete_func_by_document_uuid(self, document_uuid):
        model = Func
        query = model.update({model.is_delete.column_name: 1}).where(model.document_uuid==document_uuid)
        return await self.update_obj_by_query(model, query)

    async def update_func_delete_status_by_func_uuid_module_uuid(
            self, func_uuid_list: List[str], module_uuid: str, is_delete: bool
    ) -> int:
        return await self.update_obj_by_query(
            Func,
            Func
            .update({Func.is_delete.column_name: is_delete})
            .where(Func.func_uuid.in_(func_uuid_list), Func.module_uuid == module_uuid)
        )

    async def update_py_module_func_info(
            self, func_uuid: str, module_uuid: str,
            func_name: str, py_module_line_no: str, arg_list: List[Dict], return_list: List[Dict],
            front_module: Optional[str] = None,
            is_delete: bool = False
    ):
        return await self.update_obj_by_query(
            Func,
            Func
            .update({Func.is_delete.column_name: is_delete,
                     Func.py_module_line_no.column_name: py_module_line_no,
                     Func.func_name.column_name: func_name,
                     Func.arg_list.column_name: arg_list,
                     Func.return_list.column_name: return_list,
                     Func.front_module.column_name: front_module})
            .where(Func.func_uuid == func_uuid, Func.module_uuid == module_uuid),
            need_delete=True
        )

    async def delete_py_module_func_by_document_uuid(self, module_uuid: str, document_uuid: str, exclude_func_uuids: Iterable[str]) -> int:
        return await self.update_obj_by_query(
            Func,
            Func
            .update({
                Func.is_delete.column_name: True,
            })
            .where(
                Func.module_uuid == module_uuid,
                Func.document_uuid == document_uuid,
                Func.func_uuid.not_in(list(exclude_func_uuids))
            )
        )

    async def delete_connect_by_document_uuid(self, document_uuid):
        model = Connector
        query = model.delete().where(model.document_uuid==document_uuid)
        result = await self.db.objs.execute(query)
        return result

    async def delete_excel_template_by_document_uuid(self, document_uuid):
        model = TemplateTable
        query = model.update({model.is_delete.column_name: 1}).where(model.document_uuid==document_uuid)
        result = await self.db.objs.execute(query)
        return result

    async def delete_workflow_by_document_uuid(self, document_uuid):
        model = Workflow
        query = model.update({model.is_delete.column_name: 1}).where(model.document_uuid==document_uuid)
        return await self.update_obj_by_query(model, query)

    async def select_enum_by_enum_uuid(self, enum_uuid: str, as_dict: bool=True):
        model = EnumTable
        query = model.select().where(model.enum_uuid==enum_uuid)
        return await self.list_obj(model, query, as_dict=as_dict)

    async def select_state_by_state_uuid(self, state_uuid: str) -> State:
        model = State
        fields = (
            model.state_uuid,
            model.state_name,
            model.state_type,
            model.variable_list
        )
        query = model.select(*fields).where(model.state_uuid==state_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_state_variable_by_state_uuid(self, state_uuid: str) -> State:
        model = State
        fields = (
            model.state_uuid,
            model.state_name,
            model.state_type,
            model.variable_list.alias("children")
        )
        query = model.select(*fields).where(model.state_uuid==state_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_state_by_app_uuid(
        self, app_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = State
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.sm_uuid,
                model.state_uuid,
                model.state_name,
                model.state_type
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def select_page_by_page_uuid(self, page_uuid: str, fields: list = None):
        model = Page
        if not fields:
            fields = [
                model.app_uuid,
                model.module_uuid,
                model.page_uuid,
                model.page_name,
                model.open_type,
                model.form_count,
                model.datalist_count,
                model.cardlist_count,
                model.datagrid_count,
                model.container_model
            ]
        query = model.select(*fields).where(model.page_uuid==page_uuid)
        return await self.select_obj_by_query(model, query)

    async def select_page_content_by_page_uuid(self, page_uuid: str, ext_tenant = None) -> Page:
        model = Page
        d_model = DocumentContent
        doc_model = Document
        fields = [model.page_uuid, model.page_name, model.open_type]

        # if ext_tenant:
        #     d_model_alias = d_model.alias("doc_content_ext")
        #     subq = d_model.select(
        #         d_model.document_uuid.alias("document_uuid"),
        #         d_model.document_content.alias("source"),
        #         d_model_alias.document_content.alias("delta")
        #         ).join(d_model_alias, join_type=JOIN.LEFT_OUTER, on=(d_model_alias.source_uuid==d_model.document_uuid))
        #     func = fn.JSON_OBJECT("source", subq.c.source, "delta", subq.c.delta).python_value(patch_document_content)
        #     # query = model.select(model.page_uuid, model.page_name, d_model.document_content
        #     # ).join(doc_model, on=((doc_model.source==model.document_uuid)&(doc_model.ext_tenant==model.ext_tenant))
        #     # ).join(d_model, on=(doc_model.document_uuid==d_model.document_uuid)).where(model.page_uuid==page_uuid,
        #     # model.ext_tenant==ext_tenant)
        #     fields.append(func.alias("document_content"))
        #     query = model.select(*fields).join(subq, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==subq.c.document_uuid)
        #         ).where(model.page_uuid==page_uuid, ((model.ext_tenant.is_null())|(model.ext_tenant==ext_tenant)))
        # else:
        fields.append(d_model.document_content)
        query = model.select(*fields).join(d_model, on=(model.document_uuid==d_model.document_uuid)).where(
            model.page_uuid==page_uuid)
        return await self.select_obj_by_query(model, query)

    async def select_page_document_by_page_uuid(self, page_uuid: str) -> Page:
        model = Page
        d_model = Document
        query = model.select(
            model.app_uuid,
            model.module_uuid,
            model.page_uuid,
            model.page_name,
            d_model.document_name,
            d_model.document_puuid,
            d_model.document_path
        ).join(d_model, on=(model.document_uuid==d_model.document_uuid)).where(
            model.page_uuid==page_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_page_by_app_uuid(
        self, app_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = Page
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.page_uuid,
                model.page_name,
                model.form_count,
                model.container_model,
                model.is_delete,
                model.open_type,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def check_page_table_available(
        self, app_uuid: str) -> list:
        model = Page
        fields = (
            model.id,
            model.app_uuid
        )
        query = model.select(*fields).where(model.app_uuid==app_uuid).limit(1)
        return await self.list_obj(model, query)

    async def list_page_by_module_uuid(
        self, module_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = Page
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.page_uuid,
                model.page_name,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        query = model.select(*fields).where(model.module_uuid==module_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_page_by_app_uuid_open_type(
        self, app_uuid: str, open_type: list = None, form_count: int = None, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = Page
        d_model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                d_model.document_name,
                model.page_uuid,
                model.page_name,
                model.open_type,
                d_model.id
            )
        query = model.select(*fields).join(
            d_model, on=(model.document_uuid==d_model.document_uuid)).where(
                model.app_uuid==app_uuid, d_model.is_delete==False).order_by(
                    model.module_uuid, d_model.document_puuid)
        if isinstance(open_type, list) and open_type:
            query = query.where(model.open_type.in_(open_type))
        if isinstance(form_count, int):
            query = query.where(model.form_count==form_count)
        query = query.order_by(d_model.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def select_print_content_by_print_uuid(self, print_uuid: str, ext_tenant = None) -> Page:
        model = Print
        d_model = DocumentContent
        doc_model = Document
        fields = [model.print_uuid, model.print_name]

        # if ext_tenant:
        #     d_model_alias = d_model.alias("doc_content_ext")
        #     subq = d_model.select(
        #         d_model.document_uuid.alias("document_uuid"),
        #         d_model.document_content.alias("source"),
        #         d_model_alias.document_content.alias("delta")
        #         ).join(d_model_alias, join_type=JOIN.LEFT_OUTER, on=(d_model_alias.source_uuid==d_model.document_uuid))
        #     func = fn.JSON_OBJECT("source", subq.c.source, "delta", subq.c.delta).python_value(patch_document_content)
        #     fields.append(func.alias("document_content"))
        #     query = model.select(*fields).join(subq, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==subq.c.document_uuid)
        #         ).where(model.print_uuid==print_uuid, ((model.ext_tenant.is_null())|(model.ext_tenant==ext_tenant)))
        # else:
        fields.append(d_model.document_content)
        query = model.select(*fields).join(d_model, on=(model.document_uuid==d_model.document_uuid)).where(
            model.print_uuid==print_uuid)
        return await self.select_obj_by_query(model, query)

    async def select_label_content_by_label_uuid(self, label_uuid: str, ext_tenant = None) -> Optional[LabelPrint]:
        model = LabelPrint
        d_model = DocumentContent
        doc_model = Document
        fields = [model.label_uuid, model.label_name, doc_model.document_type]

        # if ext_tenant:
        #     d_model_alias = d_model.alias("doc_content_ext")
        #     subq = d_model.select(
        #         d_model.document_uuid.alias("document_uuid"),
        #         d_model.document_content.alias("source"),
        #         d_model_alias.document_content.alias("delta")
        #     ).join(d_model_alias, join_type=JOIN.LEFT_OUTER, on=(d_model_alias.source_uuid==d_model.document_uuid))
        #     func = fn.JSON_OBJECT("source", subq.c.source, "delta", subq.c.delta).python_value(patch_document_content)
        #     fields.append(func.alias("document_content"))
        #     query = model.select(*fields).join(subq, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==subq.c.document_uuid)
        #         ).join(doc_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==doc_model.document_uuid)).where(
        #             model.label_uuid==label_uuid, ((model.ext_tenant.is_null())|(model.ext_tenant==ext_tenant)))

        # else:
        fields.append(d_model.document_content)
        query = model.select(*fields).join(d_model, on=(model.document_uuid==d_model.document_uuid)).join(
            doc_model, join_type=JOIN.LEFT_OUTER, on=(d_model.document_uuid==doc_model.document_uuid)).where(
            model.label_uuid==label_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_print_by_app_uuid(
        self, app_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = Print
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.print_uuid,
                model.print_name,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_label_print_by_app_uuid(
        self, app_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = LabelPrint
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.label_uuid,
                model.label_name,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_docuemnt_content_by_app_uuid(
        self, app_uuid: str, as_dict: bool = True,
        need_delete: bool = False, ext_tenant: str = None):
        model = LabelPrint
        document_content = DocumentContent
        fields = (
            model.app_uuid,
            model.document_uuid,
            model.label_uuid,
            model.label_name,
            model.ext_tenant.is_null(False).alias("is_extension"),
            document_content.document_content
        )
        query = model.select(*fields).join(document_content, on=(model.document_uuid== \
            document_content.document_uuid)).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_print_join_document_by_app_uuid(
        self, app_uuid: str, form_count: int = None, model_uuid: str = None,
        fields: list = None, as_dict: bool = True, need_delete: bool = False,
        ext_tenant: str = None) -> list:
        model = Print
        d_model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.print_uuid,
                model.container_model,
                d_model.document_name.alias(model.print_name.name),
                d_model.document_puuid,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        query = model.select(*fields).join_from(
            model, d_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==d_model.document_uuid)).where(
                model.app_uuid==app_uuid, d_model.is_delete==False)
        if isinstance(form_count, int):
            query = query.where(model.form_count==form_count)
        query = query.order_by(d_model.order.asc())
        result = await self.list_obj(
            model, query, as_dict=as_dict, need_delete=need_delete,
            ext_tenant=ext_tenant)
        if model_uuid:
            new_result = []
            for data in result:
                container_model_data = data.get(model.container_model.name)
                for _, container_dict in container_model_data.items():
                    if container_dict.get("model") == model_uuid:
                        new_result.append(data)
            return new_result
        else:
            return result

    async def list_label_print_join_document_by_app_uuid(
        self, app_uuid: str, fields: list = None, as_dict: bool = True,
        need_delete: bool = False, ext_tenant: str = None) -> list:
        model = LabelPrint
        d_model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.label_uuid,
                model.container_model,
                d_model.document_name.alias(model.label_name.name),
                d_model.document_puuid,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        query = model.select(*fields).join_from(
            model, d_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==d_model.document_uuid)).where(
                model.app_uuid==app_uuid, d_model.is_delete==False)
        query = query.order_by(d_model.order.asc())
        result = await self.list_obj(
            model, query, as_dict=as_dict, need_delete=need_delete,
            ext_tenant=ext_tenant)
        return result

    async def select_event_by_event_uuid(self, event_uuid: str) -> Event:
        model = Event
        fields = (
            model.event_uuid,
            model.event_name,
            model.description,
            model.arg_list
        )
        query = model.select(*fields).where(model.event_uuid==event_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_event_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = Event
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.sm_uuid,
                model.event_uuid,
                model.event_name,
                model.description,
                model.arg_list
            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_event_join_sm_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = Event
        sm_model = StateMachine
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.sm_uuid,
                sm_model.sm_name,
                model.event_uuid,
                model.event_name,
                model.description,
                model.arg_list
            )
        query = model.select(*fields).join(
            sm_model, on=(model.sm_uuid==sm_model.sm_uuid)).order_by(model.sm_uuid)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def select_workflow_content_by_wf_uuid(self, wf_uuid: str) -> Page:
        model = Workflow
        d_model = DocumentContent
        query = model.select(
            model.wf_uuid,
            model.wf_name,
            model.online_version,
            model.pc_from,
            model.mobile_from,
            model.pad_from,
            model.type,
            d_model.document_content
        ).join(d_model, on=(model.document_uuid==d_model.document_uuid)).where(
            model.wf_uuid==wf_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_workflow_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = Workflow
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.wf_uuid,
                model.wf_name,
                model.description,
                model.pc_from,
                model.mobile_from,
                model.pad_from,
                model.type,
                model.is_delete
            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_workflow_by_module_uuid(
            self, module_uuid: str, as_dict: bool = True, ext_tenant=None, prefix: str = ""
    ) -> list:
        model = Workflow
        query = model.select().where(model.module_uuid == module_uuid)
        if prefix:
            query = query.where(model.wf_name.startswith(prefix))
        return await self.list_obj(model, query, as_dict=as_dict, ext_tenant=ext_tenant)

    async def list_workflow_join_document_by_app_uuid(
        self, app_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = Workflow
        d_model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.wf_uuid,
                d_model.document_name.alias(model.wf_name.name),
                d_model.document_puuid
            )
        query = model.select(*fields).join_from(
            model, d_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==d_model.document_uuid)).where(
                model.app_uuid==app_uuid, d_model.is_delete==False,
                d_model.document_type==DocumentType.WORKFLOW)
        query = query.order_by(d_model.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_approvalflow_join_document_by_app_uuid(
        self, app_uuid: str, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = Workflow
        d_model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.wf_uuid,
                d_model.document_name.alias(model.wf_name.name),
                d_model.document_puuid
            )
        query = model.select(*fields).join_from(
            model, d_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==d_model.document_uuid)).where(
                model.app_uuid==app_uuid, d_model.is_delete==False,
                d_model.document_type==DocumentType.APPROVALFLOW
                )
        query = query.order_by(d_model.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_wf_variable_by_wf_uuid(
            self, wf_uuid: str,fields: list = None
        ):
        model = Workflow
        if fields is None:
            fields = (
                model.wf_uuid,
                model.wf_name,
                model.variables.alias("children")
            )
        query = model.select(*fields).where(model.wf_uuid==wf_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_connector_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = Connector
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.connector_uuid,
                model.connector_name,

            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_template_doc_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False, model_uuid="") -> List[Document]:
        model = Document
        document_content = DocumentContent
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.is_delete,
                model.document_name,
            )
        query = model.select(*fields).where(model.document_type==DocumentType.EXCEL_TEMPLATE)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        if model_uuid:
            query = query.join(document_content, join_type=JOIN.LEFT_OUTER, on=(document_content.document_uuid==model.document_uuid))

            query = query.where(fn.JSON_EXTRACT(document_content.document_content, '$.data_source.model')==model_uuid)
        if not need_delete:  # 删除标记仅在document上
            query = query.where(model.is_delete == False)
        query = query.order_by(model.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_excel_template_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = TemplateTable
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.template_uuid,
                model.template_name,

            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def select_excel_template(self, template_uuid: str):
        model = TemplateTable
        fields = [model.template, model.template_name, model.template_uuid]
        query = model.select(*fields).where(model.template_uuid==template_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_excel_template_by_document_uuid(self, document_uuid: str, app_uuid: str, fields=None):
        model = TemplateTable
        content_model = DocumentContent
        if fields is None:
            fields = (
                model.app_uuid,
                model.template_name,
                model.template_uuid,
                model.state,
                model.template_type,
                fn.JSON_EXTRACT(content_model.document_content,
                                '$.data_source.model').alias("document_model")
            )
        query = model.select(*fields).where(
                model.document_uuid==document_uuid, model.state==1, model.app_uuid==app_uuid).join(
                    content_model, on=(model.document_uuid==content_model.document_uuid))
        return await self.list_obj(model, query)

    async def list_export_template_doc_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False, model_uuid="") -> List[Document]:
        model = Document
        document_content = DocumentContent
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.is_delete,
                model.document_name,
            )
        query = model.select(*fields).where(model.document_type==DocumentType.EXPORT_TEMPLATE)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        if model_uuid:
            query = query.join(document_content, join_type=JOIN.LEFT_OUTER, on=(document_content.document_uuid==model.document_uuid))

            query = query.where(fn.JSON_EXTRACT(document_content.document_content, '$.data_source.model')==model_uuid)
        if not need_delete:  # 删除标记仅在document上
            query = query.where(model.is_delete == False)
        query = query.order_by(model.order.asc())
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_export_template_by_app_uuid(
        self, app_uuid: str, fields: list = None, with_sys=False,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = ExportTemplate
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.template_uuid,
                model.template_name,

            )
        query = model.select(*fields)
        if with_sys:
            expressions = [model.app_uuid==app_uuid]
            query = self.build_query_with_sys(query, model, expressions)
        else:
            query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def select_export_template_by_template_uuid(self, template_uuid: str):
        model = ExportTemplate
        fields = [model.template, model.template_name, model.template_uuid]
        query = model.select(*fields).where(model.template_uuid==template_uuid)
        return await self.select_obj_by_query(model, query)

    async def list_export_template_by_document_uuid(self, document_uuid: str, app_uuid: str, fields=None):
        model = ExportTemplate
        if fields is None:
            fields = (
                model.app_uuid,
                model.template_name,
                model.template_uuid,
                model.state,
                model.template_type,
                model.operation_type,
                fn.JSON_EXTRACT(model.template, '$.template_url').alias("template_url")
            )
        query = model.select(*fields).where(
                model.document_uuid==document_uuid, model.state==1, model.app_uuid==app_uuid)
        return await self.list_obj(model, query)


    async def create_tool_category(self, **data):
        model = ToolCategory
        tool_category = await self.create_obj(model, **data)
        return tool_category

    async def get_tool_category(self, **data) -> Func:
        model = ToolCategory
        return await self.get_obj(model, **data)

    async def get_tool_category_by_uuid(self, category_uuid: str) -> ToolCategory:
        model = ToolCategory
        data = {
            model.category_uuid.name: category_uuid
        }
        return await self.get_tool_category(**data)

    async def get_tool_category_with_unique_key(
        self, category_type, app_uuid, entity_type, category_name) -> ToolCategory:
        model = ToolCategory
        data = {
            model.category_type.name: category_type,
            model.app_uuid.name: app_uuid,
            model.entity_type.name: entity_type,
            model.category_name.name: category_name
        }
        return await self.get_tool_category(**data)

    async def list_tool_category_by_category_type(
        self, category_type: int, app_uuid: str, entity_type: int,
        fields: list = None, as_dict: bool = True, need_delete: bool = False) -> list:
        model = ToolCategory
        if fields is None:
            fields = (
                model.app_uuid,
                model.category_uuid,
                model.category_name,
                model.category_type,
                model.entity_type
            )
        query = model.select(*fields).where(
                model.category_type==category_type, model.app_uuid==app_uuid,
                model.entity_type==entity_type)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_tool_category_by_sys(
        self, entity_type: int, fields: list = None,
        as_dict: bool = True, need_delete: bool = False) -> list:
        category_type = ToolCategoryType.SYS
        app_uuid = ""
        return await self.list_tool_category_by_category_type(
            category_type=category_type, app_uuid=app_uuid, entity_type=entity_type,
            fields=fields, as_dict=as_dict, need_delete=need_delete)

    async def list_tool_category_by_user(
        self, app_uuid: str, entity_type: int, fields: list = None,
        as_dict: bool = True, need_delete: bool = False) -> list:
        category_type = ToolCategoryType.USER
        return await self.list_tool_category_by_category_type(
            category_type=category_type, app_uuid=app_uuid, entity_type=entity_type,
            fields=fields, as_dict=as_dict, need_delete=need_delete)

    async def create_sys_tool(self, **data):
        model = SYSTool
        tool = await self.create_obj(model, **data)
        return tool

    async def update_sys_tool(self, tool_uuid, **data):
        model = SYSTool
        query = model.update(**data).where(model.tool_uuid==tool_uuid)
        return await self.update_obj_by_query(model, query)

    async def get_sys_tool(self, **data) -> SYSTool:
        model = SYSTool
        return await self.get_obj(model, **data)

    async def create_user_tool(self, **data):
        model = UserTool
        tool = await self.create_obj(model, **data)
        return tool

    async def update_user_tool(self, package_uuid, **data):
        model = UserTool
        query = model.update(**data).where(model.package_uuid==package_uuid)
        return await self.update_obj_by_query(model, query)

    async def get_user_tool(self, **data) -> UserTool:
        model = UserTool
        return await self.get_obj(model, **data)

    async def get_user_tool_with_unique_key(self, category_uuid, tool_name) -> UserTool:
        model = UserTool
        data = {
            model.category_uuid.name: category_uuid,
            model.tool_name.name: tool_name
        }
        return await self.get_user_tool(**data)

    async def list_sys_tool_join_category(
        self, entity_type: int, fields: list = None,
        as_dict: bool = True, need_delete: bool = False) -> list:
        model = SYSTool
        category_type = ToolCategoryType.SYS
        app_uuid = ""
        c_model = ToolCategory
        if fields is None:
            fields = (
                c_model.category_uuid,
                c_model.category_name,
                c_model.category_type,
                c_model.category_sort,
                model.tool_uuid,
                model.tool_name,
                model.tool_type,
                model.tool_class,
                model.icon_type,
                model.icon,
                model.content
            )
        condition_list = (
            c_model.category_type==category_type,
            c_model.app_uuid==app_uuid,
            c_model.entity_type==entity_type
        )
        query = c_model.select(*fields).join(
            model, join_type=JOIN.LEFT_OUTER, on=(c_model.category_uuid==model.category_uuid)).where(
                *condition_list).order_by(c_model.id, model.sort)
        if not need_delete:
            if hasattr(model, "is_delete"):
                app_log.debug(f"List query add is_delete==False")
                query = query.where(model.is_delete==False)
        return await self.list_obj(c_model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_user_tool_join_category(
        self, app_uuid: str, entity_type: int = None, fields: list = None,
        as_dict: bool = True, need_delete: bool = False) -> list:
        """
        # 这里的查询需求
        # *** 要能查询出所有的分类、即使这个分类没有工具 ***
        # 所以，要以 lemon_toolcategory left outer join lemon_usertool
        # 这样，保证分类是全部能查出来的
        """
        model = UserTool
        category_type = ToolCategoryType.USER
        c_model = ToolCategory
        i_model = ImageTable
        if fields is None:
            fields = (
                c_model.category_uuid,
                c_model.category_name,
                c_model.category_type,
                c_model.category_sort,
                model.tool_uuid,
                model.tool_name,
                model.tool_type,
                model.tool_class,
                model.icon_color,
                model.icon_type,
                model.icon,
                model.content,
                i_model.image_name,
                i_model.url
            )
        condition_list = (
            c_model.category_type==category_type,
            model.app_uuid==app_uuid,
            model.is_delete==False
        )
        if entity_type is not None:
            condition_list = condition_list + (c_model.entity_type==entity_type,)
        query = c_model.select(*fields).join(
            model, join_type=JOIN.LEFT_OUTER, on=(c_model.category_uuid==model.category_uuid)).join(
                i_model, join_type=JOIN.LEFT_OUTER, on=(model.icon==i_model.image_uuid)).where(
                *condition_list)
        return await self.list_obj(c_model, query, as_dict=as_dict, need_delete=need_delete)

    async def create_navigation(self, user_uuid, **data):
        model = Navigation
        navigation_uuid = data.get("navigation_uuid")
        action = Action.DELETE  # 此处需要用用户身份权限来确认
        async with self.db.objs.atomic():
            module = await self.create_obj(model, **data)
            await self.create_policy(
                group_id=user_uuid,
                resource_id=navigation_uuid,
                action=action,
                app_uuid=data.get("app_uuid"),
                resource_type=ResourceType.NAVIGATION
            )
            return module

    async def list_navigation_by_app_uuid(self, app_uuid, as_dict: bool = True, need_delete: bool = False) -> list:
        model = Navigation
        query = model.select().where(
            model.app_uuid==app_uuid
        )
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def create_navigationitem(self, user_uuid, ext_tenant=None, **data):
        model = NavigationItem
        item_uuid = data.get("item_uuid")
        group_id = user_uuid
        action = Action.DELETE  # 此处需要用用户身份权限来确认
        async with self.db.objs.atomic():
            module, result = await self.db.objs.create_or_get(model, **data)
            if not result:  # 可能是 客户端undo操作导致insert数据库索引报错
                data["is_delete"] = False
                query = module.update(**data).where(module._pk_expr())
                await self.update_obj_by_query(model, query, need_delete=True)
            await self.create_policy(
                group_id=group_id,
                resource_id=item_uuid,
                action=action,
                app_uuid=data.get("app_uuid"),
                resource_type=ResourceType.NAVIGATION_ITEM
            )
            return module

    async def list_navigationitem(self, app_uuid, navigation_uuid: str=None,
        fields: list = None, as_dict: bool = True, need_delete: bool = False,
        ext_tenant: str = None) -> list:
        model = NavigationItem
        if fields is None:
            fields = (
                model.app_uuid,
                model.navigation_uuid,
                model.item_name,
                model.item_uuid,
                model.item_type,
                model.page_url,
                model.item_title,
                model.order,
                model.level,
                model.master,
                model.icon_type,
                model.icon,
                model.icon_color,
                model.page_uuid,
                model.param,
                model.permission,
                model.icon_text,
                model.item_position,
                model.badge,
                model.permission_config,
                model.ext_tenant.is_null(False).alias("is_extension")
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid).order_by(model.level, model.order)
        if navigation_uuid:
            query = query.where(model.navigation_uuid==navigation_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)


    async def get_navigation_by_platform(self, app_uuid, platform) -> Navigation:
        model = Navigation
        data = {
            model.app_uuid.name: app_uuid,
            model.platform.name: platform
        }
        return await self.get_obj(model, **data)

    async def list_runtime_account_by_app_uuid(self, app_uuid: str, fields: list = None,
                                            as_dict: bool = True, need_delete: bool = False) -> list:
        model = RuntimeAccount
        if fields is None:
            fields = (
                model.app_uuid,
                model.user_uuid,
                model.user_role,
                model.is_test,
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_runtime_account_by_app_uuid_user_role(self, app_uuid: str, user_role: str, fields: list = None,
                                            as_dict: bool = True, need_delete: bool = False) -> list:
        model = RuntimeAccount
        if fields is None:
            fields = (
                model.app_uuid,
                model.user_uuid,
                model.user_role,
                model.is_test,
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid, model.user_role==user_role)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def add_runtime_account(self, **data):
        model = RuntimeAccount
        return await self.create_obj(model, **data)

    async def remove_runtime_account(self, app_uuid, user_uuid, user_role):
        model = RuntimeAccount
        data = dict(
            is_delete=True
        )
        query = model.update(**data).where(model.app_uuid==app_uuid, model.user_uuid==user_uuid, model.user_role==user_role)
        return await self.update_obj_by_query(model, query)

    async def create_admin_user_role_by_app_uuid(self, app_uuid: str):
        model = UserRole
        data = {
            model.app_uuid.name: app_uuid,
            model.is_admin.name: True
        }
        admin_user_role = await self.get_obj(model, need_delete=True, **data)
        data = {
                model.role_uuid.name: lemon_uuid(),
                model.role_name.name: "系统管理员",
                model.app_uuid.name: app_uuid,
                model.order.name: 0,
                model.is_admin.name: True,
                model.is_delete.name: False
            }
        if admin_user_role:
            data.pop(model.role_uuid.name)
            query = model.update(**data).where(model.app_uuid==app_uuid, model.role_uuid==admin_user_role.role_uuid)
            return await self.update_obj_by_query(model, query, need_delete=True)
        else:
            return await self.create_obj(model, **data)

    async def get_user_role(self, **data):
        model = UserRole
        return await self.get_obj(model, **data)

    async def list_user_role_by_role_uuid(self, role_uuids: list, fields: list = None):
        model = UserRole
        if fields is None:
            fields = (
                model.id,
                model.role_uuid
            )
        query = model.select(*fields).where(model.role_uuid.in_(role_uuids))
        return await self.list_obj(model, query)

    async def list_user_role_by_app_uuid(self, app_uuid: str, fields: list = None,
                                            as_dict: bool = True, need_delete: bool = False) -> list:
        model = UserRole
        if fields is None:
            fields = (
                model.id,
                model.role_name,
                model.role_uuid,
                model.is_admin,
                model.document_uuid
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid).order_by(model.order)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_user_role_content_by_app_uuid(self, app_uuid, fields:list):
        model = UserRoleContent
        r_model = UserRole
        query = r_model.select(*fields).join(
            model, on=(model.user_role==r_model.role_uuid)).where(UserRole.app_uuid==app_uuid)
        return await self.list_obj(r_model, query)

    async def list_user_role_content_by_user_role(self, user_role_uuid: str, fields: list = None,
                                            as_dict: bool = True, need_delete: bool = False) -> list:
        model = UserRoleContent
        if fields is None:
            fields = (
                model.module_uuid,
                model.user_role,
                model.module_role
            )
        query = model.select(*fields).where(model.user_role==user_role_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_user_role_content_by_user_role_list(self, user_role_uuid_list: list, fields: list = None,
                                                       as_dict: bool = True, need_delete: bool = False) -> list:
        model = UserRoleContent
        if fields is None:
            fields = (
                model.module_uuid,
                model.user_role,
                model.module_role
            )
        query = model.select(*fields).where(model.user_role.in_(user_role_uuid_list))
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def add_module_relationship_on_user_role_content(self, app_uuid, module_uuid, user_list, module_role_dict=None):
        model = UserRoleContent
        app_security_document = await self.list_document_by_app_uuid_document_type(
            app_uuid, DocumentType.APP_SECURITY)
        if app_security_document:
            app_security_document = app_security_document[0]
        else:
            raise

        if not module_role_dict:
            module_role_dict = dict()

        data_list = [{
            model.module_uuid.name: module_uuid,
            model.document_uuid.name: app_security_document.get("document_uuid"),
            model.user_role.name: user["role_uuid"],
            model.module_role.name: module_role_dict.get(user["role_uuid"], list()),
            model.is_delete.name: False} for user in user_list]

        async with self.db.objs.atomic():
            query = model.delete().where(model.module_uuid==module_uuid)
            await self.delete_obj_by_query(model, query)

        async with self.db.objs.atomic():
            res = await self.insert_many_obj(model, data_list)
        return res

    async def list_app_user_role_content(self, app_uuid, need_delete=False):
        model = UserRoleContent
        u_model = UserRole
        query = model.select().join(u_model, on=(model.user_role==u_model.role_uuid)).where(
            u_model.app_uuid==app_uuid)
        if not need_delete:
            query = query.where(u_model.is_delete==True)
        return await self.list_obj(model, query, need_delete=need_delete)

    async def list_module_role_by_app_uuid(self, app_uuid: str, fields: list = None,
                                            as_dict: bool = True, need_delete: bool = False) -> list:
        model = ModuleRole
        if fields is None:
            fields = (
                model.module_uuid,
                model.document_uuid,
                model.role_uuid,
                model.role_name,
                model.pages,
                model.funcs,
                model.models
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_module_role_by_module_uuid(self, module_uuid: str, fields: list = None,
                                            as_dict: bool = True, need_delete: bool = False, ext_tenant=None) -> list:
        model = ModuleRole
        if fields is None:
            fields = (
                model.module_uuid,
                model.document_uuid,
                model.role_uuid,
                model.role_name
            )
        query = model.select(*fields).where(model.module_uuid==module_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def list_resource_ext_policy(self, app_uuid, resource_id):
        model = ExtPolicy
        if isinstance(resource_id, str):
            resource_id = [resource_id]
        query = model.select().where(model.app_uuid==app_uuid, model.resource_id.in_(resource_id))
        return await self.list_obj(model, query)

    async def update_resource_ext_policy(self, app_uuid, resource_id, visible, additional):
        model = ExtPolicy
        if isinstance(resource_id, str):
            resource_id = [resource_id]
        for rid in resource_id:
            data = {
                model.app_uuid.name: app_uuid,
                model.resource_id.name: rid
            }
            await self.db.objs.get_or_create(model, **data)
        update_data = dict(
            is_delete = False
        )
        if visible is not None:
            update_data.update({"visible": visible})
        if additional is not None:
            update_data.update({"additional": additional})
        query = model.update(**update_data).where(model.app_uuid==app_uuid, model.resource_id.in_(resource_id))
        return await self.update_obj_by_query(model, query, need_delete=True)

    async def is_resource_extendable(self, resource_id, app_uuid = None):
        model = ExtPolicy
        exp = NodeList([model.additional, SQL('->>"extendable" = %s', ('true',))], glue='')
        query = model.select().where(
            model.resource_id==resource_id)
        resource = await self.select_obj_by_query(model, query, as_dict=False)
        if resource:
            return resource.additional.get("extendable", False)
        return False

    async def is_resource_visible(self, resource_id, app_uuid = None):
        model = ExtPolicy
        exp = NodeList([model.additional, SQL('->>"extendable" = %s', ('true',))], glue='')
        query = model.select().where(
            model.resource_id==resource_id, model.visible==True)
        return await self.select_obj_by_query(model, query, as_dict=False)

    async def create_client(self, user_uuid: str, **data) -> Client:
        model = Client
        data.update({"uuser_uuid": user_uuid})
        client_uuid = data.get("client_uuid")
        async with self.db.objs.atomic():
            client = await self.create_obj(model, **data)
        return client

    async def get_client(self, **data):
        model = Client
        return await self.get_obj(model, **data)

    async def get_client_by_client_id(self, client_id):
        model = Client
        data = {
            model.client_uuid.name: client_id
        }
        return await self.get_data(**data)

    async def list_client_by_user_uuid(self, user_uuid: str) -> list:
        model = Client
        query = model.select(
            model.id,
            model.client_uuid,
            model.client_secret,
            model.client_type,
            model.redirect_uri,
            model.scope,
            ).where(model.user_uuid==user_uuid)
        return await self.list_obj(model, query)

    async def create_app_theme(self, app_uuid, user_uuid, theme_content):
        model = Document
        c_model = DocumentContent
        theme = await self.list_document_by_app_uuid_document_type(app_uuid=app_uuid, document_type=DocumentType.THEME, as_dict=False, need_delete=True)
        current = time.time()
        if not theme:
            doc_uuid = lemon_uuid()

            theme_doc = {
                model.app_uuid.name: app_uuid,
                model.module_uuid.name: "",
                model.document_uuid.name: doc_uuid,
                model.document_name.name: "主题配置",
                model.document_type.name: DocumentType.THEME,
                model.document_path.name: "",
                model.document_puuid.name: "",
                model.create_timestamp.name: current,
                model.update_timestamp.name: current
            }
            content_data = {
                c_model.document_uuid.name: doc_uuid,
                c_model.document_content.name: theme_content
            }
            async with self.db.objs.atomic():
                await self.create_document(user_uuid, **theme_doc)
                await self.create_document_content(**content_data)
        else:
            theme = list(theme)[0]
            data = {DocumentContent.document_content.name: theme_content}
            async with self.db.objs.atomic():
                await self.db.objs.execute(theme.update(document_version=0, update_timestamp=current,
                                            create_timestamp=current, is_delete=False).where(theme._pk_expr()))
                await self.update_document_content_by_document_uuid(theme.document_uuid, **data)

    async def update_app_publish_message(self, app_uuid, message_info, app_revision=0, message_level=0, ext_tenant=None):
        model = AppPublishMessage
        data = {
            model.app_uuid.name: app_uuid,
            model.ext_tenant.name: ext_tenant,
            model.message_level.name: message_level
        }
        app_message = await self.get_obj(model, **data)
        if app_message:
            query = app_message.update(message=message_info, app_revision=app_revision).where(app_message._pk_expr())
            return await self.update_obj_by_query(model, query)
        else:
            data.update({
                model.message.name: message_info,
                model.app_revision.name: app_revision
            })
            return await self.create_obj(model, **data)

    async def list_app_publish_message(self, app_uuid, message_level=0, fields=None, ext_tenant=None, as_dict=True):
        model = AppPublishMessage
        if fields is None:
            fields = (
                model.app_uuid,
                model.message_level,
                model.message
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid, model.message_level==message_level,
            model.ext_tenant==ext_tenant)
        return await self.list_obj(model, query, as_dict=as_dict)

    async def create_or_update_submit_audit(self, **data):
        model = SubmitAuditWX
        preserve = [model.auditstatus]
        # model.is_delete, model.auditid, model.appid, model.app_uuid, 
        return await self.insert_on_conflict(model, data, preserve=preserve, update=None)

    async def get_all_submitaudit_wx(self, app_uuid):
        model = SubmitAuditWX
        query = model.select().where(model.app_uuid==app_uuid)
        result = await self.db.objs.execute(query)
        return result

    async def get_bind_wx_by_app_uuid(self, app_uuid):
        model = AuthorizedWX
        query = model.select().where(model.app_uuid==app_uuid)
        result = await self.db.objs.execute(query)
        return result

    async def create_or_update_authorizer_wx_app(self, **data):
        model = AuthorizedWX
        preserve = [model.is_delete, model.refresh_token, model.app_uuid, model.func_info]
        return await self.insert_on_conflict(model, data, preserve=preserve, update=None)

    async def delete_authorizer_wx_app(self, app_uuid):
        model = AuthorizedWX
        query = model.update({model.is_delete.column_name: True}).where(model.app_uuid==app_uuid)
        return await self.update_obj_by_query(model, query, need_delete=True)

    async def update_authorizer_wx_auditid(self, app_uuid, auditid):
        model = AuthorizedWX
        query = model.update({model.auditid.column_name: auditid}).where(model.app_uuid==app_uuid)
        return await self.update_obj_by_query(model, query, need_delete=True)

    async def get_authorizer_wx_app_by_appid(self, appid, need_delete=False):
        model = AuthorizedWX
        data = {
            model.appid.name: appid
        }
        return await self.get_obj(model, need_delete=need_delete, **data)

    async def get_authorizer_wx(self, **data) -> AuthorizedWX:
        model = AuthorizedWX
        return await self.get_obj(model, **data)

    async def get_authorized_wx_by_app_uuid(self, app_uuid: str) -> APP:
        model = AuthorizedWX
        data = {
            model.app_uuid.name: app_uuid
        }
        return await self.get_obj(model, **data)

    async def get_app_config_by_app_uuid(self, app_uuid: str) -> dict:
        model = AppConfig
        data = {
            model.app_uuid.name: app_uuid
        }
        return await self.get_obj(model, **data)

    async def get_error_message_by_element(
        self, element_uuid, element_type_num, as_dict=True):
        if isinstance(element_uuid, str):
            element_uuid = [element_uuid]
        element_type2model = {
            1: {
                "model": RelationshipBasic,
                "name_field": RelationshipBasic.relationship_name,
                "uuid_field": RelationshipBasic.relationship_uuid
            },
            2: {
                "model": ModelField,
                "name_field": ModelField.field_name,
                "uuid_field": ModelField.field_uuid
            },
            3: {
                "model": ModelBasic,
                "name_field": ModelBasic.model_name,
                "uuid_field": ModelBasic.model_uuid
            },
            4: {
                "model": EnumTable,
                "name_field": EnumTable.enum_name,
                "uuid_field": EnumTable.enum_uuid
            }
        }
        element_info = element_type2model.get(element_type_num)
        if not element_info:
            return {}
        name_field = element_info.get("name_field")
        uuid_field = element_info.get("uuid_field")

        model = element_info.get("model")
        d_model = Document
        m_model = Module
        b_model = ModelBasic
        fields = (
            name_field.alias("element_name"),
            uuid_field.alias("element_uuid"),
            d_model.module_uuid,
            d_model.document_uuid,
            d_model.document_type,
            d_model.document_name,
            m_model.module_name
        )
        query = model.select(*fields).\
            join(d_model, on=(d_model.document_uuid==model.document_uuid)).\
            join(m_model, on=(d_model.module_uuid==m_model.module_uuid)).\
                where(uuid_field.in_(element_uuid))
        if element_type_num == 2:
            query = query.join(b_model, on=(model.model_uuid==b_model.model_uuid))
            query = query.select_extend(b_model.model_name, b_model.model_uuid)

        # result = await self.select_obj_by_query(model, query, as_dict=as_dict)
        app_log.info(query)
        result = await self.list_obj(model, query, as_dict=as_dict)
        return list(result) or []

    async def update_reference_by_document_uuid(self, document_uuid: str,  **data) -> int:
        model = DocumentReference
        query = model.update(**data).where(
            model.document_uuid==document_uuid
        )
        return await self.update_obj_by_query(model, query)

    async def create_document_reference(self, **data):
        model = DocumentReference
        check_message = await self.create_obj(model, **data)
        return check_message

    async def update_document_reference_by_document_uuid(self, document_uuid: str, **data) -> int:
        model = DocumentReference
        query = model.update(**data).where(
            model.document_uuid==document_uuid
        )
        return await self.update_obj_by_query(model, query)

    async def delete_reference_by_document_uuid(self, document_uuid):
        model = DocumentReference
        query = model.delete().where(model.document_uuid==document_uuid)
        result = await self.db.objs.execute(query)
        return result

    async def insert_reference(self, data):
        model = DocumentReference
        preserve = [model.field_reference, model.model_reference, model.r_reference,
                    model.func_reference, model.enum_reference, model.page_reference,
                    model.print_reference, model.label_print_reference, model.wf_reference,
                    model.enum_item_reference]
        # preserve = None
        result = await self.insert_on_conflict(model, data, preserve, None)
        return result

    async def get_document_reference(self, **data):
        model = DocumentReference
        return await self.get_obj(model, **data)

    async def get_document_reference_by_document_uuid(self,
                                                      document_uuid: str) -> DocumentReference:
        model = DocumentReference
        data = {
            model.document_uuid.name: document_uuid
        }
        return await self.get_document_reference(**data)

    async def list_reference_by_reference_uuid(self, reference_uuid, reference_field, app_uuid, as_dict=True):
        model = DocumentReference
        document_model = Document
        module_model = Module
        fields = (
           reference_field,
           document_model.document_name,
           model.document_uuid,
           document_model.document_type,
           module_model.module_uuid,
           module_model.module_name
        )
        if not isinstance(reference_uuid, list):
            reference_list = [reference_uuid]
        else:
            reference_list = reference_uuid
        reference_path_list = []
        for reference_uuid in reference_list:
            reference_field_path = '$.\"{}\"'.format(reference_uuid)
            reference_path_list.append(reference_field_path)

        query  = model.select(*fields).join(document_model, join_type=JOIN.LEFT_OUTER,
                                        on=(model.document_uuid==document_model.document_uuid)
                                )
        query = query.join(module_model, join_type=JOIN.LEFT_OUTER, on=(
                                    document_model.module_uuid==module_model.module_uuid))
        query = query.where(model.app_uuid==app_uuid, document_model.is_delete==False,
            fn.JSON_CONTAINS_PATH(reference_field, "one", *reference_path_list))
        app_log.info(query)
        return await self.list_obj(model, query, as_dict=as_dict)

    async def list_field_reference_by_field_uuid(self, field_uuid, app_uuid):
        reference_field = DocumentReference.field_reference
        return await self.list_reference_by_reference_uuid(
            field_uuid, reference_field, app_uuid)

    async def list_model_reference_by_model_uuid(self, model_uuid, app_uuid):
        reference_field = DocumentReference.model_reference
        return await self.list_reference_by_reference_uuid(
            model_uuid, reference_field, app_uuid)

    async def list_relationship_reference_by_relationship_uuid(self, relationship_uuid, app_uuid):
        reference_field = DocumentReference.r_reference
        return await self.list_reference_by_reference_uuid(
            relationship_uuid, reference_field, app_uuid)

    async def search_document_by_name(self, name, app_uuid):
        model = Document
        m_model = Module
        fields = (
            model.module_uuid,
            model.document_uuid,
            model.document_name,
            model.document_type,
            model.document_version,
            model.ext_tenant.is_null(False).alias("is_extension"),
            m_model.module_name
        )
        document_type_list = list()
        expr_list = list()
        if isinstance(name, list):
            for n in name:
                expr_list.append(model.document_name.contains(n))
                d_type_list = document_name_to_document_type(n)
                document_type_list.extend(d_type_list)
            document_type_list = list(set(document_type_list))
        else:
            expr_list.append(model.document_name.contains(name))
            document_type_list = document_name_to_document_type(name)

        name_condition = reduce(operator.or_, expr_list)
        type_condition = model.document_type.in_(document_type_list)

        name_query = model.select(*fields).join_from(
            model, m_model, on=(model.module_uuid==m_model.module_uuid)).where(
            model.app_uuid==app_uuid,
            model.document_type!=DocumentType.DIR,
            name_condition).order_by(
                model.module_uuid, model.create_timestamp)
        type_query = model.select(*fields).join_from(
            model, m_model, on=(model.module_uuid==m_model.module_uuid)).where(
            model.app_uuid==app_uuid,
            model.document_type!=DocumentType.DIR,
            type_condition).order_by(
                model.module_uuid, model.create_timestamp)
        name_result = await self.list_obj(model, name_query)
        type_result = await self.list_obj(model, type_query)
        return name_result, type_result

    async def search_document_content_by_document_type(self, app_uuid, document_type, search_name):
        model = Document
        c_model = DocumentContent
        m_model = Module
        fields = [
            model.module_uuid,
            model.document_uuid,
            model.document_name,
            model.document_type,
            model.document_path,
            m_model.module_name,
            m_model.module_type
        ]
        where_expr = None
        if document_type == DocumentType.FUNC:
            fields.append(fn.JSON_EXTRACT(c_model.document_content, '$.func').alias("func"))
            where_expr = fn.JSON_EXTRACT(c_model.document_content, '$.func').contains(search_name)
        query = model.select(*fields).join(c_model, on=(model.document_uuid==c_model.document_uuid)
                ).join(m_model, on=(model.module_uuid==m_model.module_uuid))
        if where_expr:
            query = query.where(model.app_uuid==app_uuid, model.document_type==document_type, where_expr)
        else:
            query = query.where(model.app_uuid==app_uuid, model.document_type==document_type)
        app_log.info(query)
        return await self.list_obj(model, query)

    async def get_document_detail_by_document_uuid(self, document_uuid, need_delete=False):
        async def get_doc_path(doc_uuid, path):
            p_doc = await self.get_obj(Document, need_delete=need_delete, **{model.document_uuid.name: doc_uuid})
            if p_doc:
                path = os.path.join(p_doc.document_name, path)
                if p_doc.document_puuid:
                    path = await get_doc_path(p_doc.document_puuid, path)
            return path

        model = Document
        c_model = DocumentContent
        m_model = Module
        fields = (
            model.module_uuid,
            model.app_uuid,
            model.document_name,
            model.document_type,
            model.document_path,
            model.document_puuid,
            m_model.module_name,
            c_model.document_content
        )
        query = model.select(*fields).join(c_model, join_type=JOIN.LEFT_OUTER, on=(c_model.document_uuid==model.document_uuid)
                ).join(m_model, join_type=JOIN.LEFT_OUTER, on=(model.module_uuid==m_model.module_uuid)).where(model.document_uuid==document_uuid)
        # current_workspace = get_current_workspace()
        # if current_workspace and current_workspace.user_uuid and current_workspace.branch_uuid:
        #     query = query.where(c_model.user_uuid==current_workspace.user_uuid, c_model.branch_uuid==current_workspace.branch_uuid)
        document_info = await self.select_obj_by_query(model, query, need_delete=need_delete)
        if not document_info:
            return None
        if document_info["document_path"] == "":
            document_info["full_path"] = document_info["document_name"]
            return document_info
        if document_info["document_path"] == "/":
            document_info["full_path"] = (document_info["module_name"] or "") + document_info["document_path"] + document_info["document_name"]
            return document_info
        path = ""
        if document_info["document_puuid"]:
            path = await get_doc_path(document_info["document_puuid"], path)
        document_info["full_path"] = os.path.join(document_info["module_name"], path, document_info["document_name"])
        return document_info
        document_path_list = document_info["document_path"].lstrip("/").rstrip("/").split("/")
        # todo 优化
        objs = []
        for pk in document_path_list:
            objs.append(await self.get_obj(Document, need_delete=need_delete, **{model.id.name: int(pk)},))
        paths = [""] + [obj.document_name for obj in objs if obj] + [""]
        document_info["document_path"] = ("/").join(paths)
        document_info["full_path"] = document_info["module_name"] + document_info["document_path"] + document_info["document_name"]
        return document_info

    async def list_document_by_document_id_list(
        self, document_id_list=None, parent_id=False, order_by_expr=None, document_uuid_list=None, need_delete=False):
        model = Document
        fields = (
            model.id,
            model.document_uuid,
            model.document_name,
            model.document_type,
            model.document_version,
            model.document_puuid,
            model.ext_tenant.is_null(False).alias("is_extension")
        )
        query = model.select(*fields)
        if parent_id:
            query = query.where(model.document_puuid.in_(document_id_list))
        elif document_id_list:
            query = query.where(model.id.in_(document_id_list))
        elif document_uuid_list:
            query = query.where(model.document_uuid.in_(document_uuid_list))
        if not order_by_expr:
            query = query.order_by(
                model.module_uuid, model.create_timestamp)
        else:
            query = query.order_by(
                model.module_uuid, order_by_expr
            )
        return await self.list_obj(model, query, need_delete=need_delete)

    async def list_document_by_document_puuid_list(
        self, document_puuid_list, order_by_expr=None):
        return await self.list_document_by_document_id_list(
            document_puuid_list, parent_id=True, order_by_expr=order_by_expr)

    async def list_document_order_by_father_uuid_list(
        self, father_uuid_list):
        model = DocumentOrder
        fields = (
            model.father_uuid,
            model.document_uuid_order_list
        )
        query = model.select(*fields).where(
            model.father_uuid.in_(father_uuid_list))
        return await self.list_obj(model, query)

    async def update_document_path_by_source_target(
        self, source_path, target_path, module_uuid):
        model = Document
        # 移动目录后，重新赋值 document_path 的结构
        query = model.update(
            {model.document_path: fn.REPLACE(
                model.document_path, source_path, target_path)}).where(
                    model.module_uuid==module_uuid,
                    model.document_path.startswith(source_path))
        return await self.update_obj_by_query(model, query)

    async def list_extension(
        self, page=1, fields=None, search=None, release_type=None, business=None, page_size=12,
        extend_from: list=None, scope: int=None, publisher: int=None, user_uuid: str=None):
        model = Extension
        p_model = Publisher
        case = Case(
            None,
            [(model.scope == 0, True), (model.scope == 1, False)],
            False
        )
        if fields is None:
            fields = (
                model.id,
                model.app_uuid,
                model.extension_uuid,
                model.extension_name,
                model.show_name,
                model.version,
                model.private,
                model.publisher,
                model.release_type,
                model.business,
                model.cover,
                model.preview,
                model.introduction,
                model.on_shelf_time,
                model.update_time,
                model.scope,
                case.alias("on_shelf"),
                p_model.name.alias("publisher_name")
            )
        query = model.select(*fields).join(Publisher, join_type=JOIN.LEFT_OUTER, on=(model.publisher==p_model.id))
        if search is not None:
            search = search.strip()
            query = query.where(model.show_name.contains(search) | model.business.contains(search))
        if release_type is not None:
            query = query.where(model.release_type==release_type)
        if business is not None:
            query = query.where(model.business==business)
        if scope is not None:
            if isinstance(scope, int):
                query = query.where(model.scope==scope)
            elif isinstance(scope, list):
                query = query.where(model.scope.in_(scope))
        if user_uuid is not None:
            query = query.where(model.user_uuid==user_uuid)
        if publisher is not None:
            query = query.where(model.publisher==publisher)
            query = query.order_by(model.on_shelf_time.desc())  # 需要按上架到私有时间
        else:
            query = query.order_by(model.update_time.desc())  # 需要按最新版本的更新时间
        if extend_from and isinstance(extend_from, list):
            query = query.where(model.id.in_(extend_from))

        app_log.info(query)
        return await self.list_obj_with_pagination_by_query(
            model, query, query, page, page_size=page_size)

    async def create_extension_import_record(
        self, app_uuid, user_uuid, extension_uuid, extension_pk, action,
        import_type=ImportType.ACTIVE_IMPORT, apply_user_uuid=None):
        """
        
        可能是主动导入 或 发行商推送
        """
        model = ImportRecord
        action_time = int(time.time())
        data = {
            model.app_uuid.name: app_uuid,
            model.user_uuid.name: user_uuid,
            model.extension_uuid.name: extension_uuid,
            model.import_type.name: import_type,
            model.action.name: action,
            model.apply_time.name: action_time,
            model.extension_pk.name: extension_pk,
            model.apply_user_uuid.name: apply_user_uuid
        }
        if action in [ImportResult.IMPORTED, ImportResult.REFUSED]:
            data.update({
                model.action_time.name: action_time
            })

        return await self.create_obj(model, **data)

    async def list_extension_import_record(
        self, app_uuid, user_uuid, extension_uuid=None, action=None):
        model = ImportRecord
        fields = (
            model.app_uuid.alias("accept_app_uuid"),
            model.extension_uuid,
            model.apply_time,
            Extension.app_uuid.alias("target_app_uuid"),
            Extension.show_name,
            Extension.version,
            User.mobile_phone,
            Extension.release_type
        )
        query = model.select(*fields).where(model.app_uuid==app_uuid, model.user_uuid==user_uuid)
        query = query.join(Extension, on=(model.extension_pk==Extension.id)
                           ).join(
                               User, join_type=JOIN.LEFT_OUTER, on=(model.apply_user_uuid==User.user_uuid)
                           )
        if action is not None:
            query = query.where(model.action==action)
        return await self.list_obj(model, query, as_dict=True)

    async def get_scope_last_document_order(self, module_uuid, document_puuid=None):
        # 获取模块下/目录下排序最后的文档
        order_by_expr = Document.order.desc()
        documents = await self.list_document_by_module_document_puuid(
            module_uuid, document_puuid, order_by_expr=order_by_expr, limit=1)
        documents = list(documents)
        last_order = 0
        if documents:
            last_order_document = documents[0]
            last_order = last_order_document.get(Document.order.name)
            last_order = float(last_order)
        return last_order

    async def list_sys_relationship(self):
        model = RelationshipBasic
        query = model.select().where(model.document_uuid==SYSConfig.MODEL_UUID)
        return await self.list_obj(model, query, as_dict=True, need_delete=False)

    async def list_restful_by_app_uuid(self, app_uuid, fields: list = None, as_dict: bool = True, need_delete: bool = False):
        model = RestfulTable
        if fields is None:
            fields = (
                model.app_uuid,
                model.document_uuid,
                model.module_uuid,
                model.restful_uuid,
                model.restful_uri_list,
                model.is_delete
            )
        query = model.select(*fields).where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_restful_request_by_app_uuid(
            self, app_uuid, basic_auth: bool = True,
            as_dict: bool = True, need_delete: bool = False):
        model = RestfulTable
        d_model = Document
        # table_sql = """'$[*]' COLUMNS (
        # `restful_uuid` CHAR(32) PATH '$.restful_uuid',
        # `auth_type` INT PATH '$.auth_type',
        # `version` CHAR(200) PATH '$.version',
        # `resource` CHAR(200) PATH '$.resource',
        # `method` CHAR(200) PATH '$.method')
        # """
        # json_table = fn.JSON_TABLE(
        #     model.requests, SQL(table_sql)).alias("j_table")
        query = model.select(
            model.app_uuid, model.module_uuid, model.restful_uuid,
            model.document_uuid, model.auth_type, d_model.document_name,
            d_model.document_puuid,
            model.requests.alias("children")).join_from(
                model, d_model,
                on=(model.document_uuid==d_model.document_uuid)).where(
            model.app_uuid==app_uuid)
        if not basic_auth:
            query = query.where(
                model.auth_type != RestfulAuthorizationType.BASIC)
        # query._from_list.append(json_table)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def get_app_branch(self, app_uuid, branch_uuid, fields: list=None):
        model = APPBranch
        if fields is None:
            fields = (
                model.app_uuid,
                model.branch_uuid,
                model.branch_name,
                model.status,
            )
        query = model.select(*fields).where(
            model.app_uuid==app_uuid, branch_uuid==branch_uuid)

    async def list_app_branch(self, app_uuid):
        model = APPBranch
        query = model.select().where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query)

    async def create_user_git_access(self, user_uuid, repo_type, git_host, user_token, ssh_host, token_expire=None):
        model = GitAccessManagement
        data = {
            model.user_uuid.name: user_uuid,
            model.repo_type.name: repo_type,
            model.git_host.name: git_host,
            model.user_token.name: user_token,
            model.token_expire.name: token_expire,
            model.ssh_host.name: ssh_host,
        }
        if token_expire is None:
            data.update({model.token_expire.name: -1})
        return await self.create_obj(model, **data)

    async def update_user_git_access(self, user_uuid, repo_type, **data):
        model = GitAccessManagement
        query = model.update(**data).where(model.user_uuid==user_uuid, model.repo_type==repo_type)
        return await self.update_obj_by_query(model, query)

    async def get_user_git_access(self, user_uuid, repo_type):
        model = GitAccessManagement
        return await self.get_obj(model, **{model.user_uuid.name: user_uuid, model.repo_type.name: repo_type})

    async def get_app_publisher(self, user_uuid):
        model = Publisher
        u_model = User

        u_query = model.select().join(u_model, on=(model.id==u_model.publisher)).where(
            u_model.user_uuid==user_uuid
        )
        t_model = Team
        t_query = model.select().join(t_model, on=(model.id==t_model.publisher)).where(
            t_model.team_uuid==user_uuid
        )
        query = u_query | t_query
        return await self.select_obj_by_query(model, query)

    async def list_teamplayer_app(self, team_uuid, user_uuid_list, permission_list: list = None):
        model = Combine
        app_model = APP
        u_model = User
        fields = (
            model.permission,
            model.user_uuid,
            app_model.app_name,
            app_model.app_uuid,
            app_model.user_uuid.alias("creator"),
            u_model.full_name.alias("creator_name")
        )
        query = model.select(*fields).join(
            app_model, on=(model.app_uuid==app_model.app_uuid)).join(
                u_model, join_type=JOIN.LEFT_OUTER, on=(u_model.user_uuid==app_model.middle_user)).where(
                model.team_uuid==team_uuid, model.user_uuid.in_(user_uuid_list),
                app_model.is_delete==False
            ).order_by(app_model.create_time.desc())
        if permission_list is not None:
            query = query.where(model.permission.in_(permission_list))
        return await self.list_obj(model, query)

    async def list_app_creator_info(self, app_uuid_list):
        model = APP
        u_model = User
        fields = (
            model.app_name,
            model.app_uuid,
            model.user_uuid.alias("creator"),
            u_model.full_name.alias("creator_name")
        )
        query = model.select(*fields).join(u_model, on=(u_model.user_uuid==model.middle_user)).where(
            model.app_uuid.in_(app_uuid_list)
        )
        return await self.list_obj(model, query)

    async def delete_teamplayer_combine(self, team_uuid, user_uuid, app_uuid=None):
        model = Combine
        query = model.delete().where(model.team_uuid==team_uuid, model.user_uuid==user_uuid)
        if app_uuid is not None:
            query = query.where(model.app_uuid==app_uuid)
        return await self.delete_obj_by_query(model, query)

    async def delete_all_teamplayer_combine(self, team_uuid=None, app_uuid=None):
        model = Combine
        query = model.delete()
        if not any([team_uuid, app_uuid]):
            raise
        if team_uuid is not None:
            query = query.where(model.team_uuid==team_uuid)
        if app_uuid is not None:
            query = query.where(model.app_uuid==app_uuid)
        return await self.delete_obj_by_query(model, query)

    async def get_applet_code_version(self, environment=None):
        model = AppletCodeManager
        # if environment:
        #     query = model.select().where(model.env == environment).limit(1)
        # else:
        query = model.select().limit(1)
        return await self.db.objs.get(query)

    async def update_policy(self, app_uuid, **data):
        model = Policy
        query = model.update(**data).where(model.app_uuid==app_uuid)
        return await self.update_obj_by_query(model, query)

    async def list_app_user_tool(self, app_uuid):
        model = UserTool
        query = model.select().where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query)

    async def delete_sandbox_user(self, user_uuid, app_uuid=None, sandbox_uuid=None):
        s_model = Sandbox
        model = SandboxUser
        query = model.select()
        if app_uuid is not None:
            query = query.join(s_model, on=(model.sandbox_uuid==s_model.sandbox_uuid)).where(
            s_model.app_uuid==app_uuid)
        if sandbox_uuid is not None:
            query = query.where(model.sandbox_uuid==sandbox_uuid)
        query = query.where(model.user_uuid==user_uuid)
        app_log.info(f"select_sandbox_user query: {query}")
        sandbox_list = await self.list_obj(model, query, as_dict=False)

        if sandbox_list:
            sandbox_list_ids = [s.id for s in sandbox_list]
            del_query = model.delete().where(model.id.in_(sandbox_list_ids))
            app_log.info(f"delete_sandbox_user query: {del_query}")
            await self.delete_obj_by_query(model, del_query)

    async def list_sandbox(self, app_uuid, user_uuid=None):
        model = Sandbox
        u_model = SandboxUser

        query = model.select()
        if user_uuid is not None:
            query = query.join(u_model, on=(model.sandbox_uuid==u_model.sandbox_uuid)).where(
            u_model.user_uuid==user_uuid)
        query = query.where(model.app_uuid==app_uuid)
        return await self.list_obj(model, query)

    async def list_sandbox_by_subenv(self, app_uuid, subenv) -> Sandbox:
        model = Sandbox
        u_model = SandboxUser
        query = model.select()
        query = query.where(model.app_uuid==app_uuid, model.sub_env==subenv)
        return await self.list_obj(model, query, as_dict=False)

    async def get_sandbox_by_sandbox_uuid(self, sandbox_uuid: str, app_uuid=None) -> Sandbox:
        model = Sandbox
        data = {
            model.sandbox_uuid.name: sandbox_uuid
        }
        if app_uuid:
            data.update({model.app_uuid.name: app_uuid})
        return await self.get_obj(model, **data)

    async def delete_app_sandbox(self, app_uuid):
        model = Sandbox
        u_model = SandboxUser

        all_app_sandboxusers_query = u_model.select().join(
            model, on=(model.sandbox_uuid==u_model.sandbox_uuid)).where(
                model.app_uuid==app_uuid
            )
        all_app_sandboxusers = await self.list_obj(u_model, all_app_sandboxusers_query, as_dict=False)
        if all_app_sandboxusers:
            all_app_sandboxusers = [s.id for s in all_app_sandboxusers]
            del_su_query = u_model.delete().where(u_model.id.in_(all_app_sandboxusers))
            await self.delete_obj_by_query(u_model, del_su_query)
        del_sandbox_query = model.delete().where(model.app_uuid==app_uuid)
        await self.delete_obj_by_query(model, del_sandbox_query)

    async def list_document_localhistory(self, document_uuid, page_size=10, current_page=1):
        model = Localhistory
        p_model = LocalhistoryPatch
        timestamp_field = fn.IFNULL(p_model.timestamp, model.timestamp).alias('timestamp')
        fields = (
            model.document_uuid,
            timestamp_field,
            # model.document_content,
            SQL("2 as type")

        )
        query = model.select(*fields).join_from(model, p_model, join_type=JOIN.LEFT_OUTER,
                                                on=(model.id==p_model.history_id)
                                                ).where(model.document_uuid==document_uuid
                                                        ).order_by(timestamp_field.desc()
                                                                   ).paginate(current_page, page_size)
        app_log.info(query)
        return await self.list_obj(model, query)

    async def get_document_localhistory(self, document_uuid, timestamp):
        def patch_content(json_str: str):
            json_obj = ujson.loads(json_str)
            patch = json_obj["patch"]
            if patch is None:
                return json_obj["source"]
            return jsonpatch.apply_patch(json_obj["source"], zlib.decompress(base64.b64decode(json_obj["patch"].encode())).decode())

        model = Localhistory
        p_model = LocalhistoryPatch
        hp_model = p_model.alias()
        h_model = model.alias()
        ps_query = hp_model.select().where(hp_model.document_uuid==document_uuid).alias("ps_query")
        hs_query = h_model.select().where(h_model.document_uuid==document_uuid).alias("hs_query")
        timestamp_field = fn.IFNULL(ps_query.c.timestamp, hs_query.c.timestamp)
        # python_value 只对 字段，字段别名 不生效 ？
        document_content = fn.JSON_OBJECT("source", hs_query.c.document_content, "patch", ps_query.c.patch
                                          ).alias("document_content")
        fields = (
            hs_query.c.document_uuid,
            timestamp_field.alias('timestamp'),
            document_content
        )
        query = ps_query.select_from(*fields).join(
            hs_query, join_type=JOIN.LEFT_OUTER, 
            on=(hs_query.c.id==ps_query.c.history_id)).where(
                hs_query.c.document_uuid==document_uuid,
                timestamp_field==timestamp)
        app_log.info(query)
        history = await self.list_obj(p_model, query, as_dict=False)
        if history:
            history[0].document_content = patch_content(history[0].document_content)
            return history[0]

    async def restore_document_content(self, document_uuid, timestamp):
        model = Localhistory
        c_model = DocumentContent
        p_model = LocalhistoryPatch
        doc_localhistory = await self.get_document_localhistory(document_uuid, timestamp)
        if not doc_localhistory:
            raise False
        data = {
            c_model.document_content.name: doc_localhistory.document_content
        }
        res = await self.update_document_content_by_document_uuid(document_uuid, **data)
        return data


    async def create_localhistory(self, **data):
        model = Localhistory
        p_model = LocalhistoryPatch
        document_uuid = data.get(model.document_uuid.name)
        document_content = data.get(model.document_content.name)
        query = model.select().where(model.document_uuid == document_uuid).order_by(model.timestamp.desc()).limit(1)
        history = await self.list_obj(model, query, as_dict=False)
        if history:
            history = history[0]
            history_document_content = history.document_content
            last_backup_time = history.timestamp
        else:
            history_document_content = document_content
            last_backup_time = 0
        async with self.db.objs.atomic():
            timestamp = data.get(model.timestamp.name, int(time.time()))
            if timestamp - last_backup_time > 60 * 60 * 24 * 3:
                instance = await self.create_obj(model, **data)
                pk = instance.id
            else:
                pk = history.id
            patch_data = {
                p_model.app_uuid.name: data.get(model.app_uuid.name),
                p_model.document_uuid.name: document_uuid,
                p_model.history_id.name: pk,
                p_model.timestamp.name: timestamp,
                p_model.patch.name: base64.b64encode(zlib.compress(ujson.dumps(
                    list(jsonpatch.make_patch(history_document_content, document_content))
                ).encode())).decode()
            }
            await self.insert_on_conflict(p_model, patch_data,
                                          preserve=[p_model.patch],
                                          update=None)

    async def create_localhistory_patch(self, **data):
        model = LocalhistoryPatch
        await self.create_obj(model, **data)

    async def get_app_deploy_config(self, app_uuid):
        model = Document
        c_model = DocumentContent
        fields = (
            model.document_uuid,
            c_model.document_content,
        )
        query = model.select(*fields).join(
            c_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==c_model.document_uuid)
            ).where(
                model.app_uuid==app_uuid,
                model.document_type==DocumentType.DEPLOY_CONFIG)
        result = await self.list_obj(model, query)
        deploy_config  =  result[0] if result else dict()
        return deploy_config

    async def get_app_local_config(self, app_uuid):
        deploy_config = await self.get_app_deploy_config(app_uuid)
        deploy_config  = deploy_config.get("document_content", dict())
        local_config = deploy_config.get("local_config", dict())
        return local_config

    async def get_app_extension_package(self, app_uuid):
        deploy_config = await self.get_app_deploy_config(app_uuid)
        deploy_config  = deploy_config.get("document_content", dict())
        extension_pack = deploy_config.get("extension_pack", dict())
        return extension_pack

    async def get_all_extension_package(self, app_uuid):

        all_package = {}
        all_packages = []
        app_ext_pack = await self.get_app_deploy_config(app_uuid)
        sys_Packages = app_ext_pack.get("document_content", dict()).get("extension_pack", dict()).get("sysPackages", dict())
        module_ext_packages = await self.list_document_content_by_app_uuid_document_type(
            app_uuid=app_uuid, document_type=DocumentType.EXPANSION_PACK)
        module_ext_packages_list = list(module_ext_packages)
        ext_packages_list = [app_ext_pack] + module_ext_packages_list

        for module_ext_pack in ext_packages_list:
            ext_pack = module_ext_pack.get("document_content", dict()).get("extension_pack", dict())
            module_packages = ext_pack.get("packages", dict())
            for module_package in module_packages:
                name = module_package.get("name")
                package_version = module_package.get("version")
                namespace = module_package.get("namespaces", [])
                if name in all_package:
                    if version.parse(package_version) > version.parse(all_package[name].get("version", "")):
                        all_package[name] = {"name": name, "version": package_version, "namespaces": namespace}
                else:
                    all_package[name] = {"name": name, "version": package_version, "namespaces": namespace}
            module_sys_packages = ext_pack.get("sysPackages", dict())
            for pack_name in module_sys_packages:
                if pack_name not in sys_Packages:
                    sys_Packages[pack_name] = module_sys_packages[pack_name]
            
        all_packages = list(all_package.values())
        return {"extension_packages": {"packages": all_packages, "sys_Packages": sys_Packages}}

    async def get_app_iconfont_config(self, app_uuid):
        deploy_config = await self.get_app_deploy_config(app_uuid)
        deploy_config  = deploy_config.get("document_content", dict())
        iconfont_config = deploy_config.get("icon_config", dict())
        return iconfont_config

    async def get_app_deploy_config_json(self, app_uuid):
        deploy_config = await self.get_app_deploy_config(app_uuid)
        deploy_config  = deploy_config.get("document_content", dict())
        iconfont_config = deploy_config.get("app_deploy_config", dict())
        return iconfont_config

    async def get_app_watermark(self, app_uuid):
        model = Document
        c_model = DocumentContent
        fields = (
            model.document_uuid,
            c_model.document_content,
        )
        query = model.select(*fields).join(
            c_model, join_type=JOIN.LEFT_OUTER, on=(model.document_uuid==c_model.document_uuid)
            ).where(
                model.app_uuid==app_uuid, model.document_type==DocumentType.WATERMARK)
        return await self.list_obj(model, query)

    async def list_teamplayer(self, team_uuid, status=0, permission=None):
        model = Teamplayer
        u_model: User = User.alias("invitee")
        u_model_r: User = User.alias("invitor")

        fields = (
            model.user_uuid,
            model.permission,
            model.is_active,
            fn.insert(u_model.mobile_phone, 4, 4, "****").alias(u_model.mobile_phone.name),
            u_model.full_name,
            u_model.login_as,
            u_model_r.full_name.alias("invitor_full_name"),
        )
        query = model.select(*fields).join(
            u_model, on=(model.user_uuid==u_model.user_uuid)).join(
                u_model_r, on=(model.invitor==u_model_r.user_uuid)).where(
                    model.team_uuid==team_uuid)
        if status is not None:
            query = query.where(model.status==status)
        if permission is not None:
            query = query.where(model.permission==permission)
        result = await self.list_obj(model, query)
        return result

    async def list_teamplayer_by_status(self, team_uuid, status=None, need_pagination=True,
                                        pagination=1, page_size=5, account=None, skip_app_uuid=None):
        model = Teamplayer
        c_model = Combine
        u_model: User = User.alias("invitee")
        u_model_r: User = User.alias("invitor")
        skip_user_uuids = set()
        if skip_app_uuid:
            skip_users = await self.list_combine_by_app_uuid(skip_app_uuid, team_uuid=team_uuid)
            skip_user_uuids = {u["user_uuid"] for u in skip_users}

        fields = (
            model.user_uuid,
            model.permission,
            model.is_active,
            model.combine_app_info,
            fn.insert(u_model.mobile_phone, 4, 4, "****").alias(u_model.mobile_phone.name),
            u_model.full_name,
            u_model.login_as,
            u_model_r.full_name.alias("invitor_full_name"),
            fn.COUNT(c_model.app_uuid).alias("join_apps_count")
        )
        query = model.select(*fields).join(
            u_model, on=(model.user_uuid==u_model.user_uuid)).join(
                u_model_r, on=(model.invitor==u_model_r.user_uuid)).where(
                    model.team_uuid==team_uuid).group_by(model.user_uuid)
        if status is not None:
            query = query.where(model.status==status)
        if account is not None:
            query = query.where(u_model.mobile_phone.contains(account) | u_model.full_name.contains(account))
        query = query.join(c_model, join_type=JOIN.LEFT_OUTER,
                           on=((model.user_uuid==c_model.user_uuid) & (model.team_uuid==c_model.team_uuid))
                        ).group_by(model.user_uuid)
        if skip_user_uuids:
            query = query.where(model.user_uuid.not_in(skip_user_uuids))
        if need_pagination:
            return await self.list_obj_with_pagination_by_query(
                model, query, query, pagination, page_size)
        else:
            result = await self.list_obj(model, query)
            return len(list(result)), result

    async def list_document_by_document_puuid(self, document_puuid: str, is_module=False):
        model = Document
        if is_module:
            query = model.select().where(model.document_puuid=="", model.module_uuid==document_puuid)
        else:
            query = model.select().where(model.document_puuid==document_puuid)
        return await self.list_obj(model, query)

    async def list_css_style_by_document_uuid(self, document_uuid):
        model = DocumentContent
        style_fields = fn.JSON_EXTRACT(
            model.document_content, '$.main_folder')
        fields = (
            style_fields.alias("main_folder"),
            model.document_uuid
        )
        query = model.select(*fields).where(
           model.document_uuid == document_uuid, style_fields.is_null(False))
        return await self.list_obj(model, query)

    async def list_module_theme_by_app_uuid_document_type(
        self, app_uuid: str, document_type, fields: list = None,
        as_dict: bool = True, need_delete: bool = False, ext_tenant: str = None) -> list:
        model = Document
        if fields is None:
            fields = (
                model.app_uuid,
                model.module_uuid,
                model.document_uuid,
                model.document_name,
                model.update_timestamp,
                model.order,
                model.document_puuid,
                model.ext_tenant.is_null(False).alias("is_extension"),
                model.id
            )
        query = model.select(*fields).where(
            model.app_uuid==app_uuid, model.document_type==document_type, model.is_delete==False)
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete, ext_tenant=ext_tenant)

    async def get_teamplayer_by_user_uuid(self, team_uuid, user_uuid, permission_list: list = None):
        model = Teamplayer
        app_model = APP
        fields = (
            model.user_uuid,
            model.team_uuid
        )
        query = model.select(*fields).join(
            app_model, on=(model.team_uuid==app_model.user_uuid)).where(
                model.team_uuid==team_uuid, model.user_uuid==user_uuid, app_model.is_delete==False
            )
        if permission_list is not None:
            query = query.where(model.permission.in_(permission_list))
        return await self.select_obj_by_query(model, query)

    async def list_app_publish_info(self, app_uuid, environment=None, fields=None):
        model = APPPublish
        if fields is None:
            fields = tuple()
        query = model.select(*fields).where(model.app_uuid == app_uuid)
        if environment:
            query = query.where(model.environment == environment)
        return await self.list_obj(model, query)

    async def list_document_by_link_uuid(self, document_uuid: str, as_dict=True):
        # 被引用列表
        model = DocumentLinks
        fields = (
            model.document_uuid,
            model.link_document,
            model.link_data,
            model.link_type,
            model.link_resource,
        )
        query = model.select(*fields).where(model.link_document==document_uuid)
        return await self.list_obj(model, query, as_dict=as_dict)

    async def list_documentlinks_by_document_uuid(self, document_uuid: str, as_dict=True):
        # 引用列表
        model = DocumentLinks
        fields = (
            model.id,
            model.document_uuid,
            model.link_document,
            model.link_data,
            model.link_type,
            model.link_resource,
        )
        query = model.select(*fields).where(model.document_uuid==document_uuid)
        return await self.list_obj(model, query, as_dict=as_dict)

    async def delete_document_links_by_document_uuid(self, document_uuid):
        l_model = DocumentLinks
        query = l_model.delete().where(l_model.document_uuid==document_uuid)
        return await self.delete_obj_by_query(l_model, query)

    async def list_resource_by_app_uuid(self, app_uuid, as_dict: bool = True, need_delete: bool = False) -> list:
        model = Resource
        fields = (
            model.id,
            model.module_uuid,
            model.resource_uuid,
            model.superior_resource
        )
        query = model.select(*fields).where(
            model.app_uuid==app_uuid
        )
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_tag_by_resource_uuid(self, resource_uuid, as_dict: bool = True, need_delete: bool = False) -> list:
        model = Tag
        fields = (
            model.tag_uuid,
        )
        query = model.select(*fields).where(
            model.resource_uuid==resource_uuid
        )
        return await self.list_obj(model, query, as_dict=as_dict, need_delete=need_delete)

    async def list_all_resource_by_app_uuid_module_uuid(self, app_uuid, module_uuid=None) -> list:
        model = Resource
        fields = (
            model.id,
            model.module_uuid,
            model.resource_uuid,
            model.resource_name,
            model.resource_type,
            model.superior_resource
        )
        query = model.select(*fields).where(model.app_uuid == app_uuid, model.is_delete == False)
        if module_uuid:
            query = query.where(model.module_uuid == module_uuid)
        return await self.list_obj(model, query)

    async def list_module_resource_tag(self, app_uuid, module_uuid=None, action=None):

        model = Resource
        t_model = Tag
        t_model: Tag = t_model.alias("tag_model")
        fields = (
            model.id.alias("resource_id"),
            model.module_uuid,
            model.resource_uuid,
            model.resource_name,
            model.resource_type,
            t_model.id,
            t_model.tag_uuid,
            t_model.tag_name,
            t_model.action
        )
        query = model.select(*fields).join(t_model, on=(model.resource_uuid == t_model.resource_uuid)).where(
            model.app_uuid == app_uuid, t_model.is_delete == False)
        if action is not None:
            conditions = []
            for a in split_binary_string(action):
                action_condition = SQL(f"CONV(tag_model.action, 2, 10) & CONV({a}, 2, 10) = CONV({a}, 2, 10)")
                conditions.append(action_condition)
            or_conditions = reduce(lambda x, y: x | y, conditions)
            query = query.where(or_conditions)
        if module_uuid:
            query = query.where(model.module_uuid == module_uuid)
        return await self.list_obj(model, query)

    async def list_module_resource_tag_groupby_module(self, app_uuid, module_uuid, obj_list):
        module_result = await self.list_module_by_app_uuid(app_uuid, with_sys=False)
        if module_uuid:
            module_result = [m for m in module_result if m.get("module_uuid") == module_uuid]
        return await GlobalVars.engine.service.query.list_obj_groupby_module(app_uuid, module_result, obj_list)

    async def list_tag(self, app_uuid, module_uuid=None):
        model = Tag
        fields = (
            model.id,
            model.module_uuid,
            model.document_uuid,
            model.tag_uuid,
            model.tag_name,
            model.action
        )
        query = model.select(*fields).where(model.app_uuid == app_uuid)
        if module_uuid:
            query = query.where(model.module_uuid == module_uuid)
        return await self.list_obj(model, query)

    async def list_iconfont(self, app_uuid):
        model = IconFont
        fields = (
            model.id,
            model.name,
            model.content,
            model.font_class
        )
        query = model.select(*fields).where(model.app_uuid == app_uuid)
        return await self.list_obj(model, query)

    async def list_iconfont_name(self, app_uuid):
        model = IconFont
        fields = (
            fn.JSON_ARRAYAGG(model.name).alias("name_list"),
        )
        query = model.select(*fields).where(model.app_uuid == app_uuid)
        return await self.list_obj(model, query)

    async def update_iconfont(self, app_uuid, data_list):
        query = IconFont.insert_many(data_list).on_conflict(preserve=[IconFont.content, IconFont.is_delete])
        await self.db.objs.execute(query)

    async def delete_iconfont_by_name(self, app_uuid, name_list):
        model = IconFont
        query = model.update({IconFont.is_delete.name: True}).where(model.app_uuid==app_uuid, model.name.in_(name_list))
        return await self.update_obj_by_query(model, query)

    async def select_extension(
            self, app_uuid, extension_uuid, scope_limit: list = None, need_detail=False, as_dict=True):
        model = Extension
        fields = [
            model.id,
            model.app_uuid,
            model.extension_uuid,
            model.extension_name,
            model.show_name,
            model.version,
            model.private,
            model.publisher,
            model.release_type,
            model.business,
            model.cover,
            model.preview,
            model.introduction,
            model.on_shelf_time,
            model.update_time,
            model.scope,
            model.user_uuid
        ]
        query = model.select(*fields).where(
            model.app_uuid == app_uuid,
            model.extension_uuid == extension_uuid
            )
        if need_detail:
            query = query.join(ExtensionDetail, on=(model.id == ExtensionDetail.extension_id))
            query = query.select_extend(ExtensionDetail.extension_info)
        if scope_limit:
            query = query.where(model.scope.in_(scope_limit))
        app_log.info(f"search extension query: {query}")
        return await self.select_obj_by_query(model, query, as_dict=as_dict)

    async def get_label_print_by_module_uuid_label_name(
            self, module_uuid: str, label_name: str
    ) -> Optional[LabelPrint]:
        return await self.get_obj(LabelPrint, module_uuid=module_uuid, label_name=label_name)

    async def get_latest_edit_by_document_type_module(
            self, app_uuid: str,
            include_document_types: List[int],
            exclude_documents: List[str],
            include_modules: Optional[List[str]] = None
    ):
        query = (
            Document
            .select(
                Document.module_uuid,
                Document.document_type,
                fn.MAX(Document.update_timestamp).alias('latest_update_timestamp')
            )
            .where(
                Document.document_type.in_(include_document_types),
                Document.document_uuid.not_in(exclude_documents),
                Document.app_uuid == app_uuid
            )
            .group_by(Document.document_type, Document.module_uuid)
        )
        if include_modules:
            query = query.where(Document.module_uuid.in_(include_modules))
        return list(await self.list_obj(Document, query))

    async def get_func_description_by_document_uuid(self, document_uuid: str) -> Optional[str]:
        description_field = fn.JSON_EXTRACT(DocumentContent.document_content, '$.description').alias("description")
        res = list(await self.list_obj(
            DocumentContent,
            DocumentContent
            .select(description_field)
            .where(
                DocumentContent.document_uuid == document_uuid
            )
        ))
        if not res:
            return None
        return res[0].get("description")

    async def list_app_publish_group(self, app_uuid):
        model = APP
        middle_model = AppGroup
        group_model = PublishGroup
        fields = (
            model.app_uuid,
            group_model.id,
            group_model.group_uuid,
            group_model.group_name
        )
        query = model.select(
                    *fields
                ).join(
                    middle_model, on=(model.id==middle_model.app_id)
                ).join(
                    group_model, on=(middle_model.group_id==group_model.id)
                ).where(
                    model.app_uuid==app_uuid
                )
        return await self.list_obj(model, query)

    async def get_tenant_by_group(self, group_uuid):
        group = PublishGroup
        tenant = Tenant
        fields = (
            tenant.tenant_uuid,
            group.group_uuid
        )
        query = group.select(
                *fields
            ).join(
                tenant, join_type=JOIN.INNER, on=(group.tenant_id==tenant.id)
            ).where(
                group.group_uuid==group_uuid
            )
        return await self.select_obj_by_query(group, query)

    async def get_tenant(self, tenant_uuid):
        tenant = Tenant

        query = tenant.select().where(tenant.tenant_uuid==tenant_uuid)
        return await self.select_obj_by_query(tenant, query)
