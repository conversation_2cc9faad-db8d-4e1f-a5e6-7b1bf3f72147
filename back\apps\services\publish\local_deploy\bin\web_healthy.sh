#!/bin/bash
web_code="404"
ucenter_code="404"
for i in `seq 1 5`
    do 
        if [ $web_code == "200" ]&&[ $ucenter_code == "200" ]; then
            break
        else
            if [ $web_code != "200" ];then
                web_code=$(curl --max-time 5  --write-out %{http_code} --silent --output /dev/null http://127.0.0.1:7000/api/runtime/v1/check_app_alive.json)
            fi
            if [ $ucenter_code != "200" ];then
                ucenter_code=$(curl --max-time 5  --write-out %{http_code} --silent --output /dev/null http://127.0.0.1:6500/api/index/v1/check_app_alive.json)
            fi
        fi
    done
if [ $web_code != "200" ] 
then
echo `date` &>> /tmp/healthcheck.log
echo $web_code &>> /tmp/healthcheck.log
echo "restart runtime ..." &>> /tmp/healthcheck.log
supervisorctl restart runtime:
fi

if [ $ucenter_code != "200" ]
then
echo `date` &>> /tmp/healthcheck.log
echo $ucenter_code &>> /tmp/healthcheck.log
echo "restart ucenter ..." &>> /tmp/healthcheck.log
supervisorctl restart ucenter
fi
