# -*- coding:utf-8 -*-

import weakref

from baseutils.log import app_log
from runtime.core.value_editor import LemonValueEditor


class Condition(object):

    def __init__(self, condition_dict):
        self.uuid = condition_dict.get("uuid")
        self.name = condition_dict.get("name")
        self.to_state = condition_dict.get("to_state")
        self.default = condition_dict.get("default", True)
        expr_dict = condition_dict.get("expr", dict())
        self.expr_dict = expr_dict if isinstance(expr_dict, dict) else dict()
        self.action_list = condition_dict.get("action_list", list())
        self._lsm = None

    def __repr__(self):
        ct = "Default" if self.default else "Non"
        return "<%s('%s')('%s')@%s>" % (type(self).__name__, self.name, ct, id(self))

    @property
    def lsm(self):
        return self._lsm()

    @lsm.setter
    def lsm(self, value):
        self._lsm = weakref.ref(value)

    @property
    def value(self):
        if self.default:
            return True
        if self.expr_dict and self.lsm:
            expr = self.lsm.editor.init(self.expr_dict)
            return expr.value
        return True


class ConditionWrapper(object):

    def __init__(self, condition: Condition, lsm):
        self.condition = condition
        self.condition.lsm = lsm

    def __call__(self):
        value = self.condition.value
        if not value:
            app_log.info(f"condition failed: value: {value}")
        return value
