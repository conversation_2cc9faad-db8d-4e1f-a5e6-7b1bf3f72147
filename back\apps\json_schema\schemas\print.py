from apps.json_schema.schemas.utils import gen_const_condition_schema


text = {
    "type": "object",
    "properties": {
        "data_source": {
            "type": "object",
            "properties": {
                "edit_value": {
                    "$ref": "mem://common/value_editor_all"
                }
            }
        }
    }
}

pic = {
    "type": "object",
    "properties": {
        "data_source": {
            "type": "object",
            "properties": {
                "image": {
                    "type": "object",
                    "properties": {
                        "uuid": {
                            "$ref": "mem://common/image_ref"
                        }
                    }
                }
            }
        }
    }
}

form = {
    "type": "object",
    "properties": {
        "data_source": {
            "model": {
                "$ref": "mem://common/model_ref"
            }
        },
        "children": {
            "type": "array",
            "items": {
                "allOf": [
                    gen_const_condition_schema(22, "mem://print/text"),
                    gen_const_condition_schema(21, "mem://print/pic"),
                    gen_const_condition_schema(0, "mem://print/form"),
                    gen_const_condition_schema(1, "mem://print/card_list"),
                    gen_const_condition_schema(2, "mem://print/data_list"),
                ]
            }
        }
    }
}

card_list = {
    "type": "object",
    "properties": {
        "data_source": {
            "model": {
                "$ref": "mem://common/model_ref"
            }
        },
        "children": {
            "type": "array",
            "items": {
                "allOf": [
                    gen_const_condition_schema(22, "mem://print/text"),
                    gen_const_condition_schema(21, "mem://print/pic"),
                    gen_const_condition_schema(0, "mem://print/form"),
                    gen_const_condition_schema(1, "mem://print/card_list"),
                    gen_const_condition_schema(2, "mem://print/data_list"),
                ]
            }
        }
    }
}

data_list = {
    "type": "object",
    "properties": {
        "data_source": {
            "model": {
                "$ref": "mem://common/model_ref"
            }
        },
        "columns": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "data_source": {
                        "type": "object",
                        "properties": {
                            "edit_value": {
                                "$ref": "mem://common/value_editor_all"
                            }
                        }
                    }
                }
            }
        }
    }
}

table = {
    "type": "object",
    "properties": {
        "children": {
            "type": "array",
            "items": {
                "allOf": [
                    gen_const_condition_schema(22, "mem://print/text"),
                    gen_const_condition_schema(21, "mem://print/pic"),
                    gen_const_condition_schema(0, "mem://print/form"),
                    gen_const_condition_schema(1, "mem://print/card_list"),
                    gen_const_condition_schema(2, "mem://print/data_list"),
                    gen_const_condition_schema(10, "mem://print/table"),
                ]
            }
        }
    }
}

print_schema = {
    "is_element": True,
    "attr_name": "打印模板",
    "type": "object",
    "properties": {
        "children": {
            "type": "array",
            "items": {
                "allOf": [
                    gen_const_condition_schema(0, "mem://print/form"),
                    gen_const_condition_schema(1, "mem://print/card_list"),
                    gen_const_condition_schema(2, "mem://print/data_list"),
                    gen_const_condition_schema(10, "mem://print/table"),
                ]
            }
        }
    }
}