# -*- coding:utf-8 -*-

from apps.utils import Json


class ImageTable(Json):

    def __init__(
        self, uuid, name, description, image_type, width, height, url, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.type = image_type
        self.width = width
        self.height = height
        self.url = url
        # self.s_url = s_url
        super().__init__(*args, **kwargs)


class ConstTable(Json):

    def __init__(
        self, uuid, name, description, const_type, value, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.type = const_type
        self.value = value
        super().__init__(*args, **kwargs)


class JsonTable(Json):

    def __init__(
        self, uuid, name, description, value, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.value = value
        super().__init__(*args, **kwargs)


class EnumItem(Json):

    def __init__(self, name, description, icon_type, icon, color, *args, **kwargs):
        self.name = name
        self.description = description
        self.icon_type = icon_type
        self.icon = icon
        self.color = color
        super().__init__(*args, **kwargs)


class EnumTable(Json):

    def __init__(
        self, uuid, name, description, value, *args, **kwargs):
        self.uuid = uuid
        self.name = name
        self.description = description
        self.value = value
        super().__init__(*args, **kwargs)
