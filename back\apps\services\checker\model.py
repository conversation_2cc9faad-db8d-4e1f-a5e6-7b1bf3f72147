# -*- coding:utf-8 -*-

import uuid
import uj<PERSON>
import base64
import asyncio

from baseutils.log import app_log
from apps.exceptions import CheckNameError, CheckUUIDError, CheckUUIDUniqueError
from apps.entity import ModelBasic, RelationshipBasic, ModelIndex, ModelField
from apps.utils import (
    check_lemon_name, check_lemon_uuid, check_common_name, gen_model_graph,
    new_relationship_uuid, gen_model_graph_depend_model,
    create_system_relationships
)
from baseutils.const import LemonPublishError, ReturnCode, SystemField, SystemTable, CalculateType
from baseutils.utils import topo_sort

from apps.ide_const import (LemonDesignerErrorCode as LDEC, 
    FieldType, RelationshipType, SerialGenType, ValueEditorType
)
from apps.ide_const import ModelDocument, Field, Index, Relationship, SerialRule
from apps.base_entity import UUID
from apps.services import CheckerService, DocumentCheckerService
from apps.value_editor_utils import LemonBaseValueEditor
from apps.services.checker import checker
from tests.document.lemon_sys_model import (
    document_uuid as sys_model_document_uuid
)
from apps.base_utils import lemon_uuid

class IndexCheckerService(CheckerService):

    attr_class = Index.ATTR
    uuid_error = LDEC.INDEX_UUID_ERROR
    uuid_unique_error = LDEC.INDEX_UUID_UNIQUE_ERROR
    name_error = LDEC.INDEX_NAME_FAILED
    name_unique_error = LDEC.INDEX_NAME_NOT_UNIQUE
    allow_keywords = False
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, model_uuid: str, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.model_uuid = model_uuid
    
    def initialize(self):
        super().initialize()
        self.description = self.element.get("description", "")
        self.is_unique = self.element.get("is_unique", True)
        self.fields_source = self.element.get("fields", [])
        if not isinstance(self.fields_source, list):
            self.fields_source = list()
        # 去重 再 排序
        self.fields = list(set(self.fields_source))
        self.fields.sort(key=self.fields_source.index)
        self.field_name_list = []
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            ModelIndex.app_uuid.name: self.app_uuid,
            ModelIndex.module_uuid.name: self.module_uuid,
            ModelIndex.document_uuid.name: self.document_uuid,
            ModelIndex.model_uuid.name: self.model_uuid,
            ModelIndex.index_uuid.name: self.element_uuid,
            ModelIndex.index_name.name: self.element_name,
            ModelIndex.description.name: self.description,
            ModelIndex.is_unique.name: self.is_unique,
            ModelIndex.fields.name: self.field_name_list
        }
    
    def build_update_query_data(self):
        return {
            ModelIndex.index_name.name: self.element_name,
            ModelIndex.description.name: self.description,
            ModelIndex.is_unique.name: self.is_unique,
            ModelIndex.fields.name: self.field_name_list,
            ModelField.is_delete.name: False
        }
    def _add_error_list(self, attr: str, return_code: ReturnCode, **kwargs):
        kwargs.update({"model_uuid": self.model_uuid})
        return super()._add_error_list(attr, return_code, **kwargs)

    def build_update_query(self):
        query_data = self.build_update_query_data()
        return ModelIndex.update(**query_data).where(ModelIndex.index_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(index_uuid):
        return ModelIndex.update(**{
                ModelIndex.is_delete.name: True
            }).where(ModelIndex.index_uuid==index_uuid)
    
    # 检查该索引是 新增 或 更新
    def check_modify(self, document_index_uuid_set):
        if self.element_uuid in document_index_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

    # 检查索引唯一属性的值，是否在可选范围内，如果不在，会向错误列表添加一条报错信息
    def check_is_unique(self):
        attr = Index.ATTR.IS_UNIQUE
        return_code = LDEC.INDEX_TYPE_NOT_SUPPORT
        if self.is_unique not in [True, False]:
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查索引所选的字段，是否为空列表，如果为空列表，会向错误列表添加一条报错信息
    def check_fields_is_null(self):
        attr = Index.ATTR.FIELDS
        return_code = LDEC.INDEX_FIELDS_IS_NULL
        if not self.fields:
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查索引所选的字段，是否在模型中存在，如果不存在，会向错误列表添加一条报错信息
    def check_field_exists(self, field_uuid_dict: dict, relation_uuid_list: list):
        attr = Index.ATTR.FIELDS
        return_code = LDEC.INDEX_FIELD_NOT_EXISTS
        for field_uuid in self.fields:
            field_info = field_uuid_dict.get(field_uuid, {})
            if field_info and isinstance(field_info, dict):
                normal_field = True
                aggre_field = field_info.get("aggre_field")
                if aggre_field is True:
                    normal_field = False
                    self._add_error_list(attr, LDEC.INDEX_FIELD_IS_AGGRE_FIELD)
                hierarchy_field = field_info.get("hierarchy_field")
                if hierarchy_field == 1:
                    normal_field = False
                    self._add_error_list(attr, LDEC.INDEX_FIELD_IS_HIERARCHY_FIELD)
                calculate_field = field_info.get("calculate_field")
                generated_column = field_info.get("calculate_type") == CalculateType.GENERATED_COLUMN
                if calculate_field and not generated_column:
                    normal_field = False
                    self._add_error_list(attr, LDEC.INDEX_FIELD_IS_CALCULATE_FIELD)
                if normal_field:
                    self.field_name_list.append(field_uuid)
            else:
                has_relation = False
                for relation_dict in relation_uuid_list:
                    if field_uuid == relation_dict.get("uuid"):
                        if relation_dict.get('type') == '1':
                            pass
                        else:
                            has_relation = True
                            self.field_name_list.append(field_uuid)
                # relation_info = relation_uuid_list.get(field_uuid, {})
                # if relation_info and isinstance(relation_info, dict):
                #     if relation_info.get('type') == '1':
                #         pass
                #     else:
                #         self.field_name_list.append(field_uuid)
                if not has_relation:
                    self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查索引所选的字段中，是否有重复的字段，如果存在，会向错误列表添加一条报错信息
    def check_field_repetition(self):
        attr = Index.ATTR.FIELDS
        return_code = LDEC.INDEX_FIELD_REPETITION
        if self.fields != self.fields_source:
            self._add_error_list(attr=attr, return_code=return_code)
    
    # 检查索引所选的字段 和 排列顺序的组合，是否在模型中唯一，如果不唯一，会向错误列表添加一条报错信息
    def check_field_order_unique(self, index_fields_set: set):
        attr = Index.ATTR.FIELDS
        return_code = LDEC.INDEX_FIELD_ORDER_UNIQUE
        field_str = "".join(self.fields)
        if field_str in index_fields_set:
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            index_fields_set.add(field_str)


class FieldCheckerService(CheckerService):

    attr_class = Field.ATTR
    uuid_error = LDEC.FIELD_UUID_ERROR
    uuid_unique_error = LDEC.FIELD_UUID_NOT_UNIQUE
    name_error = LDEC.FIELD_NAME_FAILED
    name_unique_error = LDEC.FIELD_NAME_NOT_UNIQUE
    allow_chinese_name = True
    allow_keywords = False
    
    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str, 
        document_uuid: str, document_name: str, 
        element: dict, model_uuid: str, field_sort: 0, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.model_uuid = model_uuid
        self.field_sort = field_sort
        
    def _add_error_list(self, attr: str, return_code: ReturnCode, **kwargs):
        kwargs.update({"model_uuid": self.model_uuid})
        return super()._add_error_list(attr, return_code, **kwargs)
    
    def initialize(self):
        super().initialize()
        self.display_name = self.element.get("display_name", "") or self.element_name
        self.description = self.element.get("description", "")
        self.type = int(self.element.get("type", Field.TYPE.STRING))
        self.length = int(self.element.get("length", 0) or 0)
        self.length = 300 if self.type == Field.TYPE.STRING and self.length < 1 else self.length
        self.decimals = int(self.element.get("decimals", 0) or 0)
        self.default = self.element.get("default", "")
        self.enum = self.element.get("enum", "")
        self.calculate_field = self.element.get("calculate_field", False)
        self.calculate_function = self.element.get("calculate_function", dict())
        self.generated_function = self.element.get("generated_function", dict())
        self.calculate_type = self.element.get("calculate_type", 0)
        self.calculate_func = self.element.get("calculate_func", "")
        self.is_unique = self.element.get("is_unique", False)
        self.is_required = self.element.get("is_required", False)
        self.is_visible = self.element.get("is_visible", False)
        self.is_serial = self.element.get("is_serial", False)
        self.serial_rule = self.element.get("serial_rule", 0)
        self.serial_prefix = self.element.get("serial_prefix", "")
        self.serial_prefix_info = self.element.get("serial_prefix_info", dict())
        self.serial_num_length = self.element.get("serial_num_length", 0)
        self.serial_gen_type = self.element.get("serial_gen_type", 0)
        self.serial_start_num = self.element.get("serial_start_num", 1)
        if self.serial_start_num == "" or self.serial_start_num is None:
            self.serial_start_num = 1
        self.check_function = self.element.get("check_function", dict())
        self.check_error_message = self.element.get("check_error_message", "")
        self.aggre_field = self.element.get("aggre_field", False)
        self.aggre_func = self.element.get("aggre_func", {})
        self.hierarchy_field = self.element.get("hierarchy_field", False)
        self.hierarchy_list = self.element.get("hierarchy_list", [])
        self.ext_tenant = self.kwargs.get("ext_tenant")
        self.hide = self.element.get("hide", False)
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
    
    def build_insert_query_data(self):
        return {
            ModelField.app_uuid.name: self.app_uuid,
            ModelField.module_uuid.name: self.module_uuid,
            ModelField.document_uuid.name: self.document_uuid,
            ModelField.model_uuid.name: self.model_uuid,
            ModelField.field_uuid.name: self.element_uuid,
            ModelField.field_name.name: self.element_name,
            ModelField.display_name.name: self.display_name,
            ModelField.description.name: self.description,
            ModelField.field_type.name: self.type,
            ModelField.sort.name: self.field_sort, 
            ModelField.length.name: self.length,
            ModelField.decimals.name: self.decimals,
            ModelField.default.name: ujson.dumps(self.default),
            ModelField.enum_uuid.name: self.enum,
            ModelField.calculate_field.name: self.calculate_field,
            ModelField.calculate_function.name: self.calculate_function,
            ModelField.generated_function.name: self.generated_function,
            ModelField.calculate_type.name: self.calculate_type,
            ModelField.calculate_func.name: self.calculate_func,
            ModelField.is_required.name: self.is_required,
            ModelField.is_visible.name: self.is_visible,
            ModelField.is_serial.name: self.is_serial, 
            ModelField.serial_num_length.name: self.serial_num_length, 
            ModelField.serial_prefix.name: self.serial_prefix, 
            ModelField.serial_prefix_info.name: self.serial_prefix_info, 
            ModelField.serial_rule.name: self.serial_rule, 
            ModelField.serial_gen_type.name: self.serial_gen_type, 
            ModelField.serial_start_num.name: self.serial_start_num, 
            ModelField.is_unique.name: self.is_unique,
            ModelField.check_function.name: self.check_function,
            ModelField.check_error_message.name: self.check_error_message,
            ModelField.aggre_field.name: self.aggre_field,
            ModelField.aggre_func.name: self.aggre_func,
            ModelField.hierarchy_field.name: self.hierarchy_field,
            ModelField.hierarchy_list.name: self.hierarchy_list,
            ModelField.hide.name: self.hide,
            ModelField.ext_tenant.name: self.ext_tenant
        }
    
    def build_update_query_data(self):
        return {
            ModelField.field_name.name: self.element_name,
            ModelField.display_name.name: self.display_name,
            ModelField.description.name: self.description,
            ModelField.field_type.name: self.type,
            ModelField.sort.name: self.field_sort, 
            ModelField.length.name: self.length,
            ModelField.decimals.name: self.decimals,
            ModelField.default.name: ujson.dumps(self.default),
            ModelField.enum_uuid.name: self.enum,
            ModelField.calculate_field.name: self.calculate_field,
            ModelField.calculate_function.name: self.calculate_function,
            ModelField.generated_function.name: self.generated_function,
            ModelField.calculate_type.name: self.calculate_type,
            ModelField.calculate_func.name: self.calculate_func,
            ModelField.is_required.name: self.is_required,
            ModelField.is_visible.name: self.is_visible,
            ModelField.is_serial.name: self.is_serial, 
            ModelField.serial_num_length.name: self.serial_num_length, 
            ModelField.serial_prefix.name: self.serial_prefix, 
            ModelField.serial_prefix_info.name: self.serial_prefix_info, 
            ModelField.serial_rule.name: self.serial_rule, 
            ModelField.serial_gen_type.name: self.serial_gen_type, 
            ModelField.serial_start_num.name: self.serial_start_num, 
            ModelField.is_unique.name: self.is_unique,
            ModelField.check_function.name: self.check_function,
            ModelField.check_error_message.name: self.check_error_message,
            ModelField.aggre_field.name: self.aggre_field,
            ModelField.aggre_func.name: self.aggre_func,
            ModelField.hierarchy_field.name: self.hierarchy_field,
            ModelField.hierarchy_list.name: self.hierarchy_list,
            ModelField.hide.name: self.hide,
            ModelField.is_delete.name: False,
            ModelField.ext_tenant.name: self.ext_tenant
        }

    @checker.run
    def check_filed_check_function(self):
        if self.check_function:
            if not self.check_function.get("expr"):
                attr = Field.ATTR.CHECKFUN
                return_code = LDEC.CHECK_FUNCTION_NOT_FIELD_IN_FIELD
                return_code.message = return_code.message.format(name=self.element_name)
                # app_log.error(f"check_function: {self.check_function}")
                self._add_error_list(attr, return_code)


    def build_update_query(self):
        query_data = self.build_update_query_data()
        return ModelField.update(**query_data).where(ModelField.field_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(field_uuid):
        return ModelField.update(**{
                ModelField.is_delete.name: True
            }).where(ModelField.field_uuid==field_uuid)
    
    # 检查该字段是 新增 或 更新
    def check_modify(self, document_field_uuid_set):
        if self.element_uuid in document_field_uuid_set:
            query = self.build_update_query()
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

    # 检查 display_name 格式，是否正确，如果不通过，会向错误列表添加一条报错信息
    def check_display_name(self):
        attr = Field.ATTR.DISPLAY_NAME
        return_code = LDEC.FIELD_DISPLAY_NAME_FAILED
        if not check_lemon_name(self.display_name, chinese=True, size=40):
            app_log.error(f"display_name: {self.display_name}")
            self._add_error_list(attr, return_code)
            raise CheckNameError(message=return_code)
        else:
            if self.display_name.lower() in SystemField.FIELD_NAME_LIST + ["tenant_uuid"]:
                app_log.info("field display_name cannot overlap with the system fields: %s", self.display_name.lower())
                return_code = LDEC.FILED_NAME_ERROR
                return_code.message = return_code.message_init.format(field_name=self.display_name)
                self._add_error_list(attr, return_code)
                raise CheckNameError(message=return_code)

    # 检查字段类型，是否在允许的字段类型范围内，如果不在，会向错误列表添加一条报错信息
    def check_type(self):
        attr = Field.ATTR.TYPE
        return_code = LDEC.FIELD_TYPE_NOT_SUPPORT
        if self.type not in Field.TYPE.ALL:
            self._add_error_list(attr=attr, return_code=return_code)
        return_code = LDEC.FIELD_TYPE_OBSOLETE
        if self.type in Field.TYPE.OBSOLETE:
            return_code.message = return_code.message_init.format(Field.TYPE.OBSOLETE.get(self.type))
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查枚举类型，是否在应用中，如果不在，会向错误列表添加一条报错信息
    def check_enum(self, app_enum_uuid_set):
        attr = Field.ATTR.ENUM
        return_code = LDEC.FIELD_ENUM_NOE_EXISTS
        if self.type == Field.TYPE.ENUM:
            element_uuid = self.element.get("uuid")
            title = self.element.get("name")
            self.update_enum_reference(self.enum, title, element_uuid, attr)
            if self.enum not in app_enum_uuid_set:
                self._add_error_list(attr=attr, return_code=return_code)

    def check_default_value(self, app_enum_value_uuid_set):
        attr = Field.ATTR.NAME
        return_code = LDEC.FIELD_DEFAULT_VALUE_ERROR
        if self.default:
            default_value_dict = self.default
            if default_value_dict:
                enum_value_uuid = default_value_dict.get("enum_value_uuid")
                value_editor_type = default_value_dict.get("type")
                if (value_editor_type == ValueEditorType.ENUM and
                    enum_value_uuid is not None and
                        enum_value_uuid not in app_enum_value_uuid_set):
                    self._add_error_list(attr=attr, return_code=LDEC.FIELD_DEFAULT_VALUE_ENUM_NOT_EXISTS)
                default_value = LemonBaseValueEditor(**default_value_dict).value
                column_lemon_type = self.type
                if default_value is not None:
                    if column_lemon_type == FieldType.STRING:  # str
                        if not isinstance(default_value, str):
                            self._add_error_list(attr=attr, return_code=return_code)
                    elif column_lemon_type == FieldType.FILE:  # file
                        if not isinstance(default_value, str):
                            self._add_error_list(attr=attr, return_code=return_code)
                    elif column_lemon_type in [FieldType.INTEGER, FieldType.DECIMAL]:  # int, float
                        if not isinstance(default_value, (int, float)):
                            self._add_error_list(attr=attr, return_code=return_code)
                    elif column_lemon_type == FieldType.BOOLEAN:  # bool
                        if not isinstance(default_value, bool):
                            self._add_error_list(attr=attr, return_code=return_code)
                    elif column_lemon_type == FieldType.DATETIME:  # datetime
                        if not isinstance(default_value, (int, float)):
                            self._add_error_list(attr=attr, return_code=return_code)
                    elif column_lemon_type == FieldType.ENUM:  # enum
                        if not isinstance(default_value, (int, str)):
                            self._add_error_list(attr=attr, return_code=return_code)

    # 检查是否为旧流水号,旧数据需要新增check_serial_prefix
    def check_old_serial_update(self):
        if self.serial_prefix is not None:
            if isinstance(self.serial_prefix, str) and self.serial_prefix:
                if isinstance(self.serial_prefix_info, dict) and not self.serial_prefix_info:
                    self.serial_prefix_info = {
                        'is_monitor': True, 'once': True, 'type': 0,
                        'uuid': lemon_uuid(), 'value': self.serial_prefix
                    }
                    self.element.update({"serial_prefix_info": self.serial_prefix_info})

    # 检查流水号
    def check_serial(self):
        if self.is_serial:
            if self.type == Field.TYPE.STRING:
                self.check_serial_rule()
                self.check_serial_gen_type()
                self.check_old_serial_update()
                if self.calculate_field:
                    attr = Field.ATTR.SERIAL
                    return_code = LDEC.SERIAL_CALCULATE_ERROR
                    self._add_error_list(attr=attr, return_code=return_code)
            else:
                attr = Field.ATTR.TYPE
                return_code = LDEC.FIELD_SERIAL_TYPE_ERROR
                self._add_error_list(attr=attr, return_code=return_code)

    def check_serial_rule(self):
        if self.serial_rule not in SerialRule.BUILT_IN:
            attr = Field.ATTR.SERIAL
            return_code = LDEC.FIELD_SERIAL_TYPE_ERROR
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            # TODO 这里应该由serial_rule决定对应的检查, 目前serial_rule只有一种, 先写死
            self.check_prefix()
            self.check_serial_num_length()
            self.check_serial_start_num()
    
    def check_serial_gen_type(self):
        if self.serial_gen_type not in SerialGenType.ALL:
            attr = Field.ATTR.SERIAL
            return_code = LDEC.FIELD_SERIAL_GEN_TYPE_ERROR
            self._add_error_list(attr=attr, return_code=return_code)

    def check_prefix(self):
        if isinstance(self.serial_prefix, str):
            serial_name = self.serial_prefix
        elif self.serial_prefix.get('type') is not None:
            serial_type = self.serial_prefix.get('type')
            if serial_type == 0:
                serial_name = self.serial_prefix.get('value')
            else:
                serial_name = ""
        else:
            serial_name = ""
        if serial_name != "":
            if not check_common_name(serial_name, chinese=True):
                attr = Field.ATTR.SERIAL
                return_code = LDEC.FIELD_SERIAL_PREFIX_ERROR
                self._add_error_list(attr=attr, return_code=return_code)

    def check_serial_num_length(self):
        # 使用八位日期YYYYMMDD
        if self.serial_rule == SerialRule.PREFIX_DATE_SERIAL:
            date_length = 8
        else:
            date_length = 0
        if (
            not isinstance(self.serial_num_length, int) or
            self.serial_num_length < 1 or
            self.serial_num_length > (self.length -
                                      len(self.serial_prefix) - date_length)
        ):
            attr = Field.ATTR.SERIAL
            return_code = LDEC.FIELD_SERIAL_NUM_LEN_ERROR
            self._add_error_list(attr=attr, return_code=return_code)

    def check_serial_start_num(self):
        if self.serial_rule == SerialRule.PREFIX_SERIAL:
            if (
                self.serial_start_num < 0 or
                self.serial_start_num > int(int(self.serial_num_length) * "9")
            ):
                attr = Field.ATTR.SERIAL
                return_code = LDEC.FIELD_SERIAL_START_NUM_ERROR
                self._add_error_list(attr=attr, return_code=return_code)

    def check_decimal_valid(self):
        if self.type in [FieldType.DECIMAL]:
            if self.length < self.decimals:
                attr = Field.ATTR.TYPE
                return_code = LDEC.DECIMAL_LENGTH_ERROR
                self._add_error_list(attr=attr, return_code=return_code)
    
    def check_calc_field_have_func(self, app_func_uuid_dict):
        if self.calculate_field:
            if self.calculate_type == 1:  # 0 值编辑器  1 云函数  2 生成列
                if self.calculate_func is None or self.calculate_func == '':
                    attr = Field.ATTR.TYPE
                    return_code = LDEC.MODEL_NAME_FIELD_NOT_FUNC
                    return_code.message = return_code.message.format(field_name=self.element_name)
                    self._add_error_list(attr=attr, return_code=return_code)
                elif self.calculate_func not in app_func_uuid_dict:
                    attr = Field.ATTR.TYPE
                    return_code = LDEC.MODEL_NAME_FIELD_FUNC_NOT_EXISTS
                    return_code.message = return_code.message.format(field_name=self.element_name)
                    self._add_error_list(attr=attr, return_code=return_code)
            if self.calculate_type == 0:
                if self.calculate_function is None or self.calculate_function == {}:
                    attr = Field.ATTR.TYPE
                    return_code = LDEC.MODEL_FIELD_VALUE_EDITOR_NONE
                    return_code.message = return_code.message.format(field_name=self.element_name)
                    self._add_error_list(attr=attr, return_code=return_code)
                else:
                    self.check_calculate_function(self.calculate_function)
            if self.calculate_type == 2:
                self.check_calculate_function(self.generated_function)

    def check_calculate_function(self, function):
        if not isinstance(function, dict):
            return

        editor_type = function.get("type")
        if editor_type != ValueEditorType.EXPR:
            return

        for exprarr in function.get("exprArr", []):
            value = exprarr.get("value", {})
            if value.get("field", "") == self.element_uuid:
                attr = Field.ATTR.TYPE
                return_code = LDEC.CALCULATE_FIELD_VALUE_EDITOR_ERROR
                return_code.message = return_code.message.format(field_name=self.element_name)
                self._add_error_list(attr=attr, return_code=return_code)

    def check_decimals(self):
        if self.type == FieldType.DECIMAL:
            if not self.decimals:
                attr = Field.ATTR.DECIMALS
                return_code = LDEC.DECIMALS_NONE_ERROR
                self._add_error_list(attr=attr, return_code=return_code)

    def check_description(self):
        if '%' in self.description:
            attr = Field.ATTR.TYPE
            return_code = LDEC.FIELD_DESCRIPTION_ERROR
            self._add_error_list(attr=attr, return_code=return_code)


class ModelCheckerService(CheckerService):

    attr_class = ModelDocument.ATTR
    uuid_error = LDEC.MODEL_UUID_ERROR
    uuid_unique_error = LDEC.MODEL_UUID_NOT_UNIQUE
    name_error = LDEC.MODEL_NAME_FAILED
    name_unique_error = LDEC.MODEL_NAME_NOT_UNIQUE
    allow_chinese_name = True
    allow_keywords = False
    allow_lemon_keywords = False
    
    def initialize(self):
        super().initialize()
        self.fields = self.element.get("fields", [])
        self.indexes = self.element.get("indexes", [])
        self.display_name = self.element.get("display_name", "") or self.element_name
        self.description = self.element.get("description", "")
        self.is_temp = self.element.get("is_temp", False)
        self.is_import = self.element.get("is_import", False)
        self.is_ext = self.element.get("is_extension", False)
        self.select_enabled = self.element.get("is_search", True)
        self.events = self.element.get("events", [])
        self.ext_tenant = self.kwargs.get("ext_tenant")
        self.relationships = self.kwargs.get("relationships", [])
        self.find_name_field()
        self.data_name = ""
        # self.data_field = self.element.get("data_field", "")
        # self.sort_field = self.element.get("sort_field", "")
        # self.check_field = self.element.get("check_field", "")
        # self.user_model = self.element.get("user_model", "")
        # self.department_model = self.element.get("department_model", "")
        self.field_error_list = []
        self.index_error_list = []
        self.relationship_error_list = []
        self.field_uuid_dict = dict()
        self.field_uuid_set = set()
        self.field_name_set = set()
        self.index_uuid_set = set()
        self.index_name_set = set()
        self.index_fields_set = set()
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []
        self.field_insert_list = []
        self.field_update_list = []
        self.field_delete_list = []
        self.index_insert_list = []
        self.index_update_list = []
        self.index_delete_list = []
    
    def find_name_field(self):
        self.name_field = self.element.get("name_field")
        if self.name_field is None:
            self.name_field = ""
            self.element.update({"name_field": self.name_field})
        self.name_field = "" if self.name_field is None else self.name_field
    
    def build_insert_query_data(self):
        return {
            ModelBasic.app_uuid.name: self.app_uuid,
            ModelBasic.module_uuid.name: self.module_uuid,
            ModelBasic.document_uuid.name: self.document_uuid,
            ModelBasic.model_uuid.name: self.element_uuid,
            ModelBasic.model_name.name: self.element_name,
            ModelBasic.display_name.name: self.display_name,
            ModelBasic.description.name: self.description,
            ModelBasic.is_temp.name: self.is_temp,
            ModelBasic.fields.name: list(self.field_name_set),
            ModelBasic.events.name: list(self.events),
            ModelBasic.ext_tenant.name: self.ext_tenant,
            ModelBasic.name_field.name: self.name_field,
            ModelBasic.data_name.name: self.data_name,
            ModelBasic.is_import.name:self.is_import,
            ModelBasic.select_enabled.name:self.select_enabled,
            # ModelBasic.data_field.name: self.data_field,
            # ModelBasic.sort_field.name: self.sort_field,
            # ModelBasic.check_field.name: self.check_field,
            # ModelBasic.user_model.name: self.user_model,
            # ModelBasic.department_model.name: self.department_model
        }
    
    def build_update_query_data(self):
        return {
            ModelBasic.model_name.name: self.element_name,
            ModelBasic.display_name.name: self.display_name,
            ModelBasic.description.name: self.description,
            ModelBasic.is_temp.name: self.is_temp,
            ModelBasic.fields.name: list(self.field_name_set),
            ModelBasic.events.name: list(self.events),
            ModelBasic.ext_tenant.name: self.ext_tenant,
            ModelBasic.name_field.name: self.name_field,
            ModelBasic.data_name.name: self.data_name,
            ModelBasic.is_import.name: self.is_import,
            ModelBasic.select_enabled.name: self.select_enabled,
            # ModelBasic.data_field.name: self.data_field,
            # ModelBasic.sort_field.name: self.sort_field,
            # ModelBasic.check_field.name: self.check_field,
            # ModelBasic.user_model.name: self.user_model,
            # ModelBasic.department_model.name: self.department_model,
            ModelBasic.is_delete.name: False
        }
    
    def build_update_query(self):
        query_data = self.build_update_query_data()
        return ModelBasic.update(**query_data).where(ModelBasic.model_uuid==self.element_uuid)
    
    @staticmethod
    def build_delete_query(model_uuid):
        return ModelBasic.update(**{
                ModelBasic.is_delete.name: True
            }).where(ModelBasic.model_uuid==model_uuid)
    
    # 检查该模型是 新增 或 更新
    def check_modify(self, document_model_uuid_set):
        build_query = True
        if self.ext_tenant and not self.is_ext:
            build_query = False
        if build_query:
            if self.element_uuid in document_model_uuid_set:
                    query = self.build_update_query()
                    query_list = self.update_query_list
            else:
                query = self.build_insert_query_data()
                query_list = self.insert_query_list
            query_list.append(query)
    
    # 模型字段检查
    def check_fields(self, document_uuid_set, app_enum_uuid_set, app_func_uuid_dict, app_enum_value_uuid_set):
        this_uuid_set = set()
        for field_sort, field in enumerate(self.fields, start=0):
            if self.new_ext_doc:
                need_check = True
            else:
                is_ext = field.get("is_extension")
                need_check = True
                if self.ext_tenant and not is_ext:
                    need_check = False
            field_check_service = FieldCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, self.document_uuid, self.document_name, 
                field, self.element_uuid, field_sort=field_sort, ext_tenant=self.ext_tenant)
            field_check_service.document_other_info = self.document_other_info
            field_uuid = field_check_service.element_uuid
            try:
                field_check_service.check_uuid()
                if need_check:
                    field_check_service.check_uuid_unique(self.field_uuid_set)
                field_check_service.check_name()
                if need_check:
                    field_check_service.check_name_unique(self.field_name_set)
                field_check_service.check_display_name()
                field_check_service.check_filed_check_function()
                field_check_service.check_type()
                field_check_service.check_enum(app_enum_uuid_set)
                field_check_service.check_default_value(app_enum_value_uuid_set)
                field_check_service.check_serial()
                field_check_service.check_decimal_valid()
                field_check_service.check_calc_field_have_func(app_func_uuid_dict)
                field_check_service.check_decimals()
                field_check_service.check_description()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(field_check_service)
                raise e
            else:
                self.update_any_list(field_check_service)
            self.field_uuid_dict.update({field_uuid: field})
            this_uuid_set.add(field_uuid)

            # 找到新增 或 更新的 字段
            if need_check:
                field_check_service.check_modify(document_uuid_set)
            if field_check_service.insert_query_list:
                self.field_insert_list.extend(field_check_service.insert_query_list)
            if field_check_service.update_query_list:
                self.field_update_list.extend(field_check_service.update_query_list)
            
            # 找出删除的 字段，将其 is_delete 置为 True
        delete_uuid_set = document_uuid_set - this_uuid_set
        for this_uuid in delete_uuid_set:
            query = FieldCheckerService.build_delete_query(this_uuid)
            self.field_delete_list.append(query)
    
    # 模型索引检查
    def check_indexes(self, document_uuid_set):
        this_uuid_set = set()
        for index in self.indexes:
            index_check_service = IndexCheckerService(
                self.app_uuid, self.module_uuid, self.module_name, 
                self.document_uuid, self.document_name, index, self.element_uuid)
            index_uuid = index_check_service.element_uuid
            try:
                index_check_service.check_uuid()
                index_check_service.check_uuid_unique(self.index_uuid_set)
                index_check_service.check_name()
                index_check_service.check_name_unique(self.index_name_set)
                index_check_service.check_is_unique()
                index_check_service.check_fields_is_null()
                index_check_service.check_field_exists(self.field_uuid_dict, self.relationships)
                index_check_service.check_field_repetition()
                index_check_service.check_field_order_unique(self.index_fields_set)
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(index_check_service)
                raise e
            else:
                self.update_any_list(index_check_service)
            this_uuid_set.add(index_uuid)

            # 找到新增 或 更新的 索引
            index_check_service.check_modify(document_uuid_set)
            if index_check_service.insert_query_list:
                self.index_insert_list.extend(index_check_service.insert_query_list)
            if index_check_service.update_query_list:
                self.index_update_list.extend(index_check_service.update_query_list)
            
            # 找出删除的 索引，将其 is_delete 置为 True
        delete_uuid_set = document_uuid_set - this_uuid_set
        for this_uuid in delete_uuid_set:
            query = IndexCheckerService.build_delete_query(this_uuid)
            self.index_delete_list.append(query)

    # 检查 display_name 格式，是否正确，如果不通过，会向错误列表添加一条报错信息
    def check_display_name(self):
        attr = ModelDocument.ATTR.DISPLAY_NAME
        return_code = LDEC.MODEL_DISPLAY_NAME_FAILED
        if not check_lemon_name(self.display_name, chinese=True, size=40):
            app_log.error(f"display_name: {self.display_name}")
            self._add_error_list(attr, return_code)
            raise CheckNameError(message=return_code)
        system_names = set()
        for v in UUID._ALL + UUID._NEED_TENANT + UUID._ALL_WF:
            if hasattr(v, "display_name"):
                system_names.add(v.display_name.value)
        if self.display_name in system_names:
            return_code = LDEC.MODEL_NAME_REPEAT_SYS_MODEL
            self._add_error_list(attr, return_code)
    
    def check_field_in_model(self, field_uuid: str, attr: str, return_code: ReturnCode):
        if field_uuid:
            field_info = self.field_uuid_dict.get(field_uuid)
            if field_info:
                if isinstance(field_info, dict):
                    field_display_name = field_info.get("display_name")
                    self.data_name = field_display_name
                    aggre_field = field_info.get("aggre_field")
                    if aggre_field is True:
                        self._add_error_list(attr, LDEC.MODEL_NAME_FIELD_IS_AGGRE_FIELD)
                    hierarchy_field = field_info.get("hierarchy_field")
                    if hierarchy_field == 1:
                        self._add_error_list(attr, LDEC.MODEL_NAME_FIELD_IS_HIERARCHY_FIELD)
            else:
                # 数据名称允许为空
                self.name_field = ""
                self.element.update({"name_field": self.name_field})
                # self._add_error_list(attr, return_code)
    
    # 检查数据名称字段，是否在模型中存在，如果不存在，清空该名称
    def check_name_field(self):
        attr = ModelDocument.ATTR.NAME_FIELD
        field_uuid = self.name_field
        self.check_field_in_model(field_uuid, attr, LDEC.MODEL_NAME_FIELD_NOT_EXISTS)
    
    # # 检查排序字段，是否在模型中存在，如果不存在，会向错误列表添加一条报错信息
    # def check_sort_field(self):
    #     attr = ModelDocument.ATTR.SORT_FIELD
    #     field_uuid = self.sort_field
    #     self.check_field_in_model(field_uuid, attr, LDEC.MODEL_SORT_FIELD_NOT_EXISTS)
    
    # # 检查有效性字段，是否在模型中存在，如果不存在，会向错误列表添加一条报错信息
    # def check_check_field(self):
    #     attr = ModelDocument.ATTR.CHECK_FIELD
    #     field_uuid = self.check_field
    #     self.check_field_in_model(field_uuid, attr, LDEC.MODEL_CHECK_FIELD_NOT_EXISTS)
    
    # # 检查所属用户关联模型，是否在整个应用的数据模型中存在，如果不存在，会向错误列表添加一条报错信息
    # def check_user_model(self, model_uuid_set: set):
    #     attr = ModelDocument.ATTR.USER_MODEL
    #     return_code = LDEC.MODEL_USER_MODEL_NOT_EXISTS
    #     if self.user_model:
    #         if self.user_model not in model_uuid_set:
    #             self._add_error_list(attr=attr, return_code=return_code)
    
    # # 检查所属部门关联模型，是否在整个应用的数据模型中存在，如果不存在，会向错误列表添加一条报错信息
    # def check_department_model(self, model_uuid_set: set):
    #     attr = ModelDocument.ATTR.DEPARTMENT_MODEL
    #     return_code = LDEC.MODEL_DEPARTMENT_MODEL_NOT_EXISTS
    #     if self.department_model:
    #         if self.department_model not in model_uuid_set:
    #             self._add_error_list(attr=attr, return_code=return_code)

    def check_event(self):
        attr = self.attr_class.EVENT
        self._check_event(attr)

    def _check_event(self, attr, element_data=None, **kwargs):
        events = self.element.get("events", [])
        for event_info in events:
            element_uuid = self.element_uuid
            title = self.element_name
            func_uuid = event_info.get("func")
            if func_uuid:
                if func_uuid in self.app_func_uuid_dict:
                    self.update_cloud_func_reference(func_uuid, title, element_uuid, attr)
                else:
                    self._add_error_list(attr, return_code=LDEC.FUNC_IS_DELETED, element_data=self.element)
            else:
                self._add_error_list(attr, return_code=LDEC.FUNC_NOT_EXIST, element_data=self.element)

class RelationshipCheckerService(CheckerService):

    attr_class = Relationship.ATTR
    uuid_error = LDEC.RELATIONSHIP_UUID_ERROR
    uuid_unique_error = LDEC.RELATIONSHIP_UUID_NOT_UNIQUE
    name_error = LDEC.RELATIONSHIP_NAME_FAILED
    name_unique_error = LDEC.RELATIONSHIP_NAME_NOT_UNIQUE
    allow_chinese_name = True
    allow_keywords = False

    def initialize(self):
        super().initialize()
        self.system_relationship_backref = self.kwargs.get("system_relationship_backref", {})
        self.type = int(self.element.get("type", Relationship.TYPE.ONE_TO_MANY))
        self.display_name = self.element.get("display_name", "") or self.element_name
        self.description = self.element.get("description", "")
        self.source_model_uuid = self.element.get("source_model", "")
        self.target_model_uuid = self.element.get("target_model", "")
        self.frontref = self.element.get("frontref", "")
        self.backref = self.element.get("backref", "")
        self.source_model_on_delete = int(self.element.get(
            "source_model_on_delete", Relationship.DELETE_TYPE.NO_ACTION))
        self.target_model_on_delete = int(self.element.get(
            "target_model_on_delete", Relationship.DELETE_TYPE.NO_ACTION))
        self.extra = self.element.get("extra", {})
        self.is_system = self.element.get("is_system", False)
        self.system_relationship = False
        self.to_field_name = self.element.get("to_field_name", "id")
        self.insert_query_list = []
        self.update_query_list = []
        self.delete_query_list = []

    def _add_error_list(self, attr: str, return_code: ReturnCode, **kwargs):
        kwargs.update({"model_uuid": self.source_model_uuid})
        return super()._add_error_list(attr, return_code, **kwargs)

    def build_insert_query_data(self):
        return {
            RelationshipBasic.app_uuid.name: self.app_uuid,
            RelationshipBasic.module_uuid.name: self.module_uuid,
            RelationshipBasic.document_uuid.name: self.document_uuid,
            RelationshipBasic.relationship_uuid.name: self.element_uuid,
            RelationshipBasic.relationship_name.name: self.element_name,
            RelationshipBasic.display_name.name: self.display_name,
            RelationshipBasic.description.name: self.description,
            RelationshipBasic.relationship_type.name: self.type,
            RelationshipBasic.source_model.name: self.source_model_uuid,
            RelationshipBasic.target_model.name: self.target_model_uuid,
            RelationshipBasic.frontref.name: self.frontref,
            RelationshipBasic.backref.name: self.backref,
            RelationshipBasic.source_model_on_delete.name: self.source_model_on_delete,
            RelationshipBasic.target_model_on_delete.name: self.target_model_on_delete,
            RelationshipBasic.extra.name: self.extra,
            RelationshipBasic.is_system.name: self.is_system,
            RelationshipBasic.system_relationship.name: self.system_relationship,
            RelationshipBasic.to_field_name.name: self.to_field_name
        }

    def build_insert_query_data_by_other(self, relationship_uuid):
        return {
            RelationshipBasic.app_uuid.name: self.app_uuid,
            RelationshipBasic.module_uuid.name: self.module_uuid,
            RelationshipBasic.document_uuid.name: self.document_uuid,
            RelationshipBasic.relationship_uuid.name: relationship_uuid,
            RelationshipBasic.relationship_name.name: self.element_name + "other",
            RelationshipBasic.display_name.name: self.display_name,
            RelationshipBasic.description.name: self.description,
            RelationshipBasic.relationship_type.name: self.type,
            RelationshipBasic.source_model.name: self.target_model_uuid,
            RelationshipBasic.target_model.name: self.source_model_uuid,
            RelationshipBasic.frontref.name: self.backref,
            RelationshipBasic.backref.name: self.frontref,
            RelationshipBasic.source_model_on_delete.name: self.target_model_on_delete,
            RelationshipBasic.target_model_on_delete.name: self.source_model_on_delete,
            RelationshipBasic.extra.name: self.extra,
            RelationshipBasic.is_system.name: self.is_system,
            RelationshipBasic.system_relationship.name: self.system_relationship,
            RelationshipBasic.to_field_name.name: self.to_field_name
        }

    def build_update_query_data(self):
        return {
            RelationshipBasic.relationship_name.name: self.element_name,
            RelationshipBasic.description.name: self.description,
            RelationshipBasic.relationship_type.name: self.type,
            RelationshipBasic.display_name.name: self.display_name,
            RelationshipBasic.source_model.name: self.source_model_uuid,
            RelationshipBasic.target_model.name: self.target_model_uuid,
            RelationshipBasic.frontref.name: self.frontref,
            RelationshipBasic.backref.name: self.backref,
            RelationshipBasic.source_model_on_delete.name: self.source_model_on_delete,
            RelationshipBasic.target_model_on_delete.name: self.target_model_on_delete,
            RelationshipBasic.is_delete.name: False,
            RelationshipBasic.extra.name: self.extra,
            RelationshipBasic.is_system.name: self.is_system,
            RelationshipBasic.system_relationship.name: self.system_relationship,
            RelationshipBasic.to_field_name.name: self.to_field_name
        }

    def build_update_query_data_by_other(self):
        return {
            RelationshipBasic.relationship_name.name: self.element_name + "other",
            RelationshipBasic.display_name.name: self.display_name,
            RelationshipBasic.description.name: self.description,
            RelationshipBasic.relationship_type.name: self.type,
            RelationshipBasic.source_model.name: self.target_model_uuid,
            RelationshipBasic.target_model.name: self.source_model_uuid,
            RelationshipBasic.frontref.name: self.backref,
            RelationshipBasic.backref.name: self.frontref,
            RelationshipBasic.source_model_on_delete.name: self.target_model_on_delete,
            RelationshipBasic.target_model_on_delete.name: self.source_model_on_delete,
            RelationshipBasic.is_delete.name: False,
            RelationshipBasic.extra.name: self.extra,
            RelationshipBasic.is_system.name: self.is_system,
            RelationshipBasic.system_relationship.name: self.system_relationship,
            RelationshipBasic.to_field_name.name: self.to_field_name
        }

    def build_update_query(self, relationship_uuid):
        query_data = self.build_update_query_data()
        return RelationshipBasic.update(**query_data).where(
            RelationshipBasic.relationship_uuid == relationship_uuid)

    def build_update_query_by_other(self, relationship_uuid):
        query_data = self.build_update_query_data_by_other()
        return RelationshipBasic.update(**query_data).where(
            RelationshipBasic.relationship_uuid == relationship_uuid)

    @staticmethod
    def build_delete_query(relationship_uuid):
        return RelationshipBasic.update(**{
            RelationshipBasic.is_delete.name: True
        }).where(RelationshipBasic.relationship_uuid == relationship_uuid)

    def check_modify(self, document_relationship_uuid_set):
        if self.element_uuid in document_relationship_uuid_set:
            query = self.build_update_query(self.element_uuid)
            query_list = self.update_query_list
        else:
            query = self.build_insert_query_data()
            query_list = self.insert_query_list
        query_list.append(query)

        # 血和泪的教训；多对多生成两条关联记录；
        # 思豪在生成运行时代码时已经处理了，如果有问题，再试试下面的方法吧
        # if self.type == Relationship.TYPE.MANY_TO_MANY:
        #     other_uuid = base64.b64encode(
        #         uuid.UUID(self.element_uuid.encode()).bytes).decode().rstrip(
        #             "=").replace("+", "_").replace("/", "-")

        #     if other_uuid in document_relationship_uuid_set:
        #         query = self.build_update_query_by_other(other_uuid)
        #         query_list = self.update_query_list
        #     else:
        #         query = self.build_insert_query_data_by_other(other_uuid)
        #         query_list = self.insert_query_list
        #     query_list.append(query)

    def check_display_name(self):
        return_code = LDEC.RELATIONSHIP_DISPLAY_NAME_FAILED
        if len(self.display_name) > 40 or not check_lemon_name(self.display_name, chinese=True, size=40):
            # self._add_error_list(attr=attr, return_code=return_code)
            raise CheckNameError(message=return_code)

    # 检查 映射关系，是否在可选范围内，如果不在，会向错误列表添加一条报错信息
    def check_type(self):
        attr = Relationship.ATTR.TYPE
        return_code = LDEC.RELATIONSHIP_TYPE_NOT_SUPPORT
        if self.type not in Relationship.TYPE.ALL:
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            if not self.is_system:
                self.check_system_model_is_many(attr)

    def check_system_model_is_many(self, attr):
        source_is_system = self.source_model_uuid in SystemTable.ALL_UUIDS
        target_is_system = self.target_model_uuid in SystemTable.ALL_UUIDS
        return_code = LDEC.RELATIONSHIP_SYSTEM_TABLE_IS_SOURCE
        if self.type == RelationshipType.MANY_TO_MANY:
            if source_is_system or target_is_system:
                name = SystemTable.UUID_NAME.get(self.source_model_uuid)
                return_code.message = return_code.message_init.format(name=name)
                self._add_error_list(attr=attr, return_code=return_code)
        elif self.type in [RelationshipType.ONE_TO_MANY, RelationshipType.ONE_TO_ONE]:
            if self.source_model_uuid in SystemTable.ALL_UUIDS:
                name = SystemTable.UUID_NAME.get(self.source_model_uuid)
                return_code.message = return_code.message_init.format(name=name)
                self._add_error_list(attr=attr, return_code=return_code)

    # 检查 所选的关联模型，是否在应用中存在，如果不在，会向错误列表添加一条报错信息
    def check_relationship_model_exists(self, model_uuid_dict: dict):
        attr = Relationship.ATTR.MODEL
        source_model = model_uuid_dict.get(self.source_model_uuid, None)
        if self.source_model_uuid:
            if not source_model or source_model.get("is_delete"):
                return_code = LDEC.RELATIONSHIP_SOURCE_MODEL_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
        # app_log.info(f"{SystemTable.UUIDS}")
        target_model = model_uuid_dict.get(self.target_model_uuid, None)
        if self.target_model_uuid:
            if not target_model or target_model.get("is_delete"):
                return_code = LDEC.RELATIONSHIP_TARGET_MODEL_NOT_EXISTS
                self._add_error_list(attr=attr, return_code=return_code)
            if self.target_model_uuid in SystemTable.UUIDS:
                self.system_relationship = True
                self.element.update({"system_relationship": True})

    # 检查 起始模型 的 删除行为，是否在可选范围内，如果不在，会向错误列表添加一条报错信息
    def check_source_model_on_delete(self):
        attr = Relationship.ATTR.SOURCE_MODEL_ON_DELETE
        return_code = LDEC.RELATIONSHIP_SOURCE_ON_DELETE
        if self.source_model_on_delete not in Relationship.DELETE_TYPE.ALL:
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查 目标模型 的 删除行为，是否在可选范围内，如果不在，会向错误列表添加一条报错信息
    def check_target_model_on_delete(self):
        attr = Relationship.ATTR.TARGET_MODEL_ON_DELETE
        return_code = LDEC.RELATIONSHIP_TARGET_ON_DELETE
        if self.target_model_on_delete not in Relationship.DELETE_TYPE.ALL:
            self._add_error_list(attr=attr, return_code=return_code)

    # 检查 起始模型 链接到 目标模型的字段 frontref，如果名称在 起始模型 的字段内，会向错误列表添加一条报错信息
    def check_frontref(self, app_model_uuid_dict: dict):
        attr = Relationship.ATTR.FRONTREF
        model = app_model_uuid_dict.setdefault(self.source_model_uuid, {})
        model_name = model.setdefault(ModelBasic.model_name.name, "")
        fields = model.setdefault(ModelBasic.fields.name, [])
        fields_set = set(fields)
        self._check_name_kwords(attr)
        if self.frontref in SystemField.FIELD_NAME_LIST:
            return_code = LDEC.FILED_NAME_ERROR
            return_code.message = return_code.message_init.format(field_name=self.frontref)
            self._add_error_list(attr, return_code)
        if not check_lemon_name(self.frontref, chinese=True, size=40):
            return_code = LDEC.RELATIONSHIP_FRONTREF_FAILED
            self._add_error_list(attr=attr, return_code=return_code)
        if self.frontref in fields_set:
            return_code = LDEC.RELATIONSHIP_FRONTREF_NOT_UNIQUE
            return_code.message = return_code.message_init.format(self.element_name, self.frontref, model_name)
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            fields.append(self.frontref)

    # 检查 目标模型 链接到 起始模型的字段 backref，如果名称在 目标模型 的字段内，会向错误列表添加一条报错信息
    def check_backref(self, app_model_uuid_dict: dict):
        attr = Relationship.ATTR.BACKREF
        model = app_model_uuid_dict.setdefault(self.target_model_uuid, {})
        model_name = model.setdefault(ModelBasic.model_name.name, "")
        fields = model.setdefault(ModelBasic.fields.name, [])
        fields_set = set(fields)
        self._check_name_kwords(attr)
        if self.backref in SystemField.FIELD_NAME_LIST:
            return_code = LDEC.FILED_NAME_ERROR
            return_code.message = return_code.message_init.format(field_name=self.backref)
            self._add_error_list(attr, return_code)
        if self.target_model_uuid in SystemTable.ALL_UUIDS:
            relationship_backref = self.system_relationship_backref.get(self.target_model_uuid, {})
            backref_dict = relationship_backref.get(self.backref, {})
            if self.element_uuid in backref_dict:
                del backref_dict[self.element_uuid]
            if backref_dict:
                relation_names = " ".join(backref_dict.values())
                return_code = LDEC.SYSTEM_BACKREF_FILED_NAME_ERROR
                return_code.message = return_code.message_init.format(
                    field_name=self.backref, relation_name=relation_names)
                self._add_error_list(attr, return_code)

        if not check_lemon_name(self.backref, chinese=True, size=40):
            return_code = LDEC.RELATIONSHIP_BACKREF_FAILED
            self._add_error_list(attr=attr, return_code=return_code)
        if self.backref in fields_set:
            return_code = LDEC.RELATIONSHIP_BACKREF_NOT_UNIQUE
            return_code.message = return_code.message_init.format(self.element_name, self.backref, model_name)
            self._add_error_list(attr=attr, return_code=return_code)
        else:
            fields.append(self.backref)

    # 检查 类型为 永久存储 的 起始模型，目标模型是否为 永久存储，如果不是，会想错误列表添加一条报错信息
    def check_storage_type(self, model_uuid_dict: dict):
        attr = Relationship.ATTR.SOURCE_TARGET
        source_model = model_uuid_dict.get(self.source_model_uuid, dict())
        source_model_is_temp = source_model.get("is_temp", False)
        if not source_model_is_temp:
            target_model = model_uuid_dict.get(self.target_model_uuid, dict())
            target_model_is_temp = target_model.get("is_temp", False)
            if target_model_is_temp:
                self._add_error_list(
                    attr=attr,
                    return_code=LDEC.RELATIONSHIP_TARGET_MODEL_NOT_STORAGE)

    # 检查 起始模型 的 目标模型，是否已在应用中存在关联，如果存在，会想错误列表添加一条报错信息
    def check_relationship_exists(self, relationship_model_uuid_dict: dict()):
        pass
        # attr = Relationship.ATTR.MODEL
        # target_model_uuid_set = relationship_model_uuid_dict.get(self.source_model_uuid, set())
        # if self.target_model_uuid in target_model_uuid_set:
        #     return_code = LDEC.RELATIONSHIP_EXISTS
        #     self._add_error_list(attr=attr, return_code=return_code)

    # 检查自关联
    def check_self_referential(self):
        attr = Relationship.ATTR.MODEL
        if self.source_model_uuid == self.target_model_uuid:
            if self.type == Relationship.TYPE.MANY_TO_MANY:
                self._add_error_list(attr=attr, return_code=LDEC.RELATIONSHIP_SELF_REFERENTIAL_FAILED)
            # 生成新的 relationship_uuid 方便在运行时查 log 时
            # 找到这个新的 relationship_uuid 但设计时文档却找不到时, 带来的疑问
            source_uuid = new_relationship_uuid(self.element_uuid)
            target_uuid = new_relationship_uuid(self.element_uuid, from_source=False)
            self.element.update({
                "relationship_uuid_source": source_uuid,
                "relationship_uuid_target": target_uuid
            })

    def check_if_in_circle(self, circle_uuids):
        attr = Relationship.ATTR.MODEL
        if self.element_uuid in circle_uuids:
            self._add_error_list(attr=attr, return_code=LDEC.RELATIONSHIP_LOOP)


class ModelDocumentCheckerService(CheckerService):

    allow_chinese_name = True

    def __init__(
        self, app_uuid: str, module_uuid: str, module_name: str,
        document_uuid: str, document_name: str,
        element: dict, document_version: int,
        app_model_list: list, app_relationship_list: list,
        app_field_list: list, app_index_list: list, app_enum_uuid_set, app_enum_value_uuid_set,
            app_func_uuid_dict, app_enum_list, *args, **kwargs):
        super().__init__(
            app_uuid, module_uuid, module_name, document_uuid, document_name, 
            element, *args, **kwargs)
        self.document_version = document_version
        self.models = self.element.get("models", [])
        self.relationships = self.element.get("relationships", [])
        self.system_relationships = self.element.get("system_relationships", [])
        self.ext_tenant = kwargs.get("ext_tenant")
        self.system_relationship_backref = kwargs.get("system_relationship_backref", {})
        self.app_model_uuid_set = set()
        self.app_model_uuid_dict = dict()
        self.app_relationship_uuid_set = set()
        self.app_relationship_name_set = set()
        self.app_relationship_model_uuid_dict = dict()
        self.app_field_uuid_set = set()
        self.app_index_uuid_set = set()
        self.app_enum_uuid_set = app_enum_uuid_set
        self.app_func_uuid_dict = app_func_uuid_dict
        self.app_enum_value_uuid_set = app_enum_value_uuid_set
        self.module_model_uuid_set = set()
        self.module_model_name_set = set()
        self.module_model_uuid_dict = dict()
        self.document_model_uuid_set = set()
        self.document_relationship_uuid_set = set()
        self.model_field_uuid_dict = dict()
        self.model_index_uuid_dict = dict()
        self.model_insert_list = []
        self.model_update_list = []
        self.model_delete_list = []
        self.relationship_insert_list = []
        self.relationship_update_list = []
        self.relationship_delete_list = []
        self.field_insert_list = []
        self.field_update_list = []
        self.field_delete_list = []
        self.index_insert_list = []
        self.index_update_list = []
        self.index_delete_list = []
        self.other_conflict_document_names_set = kwargs.get("other_conflict_document_names_set", set())
        for model in app_model_list:
            model_uuid = model.get("model_uuid", "")
            model_name = model.get("model_name", "")
            model_module_uuid = model.get("module_uuid", "")
            model_document_uuid = model.get("document_uuid", "")

            # 找到模块所有的数据模型，为了检查模块中数据模型，uuid、name 是否重复等
            if model_module_uuid == self.module_uuid:
                self.module_model_uuid_dict.update({model_uuid: model})

            # 找到原文档中所有的数据模型，为了新增、更新、删除文档的数据模型
            if model_document_uuid == self.document_uuid:
                self.document_model_uuid_set.add(model_uuid)
            else:
                # 排除当前文档所有的 model_uuid，获取应用的所有 model_uuid
                self.app_model_uuid_set.add(model_uuid)
                self.app_model_uuid_dict.update({model_uuid: model})
                # 排除当前文档所有的 model_uuid，获取模块的所有 model_uuid
                if model_module_uuid == self.module_uuid:
                    self.module_model_uuid_set.add(model_uuid)
                    self.module_model_name_set.add(model_name)

        self.module_enum_name_set = set()
        for _app_enum in app_enum_list:
            # enum_uuid = _app_enum.get("enum_uuid", "")
            enum_name = _app_enum.get("enum_name", "")
            enum_module_uuid = _app_enum.get("module_uuid", "")
            enum_document_uuid = _app_enum.get("document_uuid", "")

            # 找到原文档中所有的 枚举 ，为了新增、更新、删除文档的 枚举
            if enum_document_uuid == self.document_uuid:
                pass
                # self.document_enum_uuid_set.add(enum_uuid)
            else:
                # 排除当前文档所有的 enum_uuid ，获取模块的所有 enum_uuid
                if enum_module_uuid == self.module_uuid and not _app_enum.get("is_delete"):
                    self.module_enum_name_set.add(enum_name)

        for relationship in app_relationship_list:
            relationship_uuid = relationship.get("relationship_uuid", "")
            relationship_name = relationship.get("relationship_name", "")
            relationship_docuemtn_uuid = relationship.get("document_uuid", "")
            source_model = relationship.get("source_mode", "")
            target_model = relationship.get("target_mode", "")

            # 找到原文档中的所有的关联，为了新增、更新、删除文档的关联
            if relationship_docuemtn_uuid == self.document_uuid:
                self.document_relationship_uuid_set.add(relationship_uuid)
            else:
                self.app_relationship_uuid_set.add(relationship_uuid)
                if not relationship.get("is_delete"):
                    self.app_relationship_name_set.add(relationship_name)
                if source_model:
                    self.app_relationship_model_uuid_dict.setdefault(source_model, set())
                    self.app_relationship_model_uuid_dict[source_model].add(target_model)

        for field in app_field_list:
            field_uuid = field.get("field_uuid", "")
            field_document_uuid = field.get("document_uuid", "")
            field_model_uuid = field.get("model_uuid", "")

            # 找到原文档中的所有的 字段，为了新增、更新、删除文档的 字段
            if field_document_uuid == self.document_uuid:
                self.model_field_uuid_dict.setdefault(field_model_uuid, set())
                self.model_field_uuid_dict[field_model_uuid].add(field_uuid)
            else:
                # 排除当前文档所有的 field_uuid ，获取应用的所有 field_uuid
                self.app_field_uuid_set.add(field_uuid)

        for index in app_index_list:
            index_uuid = index.get("index_uuid", "")
            index_document_uuid = index.get("document_uuid", "")
            index_model_uuid = index.get("model_uuid", "")

            # 找到原文档中的所有的 索引，为了新增、更新、删除文档的 索引
            if index_document_uuid == self.document_uuid:
                self.model_index_uuid_dict.setdefault(index_model_uuid, set())
                self.model_index_uuid_dict[index_model_uuid].add(index_uuid)
            else:
                # 排除当前文档所有的 index_uuid ，获取应用的所有 index_uuid
                self.app_index_uuid_set.add(index_uuid)

    def check_relationship_circle(self):
        model_graph, _, _, _ = gen_model_graph(*self.graph_depend_model)
        app_log.info(model_graph)
        try:
            topo_sort(model_graph, calc_fail_node=True)
        except LemonPublishError as e:
            app_log.info(e)
            circle_relationship_uuids = e.element_uuid
        else:
            circle_relationship_uuids = list()
        return circle_relationship_uuids

    async def get_check_after_commit_depend(self):
        self.graph_depend_model = await gen_model_graph_depend_model(
            self.app_uuid, self.engine, async_select=False)
        self.graph_depend_model = list(self.graph_depend_model)

    def check_after_commit(self):
        """
        文档保存之后被check_commit调用
        """
        ...
        # self.check_relationships_in_cycle()

    def skip_not_exist_relationships(self):
        """
        这是由于事务混乱导致数据不一致, 打的补丁
        """
        relationships = self.graph_depend_model[4]
        new_relationships = list()
        relationship_uuids = [x.get("uuid") for x in self.relationships]
        for r in relationships:
            if r.relationship_uuid in relationship_uuids:
                new_relationships.append(r)
        self.graph_depend_model[4] = new_relationships

    def check_relationships_in_cycle(self):
        if self.ext_tenant:
            return
        self.skip_not_exist_relationships()
        circle_relationship_uuids = self.check_relationship_circle()
        for relationship in self.relationships:
            relationship_check_service = RelationshipCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, relationship)
            relationship_check_service.check_if_in_circle(circle_relationship_uuids)
            self.update_any_list(relationship_check_service)

    @checker.run
    def check_models(self):
        this_document_model_uuid_set = set()
        model_relationships = dict()
        for relationship_dict in self.relationships:
            source_model = relationship_dict.get("source_model")
            r_type = relationship_dict.get("type")
            if source_model and r_type != "1":
                r_list = model_relationships.setdefault(source_model, list())
                r_list.append(relationship_dict)
        for model in self.models:
            if self.new_ext_doc:
                need_check = True
            else:
                is_ext = model.get("is_extension")
                need_check = True
                if self.ext_tenant and not is_ext:
                    need_check = False
            model_uuid = model.get("uuid", "")
            events = model.get("events")
            relationships = model_relationships.get(model_uuid, [])
            model_check_service = ModelCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, model, ext_tenant=self.ext_tenant,
                new_ext_doc=self.new_ext_doc, allow_chinese_name=True,
                relationships=relationships, events=events, app_func_uuid_dict=self.app_func_uuid_dict)
            model_uuid = model_check_service.element_uuid
            model_check_service.document_other_info = self.document_other_info
            this_document_model_uuid_set.add(model_uuid)
            model_field_uuid_set = self.model_field_uuid_dict.get(model_uuid, set())
            model_index_uuid_set = self.model_index_uuid_dict.get(model_uuid, set())

            # 数据模型的 uuid 必须在整个 应用 唯一
            try:
                model_check_service.check_uuid()
                if need_check:
                    model_check_service.check_uuid_unique(self.app_model_uuid_set)

                # 数据模型的 name 必须在整个 模块 唯一
                model_check_service.check_name()
                if need_check:
                    model_check_service.check_name_unique(self.module_model_name_set)
                    model_check_service.check_name_unique(self.other_conflict_document_names_set)
                model_check_service.check_display_name()

                # # 用户关联模型 和 部门关联模型 必须在应用中存在
                # model_check_service.check_user_model(self.app_model_uuid_set)
                # model_check_service.check_department_model(self.app_model_uuid_set)

                model_check_service.check_fields(model_field_uuid_set, self.app_enum_uuid_set,
                                                 self.app_func_uuid_dict, self.app_enum_value_uuid_set)
                if need_check:
                    model_check_service.check_indexes(model_index_uuid_set)

                model_check_service.check_name_field()
                # 模型的字段依赖关系检查，需放到字段检查后
                # model_check_service.check_data_field()
                # model_check_service.check_sort_field()
                # model_check_service.check_check_field()
                model_check_service.check_event()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(model_check_service)
                raise e
            else:
                self.update_any_list(model_check_service)

            # 更新数据模型字段
            self.field_insert_list.extend(model_check_service.field_insert_list)
            self.field_update_list.extend(model_check_service.field_update_list)
            self.field_delete_list.extend(model_check_service.field_delete_list)

            # 更新数据模型索引
            self.index_insert_list.extend(model_check_service.index_insert_list)
            self.index_update_list.extend(model_check_service.index_update_list)
            self.index_delete_list.extend(model_check_service.index_delete_list)

            # 找到新增 或 更新的数据模型
            model_check_service.check_modify(self.document_model_uuid_set)
            if model_check_service.insert_query_list:
                self.model_insert_list.extend(model_check_service.insert_query_list)
                insert_query_data = model_check_service.build_insert_query_data()
                self.app_model_uuid_dict.update({model_uuid: insert_query_data})
            if model_check_service.update_query_list:
                self.model_update_list.extend(model_check_service.update_query_list)
                update_query_data = model_check_service.build_update_query_data()
                self.app_model_uuid_dict.update({model_uuid: update_query_data})

        # 找出删除的数据模型，将其 is_delete 置为 True
        delete_model_uuid_set = self.document_model_uuid_set - this_document_model_uuid_set
        for this_model_uuid in delete_model_uuid_set:
            if this_model_uuid in self.app_model_uuid_dict:
                del self.app_model_uuid_dict[this_model_uuid]
            query = ModelCheckerService.build_delete_query(this_model_uuid)
            self.model_delete_list.append(query)

    @checker.run
    def check_relationships(self):
        if self.ext_tenant:
            return

        this_relationship_uuid_set = set()

        # 要先判断，模型是不是已经有了系统关联，有的话，就不要再遍历了
        # 遍历每个模型，都增加两条系统关联，然后在这里遍历这些系统关联
        # 增加 系统关联 的标记，但要跟思豪商量，默认不显示系统关联 ？

        # 暂时屏蔽 所有模型生成 system_relationship ，不影响测试服使用
        # 生成的耗时 只有几毫秒
        if self.document_uuid != sys_model_document_uuid:
            self.system_relationships = create_system_relationships(self.models)
            self.element.update({"system_relationships": self.system_relationships})
        for relationship in self.relationships:
            relationship_check_service = RelationshipCheckerService(
                self.app_uuid, self.module_uuid, self.module_name,
                self.document_uuid, self.document_name, relationship,
                system_relationship_backref=self.system_relationship_backref)
            this_relationship_uuid_set.add(relationship_check_service.element_uuid)

            try:
                # 关联的 uuid 必须在整个 应用 唯一
                relationship_check_service.check_uuid()
                relationship_check_service.check_uuid_unique(self.app_relationship_uuid_set)

                # 关联的 uuid 必须在整个 模块 唯一
                relationship_check_service.check_name()
                # relationship_check_service.check_name_unique(self.app_relationship_name_set)

                relationship_check_service.check_display_name()
                relationship_check_service.check_type()
                relationship_check_service.check_relationship_model_exists(self.app_model_uuid_dict)
                relationship_check_service.check_source_model_on_delete()
                relationship_check_service.check_target_model_on_delete()
                relationship_check_service.check_storage_type(self.app_model_uuid_dict)
                relationship_check_service.check_frontref(self.app_model_uuid_dict)
                relationship_check_service.check_backref(self.app_model_uuid_dict)
                relationship_check_service.check_relationship_exists(self.app_relationship_model_uuid_dict)
                relationship_check_service.check_self_referential()
            except (CheckUUIDError, CheckUUIDUniqueError, CheckNameError) as e:
                self.update_any_list(relationship_check_service)
                raise e
            else:
                self.update_any_list(relationship_check_service)

            # 找到新增 或 更新的关联
            relationship_check_service.check_modify(self.document_relationship_uuid_set)
            if relationship_check_service.insert_query_list:
                self.relationship_insert_list.extend(relationship_check_service.insert_query_list)
                source_model = relationship_check_service.source_model_uuid
                target_model = relationship_check_service.target_model_uuid
                self.app_relationship_model_uuid_dict.setdefault(source_model, set())
                self.app_relationship_model_uuid_dict[source_model].add(target_model)
            if relationship_check_service.update_query_list:
                self.relationship_update_list.extend(relationship_check_service.update_query_list)
                source_model = relationship_check_service.source_model_uuid
                target_model = relationship_check_service.target_model_uuid
                self.app_relationship_model_uuid_dict.setdefault(source_model, set())
                self.app_relationship_model_uuid_dict[source_model].add(target_model)

        # 找到删除的关联，将其 is_delete 置为 True
        delete_relationship_uuid_set = self.document_relationship_uuid_set - this_relationship_uuid_set
        for relationship_uuid in delete_relationship_uuid_set:
            query = RelationshipCheckerService.build_delete_query(relationship_uuid)
            self.relationship_delete_list.append(query)

    async def commit_modify(self, engine):
        model_update_func_list = []
        model_delete_func_list = []
        relationship_update_func_list = []
        relationship_delete_func_list = []
        field_update_func_list = []
        field_delete_func_list = []
        index_update_func_list = []
        index_delete_func_list = []

        for query in self.model_update_list:
            func = engine.access.update_obj_by_query(ModelBasic, query, need_delete=True)
            model_update_func_list.append(func)
        for query in self.model_delete_list:
            func = engine.access.update_obj_by_query(ModelBasic, query, need_delete=True)
            model_delete_func_list.append(func)

        for query in self.relationship_update_list:
            func = engine.access.update_obj_by_query(RelationshipBasic, query, need_delete=True)
            relationship_update_func_list.append(func)
        for query in self.relationship_delete_list:
            func = engine.access.update_obj_by_query(RelationshipBasic, query, need_delete=True)
            relationship_delete_func_list.append(func)

        for query in self.field_update_list:
            func = engine.access.update_obj_by_query(ModelField, query, need_delete=True)
            field_update_func_list.append(func)
        for query in self.field_delete_list:
            func = engine.access.update_obj_by_query(ModelField, query, need_delete=True)
            field_delete_func_list.append(func)

        for query in self.index_update_list:
            func = engine.access.update_obj_by_query(ModelIndex, query, need_delete=True)
            index_update_func_list.append(func)
        for query in self.index_delete_list:
            func = engine.access.update_obj_by_query(ModelIndex, query, need_delete=True)
            index_delete_func_list.append(func)

        # 这里如果数据量大的话，会有性能问题
        async with engine.db.objs.atomic():
            if self.model_insert_list:
                app_log.info(f"Insert {ModelBasic.__name__}, len: {len(self.model_insert_list)}")
                await engine.access.insert_many_obj(ModelBasic, self.model_insert_list)
            app_log.info(f"Update {ModelBasic.__name__}, len: {len(model_update_func_list)}")
            for f in model_update_func_list:
                await f
            # await asyncio.gather(*model_update_func_list)
            app_log.info(f"Update {ModelBasic.__name__}.is_delete, len: {len(model_delete_func_list)}")
            for f in model_delete_func_list:
                await f
            # await asyncio.gather(*model_delete_func_list)

            if self.relationship_insert_list:
                app_log.info(f"Insert {RelationshipBasic.__name__}, len: {len(self.relationship_insert_list)}")
                await engine.access.insert_many_obj(RelationshipBasic, self.relationship_insert_list)
            app_log.info(f"Update {RelationshipBasic.__name__}, len: {len(relationship_update_func_list)}")
            for f in relationship_update_func_list:
                await f
            # await asyncio.gather(*relationship_update_func_list)
            app_log.info(f"Update {RelationshipBasic.__name__}.is_delete, len: {len(relationship_delete_func_list)}")
            for f in relationship_delete_func_list:
                await f
            # await asyncio.gather(*relationship_delete_func_list)

            if self.field_insert_list:
                app_log.info(f"Insert {ModelField.__name__}, len: {len(self.field_insert_list)}")
                await engine.access.insert_many_obj(ModelField, self.field_insert_list)
            app_log.info(f"Update {ModelField.__name__}, len: {len(field_update_func_list)}")
            for f in field_update_func_list:
                await f
            # await asyncio.gather(*field_update_func_list)
            app_log.info(f"Update {ModelField.__name__}.is_delete, len: {len(field_delete_func_list)}")
            for f in field_delete_func_list:
                await f
            # await asyncio.gather(*field_delete_func_list)

            if self.index_insert_list:
                app_log.info(f"Insert {ModelIndex.__name__}, len: {len(self.index_insert_list)}")
                await engine.access.insert_many_obj(ModelIndex, self.index_insert_list)
            app_log.info(f"Update {ModelIndex.__name__}, len: {len(index_update_func_list)}")
            for f in index_update_func_list:
                await f
            # await asyncio.gather(*index_update_func_list)
            app_log.info(f"Update {ModelIndex.__name__}.is_delete, len: {len(index_delete_func_list)}")
            for f in index_delete_func_list:
                await f
            # await asyncio.gather(*index_delete_func_list)
