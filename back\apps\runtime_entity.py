# -*- coding:utf-8 -*-

import time
from peewee import SQL
from peewee import (
    FixedCharField, CharField, BooleanField, IntegerField
)
from apps.base_entity import RuntimeBaseModel as BaseModel
from apps.peewee_ext import (
    LemonCharField, LemonIntegerField, LemonBooleanField, JsonField
)


class AppEditor(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("app_uuid", "tenant_uuid", "user_uuid"), False),
        )


class Tenant(BaseModel):
    # lemon_tenant_center.tenant 视图
    tenant_uuid = FixedCharField(max_length=32, unique=True)
    tenant_name = CharField(max_length=100, default="", constraints=[SQL('DEFAULT ""')])
    credential_no = FixedCharField(max_length=18, unique=True)
    tenant_id = FixedCharField(max_length=18, unique=True)
    province = JsonField(null=True)
    city = JsonField(null=True)
    district = JsonField(null=True)
    address = CharField(max_length=300, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    setting = JsonField(null=True, default={"allow_da_add": True, "allow_da_edit": True})
    sys_create = BooleanField(default=False, constraints=[SQL('DEFAULT False')])  # 系统自动创建默认租户
    creator = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    # app_env = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0生产环境  1测试环境

    class Meta():
        schema = "lemon_tenant_center"
        evolve = True
        indexes = ()


class SMStore(BaseModel):

    sm_uuid = FixedCharField(max_length=32, unique=True)
    state_uuid = FixedCharField(max_length=64, default="", constraints=[SQL('DEFAULT ""')])
    state_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    variable_list = JsonField(null=True)
    sm_dict = JsonField(null=True)

    class Meta():
        sys_table = True
        # schema = "lemon_tenant_center"
        evolve = True


class GitAccessManagement(BaseModel):
    user_uuid = LemonCharField(max_length=32, unique=False, constraints=[SQL("comment 'user_uuid'")], aka="user_uuid")
    repo_type = LemonIntegerField(default=0, constraints=[SQL("comment 'repo type'")], aka="repo_type")
    git_host = LemonCharField(max_length=255, null=True, constraints=[SQL("comment 'git host'")])
    ssh_host = LemonCharField(max_length=255, null=True, constraints=[SQL("comment 'ssh host'")])
    token_expire = LemonIntegerField(default=-1, constraints=[SQL("comment 'token_expire'")], aka="token_expire")
    user_token = LemonCharField(max_length=30, null=True, constraints=[SQL("comment 'user token'")])

    class Meta():
        aka = "git_access_management"
        table_name = "git_access_management"
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("user_uuid", "repo_type"), False),
        )


class Team(BaseModel):
    team_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])  # 起到user_uuid的作用
    team_id = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    team_name = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    create_at = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    publisher = LemonIntegerField(default=None, null=True, constraints=[SQL('DEFAULT NULL')])
    is_delete = LemonBooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        table_name = "team"
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("team_uuid", ), True),
            (("team_id", ), False),
            (("team_name", ), False)
        )


class Teamplayer(BaseModel):
    user_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    team_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    is_active = LemonBooleanField(default=True, constraints=[SQL('DEFAULT True')])
    invitor = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    permission = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0: 普通成员  1: 管理员  2: 超级管理员
    status = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0: 正常  1: 邀请中  2: 已同意,待审核
    join_time = LemonIntegerField(default=None, null=True, constraints=[SQL('DEFAULT NULL')])
    combine_app_info = JsonField(null=True)

    class Meta():
        table_name = "teamplayer"
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("team_uuid", "user_uuid"), False),
        )


class TeamInvitation(BaseModel):
    team_uuid = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    invitor = LemonCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    code_flag = LemonCharField(max_length=8, default="", constraints=[SQL('DEFAULT ""')])
    create_time = LemonIntegerField(default=None, null=True, constraints=[SQL('DEFAULT NULL')])
    permission = LemonIntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0: 普通成员  1: 管理员  2: 超级管理员
    combine_app_info = JsonField(null=True)
    is_delete = LemonBooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        table_name = "team_invitation"
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("team_uuid", ), False),
            (("team_uuid", "code_flag"), False),
        )


class AppTenant(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    add_time = IntegerField(default=int(time.time()), null=True)
    is_test = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    service_start_at = IntegerField(default=0,  constraints=[SQL('DEFAULT 0')])
    service_end_at = IntegerField(default=0,  constraints=[SQL('DEFAULT 0')])
    additional = CharField(max_length=500, default="", constraints=[SQL('DEFAULT ""')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    inviting_status = IntegerField(default=0,  constraints=[SQL('DEFAULT 0')])  # 0: 同意 1: 等待中 2: 忽略/拒绝
    # jdbc_allowed = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    jdbc_config = JsonField(null=True)

    class Meta():
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("app_uuid", "tenant_uuid"), False),
        )


class InvitationCode(BaseModel):
    code = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    create_time = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        schema = "lemon_ucenter"
        indexes = (
            (("code",), True),
            )


class AccessKey(BaseModel):

    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_key = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    app_secret = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    status = BooleanField(default=True, constraints=[SQL('DEFAULT True')])  # 是否启用
    created_at = IntegerField(default=None, null=True, constraints=[SQL('DEFAULT null')])
    last_used_at = IntegerField(default=None, null=True, constraints=[SQL('DEFAULT null')])  # 最后获取token时间
    secret_created_at = IntegerField(default=None, null=True, constraints=[SQL('DEFAULT null')])
    app_env = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0正式环境  1测试环境
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        evolve = True
        schema = "lemon_tenant_center"
        indexes = (
            (("app_key", ), False),
        )


class DepartmentMember(BaseModel):
    # 这是tenant_center的视图
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    department_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    user_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    job = CharField(max_length=30, default="", constraints=[SQL('DEFAULT ""')])
    is_admin = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0 普通成员 1 子管理员 2 主管
    is_major = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        schema = "lemon_ucenter"
        evolve = True
        indexes = (
            (("tenant_uuid", "department_uuid"), False),
            (("tenant_uuid", "user_uuid"), False),
        )


class RoleMember(BaseModel):
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])   # 不同app会共用一个租户的组织架构，但权限不共用
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    role_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    member_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    member_type = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 0 user 1 department
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    app_env = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])  # 0生产环境  1测试环境

    class Meta():
        evolve = True
        indexes = (
            (("tenant_uuid", "role_uuid", "member_uuid", "app_env"), False),
        )


class Terminal(BaseModel):

    # 这是tenant_center的视图
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    terminal_id = FixedCharField(max_length=32, unique=True)
    name = FixedCharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')])
    version = FixedCharField(max_length=20, default="", constraints=[SQL('DEFAULT ""')])
    status = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 0 offline  1 online
    is_block = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        sys_table = True
        evolve = True
        indexes = (
            (("tenant_uuid", "terminal_id", ), False),
        )


class Gateway(BaseModel):

    # 这是tenant_center的视图
    app_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    tenant_uuid = FixedCharField(max_length=32, default="", constraints=[SQL('DEFAULT ""')])
    gateway_id = FixedCharField(max_length=32, unique=True)
    name = FixedCharField(max_length=40, default="", constraints=[SQL('DEFAULT ""')])
    version = FixedCharField(max_length=20, default="", constraints=[SQL('DEFAULT ""')])
    status = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 0 offline  1 online
    connect_status = IntegerField(default=0, constraints=[SQL('DEFAULT 0')])   # 0 disconnect  1 connect
    machine_info = FixedCharField(max_length=255, default="", constraints=[SQL('DEFAULT ""')])
    is_block = BooleanField(default=False, constraints=[SQL('DEFAULT False')])
    is_delete = BooleanField(default=False, constraints=[SQL('DEFAULT False')])

    class Meta():
        sys_table = True
        evolve = True
        indexes = (
            (("tenant_uuid", "gateway_id", ), False),
        )
