version: '3'
services:
  mysql-{{app_env}}:
    restart: always
    container_name: mysql-{{app_env}}
    environment:
      - MYSQL_ROOT_PASSWORD={{mysql_pwd}}
      - MYSQL_ROOT_HOST=%
      - TZ=Asia/Shanghai
    ports:
      - "${mysql_port}:3306"
    volumes:
      - ./data/mysql:/var/lib/mysql
    image: harbor.lemonstudio.tech/library/mysql:8.0.27
  redis-{{app_env}}:
    restart: always
    container_name: redis-{{app_env}}
    ports:
      - "6379"
    volumes:
      - ./data/redis:/data
    command: /bin/bash -c "redis-server /etc/redis/redis.conf;tail -f /etc/hosts"
    image: harbor.lemonstudio.tech/library/redis:5.0.7
  nginx-{{app_env}}:
    restart: always
    container_name: nginx-{{app_env}}
    ports:
      - "${nginx_port}:8888"
    volumes:
      - ./nginx/web.conf:/etc/nginx/conf.d/web.conf
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/www/html:/data0/nfs/www/html
      {% if ssl %}
      - {{ssl_certificate}}:{{ssl_certificate_nginx}}
      - {{ssl_certificate_key}}:{{ssl_certificate_key_nginx}}
      {% endif %}
    image: harbor.lemonstudio.tech/library/nginx:1.18.0
    depends_on:
        - web-{{app_env}}
  web-{{app_env}}:
    restart: always
    container_name: web-{{app_env}}
    healthcheck:
        test: ["CMD-SHELL", "/root/healthcheck.sh || exit 1"]
        interval: 60s
        timeout: 52s
        retries: 5
    expose:
       - "7000"
    environment:
        - USER_UUID={{middle_user_uuid}}
        - APP_UUID={{app_uuid}}
        - APP_REVISION={{app_revision}}
    volumes:
      - ./web/local_config.py:/root/back/runtime/local_config.py
      - ./web/local_config.py:/root/back/ucenter/local_config.py
      - ./web/local_config.py:/root/back/apps/local_config.py
      - ${web_data_dir}:/data
      - ${runtime_file_storage_dir}:/data/local_file/runtime
      - ./design_file/design:/data/local_file/design
      - ./bin/web_healthy.sh:/root/healthcheck.sh
      - ./nginx/web:/etc/nginx/sites-enabled/web
      - ./nginx/pc:/etc/nginx/sites-enabled/pc
      - ./data/log/supervisor:/var/log/supervisor
      - ./nginx/www:/data0/nfs/www
    depends_on:
      - mysql-{{app_env}}
      - redis-{{app_env}}
    command: /bin/bash -c "sleep 2; mkdir /var/log/supervisor/old | cp /var/log/supervisor/*.log* /var/log/supervisor/old | supervisord; nginx ;tail -f /etc/hosts"
    image: {{deploy_image_name}}:{{app_revision}}
  print-{{app_env}}:
      restart: always
      container_name: print-{{app_env}}
      expose:
        - "5001"
      volumes:
        - ./nginx/www:/var/www
      command: /bin/bash -c "redis-server /etc/redis/redis.conf;tail -f /etc/hosts"
      image: {{deploy_image_name}}:print-{{app_revision}}
  # minio-{{app_env}}:
  #   restart: always
  #   container_name: minio-{{app_env}}
  #   environment:
  #     MINIO_CONFIG_ENV_FILE: /etc/config.env
  #     TZ: Asia/Shanghai
  #     MINIO_ROOT_USER: ${MINIO_USER}
  #     MINIO_ROOT_PASSWORD: ${MINIO_PWD}
  #   ports:
  #     - "${MINIO_PORT}:9000"
  #     - "${MINIO_CONSOLE_PORT}:9001"
  #   volumes:
  #     - ./data/minio:/mnt/data
  #   image: harbor.lemonstudio.tech/k8s/minio:v1
  #   command: minio server /mnt/data  --console-address ":9001"
networks:
    default:
        external:
            name: lemon-bridge
    
